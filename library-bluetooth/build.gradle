buildscript {
    repositories {
        maven { url 'https://jitpack.io' }
        google()
    }

    dependencies {
        classpath 'com.android.tools.build:gradle:7.2.2'
    }
}

plugins {
    id 'com.android.library'
    id 'org.jetbrains.kotlin.android'
}

android {
    compileSdk 31
    namespace 'co.timekettle.btkit'

    defaultConfig {
        minSdk 24
        targetSdk 31
        versionCode 1
        versionName "1.0"

        testInstrumentationRunner "androidx.test.runner.AndroidJUnitRunner"
    }

    buildTypes {
        release {
            minifyEnabled false
            proguardFiles getDefaultProguardFile('proguard-android-optimize.txt'), 'proguard-rules.pro'
        }
    }
    compileOptions {
        sourceCompatibility JavaVersion.VERSION_1_8
        targetCompatibility JavaVersion.VERSION_1_8
    }

    kotlinOptions {
        jvmTarget = "1.8"
    }
    buildFeatures {
        viewBinding = true
    }
    lintOptions {
        abortOnError false
    }
}

dependencies {
    implementation fileTree(include: ['*.jar', '*.aar'], dir: 'src/main/jniLibs')
//    api project(':easyble-x')

    implementation 'com.google.code.gson:gson:2.9.0'
    implementation 'no.nordicsemi.android:dfu:2.0.2'
    implementation 'com.github.getActivity:XXPermissions:18.6'
    implementation 'org.greenrobot:eventbus:3.3.1'
    implementation 'io.github.jeremyliao:live-event-bus-x:1.8.0'

    implementation 'androidx.appcompat:appcompat:1.4.1'
    implementation 'com.google.android.material:material:1.6.0'
    implementation 'androidx.core:core-ktx:1.8.0'
    implementation 'androidx.lifecycle:lifecycle-viewmodel-ktx:2.2.0'
    testImplementation 'junit:junit:4.13.2'
    androidTestImplementation 'androidx.test.ext:junit:1.1.3'
    androidTestImplementation 'androidx.test.espresso:espresso-core:3.4.0'

    implementation 'com.github.timekettledev:easyble-x:1.0.6'

    implementation "androidx.preference:preference-ktx:1.2.0"


}