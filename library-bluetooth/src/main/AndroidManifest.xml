<?xml version="1.0" encoding="utf-8"?>
<manifest xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    package="co.timekettle.btkit">

    <!--    <uses-permission android:name="android.permission.BLUETOOTH" />-->
    <!--    android 12     -->
    <!--    <uses-permission android:name="android.permission.BLUETOOTH_ADMIN" />-->
    <uses-permission android:name="android.permission.BLUETOOTH_CONNECT" />
    <uses-permission android:name="android.permission.BLUETOOTH_SCAN" />
    <!--    android 12     -->

    <uses-permission android:name="android.permission.BLUETOOTH" />
    <uses-permission android:name="android.permission.BLUETOOTH_ADMIN" />
    <uses-permission android:name="android.permission.ACCESS_COARSE_LOCATION" />
    <uses-permission android:name="android.permission.ACCESS_FINE_LOCATION" />

    <uses-permission android:name="android.permission.BLUETOOTH_ADVERTISE" />

    <uses-permission android:name="android.permission.FOREGROUND_SERVICE" />


    <!--    &lt;!&ndash; Request legacy Bluetooth permissions on older devices. &ndash;&gt;-->
    <!--    <uses-permission android:name="android.permission.BLUETOOTH"-->
    <!--        android:maxSdkVersion="30" />-->
    <!--    <uses-permission android:name="android.permission.BLUETOOTH_ADMIN"-->
    <!--        android:maxSdkVersion="30" />-->

    <!--    <uses-permission android:name="android.permission.BLUETOOTH_SCAN" />-->
    <!--    <uses-permission android:name="android.permission.BLUETOOTH_ADVERTISE" />-->
    <!--    <uses-permission android:name="android.permission.BLUETOOTH_CONNECT" />-->

    <application>
        <service android:name=".dfu.DfuService" />

        <activity
            android:name="co.timekettle.btkit.sample.BleScanActivity"
            android:screenOrientation="behind"
            android:theme="@style/Theme.AppCompat.Light.Blue" />
        <activity
            android:name="co.timekettle.btkit.sample.OperationActivity"
            android:screenOrientation="behind"
            android:theme="@style/Theme.AppCompat.Light.Blue" />
        <activity
            android:name="co.timekettle.btkit.sample.BleSettingActivity"
            android:screenOrientation="behind"
            android:theme="@style/Theme.AppCompat.Light.Blue" />

    </application>


</manifest>