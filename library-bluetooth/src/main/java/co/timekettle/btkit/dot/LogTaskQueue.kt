package co.timekettle.btkit.dot

import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.channels.Channel
import kotlinx.coroutines.launch

// 定义一个任务类型，可以是一个函数类型
typealias Task = suspend () -> Unit

class LogTaskQueue {

    private var channel = Channel<Task>()

    private val scope = CoroutineScope(Dispatchers.IO)


    // 按顺序执行队列中的任务
    fun startProcessing() {
        scope.launch {
            for (task in channel) {
                task()
            }
        }
    }

    // 添加任务到队列中
    fun addTask(task: Task) {
        scope.launch {
            channel.send(task)
        }
    }

    // 关闭任务队列
    fun close() {
        channel.close()
    }
}