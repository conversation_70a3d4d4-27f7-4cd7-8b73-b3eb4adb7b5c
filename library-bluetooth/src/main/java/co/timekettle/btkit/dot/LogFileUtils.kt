package co.timekettle.btkit.dot

import android.util.Log
import java.io.File
import java.io.FileOutputStream
import java.io.IOException

/**
 * author: weiconglee
 **/
object LogFileUtils {
    fun writeToFile(fileName: String, content: String, append: Boolean = true) {
        val file = File(fileName)
        if (!file.exists()) file.createNewFile()
        try {
            FileOutputStream(file, append).use { output ->
                output.write(content.toByteArray())
            }
        } catch (e: IOException) {
            e.printStackTrace()
        }
    }


    fun readFile(fileName: String): String {
        val file = File(fileName)
        if (!file.exists()) return ""
        val sb = StringBuilder()
        try {
            file.forEachLine {
                sb.append(it + "\n")
            }
        } catch (e: IOException) {
            e.printStackTrace()
        }
        return sb.toString()
    }

    // 替换文件的第一行
    fun writeLogLine(fileName: String, newFirstLine: String, newLine: String) {
        val file = File(fileName)
        if (!file.exists()) file.createNewFile()

        // 读取文件内容到 List
        val lines = file.readLines().toMutableList()

        if (lines.isNotEmpty()) {
            lines[0] = newFirstLine // 替换第一行
        } else {
            lines.add(newFirstLine) // 如果文件为空，添加新行
        }

        // 追加新行
        lines.add(newLine)

        lines[0] += "  >>>>>>>>平均耗时[${getAvgTime(lines)}ms]"

        // 将 List 写回文件
        file.writeText(lines.joinToString("\n"))
    }

    fun deleteDirFirstFile(directoryPath: String, startName: String, max: Int) {
        val directory = File(directoryPath)

        // 列出所有以startName开头的文件
        val files = directory.listFiles { file ->
            file.isFile && file.name.startsWith(startName)
        }?.sortedBy { it.lastModified() }

        if (files != null && files.size > max) {
            files.first().delete()
        }
    }

    /**
     * 限制文件夹数量keepCount，保留keepFolderName文件夹，删除最早的文件夹
     * @param directoryPath String
     * @param keepCount Int
     * @param keepFolderName String
     */
    fun deleteOldestFolders(directoryPath: String, keepCount: Int, keepFolderName: String) {
        val directory = File(directoryPath)
        if (!directory.exists() || !directory.isDirectory) {
            return
        }

        // 获取文件夹列表，排除keepFolderName文件夹
        val folders = directory.listFiles { file -> file.isDirectory && file.name != keepFolderName } ?: return
        if (folders.size <= keepCount) {
            return
        }

        // 按照最后修改时间排序
        val sortedFolders = folders.sortedBy { it.lastModified() }

        // 需要删除的文件夹数量
        val deleteCount = folders.size - keepCount

        // 删除最早创建的文件夹
        for (i in 0 until deleteCount) {
            val folderToDelete = sortedFolders[i]
            folderToDelete.deleteRecursively()
        }
    }

    private fun getAvgTime(lines: MutableList<String>): Double {
        val timeList = mutableListOf<Int>()
        val regex = """耗时:(\d+)ms""".toRegex()
        for (line in lines) {
            val matchResult = regex.find(line)
            if (matchResult != null) {
                try {
                    val time = matchResult.groupValues[1].toInt()
                    timeList.add(time)
                } catch (e: Exception) {
//                    e.printStackTrace()
                }
            }
        }
        return timeList.average()
    }
}