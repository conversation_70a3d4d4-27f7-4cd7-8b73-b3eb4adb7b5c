package co.timekettle.btkit.dot

/**
 * author: weiconglee
 **/
abstract class DotExceptionHandle {
    protected fun <T> runWithExceptionHandling(block: () -> T): T? {
        return try {
            block()
        } catch (e: Exception) {
            handleException(e)
            null
        }
    }

    private fun handleException(e: Exception) {
        // 处理异常的逻辑
        println("Exception caught: ${e.message}")
    }
}