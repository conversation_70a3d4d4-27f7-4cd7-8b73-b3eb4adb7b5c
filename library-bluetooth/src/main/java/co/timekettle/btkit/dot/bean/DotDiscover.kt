package co.timekettle.btkit.dot.bean

import co.timekettle.btkit.dot.ConsumeData
import co.timekettle.btkit.dot.LogFileUtils

/**
 * author: weiconglee
 **/
class DotDiscover : BaseDot() {

    override fun dotStart(strArray: Array<String?>, time: Long) {
        val mac = strArray[0]
        val uuid = strArray[1]
        if (mac.isNullOrBlank()) return
        log("服务发现开始 $mac")
        if (rateData == null) rateData = getRatedDot(path)
        val connConsume = mapConsume.getOrPut(uuid) { ConsumeData() }
        connConsume.mac = mac.toString()
        connConsume.tStart = time
    }

    override fun dotSuccess(strArray: Array<String?>, time: Long) {
        val mac = strArray[0]
        val uuid = strArray[1]
        if (mac.isNullOrBlank()) return
        log("服务发现成功 $mac")
        val consumeData = mapConsume.getOrPut(uuid) { ConsumeData() }
        val dotData = rateData ?: getRatedDot(path)
        consumeData.tSuccess = time
        dotData.success += 1
        LogFileUtils.writeLogLine(
            path,
            dotData.toString(),
            consumeData.toString()
        )
        mapConsume.remove(uuid)
    }


    override fun dotFail(strArray: Array<String?>, time: Long) {
        val mac = strArray[0]
        val uuid = strArray[1]
        val desc = strArray[2]
        if (mac.isNullOrBlank()) return
        log("服务发现失败 $mac")
        val consumeData = mapConsume.getOrPut(uuid) { ConsumeData() }
        val dotData = rateData ?: getRatedDot(path)
        consumeData.tFail = time
        consumeData.desc = desc
        dotData.fail += 1
        LogFileUtils.writeLogLine(
            path,
            dotData.toString(),
            consumeData.toString()
        )
        mapConsume.remove(uuid)
    }
}