package co.timekettle.btkit.dot.bean

import co.timekettle.btkit.dot.ConsumeData
import co.timekettle.btkit.dot.LogFileUtils

/**
 * author: weiconglee
 **/
class DotAuth : BaseDot() {
    override fun dotStart(strArray: Array<String?>, time: Long) {
        val mac = strArray[0]
        if (mac.isNullOrBlank()) return
        log("鉴权开始 $mac")
        val connConsume = mapConsume.getOrPut(mac.toString()) { ConsumeData() }
        connConsume.tStart = time
    }

    override fun dotEnd(strArray: Array<String?>, time: Long) {
        val mac = strArray[0]
        if (mac.isNullOrBlank()) return
        log("鉴权结束 $mac")
        val connConsume = mapConsume.getOrPut(mac.toString()) { ConsumeData() }
        connConsume.mac = mac.toString()
        connConsume.tSuccess = time
        LogFileUtils.writeLogLine(path, "", connConsume.toString())
        mapConsume.remove(mac)
    }
}