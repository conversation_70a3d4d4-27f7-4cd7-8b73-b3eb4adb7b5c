package co.timekettle.btkit.dot.bean

import co.timekettle.btkit.dot.ConsumeData
import co.timekettle.btkit.dot.LogFileUtils

/**
 * author: weiconglee
 **/
class DotConnect : BaseDot() {

    override fun dotStart(strArray: Array<String?>, time: Long) {
        val mac = strArray[0]
        val uuid = strArray[1]
        if (mac.isNullOrBlank()) return
        log("开始连接 $mac")
        if (rateData == null) rateData = getRatedDot(path)
        val connConsume = mapConsume.getOrPut(uuid) { ConsumeData() }
        connConsume.tStart = time
        connConsume.mac = mac.toString()
    }

    override fun dotSuccess(strArray: Array<String?>, time: Long) {
        val mac = strArray[0]
        val uuid = strArray[1]
        if (mac.isNullOrBlank()) return
        log("连接成功 $mac")
        val dotData = rateData ?: getRatedDot(path)
        val consumeData = mapConsume.getOrPut(uuid) { ConsumeData() }
        consumeData.tSuccess = time
        dotData.success += 1
        LogFileUtils.writeLogLine(
            path,
            dotData.toString(),
            consumeData.toString()
        )
        mapConsume.remove(uuid)
    }

    override fun dotFail(strArray: Array<String?>, time: Long) {
        val mac = strArray[0]
        val uuid = strArray[1]
        val desc = strArray[2]
        if (mac.isNullOrBlank()) return
        log("连接失败 $mac")
        val dotData = rateData ?: getRatedDot(path)
        val consumeData = mapConsume[uuid] ?: return
        consumeData.tFail = time
        consumeData.desc = desc
        dotData.fail += 1
        LogFileUtils.writeLogLine(
            path,
            dotData.toString(),
            consumeData.toString()
        )
        mapConsume.remove(uuid)
    }
}