package co.timekettle.btkit.dot

import android.content.Context
import co.timekettle.btkit.dot.bean.DotAuth
import co.timekettle.btkit.dot.bean.DotCmd
import co.timekettle.btkit.dot.bean.DotConnect
import co.timekettle.btkit.dot.bean.DotDiscover
import co.timekettle.btkit.dot.bean.DotPerListen
import co.timekettle.btkit.dot.bean.DotSoundSend
import java.io.File
import java.text.SimpleDateFormat
import java.util.Date
import java.util.Locale


/**
 *
 * BLE打点分析器，用来分析BLE连接耗时等信息
 * 目前已经实现的功能：
 * 1、连接耗时（包括成功耗时、失败耗时）
 * 2、连接成功率统计（通过搜索日志中的true和false计数，即可计算出成功率）
 */
class BleDotAnalyzer : DotExceptionHandle() {

    val TAG = "BleAnalyzer"

    companion object {
        var PATH_ROOT_DIR = ""
        var PATH_SOUND_LOG_DIR = ""
        var PATH_SOUND_LOG_RATE = ""
        var PATH_SOUND_TIME_SPEED = ""
        var PATH_SERVICE_ALL_LISTEN_TIME_CONSUME = ""
        var PATH_DISCONNECT = ""
    }

    private var context: Context? = null
    private var cacheToExternal = false
    private var logTaskQueue = LogTaskQueue()

    private var dotAuth = DotAuth()
    private var dotCmd = DotCmd()
    private var dotConnect = DotConnect()
    private var dotDiscover = DotDiscover()
    private var dotPerListen = DotPerListen()
    private var dotSoundSend = DotSoundSend()

    private var dateFormat: SimpleDateFormat = SimpleDateFormat("yyyyMMdd", Locale.CHINA)

    private fun getCurrentDateString(): String {
        return dateFormat.format(Date())
    }

    private fun getDateHour(): String {
        val timeFormat = SimpleDateFormat("yyyyMMddHH", Locale.CHINA)
        return timeFormat.format(Date())
    }

    init {
        logTaskQueue.startProcessing()
    }

    fun init(context: Context, toExternal: Boolean) {
        this.context = context
        this.cacheToExternal = toExternal
        logTaskQueue.addTask {
            createPathAndFile()
        }
    }

    /**
     * 初始化日志文件夹
     */
    private fun createPathAndFile() {
        runWithExceptionHandling {
            var MAIN_PATH = "/Log/BLE_ANALYZER/"
            if (cacheToExternal) MAIN_PATH = "/userlog/BLE_ANALYZER/"
            // 删除最早的一份埋点数据
            LogFileUtils.deleteOldestFolders(
                context?.cacheDir?.absolutePath + MAIN_PATH,
                10,
                "AudioLog"
            )
            PATH_ROOT_DIR = if (cacheToExternal) {
                context?.externalCacheDir?.absolutePath + "$MAIN_PATH${getCurrentDateString()}"
            } else {
                context?.cacheDir?.absolutePath + "$MAIN_PATH${getCurrentDateString()}"

            }
            PATH_SOUND_LOG_DIR = if (cacheToExternal) {
                context?.externalCacheDir?.absolutePath + "$MAIN_PATH/AudioLog"
            } else {
                context?.cacheDir?.absolutePath + "$MAIN_PATH/AudioLog"

            }
            // 创建当天文件夹
            val rootDir = File(PATH_ROOT_DIR)
            if (!rootDir.exists()) {
                rootDir.mkdirs()
            }
            // 创建音频数据发送埋点文件夹
            val audioLogDir = File(PATH_SOUND_LOG_DIR)
            if (!audioLogDir.exists()) {
                audioLogDir.mkdirs()
            }
            PATH_SOUND_TIME_SPEED = "$PATH_ROOT_DIR/音频数据发送速率.txt"
            PATH_SERVICE_ALL_LISTEN_TIME_CONSUME = "$PATH_ROOT_DIR/服务总监听耗时.txt"
            PATH_DISCONNECT = "$PATH_ROOT_DIR/断连记录.txt"
            PATH_SOUND_LOG_RATE = "$PATH_SOUND_LOG_DIR/总体成功率.txt"
            dotConnect.setFilePath("$PATH_ROOT_DIR/连接耗时.txt")
            dotDiscover.setFilePath("$PATH_ROOT_DIR/服务发现耗时.txt")
            dotPerListen.setFilePath("$PATH_ROOT_DIR/服务各个监听耗时.txt")
            dotAuth.setFilePath("$PATH_ROOT_DIR/鉴权处理耗时.txt")
            dotCmd.setFilePath("$PATH_ROOT_DIR/指令发送耗时.txt")
            LogFileUtils.deleteDirFirstFile(PATH_SOUND_LOG_DIR, "音频发送耗时", 8)
            dotSoundSend.setFilePath("${PATH_SOUND_LOG_DIR}/音频发送耗时_${getDateHour()}.txt")
        }
    }

    //----------------鉴权 start-----------
    fun dotAuthStart(mac: String?, time: Long) {
        logTaskQueue.addTask {
            dotAuth.doStart(arrayOf(mac), time)
        }
    }

    fun dotAuthEnd(mac: String?, time: Long) {
        logTaskQueue.addTask {
            dotAuth.doEnd(arrayOf(mac), time)
        }
    }
    //----------------鉴权 end-----------

    //----------------连接 start-----------
    fun dotStartConn(mac: String?, uuid: String, time: Long) {
        logTaskQueue.addTask {
            createPathAndFile()
            dotConnect.doStart(arrayOf(mac, uuid), time)
        }
    }

    fun dotConnSuccess(mac: String?, uuid: String, time: Long) {
        logTaskQueue.addTask {
            dotConnect.doSuccess(arrayOf(mac, uuid), time)
        }
    }

    fun dotConnFail(mac: String?, uuid: String, errDesc: String?, time: Long) {
        logTaskQueue.addTask {
            dotConnect.doFail(arrayOf(mac, uuid, errDesc), time)
        }
    }
    //----------------连接 end-----------


    //----------------服务发现 start-----------
    fun dotDiscoverStart(mac: String?, uuid: String, time: Long) {
        logTaskQueue.addTask {
            dotDiscover.doStart(arrayOf(mac, uuid), time)
        }
    }

    fun dotDiscoverSuccess(mac: String?, uuid: String, time: Long) {
        logTaskQueue.addTask {
            dotDiscover.doSuccess(arrayOf(mac, uuid), time)
        }
    }

    fun dotDiscoverFail(mac: String?, uuid: String, errDesc: String?, time: Long) {
        logTaskQueue.addTask {
            dotDiscover.doFail(arrayOf(mac, uuid, errDesc), time)
        }
    }
    //----------------服务发现 end-----------

    //----------------服务监听 start-----------
    fun dotPerListenStart(mac: String?, uuid: String, type: String, time: Long) {
        logTaskQueue.addTask {
            dotPerListen.doStart(arrayOf(mac, uuid, type), time)
        }
    }

    fun dotPerListenSuccess(mac: String?, uuid: String, time: Long) {
        logTaskQueue.addTask {
            dotPerListen.doSuccess(arrayOf(mac, uuid), time)
        }
    }

    fun dotPerListenFail(mac: String?, uuid: String, errDesc: String?, time: Long) {
        logTaskQueue.addTask {
            dotPerListen.doFail(arrayOf(mac, uuid, errDesc), time)
        }
    }

    @Synchronized
    fun dotAllListenTime(mac: String?, startTime: Long, endTime: Long) {
        logTaskQueue.addTask {
            runWithExceptionHandling {
                val consumeTime = endTime - startTime
                val content = "mac:$mac, 耗时:${consumeTime}ms\n"
                LogFileUtils.writeLogLine(PATH_SERVICE_ALL_LISTEN_TIME_CONSUME, "", content)
            }
        }
    }

    //----------------服务监听 end-----------

    //----------------指令发送 start-----------
    fun dotCmdStart(mac: String?, uuid: String, type: String, time: Long) {
        logTaskQueue.addTask {
            dotCmd.doStart(arrayOf(mac, uuid, type), time)
        }
    }

    fun dotCmdSuccess(mac: String?, uuid: String, time: Long) {
        logTaskQueue.addTask {
            dotCmd.doSuccess(arrayOf(mac, uuid), time)
        }
    }

    fun dotCmdFail(mac: String?, uuid: String, errDesc: String?, time: Long) {
        logTaskQueue.addTask {
            dotCmd.doFail(arrayOf(mac, uuid, errDesc), time)
        }
    }

    fun dotCmdEnd(mac: String?, uuid: String, time: Long) {
        logTaskQueue.addTask {
            dotCmd.doEnd(arrayOf(mac, uuid), time)
        }
    }
    //----------------指令发送 end-----------

    //----------------指令发送 start-----------
    fun dotSoundStart(mac: String?, uuid: String, type: String, time: Long) {
        logTaskQueue.addTask {
            dotSoundSend.doStart(arrayOf(mac, uuid, type), time)
        }
    }

    fun dotSoundSuccess(mac: String?, uuid: String, time: Long) {
        logTaskQueue.addTask {
            dotSoundSend.doSuccess(arrayOf(mac, uuid), time)
        }
    }

    fun dotSoundFail(mac: String?, uuid: String, errDesc: String?, time: Long) {
        logTaskQueue.addTask {
            dotSoundSend.doFail(arrayOf(mac, uuid, errDesc), time)
        }
    }
    //----------------指令发送 end-----------

    @Synchronized
    fun dotDisconnect(time: Long, mac: String?, desc: String) {
        logTaskQueue.addTask {
            runWithExceptionHandling {
                val content =
                    "开始时间:${ConsumeData.timeStamp2Date(time)}, mac:$mac, desc:${desc}\n"
                LogFileUtils.writeToFile(PATH_DISCONNECT, content)
            }
        }
    }

    //----------------音频数据发送速度 start-----------
    @Synchronized
    fun dotSoundSendTime(starTime: Long, endTime: Long, dataSize: Int) {
        logTaskQueue.addTask {
            runWithExceptionHandling {
                val consumeTime = endTime - starTime
                val content =
                    "数据长度:${dataSize}  耗时:${consumeTime}ms  发送速度:${(dataSize.toFloat() / consumeTime) * 1000} byte/s\n"
                LogFileUtils.writeToFile(PATH_SOUND_TIME_SPEED, content)
            }
        }
    }
    //----------------音频数据发送速度 end-----------
}