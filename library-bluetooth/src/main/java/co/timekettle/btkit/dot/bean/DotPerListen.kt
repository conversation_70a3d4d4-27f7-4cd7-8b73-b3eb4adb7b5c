package co.timekettle.btkit.dot.bean

import co.timekettle.btkit.dot.ConsumeData
import co.timekettle.btkit.dot.LogFileUtils

/**
 * author: weiconglee
 **/
class DotPerListen : BaseDot() {

    override fun dotStart(strArray: Array<String?>, time: Long) {
        val mac = strArray[0]
        val uuid = strArray[1]
        val type = strArray[2]
        if (mac.isNullOrBlank()) return
        log("服务监听开始 $mac")
        if (rateData == null) rateData = getRatedDot(path)
        val connConsume = mapConsume.getOrPut(uuid) { ConsumeData() }
        connConsume.mac = mac.toString()
        connConsume.tStart = time
        connConsume.type = type
    }


    override fun dotSuccess(strArray: Array<String?>, time: Long) {
        val mac = strArray[0]
        val uuid = strArray[1]
        if (mac.isNullOrBlank()) return
        log("服务监听成功 $mac")
        val consumeData = mapConsume.getOrPut(uuid) { ConsumeData() }
        val dotData = rateData ?: getRatedDot(path)
        dotData.success += 1
        consumeData.tSuccess = time
        LogFileUtils.writeLogLine(
            path,
            dotData.toString(),
            consumeData.toString()
        )
        mapConsume.remove(uuid)
    }


    override fun dotFail(strArray: Array<String?>, time: Long) {
        val mac = strArray[0]
        val uuid = strArray[1]
        val desc = strArray[2]
        if (mac.isNullOrBlank()) return
        log("服务监听失败 $mac")
        val consumeData = mapConsume.getOrPut(uuid) { ConsumeData() }
        val dotData = rateData ?: getRatedDot(path)
        consumeData.tFail = time
        consumeData.desc = desc
        dotData.fail += 1
        LogFileUtils.writeLogLine(
            path,
            dotData.toString(),
            consumeData.toString()
        )
        mapConsume.remove(uuid)
    }

}