package co.timekettle.btkit.dot.bean

import android.util.Log
import co.timekettle.btkit.dot.ConsumeData
import co.timekettle.btkit.dot.DotExceptionHandle
import co.timekettle.btkit.dot.LogFileUtils
import co.timekettle.btkit.dot.RateData
import java.util.Hashtable

/**
 * author: weiconglee
 **/
abstract class BaseDot : DotExceptionHandle() {
    val TAG = "BleAnalyzer"

    private var openLog = false

    protected var mapConsume = Hashtable<String, ConsumeData>()
    protected var rateData: RateData? = null
    protected var path: String = ""


    protected open fun dotStart(strArray: Array<String?>, time: Long) {}

    protected open fun dotSuccess(strArray: Array<String?>, time: Long) {}

    protected open fun dotFail(strArray: Array<String?>, time: Long) {}

    protected open fun dotEnd(strArray: Array<String?>, time: Long) {}

    fun doStart(strArray: Array<String?>, time: Long) {
        runWithExceptionHandling {
            dotStart(strArray, time)
        }
    }

    fun doSuccess(strArray: Array<String?>, time: Long) {
        runWithExceptionHandling {
            dotSuccess(strArray, time)
        }
    }

    fun doFail(strArray: Array<String?>, time: Long) {
        runWithExceptionHandling {
            dotFail(strArray, time)
        }
    }

    fun doEnd(strArray: Array<String?>, time: Long) {
        runWithExceptionHandling {
            dotEnd(strArray, time)
        }
    }

    fun setFilePath(path: String) {
        this.path = path
        rateData = null
    }

    protected fun getRatedDot(fileName: String): RateData {
        val fileContentFirstLine = LogFileUtils.readFile(fileName).substringBefore("\n")
        log("文件第一行内容：$fileContentFirstLine")
        val mRateData = RateData()
        if (fileContentFirstLine.trim().isEmpty()) {
            return mRateData
        }
        val split = fileContentFirstLine.split(",")
        mRateData.success = split[1].split("：")[1].toInt()
        mRateData.fail = split[2].split("：")[1].toInt()
        return mRateData
    }


    protected fun log(msg: String) {
        if (openLog) {
            Log.d(TAG, msg)
        }
    }
}