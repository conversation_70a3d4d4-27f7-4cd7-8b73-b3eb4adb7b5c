package co.timekettle.btkit.dot

import android.annotation.SuppressLint
import java.text.SimpleDateFormat
import java.util.Date

/**
 * author: weiconglee
 **/
data class ConsumeData(
    var mac: String = "",
    var type: String? = "",
    var tStart: Long = 0L,
    var tSuccess: Long = 0L,
    var tFail: Long = 0L,
    var desc: String? = ""
) {
    companion object {
        @SuppressLint("SimpleDateFormat")
        fun timeStamp2Date(timeStamp: Long): String {
            return SimpleDateFormat("HH:mm:ss.SSS").format(Date(timeStamp))
        }
    }

    override fun toString(): String {
        val consumeTime = if (tSuccess == 0L) tFail - tStart else tSuccess - tStart
        val endTimeStr = if (tSuccess == 0L) "失败时间:${timeStamp2Date(tFail)}" else "成功时间:${
            timeStamp2Date(tSuccess)
        }"
        val result = tSuccess != 0L
        return "开始时间:${timeStamp2Date(tStart)}, ${endTimeStr}, mac:$mac,${if (type.isNullOrEmpty()) "" else " type:$type,"} 耗时:${consumeTime}ms,${if (desc.isNullOrEmpty()) "" else " desc:$desc,"} 是否成功：$result"
    }
}