package co.timekettle.btkit;

import androidx.annotation.NonNull;

import java.io.ByteArrayOutputStream;
import java.io.IOException;

import static co.timekettle.btkit.BleCmdContant.UUIDWith;

/*
 W 系列指令内容      header(0x74,0x6b)     cmd       paramLen  [+ param]
 W 系列指令长度         2                   2           1
 ------------------------------
 M 系列指令内容      裸数据
 */
public class BleCmd {
   enum Type {
      READ("read"),
      WRITE("write"),
      NOTIFY("notify"),
      NONE("none");

      private final String mName;
      Type(final String name) {
         mName = name;
      }

      @NonNull
      @Override
      public String toString() {
         return mName;
      }
   }

   public Type type; // 读写类型
   public String id;  // id 也是指令描述
   public String service = UUIDWith("0004"); // 服务 uuid
   public String characteristic = UUIDWith("0005"); // 特征 uuid

   // W 系列 指令内容 header + cmd + paramLen [+ param], 若 header 为空, 则是裸数据传输
   byte[] header = {0x74, 0x6b};
   byte[] cmd; // 指令数据
   int paramLen; // 参数长度
   byte[] param; // 参数, 默认是 0, 占位使用
   byte[] values; // 有效参数数值的数组, 用于参数长度是 1 的时候

   static String default_service = UUIDWith("0004"); // W 系列服务 uuid
   static String default_characteristic = UUIDWith("0005"); // W 系列特征 uuid

   private String cmdTag;
   public String getCmdTag() {
      if (this.type == Type.READ) return null;
      if (cmdTag == null) {
         cmdTag = cmd != null ? BleUtil.toHexStr(cmd, "-") : null;
      }
      return cmdTag;
   }

   public byte[] getData() {
      assert this.type == Type.WRITE;
      if (this.header == null) return new byte[]{};

      ByteArrayOutputStream array = new ByteArrayOutputStream();
      try {
         array.write(this.header);
         array.write(this.cmd);
         array.write(this.paramLen);
         if (this.paramLen > 0) {
            assert this.param.length > 0 : "缺少参数, 使用 getData(byte[] param)";
            array.write(this.param);
         }
      } catch (IOException e) {
         e.printStackTrace();
      }
      return array.toByteArray();
   }

   public byte[] getData(byte[] param) {
      assert this.type == Type.WRITE;
      if (this.header == null) return param;
      ByteArrayOutputStream array = new ByteArrayOutputStream();
      try {
         array.write(this.header);
         array.write(this.cmd);
         array.write(this.paramLen);
         if (this.paramLen > 0) {
            assert param != null && param.length > 0 : "缺少参数";
            array.write(param);
         }
      } catch (IOException e) {
         e.printStackTrace();
      }
      return array.toByteArray();
   }

   byte[] getMSeriesData(byte[] param) {
      assert this.type == Type.WRITE;
      if (this.header == null) return param;
      ByteArrayOutputStream array = new ByteArrayOutputStream();
      try {
         array.write(this.header);
         array.write(this.cmd);
         if (this.paramLen > 0) {
            array.write(this.paramLen);
            assert param != null && param.length > 0 : "缺少参数";
            array.write(param);
         }      } catch (IOException e) {
         e.printStackTrace();
      }
      return array.toByteArray();
   }

   public int getParamLen() {
      return paramLen;
   }

   BleCmd(Type type, String id) {
      this.type = type;
      this.id = id;
      this.service = BleCmd.default_service;
      this.characteristic = BleCmd.default_characteristic;
   }

   BleCmd(Type type, String id, String service, String characteristic) {
      this.type = type;
      this.id = id;
      this.service = service;
      this.characteristic = characteristic;
   }

   static BleCmd ofRead(String id, String service, String characteristic) {
      BleCmd wCmd = new BleCmd(Type.READ, id, service, characteristic);
//        wCmd.type = BleCmdType.read
//        wCmd.id = id
//        wCmd.service = service
//        wCmd.characteristic = characteristic
      return wCmd;
   }

   static BleCmd ofWrite(String id, byte[] cmd) {
      BleCmd wCmd = new BleCmd(Type.WRITE, id);
//        wCmd.type = BleCmdType.write
//        wCmd.id = id
//        cmd.service = service
//        cmd.characteristic = characteristic
//        cmd.header = characteristic
      wCmd.cmd = cmd;
//        wCmd.paramLen = paramLen
//        wCmd.param = param
      return wCmd;
   }

   static BleCmd ofNotify(String id, byte[] cmd) {
      BleCmd wCmd = new BleCmd(Type.WRITE, id);
//        wCmd.type = BleCmdType.write
//        wCmd.id = id
//        cmd.service = service
//        cmd.characteristic = characteristic
//        cmd.header = characteristic
      wCmd.cmd = cmd;
//        wCmd.paramLen = paramLen
//        wCmd.param = param
      return wCmd;
   }

   static BleCmd ofWrite(String id, byte[] cmd, int paramLen, byte[] param) {
      BleCmd wCmd = new BleCmd(Type.WRITE, id);
//        wCmd.type = BleCmdType.write
//        wCmd.id = id
//        cmd.service = service
//        cmd.characteristic = characteristic
//        cmd.header = characteristic
      wCmd.cmd = cmd;
      wCmd.paramLen = paramLen;
      wCmd.param = param;
      return wCmd;
   }

   static BleCmd ofWrite(String id, byte[] cmd, int paramLen, byte[] param, byte[] values) {
      BleCmd wCmd = new BleCmd(Type.WRITE, id);
//        wCmd.type = BleCmdType.write
//        wCmd.id = id
//        cmd.service = service
//        cmd.characteristic = characteristic
//        cmd.header = characteristic
      wCmd.cmd = cmd;
      wCmd.paramLen = paramLen;
      wCmd.param = param;
      wCmd.values = values;
      return wCmd;
   }

   static BleCmd ofWrite(String id, String service, String characteristic) {
      BleCmd wCmd = new BleCmd(Type.WRITE, id, service, characteristic);
//        wCmd.type = BleCmdType.write
//        wCmd.id = id
//        wCmd.service = service
//        wCmd.characteristic = characteristic
      wCmd.header = null;
      wCmd.cmd = null;
//      wCmd.paramLen = paramLen;
//      wCmd.param = param;
//      wCmd.values = values;
      return wCmd;
   }

   static BleCmd ofWrite(String id, String service, String characteristic, byte[] cmd) {
      BleCmd wCmd = new BleCmd(Type.WRITE, id, service, characteristic);
//        wCmd.type = BleCmdType.write
//        wCmd.id = id
//        wCmd.service = service
//        wCmd.characteristic = characteristic
      wCmd.header = new byte[0];
      wCmd.cmd = cmd;
//      wCmd.paramLen = paramLen;
//      wCmd.param = param;
//      wCmd.values = values;
      return wCmd;
   }

   static BleCmd ofNotify(String id, String service, String characteristic, byte[] cmd) {
      BleCmd wCmd = new BleCmd(Type.NOTIFY, id, service, characteristic);
//        wCmd.type = BleCmdType.write
//        wCmd.id = id
//        wCmd.service = service
//        wCmd.characteristic = characteristic
      wCmd.header = new byte[0];
      wCmd.cmd = cmd;
//      wCmd.paramLen = paramLen;
//      wCmd.param = param;
//      wCmd.values = values;
      return wCmd;
   }

   static BleCmd ofWrite(String id, String service, String characteristic, byte[] header, byte[] cmd, int paramLen, byte[] param, byte[] values) {
      BleCmd wCmd = new BleCmd(Type.WRITE, id, service, characteristic);
//        wCmd.type = BleCmdType.write
//        wCmd.id = id
//        wCmd.service = service
//        wCmd.characteristic = characteristic
      wCmd.header = header;
      wCmd.cmd = cmd;
      wCmd.paramLen = paramLen;
      wCmd.param = param;
      wCmd.values = values;
      return wCmd;
   }

}
