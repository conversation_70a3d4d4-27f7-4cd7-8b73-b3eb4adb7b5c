
package co.timekettle.btkit.dfu;

import android.annotation.SuppressLint;
import android.app.NotificationManager;
import android.content.Context;
import android.os.Build;
import android.os.Handler;
import android.util.Log;

import androidx.annotation.Nullable;

import java.io.File;
import java.util.HashMap;

import co.timekettle.btkit.LogUtil;
import no.nordicsemi.android.dfu.*;

public class DfuUtil {
    public static final String TAG = "NordicDfuUtil";

    private final String dfuStateEvent = "DFUStateChanged";
    private final String progressEvent = "DFUProgress";

    private DfuServiceController controller;
    private DfuServiceInitiator starter;
    private Promise mPromise = null;
    private Context context;
    private boolean isDebug = false;

    @SuppressLint("StaticFieldLeak")
    public static final DfuUtil shared = new DfuUtil();

    public void showLog(boolean open) {
        isDebug = open;
    }

    public void init(Context context) {
        if (this.context == null) {
            this.context = context.getApplicationContext();
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
                DfuServiceInitiator.createDfuNotificationChannel(context);
            }
        }
    }

    public void startDFU(String address, String name, String filePath, Promise promise) {
        LogUtil.d(TAG, "startDFU: " + address + " " + name + " " + filePath + " " + new File(filePath).exists());

        mPromise = promise;
        DfuServiceListenerHelper.registerProgressListener(context, mDfuProgressListener);
        starter = new DfuServiceInitiator(address).setKeepBond(false);
        if (name != null) {
            starter.setDeviceName(name);
        }
        starter.setUnsafeExperimentalButtonlessServiceInSecureDfuEnabled(true);
        starter.setZip(filePath);
        controller = starter.start(context, DfuService.class);
    }


    /**
     * 停止dfu升级
     */
    public void stopDfu() {
        if (controller != null) {
            controller.abort();
            DfuServiceListenerHelper.unregisterProgressListener(context, mDfuProgressListener);
        }
    }

    private void sendEvent(String eventName, @Nullable HashMap params) {
        if (isDebug)
            LogUtil.d(TAG, "sendEvent: " + eventName + " " + params.toString());
    }

    private void sendStateUpdate(String state, String deviceAddress) {
        HashMap map = new HashMap();
        map.put("state", state);
        map.put("deviceAddress", deviceAddress);
        sendEvent(dfuStateEvent, map);
    }


//    @Override
//    public void onHostResume() {
//    }
//
//    @Override
//    public void onHostPause() {
//    }
//
//    @Override
//    public void onHostDestroy() {
//        DfuServiceListenerHelper.unregisterProgressListener(this.context, mDfuProgressListener);
//    }


    private final DfuProgressListener mDfuProgressListener = new DfuProgressListenerAdapter() {
        @Override
        public void onDeviceConnecting(final String deviceAddress) {
            sendStateUpdate("CONNECTING", deviceAddress);
        }

        @Override
        public void onDfuProcessStarting(final String deviceAddress) {
            sendStateUpdate("DFU_PROCESS_STARTING", deviceAddress);
        }

        @Override
        public void onEnablingDfuMode(final String deviceAddress) {
            sendStateUpdate("ENABLING_DFU_MODE", deviceAddress);
        }

        @Override
        public void onFirmwareValidating(final String deviceAddress) {
            sendStateUpdate("FIRMWARE_VALIDATING", deviceAddress);
        }

        @Override
        public void onDeviceDisconnecting(final String deviceAddress) {
            sendStateUpdate("DEVICE_DISCONNECTING", deviceAddress);
        }

        @Override
        public void onDfuCompleted(final String deviceAddress) {
            if (mPromise != null) {
                mPromise.onComplete(deviceAddress);
                mPromise = null;
            }
            sendStateUpdate("DFU_COMPLETED", deviceAddress);

            // FIXME: 2022/5/25 子线程会有问题?
            new Handler().postDelayed(new Runnable() {
                @Override
                public void run() {

                    // if this activity is still open and upload process was completed, cancel the notification
                    final NotificationManager manager = (NotificationManager) context.getSystemService(Context.NOTIFICATION_SERVICE);
                    manager.cancel(DfuService.NOTIFICATION_ID);
                }
            }, 200);
        }

        @Override
        public void onDfuAborted(final String deviceAddress) {
            sendStateUpdate("DFU_ABORTED", deviceAddress);
            if (mPromise != null) {
                mPromise.onFail(deviceAddress, "2", "DFU ABORTED");
                mPromise = null;
            }
        }

        @Override
        public void onProgressChanged(final String deviceAddress, final int percent, final float speed, final float avgSpeed, final int currentPart, final int partsTotal) {
            HashMap map = new HashMap();
            map.put("deviceAddress", deviceAddress);
            map.put("percent", percent);
            map.put("speed", speed);
            map.put("avgSpeed", avgSpeed);
            map.put("currentPart", currentPart);
            map.put("partsTotal", partsTotal);
            sendEvent(progressEvent, map);

            if (mPromise != null) {
                mPromise.onProgressChanged(deviceAddress, percent);
            }
        }

        @Override
        public void onError(final String deviceAddress, final int error, final int errorType, final String message) {
            sendStateUpdate("DFU_FAILED", deviceAddress);
            if (mPromise != null) {
                mPromise.onFail(deviceAddress, Integer.toString(error), message);
                mPromise = null;
            }
        }
    };


    public interface Promise {

        void onComplete(String deviceAddress);

        void onProgressChanged(String deviceAddress, final int percent);

        void onFail(String deviceAddress, String code, String message);
    }
}