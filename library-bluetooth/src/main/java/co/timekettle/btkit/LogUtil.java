package co.timekettle.btkit;

import android.util.Log;

public class LogUtil {
   static int logLevel = 0; // 0 表示关闭, 1 表示 error 级别, 2 表示 debug 级别
   public static BleLogCallback mCallback = null; // 根据设置等级进行回调
   public static void setLogLevel(int l) {
      logLevel = l;
   }
   public static void setLogCallback(BleLogCallback callback) {
      mCallback = callback;
   }

   public static int d(String tag, String msg) {
      if (logLevel < 2) return 0;

      if (mCallback != null) {
         mCallback.invoke(2, tag, msg, null);
         return 0;
      } else {
         return Log.d(tag, msg);
      }
   }

   public static int d(String tag, String msg, Throwable tr) {
      if (logLevel < 2) return 0;

      if (mCallback != null) {
         mCallback.invoke(2, tag, msg, tr);
         return 0;
      } else {
         return Log.d(tag, msg, tr);
      }
   }

   public static int e(String tag, String msg) {
      if (logLevel < 1) return 0;

      if (mCallback != null) {
         mCallback.invoke(1, tag, msg, null);
         return 0;
      } else {
         return Log.e(tag, msg);
      }
   }

   public static int e(String tag, String msg, Throwable tr) {
      if (logLevel < 1) return 0;

      if (mCallback != null) {
         mCallback.invoke(1, tag, msg, tr);
         return 0;
      } else {
         return Log.e(tag, msg, tr);
      }
   }

   public interface BleLogCallback {
      void invoke(int level, String tag, String msg, Throwable tr);
   }
}
