package co.timekettle.btkit.bluetoothlib.bluetooth.event;

import java.util.HashMap;

import co.timekettle.btkit.bluetoothlib.bluetooth.BluetoothState;

public class BluetoothStateEvent extends BluetoothEvent {

    private BluetoothState state;

    public BluetoothStateEvent(BluetoothState state) {
        super(BluetoothState.ENABLED == state
                ? EventType.BLUETOOTH_ENABLED : EventType.BLUETOOTH_DISABLED);
        this.state = state;
    }


    @Override
    public HashMap buildMap() {
        HashMap map = new HashMap();
        map.put("state", state.name());
        map.put("enabled", BluetoothState.ENABLED == state);
        return map;
    }
}
