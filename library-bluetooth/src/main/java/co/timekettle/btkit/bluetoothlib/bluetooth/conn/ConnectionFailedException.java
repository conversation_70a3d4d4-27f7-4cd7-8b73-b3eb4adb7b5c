package co.timekettle.btkit.bluetoothlib.bluetooth.conn;

import co.timekettle.btkit.bluetoothlib.bluetooth.BluetoothException;
import co.timekettle.btkit.bluetoothlib.bluetooth.Exceptions;
import co.timekettle.btkit.bluetoothlib.bluetooth.device.NativeDevice;

/**
 * Connection failed.
 *
 * <AUTHOR>
 */
public class ConnectionFailedException extends BluetoothException {

    public ConnectionFailedException(NativeDevice device, Throwable error) {
        super(device,
                Exceptions.CONNECTION_FAILED.message(device.getAddress()),
                error);
    }
}
