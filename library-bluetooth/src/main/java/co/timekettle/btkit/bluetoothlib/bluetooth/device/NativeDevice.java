package co.timekettle.btkit.bluetoothlib.bluetooth.device;

import android.bluetooth.BluetoothClass;
import android.bluetooth.BluetoothDevice;
import android.os.ParcelUuid;

import java.util.HashMap;
import java.util.Map;

import co.timekettle.btkit.bluetoothlib.bluetooth.Mappable;

/**
 * Provides wrapping of {@link BluetoothDevice} details and communication.
 * Primarily used for providing the {@link Mappable#map()} method.
 *
 * <AUTHOR>
 */
public class NativeDevice implements Mappable {

    private BluetoothDevice mDevice;
    private Map<String,Object> mExtra;

    public NativeDevice(BluetoothDevice device) {
        this.mDevice = device;
        this.mExtra = new HashMap<>();
    }

    public BluetoothDevice getDevice() { return mDevice; }

    public String getAddress() {
        return mDevice.getAddress();
    }

    public String getName() {
        return mDevice.getName();
    }

    public int getBondState() {
        return mDevice.getBondState();
    }

    public BluetoothClass getBluetoothClass() {
        return mDevice.getBluetoothClass();
    }

    public ParcelUuid[] getUuids() {
        return mDevice.getUuids();
    }

    public <T> T getExtra(String key) {
        return (T) mExtra.get(key);
    }

    public <T> T putExtra(String key, T value) {
        return (T) mExtra.put(key, value);
    }

    @Override
    public HashMap map() {
        HashMap mapped = new HashMap();

        mapped.put("name", mDevice.getName() != null ? mDevice.getName() : mDevice.getAddress());
        mapped.put("address", mDevice.getAddress());
        mapped.put("id", mDevice.getAddress());
        mapped.put("bonded", mDevice.getBondState() == BluetoothDevice.BOND_BONDED);

        if (mDevice.getBluetoothClass() != null) {
            HashMap deviceClass = new HashMap();
            deviceClass.put("deviceClass", mDevice.getBluetoothClass().getDeviceClass());
            deviceClass.put("majorClass", mDevice.getBluetoothClass().getMajorDeviceClass());
        }

        mapped.put("extra", mExtra);

        return mapped;
    }
}
