package co.timekettle.btkit.bluetoothlib.bluetooth.conn;

import co.timekettle.btkit.bluetoothlib.bluetooth.BluetoothException;
import co.timekettle.btkit.bluetoothlib.bluetooth.Exceptions;
import co.timekettle.btkit.bluetoothlib.bluetooth.device.NativeDevice;

/**
 * Connection lost.
 *
 * <AUTHOR>
 */
public class ConnectionLostException extends BluetoothException {

    public ConnectionLostException(NativeDevice device, Throwable e) {
        super(device,
                Exceptions.CONNECTION_LOST.message(device.getAddress()),
                e);
    }
}
