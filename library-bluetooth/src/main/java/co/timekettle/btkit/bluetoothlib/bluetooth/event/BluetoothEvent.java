package co.timekettle.btkit.bluetoothlib.bluetooth.event;

import java.util.Calendar;
import java.util.HashMap;

import co.timekettle.btkit.bluetoothlib.bluetooth.Mappable;
import co.timekettle.btkit.bluetoothlib.bluetooth.Utilities;

public abstract class BluetoothEvent implements Mappable {

    private EventType eventType;

    public BluetoothEvent(EventType eventType) {
        this.eventType = eventType;
    }

    @Override
    public HashMap map() {
        HashMap map = new HashMap();
        map.put("eventType", eventType.name());
        map.put("timestamp", Utilities.formatDate(Calendar.getInstance().getTime()));
        map.putAll(buildMap());
        return map;
    }

    /**
     * Applies custom information for the event.
     *
     * @return
     */
    public abstract HashMap buildMap();

}
