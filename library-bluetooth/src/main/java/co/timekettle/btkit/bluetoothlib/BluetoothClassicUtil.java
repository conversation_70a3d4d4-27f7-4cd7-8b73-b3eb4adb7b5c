package co.timekettle.btkit.bluetoothlib;

import android.Manifest;
import android.app.Activity;
import android.bluetooth.BluetoothAdapter;
import android.bluetooth.BluetoothDevice;
import android.bluetooth.BluetoothSocket;
import android.content.ActivityNotFoundException;
import android.content.BroadcastReceiver;
import android.content.Context;
import android.content.Intent;
import android.content.IntentFilter;
import android.content.pm.PackageManager;
import android.net.MacAddress;
import android.os.Build;
import android.os.Handler;
import android.os.HandlerThread;
import android.provider.Settings;
import android.text.TextUtils;
import android.util.Base64;
import android.util.Log;

import androidx.core.app.ActivityCompat;

import org.greenrobot.eventbus.EventBus;

import java.io.IOException;
import java.lang.reflect.InvocationTargetException;
import java.lang.reflect.Method;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collection;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Properties;
import java.util.Set;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.Semaphore;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicInteger;

import javax.xml.transform.Result;

import co.timekettle.btkit.BleCmdContant;
import co.timekettle.btkit.BluetoothCmd;
import co.timekettle.btkit.bean.RawBlePeripheral;
import co.timekettle.btkit.bean.SppEventBean;
import co.timekettle.btkit.bluetoothlib.android.BiConsumer;
import co.timekettle.btkit.bluetoothlib.bluetooth.BluetoothException;
import co.timekettle.btkit.bluetoothlib.bluetooth.BluetoothRequest;
import co.timekettle.btkit.bluetoothlib.bluetooth.BluetoothState;
import co.timekettle.btkit.bluetoothlib.bluetooth.DevicePairingException;
import co.timekettle.btkit.bluetoothlib.bluetooth.Exceptions;
import co.timekettle.btkit.bluetoothlib.bluetooth.InvalidBluetoothEventException;
import co.timekettle.btkit.bluetoothlib.bluetooth.Utilities;
import co.timekettle.btkit.bluetoothlib.bluetooth.conn.AcceptFailedException;
import co.timekettle.btkit.bluetoothlib.bluetooth.conn.ByteArrayDeviceConnectionImpl;
import co.timekettle.btkit.bluetoothlib.bluetooth.conn.ConnectionAcceptor;
import co.timekettle.btkit.bluetoothlib.bluetooth.conn.ConnectionAcceptorFactory;
import co.timekettle.btkit.bluetoothlib.bluetooth.conn.ConnectionConnector;
import co.timekettle.btkit.bluetoothlib.bluetooth.conn.ConnectionConnectorFactory;
import co.timekettle.btkit.bluetoothlib.bluetooth.conn.ConnectionFailedException;
import co.timekettle.btkit.bluetoothlib.bluetooth.conn.DelimitedStringDeviceConnectionImpl;
import co.timekettle.btkit.bluetoothlib.bluetooth.conn.DeviceConnection;
import co.timekettle.btkit.bluetoothlib.bluetooth.conn.DeviceConnectionFactory;
import co.timekettle.btkit.bluetoothlib.bluetooth.conn.RfcommAcceptorThreadImpl;
import co.timekettle.btkit.bluetoothlib.bluetooth.conn.RfcommConnectorThreadImpl;
import co.timekettle.btkit.bluetoothlib.bluetooth.conn.StandardOption;
import co.timekettle.btkit.bluetoothlib.bluetooth.device.NativeDevice;
import co.timekettle.btkit.bluetoothlib.bluetooth.event.EventType;
import co.timekettle.btkit.bluetoothlib.bluetooth.receiver.ActionACLReceiver;
import co.timekettle.btkit.bluetoothlib.bluetooth.receiver.DiscoveryReceiver;
import co.timekettle.btkit.bluetoothlib.bluetooth.receiver.PairingReceiver;
import co.timekettle.btkit.bluetoothlib.bluetooth.receiver.StateChangeReceiver;
import co.timekettle.btkit.bluetoothlib.bluetooth.tools.BluetoothHelper;
import co.timekettle.btkit.bluetoothlib.bluetooth.tools.IBTConnectListener;
import co.timekettle.btkit.bluetoothlib.bluetooth.tools.IBluetoothHelper;
import no.nordicsemi.android.dfu.BuildConfig;

@SuppressWarnings({"WeakerAccess"})
public class BluetoothClassicUtil implements StateChangeReceiver.StateChangeCallback, ActionACLReceiver.ActionACLCallback {

    /**
     * Name of the module when provided to React Native {@code NativeModules}.
     */
    public static final String MODULE_NAME = "RNBluetoothClassic";

    /**
     * Logging definition.
     */
    private static final String TAG = BluetoothClassicUtil.class.getSimpleName();

    /**
     * Local access to the default {@link BluetoothAdapter}.  Generally we just need to check things
     * like:
     * <ul>
     *     <li>Is bluetooth enabled?</li>
     *     <li>Is bluetooth searching / advertising?</li>
     *     <li>Etc.</li>
     * </ul>
     */
    private final BluetoothAdapter mAdapter;

    /**
     * Provides {@link ConnectionAcceptorFactory}(s) to {@link #accept} method.
     */
    private final Map<String, ConnectionAcceptorFactory> mAcceptorFactories;

    /**
     * Provides a map of all available {@link ConnectionConnectorFactory} available to the
     * {@link #connectToDevice} method.   A {@link ConnectionConnector} is first started, then upon
     * completion the {@link BluetoothSocket} is passed into the requested {@link DeviceConnection}.
     */
    private final Map<String, ConnectionConnectorFactory> mConnectorFactories;

    /**
     * Provides a map of all the available {@link DeviceConnectionFactory} available to the
     * {@link #connectToDevice} method.
     */
    private final Map<String, DeviceConnectionFactory> mConnectionFactories;

    /**
     * Manages {@link DeviceConnection} wrapping {@link BluetoothDevice} by
     * {@link BluetoothDevice#getAddress()}.  Currently the initial capacity is 1, since the main
     * goal of this was a simple connection.  This may need to be updated to have the default
     * size updated during package creation.
     */
    private Map<String, DeviceConnection> mConnections;

    /**
     * Maintains a map of {@link ConnectionConnector}(s) keyed on {@link BluetoothDevice} address.
     * Connectors are added during the {@link #connectToDevice} request and removed when either
     * successful or failed.
     */
    private Map<String, ConnectionConnector> mConnecting;

    /**
     * Manages intents while the application and {@link BluetoothAdapter} are in discovery mode.
     * This will be cancelled when the application is paused or ends discovery.
     */
    private BroadcastReceiver mDiscoveryReceiver;

    /**
     * Intent receiver responsible for handling changes to BluetoothAdapter state (on/off).  Fires
     * an event to the ReactNative emitter based on the new state.
     * https://developer.android.com/reference/android/bluetooth/BluetoothAdapter#ACTION_STATE_CHANGED
     */
    private BroadcastReceiver mStateChangeReceiver;

    /**
     * Intent receiver responsible for handling changes to Bluetooth connections.  This Intent is
     * fired when the BluetoothAdapter connection state to any device changes.  It fires an event
     * to the ReactNative emitter containing the state and deviceId which was connected.
     * https://developer.android.com/reference/android/bluetooth/BluetoothAdapter#ACTION_CONNECTION_STATE_CHANGED
     */
    private BroadcastReceiver mActionACLReceiver;

    /**
     * Promise must be maintained across Activity requests for managing the enabled request
     * status.
     */
    private Promise mEnabledPromise;

    /**
     * Manage the number of listeners of a specific type - these event types are that of
     * the bluetooth mAdapter in general (connect, disconnect, etc.) and not those which are
     * reading.  Those are managed separately within the device itself.
     */
    private Map<String, AtomicInteger> mListenerCounts;

    /**
     * Maintains the {@link ConnectionAcceptor} when the module has been placed into accept
     * mode.   Only one type of {@link ConnectionAcceptor} is allowed at one time, regardless
     * of how many are configured.  Current accepting should be cancelled and restarted in order
     * to change the type.
     */
    private ConnectionAcceptor mAcceptor;
    private IBluetoothHelper mBluetoothHelper; // 根据Mac连接蓝牙的工具类
    //region: Constructors

    private Context mContext;

    public static final BluetoothClassicUtil shared = new BluetoothClassicUtil();

    /**
     * Creates the RNBlutoothClassicModule.  As a final step of initialization the appropriate
     * {@link EventType#BLUETOOTH_ENABLED}/{@link EventType#BLUETOOTH_DISABLED} is
     * sent and the activity and lifecyle listeners are register.
     */
    public BluetoothClassicUtil() {
        this.mConnectionFactories = new HashMap<String, DeviceConnectionFactory>() {{
            put("delimited", DelimitedStringDeviceConnectionImpl::new);
            put("binary", ByteArrayDeviceConnectionImpl::new);
        }};
        this.mAcceptorFactories = Collections.singletonMap(
                StandardOption.ACCEPTOR_TYPE.defaultValue(),
                RfcommAcceptorThreadImpl::new);
        this.mConnectorFactories = Collections.singletonMap(
                StandardOption.CONNECTOR_TYPE.defaultValue(),
                RfcommConnectorThreadImpl::new);

        this.mAdapter = BluetoothAdapter.getDefaultAdapter();

//        this.mAcceptorFactories = Collections.unmodifiableMap(acceptFactories);
//        this.mConnectorFactories = Collections.unmodifiableMap(connectFactories);
//        this.mConnectionFactories = Collections.unmodifiableMap(factories);
    }

    public void init(Context context) {
        mContext = context.getApplicationContext();

        this.mConnections = new ConcurrentHashMap<>(1);
        this.mConnecting = new ConcurrentHashMap<>(1);
        this.mListenerCounts = new ConcurrentHashMap<>();

//        if (mAdapter != null && mAdapter.isEnabled()) {
//            sendEvent(EventType.BLUETOOTH_ENABLED,
//                    new BluetoothStateEvent(BluetoothState.ENABLED).map());
//        } else {
//            sendEvent(EventType.BLUETOOTH_DISABLED,
//                    new BluetoothStateEvent(BluetoothState.DISABLED).map());
//        }

//        mContext.addActivityEventListener(this);
//        mContext.addLifecycleEventListener(this);
        mBluetoothHelper = new BluetoothHelper();
//        mBluetoothHelper.setBTStateListener(mBTStateListener);//设置打开关闭状态监听
//        mBluetoothHelper.setBTScanListener(mBTScanListener);//设置扫描监听
//        mBluetoothHelper.setBTBoudListener(mBTBoudListener);//设置配对监听
        mBluetoothHelper.init(context);
    }
    //endregion

    //region: Helper/Utility Methods
    private boolean checkBluetoothAdapter() {
        return (mAdapter != null && mAdapter.isEnabled());
    }
    //endregion

    //region: ActivityEventListener

    /**
     * Handles results from the requested Android Intents.  Currently there are only two activities
     * started for result:
     * <ul>
     * <li><strong>ENABLE_BLUETOOTH</strong> requests the user to enable Bluetooth from settings.</li>
     * <li><strong>PAIR_DEVICE</strong> after a user has completed pairing the device.</li>
     * </ul>
     * This sends a {@link EventType#BLUETOOTH_ENABLED} event.  It probably shouldn't duplicate
     * the promise but this gives the opportunity to do both things.
     *
     * @param activity    the activity which is returning the result
     * @param requestCode request code provided to the outgoing intent
     * @param resultCode  result of the requested Intent
     * @param data        the intent which triggered this result
     */
    public void onActivityResult(Activity activity, int requestCode, int resultCode, Intent data) {
        if (BuildConfig.DEBUG)
            Log.d(TAG, String.format("onActivityResult requestCode: %d resultCode: %d", requestCode, resultCode));

        if (requestCode == BluetoothRequest.ENABLE_BLUETOOTH.code) {
            if (resultCode == Activity.RESULT_OK) {
                if (BuildConfig.DEBUG)
                    Log.d(TAG, "User enabled Bluetooth");

                if (mEnabledPromise != null) {
                    mEnabledPromise.resolve(true);
//                    sendEvent(EventType.BLUETOOTH_ENABLED,
//                            new BluetoothStateEvent(BluetoothState.ENABLED).map());
                    if (listener != null)
                        listener.onBlueToothEnable();
                }
            } else {
                if (BuildConfig.DEBUG)
                    Log.d(TAG, "User did *NOT* enable Bluetooth");

                if (mEnabledPromise != null) {
                    mEnabledPromise.reject(new Exception("User did not enable Bluetooth"));
                }
            }
            mEnabledPromise = null;
        }
    }


    public void onNewIntent(Intent intent) {
        if (BuildConfig.DEBUG)
            Log.d(TAG, "onNewIntent: " + intent.getAction());
    }
    //endregion

    //region: LifecycleEventListener
    public void onHostResume() {
        if (BuildConfig.DEBUG)
            Log.d(TAG, "onHostResume: register Application receivers");
        registerBluetoothReceivers();
    }

    public void onHostPause() {
        if (BuildConfig.DEBUG)
            Log.d(TAG, "onHostPause: unregister receivers");
//        unregisterBluetoothReceivers(); // 在这里取消了监听
    }

    public void onHostDestroy() {
        if (BuildConfig.DEBUG)
            Log.d(TAG, "onHostDestroy: stop discovery, connections and unregister receivers");
        unregisterBluetoothReceivers();
        mAdapter.cancelDiscovery();
    }
    //endregion

    /**
     * Requests that the Android Bluetooth mAdapter be enabled.  If the mAdapter is already enabled
     * then the promise is returned true.  If the mAdapter is not enabled, an Intent request is sent
     * to Android (promised saved for use upon result).
     * <p>
     * Note that this does not inherently fire a state change event, as the manual act seems to
     * skip the StateChangeReceiver functionality.
     *
     * @param promise resolves <strong>true</strong> if Bluetooth is already enabled or when
     *                Bluetooth becomes enabled.  Rejects if anything else
     */
    public void requestBluetoothEnabled(Activity activity, Promise promise) {
        if (checkBluetoothAdapter()) {
            promise.resolve(true);
        } else {
//            Activity activity = getCurrentActivity();
            if (activity != null) {
                mEnabledPromise = promise;
                Intent intent = new Intent(BluetoothAdapter.ACTION_REQUEST_ENABLE);
                activity.startActivityForResult(intent, BluetoothRequest.ENABLE_BLUETOOTH.code);
            } else {
                ActivityNotFoundException e = new ActivityNotFoundException();
                mEnabledPromise.reject(e);
                mEnabledPromise = null;
            }
        }
    }

    /**
     * Determine whether Bluetooth is available.  The promise is never rejected, only resolved with the
     * appropriate boolean flag.
     * <p>
     * return based on Bluetooth being available on the device.
     */
    public void isBluetoothAvailable(Promise promise) {
        promise.resolve(mAdapter != null);
    }

    public boolean isBluetoothAvailable() {
        return mAdapter != null;
    }

    /**
     * Determine whether Bluetooth is enabled.  The promise is never rejected, only resolved with the
     * appropriate boolean flag.
     * <p>
     * return based on Bluetooth status
     */
    public void isBluetoothEnabled(Promise promise) {
        promise.resolve(checkBluetoothAdapter());
    }

    public boolean isBluetoothEnabled() {
        return checkBluetoothAdapter();
    }

    /**
     * Retrieves the currently bonded devices.  Bonded devices may or may not be connected.  This
     * method was refactored from <strong>list</strong> as there was a bunch of confusion with bonded
     * and connected devices.
     * <p>
     * return the list of bonded devices.
     */
    public void getBondedDevices(Promise promise) {
        if (!checkBluetoothAdapter()) {
            promise.reject(Exceptions.BLUETOOTH_NOT_ENABLED.name(),
                    Exceptions.BLUETOOTH_NOT_ENABLED.message());
        } else {
            ArrayList bonded = new ArrayList();
            for (BluetoothDevice device : mAdapter.getBondedDevices()) {
                NativeDevice nativeDevice = new NativeDevice(device);
                bonded.add(nativeDevice.map());
            }

            promise.resolve(bonded);
        }
    }

    public List<NativeDevice> getBondedDevices() {
        if (!checkBluetoothAdapter()) {
//            promise.reject(Exceptions.BLUETOOTH_NOT_ENABLED.name(),
//                    Exceptions.BLUETOOTH_NOT_ENABLED.message());
            return null;
        } else {
            ArrayList<NativeDevice> bonded = new ArrayList<NativeDevice>();
            for (BluetoothDevice device : mAdapter.getBondedDevices()) {
                NativeDevice nativeDevice = new NativeDevice(device);
                bonded.add(nativeDevice);
            }
            return bonded;
        }
    }

    /**
     * Lists all the currently connected devices.  Provides a {@link ArrayList}
     * of {@link BluetoothDevice}(s) which currently have an active/open connection.  Please note
     * that this does NOT list PAIRED devices, only those that are actually connected!
     * <p>
     * return the currently connected devices, may be empty.
     */
    public void getConnectedDevices(Promise promise) {
        ArrayList connected = new ArrayList();
        for (DeviceConnection connection : mConnections.values()) {
            connected.add(new NativeDevice(connection.getDevice()).map());
        }
//        Log.d(TAG, "getConnectedDevices: " + connected.toString());
        promise.resolve(connected);
    }

    public List<NativeDevice> getConnectedDevices() {
        ArrayList<NativeDevice> connected = new ArrayList<NativeDevice>();
        if (mConnections == null || mConnections.isEmpty()) {
            return connected;
        }
        for (DeviceConnection connection : mConnections.values()) {
            connected.add(new NativeDevice(connection.getDevice()));
        }
//        Log.d(TAG, "getConnectedDevices: " + connected.toString());
        return connected;
    }

    /**
     * Registers a {@link DiscoveryReceiver} and starts discovery.
     *
     * @param promise resolve or reject the request to discoverDevices
     */
    public void startDiscovery(final Promise promise) {
        if (!checkBluetoothAdapter()) {
            promise.reject(Exceptions.BLUETOOTH_NOT_ENABLED.name(),
                    Exceptions.BLUETOOTH_NOT_ENABLED.message());
        } else if (mDiscoveryReceiver != null) {
            promise.reject(Exceptions.BLUETOOTH_IN_DISCOVERY.name(),
                    Exceptions.BLUETOOTH_IN_DISCOVERY.message());
        } else {
            mDiscoveryReceiver = new DiscoveryReceiver(new DiscoveryReceiver.DiscoveryCallback() {
                @Override
                public void onDeviceDiscovered(NativeDevice device) {
                    // This wasn't previously an event, but now we can send out and request them
                    Log.d(TAG, String.format("Discovered device %s", device.getAddress()));
//                    sendEvent(EventType.DEVICE_DISCOVERED, device.map());
                    if (listener != null)
                        listener.onDiscovered(device);
                }

                @Override
                public void onDiscoveryFinished(Collection<NativeDevice> devices) {
                    ArrayList array = new ArrayList();
                    for (NativeDevice device : devices) {
                        array.add(device.map());
                    }

                    promise.resolve(array);
                    mDiscoveryReceiver = null;
                }

                @Override
                public void onDiscoveryFailed(Throwable e) {
                    promise.reject(Exceptions.DISCOVERY_FAILED.name(),
                            Exceptions.DISCOVERY_FAILED.message(e.getMessage()));
                    mDiscoveryReceiver = null;
                }
            });

            mContext.registerReceiver(mDiscoveryReceiver,
                    DiscoveryReceiver.intentFilter());

            mAdapter.startDiscovery();
        }
    }

    /**
     * Opens Android's Bluetooth Settings activity.
     */
    public void openBluetoothSettings() {
        Intent intent = new Intent(Settings.ACTION_BLUETOOTH_SETTINGS);
        if (intent.resolveActivity(mContext.getPackageManager()) != null) {
            intent.setFlags(Intent.FLAG_ACTIVITY_NEW_TASK);
            mContext.startActivity(intent);
        }
    }

    /**
     * 获取已连接的蓝牙设备的电量
     */
    public static void getBluetoothDeviceBattery(String macAddress, Promise promise) {
        if (macAddress == null || TextUtils.isEmpty(macAddress)) promise.resolve(0);
//        Log.d(TAG, "获取蓝牙设备电量: " + macAddress);
        BluetoothAdapter btAdapter = BluetoothAdapter.getDefaultAdapter();
        Class<BluetoothAdapter> bluetoothAdapterClass = BluetoothAdapter.class;
        try {
            Method method = bluetoothAdapterClass.getDeclaredMethod("getConnectionState", (Class[]) null);
            method.setAccessible(true);
            int state = (int) method.invoke(btAdapter, (Object[]) null);
            if (state == BluetoothAdapter.STATE_CONNECTED) {
                //获取在系统蓝牙的配对列表中的设备--！已连接设备包含在其中
                Set<BluetoothDevice> devices = btAdapter.getBondedDevices();
                for (BluetoothDevice device : devices) {
                    if (macAddress.toUpperCase().equals(device.getAddress().toUpperCase())) {
                        Method batteryMethod = BluetoothDevice.class.getDeclaredMethod("getBatteryLevel", (Class[]) null);
                        batteryMethod.setAccessible(true);
                        Method isConnectedMethod = BluetoothDevice.class.getDeclaredMethod("isConnected", (Class[]) null);
                        isConnectedMethod.setAccessible(true);
                        boolean isConnected = (boolean) isConnectedMethod.invoke(device, (Object[]) null);
                        int level = (int) batteryMethod.invoke(device, (Object[]) null);
                        if (device != null && level > 0 && isConnected) {
                            String deviceName = device.getName();
//                            Log.e(TAG, deviceName + "    电量:  " + level);
                            promise.resolve(level);
                        }
                    }
                }
                promise.resolve(0);
            } else {
                promise.resolve(0);
            }
        } catch (Exception e) {
            e.printStackTrace();
            promise.resolve(0);
        }
    }

    public static int getBluetoothDeviceBattery(String macAddress) {
        if (macAddress == null || TextUtils.isEmpty(macAddress)) return 0;
//        Log.d(TAG, "获取蓝牙设备电量: " + macAddress);
        BluetoothAdapter btAdapter = BluetoothAdapter.getDefaultAdapter();
        Class<BluetoothAdapter> bluetoothAdapterClass = BluetoothAdapter.class;
        try {
            Method method = bluetoothAdapterClass.getDeclaredMethod("getConnectionState", (Class[]) null);
            method.setAccessible(true);
            int state = (int) method.invoke(btAdapter, (Object[]) null);
            if (state == BluetoothAdapter.STATE_CONNECTED) {
                //获取在系统蓝牙的配对列表中的设备--！已连接设备包含在其中
                Set<BluetoothDevice> devices = btAdapter.getBondedDevices();
                for (BluetoothDevice device : devices) {
                    if (macAddress.toUpperCase().equals(device.getAddress().toUpperCase())) {
                        Method batteryMethod = BluetoothDevice.class.getDeclaredMethod("getBatteryLevel", (Class[]) null);
                        batteryMethod.setAccessible(true);
                        Method isConnectedMethod = BluetoothDevice.class.getDeclaredMethod("isConnected", (Class[]) null);
                        isConnectedMethod.setAccessible(true);
                        boolean isConnected = (boolean) isConnectedMethod.invoke(device, (Object[]) null);
                        int level = (int) batteryMethod.invoke(device, (Object[]) null);
                        if (device != null && level > 0 && isConnected) {
                            String deviceName = device.getName();
//                            Log.e(TAG, deviceName + "    电量:  " + level);
                            return level;
                        }
                    }
                }
                return 0;
            } else {
                return 0;
            }
        } catch (Exception e) {
            e.printStackTrace();
            return 0;
        }
    }

    /**
     * Attempts to cancel the discovery process.  Cancel request is always resolved as true at this
     * point, which may need to be changed, but for now whether anything happens or not it's
     * seen as successful.
     * <p>
     * Note - the Discovery promise will be resolved with any devices that were found during the
     * discovery period, so effectively cancelling resolves two promises.
     * <p>
     * cancel request
     */
    public void cancelDiscovery(final Promise promise) {
        if (!checkBluetoothAdapter()) {
            promise.reject(Exceptions.BLUETOOTH_NOT_ENABLED.name(),
                    Exceptions.BLUETOOTH_NOT_ENABLED.message());
        } else {
            promise.resolve(mAdapter.cancelDiscovery());
        }
    }

    public boolean cancelDiscovery() {
        if (!checkBluetoothAdapter()) {
//            promise.reject(Exceptions.BLUETOOTH_NOT_ENABLED.name(), Exceptions.BLUETOOTH_NOT_ENABLED.message());
            Log.e(TAG, "cancelDiscovery error: " + Exceptions.BLUETOOTH_NOT_ENABLED.name() + " " + Exceptions.BLUETOOTH_NOT_ENABLED.message());
            return false;
        } else {
            return mAdapter.cancelDiscovery();
        }
    }

    /**
     * Attempts to pair/bond with the device specified by address, {@link BluetoothDevice#createBond()}
     * is only available after SDK v19.
     *
     * @param address the address of the BluetoothDevice to which we attempt pairing
     * @param promise resolves when the BluetoothDevice is paired, rejects if the SDK version is
     *                less than 19, Bluetooth is not enabled, or an Exception occurs.
     */
    public void pairDevice(String address, Promise promise) {
        if (!checkBluetoothAdapter()) {
            promise.reject(Exceptions.BLUETOOTH_NOT_ENABLED.name(),
                    Exceptions.BLUETOOTH_NOT_ENABLED.message());
        } else if (Build.VERSION.SDK_INT < 19) {
            promise.reject(Exceptions.BONDING_UNAVAILABLE_API.name(),
                    Exceptions.BONDING_UNAVAILABLE_API.message());
        } else {
            if (BuildConfig.DEBUG)
                Log.d(TAG, String.format("Attempting to pair with device %s", address));

            final PairingReceiver pr = new PairingReceiver(mContext,
                    new PairingReceiver.PairingCallback() {
                        @Override
                        public void onPairingSuccess(NativeDevice device) {
                            promise.resolve(device.map());
                        }

                        @Override
                        public void onPairingFailure(Exception cause) {
                            promise.reject(new DevicePairingException(null, cause));
                        }
                    });
            mContext.registerReceiver(pr, PairingReceiver.intentFilter());
            try {
                BluetoothDevice device = mAdapter.getRemoteDevice(address);
                Method m = device.getClass().getMethod("createBond", (Class[]) null);
                m.invoke(device, (Object[]) null);
            } catch (IllegalAccessException e) {
                mContext.unregisterReceiver(pr);
                promise.reject(Exceptions.BONDING_UNAVAILABLE_API.name(),
                        Exceptions.BONDING_UNAVAILABLE_API.message());
            } catch (InvocationTargetException e) {
                mContext.unregisterReceiver(pr);
                promise.reject(Exceptions.BONDING_UNAVAILABLE_API.name(),
                        Exceptions.BONDING_UNAVAILABLE_API.message());
            } catch (NoSuchMethodException e) {
                mContext.unregisterReceiver(pr);
                promise.reject(Exceptions.BONDING_UNAVAILABLE_API.name(),
                        Exceptions.BONDING_UNAVAILABLE_API.message());
            }
        }
    }

    /**
     * Request that a device be unpaired.  The device Id is required - looked up using the
     * BluetoothAdapter and unpaired.
     *
     * @param address the address of the BluetoothDevice to which we attempt pairing
     * @param promise resolves when the BluetoothDevice is paired, rejects if there are any issues
     */
    public void unpairDevice(String address, Promise promise) {
        if (!checkBluetoothAdapter()) {
            promise.reject(Exceptions.BLUETOOTH_NOT_ENABLED.name(),
                    Exceptions.BLUETOOTH_NOT_ENABLED.message());
        } else if (Build.VERSION.SDK_INT >= 19) {
            promise.reject(Exceptions.BONDING_UNAVAILABLE_API.name(),
                    Exceptions.BONDING_UNAVAILABLE_API.message());
        } else {
            if (BuildConfig.DEBUG)
                Log.d(TAG, String.format("Attempting to pair with device %s", address));

            try {
                BluetoothDevice device = mAdapter.getRemoteDevice(address);
                Method m = device.getClass().getMethod("createBond", (Class[]) null);
                m.invoke(device, (Object[]) null);

                IntentFilter intentFilter = new IntentFilter();
                intentFilter.addAction(BluetoothDevice.ACTION_BOND_STATE_CHANGED);

                PairingReceiver pr = new PairingReceiver(mContext,
                        new PairingReceiver.PairingCallback() {
                            @Override
                            public void onPairingSuccess(NativeDevice device) {
                                promise.resolve(device.map());
                            }

                            @Override
                            public void onPairingFailure(Exception cause) {
                                promise.reject(new DevicePairingException(new NativeDevice(device), cause));
                            }
                        });

                mContext.registerReceiver(pr, intentFilter);
            } catch (IllegalAccessException e) {
                promise.reject(Exceptions.BONDING_UNAVAILABLE_API.name(),
                        Exceptions.BONDING_UNAVAILABLE_API.message());
            } catch (InvocationTargetException e) {
                promise.reject(Exceptions.BONDING_UNAVAILABLE_API.name(),
                        Exceptions.BONDING_UNAVAILABLE_API.message());
            } catch (NoSuchMethodException e) {
                promise.reject(Exceptions.BONDING_UNAVAILABLE_API.name(),
                        Exceptions.BONDING_UNAVAILABLE_API.message());
            }
        }
    }

    /**
     * Puts the {@link BluetoothAdapter} into an accept mode using the provided accept type
     * configured on the module.
     *
     * @param promise resolve or reject the requested listening
     */
    public void accept(HashMap parameters, Promise promise) {
        if (!checkBluetoothAdapter()) {
            promise.reject(Exceptions.BLUETOOTH_NOT_ENABLED.name(),
                    Exceptions.BLUETOOTH_NOT_ENABLED.message());
        } else if (mAcceptor != null) {
            promise.reject(Exceptions.BLUETOOTH_IN_ACCEPTING.name(),
                    Exceptions.BLUETOOTH_IN_ACCEPTING.message());
        } else {
            Properties properties = Utilities.mapToProperties(parameters);

            try {
                String connectorType = StandardOption.ACCEPTOR_TYPE.get(properties);
                if (!mAcceptorFactories.containsKey(connectorType))
                    throw new IllegalStateException(
                            String.format("No ConnectionAcceptorFactory configured for type %s", connectorType));

                ConnectionAcceptorFactory acceptorFactory = mAcceptorFactories.get(connectorType);
                ConnectionAcceptor acceptor = acceptorFactory.create(mAdapter, properties);
                acceptor.addListener(new ConnectionAcceptor.AcceptorListener<BluetoothSocket>() {
                    @Override
                    public void success(BluetoothSocket bluetoothSocket) {
                        BluetoothDevice device = bluetoothSocket.getRemoteDevice();
                        NativeDevice nativeDevice = new NativeDevice(device);

                        try {
                            // Create the appropriate Connection type and add it to the connected list

                            String connectionType = StandardOption.CONNECTION_TYPE.get(properties);
                            DeviceConnectionFactory connectionFactory = mConnectionFactories.get(connectionType);
                            DeviceConnection connection = connectionFactory.create(bluetoothSocket, properties);
                            connection.onDisconnect(onDisconnect);
                            mConnections.put(device.getAddress(), connection);

                            // Now start the connection and let React Native know
                            Thread ct = new Thread(connection);
                            ct.start();

                            promise.resolve(nativeDevice.map());
                        } catch (IOException e) {
                            promise.reject(new ConnectionFailedException(nativeDevice, e));
                        }
                    }

                    @Override
                    public void failure(Exception e) {
                        promise.reject(new AcceptFailedException(e.getMessage(), e));
                    }
                });

                this.mAcceptor = acceptor;
                this.mAcceptor.start();

            } catch (IOException e) {
                promise.reject(new AcceptFailedException(e.getMessage(), e));
            } catch (IllegalStateException e) {
                promise.reject(e);
            }
        }
    }

    /**
     * Attempts to cancel the Accepting thread.
     * <p>
     * If the {@link BluetoothAdapter} is unavailable then the promise will be rejected with a
     * Bluetooth not enabled message.
     * <p>
     * Otherwise the promise will be resolved {@code true}.  This was changed as previously an
     * reject would also occur if the Device was not in accept mode.  This has been changed as it
     * made more sense to just let React Native app set {@code accepting: false} (kind of like a
     * status check without {@code isAccepting} being required.
     * <p>
     * return based on cancel success
     */
    public void cancelAccept(Promise promise) {
        if (!checkBluetoothAdapter()) {
            promise.reject(Exceptions.BLUETOOTH_NOT_ENABLED.name(),
                    Exceptions.BLUETOOTH_NOT_ENABLED.message());
        } else {
            if (mAcceptor != null) {
                mAcceptor.cancel();
            }

            mAcceptor = null;

            promise.resolve(true);
        }
    }

    public boolean cancelAccept() {
        if (!checkBluetoothAdapter()) {
//            promise.reject(Exceptions.BLUETOOTH_NOT_ENABLED.name(), Exceptions.BLUETOOTH_NOT_ENABLED.message());
            Log.e(TAG, "cancelAccept error: " + Exceptions.BLUETOOTH_NOT_ENABLED.name() + " " + Exceptions.BLUETOOTH_NOT_ENABLED.message());
            return false;
        } else {
            if (mAcceptor != null) {
                mAcceptor.cancel();
            }

            mAcceptor = null;

            return true;
        }
    }

    /**
     * Attempts to connect to the device with the provided Id.  While the connection request is
     * active the Cancellable request will be found in the connecting map; once completed the
     * connection will be moved to the connections map.
     * <p>
     * The default client connection type will be used.  If you've provided
     * customized {@link DeviceConnection}(s) then it'll be used.
     *
     * @param address    the address to which we want to connect
     * @param parameters the parameters controlling the type of connection to make
     * @param promise    resolve or reject the requested connection
     *                   连接到蓝牙SPP
     */
    public void connectToDevice(String address, HashMap parameters, Promise promise) {
        if (!checkBluetoothAdapter()) {
            promise.reject(Exceptions.BLUETOOTH_NOT_ENABLED.name(),
                    Exceptions.BLUETOOTH_NOT_ENABLED.message());
        } else if (mConnecting.containsKey(address)) {
            promise.reject(Exceptions.ALREADY_CONNECTING.name(),
                    Exceptions.ALREADY_CONNECTING.message(address));
        } else if (mConnections.containsKey(address)) {
            // If it's already connected just return the device now.
            DeviceConnection connection = mConnections.get(address);
            promise.resolve(new NativeDevice(connection.getDevice()).map());
        } else {
            final BluetoothDevice device = mAdapter.getRemoteDevice(address);
            final NativeDevice nativeDevice = new NativeDevice(device);

            try {
                // Issue/84 just in case the React Native side gets circumvented somehow
                // this matches the IOS side of a new parameters being added to a NSDictionary
                Properties properties = parameters == null
                        ? new Properties() : Utilities.mapToProperties(parameters);

                final String connectorType = StandardOption.CONNECTOR_TYPE.get(properties);
                if (!mConnectorFactories.containsKey(connectorType)) {
                    promise.reject(Exceptions.INVALID_CONNECTOR_TYPE.name(),
                            Exceptions.INVALID_CONNECTOR_TYPE.message(connectorType));
                    return;
                }

                final String connectionType = StandardOption.CONNECTION_TYPE.get(properties);
                if (!mConnectionFactories.containsKey(connectionType)) {
                    promise.reject(Exceptions.INVALID_CONNECTION_TYPE.name(),
                            Exceptions.INVALID_CONNECTION_TYPE.message(connectorType));
                    return;
                }

                ConnectionConnectorFactory connectorFactory = mConnectorFactories.get(connectorType);
                ConnectionConnector connector = connectorFactory.create(device, properties);
                connector.addListener(new ConnectionConnector.ConnectorListener<BluetoothSocket>() {
                    @Override
                    public void success(BluetoothSocket bluetoothSocket) {
                        // Remove from connecting and add to connected
                        mConnecting.remove(address);

                        try {
                            // Create the appropriate Connection type and add it to the connected list
                            DeviceConnectionFactory connectionFactory = mConnectionFactories.get(connectionType);
                            DeviceConnection connection = connectionFactory.create(bluetoothSocket, properties);
                            connection.onDisconnect(onDisconnect);
                            mConnections.put(address, connection);

                            // Now start the connection and let React Native know
                            new Thread(connection).start();
                            promise.resolve(nativeDevice.map());

                            BluetoothClassicUtil.this.semaphore = new Semaphore(0);
                        } catch (IOException e) {
                            promise.reject(new ConnectionFailedException(nativeDevice, e));
                        }
                    }

                    @Override
                    public void failure(Exception e) {
                        // Remove from connecting and notify of failure
                        mConnecting.remove(address);
                        promise.reject(new ConnectionFailedException(nativeDevice, e));
                    }
                });

                mConnecting.put(address, connector);
                connector.start();
            } catch (IOException e) {
                promise.reject(new ConnectionFailedException(nativeDevice, e));
            } catch (IllegalStateException e) {
                promise.reject(e);
            }
        }
    }

    public void connectBtByMac(String address, HashMap parameters, Promise promise) {
        Log.d(TAG, "connectBtByMac");
        if (!checkBluetoothAdapter()) {
            promise.reject(Exceptions.BLUETOOTH_NOT_ENABLED.name(),
                    Exceptions.BLUETOOTH_NOT_ENABLED.message());
        } else if (mConnecting.containsKey(address)) {
            promise.reject(Exceptions.ALREADY_CONNECTING.name(),
                    Exceptions.ALREADY_CONNECTING.message(address));
        } else if (mConnections.containsKey(address)) {
            // If it's already connected just return the device now.
            DeviceConnection connection = mConnections.get(address);
            promise.resolve(new NativeDevice(connection.getDevice()).map());
        } else {
            // 实际走的是这里
            final BluetoothDevice device = mAdapter.getRemoteDevice(address);
            IBTConnectListener connectListener = new IBTConnectListener() {
                // IBTConnectListener 监听蓝牙连接事件
                @Override
                public void onConnecting(BluetoothDevice bluetoothDevice) {
                    Log.e(TAG, "IBTConnectListener onConnecting");
                }

                @Override
                public void onConnected(BluetoothDevice bluetoothDevice) {
                    Log.e(TAG, "IBTConnectListener onConnected");
                    final NativeDevice nativeDevice = new NativeDevice(bluetoothDevice);
                    promise.resolve(nativeDevice.map());
                }

                @Override
                public void onDisConnecting(BluetoothDevice bluetoothDevice) {
                    Log.e(TAG, "IBTConnectListener onDisConnecting");
                }

                @Override
                public void onDisConnect(BluetoothDevice bluetoothDevice) {
                    Log.e(TAG, "IBTConnectListener onDisConnect");
                    final NativeDevice nativeDevice = new NativeDevice(bluetoothDevice);
                    promise.reject(new ConnectionFailedException(nativeDevice, null));
                }

                @Override
                public void onConnectedDevice(List<BluetoothDevice> devices) {
                    Log.e(TAG, "IBTConnectListener onConnectedDevice");

                }
            };
            mBluetoothHelper.setBTConnectListener(connectListener);//设置连接监听
            mBluetoothHelper.connect(device);
        }
    }

    /**
     * Disconnect the BluetoothService from the currently connected device.
     *
     * @param address address of the device from which we disconnect
     * @return whether the disconnect request was successful
     */
    public void disconnectFromDevice(String address, Promise promise) {
        if (!checkBluetoothAdapter()) {
            promise.reject(Exceptions.BLUETOOTH_NOT_ENABLED.name(),
                    Exceptions.BLUETOOTH_NOT_ENABLED.message());
        } else if (!mConnections.containsKey(address)) {
            promise.reject(Exceptions.NOT_CURRENTLY_CONNECTED.name(),
                    Exceptions.NOT_CURRENTLY_CONNECTED.message(address));
        } else {
            DeviceConnection connection = mConnections.remove(address);
            connection.disconnect();

            promise.resolve(true);
        }
    }

    // 断开所有的SPP连接
    public void disconnectAllSppDevices() {
        if (!checkBluetoothAdapter()) {
            return;
        } else {
            mConnections.forEach((key, connection) -> {
                connection.disconnect();
            });
            mConnections.clear();
        }
    }

    public boolean disconnectFromDevice(String address) {
        if (!checkBluetoothAdapter()) {
//            promise.reject(Exceptions.BLUETOOTH_NOT_ENABLED.name(), Exceptions.BLUETOOTH_NOT_ENABLED.message());
            Log.e(TAG, "disconnectFromDevice error: " + Exceptions.BLUETOOTH_NOT_ENABLED.name() + " " + Exceptions.BLUETOOTH_NOT_ENABLED.message(address));
            return false;
        } else if (!mConnections.containsKey(address)) {
//            promise.reject(Exceptions.NOT_CURRENTLY_CONNECTED.name(), Exceptions.NOT_CURRENTLY_CONNECTED.message(address));
            Log.e(TAG, "disconnectFromDevice error: " + Exceptions.NOT_CURRENTLY_CONNECTED.name() + " " + Exceptions.NOT_CURRENTLY_CONNECTED.message(address));
            return false;
        } else {
            DeviceConnection connection = mConnections.remove(address);
            connection.disconnect();
            return true;
        }
    }

    /**
     * Check to see whether the requested device has a currently established connection.  Note that
     * this is NOT paired, the connection is specific to an RFCOMM socket being open.
     *
     * @param address the address of the device which is being queried
     * @return the connected status
     */
    public void isDeviceConnected(String address, Promise promise) {
        if (!checkBluetoothAdapter()) {
            promise.reject(Exceptions.BLUETOOTH_NOT_ENABLED.name(),
                    Exceptions.BLUETOOTH_NOT_ENABLED.message());
        } else {
            promise.resolve(mConnections.containsKey(address));
        }
    }

    public boolean isDeviceConnected(String address) {
        if (!checkBluetoothAdapter()) {
//            promise.reject(Exceptions.BLUETOOTH_NOT_ENABLED.name(), Exceptions.BLUETOOTH_NOT_ENABLED.message());
            Log.e(TAG, "isDeviceConnected error: " + Exceptions.BLUETOOTH_NOT_ENABLED.name() + " " + Exceptions.BLUETOOTH_NOT_ENABLED.message());
            return false;
        } else {
            return mConnections.containsKey(address);
        }
    }

    // 根据Mac地址，判断设备的蓝牙是否连接
    public void isDeviceBtConnected(String address, Promise promise) {
        if (!checkBluetoothAdapter()) {
            promise.reject(Exceptions.BLUETOOTH_NOT_ENABLED.name(),
                    Exceptions.BLUETOOTH_NOT_ENABLED.message());
        } else {
            promise.resolve(mBluetoothHelper.isConnected(address));
        }
    }

    // 根据Mac地址，判断设备的蓝牙是否连接
    public boolean isDeviceBtConnected(String address) {
        if (!checkBluetoothAdapter()) {
//            promise.reject(Exceptions.BLUETOOTH_NOT_ENABLED.name(), Exceptions.BLUETOOTH_NOT_ENABLED.message());
            Log.e(TAG, "disconnectFromDevice error: " + Exceptions.BLUETOOTH_NOT_ENABLED.name() + " " + Exceptions.BLUETOOTH_NOT_ENABLED.message());
            return false;
        } else {
            return mBluetoothHelper.isConnected(address);
        }
    }

    /**
     * Attempt to get the connection representing the device address requested.
     *
     * @param address the address of the device which is being queried
     * @return the connected status
     */
    public void getConnectedDevice(String address, Promise promise) {
        if (!checkBluetoothAdapter()) {
            promise.reject(Exceptions.BLUETOOTH_NOT_ENABLED.name(),
                    Exceptions.BLUETOOTH_NOT_ENABLED.message());
        } else {
            if (!mConnections.containsKey(address)) {
                promise.reject(new BluetoothException(address + " is not currently connected"));
            } else {
                DeviceConnection connection = mConnections.get(address);
                promise.resolve(new NativeDevice(connection.getDevice()).map());
            }
        }
    }

    public NativeDevice getConnectedDevice(String address) {
        if (!checkBluetoothAdapter()) {
//            promise.reject(Exceptions.BLUETOOTH_NOT_ENABLED.name(), Exceptions.BLUETOOTH_NOT_ENABLED.message());
            Log.e(TAG, "getConnectedDevice error: " + Exceptions.BLUETOOTH_NOT_ENABLED.name() + " " + Exceptions.BLUETOOTH_NOT_ENABLED.message());
            return null;
        } else {
            if (!mConnections.containsKey(address)) {
//                Log.e(TAG, "getConnectedDevice: " + address + " is not currently connected");
                return null;
            } else {
                DeviceConnection connection = mConnections.get(address);
                return new NativeDevice(connection.getDevice());
            }
        }
    }


    /**
     * Attempts to write to the device.  I'm not sure if there is a better way to do this, but all
     * communication needs to come through the module.  It would be awesome if we could
     * dynamically add a BluetoothConnectionModuleXXXX to the Application in order to allow
     * each device to be it's own module for communication.
     *
     * @param address address of the device to which we will write the data
     * @param message base64 encoded message to be sent
     * @return whether the message has been written.
     */
    public void writeToDevice(String address, String message, Promise promise) {
        if (!checkBluetoothAdapter()) {
            promise.reject(Exceptions.BLUETOOTH_NOT_ENABLED.name(),
                    Exceptions.BLUETOOTH_NOT_ENABLED.message());
        } else if (!mConnections.containsKey(address)) {
            promise.reject(Exceptions.NOT_CURRENTLY_CONNECTED.name(),
                    Exceptions.NOT_CURRENTLY_CONNECTED.message(address));
        } else {
            // Decode the Base64 value provided by React Native module.  At this point it gets
            // left in it's raw byte[] and it's up to the DeviceConnection to handle/write
            // accordingly
            byte[] data = Base64.decode(message, Base64.DEFAULT);

            try {
                mConnections.get(address).write(data);
                promise.resolve(true);
            } catch (IOException e) {
                promise.reject(Exceptions.WRITE_FAILED.name(),
                        Exceptions.WRITE_FAILED.message(e.getMessage()));
            }
        }
    }

    private boolean writeToDevice(String address, byte[] data) {
        if (!checkBluetoothAdapter()) {
//            promise.reject(Exceptions.BLUETOOTH_NOT_ENABLED.name(), Exceptions.BLUETOOTH_NOT_ENABLED.message());
            Log.e(TAG, "writeToDevice error: " + Exceptions.BLUETOOTH_NOT_ENABLED.name() + " " + Exceptions.BLUETOOTH_NOT_ENABLED.message());
            return false;
        } else if (mConnections == null || mConnections.isEmpty()) {
            Log.e(TAG, "writeToDevice error: mConnections is Null OR Empty");
            return false;
        } else if (!mConnections.containsKey(address)) {
//            promise.reject(Exceptions.NOT_CURRENTLY_CONNECTED.name(), Exceptions.NOT_CURRENTLY_CONNECTED.message(address));
            Log.e(TAG, "writeToDevice error: " + Exceptions.NOT_CURRENTLY_CONNECTED.name() + " " + Exceptions.NOT_CURRENTLY_CONNECTED.message(address));
            return false;
        } else {
            // Decode the Base64 value provided by React Native module.  At this point it gets
            // left in it's raw byte[] and it's up to the DeviceConnection to handle/write
            // accordingly
//            byte[] data = Base64.decode(message, Base64.DEFAULT);

            try {
                mConnections.get(address).write(data);
                return true;
            } catch (IOException e) {
//                promise.reject(Exceptions.WRITE_FAILED.name(), Exceptions.WRITE_FAILED.message(e.getMessage()));
                Log.e(TAG, "writeToDevice error: " + Exceptions.WRITE_FAILED.name() + " " + Exceptions.WRITE_FAILED.message(e.getMessage()));
                return false;
            }
        }
    }

    /**
     * Attempts to read from the device.  The full buffer is read (then cleared) without using the
     * mDelimiter.  Note - there will never be data within the buffer if the application is currently
     * registered to receive read events.
     * <p>
     * Might be configurable to reject when there is no data, instead of resolve null.
     *
     * @param address device address to which we wish to read
     * @return data, could be null or 0 length
     */
    public void readFromDevice(String address, Promise promise) {
        if (!checkBluetoothAdapter()) {
            promise.reject(Exceptions.BLUETOOTH_NOT_ENABLED.name(),
                    Exceptions.BLUETOOTH_NOT_ENABLED.message());
        } else if (!mConnections.containsKey(address)) {
            promise.reject(Exceptions.NOT_CURRENTLY_CONNECTED.name(),
                    Exceptions.NOT_CURRENTLY_CONNECTED.message(address));
        } else {
            String message = mConnections.get(address).read();
            promise.resolve(message);
        }
    }

    public String readFromDevice(String address) {
        if (!checkBluetoothAdapter()) {
//            promise.reject(Exceptions.BLUETOOTH_NOT_ENABLED.name(), Exceptions.BLUETOOTH_NOT_ENABLED.message());
            Log.e(TAG, "readFromDevice error: " + Exceptions.BLUETOOTH_NOT_ENABLED.name() + " " + Exceptions.BLUETOOTH_NOT_ENABLED.message());
            return null;
        } else if (!mConnections.containsKey(address)) {
//            promise.reject(Exceptions.NOT_CURRENTLY_CONNECTED.name(), Exceptions.NOT_CURRENTLY_CONNECTED.message(address));
            Log.e(TAG, "readFromDevice error: " + Exceptions.NOT_CURRENTLY_CONNECTED.name() + " " + Exceptions.NOT_CURRENTLY_CONNECTED.message(address));
            return null;
        } else {
            String message = mConnections.get(address).read();
            return message;
        }
    }

    /**
     * Clears the buffer.
     *
     * @param address the address of the device whose buffer is to be cleared
     * @return
     */
    public void clearFromDevice(String address, Promise promise) {
        if (!checkBluetoothAdapter()) {
            promise.reject(Exceptions.BLUETOOTH_NOT_ENABLED.name(),
                    Exceptions.BLUETOOTH_NOT_ENABLED.message());
        } else if (!mConnections.containsKey(address)) {
            promise.reject(Exceptions.NOT_CURRENTLY_CONNECTED.name(),
                    Exceptions.NOT_CURRENTLY_CONNECTED.message(address));
        } else {
            mConnections.get(address).clear();
            promise.resolve(true);
        }
    }

    public boolean clearFromDevice(String address) {
        if (!checkBluetoothAdapter()) {
//            promise.reject(Exceptions.BLUETOOTH_NOT_ENABLED.name(), Exceptions.BLUETOOTH_NOT_ENABLED.message());
            Log.e(TAG, "clearFromDevice error: " + Exceptions.BLUETOOTH_NOT_ENABLED.name() + " " + Exceptions.BLUETOOTH_NOT_ENABLED.message());
            return false;
        } else if (!mConnections.containsKey(address)) {
//            promise.reject(Exceptions.NOT_CURRENTLY_CONNECTED.name(), Exceptions.NOT_CURRENTLY_CONNECTED.message(address));
            Log.e(TAG, "clearFromDevice error: " + Exceptions.NOT_CURRENTLY_CONNECTED.name() + " " + Exceptions.NOT_CURRENTLY_CONNECTED.message(address));
            return false;
        } else {
            mConnections.get(address).clear();
            return true;
        }
    }

    /**
     * Gets the available information within the buffer.  There is no concept of the delimiter in
     * this request - which may need to be changed - since in most cases I can see a full message
     * needing to be available.
     *
     * @param address device address for which the client wishes to read
     * @return length of buffer, could be 0
     */
    public void availableFromDevice(String address, Promise promise) {
        if (!checkBluetoothAdapter()) {
            promise.reject(Exceptions.BLUETOOTH_NOT_ENABLED.name(),
                    Exceptions.BLUETOOTH_NOT_ENABLED.message());
        } else if (!mConnections.containsKey(address)) {
            promise.reject(Exceptions.NOT_CURRENTLY_CONNECTED.name(),
                    Exceptions.NOT_CURRENTLY_CONNECTED.message(address));
        } else {
            promise.resolve(mConnections.get(address).available());
        }
    }

    public int availableFromDevice(String address) {
        if (!checkBluetoothAdapter()) {
//            promise.reject(Exceptions.BLUETOOTH_NOT_ENABLED.name(), Exceptions.BLUETOOTH_NOT_ENABLED.message());
            Log.e(TAG, "availableFromDevice error: " + Exceptions.BLUETOOTH_NOT_ENABLED.name() + " " + Exceptions.BLUETOOTH_NOT_ENABLED.message());
            return 0;
        } else if (!mConnections.containsKey(address)) {
//            promise.reject(Exceptions.NOT_CURRENTLY_CONNECTED.name(), Exceptions.NOT_CURRENTLY_CONNECTED.message(address));
            Log.e(TAG, "availableFromDevice error: " + Exceptions.NOT_CURRENTLY_CONNECTED.name() + " " + Exceptions.NOT_CURRENTLY_CONNECTED.message());
            return 0;
        } else {
            return mConnections.get(address).available();
        }
    }


    /**
     * Registers the module wide {@link BroadcastReceiver}(s).  These include:
     * <ul>
     *     <li>{@link BluetoothAdapter} state changes</li>
     *     <li>{@link BluetoothDevice} ACL action changes - these include device connections and
     *          disconnections</li>
     * </ul>
     */
    private void registerBluetoothReceivers() {
        if (mStateChangeReceiver == null) {
            mStateChangeReceiver = new StateChangeReceiver(this);
            mContext
                    .registerReceiver(mStateChangeReceiver, StateChangeReceiver.intentFilter());
        }

        if (mActionACLReceiver == null) {
            mActionACLReceiver = new ActionACLReceiver(this);
            mContext
                    .registerReceiver(mActionACLReceiver, ActionACLReceiver.intentFilter());
        }
    }

    /**
     * Unregister receivers.  "Global" receivers need to always be removed, they are not configured
     * to be removed on their own.
     */
    private void unregisterBluetoothReceivers() {
        if (mStateChangeReceiver != null) {
            mContext.unregisterReceiver(mStateChangeReceiver);
            mStateChangeReceiver = null;
        }

        if (mActionACLReceiver != null) {
            mContext.unregisterReceiver(mActionACLReceiver);
            mActionACLReceiver = null;
        }

        if (mDiscoveryReceiver != null) {
            mAdapter.cancelDiscovery();
            mContext.unregisterReceiver(mDiscoveryReceiver);
            mDiscoveryReceiver = null;
        }
    }

    private BiConsumer<BluetoothDevice, Exception> onDisconnect = (BluetoothDevice device, Exception e) -> {
        Log.d(TAG, String.format("Disconnected from device %s due to %s", device.getName(), e.getMessage()));

        // At this point just remove the connection, the DEVICE_DISCONNECTED should have been
        // sent from the ACL message already.
        mConnections.remove(device.getAddress());
        EventBus.getDefault().post(new SppEventBean(SppEventBean.SppEventName.SppDisconnect, device.getAddress()));
        if (this.listener != null) {
            this.listener.onDeviceDisconnect(new NativeDevice(device), new BluetoothException(e.getMessage()));
        }
    };

    private BiConsumer<BluetoothDevice, String> onReceivedData = (BluetoothDevice device, String data) -> {
        Log.d(TAG, String.format("Received translated data from the device: %s", data));
        NativeDevice nativeDevice = new NativeDevice(device);
//        BluetoothMessage bluetoothMessage = new BluetoothMessage<>(nativeDevice, data);
//        sendEvent(EventType.DEVICE_READ, nativeDevice, bluetoothMessage.asMap());
        if (this.listener != null)
            this.listener.onDeviceRead(nativeDevice, data);

        byte[] response = Base64.decode(data, Base64.DEFAULT);
        String cmdName = BluetoothCmd.getNotifyCmdName(response);
        if (cmdName == null) {
            Log.e(TAG, "Received 未知指令, value: " + Arrays.toString(response));
            return;
        }
        byte[] value = BluetoothCmd.getNotifyCmdData(response);
        Log.d(TAG, "Received 指令类型: " + cmdName + " value: " + Arrays.toString(value));
        synchronized (BluetoothClassicUtil.this) {
            BluetoothClassicUtil.this.semaphore.release();
        }
        switch (cmdName) {
            case BleCmdContant.AppCmdId.ChangeRecordToLeft:
                if (BluetoothClassicUtil.this.touchEventCallback != null)
                    BluetoothClassicUtil.this.touchEventCallback.invoke(RawBlePeripheral.Role.Left);
                break;
            case BleCmdContant.AppCmdId.ChangeRecordToRight:
                if (BluetoothClassicUtil.this.touchEventCallback != null)
                    BluetoothClassicUtil.this.touchEventCallback.invoke(RawBlePeripheral.Role.Right);
                break;
            case BleCmdContant.AppCmdId.WorkInHfpMode:
                break;
            case BleCmdContant.AppCmdId.WorkInTwsMode:
                break;
            case BleCmdContant.AppCmdId.WorkInNoneMode:
                break;
            case BleCmdContant.AppCmdId.Electric:
                if (BluetoothClassicUtil.this.electricValueCallback != null)
                    BluetoothClassicUtil.this.electricValueCallback.invoke(Arrays.copyOfRange(value, 1, 3));
                break;
            case BleCmdContant.AppCmdId.MacAddress:
                if (BluetoothClassicUtil.this.macAddressCallback != null)
                    BluetoothClassicUtil.this.macAddressCallback.invoke(value);
                break;
            case BleCmdContant.AppCmdId.FirmwareVersion:
                break;
            case BleCmdContant.AppCmdId.SubSppDisconnect:
                EventBus.getDefault().post(new SppEventBean(SppEventBean.SppEventName.SubSppDisconnected,device.getAddress()));
                break;
            case BleCmdContant.AppCmdId.SubSppConnected:
                EventBus.getDefault().post(new SppEventBean(SppEventBean.SppEventName.SubSppConnected, device.getAddress()));
                break;
            case BleCmdContant.AppCmdId.LedR:
                if (BluetoothClassicUtil.this.ledStatusEventCallback != null)
                    BluetoothClassicUtil.this.ledStatusEventCallback.invoke(value[0] == 0);
                break;
            default: break;
        }

        // FIXME: 2022/11/28 需要检测时点击事件(touchLeft/touchRight)才调用
        sendToIMModule(device, data);
    };

    // 将SPP数据组合成为IM模块需要的BLE数据
    public void sendToIMModule(BluetoothDevice device, String data) {
        Intent intent = new Intent("com.tmk.BleManagerDidUpdateValueForCharacteristic");
        intent.putExtra("peripheral", device.getAddress());
        intent.putExtra("characteristic", "592eff01-432f-4669-84b1-ddc45cc40dd9");
        intent.putExtra("service", "592eff00-432f-4669-84b1-ddc45cc40dd9");
        ArrayList<Integer> integers = new ArrayList<>();
        if (data.trim().equals("qswAAQEBAQAD")) { //单击了左耳
            integers.add(1 & 0xFF);
        } else if (data.trim().equals("qswAAQECAgAE")) {
            integers.add(2 & 0xFF);
        }
        intent.putExtra("value", integers);
        mContext.sendBroadcast(intent); // 发送广播
    }

    /**
     * Adds a new listener for the {@link EventType} provided.
     * <p>
     * Listeners can be provided with or without a device context.  A device context is applied
     * by sending the event name followed by a device's address.  An example of this would be
     * {@code READ@12:34:56:78:90}.
     *
     * @param requestedEvent {@link EventType} name for which the client wishes to listen
     */
    @SuppressWarnings({"unused"})
    public void addListener(String requestedEvent) {
        String eventType = requestedEvent,
                eventDevice = null;

        if (requestedEvent.contains("@")) {
            String[] context = requestedEvent.split("@");
            eventType = context[0];
            eventDevice = context[1];
        }

        if (!EventType.eventNames().containsKey(eventType)) {
            throw new InvalidBluetoothEventException(requestedEvent);
        }

        EventType event = EventType.valueOf(eventType);

        if (EventType.DEVICE_READ == event) {
            if (!mConnections.containsKey(eventDevice)) {
                throw new IllegalStateException(String.format("Cannot read from %s, not currently connected", requestedEvent));
            }

            DeviceConnection connection = mConnections.get(eventDevice);
            connection.onDataReceived(onReceivedData);
        }

        // Now we can increment the listener as appropriate
        AtomicInteger listenerCount = mListenerCounts.get(requestedEvent);
        if (listenerCount == null) {
            listenerCount = new AtomicInteger(0);
            if (mListenerCounts.containsKey(requestedEvent))
                mListenerCounts.put(requestedEvent, listenerCount);
        }
        int currentCount = listenerCount.incrementAndGet();

        Log.d(TAG, String.format("Adding listener to %s, currently have %d listeners",
                requestedEvent, currentCount));
    }

    /**
     * Removes the specified {@link EventType}.  If this is a {@link EventType#DEVICE_READ}
     * the device address must be supplied (separated by an @) in the same way as when the
     * listener was applied.
     *
     * @param requestedEvent name of the {@link EventType} for which the client wishes to remove
     *                       listener.
     */
    @SuppressWarnings({"unused"})
    public void removeListener(String requestedEvent) {
        String eventType = requestedEvent,
                eventDevice = null;

        if (requestedEvent.contains("@")) {
            String[] context = requestedEvent.split("@");
            eventType = context[0];
            eventDevice = context[1];
        }

        if (!EventType.eventNames().containsKey(eventType)) {
            return;
        }

        EventType event = EventType.valueOf(eventType);

        if (EventType.DEVICE_READ == event && mConnections.containsKey(eventDevice)) {
            // #139 Originally if there was no current connection (ie. the device had already been disconnected) this would
            // throw an exception.  At this point we don't really care, but if the connection does exist we need to
            // remove it and clear it.
            DeviceConnection connection = mConnections.get(eventDevice);
            connection.clearOnDataReceived();
        }

        // Only remove the listener if it currently exists.  If you're attemping to remove a listener
        // which hasn't been added, just let it go.
        if (mListenerCounts.containsKey(eventType)) {
            AtomicInteger listenerCount = mListenerCounts.get(eventType);
            int currentCount = listenerCount.decrementAndGet();

            Log.d(TAG,
                    String.format("Removing listener to %s, currently have %d listeners",
                            eventType, currentCount));
        }
    }

    /**
     * Remove all the listeners for the provided eventName.   Removing all listeners also has a
     * context (prefixed with @) which will remove all the listeners for that specified device.
     *
     * @param requestedEvent for which all listeners will be removed
     */
    @SuppressWarnings({"unused"})
    public void removeAllListeners(String requestedEvent) {
        String eventType = requestedEvent,
                eventDevice = null;

        if (requestedEvent.contains("@")) {
            String[] context = requestedEvent.split("@");
            eventType = context[0];
            eventDevice = context[1];
        }

        if (!EventType.eventNames().containsKey(eventType)) {
            return;
        }

        EventType event = EventType.valueOf(eventType);

        if (EventType.DEVICE_READ == event) {
            if (!mConnections.containsKey(eventDevice)) {
                throw new IllegalStateException(String.format("Cannot read from %s, not currently connected", eventType));
            }

            DeviceConnection connection = mConnections.get(eventDevice);
            connection.clearOnDataReceived();
        }

        // Only remove the listener if it currently exists.  If you're attemping to remove a listener
        // which hasn't been added, just let it go.
        if (mListenerCounts.containsKey(eventType)) {
            AtomicInteger listenerCount = mListenerCounts.get(eventType);
            listenerCount.set(0);

            Log.d(TAG,
                    String.format("Removing listener to %s, currently have %d listeners",
                            eventType, 0));
        }
    }
    //endregion

    /**
     * Called from the {@link StateChangeReceiver} when the {@link BluetoothAdapter} state
     * is changed.  Fires the appropriate event to any listeners.
     *
     * @param newState the new {@link BluetoothState}
     * @param oldState the previous {@link BluetoothState}
     */
    @Override
    public void onStateChange(BluetoothState newState, BluetoothState oldState) {
        Log.d(TAG, "onStateChange from " + oldState.name() + "  to " + newState.name());

//        EventType event = (BluetoothState.ENABLED == newState)
//                ? EventType.BLUETOOTH_ENABLED : EventType.BLUETOOTH_DISABLED;
//        sendEvent(event, new BluetoothStateEvent(newState).map());
        if (this.listener != null) {
            if (BluetoothState.ENABLED == newState) {
                this.listener.onBlueToothEnable();
            } else {
                this.listener.onBlueToothDisable();
            }
        }
    }

    /**
     * Not sure whether this provides any actual info.
     *
     * @param device the {@link NativeDevice} which just requested disconnect
     * @deprecated may be removed at some point unless it is found useful (opposed to ACLDisconnected)
     */
    @Override
    public void onACLDisconnectRequest(NativeDevice device) {
        Log.d(TAG, "onACLDisconnectRequest to " + device.getAddress());
    }

    /**
     * Called when we get an ACL level device disconnection.  This happens when a socket is closed,
     * for whatever reason.  If there is a currently active connection (which there should be)
     * it's disconnected, then a DEVICE_DISCONNECTED event is fired.
     * <p>
     * This also sends an event to the {@code DEVICE_DISCONNECTED@address} making the Device more
     * responsible for it's own connectivity.
     *
     * @param device the {@link NativeDevice} which was just disconnected
     */
    @Override
    public void onACLDisconnected(NativeDevice device) {
        Log.d(TAG, "onACLDisconnected to " + device.getAddress());

        mConnections.remove(device.getAddress());
//        sendEvent(EventType.DEVICE_DISCONNECTED, device.map());
        if (this.listener != null) {
            this.listener.onDeviceDisconnect(device, null);
        }
    }

    // region 读写指令
    private Handler       mWriteHandler;
    private HandlerThread mWriteHandlerThread;
    private Semaphore semaphore = new Semaphore(0);

    /**
     * 向设备发送指令
     * @param address 设备 mac 地址
     * @param data 指令数据
     * @param needWaitRespone 有 notify 的指令, 尽量设置成 true, 以免等待过多的时间
     */
    public synchronized void writeToQueue(String address, byte[] data, boolean needWaitRespone) {
        if (mWriteHandlerThread == null) {
            mWriteHandlerThread = new HandlerThread("Tmk-Bluetooth-Cmd-Thread");
            mWriteHandlerThread.start();
        }
        if (mWriteHandler == null) mWriteHandler = new Handler(mWriteHandlerThread.getLooper());
        mWriteHandler.post(() -> {
            this.writeToDevice(address, data);
            Log.d(TAG, "writeToQueue: 等待指令完成 [" + Arrays.toString(data));
            try {
                if (needWaitRespone) semaphore.tryAcquire(500, TimeUnit.MILLISECONDS);
                else Thread.sleep(500);
            } catch (InterruptedException e) {
                e.printStackTrace();
            }
            Log.d(TAG, "writeToQueue: 指令调用完成 [" + Arrays.toString(data));
        });
    }

    public void sendCmdData(String address, byte[] data) {
        this.writeToQueue(address, data, false);
    }

    public void switchMic(String address, boolean shouldTurnOn, TouchEventCallback callback) {
        this.touchEventCallback = callback;
        this.writeToQueue(address, shouldTurnOn ? BluetoothCmd.Write.LedOpen : BluetoothCmd.Write.LedClose, true);
    }

    public void turnLightStatus(String address, boolean shouldTurnOn, LedStatusEventCallback callback) {
        this.writeToQueue(address, shouldTurnOn ? BluetoothCmd.Write.LedOpen : BluetoothCmd.Write.LedClose, false);

        this.readLightStatus(address, callback);
    }

    public void readLightStatus(String address, LedStatusEventCallback callback) {
        this.ledStatusEventCallback = callback;
        this.writeToQueue(address, BluetoothCmd.Write.LedR, true);
    }

    public void readElectricValue(String address, ElectricValueCallback callback) {
        this.electricValueCallback = callback;
        this.writeToQueue(address, BluetoothCmd.Write.MSeriesElectric, true);
    }

    public void readMacAddress(String address, MacAddressCallback callback) {
        this.macAddressCallback = callback;
        this.writeToQueue(address, BluetoothCmd.Write.MacAddress, true);
    }
    // endregion

    // region 回调
    private Listener listener;
    public void setListener(Listener listener) {
        this.listener = listener;
    }

    private TouchEventCallback touchEventCallback;
    private LedStatusEventCallback ledStatusEventCallback;
    private ElectricValueCallback electricValueCallback;
    private MacAddressCallback macAddressCallback;

    public void setTouchEventCallback(TouchEventCallback callback) {
        touchEventCallback = callback;
    }

    /**
     * 耳机点击事件, 指示点击的左耳或者右耳(角色 role 指示左右)
     */
    public interface TouchEventCallback {
        void invoke(RawBlePeripheral.Role role);
    }
    /**
     * 耳机灯光回调, isOn 是否已开启
     */
    public interface LedStatusEventCallback {
        void invoke(Boolean isOn);
    }
    /**
     * 耳机电量回调, 电量值回调, value 为左右耳机电量, [50, 70] 即为 左耳50% 右耳70%
     */
    public interface ElectricValueCallback {
        void invoke(byte[] value);
    }
    /**
     * 耳机 mac 地址回调, value 多个 mac 地址
     */
    public interface MacAddressCallback {
        void invoke(byte[] value);
    }
    // endregion

    public interface Promise {
        void resolve(Object var1);

        void reject(String var1, String var2);

        void reject(Throwable var1);
    }

    public interface Listener {
        default void onBlueToothEnable() {
        }

        default void onBlueToothDisable() {
        }

        default void onDeviceConnect() {
        }

        void onDeviceDisconnect(NativeDevice device, BluetoothException e);

        void onDeviceRead(NativeDevice device, String data);

        default void onError() {
        }

        void onDiscovered(NativeDevice device);
    }


    public void registerDataReceiver() {
        if (this.getConnectedDevices().isEmpty()) {
            Log.e(TAG, "没有任何连接，无法注册监听");
            return;
        }
        String macAddress = this.getConnectedDevices().get(0).getAddress();
        if (!mConnections.containsKey(macAddress)) {
            Log.e(TAG, macAddress + " 的地址没有建立连接，无法注册监听");
            return;
        }
        DeviceConnection connection = mConnections.get(macAddress);
        connection.onDataReceived(this.onReceivedData);
    }


}
