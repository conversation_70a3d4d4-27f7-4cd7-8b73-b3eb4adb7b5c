package co.timekettle.btkit.bluetoothlib.bluetooth;

import java.util.Calendar;
import java.util.Date;
import java.util.HashMap;

/**
 * Wraps bluetooth message data within a message containing the device from which and timestamp
 * of when the message was received.  Provides customized data to be transferred by configuring
 * each connection with a specific type of DataTransformer (Byte[] to T).
 *
 * @param <T> type of data being transferred.
 */
public class BluetoothMessage<T> {

    private HashMap device;
    private Date timestamp;
    private T data;

    public BluetoothMessage(HashMap device, T data) {
        this.device = device;
        this.data = data;
        this.timestamp = Calendar.getInstance().getTime();
    }

    public HashMap asMap() {
        HashMap map = new HashMap();
        map.put("device", device);
        map.put("data", String.valueOf(data));
        map.put("timestamp", Utilities.formatDate(timestamp));
        return map;
    }
}
