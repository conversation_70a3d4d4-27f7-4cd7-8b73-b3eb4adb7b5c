package co.timekettle.btkit.protocol


/**
 * WT2的协议类
 */
interface IProtocolWT2 {
    fun writeAppAuth(receiveData: ByteArray)         // 发送鉴权指令
    fun writeButtonEnabled(enabled: Boolean)        // 设置按键有效/无效
    fun writeDepluxOpen(isOpen: Boolean)            // 开启/关闭双工模式
    fun writeRecordEnabled(enabled: Boolean)        // 开启录音 / 关闭录音
    fun writeModeFactory()                          // 进入工厂模式
    fun writeModeUser()                             // 进入用户模式
    fun writeVolume(value: Float)                   // 设置音量（0.0f - 1.0f）
    fun writeNRW(params: ByteArray)                  // 设置耳机的算法参数
    fun writeOpusCompressionRatio(level: Int)       // 设置Opus压缩比
}