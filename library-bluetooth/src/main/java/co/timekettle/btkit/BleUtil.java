package co.timekettle.btkit;

import static co.timekettle.btkit.BleCmdContant.ElectricDesc;
import static co.timekettle.btkit.BleCmdContant.M2CmdDesc;
import static co.timekettle.btkit.BleCmdContant.MSeriesCmds;
import static co.timekettle.btkit.BleCmdContant.ScanUUIDFilter;
import static co.timekettle.btkit.BleCmdContant.StandardUUIDWith;
import static co.timekettle.btkit.BleCmdContant.UUIDWith;
import static co.timekettle.btkit.BleCmdContant.WSeriesCmds;
import static co.timekettle.btkit.BleCmdContant.Wt2CmdDesc;
import static co.timekettle.btkit.BleCmdContant.Wt2DataDesc;

import android.annotation.SuppressLint;
import android.app.Activity;
import android.app.Application;
import android.bluetooth.BluetoothAdapter;
import android.bluetooth.BluetoothGatt;
import android.bluetooth.BluetoothGattCharacteristic;
import android.bluetooth.le.ScanFilter;
import android.bluetooth.le.ScanRecord;
import android.bluetooth.le.ScanResult;
import android.bluetooth.le.ScanSettings;
import android.content.Context;
import android.content.Intent;
import android.os.Build;
import android.os.Handler;
import android.os.Looper;
import android.os.ParcelUuid;
import android.util.Log;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;

import com.hjq.permissions.OnPermissionCallback;
import com.hjq.permissions.Permission;
import com.hjq.permissions.XXPermissions;

import org.greenrobot.eventbus.EventBus;

import java.io.IOException;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.HashMap;
import java.util.LinkedList;
import java.util.List;
import java.util.Objects;
import java.util.Timer;
import java.util.TimerTask;
import java.util.UUID;
import java.util.concurrent.ConcurrentLinkedQueue;
import java.util.concurrent.Executor;
import java.util.concurrent.Executors;
import java.util.concurrent.LinkedBlockingQueue;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicBoolean;
import java.util.function.Function;

import cn.wandersnail.ble.Connection;
import cn.wandersnail.ble.ConnectionConfiguration;
import cn.wandersnail.ble.Device;
import cn.wandersnail.ble.EasyBLE;
import cn.wandersnail.ble.EventObserver;
import cn.wandersnail.ble.Request;
import cn.wandersnail.ble.RequestBuilder;
import cn.wandersnail.ble.RequestBuilderFactory;
import cn.wandersnail.ble.ScanConfiguration;
import cn.wandersnail.ble.WriteOptions;
import cn.wandersnail.ble.callback.MtuChangeCallback;
import cn.wandersnail.ble.callback.NotificationChangeCallback;
import cn.wandersnail.ble.callback.ReadCharacteristicCallback;
import cn.wandersnail.ble.callback.ReadRssiCallback;
import cn.wandersnail.ble.callback.ScanListener;
import cn.wandersnail.ble.callback.WriteCharacteristicCallback;
import cn.wandersnail.commons.observer.Observe;
import cn.wandersnail.commons.poster.ThreadMode;
import co.timekettle.btkit.bean.BleEventBean;
import co.timekettle.btkit.bean.M2BlePeripheral;
import co.timekettle.btkit.bean.RawBlePeripheral;
import co.timekettle.btkit.bean.WT2BlePeripheral;
import co.timekettle.btkit.dfu.DfuUtil;
import co.timekettle.btkit.dot.BleDotAnalyzer;
import co.timekettle.btkit.parser.M2PParser;
import co.timekettle.btkit.parser.M2Parser;
import co.timekettle.btkit.parser.M3Parser;
import co.timekettle.btkit.parser.WT2Parser;

/**
 *
 */
public class BleUtil {
    public static final String TAG = "BleUtil";
    @Deprecated
    private Context context;
    private Timer timer = null;
    private final int pollInterval = 1000;
    private boolean RemoveInactive = true; // 移除不活跃设备, 通过定时检测
    private boolean isBleInit = false;

    private BleCmdContant.ProductType scanningType = BleCmdContant.ProductType.NONE;

    private final String BleRecordEventName = "BLEAudioRecordData";
    private TouchEventCallback touchEventCallback;

    private static final long TIME_WINDOW_MS = TimeUnit.SECONDS.toMillis(70); // 时间窗口大小，单位秒
    private final LinkedList<Long> failTimestamps = new LinkedList<>();  // 队列存储连接失败的时间戳
    private final ConnectionConfiguration connectionConfiguration = new ConnectionConfiguration().stopScanWhenConnecting(false);
    private BleDotAnalyzer bleDotAnalyzer = new BleDotAnalyzer();


    private Executor singleLeftThreadExecutor = Executors.newSingleThreadExecutor();
    private Executor singleRightThreadExecutor = Executors.newSingleThreadExecutor();

    public enum BleEventName {
        BleDidStatusUpdate("BleDidStatusUpdate"),
        BleConnectStandby("BleConnectStandby"), // 设备连接并且初始化完成
        BleDisconnectedPeripheral("BleDisconnectedPeripheral"),
        BleDisconnectedSubPeripheral("BleDisconnectedSubPeripheral"), // M 系列从耳断开
        BleConnectedSubPeripheral("BleConnectedSubPeripheral"), // M 系列从耳连上
        BleProtocolChanged("BleProtocolChanged"), // M系列 协议切换（是HFP或者A2DP）
        BleStError("BleStError"), // 004 st 异常
        BleConnFailedTooMuch("BleConnFailedTooMuch"); // 连接失败次数过多
        private final String mName;

        BleEventName(final String name) {
            mName = name;
        }

        @NonNull
        @Override
        public String toString() {
            return mName;
        }
    }

    public static String toHexStr(byte[] data) {
        return toHexStr(data, null);
    }

    public static String toHexStr(byte[] data, String separator) {
        StringBuilder stringBuilder = new StringBuilder();
        for (byte singleByte : data) {
            if (stringBuilder.length() > 0 && separator != null) stringBuilder.append(separator);
            stringBuilder.append(Integer.toString((singleByte & 0xff) + 0x100, 16).substring(1).toUpperCase());
        }

        return stringBuilder.toString();
    }

    private ConcurrentLinkedQueue<RawBlePeripheral> searchedPeripherals = new ConcurrentLinkedQueue<RawBlePeripheral>();

    public boolean isScanning() {
        return EasyBLE.getInstance().isScanning();
    }

    public BleCmdContant.ProductType getScanningType() {
        return scanningType;
    }

    public static final BleUtil shared = new BleUtil();

    BleUtil() {
    }

    private final EventObserver observer = new EventObserver() {
        @Observe
        @Override
        public void onBluetoothAdapterStateChanged(int state) {
            dispatchBluetoothState(state);
            if (state == BluetoothAdapter.STATE_OFF) {
                stopScan();
                searchedPeripherals.clear();
                dispatch(BleEventName.BleDidStatusUpdate, null);
            }
        }

        @Observe
        @Override
        public void onCharacteristicChanged(@NonNull Device device, @NonNull UUID service, @NonNull UUID characteristic, @NonNull byte[] value) {
            String peripId = device.getAddress();
            String serviceUuid = service.toString();
            String characteristicUuid = characteristic.toString();
            byte[] values = value;
            RawBlePeripheral perip = BleUtil.shared.getPeripheral(peripId);
            if (perip == null) {
                return;
            }
            if (ElectricDesc.characteristic.equalsIgnoreCase(characteristicUuid)) {
//                assert values != null : "特征数据 values 不能为空";

                if (perip instanceof WT2BlePeripheral) {
                    WT2BlePeripheral wt2 = (WT2BlePeripheral) perip;
                    wt2.electric = values[0];
                }
                LogUtil.d(TAG, "电量特征值更新: [" + toHexStr(values, " "));
                dispatch(BleEventName.BleDidStatusUpdate, perip);

            } else if (Wt2DataDesc.characteristic.equalsIgnoreCase(characteristicUuid)) {
//                assert values != null : "特征数据 values 不能为空";

                if (perip instanceof WT2BlePeripheral) {
                    WT2BlePeripheral wt2 = (WT2BlePeripheral) perip;
//                    wt2.recorder.nPacket++;
//                    if (wt2.recorder.nPacket % 100 == 0) {
//                        readRssi(perip); // 每2秒读取更新, 下次使用
//                    }

                    Boolean needResetIndex = wt2.needResetIndex != null;
                    if (wt2.needResetIndex != null) {
                        wt2.needResetIndex = null;
                    }
                    Object[] args = new Object[3];
                    args[0] = peripId;
                    args[1] = values;
                    args[2] = new HashMap<String, Object>() {{
                        put("id", peripId);
                        put("name", wt2.macSuffix4);
                        put("rssi", wt2.rssi);
                        put("electric", String.valueOf(wt2.electric));
                        put("firmwareVersion", wt2.firmwareVersion);
                        put("needResetIndex", needResetIndex);
                    }};
                    if (wt2.role == RawBlePeripheral.Role.Left) {
                        singleLeftThreadExecutor.execute(() -> {
                            EventBus.getDefault().post(new HashMap<String, Object>() {{
                                put(BleRecordEventName, args);
                            }});
                        });
                    } else {
                        singleRightThreadExecutor.execute(() -> {
                            EventBus.getDefault().post(new HashMap<String, Object>() {{
                                put(BleRecordEventName, args);
                            }});
                        });
                    }
//                    EventBus.getDefault().post(new HashMap<String, Object>() {{
//                        put(BleRecordEventName, args);
//                    }});
//                            LogUtil.d(TAG, peripId + " 指令响应, 录音数据: " + values.length + " thread id: " + Thread.currentThread().getId());
                }

            } else if (Wt2CmdDesc.characteristic.equalsIgnoreCase(characteristicUuid)) {
                assert values != null : "特征数据 values 不能为空";

                /// >>>> 用于远程等模式
                Intent intent = new Intent("com.tmk.BleManagerDidUpdateValueForCharacteristic");
                intent.putExtra("peripheral", peripId);
                intent.putExtra("characteristic", characteristicUuid);
                intent.putExtra("service", serviceUuid);
                ArrayList<Integer> integers = new ArrayList<>();
                for (byte b : values) {
                    integers.add((b & 0xFF));
                }
                intent.putExtra("value", integers);
                context.sendBroadcast(intent); // 发送广播
                /// <<<<

                try {
                    processWCmdCharacteristicResponse(perip, values);
                } catch (IOException e) {
                    LogUtil.e(TAG, "解析数据失败 " + e.toString());
                }
                dispatch(BleEventName.BleDidStatusUpdate, perip);
            } else if (M2CmdDesc.characteristic.equalsIgnoreCase(characteristicUuid)) {
                assert values != null : "特征数据 values 不能为空";

                /// >>>> 用于远程等模式
                Intent intent = new Intent("com.tmk.BleManagerDidUpdateValueForCharacteristic");
                intent.putExtra("peripheral", peripId);
                intent.putExtra("characteristic", characteristicUuid);
                intent.putExtra("service", serviceUuid);
                ArrayList<Integer> integers = new ArrayList<>();
                for (byte b : values) {
                    integers.add((b & 0xFF));
                }
                intent.putExtra("value", integers);
                context.sendBroadcast(intent); // 发送广播
                /// <<<<

                if (perip instanceof M2BlePeripheral) {
                    M2BlePeripheral m = (M2BlePeripheral) perip;
                    processMCmdCharacteristicResponse(m, values);
                }

                dispatch(BleEventName.BleDidStatusUpdate, perip);
            }
        }

        @Observe
        @Override
        public void onConnectionStateChanged(@NonNull Device device) {
            switch (device.getConnectionState()) {
                case DISCONNECTED:
                    LogUtil.e(TAG, "连接已断开: " + device.getAddress());
                    Intent intent = new Intent("com.tmk.BleManagerDisconnectPeripheral");
                    context.sendBroadcast(intent); // 发送广播
                    String peripUuid = device.getAddress();
                    RawBlePeripheral peripheral = searchedPeripherals.stream().filter(item -> peripUuid.equals(item.id)).findAny().orElse(null);
                    if (peripheral != null) {
                        EventBus.getDefault().post(new BleEventBean(BleEventName.BleDisconnectedPeripheral, peripheral));
                    }
                    AtomicBoolean needUpdate = new AtomicBoolean(false);
                    EasyBLE.getInstance().releaseConnection(device);
                    searchedPeripherals.removeIf(perip -> {
                        if (perip.id.equals(peripUuid)) {
                            needUpdate.set(true);
                            perip.state = RawBlePeripheral.PeripheralState.DISCONNECT;

//                            stopNotifications(perip);
                            if (perip instanceof WT2BlePeripheral) {
                                WT2BlePeripheral wt2 = (WT2BlePeripheral) perip;
                                wt2.disableSpeaker();
                            }
                            return true;
                        } else {
                            return false;
                        }
                    });

                    if (needUpdate.get())
                        dispatch(BleEventName.BleDisconnectedPeripheral, peripheral);
                    break;
                case CONNECTED:
                    new Handler(Looper.getMainLooper()).postDelayed(() -> {
                        RawBlePeripheral perip = searchedPeripherals.stream()
                                .filter(item -> device.getAddress().equals(item.id))
                                .findAny().orElse(null);
                        if (perip == null) return;
                        perip.state = RawBlePeripheral.PeripheralState.DIDCONNECT;
                        if (perip.isFacFirm()) // 厂测固件不需要鉴权
                            EventBus.getDefault().post(new BleEventBean(BleEventName.BleConnectStandby, perip));
                        dispatch(BleEventName.BleDidStatusUpdate, null);
                        bleDotAnalyzer.dotConnSuccess(perip.id, perip.id, System.currentTimeMillis());
                        bleDotAnalyzer.dotDiscoverStart(perip.id, perip.id, System.currentTimeMillis());
//                        BleDotAnalyzer.INSTANCE.dotConnSuccess(perip.id);
//                        BleDotAnalyzer.INSTANCE.dotInitStart(perip.id);
                    }, 20);
                    break;
                case SERVICE_DISCOVERED:
                    RawBlePeripheral perip = searchedPeripherals.stream()
                            .filter(item -> device.getAddress().equals(item.id))
                            .findAny().orElse(null);
                    if (perip == null) return;
                    if (perip.productType == BleCmdContant.ProductType.M2 || perip.productType == BleCmdContant.ProductType.M2P || perip.productType == BleCmdContant.ProductType.M3) {
                        perip.state = RawBlePeripheral.PeripheralState.DIDINIT;
                        dispatch(BleEventName.BleDidStatusUpdate, perip);
                    }
                    bleDotAnalyzer.dotDiscoverSuccess(perip.id, perip.id, System.currentTimeMillis());
//                    BleDotAnalyzer.INSTANCE.dotInitSuccess(perip.id);

                    LogUtil.d(TAG, "连接了设备: " + device.getAddress());
                    startNotifications(perip);
                    break;
            }
        }

        @Observe
        @Override
        public void onServiceDiscoverFailed(@NonNull Device device) {
            LogUtil.e(TAG, "onServiceDiscoverFailed mac: " + device.getAddress());
            RawBlePeripheral perip = searchedPeripherals.stream()
                    .filter(item -> device.getAddress().equals(item.id))
                    .findAny().orElse(null);
            if (perip == null) return;
            bleDotAnalyzer.dotDiscoverFail(perip.id, perip.id, "", System.currentTimeMillis());
        }

        @Observe
        @Override
        public void onConnectFailed(@NonNull Device device, int failType) {
            LogUtil.e(TAG, "onConnectFailed mac:" + device.getAddress() + " failType:" + failType);
            if (String.valueOf(failType).contains("133")) recordFailTimestamp();
            RawBlePeripheral perip = searchedPeripherals.stream()
                    .filter(item -> device.getAddress().equals(item.id))
                    .findAny().orElse(null);
            if (isFrequentFailures()) {
                dispatch(BleEventName.BleConnFailedTooMuch, perip);
                failTimestamps.clear(); // 清空队列
            }
            bleDotAnalyzer.dotConnFail(perip.id, perip.id, "code = " + failType, System.currentTimeMillis());
//            BleDotAnalyzer.INSTANCE.dotConnFail(device.getAddress());
        }

        @Observe
        @Override
        public void onConnectionError(@NonNull Device device, int status) {
            LogUtil.e(TAG, "onConnectionError mac:" + device.getAddress() + " status:" + status);
            if (String.valueOf(status).contains("133")) recordFailTimestamp();
            RawBlePeripheral perip = searchedPeripherals.stream()
                    .filter(item -> device.getAddress().equals(item.id))
                    .findAny().orElse(null);
            if (isFrequentFailures()) {
                dispatch(BleEventName.BleConnFailedTooMuch, perip);
                failTimestamps.clear(); // 清空队列
            }
            bleDotAnalyzer.dotDisconnect(System.currentTimeMillis(), perip.id, "code = " + status);
            bleDotAnalyzer.dotConnFail(perip.id, perip.id, "code = " + status, System.currentTimeMillis());
//            BleDotAnalyzer.INSTANCE.dotConnFail(device.getAddress());
        }
    };

    public void setLogLevel(int l) {
        LogUtil.setLogLevel(l);
    }

    public void setLogCallback(LogUtil.BleLogCallback callback) {
        LogUtil.setLogCallback(callback);
    }

    public void init(Context context, Boolean toExternal) {
//        BleDotAnalyzer.INSTANCE.init(context);
        RemoveInactive = true;
        this.context = context.getApplicationContext();
        initBLE((Application) context.getApplicationContext());
        DfuUtil.shared.init(this.context);
        bleDotAnalyzer.init(this.context, toExternal);
        isBleInit = true;
    }

    public void destroy() {
        EasyBLE.getInstance().destroy();
    }

    private void initBLE(Application application) {
        ScanConfiguration scanConfig = new ScanConfiguration()
                .setScanSettings(new ScanSettings.Builder()
                        .setScanMode(ScanSettings.SCAN_MODE_LOW_LATENCY)//搜索模式
                        .build());
        EasyBLE ble = EasyBLE.getBuilder().setScanConfiguration(scanConfig)
                .build();
        ble.initialize(application);

        EasyBLE.getInstance().registerObserver(observer);
        EasyBLE.getInstance().addScanListener(new ScanListener() {
            @Override
            public void onScanStart() {
                LogUtil.d(TAG, "onScanStart");
            }

            @Override
            public void onScanStop() {
                LogUtil.d(TAG, "onScanStop");
                dispatch(BleEventName.BleDidStatusUpdate, null);
            }

            @Override
            public void onScanResult(@NonNull Device device, boolean isConnectedBySys) {

                LogUtil.d(TAG, "onScanResult: " + device.getAddress() + " " + device.getName() + " " + device.getRssi());

                final BleCmdContant.ProductType pType = getProductType(device);

                if (pType == BleCmdContant.ProductType.WT2 || pType == BleCmdContant.ProductType.WT2_Edge || pType == BleCmdContant.ProductType.W3_Pro) {
                    WT2BlePeripheral wt2 = new WT2Parser().parseManufacturerSpecificData(device);
                    BleUtil.shared.updateDiscoverPeripherals(wt2);
                } else if (pType == BleCmdContant.ProductType.M2) {
                    M2BlePeripheral m2 = new M2Parser().parseManufacturerSpecificData(device);
                    BleUtil.shared.updateDiscoverM2Peripherals(m2);
                } else if (pType == BleCmdContant.ProductType.M2P) {
                    M2BlePeripheral m2 = new M2PParser().parseManufacturerSpecificData(device);
                    BleUtil.shared.updateDiscoverM2Peripherals(m2);
                } else if (pType == BleCmdContant.ProductType.M3) {
                    M2BlePeripheral m2 = new M3Parser().parseManufacturerSpecificData(device);
                    BleUtil.shared.updateDiscoverM2Peripherals(m2);
                }
            }

            @Override
            public void onScanError(int errorCode, @NonNull String errorMsg) {
                LogUtil.e(TAG, "onScanError: " + errorCode + " " + errorMsg);
            }
        });
    }

    /**
     * 可填充参数
     *
     * @param context        上下文
     * @param removeInactive 是否定期清楚不活跃的设备
     * @param opts           {"forceLegacy": true}, 注意 forceLegacy下不能扫描多设备的 uuid
     */
    public void init(Context context, boolean removeInactive, HashMap<String, Object> opts) {
        if (opts == null || opts.size() == 0) opts = new HashMap<String, Object>();
        RemoveInactive = removeInactive;
        this.context = context.getApplicationContext();
        initBLE((Application) this.context);
        DfuUtil.shared.init(this.context);
    }

    @SuppressLint("MissingPermission")
    public void enableBluetooth(Activity activity) {
//        BleManager.shareInstance().enableBluetooth(activity);
        BluetoothAdapter adapter = EasyBLE.getInstance().getBluetoothAdapter();
        if (adapter != null && !adapter.isEnabled()) {
            if (activity == null) {
                LogUtil.e(TAG, "enableBluetooth: Current activity not available");
            } else {
                Intent intentEnable = new Intent(BluetoothAdapter.ACTION_REQUEST_ENABLE);
                activity.startActivity(intentEnable);
            }
        }
    }

    // FIXME: 2022/6/9 后期需扩展 <永久拒绝授权> 后的回调
    public void requestPermission(Activity activity, Function<Boolean, Object> callback) {
        XXPermissions.with(activity)
                // 申请单个权限
                .permission(Permission.ACCESS_FINE_LOCATION)
                .permission(Permission.ACCESS_COARSE_LOCATION)
                // 申请多个权限
                .permission(Permission.Group.BLUETOOTH)
                // 设置权限请求拦截器（局部设置）
                //.interceptor(new PermissionInterceptor())
                // 设置不触发错误检测机制（局部设置）
                //.unchecked()
                .request(new OnPermissionCallback() {

                    @Override
                    public void onGranted(List<String> permissions, boolean all) {
                        if (callback != null) {
                            callback.apply(all);
                        }
                        if (all) {
                            LogUtil.d(TAG, "获取 <位置> 权限等成功");
                        } else {
                            LogUtil.e(TAG, "获取部分权限成功，但部分权限未正常授予");
                        }
                    }

                    @Override
                    public void onDenied(List<String> permissions, boolean never) {
                        if (callback != null) callback.apply(false);

                        if (never) {
                            LogUtil.e(TAG, "被永久拒绝授权，请手动授予 <位置> 权限");

                            // 如果是被永久拒绝就跳转到应用权限系统设置页面
                            XXPermissions.startPermissionActivity(activity, permissions);
                        } else {
                            LogUtil.e(TAG, "获取 <位置> 权限失败");
                        }
                    }
                });
    }



    private final List<Listener> listeners = new ArrayList<Listener>();

    public void addListener(Listener listener) {
        if (!this.listeners.contains(listener))
            this.listeners.add(listener);
    }

    public void removeListener(Listener listener) {
        this.listeners.remove(listener);
    }

    public void startScan(BleCmdContant.ProductType type) {
        if (!isBleInit) return;
        LogUtil.d(TAG, "开始扫描：" + type.toString());
        String serviceUUIDs[] = new String[]{};
        if (type == BleCmdContant.ProductType.WT2) {
            serviceUUIDs = new String[]{ScanUUIDFilter.WT2.getUuidString(), ScanUUIDFilter.WT2_BK.getUuidString(), ScanUUIDFilter.W_UPGRADE.getUuidString()};
        } else if (type == BleCmdContant.ProductType.WT2_Edge) {
            serviceUUIDs = new String[]{ScanUUIDFilter.WT2_Edge.getUuidString(), ScanUUIDFilter.WT2_Edge_Fac.getUuidString(), ScanUUIDFilter.W_UPGRADE.getUuidString()};
        } else if (type == BleCmdContant.ProductType.M2 || type == BleCmdContant.ProductType.M2P) {
            serviceUUIDs = new String[]{ScanUUIDFilter.M2.getUuidString(), ScanUUIDFilter.M2P.getUuidString()};
        } else if (type == BleCmdContant.ProductType.M3) {
            serviceUUIDs = new String[]{ScanUUIDFilter.M3.getUuidString()};
        } else if (type == BleCmdContant.ProductType.W3_Pro) {
            serviceUUIDs = new String[]{ScanUUIDFilter.W3Pro.getUuidString(), ScanUUIDFilter.W3Pro_Fac.getUuidString(), ScanUUIDFilter.W_UPGRADE.getUuidString()};
        }

        this.scanningType = type;
        this.startScan(serviceUUIDs);
    }

    public void startScan(String[] serviceUUIDs) {
        if (serviceUUIDs == null) {
            serviceUUIDs = new String[]{UUIDWith("6f25"), UUIDWith("6f28")};
        }

//        int state = BleManager.shareInstance().checkState();
//        LogUtil.d(TAG, "startScan: 当前蓝牙的状态 " + state);

        /**
         * SCAN_MODE_LOW_POWER
         * 这个是Android默认的扫描模式，耗电量最小。如果扫描不再前台，则强制执行此模式。
         * 在这种模式下， Android会扫喵0.5s,暂停4.5s.
         *
         *  SCAN_MODE_BALANCED
         * 平衡模式， 平衡扫描频率和耗电量的关系。
         * 在这种模式下，Android会扫描2s, 暂停3s。 这是一种妥协模式。
         *
         *  SCAN_MODE_LOW_LATENCY
         * 连续不断的扫描， 建议应用在前台时使用。但会消耗比较多的电量。 扫描结果也会比较快一些。
         *
         *  SCAN_MODE_OPPORTUNISTIC
         * 这种模式下， 只会监听其他APP的扫描结果回调。它无法发现你想发现的设备。
         */
//        HashMap<String, Object> o = new HashMap<String, Object>() {{
//            put("numberOfMatches", 3); // (ANDROID) Match as many advertisement per filter as hw could allow dependes on current capability and availability of the resources in hw.
//            put("matchMode", 1); // (ANDROID) Defaults to MATCH_MODE_AGGRESSIVE
////            put("scanMode", 0); // (ANDROID) Defaults to SCAN_MODE_LOW_POWER on android
////            put("scanMode", ScanSettings.SCAN_MODE_LOW_POWER); // (ANDROID) Defaults to SCAN_MODE_LOW_POWER on android
//            put("scanMode", ScanSettings.SCAN_MODE_LOW_LATENCY);
////            put("scanMode", ScanSettings.SCAN_MODE_LOW_LATENCY); // 调试发现, 导致连接会变慢, BluetoothGatt: onClientRegistered() - status=0 clientIf=12 之后 3-4s 才变为 Connected
//        }};
//        BleManager.shareInstance().scan(serviceUUIDs, 0, false, o);
        List<ScanFilter> filters = new ArrayList<>();
        for (String serviceUUID : serviceUUIDs) {
            filters.add(
                    new ScanFilter.Builder()
                            .setServiceUuid(new ParcelUuid(UUIDHelper.uuidFromString(serviceUUID)))
                            .build()
            );
        }
        EasyBLE.getInstance().scanConfiguration.setFilters(filters);
        EasyBLE.getInstance().startScan();
        if (timer != null) {
            timer.purge();
            timer.cancel();
            timer = null;
        }
        if (RemoveInactive) {
            timer = new Timer(Thread.currentThread().getName());
            timer.schedule(new TimerTask() {
                @Override
                public void run() {
                    long curTs = System.currentTimeMillis();
                    AtomicBoolean needUpdate = new AtomicBoolean(false);
                    searchedPeripherals.removeIf(perip -> {
//                    if (perip.productType == BleCmdContant.ProductType.M3) {
//                        return false;
//                    }
                        if (perip.isConnected() || perip.isConnecting()) {
                            return false;
                        } else {
                            boolean isExpired = curTs - perip.discoverTimestamp > (pollInterval + 4000);
                            if (isExpired) {
                                needUpdate.set(true);
                                return true;
                            } else {
                                return false;
                            }
                        }
                    });
                    if (needUpdate.get()) dispatch(BleEventName.BleDidStatusUpdate, null);
                }
            }, 0, pollInterval);
        }
    }

    public void stopScan() {
        if (!isBleInit) return;
        if (timer != null) {
            timer.purge();
            timer.cancel();
            timer = null;
        }
        ArrayList<RawBlePeripheral> removelist = new ArrayList<>();
        new ArrayList<RawBlePeripheral>(this.searchedPeripherals).forEach(perip -> {
            if (perip.isConnected()) {
            } else if (perip.isConnecting()) {
                disconnect(perip);
            } else {
                removelist.add(perip);
            }
        });
        if (removelist.size() > 0) this.searchedPeripherals.removeAll(removelist);
//        BleManager.shareInstance().stopScan();
        EasyBLE.getInstance().stopScan();

    }

    public List<RawBlePeripheral> getConnectedPeripherals() {
        List<RawBlePeripheral> list = new ArrayList<RawBlePeripheral>(this.searchedPeripherals);
        list.removeIf(perip -> !perip.isConnected());
        // rssi 从大到小(即信号值从强到弱排序)
        list.sort((o1, o2) -> Integer.parseInt(o2.rssi) - Integer.parseInt(o1.rssi));
        return list;
    }

    public List<RawBlePeripheral> getNotConnectPeripherals() {
        List<RawBlePeripheral> list = new ArrayList<RawBlePeripheral>(this.searchedPeripherals);
        list.removeIf(perip -> perip.isConnected());
        // rssi 从大到小(即信号值从强到弱排序)
        list.sort((o1, o2) -> Integer.parseInt(o2.rssi) - Integer.parseInt(o1.rssi));
        return list;
    }

    public List<RawBlePeripheral> getSortedSearchedPeripherals() {
        List<RawBlePeripheral> cons = getConnectedPeripherals();
        List<RawBlePeripheral> notcons = getNotConnectPeripherals();
        cons.addAll(notcons);
        return cons;
    }

    public ConcurrentLinkedQueue<RawBlePeripheral> getSearchedPeripherals() {
        return this.searchedPeripherals;
    }

    public RawBlePeripheral getPeripheralById(String id) {
        if (this.searchedPeripherals == null || this.searchedPeripherals.isEmpty()) return null;
        for (RawBlePeripheral peripheral : this.searchedPeripherals) {
            if (peripheral.id.equals(id)) {
                return peripheral;
            }
        }
        return null;
    }

    public void connect(RawBlePeripheral perip) {
        perip.state = RawBlePeripheral.PeripheralState.WILLCONNECT;
        dispatch(BleEventName.BleDidStatusUpdate, null);

        bleDotAnalyzer.dotStartConn(perip.id, perip.id, System.currentTimeMillis());
        ThreadPoolManager.INSTANCE.execute(() -> EasyBLE.getInstance().connect(perip.device, connectionConfiguration));
    }

    public void disconnect(RawBlePeripheral peripheral) {
        EasyBLE.getInstance().disconnectConnection(peripheral.device);
    }

    public void disconnectAll() {
        List<RawBlePeripheral> list = new ArrayList<>(this.searchedPeripherals);

        for (RawBlePeripheral perip : list) {
            if (perip.isConnecting() || perip.isConnected()) {
                EasyBLE.getInstance().disconnectConnection(perip.device);
            }
        }
    }

    public BluetoothGatt getDeviceGatt(String key) {
        Connection connection = EasyBLE.getInstance().getOrderedConnections().stream().filter(
                conn -> conn.getDevice().getAddress().equals(key)
        ).findFirst().orElse(null);
        return connection == null ? null : connection.getGatt();
    }

    BleServiceDesc[] getNotificationDescs(BleCmdContant.ProductType pType) {
        final BleServiceDesc[] notificationDescs;
        if (pType == BleCmdContant.ProductType.WT2 || pType == BleCmdContant.ProductType.WT2_Edge || pType == BleCmdContant.ProductType.W3_Pro) {
            notificationDescs = new BleServiceDesc[]{Wt2CmdDesc, Wt2DataDesc, ElectricDesc};
        } else if (pType == BleCmdContant.ProductType.M2 || pType == BleCmdContant.ProductType.M2P || pType == BleCmdContant.ProductType.M3) {
            notificationDescs = new BleServiceDesc[]{M2CmdDesc, ElectricDesc};
        } else {
            notificationDescs = new BleServiceDesc[0];
        }
        return notificationDescs;
    }

    void startNotifications(RawBlePeripheral perip) {
        ThreadPoolManager.INSTANCE.execute(() -> {
            final BleCmdContant.ProductType pType = perip.productType;

            //设置MTU
            Connection connection = EasyBLE.getInstance().getConnection(perip.device);
            RequestBuilder<MtuChangeCallback> mtuBuilder = new RequestBuilderFactory()
                    .getChangeMtuBuilder(185);
            Request mtuRequest = mtuBuilder.setCallback(new MtuChangeCallback() {
                @Override
                public void onMtuChanged(@NonNull Request request, int mtu) {
                    Log.d("EasyBLE", "MTU修改成功，新值：" + mtu);
                }

                @Override
                public void onRequestFailed(@NonNull Request request, int failType, int gattStatus, @Nullable Object value) {

                }
            }).build();
            mtuRequest.execute(connection);

            final BleServiceDesc[] notificationDescs = getNotificationDescs(pType);
            for (BleServiceDesc notify : notificationDescs) {
                /* FIXME: 未监听数据指令的话, 会收不到蓝牙耳机给 app 发送的 auth 的随机码, 没法完成后续初始化(蓝牙就处在未初始化阶段) */
                LogUtil.d(TAG, "监听开始 " + notify.service + " >>>>>>>>");
                String uuid = UUID.randomUUID().toString();
                bleDotAnalyzer.dotPerListenStart(perip.id, uuid, notify.type.toString(), System.currentTimeMillis());
//                BleDotAnalyzer.INSTANCE.dotPerListenStart(UUIDHelper.uuidFromString(notify.characteristic).toString(), notify.type.toString());
                Request notifyRequest = new RequestBuilderFactory()
                        .getSetNotificationBuilder(
                                UUIDHelper.uuidFromString(notify.service),
                                UUIDHelper.uuidFromString(notify.characteristic), true
                        ).setTag(notify.type.name()).setCallback(new NotificationChangeCallback() {
                            @Override
                            public void onNotificationChanged(@NonNull Request request, boolean isEnabled) {
                                bleDotAnalyzer.dotPerListenSuccess(perip.id, uuid, System.currentTimeMillis());
                            }

                            @Override
                            public void onRequestFailed(@NonNull Request request, int failType, int gattStatus, @Nullable Object value) {
                                bleDotAnalyzer.dotPerListenFail(perip.id, uuid, "code = " + failType, System.currentTimeMillis());
                            }
                        }).build();
                //不设置回调，使用观察者模式接收结果
                notifyRequest.execute(connection);
            }
        });
    }

    void stopNotifications(RawBlePeripheral perip) {
        ThreadPoolManager.INSTANCE.execute(() -> {
            Connection connection = EasyBLE.getInstance().getConnection(perip.device);
            final BleCmdContant.ProductType pType = perip.productType;

            final BleServiceDesc[] notificationDescs = getNotificationDescs(pType);
            for (BleServiceDesc notify : notificationDescs) {
                /* FIXME: 未监听数据指令的话, 会收不到蓝牙耳机给 app 发送的 auth 的随机码, 没法完成后续初始化(蓝牙就处在未初始化阶段) */
                LogUtil.d(TAG, "停止监听开始 " + notify.service + " >>>>>>>>");
                Request stopNotifyRequest = new RequestBuilderFactory()
                        .getSetNotificationBuilder(
                                UUIDHelper.uuidFromString(notify.service),
                                UUIDHelper.uuidFromString(notify.characteristic),
                                false
                        ).build();
                //不设置回调，使用观察者模式接收结果
                stopNotifyRequest.execute(connection);
            }
        });
    }

    void updateDiscoverPeripherals(WT2BlePeripheral perip) {
        if (perip == null) return; // 广播数据 Advertising 为空的情况不处理

        WT2BlePeripheral exsitPerip = null;
        for (RawBlePeripheral p : searchedPeripherals) {
            if (p instanceof WT2BlePeripheral && p.id.equalsIgnoreCase(perip.id)) {
                exsitPerip = (WT2BlePeripheral) p;
                break;
            }
        }
        if (exsitPerip != null) {
            if (perip.discoverTimestamp - exsitPerip.discoverTimestamp > 800) {
                exsitPerip.electric = perip.electric;
                exsitPerip.rssi = perip.rssi;
                exsitPerip.discoverTimestamp = perip.discoverTimestamp;
                exsitPerip.needUpgrade = false;

                dispatch(BleEventName.BleDidStatusUpdate, exsitPerip);
            }
        } else {
            searchedPeripherals.add(perip);
            dispatch(BleEventName.BleDidStatusUpdate, perip);
        }
    }

    void updateDiscoverM2Peripherals(M2BlePeripheral perip) {
        if (perip == null) return; // 广播数据 Advertising 为空的情况不处理

        M2BlePeripheral exsitPerip = null;
        for (RawBlePeripheral p : searchedPeripherals) {
            if (p.id.equals(perip.id)) {
                exsitPerip = (M2BlePeripheral) p;
                break;
            }
        }
        // M2 二次检索
        if (exsitPerip == null) {
            for (RawBlePeripheral p : searchedPeripherals) {
                if (p instanceof M2BlePeripheral) {
                    M2BlePeripheral m2 = (M2BlePeripheral) p;
                    if (m2.mac[0].equals(perip.mac[0]) || m2.mac[1].equals(perip.mac[1])) {
                        exsitPerip = perip;
                        break;
                    }
                }
            }
        }

        if (exsitPerip == null) {
            searchedPeripherals.add(perip);
            dispatch(BleEventName.BleDidStatusUpdate, perip);
        } else {
            if (perip.discoverTimestamp - exsitPerip.discoverTimestamp > 800) {
                exsitPerip.electric = perip.electric;
                exsitPerip.rssi = perip.rssi;
                exsitPerip.discoverTimestamp = perip.discoverTimestamp;

                exsitPerip.name = perip.name;
                exsitPerip.mac = perip.mac;
                exsitPerip.macBytes = perip.macBytes;
                exsitPerip.macSuffix4 = perip.macSuffix4;
                exsitPerip.firmwareVersion = perip.firmwareVersion;
                exsitPerip.host = perip.host;
                exsitPerip.isPaired = perip.isPaired;

                dispatch(BleEventName.BleDidStatusUpdate, exsitPerip);
            }
        }
    }

    public BleCmdContant.ProductType getProductType(RawBlePeripheral peripheral) {
        return this.getProductType(peripheral.device);
    }

    public BleCmdContant.ProductType getProductType(Device device) {
        ScanResult result = device.getScanResult();
        ScanRecord record = result == null ? null : result.getScanRecord();
        if (record == null || record.getServiceUuids() == null || record.getServiceUuids().isEmpty()) {
            return BleCmdContant.ProductType.NONE;
        }
        List<ParcelUuid> uuids = record.getServiceUuids();

        String serviceUUID = UUIDHelper.uuidToString(uuids.get(0).getUuid());
        if (serviceUUID.length() == 4) {
            serviceUUID = StandardUUIDWith(serviceUUID);
        }
        if (serviceUUID.equalsIgnoreCase(ScanUUIDFilter.M2P.getUuidString())) {
            return BleCmdContant.ProductType.M2P;
        } else if (serviceUUID.equalsIgnoreCase(ScanUUIDFilter.M2.getUuidString())) {
            return BleCmdContant.ProductType.M2;
        } else if (serviceUUID.equalsIgnoreCase(ScanUUIDFilter.M3.getUuidString())) {
            return BleCmdContant.ProductType.M3;
        } else if (serviceUUID.equalsIgnoreCase(ScanUUIDFilter.WT2.getUuidString()) || serviceUUID.equalsIgnoreCase(ScanUUIDFilter.WT2_BK.getUuidString()) || serviceUUID.equalsIgnoreCase(ScanUUIDFilter.W_UPGRADE.getUuidString())) {
            return BleCmdContant.ProductType.WT2;
        } else if (serviceUUID.equalsIgnoreCase(ScanUUIDFilter.WT2_Edge.getUuidString()) || serviceUUID.equalsIgnoreCase(ScanUUIDFilter.WT2_Edge_Fac.getUuidString())) {
            return BleCmdContant.ProductType.WT2_Edge;
        } else if (serviceUUID.equalsIgnoreCase(ScanUUIDFilter.W3Pro.getUuidString()) || serviceUUID.equalsIgnoreCase(ScanUUIDFilter.W3Pro_Fac.getUuidString())) {
            return BleCmdContant.ProductType.W3_Pro;
        } else {
            return BleCmdContant.ProductType.NONE;
        }
    }

    private void dispatch(BleEventName type, RawBlePeripheral perip) {
        new Handler(Looper.getMainLooper()).post(new Runnable() {
            @Override
            public void run() {
                for (Listener listener : listeners) {
                    listener.dispatchEvent(type, perip);
                }
            }
        });
    }

    private void dispatchCmd(RawBlePeripheral perip, String cmdId, byte[] values) {
        new Handler(Looper.getMainLooper()).post(new Runnable() {
            @Override
            public void run() {
                for (Listener listener : listeners) {
                    listener.dispatchCmdEvent(perip, cmdId, values);
                }
            }
        });
    }

    private void dispatchBluetoothState(int state) {
        new Handler(Looper.getMainLooper()).post(new Runnable() {
            @Override
            public void run() {
                for (Listener listener : listeners) {
                    listener.onBluetoothStateUpdate(state);
                }
            }
        });
    }

    private void readPeripheralInfo(WT2BlePeripheral peripheral) {
        ThreadPoolManager.INSTANCE.execute(() -> {
            try {
                Thread.sleep(50);
            } catch (InterruptedException e) {
                e.printStackTrace();
            }
//                String[] cmdIds = {BleCmdContant.AppCmdId.OEM, BleCmdContant.AppCmdId.SerialNumber, BleCmdContant.AppCmdId.HardwareVersion, BleCmdContant.AppCmdId.FirmwareVersion, BleCmdContant.AppCmdId.Electric};
            String[] cmdIds = {BleCmdContant.AppCmdId.Electric};
            for (String cmdId : cmdIds) {
                BleCmd cmd = getWSeriesCmd(cmdId);
                readCharacteristicData(peripheral, cmd);
            }
            String info = peripheral.oem + " " + peripheral.electric + " " + peripheral.serialNumber + " " + peripheral.hardwareVersion + " " + peripheral.firmwareVersion + " " + peripheral.stVersion;
            LogUtil.d(TAG, "更新(stVersion还在读取中) " + info);
            dispatch(BleEventName.BleDidStatusUpdate, peripheral);
        });
    }

    private void readCharacteristicData(RawBlePeripheral peripheral, BleCmd cmd) {
        final String peripUuid = peripheral.id;
        LogUtil.d(TAG, "读取开始 " + cmd.id + " 设备 " + peripUuid);
        //读取特征值
        Request readRequest = new RequestBuilderFactory()
                .getReadCharacteristicBuilder(
                        UUIDHelper.uuidFromString(cmd.service),
                        UUIDHelper.uuidFromString(cmd.characteristic)
                )
                .setPriority(Integer.MAX_VALUE)//设置请求优先级
                .setCallback(new ReadCharacteristicCallback() {
                    @Override
                    public void onRequestFailed(@NonNull Request request, int failType, int gattStatus, @Nullable Object value) {
                    }

                    @Override
                    public void onCharacteristicRead(@NonNull Request request, @NonNull byte[] value) {
                        LogUtil.d(TAG, "读取成功 " + cmd.id + " 给设备 " + peripUuid + " [" + new String(value) + "]");
                        processReadResponse(peripheral, cmd, value);
                    }
                })
                .build();
        ThreadPoolManager.INSTANCE.execute(() -> readRequest.execute(EasyBLE.getInstance().getConnection(peripheral.device)));
    }

    private void readRssi(RawBlePeripheral peripheral) {
        final String peripUuid = peripheral.id;
        //读取rssi
        Request readRequest = new RequestBuilderFactory()
                .getReadRssiBuilder()
                .setCallback(new ReadRssiCallback() {
                    @Override
                    public void onRssiRead(@NonNull Request request, int rssi) {
                        LogUtil.d(TAG, "rssi 读取成功给设备 " + peripUuid + " [" + rssi + "]");
                        peripheral.rssi = String.valueOf(rssi);
                    }

                    @Override
                    public void onRequestFailed(@NonNull Request request, int failType, int gattStatus, @Nullable Object value) {

                    }
                })
                .build();
        ThreadPoolManager.INSTANCE.execute(() -> readRequest.execute(EasyBLE.getInstance().getConnection(peripheral.device)));
    }

    // 记录一次失败的时间戳，添加到队列
    private void recordFailTimestamp() {
        long now = System.currentTimeMillis();
        failTimestamps.add(now);
        // 移除超过时间窗口的时间戳
        while (!failTimestamps.isEmpty() && (now - failTimestamps.peek() > TIME_WINDOW_MS)) {
            failTimestamps.poll();
        }
    }

    // 是否连接失败次数过多
    private boolean isFrequentFailures() {
        return failTimestamps.size() >= 3; // 这个数值，就是133连接失败的次数阈值
    }


    void processReadResponse(RawBlePeripheral peripheral, BleCmd cmd, byte[] data) {

        if (peripheral instanceof WT2BlePeripheral) {
            WT2BlePeripheral perip = (WT2BlePeripheral) peripheral;
            switch (cmd.id) {
                case BleCmdContant.AppCmdId.OEM:
                    perip.oem = new String(data);
                    break;
                case BleCmdContant.AppCmdId.SerialNumber:
                    perip.serialNumber = new String(data);
                    break;
                case BleCmdContant.AppCmdId.HardwareVersion:
                    perip.hardwareVersion = new String(data);
                    break;
                case BleCmdContant.AppCmdId.FirmwareVersion:
                    perip.firmwareVersion = new String(data);
                    break;
                case BleCmdContant.AppCmdId.Electric:
                    if (data.length > 0) perip.electric = data[0];
                    break;
                default:
                    break;
            }
        }
    }

    void processWCmdCharacteristicResponse(RawBlePeripheral peripheral, byte[] dataVal) throws IOException {
        if (dataVal.length < 3) {
            LogUtil.e(TAG, "未知响应: " + peripheral.id + " [" + toHexStr(dataVal, " "));
            return;
        }

        String tag = toHexStr(new byte[]{dataVal[2], dataVal[3]}, "-");
        BleCmd CMD = null;
        for (BleCmd cmd : WSeriesCmds) {
            if (tag.equals(cmd.getCmdTag())) {
                CMD = cmd;
                break;
            }
        }

        if (CMD == null) {
            LogUtil.d(TAG, peripheral.id + " 指令响应, 未知指令 [" + toHexStr(dataVal, " ") + "]第五个字节是长度");
            dispatchCmd(peripheral, "未知指令", dataVal);
            return;
        }
        LogUtil.d(TAG, peripheral.id + " 指令响应: " + CMD.id + " [" + toHexStr(dataVal, " ") + "]第五个字节是长度");
        dispatchCmd(peripheral, CMD.id, dataVal);

        if (peripheral instanceof WT2BlePeripheral) {
            WT2BlePeripheral perip = (WT2BlePeripheral) peripheral;
            switch (CMD.id) {
                case BleCmdContant.AppCmdId.Auth:
                    // 长度为 6 时, 是连接并监听服务后的鉴权
                    if (dataVal[4] == 0x06) {
                        // FIXME: 2022/5/20 认证的回调是在设备检查到 cmd 和 data 指令已被监听(端口已打开, 即gatt.setCharacteristicNotification(characteristic, notify)后), 后给 app 发送特征更新
                        bleDotAnalyzer.dotAuthStart(perip.id, System.currentTimeMillis());
                        perip.writeAppAuth(dataVal);
                    } else if (dataVal[4] == 0x00) { // 鉴权完成
                        bleDotAnalyzer.dotAuthEnd(perip.id, System.currentTimeMillis());
                        // 预初始化 opus
                        perip.preInitOpus();
                        perip.enableSpeaker();
                        perip.state = RawBlePeripheral.PeripheralState.DIDINIT;
                        EventBus.getDefault().post(new BleEventBean(BleEventName.BleConnectStandby, perip));  // 发送设备连接上的指令
                        dispatch(BleEventName.BleConnectStandby, perip);
                        readPeripheralInfo(perip);
//                        BleDotAnalyzer.INSTANCE.dotAuthEnd(perip.mac);
                    }
                    break;
                case BleCmdContant.AppCmdId.StW:
                    break;
                case BleCmdContant.AppCmdId.StR:
                    perip.stVersion = dataVal[dataVal.length - 1]; // 读取的固件版本为 0, 则认为失败, 需要进行内部 st 升级, 发送指令 sendSTChipUpgrade
                    perip.needUpgradeST = perip.stVersion == 0;
                    if (perip.stVersion == 0) {
                        dispatch(BleEventName.BleStError, perip);
                    }
                    break;
                case BleCmdContant.AppCmdId.TouchDownButton:
                    if (touchEventCallback != null) touchEventCallback.invoke(perip, perip.role);
                    break;
                default:
                    break;
            }
        }

    }

    // 处理 M 系列特征响应
    void processMCmdCharacteristicResponse(M2BlePeripheral peripheral, byte[] dataVal) {
        String tag = toHexStr(dataVal, "-");
        BleCmd CMD = null;
        for (BleCmd cmd : MSeriesCmds) {
            if (cmd.type != BleCmd.Type.NOTIFY) {
                continue;
            } // M 系列只匹配"监听"指令
            if (tag.equals(cmd.getCmdTag())) {
                CMD = cmd;
                break;
            }
        }
        LogUtil.d(TAG, Build.DEVICE + " ====== " + (Build.DEVICE != null && Build.DEVICE.matches(".+_cheets|cheets_.+")));

        if (CMD == null) {
            LogUtil.d(TAG, peripheral.id + " 指令响应, 未知指令 [" + toHexStr(dataVal, " "));
            return;
        }
        LogUtil.d(TAG, peripheral.id + " 指令响应: " + CMD.id + " [" + toHexStr(dataVal, " "));

        switch (CMD.id) {
            case BleCmdContant.AppCmdId.ChangeRecordToLeft:
                break;
            case BleCmdContant.AppCmdId.ChangeRecordToRight:
                break;
            case BleCmdContant.AppCmdId.LedR:
                break;
            case BleCmdContant.AppCmdId.TouchDownLeft:
                if (touchEventCallback != null)
                    touchEventCallback.invoke(peripheral, RawBlePeripheral.Role.Left);
                break;
            case BleCmdContant.AppCmdId.TouchDownRight:
                if (touchEventCallback != null)
                    touchEventCallback.invoke(peripheral, RawBlePeripheral.Role.Right);
                break;
            case BleCmdContant.AppCmdId.SlaveDisconnect:
                peripheral.isPaired = false;
                dispatch(BleEventName.BleDisconnectedSubPeripheral, peripheral);
                break;
            case BleCmdContant.AppCmdId.SlaveReconnect:
            case BleCmdContant.AppCmdId.M3SlaveReconnect:
                peripheral.isPaired = true;
                dispatch(BleEventName.BleConnectedSubPeripheral, peripheral);
                break;
            default:
                break;
        }
    }


    public void sendSTChipUpgrade(String peripUuid) {
        String cmdName = BleCmdContant.AppCmdId.StW;
        BleCmd cmd = this.getWSeriesCmd(cmdName);
        if (cmd == null) {
            LogUtil.e(TAG, "找不到指令 " + cmdName);
            return;
        }
        RawBlePeripheral peripheral = this.getPeripheral(peripUuid);
        if (peripheral == null) {
            LogUtil.e(TAG, "找不到设备 " + peripUuid);
            return;
        }

        this.sendCmd(peripheral, cmd, new byte[]{0x4});
    }


    public void sendSTChipUpgrade(RawBlePeripheral peripheral) {
        String cmdName = BleCmdContant.AppCmdId.StW;
        BleCmd cmd = this.getWSeriesCmd(cmdName);
        if (cmd == null) {
            LogUtil.e(TAG, "找不到指令 " + cmdName);
            return;
        }
        if (peripheral == null) {
            LogUtil.e(TAG, "设备为空！ ");
            return;
        }
        this.sendCmd(peripheral, cmd, new byte[]{0x4});
    }


    private void sendSTChipVersion(WT2BlePeripheral peripheral) {
        String cmdName = BleCmdContant.AppCmdId.StR;
        BleCmd cmd = this.getWSeriesCmd(cmdName);
        if (cmd == null) {
            LogUtil.e(TAG, "找不到指令 " + cmdName);
            return;
        }
        sendCmdToPeripherals(new RawBlePeripheral[]{peripheral}, cmd);
    }

    public void sendBleStartCmds(RawBlePeripheral[] perips, boolean isDuplexMode) {
        if (perips.length == 0) {
            LogUtil.e(TAG, "发送指令的设备数为空");
            return;
        }

        ArrayList<String> cmdIds = new ArrayList<String>(); // 录音开始指令
        if (isDuplexMode) cmdIds.add(BleCmdContant.AppCmdId.DepluxOpen);
        cmdIds.add(BleCmdContant.AppCmdId.RecordStart);

        for (RawBlePeripheral peripheral : perips) {
            ((WT2BlePeripheral) peripheral).needResetIndex = new Object();
        }
        this.sendCmdsToPeripherals(perips, cmdIds.toArray(new String[0]));
    }

    public void sendBleStopCmds(RawBlePeripheral[] perips, boolean isDuplexMode) {
        if (perips.length == 0) {
            LogUtil.e(TAG, "发送指令的设备数为空");
            return;
        }

        ArrayList<String> cmdIds = new ArrayList<String>(); // 录音停止指令
//        cmdIds.add(BleCmdContant.AppCmdId.StopPlayAudio);
        cmdIds.add(BleCmdContant.AppCmdId.RecordStop);
        if (isDuplexMode) cmdIds.add(BleCmdContant.AppCmdId.DepluxOpen);
        this.sendCmdsToPeripherals(perips, cmdIds.toArray(new String[0]));
    }

    public void sendBleStartManualCmds(RawBlePeripheral[] perips, boolean isDuplexMode) {
        if (perips.length == 0) {
            LogUtil.e(TAG, "发送指令的设备数为空");
            return;
        }

        ArrayList<String> cmdIds = new ArrayList<String>(); // 录音开始指令
        if (isDuplexMode) cmdIds.add(BleCmdContant.AppCmdId.DepluxOpen);
        cmdIds.add(BleCmdContant.AppCmdId.RecordStart);
        cmdIds.add(BleCmdContant.AppCmdId.EnableButton);

        for (RawBlePeripheral peripheral : perips) {
            ((WT2BlePeripheral) peripheral).needResetIndex = new Object();
        }
        this.sendCmdsToPeripherals(perips, cmdIds.toArray(new String[0]));
    }

    public void sendBleStopManualCmds(RawBlePeripheral[] perips, boolean isDuplexMode) {
        if (perips.length == 0) {
            LogUtil.e(TAG, "发送指令的设备数为空");
            return;
        }

        ArrayList<String> cmdIds = new ArrayList<String>(); // 录音停止指令
//        cmdIds.add(BleCmdContant.AppCmdId.StopPlayAudio);
        cmdIds.add(BleCmdContant.AppCmdId.RecordStop);
        cmdIds.add(BleCmdContant.AppCmdId.DisableButton);
        if (isDuplexMode) cmdIds.add(BleCmdContant.AppCmdId.DepluxOpen);
        this.sendCmdsToPeripherals(perips, cmdIds.toArray(new String[0]));
    }


    public void sendMSeriesSwitchMic(M2BlePeripheral perip, String role) {
        this.sendCmd(perip, Objects.equals(role, RawBlePeripheral.Role.Left.toString()) ? BleCmdContant.AppCmdId.ChangeRecordToLeft : BleCmdContant.AppCmdId.ChangeRecordToRight);
    }

    public void sendM3LightReadCmd(M2BlePeripheral perip) {
        this.sendCmd(perip, BleCmdContant.AppCmdId.LedR);
    }


    /**
     * 向多个设备同时(几乎)发送指令
     *
     * @param perips 设备数组
     * @param cmdIds 指令数组
     */
    public void sendCmdsToPeripherals(RawBlePeripheral[] perips, String[] cmdIds) {
        /* FIXME: 发送多个次指令, android 导致快速向一个设备写入指令, 会由于设备繁忙导致写入错误, 需要做成设备的指令队列 */
        LogUtil.d(TAG, "将发送多个指令 " + Arrays.toString(cmdIds));
        for (String cmdId : cmdIds) {
            BleCmd cmd = this.getWSeriesCmd(cmdId);
            this.sendCmdToPeripherals(perips, cmd);
        }
    }

    public void sendSound(WT2BlePeripheral peripheral, byte[] params, int maxByteSize, int queueSleepTime) {
        String cmdName = BleCmdContant.AppCmdId.Play;
        BleCmd cmd = this.getWSeriesCmd(cmdName);
        if (cmd == null) {
            LogUtil.e(TAG, "找不到指令 " + cmdName);
            return;
        }
        String uuid = UUID.randomUUID().toString();
        bleDotAnalyzer.dotSoundStart(peripheral.id, uuid, cmdName, System.currentTimeMillis());

        Request writeRequest = new RequestBuilderFactory()
                .getWriteCharacteristicBuilder(
                        UUIDHelper.uuidFromString(cmd.service),
                        UUIDHelper.uuidFromString(cmd.characteristic),
                        params
                ).setWriteOptions(
                        new WriteOptions.Builder()
                                .setPackageSize(maxByteSize)
                                .setRequestWriteDelayMillis(0)
                                .setWaitWriteResult(false)
                                .setWriteType(BluetoothGattCharacteristic.WRITE_TYPE_NO_RESPONSE)
                                .build()
                ).setTag(peripheral.role + " sendSound").setCallback(new WriteCharacteristicCallback() {
                    @Override
                    public void onCharacteristicWrite(@NonNull Request request, @NonNull byte[] value) {
                        bleDotAnalyzer.dotSoundSuccess(peripheral.id, uuid, System.currentTimeMillis());
                    }

                    @Override
                    public void onRequestFailed(@NonNull Request request, int failType, int gattStatus, @Nullable Object value) {
                        bleDotAnalyzer.dotSoundFail(peripheral.id, uuid, "code = " + failType, System.currentTimeMillis());
                    }
                }).build();

        writeRequest.execute(EasyBLE.getInstance().getConnection(peripheral.device));
//        this.sendCmd(peripheral, cmd, params, maxByteSize, queueSleepTime);

//        String peripUuid = peripheral.id;
//        byte[] data = params;
////        final String hexStr = toHexStr(data.length > 20 ? Arrays.copyOf(data, 20) : data, " ");
////        LogUtil.d(TAG, "写入开始 " + cmd.id + "指令 [" + hexStr + "] 给设备 " + peripUuid);
//        BleManager.shareInstance().write(peripUuid, cmd.service, cmd.characteristic, data, maxByteSize, queueSleepTime, (errDesc, rets) -> {
//            if (errDesc == null) {
////                LogUtil.d(TAG, "写入成功(等待回调) " + cmd.id + "指令 [" + hexStr + "] 给设备 " + peripUuid);
//            } else {
//                LogUtil.e(TAG, "写入错误 " + cmd.id + "指令 给设备 " + peripUuid + " " + errDesc);
//            }
//        });
    }

    public void setTouchEventCallback(TouchEventCallback callback) {
        touchEventCallback = callback;
    }

    /**
     * 向多个设备同时(几乎)发送指令
     *
     * @param perips 设备数组
     * @param cmd    指令
     */
    void sendCmdToPeripherals(RawBlePeripheral[] perips, BleCmd cmd) {
        LogUtil.d(TAG, "将发送单条指令 " + cmd.id);
        if (cmd.type == BleCmd.Type.READ) {
            for (RawBlePeripheral perip : perips) {
//                BleManager.shareInstance().read(perip.id, cmd.service, cmd.characteristic, (errDesc, rets) -> {
//
//                });
                //读取特征值
                Request readRequest = new RequestBuilderFactory()
                        .getReadCharacteristicBuilder(
                                UUIDHelper.uuidFromString(cmd.service),
                                UUIDHelper.uuidFromString(cmd.characteristic)
                        ).build();
                ThreadPoolManager.INSTANCE.execute(() -> readRequest.execute(EasyBLE.getInstance().getConnection(perip.device)));
            }
            return;
        }

        // 单个参数 和 多个参数
        byte[] datas = new byte[0];
        try {
            datas = cmd.getData();
//            int count = 3;
//            int index = 0;
//            while (index < count) {
            this.sendCmdToPeripherals(perips, cmd, datas);
//                LogUtil.d("[\(cmdId)] 指令是否成功 \(true) 当前次数: \(index + 1)");
//                Thread.sleep(100); // android 对单设备不能快速写入, 由于 mDeviceBusy(在 android 源码 BluetoothGatt 中)状态记录了正在进行"可靠写入"
//                index += 1;
//            }
        } catch (InterruptedException e) {
            e.printStackTrace();
        }
    }

    /**
     * 向多个设备同时(几乎)发送指令,
     *
     * @param perips 设备数组
     * @param cmd    指令
     * @param data   数据
     * @throws InterruptedException
     */
    private void sendCmdToPeripherals(RawBlePeripheral[] perips, BleCmd cmd, byte[] data) throws InterruptedException {
        for (RawBlePeripheral perip : perips) {
            String uuid = UUID.randomUUID().toString();
            bleDotAnalyzer.dotCmdStart(perip.id, uuid, cmd.type + "-" + cmd.id, System.currentTimeMillis());
            LogUtil.d(TAG, "发送开始 " + cmd.id + "指令 " + toHexStr(data) + " 给设备 " + perip.id);
            Request writeRequest = new RequestBuilderFactory()
                    .getWriteCharacteristicBuilder(
                            UUIDHelper.uuidFromString(cmd.service),
                            UUIDHelper.uuidFromString(cmd.characteristic),
                            data
                    ).setWriteOptions(
                            new WriteOptions.Builder()
                                    .setPackageSize(180)
                                    .setRequestWriteDelayMillis(0)
                                    .setWaitWriteResult(true)
                                    .setFailRetry(true)
                                    .setMaxRetryTimes(3)
                                    .setWriteType(BluetoothGattCharacteristic.WRITE_TYPE_DEFAULT)
                                    .build()
                    ).setCallback(new WriteCharacteristicCallback() {
                        @Override
                        public void onCharacteristicWrite(@NonNull Request request, @NonNull byte[] value) {
                            bleDotAnalyzer.dotCmdSuccess(perip.id, uuid, System.currentTimeMillis());
                        }

                        @Override
                        public void onRequestFailed(@NonNull Request request, int failType, int gattStatus, @Nullable Object value) {
                            // 主动清空队列导致的失败，忽略
                            if (failType == Connection.REQUEST_FAIL_TYPE_CONNECTION_CLEAR_REQUEST_QUEUE) {
                                bleDotAnalyzer.dotCmdEnd(perip.id, uuid, System.currentTimeMillis());
                                return;
                            }
                            bleDotAnalyzer.dotCmdFail(perip.id, uuid, "code = " + failType, System.currentTimeMillis());
                        }
                    }).build();

            ThreadPoolManager.INSTANCE.execute(() -> writeRequest.execute(EasyBLE.getInstance().getConnection(perip.device)));
        }

//        BleManager.shareInstance().writeMultiplePeripheral(peripUuids, cmd.service, cmd.characteristic, data, 180, (errDesc, rets) -> {
//            if (errDesc == null) {
//                LogUtil.d(TAG, "发送成功 " + cmd.id + "指令 " + toHexStr(data) + " 给设备 " + Arrays.toString(peripUuids));
//            } else {
//                LogUtil.d(TAG, "发送错误 " + cmd.id + "指令 " + toHexStr(data) + " 给设备 " + Arrays.toString(peripUuids) + " " + errDesc);
//            }
//            synchronized (perips) {
//                perips.notify();
//            }
//        });
//        synchronized (perips) {
//            perips.wait(1200);
//            BleManager.shareInstance().clearAllWriteListener();
//        }
    }

    public byte[] getM3CmdData(String cmdId) {
        BleCmd cmd = this.getMSeriesCmd(cmdId);
        if (cmd == null) {
            LogUtil.e(TAG, "找不到指令 " + cmdId);
            return null;
        }
        return cmd.getData();
    }

    // MARK: -
    private BleCmd getWSeriesCmd(String id) {
        for (BleCmd cmd : WSeriesCmds) {
            if (cmd.id.equalsIgnoreCase(id)) {
                return cmd;
            }
        }
        return null;
    }

    private BleCmd getMSeriesCmd(String id) {
        for (BleCmd cmd : MSeriesCmds) {
            if (cmd.id.equalsIgnoreCase(id)) {
                return cmd;
            }
        }
        return null;
    }

    private RawBlePeripheral getPeripheral(String id) {
        ConcurrentLinkedQueue<RawBlePeripheral> perips = this.searchedPeripherals;
        for (RawBlePeripheral perip : perips) {
            if (perip.id.equals(id)) {
                return perip;
            }
        }
        return null;
    }


//    public boolean sendCmd(String peripUuid, BleCmd cmd, byte[] data) {
//        boolean success = true;
//        LogUtil.d(TAG, "发送开始 " + cmd.id + "指令 [" + toHexStr(data, " ") + "] 给设备 " + peripUuid);
//        BleManager.shareInstance().write(peripUuid, cmd.service, cmd.characteristic, data, 180, (errDesc, rets) -> {
//            if (errDesc == null) {
//                LogUtil.d(TAG, "发送成功 " + cmd.id + "指令 [" + toHexStr(data, " ") + "] 给设备 " + peripUuid);
//            } else {
//                LogUtil.e(TAG, "发送错误 " + cmd.id + "指令 [" + toHexStr(data, " ") + "] 给设备 " + peripUuid + " " + errDesc);
//            }
//        });
//        return success;
//    }

    private void sendCmd(RawBlePeripheral perip, BleCmd cmd) {
        this.sendCmd(perip, cmd, null);
    }

    private void sendCmd(RawBlePeripheral perip, BleCmd cmd, byte[] param) {
        this.sendCmd(perip, cmd, param, 180, 0);
    }


    // 阻塞地发送命令到队列里面，外部都调用这个方法，sendCmd变成私有了
    public void sendCmdToQueue(RawBlePeripheral perip, String cmdId) {
        BleCmd cmd = getWSeriesCmd(cmdId);
        if (cmd == null) {
            LogUtil.e(TAG, "cmd没找到，cmdId不存在：" + cmdId);
            return;
        }
        try {
            sendCmdToPeripherals(new RawBlePeripheral[]{perip}, cmd);
        } catch (Exception e) {
            LogUtil.e(TAG, "sendCmdToQueue error: " + e.getMessage());
        }
    }

    private void sendCmd(RawBlePeripheral perip, String cmdId, byte[] param) {
        BleCmd cmd = this.getWSeriesCmd(cmdId);
        if (cmd == null) {
            LogUtil.e(TAG, "找不到指令 " + cmdId);
            return;
        }
        this.sendCmd(perip, cmd, param, 180, 0);
    }

    private void sendCmd(RawBlePeripheral perip, String cmdId) {
        BleCmd cmd = this.getWSeriesCmd(cmdId);
        if (cmd == null) {
            LogUtil.e(TAG, "找不到指令 " + cmdId);
            return;
        }
        this.sendCmd(perip, cmd, null, 180, 0);
    }

    private void sendCmd(M2BlePeripheral perip, String cmdId) {
        BleCmd cmd = this.getMSeriesCmd(cmdId);
        if (cmd == null) {
            LogUtil.e(TAG, "找不到指令 " + cmdId);
            return;
        }
        this.sendCmd(perip, cmd, null, 180, 0);
    }

    private void sendCmd(RawBlePeripheral perip, BleCmd cmd, byte[] param, int maxByteSize, int queueSleepTime) {
        String peripUuid = perip.id;
        if (cmd.type == BleCmd.Type.READ) {
            LogUtil.d(TAG, "读取开始 " + cmd.id + " 设备 " + peripUuid);
            //读取特征值
            Request readRequest = new RequestBuilderFactory()
                    .getReadCharacteristicBuilder(
                            UUIDHelper.uuidFromString(cmd.service),
                            UUIDHelper.uuidFromString(cmd.characteristic)
                    )
                    .setPriority(Integer.MAX_VALUE)//设置请求优先级
                    .setCallback(new ReadCharacteristicCallback() {
                        @Override
                        public void onRequestFailed(@NonNull Request request, int failType, int gattStatus, @Nullable Object value) {
                        }

                        @Override
                        public void onCharacteristicRead(@NonNull Request request, @NonNull byte[] value) {
                            LogUtil.d(TAG, "读取成功 " + cmd.id + " 给设备 " + peripUuid + " [" + new String(value) + "]");
                            processReadResponse(perip, cmd, value);
                        }
                    })
                    .build();
            ThreadPoolManager.INSTANCE.execute(() -> readRequest.execute(EasyBLE.getInstance().getConnection(perip.device)));
        } else if (cmd.type == BleCmd.Type.WRITE) {
            byte[] data = param == null ? cmd.getData() : cmd.getData(param);

            final String hexStr = toHexStr(data.length > 20 ? Arrays.copyOf(data, 20) : data, " ");
            LogUtil.d(TAG, "写入开始 " + cmd.id + " 指令 [" + hexStr + "] 给设备 " + peripUuid);

            //写特征值
            Request writeRequest = new RequestBuilderFactory()
                    .getWriteCharacteristicBuilder(
                            UUIDHelper.uuidFromString(cmd.service),
                            UUIDHelper.uuidFromString(cmd.characteristic),
                            data
                    ).setWriteOptions(
                            new WriteOptions.Builder()
                                    .setPackageSize(maxByteSize)
                                    .setPackageWriteDelayMillis(queueSleepTime)
                                    .setWaitWriteResult(true)
                                    .setFailRetry(true)
                                    .setMaxRetryTimes(3)
                                    .setWriteType(BluetoothGattCharacteristic.WRITE_TYPE_DEFAULT)
                                    .build()
                    ).setTag("cmd").build();

            ThreadPoolManager.INSTANCE.execute(() -> writeRequest.execute(EasyBLE.getInstance().getConnection(perip.device)));
        } else {
            LogUtil.e(TAG, "指令 " + cmd.id + " 不能被发送, 可能是监听指令? 此指令类型:" + cmd.type);
        }
    }

    public void dotCmdStart(String mac, String uuid, String type, Long time) {
        bleDotAnalyzer.dotCmdStart(mac, uuid, type, time);
    }

    public void dotCmdSuccess(String mac, String uuid, Long time) {
        bleDotAnalyzer.dotCmdSuccess(mac, uuid, time);
    }

    public void dotCmdFail(String mac, String uuid, String type, Long time) {
        bleDotAnalyzer.dotCmdFail(mac, uuid, type, time);
    }

    public void dotCmdEnd(String mac, String uuid, Long time) {
        bleDotAnalyzer.dotCmdEnd(mac, uuid, time);
    }

    //region: interface Listener/TouchEventCallback
    public interface Listener {
        void dispatchEvent(BleEventName type, RawBlePeripheral perip);

        default void dispatchCmdEvent(RawBlePeripheral perip, String cmdId, byte[] values) {
        }

        void onBluetoothStateUpdate(int state);
    }

    /**
     * 耳机点击事件,
     * W 系列是直接返回设备, 无需关心 role;
     * M 系列(tws)则是返回当前设备, 并且指示点击的左耳或者右耳(角色 role 指示左右)
     */
    public interface TouchEventCallback {
        void invoke(RawBlePeripheral perip, RawBlePeripheral.Role role);
    }
    //endregion
}


