package co.timekettle.btkit.parser;

import java.util.Arrays;

import co.timekettle.btkit.LogUtil;
import co.timekettle.btkit.bean.RawBlePeripheral;
import cn.wandersnail.ble.Device;

/**
 * 自定义解析类，可以解析一些默认的数据
 * @author: licoba
 * @date: 2022/7/14
 */
public abstract class StandParser<T extends RawBlePeripheral> {
    public static final String TAG = "StandParser";

    public abstract T parseManufacturerSpecificData(Device device);

    // ManufacturerData 字段类型
//    AD Type 							Value				描述
//    Flags 							0x01				广播出自己蓝牙某些特性
//    ServiceUUIDs						0x02~0x07			广播出自己服务的UUID
//    Local Name						0x08/0x09			广播出自己的蓝牙名字
//    TXPowerLevel						Ox0A				广播出自已的射频发射功率
//    SimplePairingOptionOoBTags		OxOD~0x0F 			广播出安全管理带外标签(本文忽略）
//    Security Manager TK Value 		0x10				广播出带外方式配对绑定时的TK(本文忽略)
//    SecurityManager OoB Flags			0x11				广播出带外特性标志(本文忽略）
//    Slave Connection Interval Range 	0x12				广播出自己希望的连接参数范围
//    Service Solicitation				0x14/0x15			广播出自已希望来接自己已的主机有特定的服务
//    Service Data 						0x16				服务数据
//    ManufacturerSpecificData 			OxFF				广播出厂商信息（用户可以放自定义数据）

    // manufacturerData
    // 00-02: 0x2 0x1(Flags) 0x5
    // 03-20: 0x11 0x7(ServiceUUIDs) 0xd9 0xd 0xc4 0x5c 0xc4 0xdd 0xb1 0x84 0x69 0x46 0x2f 0x43 0x28 0x6f 0x2e 0x59
    // 21-60: 0x1c 0xff(ManufacturerSpecificData) 0xff 0xff 0xec 0x45 0x3a 0xdf 0xec 0xe0 0x4 0xd 0xf 0xbd 0x5f 0x3 0x0 0xae 0x0 0x57 0x54 0x58 0x2d 0x52 0x20 0x20 0x20 0x0 0x2 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x0]
//    public byte[] getManufacturerBytes(RawBlePeripheral.RawData rawData) {
////        LogUtil.d(TAG, "将解析: " + rawData.id + " 厂商数据");
//        RawBlePeripheral.Advertising advertising = rawData.advertising;
//        assert advertising != null : "advertising 不能为空";
//        if (advertising == null) return null;
//        RawBlePeripheral.ManufacturerData manufacturerData = advertising.manufacturerData;
//        assert manufacturerData != null : "advertising.manufacturerData 不能为空";
//        if (manufacturerData == null) return null;
//        byte[] bytes = manufacturerData.bytes;
//        assert bytes != null : "advertising.manufacturerData.bytes 不能为空";
//        if (bytes == null) return null;
//        return bytes;
//    }

    // 找到自定义数据 (type为0xFF的数据)
    public byte[] getManufacturerSpecificBytes(byte[] manufacturerData) {
        // 001原始数据: [2, 1, 5,
        //             17, 7, 217, 13, 196, 92, 196, 221, 177, 132, 105, 70, 47, 67, 38, 111, 46, 89,
        //             26, 255, 255, 255, 247, 230, 121, 170, 153, 198, 0, 30, 14, 172, 55, 4, 0, 174, 0, 87, 84, 50, 45, 48, 49, 66, 76, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0]
        // 返回： [26（package长度）, 255(0xff的type), 255, 255, 247, 230, 121, 170, 153, 198, 0, 30, 14, 172, 55, 4, 0, 174, 0, 87, 84, 50, 45, 48, 49, 66, 76, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0]
        if (manufacturerData == null || manufacturerData.length == 0) { // 不断根据length裁剪ret
            return new byte[0];
        }

        byte[] bytes = manufacturerData;
        int offset = 0;
        int remain = bytes.length;
        while (remain > 1) {
            byte len = bytes[offset];
            byte type = bytes[offset + 1];
            if ((type & 0xff) == 0xff) {
                bytes = Arrays.copyOfRange(manufacturerData, offset, bytes.length);
                return bytes;
            }
            offset = offset + len + 1;
            remain = bytes.length - offset;
        }
        return new byte[0];
    }
}
