package co.timekettle.btkit.parser;

import java.util.Arrays;

import co.timekettle.btkit.BleUtil;
import co.timekettle.btkit.bean.M2BlePeripheral;
import co.timekettle.btkit.bean.RawBlePeripheral;
import cn.wandersnail.ble.Device;

public class M3Parser extends StandParser<M2BlePeripheral> {

    public static final String TAG = "M3Parser";

    // M3
//    Company ID （2字节）+ MAC地址BT（6字节）+ MAC地址BLE（6字节）+ 固件版本号（2字节）+电量L（1字节）+电量R（1字节）+硬件版本号（1字节）+广播名(M3-L/M3-R)（4字节）+ 主从配对标志（1字节）

//    Company ID：2字节。固定为FFFF。
//    Mac地址(L/R)：6字节。左右耳BT地址相同，左右耳BLE地址相同，BT和BLE地址先按相同处理，如果有问题在改成不同地址，规定蓝牙BLE地址使用的号段范围：F0:6F:2A:00:00:00—F0:6F:2A:FF:FF:FF，其中高24bit为固定F0:6F:2A，低24bit为mac递增范围
//    主从配对标志位：00代表没组队，01代表组队成功
//    固件版本号：不区分左右耳，2字节。例如原始数据是12 03，则对应的版本号是V1.2.3。
//    两个字节表示三位的版本号，则第一个字节需要拆分成两位数，V1.0.25中的：
//    第一个1，为原始数据第一个字节的高4位，16进制的1转化为10进制，也是1；
//    第二个2，为原始数据第一个字节的低4位，16进制的2转化为10进制，也是2；
//    第三个3，则由第二个字节的03(十六进制)转化而来 版本号最高表示9.9.9
//    电量(L/R)：1字节，单位百分比；例如原始数据为55，表示电量(L/R)为86%。
//    硬件版本号：1字节。例如12：高4位表示表示1，低4位表示2，则App显示为v1.2，最高为9.9
//    广播名：4字节，若右耳为主机，则广播名为M3-R；若左耳为主机，则广播名为M3-L。只有主机会发出ble广播

    //    2022.03.31修改内容
//    1. 固件版本号不区分左右耳
//    2. 耳机电量最高表示9.9.9，第一个字节做拆分
//    3. 广播名修改下划线为横杠
//    4. 硬件版本号不做左右耳区分
//    5. 新增一个翻译模式：AI外教0x06
    @Override
    public M2BlePeripheral parseManufacturerSpecificData(Device device) {
        byte[] manufacturerData = device.getScanRecord();
        byte[] specificData = getManufacturerSpecificBytes(manufacturerData);
//      assert manufacturerSpecificData.length != 0: "advertising.manufacturerData.bytes 错误";
        if (specificData.length == 0) return null;

        M2BlePeripheral peripheral = new M2BlePeripheral(device);
        peripheral.discoverTimestamp = System.currentTimeMillis();

        // 长度 + 类型（2字节）
        int offset = 2;

        // Company ID （2字节）
        offset = offset + 2;

        // MAC地址L（6字节）+ MAC地址R（6字节）
        int macLen = 6;
        int mac_end_flag = offset + macLen * 2;
        byte[] macArray = Arrays.copyOfRange(specificData, offset, mac_end_flag);
        byte[] mac0Array = Arrays.copyOfRange(macArray, 0, macLen);
        byte[] mac1Array = Arrays.copyOfRange(macArray, macLen, macLen * 2);
        peripheral.mac = new String[]{BleUtil.toHexStr(mac0Array, ":"), BleUtil.toHexStr(mac1Array, ":")};
        peripheral.macBytes = macArray;
        peripheral.macSuffix4 = new String[]{BleUtil.toHexStr(Arrays.copyOfRange(mac0Array, mac0Array.length - 2, mac0Array.length), ""), BleUtil.toHexStr(Arrays.copyOfRange(mac1Array, mac1Array.length - 2, mac1Array.length), "")};
        offset = offset + macLen * 2;

        // 固件版本号（2字节）
        int major = (specificData[offset] & 0xf0) >> 4;
        int minor = specificData[offset] & 0x0f;
        int patch = specificData[offset + 1];
        String version = major + "." + minor + "." + patch;
        peripheral.firmwareVersion = new String[]{version, version};
        offset = offset + 2;

        // 电量L（1字节）+ 电量R（1字节）
        peripheral.electric = new int[]{specificData[offset], specificData[offset + 1]};
        offset = offset + 2;

        // 硬件版本号（1字节）
        peripheral.hardwareVersion = ((specificData[offset] & 0xf0) >> 4) + "." + (specificData[offset] & 0x0f);
        offset = offset + 1;

        // 广播名(M3-L/M3-R)（4字节）
        byte[] nameArray = Arrays.copyOfRange(specificData, offset, offset + 4);
        peripheral.name = new String(nameArray);
        offset = offset + 4;

        // 主从配对标志（1字节）
        peripheral.isPaired = specificData[offset] == 0x01;
        offset = offset + 1;

        // 其他
        peripheral.host = peripheral.name.contains("-L") ? RawBlePeripheral.Role.Left : RawBlePeripheral.Role.Right;

        return peripheral;
    }
}
