package co.timekettle.btkit.parser;

import java.util.Arrays;

import co.timekettle.btkit.BleUtil;
import co.timekettle.btkit.bean.M2BlePeripheral;
import co.timekettle.btkit.bean.RawBlePeripheral;
import cn.wandersnail.ble.Device;

public class M2PParser extends StandParser<M2BlePeripheral> {

    // M2P
    // Company ID （2字节）+ MAC地址L（6字节）+MAC地址R（6字节）+固件版本号L（2字节）+固件版本号R（2字节）+电量L（1字节）+电量R（1字节）+ 硬件版本号（1字节）+广播名（M2-L/M2-R）（4字节）
//    [26, -1, -1, -1, 124, -106, -46, -123, 102, -108(macL), 124, -106, -46, -123, 102, -108(macR), 16, 14(左版本), 16, 14(右版本), 99, 100(电量), 1(硬件版本), 77, 50, 45, 82, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0]

//    Company ID：2字节。固定为FFFF。
//    Mac地址(L/R)：6字节。主从连上时，L和R的Mac地址相同；左耳(从)断开时，Mac地址L全为0；右耳(从)断开时，Mac地址R全为0。
//    固件版本号(L/R)：2字节。例如原始数据是1019，则对应的版本号是V1.0.25。两个字节表示三位的版本号，则第一个字节需要拆分成两位数，V1.0.25中的：
//    第一个1，为原始数据第一个字节的高4位，16进制的1转化为10进制，也是1；
//    第二个0，为原始数据第一个字节的低4位，16进制的0转化为10进制，也是0；
//    第三个25，则由第二个字节的19(十六进制)转化而来
//    电量(L/R)：1字节，单位百分比；例如原始数据为55，表示电量(L/R)为86%。
//    硬件版本号：1字节。
//    广播名：4字节，若右耳为主机，则广播名为M2-R；若左耳为主机，则广播名为M2-L
    @Override
    public M2BlePeripheral parseManufacturerSpecificData(Device device) {
        byte[] manufacturerData = device.getScanRecord();
        byte[] specificData = getManufacturerSpecificBytes(manufacturerData);
//      assert manufacturerSpecificData.length != 0: "advertising.manufacturerData.bytes 错误";
        if (specificData.length == 0) return null;

        M2BlePeripheral peripheral = new M2BlePeripheral(device);
        peripheral.discoverTimestamp = System.currentTimeMillis();

        // 长度 + 类型（2字节）
        int offset = 2;

        // Company ID （2字节）
        offset = offset + 2;

        // MAC地址L（6字节）+ MAC地址R（6字节）
        int macLen = 6;
        int mac_end_flag = offset + macLen * 2;
        byte[] macArray = Arrays.copyOfRange(specificData, offset, mac_end_flag);
        byte[] mac0Array = Arrays.copyOfRange(macArray, 0, macLen);
        byte[] mac1Array = Arrays.copyOfRange(macArray, macLen, macLen * 2);
        peripheral.mac = new String[]{BleUtil.toHexStr(mac0Array, ":"), BleUtil.toHexStr(mac1Array, ":")};
        peripheral.macBytes = macArray;
        peripheral.macSuffix4 = new String[]{BleUtil.toHexStr(Arrays.copyOfRange(mac0Array, mac0Array.length - 2, mac0Array.length), ""), BleUtil.toHexStr(Arrays.copyOfRange(mac1Array, mac1Array.length - 2, mac1Array.length), "")};
        offset = offset + macLen * 2;

        // 固件版本号（4字节，左耳和右耳固件版本）
        String versionL, versionR;
        {
            int major = (specificData[offset] & 0xf0) >> 4;
            int minor = specificData[offset] & 0x0f;
            int patch = specificData[offset + 1];
            versionL = major + "." + minor + "." + patch;
            offset = offset + 2;
        }
        {
            int major = (specificData[offset] & 0xf0) >> 4;
            int minor = specificData[offset] & 0x0f;
            int patch = specificData[offset + 1];
            versionR = major + "." + minor + "." + patch;
            offset = offset + 2;
        }
        peripheral.firmwareVersion = new String[]{versionL, versionR};

        // 电量（2字节，左耳和右耳电量）
        peripheral.electric = new int[]{specificData[offset], specificData[offset + 1]};
        offset = offset + 2;

        // 硬件版本号（1字节）
        peripheral.hardwareVersion = specificData[offset] + "";
        offset = offset + 1;

        // 广播名（M2）（8字节）
        byte[] nameArray = Arrays.copyOfRange(specificData, offset, offset + 4);
        peripheral.name = new String(nameArray);

        // 其他
        peripheral.host = peripheral.name.contains("-L") ? RawBlePeripheral.Role.Left : RawBlePeripheral.Role.Right;
        peripheral.isPaired = !(Arrays.equals(mac0Array, new byte[]{0, 0, 0, 0, 0, 0}) || Arrays.equals(mac1Array, new byte[]{0, 0, 0, 0, 0, 0}));

        return peripheral;
    }
}
