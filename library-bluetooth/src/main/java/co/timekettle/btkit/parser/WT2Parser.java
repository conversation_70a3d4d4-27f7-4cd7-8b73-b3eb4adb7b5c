package co.timekettle.btkit.parser;

import android.bluetooth.le.ScanRecord;
import android.bluetooth.le.ScanResult;
import android.os.ParcelUuid;
import android.text.TextUtils;

import java.util.Arrays;
import java.util.List;
import java.util.Objects;

import co.timekettle.btkit.BleCmdContant;
import co.timekettle.btkit.BleUtil;
import co.timekettle.btkit.LogUtil;
import co.timekettle.btkit.UUIDHelper;
import co.timekettle.btkit.bean.RawBlePeripheral;
import co.timekettle.btkit.bean.WT2BlePeripheral;
import cn.wandersnail.ble.Device;

import static co.timekettle.btkit.BleCmdContant.ScanUUIDFilter;
import static co.timekettle.btkit.BleCmdContant.StandardUUIDWith;

/**
 * @author: licoba
 * @date: 2022/7/14
 */
public class WT2Parser extends StandParser<WT2BlePeripheral> {

    public static final String TAG = "WT2Parser";

    // WT2 001/004
    // 长度XXH + 类型08H + 数据XXH，MAC地址 + 软件版本号 + 电压电量 + 硬件版本号 + SD版本号 + BL版本号 + 调试信息（暂时）

    // 长度XX + 类型FF + 数据，共占用29字节，数据部分27字节具体为：
    // Company ID （2字节）+ MAC地址（6字节）+ 软件版本号（2字节）+ 电压（2字节）+ 电量（1字节）+ 硬件版本号（1字节）+ SD版本号（2字节）+ BL版本号（1字节）+ 广播名（WTX-X）（8字节）+ ST状态（1字节）+ ST版本号（1字节）
    // Company ID：FFFF，2字节，大端模式
    // MAC地址：6字节；大端模式
    // 版本号：软件版本号2字节
    //      软件版本表示方法说明：如V1.0.25
    //      第一个1，为第一个字节的高4位，范围0-9
    //      第二个0，为第一个字节的低4位，范围0-9
    //      第三个25，为第二个字节，范围0-99
    // 电压：2字节，单位mv，大端模式；
    // 电量：1字节，单位百分比；
    // 硬件版本号1字节；
    // SD版本号2字节；
    // BL版本号1字节；
    // 广播名：8字节，大端模式

    // WT2 各个设备 ManufacturerSpecificData
    // WTX-R  [255, 255, 236, 69, 58, 223, 236, 224, 4, 42, 15, 214, 95, 3, 0, 174, 0, 87, 84, 88, 45, 82, 32, 32, 32, 0, 6, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0] 长度39=17+8+14
    //      --> 转 ascii 后  "ÿÿìE:ßìà{04}*\0\0\0{03}\0®\0WTX-R   \0\0"
    //
    // WT2-02 [255, 255, 255, 197, 105, 181, 231, 18(mac),  0, 29(版本号), 15, 201, 95(电量), 4, 0, 174, 0, 87, 84, 50, 45, 48, 50, 66, 76] 长度39=17+8+14
    //      --> 转 ascii 后  "ÿÿÿÅiµç{12}\0{1D}{0F}¾_{04}\0®\0WT2-02BL"
    //
    // WT2-01 [255, 255, 230, 167, 188, 145, 148, 128(mac), 0, 30(版本号), 16, 32, 100(电量), 4, 0, 174, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0] 长度31=17+14
    //      --> 转 ascii 后  "ÿÿæ§¼\0{1E}{10}{15}d{04}\0®\0"

    @Override
    public WT2BlePeripheral parseManufacturerSpecificData(Device device) {
        byte[] manufacturerData = device.getScanRecord();
        byte[] manufacturerSpecificData = getManufacturerSpecificBytes(manufacturerData);
//      assert manufacturerSpecificData.length != 0: "advertising.manufacturerData.bytes 错误";
        if (manufacturerSpecificData.length == 0) {
            ScanResult result = device.getScanResult();
            ScanRecord record = result == null ? null : result.getScanRecord();
            List<ParcelUuid> uuids = record == null ? null : record.getServiceUuids();
            if (uuids != null && !uuids.isEmpty()) {
                String serviceUUID = UUIDHelper.uuidToString(uuids.get(0).getUuid());
                if (serviceUUID.length() == 4) {
                    serviceUUID = StandardUUIDWith(serviceUUID);
                }
                if (serviceUUID.equalsIgnoreCase(ScanUUIDFilter.W_UPGRADE.getUuidString())) { // dfu 状态下的设备
                    WT2BlePeripheral peripheral = new WT2BlePeripheral(device);
                    peripheral.discoverTimestamp = System.currentTimeMillis();
                    peripheral.needUpgrade = true;
                    return peripheral;
                }
            }
            return null;
        }

        WT2BlePeripheral peripheral = new WT2BlePeripheral(device);
        peripheral.discoverTimestamp = System.currentTimeMillis();
        // 长度 + 类型（2字节）
        int offset = 2;

        // Company ID （2字节）
        offset = offset + 2;

        // MAC地址（6字节）
        int macLen = 6;
        int mac_end_flag = offset + macLen;
        byte[] macArray = Arrays.copyOfRange(manufacturerSpecificData, offset, mac_end_flag);
        peripheral.mac = BleUtil.toHexStr(macArray, ":");
        peripheral.macBytes = macArray;
        peripheral.macSuffix4 = BleUtil.toHexStr(Arrays.copyOfRange(macArray, macArray.length - 2, macArray.length), "");

        offset = offset + macLen;

        // 软件版本号（2字节）
        int major = (manufacturerSpecificData[offset] & 0xf0) >> 4;
        int minor = manufacturerSpecificData[offset] & 0x0f;
        int patch = manufacturerSpecificData[offset + 1];
        String version = (major < 10 ? "0" : "") + major + "." + (minor < 10 ? "0" : "") + minor + "." + (patch < 10 ? "0" : "") + patch;
        peripheral.firmwareVersion = version;
        offset = offset + 2;

        // 电压（2字节）
        offset = offset + 2;

        // 电量（1字节）
        peripheral.electric = manufacturerSpecificData[offset];
        offset = offset + 1;

        // 硬件版本号（1字节）
        peripheral.hardwareVersion = manufacturerSpecificData[offset] + "";
        offset = offset + 1;

        // SD版本号（2字节）
        offset = offset + 2;

        // BL版本号（1字节）
        offset = offset + 1;

        // 广播名（WTX-X）（8字节）
        if (peripheral.productType == BleCmdContant.ProductType.WT2_Edge || peripheral.productType == BleCmdContant.ProductType.W3_Pro) {
            byte[] nameArray = Arrays.copyOfRange(manufacturerSpecificData, offset, offset + 8);
            peripheral.name = new String(nameArray);
            offset = offset + 8;

            // ST状态（1字节）
            offset = offset + 1;

            // ST版本号（1字节）
            // #warning("耳机关机后重启有概率性 stVersion 读取为 0, 即收到 ble 广播时固件仍可能未读取到 st 芯片版本, 此值通过连接后指令读取")
//            peripheral.stVersion = manufacturerSpecificData[offset];
            offset = offset + 1;
        } else {
            // 广播名为空, 或者 oppo reno A 手机若广播包没有广播名称, 会返回 mac 地址作为名称, 若名称跟 mac 地址相同也去取名字
            if (peripheral.name == null || TextUtils.isEmpty(peripheral.name) || peripheral.mac.equalsIgnoreCase(peripheral.name)) {
                byte[] nameArray = Arrays.copyOfRange(manufacturerSpecificData, offset, offset + 8);
                peripheral.name = new String(nameArray);
            }
        }

        // 其他
        if (peripheral.name != null) {
            if (peripheral.productType == BleCmdContant.ProductType.WT2) {
                peripheral.role = peripheral.name.contains("-01") ? RawBlePeripheral.Role.Left : RawBlePeripheral.Role.Right;
            } else if (peripheral.productType == BleCmdContant.ProductType.WT2_Edge) {
                peripheral.role = peripheral.name.contains("-L") ? RawBlePeripheral.Role.Left : RawBlePeripheral.Role.Right;
            } else if (peripheral.productType == BleCmdContant.ProductType.W3_Pro) {
                peripheral.role = peripheral.name.contains("-L") ? RawBlePeripheral.Role.Left : RawBlePeripheral.Role.Right;
            }
            peripheral.workRole = peripheral.role == RawBlePeripheral.Role.Right ? "Self" : "Other";
        }
        return peripheral;
    }
}
