package co.timekettle.btkit.parser;

import java.util.Arrays;

import co.timekettle.btkit.BleUtil;
import co.timekettle.btkit.bean.M2BlePeripheral;
import co.timekettle.btkit.bean.RawBlePeripheral;
import cn.wandersnail.ble.Device;

/**
 * @author: licoba
 * @date: 2022/7/14
 */
public class M2Parser extends StandParser<M2BlePeripheral> {

    public static final String TAG = "M2Parser";

    // M2
    // Company ID （2字节）+ MAC地址L（6字节）+ MAC地址R（6字节）+ 软件版本号（2字节）+ 电量L（1字节）+ 电量R（1字节）+ 硬件版本号（1字节）+ 广播名（M2-L or M2-R）（8字节）
    //    Company ID：FFFF，2字节； 版本号：软件版本号2字节
    //    软件版本表示方法说明：如V1.0.25
    //    第一个1，为第一个字节的高4位，范围0-F
    //    第二个0，为第一个字节的低4位，范围0-F
    //    第三个25，为第二个字节，范围0-255
    //
    //    电量：1字节，单位百分比；
    //    硬件版本号1字节；
    //             APP扫描到广播名后，自动在列表中对显示名进行后缀补充，格式为M2-X-XXXX，其中“-XXXX”为MAC地址的最低两个字节数据。
    //    示例：如MAC地址为：FA:E4:4D:84:3A:F7，并且该设备为右耳，则显示M2-R-3AF7；该设备为左耳，则显示M2-L-3AF7
    @Override
    public M2BlePeripheral parseManufacturerSpecificData(Device device) {
        byte[] manufacturerData = device.getScanRecord();
        byte[] specificData = getManufacturerSpecificBytes(manufacturerData);
//      assert manufacturerSpecificData.length != 0: "advertising.manufacturerData.bytes 错误";
        if (specificData.length == 0) return null;

        M2BlePeripheral peripheral = new M2BlePeripheral(device);
        peripheral.discoverTimestamp = System.currentTimeMillis();

        // 长度 + 类型（2字节）
        int offset = 2;

        // Company ID （2字节）
        offset = offset + 2;

        // MAC地址L（6字节）+ MAC地址R（6字节）
        int macLen = 6;
        int mac_end_flag = offset + macLen * 2;
        byte[] macArray = Arrays.copyOfRange(specificData, offset, mac_end_flag);
        byte[] mac0Array = Arrays.copyOfRange(macArray, 0, macLen);
        byte[] mac1Array = Arrays.copyOfRange(macArray, macLen, macLen * 2);
        peripheral.mac = new String[]{BleUtil.toHexStr(mac0Array, ":"), BleUtil.toHexStr(mac1Array, ":")};
        peripheral.macBytes = macArray;
        peripheral.macSuffix4 = new String[]{BleUtil.toHexStr(Arrays.copyOfRange(mac0Array, mac0Array.length - 2, mac0Array.length), ""), BleUtil.toHexStr(Arrays.copyOfRange(mac1Array, mac1Array.length - 2, mac1Array.length), "")};
        offset = offset + macLen * 2;

        // 软件版本号（2字节）
        int major = (specificData[offset] & 0xf0) >> 4;
        int minor = specificData[offset] & 0x0f;
        int patch = specificData[offset + 1];
        String version = major + "." + minor + "." + patch;
        peripheral.firmwareVersion = new String[]{version, version};
        offset = offset + 2;

        // 电量L（1字节）+ 电量R（1字节）
        peripheral.electric = new int[]{specificData[offset], specificData[offset + 1]};
        offset = offset + 2;

        // 硬件版本号（1字节）
        peripheral.hardwareVersion = specificData[offset] + "";
        offset = offset + 1;

        // 广播名（M2-L or M2-R）（8字节）
        byte[] nameArray = Arrays.copyOfRange(specificData, offset, offset + 8);
        peripheral.name = new String(nameArray);

        // 其他
        peripheral.host = peripheral.name.contains("-L") ? RawBlePeripheral.Role.Left : RawBlePeripheral.Role.Right;
        peripheral.isPaired = !(Arrays.equals(mac0Array, new byte[]{0, 0, 0, 0, 0, 0}) || Arrays.equals(mac1Array, new byte[]{0, 0, 0, 0, 0, 0}));

        return peripheral;
    }
}
