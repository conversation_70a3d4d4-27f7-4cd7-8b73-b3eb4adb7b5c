package co.timekettle.btkit.bean;

import static co.timekettle.btkit.BleCmdContant.WSeriesCmds;


import android.bluetooth.BluetoothGattCharacteristic;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;

import org.concentus.OpusException;
import org.greenrobot.eventbus.EventBus;
import org.greenrobot.eventbus.Subscribe;
import org.greenrobot.eventbus.ThreadMode;

import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.io.Serializable;
import java.util.Arrays;
import java.util.HashMap;
import java.util.Map;
import java.util.UUID;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.LinkedBlockingQueue;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.function.Function;

import cn.wandersnail.ble.Connection;
import cn.wandersnail.ble.EasyBLE;
import cn.wandersnail.ble.Request;
import cn.wandersnail.ble.RequestBuilderFactory;
import cn.wandersnail.ble.WriteOptions;
import cn.wandersnail.ble.callback.WriteCharacteristicCallback;
import co.timekettle.btkit.BleCmd;
import co.timekettle.btkit.BleCmdContant;
import co.timekettle.btkit.BleUtil;
import co.timekettle.btkit.BytesTrans;
import co.timekettle.btkit.LogUtil;
import co.timekettle.btkit.ResampleProcessor;
import cn.wandersnail.ble.Device;
import co.timekettle.btkit.ThreadPoolManager;
import co.timekettle.btkit.UUIDHelper;
import co.timekettle.btkit.dfu.DfuUtil;
import co.timekettle.btkit.protocol.IProtocolWT2;
import co.timekettle.opus.OpusCodec;

/**
 * W系列产品的Bean类
 *
 * @author: licoba
 * @date: 2022/7/14
 */
public class WT2BlePeripheral extends RawBlePeripheral implements IProtocolWT2 {
    public static final String TAG = "WT2BlePeripheral";

    public String mac = "";
    public byte[] macBytes; // m2 m2+ 为 2 * 6 字节
    public String macSuffix4;
    public Role role; // 耳机的属性, 本身是左耳, 还是右耳

    public String oem;
    public String serialNumber;
    public String hardwareVersion;
    public String firmwareVersion;

    public int electric;

    public int stVersion = -1; // 读取的固件版本为 0, 则认为失败, 需要进行内部 st 升级(needUpgradeST 标记为 true), 发送指令 sendSTChipUpgrade，初始值置为-1
    public boolean needUpgradeST; // 内部 <ST 芯片> 是否需要升级

    public float volume;
    public int type; // 产品类型

    public Object needResetIndex; // 标记是否需要重置录音数据的序号

    public String workRole; // 通道工作角色, 用于区分, 默认右耳为"Self", 左耳为"Other", "Self"指已方语言设备, "Other"是对方语言设备

    private Speaker speaker = new Speaker(id); // 播放器
    public Recorder recorder = new Recorder(id); // 录音器
//    private Boolean isSpeakerEnabled = false;


    private static final String CmdServiceUUID = BleCmdContant.UUIDWith("0004");
    private static final String CmdWriteCharacteristicUUID = BleCmdContant.UUIDWith("0005");
    private static final String CmdNotifyCharacteristicUUID = BleCmdContant.UUIDWith("0006");

    public WT2BlePeripheral(Device device) {
        super(device);
        role = Role.Left;
        volume = 0.9f; // 初始音量
        speaker = new Speaker(id); // 播放器
        recorder = new Recorder(id); // 录音器
//        LogUtil.d(TAG, this.name + " 初始化" + this);
    }

    public void preInitOpus() {
        LogUtil.d(TAG, "preInitOpus: " + this.mac);
        try {
            speaker.getOpusEncoder().encode(new byte[640], 320, new byte[640]);
        } catch (OpusException | AssertionError e) {
            LogUtil.e(TAG, "preInitOpus error: " + e.getMessage());
        }
    }

    @Override
    protected void finalize() throws Throwable {
        super.finalize();
//        LogUtil.d(TAG, this.name + " 反初始化 " + this.discoverTimestamp);
    }


    @Override
    public String toString() {
        return "WT2BlePeripheral{" +
                "mac='" + mac + '\'' +
                ", macBytes=" + Arrays.toString(macBytes) +
                ", macSuffix4='" + macSuffix4 + '\'' +
                ", role=" + role +
                ", oem='" + oem + '\'' +
                ", serialNumber='" + serialNumber + '\'' +
                ", hardwareVersion='" + hardwareVersion + '\'' +
                ", firmwareVersion='" + firmwareVersion + '\'' +
                ", electric=" + electric +
                ", stVersion=" + stVersion +
                ", volume=" + volume +
                ", type=" + type +
                ", state=" + state +
                '}';
    }

    /**
     * 启用播放器, 设备才能够接收播放数据
     */
    public void enableSpeaker() {
//        if(isSpeakerEnabled){return;}
//        isSpeakerEnabled = true;
        if (!EventBus.getDefault().isRegistered(speaker)) {
            EventBus.getDefault().register(speaker);
        }
    }

    /**
     * 停用播放器, 设备才能够停止接收播放数据
     */
    public void disableSpeaker() {
//        LogUtil.d(TAG, this.name + " 反注册 " + this.discoverTimestamp);
        EventBus.getDefault().unregister(speaker);
        // 停止全部任务
        if (speaker != null) speaker.stop();
//        isSpeakerEnabled = false;
    }

    public void startDfu(String filePath, DfuUtil.Promise promise) {
        DfuUtil.shared.startDFU(this.id, this.name, filePath, promise);
//        DfuUtil.shared.startDFU(this.id, this.name, filePath, new DfuUtil.Promise() {
//            @Override
//            public void onComplete(String deviceAddress) {
//                LogUtil.d(TAG, "onComplete: dfu " + deviceAddress);
//            }
//
//            @Override
//            public void onProgressChanged(String deviceAddress, int percent) {
//                LogUtil.d(TAG, "onProgressChanged: dfu " + percent + "%");
//            }
//
//            @Override
//            public void onFail(String deviceAddress, String code, String message) {
//                LogUtil.e(TAG, "onProgressChanged: dfu " + code + " " + message);
//            }
//        });
    }

    public void stopDfu() {
        DfuUtil.shared.stopDfu();
    }

    /// 目前供测试用
    public void playSound(byte[] sound) {
        this.speaker.addTask(new Task<Long>(999999L, sound, "", (l) -> {
            return null;
        }, (l) -> {
            return null;
        }, true));
    }

    public void testPlaySound(byte[] sound, String exceptKey, Boolean needWait) {
        EventBus.getDefault().post(new HashMap<String, Object>() {{
            put(Speaker.BLE_EVENT_NAME_KEY, Speaker.BLE_EVENT_NAME_PLAY);
            put(Speaker.BLE_EVENT_PLAY_TASKID_KEY, 999999L);
            put(Speaker.BLE_EVENT_PLAY_SOUND_KEY, sound);
            put(Speaker.BLE_EVENT_PLAY_EXCEPT_KEY, exceptKey);
            put(Speaker.BLE_EVENT_PLAY_BEGINCALLBACK_KEY, (Function<Long, Object>) aLong -> null);
            put(Speaker.BLE_EVENT_PLAY_ENDCALLBACK_KEY, (Function<Long, Object>) aLong -> null);
            put(Speaker.BLE_EVENT_TASK_NEED_WAIT, needWait);
        }});
    }

    public void executeCmd(BleCmd cmd, byte[] data) {
        byte[] param = (data == null ? cmd.getData() : cmd.getData(data));
        String uuid = UUID.randomUUID().toString();
        BleUtil.shared.dotCmdStart(id, uuid, cmd.type + "-" + cmd.id, System.currentTimeMillis());
        Request readRequest = new RequestBuilderFactory()
                .getWriteCharacteristicBuilder(
                        UUIDHelper.uuidFromString(CmdServiceUUID),
                        UUIDHelper.uuidFromString(CmdWriteCharacteristicUUID),
                        param
                ).setWriteOptions(
                        new WriteOptions.Builder()
                                .setWaitWriteResult(true)
                                .setFailRetry(true)
                                .setMaxRetryTimes(3)
                                .setWriteType(BluetoothGattCharacteristic.WRITE_TYPE_DEFAULT)
                                .build()
                ).setCallback(new WriteCharacteristicCallback() {
                    @Override
                    public void onCharacteristicWrite(@NonNull Request request, @NonNull byte[] value) {
                        BleUtil.shared.dotCmdSuccess(id, uuid, System.currentTimeMillis());
                    }

                    @Override
                    public void onRequestFailed(@NonNull Request request, int failType, int gattStatus, @Nullable Object value) {
                        // 主动清空队列导致的失败，忽略
                        if (failType == Connection.REQUEST_FAIL_TYPE_CONNECTION_CLEAR_REQUEST_QUEUE) {
                            BleUtil.shared.dotCmdEnd(id, uuid, System.currentTimeMillis());
                            return;
                        }
                        BleUtil.shared.dotCmdFail(id, uuid, "code = " + failType, System.currentTimeMillis());
                    }
                }).build();
        ThreadPoolManager.INSTANCE.execute(() -> readRequest.execute(EasyBLE.getInstance().getConnection(device)));
    }


    /**
     * @param receiveData 从Device收到的原始数据
     */
    @Override
    public void writeAppAuth(@NonNull byte[] receiveData) {
        byte[] param = Arrays.copyOfRange(receiveData, 5, receiveData.length);
        byte[] result = arrayAdd(param, macBytes);
        BleCmd cmd = this.getWSeriesCmd(BleCmdContant.AppCmdId.Auth);
        String uuid = UUID.randomUUID().toString();
        BleUtil.shared.dotCmdStart(id, uuid, cmd.type + "-" + cmd.id, System.currentTimeMillis());
        Request readRequest = new RequestBuilderFactory()
                .getWriteCharacteristicBuilder(
                        UUIDHelper.uuidFromString(CmdServiceUUID),
                        UUIDHelper.uuidFromString(CmdWriteCharacteristicUUID),
                        cmd.getData(result)
                ).setCallback(new WriteCharacteristicCallback() {
                    @Override
                    public void onCharacteristicWrite(@NonNull Request request, @NonNull byte[] value) {
                        BleUtil.shared.dotCmdSuccess(id, uuid, System.currentTimeMillis());
                    }

                    @Override
                    public void onRequestFailed(@NonNull Request request, int failType, int gattStatus, @Nullable Object value) {
                        // 主动清空队列导致的失败，忽略
                        if (failType == Connection.REQUEST_FAIL_TYPE_CONNECTION_CLEAR_REQUEST_QUEUE) {
                            BleUtil.shared.dotCmdEnd(id, uuid, System.currentTimeMillis());
                            return;
                        }
                        BleUtil.shared.dotCmdFail(id, uuid, "code = " + failType, System.currentTimeMillis());
                    }
                }).build();
        ThreadPoolManager.INSTANCE.execute(() -> readRequest.execute(EasyBLE.getInstance().getConnection(device)));
    }


    private BleCmd getWSeriesCmd(String id) {
        for (BleCmd cmd : WSeriesCmds) {
            if (cmd.id.equalsIgnoreCase(id)) {
                return cmd;
            }
        }
        return null;
    }

    private byte[] arrayAdd(byte[] array1, byte[] array2) {
        int len = Math.min(array1.length, array2.length);
        byte[] out = Arrays.copyOf(array1.length > array2.length ? array1 : array2, len);
        for (int i = 0; i < len; i++) {
            out[i] = (byte) ((array1[i] & 0xff) + (array2[i] & 0xff));
        }
        return out;
    }


    @Override
    public void writeButtonEnabled(boolean enabled) {
        BleCmd cmd;
        if (enabled)
            cmd = this.getWSeriesCmd(BleCmdContant.AppCmdId.EnableButton);
        else
            cmd = this.getWSeriesCmd(BleCmdContant.AppCmdId.DisableButton);
        executeCmd(cmd, null);
    }

    @Override
    public void writeDepluxOpen(boolean isOpen) {
        BleCmd cmd;
        if (isOpen)
            cmd = this.getWSeriesCmd(BleCmdContant.AppCmdId.DepluxOpen);
        else
            cmd = this.getWSeriesCmd(BleCmdContant.AppCmdId.DepluxClose);
        executeCmd(cmd, null);
    }

    @Override
    public void writeRecordEnabled(boolean enabled) {
        BleCmd cmd;
        if (enabled)
            cmd = this.getWSeriesCmd(BleCmdContant.AppCmdId.RecordStart);
        else
            cmd = this.getWSeriesCmd(BleCmdContant.AppCmdId.RecordStop);
        executeCmd(cmd, null);
    }


    public void writeFactoryMode() {
        BleCmd cmd = this.getWSeriesCmd(BleCmdContant.AppCmdId.ModeFactory);
        executeCmd(cmd, null);
    }

    public void writeModeFactory() {
        BleCmd cmd;
        if (productType == BleCmdContant.ProductType.W3_Pro) {
            cmd = this.getWSeriesCmd(BleCmdContant.AppCmdId.ModeFactoryW3Pro);
        } else {
            cmd = this.getWSeriesCmd(BleCmdContant.AppCmdId.ModeFactory);
        }
        executeCmd(cmd, null);
    }


    public void writeModeUser() {
        BleCmd cmd;
        if (productType == BleCmdContant.ProductType.W3_Pro) {
            cmd = this.getWSeriesCmd(BleCmdContant.AppCmdId.ModeUserW3Pro);
        } else {
            cmd = this.getWSeriesCmd(BleCmdContant.AppCmdId.ModeUser);
        }
        executeCmd(cmd, null);
    }



    @Override
    public void writeVolume(float value) {
        // X1耳机的音量，只能设置 5-32，其它值都没有用（From佳俊）
        BleCmd cmd = this.getWSeriesCmd(BleCmdContant.AppCmdId.SpeakerVolumeW);
        int intValue = (int) (value * 33);
        executeCmd(cmd, new byte[]{(byte) (intValue & 0xff), 0x17, 0x00});
    }

    @Override
    public void writeNRW(@NonNull byte[] params) {
        BleCmd cmd = this.getWSeriesCmd(BleCmdContant.AppCmdId.TestSettingsNRW);
        executeCmd(cmd, params);
    }

    /**
     *  设置Opus压缩比
     * @param level 1, 2, 3, 4, 默认是 2(压缩比1:16), 每次收到 44 字节录音数据;  1(1:32), 2(1:16), 3(1:8), 4(1:4)
     */
    @Override
    public void writeOpusCompressionRatio(int level) {
        if (!(level == 1 || level == 2 || level == 3 || level == 4)) {
            LogUtil.e(TAG, "设置OpusCompressionRatioW level 不合法");
            return;
        }
        BleCmd cmd = this.getWSeriesCmd(BleCmdContant.AppCmdId.OpusCompressionRatioW);
        executeCmd(cmd, new byte[]{(byte) level});
    }


    class Task<T> {
        public T id; // 任务 id
        public byte[] sound;
        public String extRole = null; // 不需要播放的角色

        public Function<T, Object> beginCb = null;
        public Function<T, Object> endCb = null;
        public Boolean needWait;

        public boolean isStart = false;
        public boolean isEnd = false;

        public volatile boolean invalid = false;

//        public Task(T id, byte[] sound, Object extData) {
//            this.id = id;
//            this.sound = sound;
//            this.extData = extData;
//        }

        public Task(T id, byte[] sound, String extRole, Function<T, Object> beginCb, Function<T, Object> endCb, Boolean needWait) {
            this.id = id;
            this.sound = sound;
            this.extRole = extRole;
            this.beginCb = beginCb;
            this.endCb = endCb;
            this.needWait = needWait;
        }
    }

    public class Recorder {
        String name;
        public int nPacket = 0; // 记录录音包数, 20ms/包

        Recorder(String name) {
            this.name = name;
        }
    }

    class Speaker implements Serializable {
        String name;
        LinkedBlockingQueue<Task<Long>> queue = new LinkedBlockingQueue<>();
        ConcurrentHashMap<Object, Task<Long>> workingTasks = new ConcurrentHashMap<>(); // 记录正在播放的任务
        boolean shouldCurTaskStop = false;
        boolean looper = false;

        OpusCodec encoder = null;
        OpusCodec encoder8k = null;
        ResampleProcessor resampler = null;

        private static final String BLE_EVENT_NAME_KEY = "BLE_EVENT_NAME_KEY"; // 消息名的 key, 也作为 EventBus 的 key, 消息实体为 Map
        private static final String BLE_EVENT_NAME_PLAY = "BLE_EVENT_NAME_PLAY"; // 消息名的 value, {BLE_EVENT_NAME_KEY: BLE_EVENT_NAME_PLAY}
        private static final String BLE_EVENT_NAME_PLAY_STOP = "BLE_EVENT_NAME_PLAY_STOP"; // 消息名的 value, {BLE_EVENT_NAME_KEY: BLE_EVENT_NAME_PLAY_STOP}

        private static final String BLE_EVENT_PLAY_TASKID_KEY = "BLE_EVENT_PLAY_TASKID_KEY"; // {BLE_EVENT_PLAY_TASKID_KEY: taskId}, 消息播放 taskId 的 key, 值为 taskId
        private static final String BLE_EVENT_PLAY_SOUND_KEY = "BLE_EVENT_PLAY_SOUND_KEY"; // {BLE_EVENT_PLAY_SOUND_KEY: new byte[]},
        private static final String BLE_EVENT_PLAY_DEVICES_KEY = "BLE_EVENT_PLAY_DEVICES_KEY"; // {BLE_EVENT_PLAY_DEVICES_KEY: [deviceId]]},
        private static final String BLE_EVENT_PLAY_EXCEPT_KEY = "BLE_EVENT_PLAY_EXCEPT_KEY";  // {BLE_EVENT_PLAY_EXCEPT_KEY: exceptDeviceId}, 除此设备相同角色外都需要播放

        private static final String BLE_EVENT_PLAY_BEGINCALLBACK_KEY = "BLE_EVENT_PLAY_BEGINCALLBACK_KEY";
        private static final String BLE_EVENT_PLAY_ENDCALLBACK_KEY = "BLE_EVENT_PLAY_ENDCALLBACK_KEY";
        private static final String BLE_EVENT_TASK_NEED_WAIT = "BLE_EVENT_TASK_NEED_WAIT";

        Speaker(String name) {
            this.name = name;
        }

        ResampleProcessor getResampler() {
            if (resampler == null) {
                resampler = new ResampleProcessor();
            }
            return resampler;
        }

        OpusCodec getOpusEncoder() throws OpusException {
            if (this.encoder == null) {
                this.encoder = new OpusCodec(16000, 1);
            }
            return this.encoder;
        }

        OpusCodec getOpusEncoder8k() throws OpusException {
            if (this.encoder8k == null) {
                this.encoder8k = new OpusCodec(8000, 1);
            }
            return this.encoder8k;
        }

        @Subscribe(threadMode = ThreadMode.POSTING)
        public synchronized void onHandleEvent(Map<String, Object> map) {
            if (!map.containsKey(BLE_EVENT_NAME_KEY)) return;
//            LogUtil.d(TAG, this.name + " onHandlerMsg: " + map);

            String mName = (String) map.get(BLE_EVENT_NAME_KEY);
            if (mName == null) return;
            if (mName.equals(BLE_EVENT_NAME_PLAY)) { // 播放事件

                Object taskIdObj = map.get(BLE_EVENT_PLAY_TASKID_KEY);
                if (taskIdObj != null) {
                    byte[] sound = (byte[]) map.get(BLE_EVENT_PLAY_SOUND_KEY);
                    String exceptRole = (String) map.get(BLE_EVENT_PLAY_EXCEPT_KEY);
                    Function<Long, Object> beginCb = (Function<Long, Object>) map.get(BLE_EVENT_PLAY_BEGINCALLBACK_KEY);
                    Function<Long, Object> endCb = (Function<Long, Object>) map.get(BLE_EVENT_PLAY_ENDCALLBACK_KEY);
                    Boolean needWait = (Boolean) map.getOrDefault(BLE_EVENT_TASK_NEED_WAIT, true);
                    addTask(new Task<Long>((Long) taskIdObj, sound, exceptRole, beginCb, endCb, needWait));
//                    if (except.equalsIgnoreCase(role)) addTask(new Task<Long>((Long) taskIdObj, sound, except, beginCb, endCb));
                }

            } else if (mName.equals(BLE_EVENT_NAME_PLAY_STOP)) { // 停止播放事件

                Object taskIdObj = map.get(BLE_EVENT_PLAY_TASKID_KEY);
                if (taskIdObj != null) {
                    stopWorker((long) taskIdObj); // 停止某一任务
                } else {
                    stop(); // 停止全部任务
                }

            }
        }

        void addTask(Task<Long> task) {
            if (!looper) {
                looper = true;
                start();
            }
            try {
                queue.put(task);
            } catch (InterruptedException e) {
                e.printStackTrace();
            }
        }

        synchronized void startStreamPlay() {
            new Thread(() -> {
                LogUtil.d(TAG, this.name + "(ble设备) 开启音频 Looper(" + this);
                while (true) {
                    try {
                        Task<Long> task = queue.take();
                        if (task.extRole != null && task.extRole.equals(WT2BlePeripheral.this.workRole)) {
//                            LogUtil.d(TAG, "start: 流式播放不播放当前此角色任务: " + WT2BlePeripheral.this.workRole);
                            continue;
                        }

                        workingTasks.put(task.id, task);
//                        LogUtil.d(TAG, name + " start: " + task.id);
                        if (shouldCurTaskStop || task.invalid) break;

                        if (task.beginCb != null) {
                            task.beginCb.apply(task.id);
                        }

                        long startTime = System.currentTimeMillis();

                        byte[] sound = task.sound;

                        int length = sound.length;
                        int offset = 0;
                        int maxByteSize = 640;
                        int index = 0;
                        int packetCount = 4; // 一次性发的包数

                        int count = sound.length / maxByteSize;
                        if (sound.length % maxByteSize > 0) {
                            count = count + 1;
                        }

                        do { // ble 设备端缓存 25 包, 500ms 数据, 16000B, 缓存 500ms 后才播放
                            if (shouldCurTaskStop || task.invalid)
                                break; /* FIXME: 中断情况补包并且包长度设置当前已发送长度 */

                            ByteArrayOutputStream sendData = new ByteArrayOutputStream();
                            int i = 0; // 实际包数
                            for (i = 0; i < packetCount && index < count; i++) {
                                int thisChunkSize = Math.min(length - offset, maxByteSize);
                                byte[] chunk = new byte[maxByteSize];
                                System.arraycopy(sound, offset, chunk, 0, thisChunkSize);

                                byte[] header = new byte[4];
                                header[0] = (byte) (((index + 1) >> 8) & 0xff);
                                header[1] = (byte) ((index + 1) & 0xff);
                                header[2] = (byte) ((count >> 8) & 0xff);
                                header[3] = (byte) (count & 0xff);

                                byte[] encodeBuffer = new byte[maxByteSize];
                                int encodedSize = 0;
                                try {
//                                    long start = System.currentTimeMillis();
                                    encodedSize = getOpusEncoder().encode(chunk, chunk.length / 2, encodeBuffer);
//                                    LogUtil.d(TAG, "package process time = " + (System.currentTimeMillis() - start) + "ms");
                                } catch (OpusException e) {
                                    e.printStackTrace();
                                }

                                byte[] packet = new byte[encodedSize + header.length];
                                System.arraycopy(header, 0, packet, 0, header.length);
                                System.arraycopy(encodeBuffer, 0, packet, header.length, encodedSize);

                                try {
                                    sendData.write(packet);
                                } catch (IOException e) {
                                    e.printStackTrace();
                                }

                                offset += thisChunkSize;
                                index++;
                            }
                            // 向 ble 设备发送数据
                            if (shouldCurTaskStop || task.invalid)
                                break; /* FIXME: 中断情况补包并且包长度设置当前已发送长度 */
//                            peripheral.writeWhitNoResponse(sendData.toByteArray());
//                            LogUtil.d(TAG, role + " sendSound in " + System.currentTimeMillis() + " ms");
                            BleUtil.shared.sendSound(WT2BlePeripheral.this, sendData.toByteArray(), sendData.toByteArray().length, 0);
                        } while (index < count);

                        if (task.endCb != null) {
                            task.endCb.apply(task.id);
                        }
                        workingTasks.remove(task.id); // 移除任务

                    } catch (InterruptedException e) {
                        e.printStackTrace();
                    }
                }
            }).start();
        }


        synchronized void start() {
            new Thread(() -> {
                LogUtil.d(TAG, this.name + "(ble设备) 开启音频 Looper(" + this);
                while (true) {
                    try {
                        Task<Long> task = queue.take();
                        if (task.extRole != null && task.extRole.equals(WT2BlePeripheral.this.workRole)) {
//                            LogUtil.d(TAG, "start: 不播放当前此角色任务: " + WT2BlePeripheral.this.workRole);
                            continue;
                        }

                        workingTasks.put(task.id, task);
//                        LogUtil.d(TAG, name + " start: " + task.id);
                        if (shouldCurTaskStop || task.invalid) break;

                        if (task.beginCb != null) {
                            task.beginCb.apply(task.id);
                        }

                        long startTime = System.currentTimeMillis();

                        byte[] sound = task.sound;

                        int length = sound.length;
                        int offset = 0;
                        int maxByteSize = 640;
                        int index = 0;
                        int packetCount = 4; // 一次性发的包数

                        int count = sound.length / maxByteSize;
                        if (sound.length % maxByteSize > 0) {
                            count = count + 1;
                        }

//                        final boolean use8K = length <= 16000 * 2 * 6; // 6s 内的音频使用 8k
                        final boolean use8K = false;
                        do { // ble 设备端缓存 25 包, 500ms 数据, 16000B, 缓存 500ms 后才播放
                            if (shouldCurTaskStop || task.invalid)
                                break; /* FIXME: 中断情况补包并且包长度设置当前已发送长度 */

                            ByteArrayOutputStream sendData = new ByteArrayOutputStream();
                            int i = 0; // 实际包数
                            for (i = 0; i < packetCount && index < count; i++) {
                                int thisChunkSize = Math.min(length - offset, maxByteSize);
                                byte[] chunk = new byte[maxByteSize];
                                System.arraycopy(sound, offset, chunk, 0, thisChunkSize);

                                // 重采样
                                if (use8K) {
                                    short[] inBuffer = BytesTrans.getInstance().Bytes2Shorts(chunk);
                                    short[] outBuffer = new short[maxByteSize / 2 / 2];
                                    getResampler().process(outBuffer, inBuffer, 16000, 8000, maxByteSize / 2);
                                    chunk = BytesTrans.getInstance().Shorts2Bytes(outBuffer);
                                }

                                byte[] header = new byte[4];
                                header[0] = (byte) (((index + 1) >> 8) & 0xff);
                                header[1] = (byte) ((index + 1) & 0xff);
                                header[2] = (byte) ((count >> 8) & 0xff);
                                header[3] = (byte) (count & 0xff);

                                byte[] encodeBuffer = new byte[maxByteSize];
                                int encodedSize = 0;
                                try {
//                                    long start = System.currentTimeMillis();
                                    encodedSize = (use8K ? getOpusEncoder8k() : getOpusEncoder()).encode(chunk, chunk.length / 2, encodeBuffer);
//                                    LogUtil.d(TAG, "package process time = " + (System.currentTimeMillis() - start) + "ms");
                                } catch (OpusException e) {
                                    e.printStackTrace();
                                }

                                byte[] packet = new byte[encodedSize + header.length];
                                System.arraycopy(header, 0, packet, 0, header.length);
                                System.arraycopy(encodeBuffer, 0, packet, header.length, encodedSize);

                                try {
                                    sendData.write(packet);
                                } catch (IOException e) {
                                    e.printStackTrace();
                                }

                                offset += thisChunkSize;
                                index++;
                            }

                            if (task.needWait == null || task.needWait) {
                                float costTime = System.currentTimeMillis() - startTime; // 实际花的时间
                                float makeupTime = (index * 20 - costTime); // 播放需要的时间 - 实际花的时间 => 得到需要补充等待的时间
                                if (makeupTime > 100.0) { // 大于 100ms 时, 等待 makeupTime * 0.8 的时间, 以防过快发送
//                                LogUtil.d(TAG, id + " 以防过快发送, 等待一会: " + makeupTime + " ms");
                                    Thread.sleep((long) (makeupTime * 0.8));
                                }
                            }

                            // 向 ble 设备发送数据
                            if (shouldCurTaskStop || task.invalid)
                                break; /* FIXME: 中断情况补包并且包长度设置当前已发送长度 */
//                            peripheral.writeWhitNoResponse(sendData.toByteArray());
                            LogUtil.d(TAG, role + " sendSound in " + System.currentTimeMillis() + " ms");
                            BleUtil.shared.sendSound(WT2BlePeripheral.this, sendData.toByteArray(), sendData.toByteArray().length, 0);
                            if (task.needWait == null || task.needWait) {
                                Thread.sleep((long) (i * 10.0));
                            }
                        } while (index < count);

                        if (task.needWait == null || task.needWait) {
                            if (!shouldCurTaskStop && !task.invalid) {
                                float costTime = System.currentTimeMillis() - startTime;
                                float makeupTime = (count * 20 - costTime);
//                            LogUtil.d(TAG, "补齐快发时间: " + makeupTime + " ms");
                                if (makeupTime > 0) {
                                    Thread.sleep((long) (makeupTime));
                                }
                                if (WT2BlePeripheral.this.productType == BleCmdContant.ProductType.WT2) {
                                    Thread.sleep(1500); // 弥补ble耳机延迟时间
                                } else if (WT2BlePeripheral.this.productType == BleCmdContant.ProductType.WT2_Edge || WT2BlePeripheral.this.productType == BleCmdContant.ProductType.W3_Pro) {
//                                Thread.sleep(500); // 弥补ble耳机延迟时间
                                }
                            }
                        }

                        if (task.endCb != null) {
                            task.endCb.apply(task.id);
                        }
                        workingTasks.remove(task.id); // 移除任务

                    } catch (InterruptedException e) {
                        e.printStackTrace();
                    }
                }
            }).start();
        }

        /**
         * 前 24 包快发，后续用原方案
         */
        synchronized void start2() {
            new Thread(() -> {
                LogUtil.d(TAG, this.name + "(ble设备) 开启音频 Looper(" + this);
                while (true) {
                    try {
                        Task<Long> task = queue.take();
                        if (task.extRole != null && task.extRole.equals(WT2BlePeripheral.this.workRole)) {
                            LogUtil.d(TAG, "start: 不播放当前此角色任务: " + WT2BlePeripheral.this.workRole);
                            continue;
                        }

                        workingTasks.put(task.id, task);
//                        LogUtil.d(TAG, name + " start: " + task.id);
                        if (shouldCurTaskStop || task.invalid) break;

                        if (task.beginCb != null) {
                            task.beginCb.apply(task.id);
                        }

                        long startTime = System.currentTimeMillis();

                        byte[] sound = task.sound;

                        int length = sound.length;
                        int offset = 0;
                        int maxByteSize = 640;
                        int index = 0;
                        int packetCount = 4; // 一次性发的包数

                        int count = sound.length / maxByteSize;
                        if (sound.length % maxByteSize > 0) {
                            count = count + 1;
                        }

//                        final boolean use8K = length <= 16000 * 2 * 6; // 6s 内的音频使用 8k
                        final boolean use8K = false;
                        do { // ble 设备端缓存 25 包, 500ms 数据, 16000B, 缓存 500ms 后才播放
                            if (shouldCurTaskStop || task.invalid)
                                break; /* FIXME: 中断情况补包并且包长度设置当前已发送长度 */

                            ByteArrayOutputStream sendData = new ByteArrayOutputStream();
                            int i = 0; // 实际包数
                            for (i = 0; i < packetCount && index < count; i++) {
                                int thisChunkSize = Math.min(length - offset, maxByteSize);
                                byte[] chunk = new byte[maxByteSize];
                                System.arraycopy(sound, offset, chunk, 0, thisChunkSize);

                                // 重采样
                                if (use8K) {
                                    short[] inBuffer = BytesTrans.getInstance().Bytes2Shorts(chunk);
                                    short[] outBuffer = new short[maxByteSize / 2 / 2];
                                    getResampler().process(outBuffer, inBuffer, 16000, 8000, maxByteSize / 2);
                                    chunk = BytesTrans.getInstance().Shorts2Bytes(outBuffer);
                                }

                                byte[] header = new byte[4];
                                header[0] = (byte) (((index + 1) >> 8) & 0xff);
                                header[1] = (byte) ((index + 1) & 0xff);
                                header[2] = (byte) ((count >> 8) & 0xff);
                                header[3] = (byte) (count & 0xff);

                                byte[] encodeBuffer = new byte[maxByteSize];
                                int encodedSize = 0;
                                try {
                                    encodedSize = (use8K ? getOpusEncoder8k() : getOpusEncoder()).encode(chunk, chunk.length / 2, encodeBuffer);
                                } catch (OpusException e) {
                                    e.printStackTrace();
                                }

                                byte[] packet = new byte[encodedSize + header.length];
                                System.arraycopy(header, 0, packet, 0, header.length);
                                System.arraycopy(encodeBuffer, 0, packet, header.length, encodedSize);

                                try {
                                    sendData.write(packet);
                                } catch (IOException e) {
                                    e.printStackTrace();
                                }

                                offset += thisChunkSize;
                                index++;
                            }

                            float costTime = System.currentTimeMillis() - startTime; // 实际花的时间
                            float makeupTime = (index * 20 - costTime); // 播放需要的时间 - 实际花的时间 => 得到需要补充等待的时间
                            if (index >= fastSendPacketCount && makeupTime > 100.0) { // 大于 100ms 时, 等待 makeupTime * 0.8 的时间, 以防过快发送
//                                LogUtil.d(TAG, id + " 以防过快发送, 等待一会: " + makeupTime + " ms");
                                Thread.sleep((long) (makeupTime * 0.8));
                            }

                            // 向 ble 设备发送数据
                            if (shouldCurTaskStop || task.invalid)
                                break; /* FIXME: 中断情况补包并且包长度设置当前已发送长度 */
//                            peripheral.writeWhitNoResponse(sendData.toByteArray());
                            LogUtil.d(TAG, role + " sendSound in " + System.currentTimeMillis() + " ms");
                            BleUtil.shared.sendSound(WT2BlePeripheral.this, sendData.toByteArray(), sendData.toByteArray().length, 0);
                            Thread.sleep((long) (i * 10.0));
                        } while (index < count);

                        if (!shouldCurTaskStop && !task.invalid) {
                            float costTime = System.currentTimeMillis() - startTime;
                            float makeupTime = (count * 20 - costTime);
//                            LogUtil.d(TAG, "补齐快发时间: " + makeupTime + " ms");
                            if (makeupTime > 0) {
                                Thread.sleep((long) (makeupTime));
                            }
                            if (WT2BlePeripheral.this.productType == BleCmdContant.ProductType.WT2) {
                                Thread.sleep(1500); // 弥补ble耳机延迟时间
                            } else if (WT2BlePeripheral.this.productType == BleCmdContant.ProductType.WT2_Edge || WT2BlePeripheral.this.productType == BleCmdContant.ProductType.W3_Pro) {
//                                Thread.sleep(500); // 弥补ble耳机延迟时间
                            }
                        }

                        if (task.endCb != null) {
                            task.endCb.apply(task.id);
                        }
                        workingTasks.remove(task.id); // 移除任务

                    } catch (InterruptedException e) {
                        e.printStackTrace();
                    }
                }
            }).start();
        }

        int packetCountPerSend = 4; // 一次性发的包数，一包 20ms
        final int packetCacheCount = 48; // 留给设备的缓存包数
        long startTime = 0;
        boolean firstSendPerTask = true;
        /**
         * 前 24 包沿用原有逻辑，后续包增大一次发送数据量提升抗干扰性
         */
        synchronized void start3() {
            new Thread(() -> {
                LogUtil.d(TAG, this.name + "(ble设备) 开启音频 Looper(" + this);
                while (true) {
                    try {
                        Task<Long> task = queue.take();
                        if (task.extRole != null && task.extRole.equals(WT2BlePeripheral.this.workRole)) {
                            LogUtil.d(TAG, "start: 不播放当前此角色任务: " + WT2BlePeripheral.this.workRole);
                            continue;
                        }

                        workingTasks.put(task.id, task);
//                        LogUtil.d(TAG, name + " start: " + task.id);
                        if (shouldCurTaskStop || task.invalid) break;

                        if (task.beginCb != null) {
                            task.beginCb.apply(task.id);
                        }

                        packetCountPerSend = 4;
                        firstSendPerTask = true;
                        cache.reset(); // 重置数据，防止残留数据数据在此任务播放了

                        byte[] sound = task.sound;

                        int length = sound.length;
                        int offset = 0; // 已处理的原始数据字节数
                        int index = 0; // 实际发送包数

                        int amount = sound.length / nBytePerOpusDecodePacket;
                        if (sound.length % nBytePerOpusDecodePacket > 0) {
                            amount = amount + 1;
                        }

                        while (offset < length) {
                            if (shouldCurTaskStop || task.invalid) break;

                            int thisChunkSize = Math.min(length - offset, nBytePerOpusDecodePacket);
                            // 1.取数据
                            byte[] pending = new byte[nBytePerOpusDecodePacket];
                            System.arraycopy(sound, offset, pending, 0, thisChunkSize);

                            offset += thisChunkSize;

                            // 2.打包 (header + payload)
                            byte[] packet = genPacketFormPcm(index, amount, pending);

                            // 3.发送
                            if (packet != null) {
                                index++;
                                // 前 24 包使用原有逻辑
                                if (index <= fastSendPacketCount) {
                                    sendDataToPeripheral(index, packet, index == amount);
                                } else {
                                    sendDataToPeripheral2(index, packet, index == amount);
                                }
                            }

                            if (shouldCurTaskStop || task.invalid) break;
                        }

                        // 动态计算需要等待的时间
                        long costTime = System.currentTimeMillis() - startTime; // 实际花的时间
                        long needToWaitTime = (index * 20L - costTime); // 播放需要的时间 - 实际花的时间 => 得到需要补充等待的时间
                        if (needToWaitTime > 0 && !shouldCurTaskStop && !task.invalid) {
                            Thread.sleep(needToWaitTime); // 等待播放完成
                        }

                        if (task.endCb != null) {
                            task.endCb.apply(task.id);
                        }
                        workingTasks.remove(task.id); // 移除任务

                    } catch (InterruptedException e) {
                        e.printStackTrace();
                    }
                }
            }).start();
        }

        final int packetLength = 44; // 一包数据对应的长度

        /**
         * 耳机类只管发送的方案
         */
        synchronized void start4() {
            new Thread(() -> {
                LogUtil.d(TAG, this.name + "(ble设备) 开启音频 Looper(" + this);
                while (true) {
                    try {
                        Task<Long> task = queue.take();
                        if (task.extRole != null && task.extRole.equals(WT2BlePeripheral.this.workRole)) {
                            LogUtil.d(TAG, "start: 不播放当前此角色任务: " + WT2BlePeripheral.this.workRole);
                            continue;
                        }

                        workingTasks.put(task.id, task);
//                        LogUtil.d(TAG, name + " start: " + task.id);
                        if (shouldCurTaskStop || task.invalid) break;

                        if (task.beginCb != null) {
                            task.beginCb.apply(task.id);
                        }

                        packetCountPerSend = 24;
                        firstSendPerTask = true;
                        cache.reset(); // 重置数据，防止残留数据数据在此任务播放了

                        byte[] sound = task.sound;
                        int length = sound.length;
                        int index = 0;

                        int amount = length / packetLength;

                        while (index < amount) {
                            if (shouldCurTaskStop || task.invalid) break;
                            index++;
                            byte[] packet = Arrays.copyOfRange(sound, (index - 1) * packetLength, index * packetLength);
                            sendDataToPeripheral2(index, packet, index == amount);
                            if (shouldCurTaskStop || task.invalid) break;
                        }

                        // 动态计算需要等待的时间
                        long costTime = System.currentTimeMillis() - startTime; // 实际花的时间
                        long needToWaitTime = (index * 20L - costTime); // 播放需要的时间 - 实际花的时间 => 得到需要补充等待的时间
                        if (needToWaitTime > 0 && !shouldCurTaskStop && !task.invalid) {
                            Thread.sleep(needToWaitTime); // 等待播放完成
                        }

                        if (task.endCb != null) {
                            task.endCb.apply(task.id);
                        }
                        workingTasks.remove(task.id); // 移除任务

                    } catch (InterruptedException e) {
                        e.printStackTrace();
                    }
                }
            }).start();
        }

        // 全局缓存, force: 还未达到若干包仍然强制发送
        final int packetCount = 4; // 一次性发的包数
        final int fastSendPacketCount = 24; // 前面不需要延迟发送的包数
        ByteArrayOutputStream cache = new ByteArrayOutputStream(); // 全局 cache
        void sendDataToPeripheral(int index, byte[] packet, boolean force) {
            try {
                cache.write(packet);
                if (force || cache.size() == packet.length * packetCount) {
                    LogUtil.d(TAG, role + " sendSound in " + System.currentTimeMillis() + " ms send packet count: " + packetCount + ", next at least " + (System.currentTimeMillis() + packetCount * 20L) + "ms");
                    // 初次发送才记录开始时间
                    if (firstSendPerTask) {
                        firstSendPerTask = false;
                        startTime = System.currentTimeMillis();
                    }

                    BleUtil.shared.sendSound(WT2BlePeripheral.this, cache.toByteArray(), packet.length * 4, 0);
                    cache.reset(); // 发送后重置数据

                    // 动态计算需要等待的时间
                    long costTime = System.currentTimeMillis() - startTime; // 实际花的时间
                    long needToWaitTime = (index * 20L - costTime); // 播放需要的时间 - 实际花的时间 => 得到需要补充等待的时间
                    // 等待逻辑
                    if (needToWaitTime > 100) {
                        Thread.sleep((long) (needToWaitTime * 0.8));
                    } else if (needToWaitTime < -50) {
                        Thread.sleep((long) (packetCount * 10.0) / 2);
                    } else {
                        Thread.sleep((long) (packetCount * 10.0));
                    }
                }
            } catch (IOException | InterruptedException e) {
                e.printStackTrace();
            }
        }

        void sendDataToPeripheral2(int index, byte[] packet, boolean force) {
            try {
                cache.write(packet);
                int packetToSendLength = cache.size();
                if (force || packetToSendLength == packet.length * packetCountPerSend) {
                    LogUtil.d(TAG, role + " sendSound in " + System.currentTimeMillis() + " ms send packet count: " + packetCountPerSend + ", next at least " + (System.currentTimeMillis() + packetCountPerSend * 20L) + "ms");
                    // 初次发送才记录开始时间
                    if (firstSendPerTask) {
                        firstSendPerTask = false;
                        startTime = System.currentTimeMillis();
                    }

                    BleUtil.shared.sendSound(WT2BlePeripheral.this, cache.toByteArray(), packet.length * 4, 0);
                    cache.reset(); // 发送后重置数据

                    // 指数逼近缓冲包数
                    if (packetCountPerSend < packetCacheCount) {
                        packetCountPerSend = Math.min(packetCountPerSend * 2, packetCacheCount);
                    }

                    // 动态计算需要等待的时间
                    long costTime = System.currentTimeMillis() - startTime; // 实际花的时间
                    long needToWaitTime = (index * 20L - costTime); // 播放需要的时间 - 实际花的时间 => 得到需要补充等待的时间
                    // 等待逻辑
                    if (needToWaitTime > 20 * packetCacheCount) {
                        LogUtil.d(TAG, "sleep end time = " + (System.currentTimeMillis() + needToWaitTime - (20 * packetCacheCount)) + "ms");
                        Thread.sleep(needToWaitTime - (20 * packetCacheCount));
                    }
                }
            } catch (IOException | InterruptedException e) {
                e.printStackTrace();
            }
        }

        final int nBytePerOpusDecodePacket = 640;
        // index 当前包序列, amount 是总包数
        byte[] genHeader(int index, int amount) {
            byte[] header = new byte[4];
            header[0] = (byte) (((index + 1) >> 8) & 0xff);
            header[1] = (byte) ((index + 1) & 0xff);
            header[2] = (byte) ((amount >> 8) & 0xff);
            header[3] = (byte) (amount & 0xff);
            return header;
        }

        // opus 压缩数据并拼接header数据
        byte[] genPacketByConcatOpusEncodeData(OpusCodec codec, byte[] header, byte[] chunk) throws OpusException {
            assert chunk != null : "chunk为空, 期望不为空";
            assert chunk.length == nBytePerOpusDecodePacket : "chunk长度不对, 期望: " + nBytePerOpusDecodePacket + " 实际: " + chunk.length;
            byte[] encodeBuffer = new byte[chunk.length];
            int encodedSize = codec.encode(chunk, chunk.length / 2, encodeBuffer);
            byte[] packet = new byte[encodedSize + header.length];
            System.arraycopy(header, 0, packet, 0, header.length);
            System.arraycopy(encodeBuffer, 0, packet, header.length, encodedSize);
            return packet;
        }

        // 生成 packet = header + payload
        byte[] genPacketFormPcm(int index, int amount, byte[] chunk) {
            assert chunk != null : "chunk为空, 期望不为空";
            assert chunk.length == nBytePerOpusDecodePacket : "chunk长度不对, 期望: " + nBytePerOpusDecodePacket + " 实际: " + chunk.length;
//            final boolean use8K = length <= 16000 * 2 * 6; // 6s 内的音频使用 8k
            final boolean use8K = false;
            byte[] packet = null;
            try {
                chunk = use8K ? resample16kTo8k(chunk) : chunk; // 重采样
                byte[] header = genHeader(index, amount); // 生成包头
                OpusCodec codec = use8K ? getOpusEncoder8k() : getOpusEncoder(); // 找到合适codec
                packet = genPacketByConcatOpusEncodeData(codec, header, chunk); // 生成整包数据
            } catch (OpusException e) {
                e.printStackTrace();
            }
            return packet;
        }

        byte[] resample16kTo8k(byte[] chunk) {
            assert chunk != null : "chunk为空, 期望不为空";
            short[] inBuffer = BytesTrans.getInstance().Bytes2Shorts(chunk);
            short[] outBuffer = new short[chunk.length / 2 / 2];
            getResampler().process(outBuffer, inBuffer, 16000, 8000, chunk.length / 2);
            chunk = BytesTrans.getInstance().Shorts2Bytes(outBuffer);
            return chunk;
        }

        public void stop() {
            queue.clear();
            for (Task task : workingTasks.values()) {
                task.invalid = true;
            }

            shouldCurTaskStop = true; // 设置停止标记
            // 需要等待真正结束
            long wait = 1000;
//        LogUtil.d(TAG, "stop: 等待任务结束: " + workingTasks.size() + " wait: " + wait);
            while (workingTasks.size() > 0 && wait > 0) {
                try {
                    Thread.sleep(50);
                } catch (InterruptedException e) {
                    e.printStackTrace();
                }
                wait = wait - 50;
            }
            if (wait != 1000) LogUtil.d(TAG, "stop: 等待任务已结束 wait: " + (1000 - wait));
            workingTasks.clear();
            shouldCurTaskStop = false;
        }

        public void stop2() {
            LogUtil.d(TAG, role + " stop all play");
            queue.clear();
            for (Task task : workingTasks.values()) {
                task.invalid = true;
            }

            workingTasks.clear();
        }

        public void stopWorker(long workerId) {
            LogUtil.d(TAG, "stopWorker: " + workerId);

            for (Task<Long> task : queue) {
                if (workerId == task.id) {
                    task.invalid = true;
                    boolean success = queue.remove(task);
                    LogUtil.d(TAG, "stopWorker: 停止播放任务(队列中) " + workerId + " 移除任务:" + success);
                    break;
                }
            }

            for (Task<Long> task : workingTasks.values()) {
                if (workerId == task.id) {
                    task.invalid = true;
                    workingTasks.remove(workerId);
                    LogUtil.d(TAG, "stopWorker: 停止播放任务(播放中) " + workerId);
                    break;
                }
            }
        }
    }
}
