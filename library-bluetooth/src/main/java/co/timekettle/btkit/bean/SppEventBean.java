package co.timekettle.btkit.bean;

import androidx.annotation.NonNull;

import co.timekettle.btkit.BleUtil;
import co.timekettle.btkit.bluetoothlib.bluetooth.device.NativeDevice;

/**
 * <AUTHOR>
 * @date 2022/11/7 18:40
 * @email <EMAIL>
 * @desc EventBus的使用对象，用来向外部广播事件
 */
public class SppEventBean {
    public static final String TAG = "SppEventBean";
    public SppEventName eventName;
    public String  macAddress;



    public SppEventBean(SppEventName eventName, String macAddress) {
        this.eventName = eventName;
        this.macAddress = macAddress;
    }


    @Override
    public String toString() {
        return "SppEventBean{" +
                "eventName=" + eventName +
                ", macAddress=" + macAddress +
                '}';
    }

    public enum SppEventName {
        SppDisconnect("SppDisconnect"),
        SppConnected("SppConnected"),
        SubSppConnected("SubSppConnected"),
        SubSppDisconnected("SubSppDisconnected");

        private final String mName;

        SppEventName(final String name) {
            mName = name;
        }

        @NonNull
        @Override
        public String toString() {
            return mName;
        }
    }
}
