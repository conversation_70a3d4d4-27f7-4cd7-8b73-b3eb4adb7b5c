package co.timekettle.btkit.bean;

import java.util.Arrays;

import cn.wandersnail.ble.Device;

/**
 * M系列产品的Bean类
 * @author: licoba
 * @date: 2022/7/14
 */
public class M2BlePeripheral  extends RawBlePeripheral {
    public static final String TAG = "M2BlePeripheral";

    public String[] mac; // 左右
    public byte[] macBytes; // m2 m2+ 为 2 * 6 字节
    public String[] macSuffix4;

    public String[] firmwareVersion;
    public String hardwareVersion; // 硬件版本

    public int[] electric; // 左右设备电量

    public Role host; // 主机的角色, 是左或右
    public boolean isPaired; // 是否已配对
    public boolean isLightOn; // 是否已打开灯光

    public M2BlePeripheral(Device device) {
        super(device);
        host = Role.Left;
        isPaired = false;
        isLightOn = false;
    }

    @Override
    public String toString() {
        return "M2BlePeripheral{" +
                "mac=" + Arrays.toString(mac) +
                ", macBytes=" + Arrays.toString(macBytes) +
                ", macSuffix4=" + Arrays.toString(macSuffix4) +
                ", firmwareVersion=" + Arrays.toString(firmwareVersion) +
                ", hardwareVersion='" + hardwareVersion + '\'' +
                ", electric=" + Arrays.toString(electric) +
                ", host=" + host +
                ", isPaired=" + isPaired +
                ", state=" + state +
                '}';
    }
}
