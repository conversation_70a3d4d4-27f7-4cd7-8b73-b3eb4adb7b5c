package co.timekettle.btkit.bean;

import android.bluetooth.le.ScanRecord;
import android.bluetooth.le.ScanResult;
import android.os.ParcelUuid;

import androidx.annotation.NonNull;

import co.timekettle.btkit.BleCmdContant;
import co.timekettle.btkit.BleUtil;
import co.timekettle.btkit.LogUtil;
import co.timekettle.btkit.UUIDHelper;
import cn.wandersnail.ble.Device;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

/**
 * @author: licoba
 * @date: 2022/7/14
 */
public class RawBlePeripheral implements Serializable {
    //    {
    //        advertising =     {
    //            isConnectable = 1;
    //            kCBAdvDataRxPrimaryPHY = 0;
    //            kCBAdvDataRxSecondaryPHY = 0;
    //            kCBAdvDataTimestamp = "674295120.859333";
    //            manufacturerData =         {
    //                CDVType = ArrayBuffer;
    //                bytes = (225 , );
    //                data = "///oDUk1KKMEHxA6ZAMArgBXVFgtTCAgIAAG";
    //            };
    //            serviceUUIDs =         (
    //                "592E6F28-432F-4669-84B1-DDC45CC40DD9"
    //            );
    //        };
    //        id = "E06CE4FB-6AC4-84F1-D302-6BAD3D2CCB83";
    //        name = "WTX-L ";
    //        rssi = "-27";
    //    }

    public Device device;

    // 耳机本身属性, 是左耳或右耳; W 系列是分左右耳, M 系列(tws)则是不分左右耳, 但是当前会指示主机是左耳或者右耳
    public enum Role {
        Left,
        Right;
    }

    public enum PeripheralState {
        NONE("NONE"),
        WILLCONNECT("WILLCONNECT"),
        DIDCONNECT("DIDCONNECT"),
        DIDINIT("DIDINIT"),
        WILLDISCONNECT("WILLDISCONNECT"),
        DISCONNECT("DISCONNECT");

        private final String mName;

        PeripheralState(final String name) {
            mName = name;
        }

        @NonNull
        @Override
        public String toString() {
            return mName;
        }
    }

    public String id = "";
    public String name = "";
    public String rssi = "";
    public long discoverTimestamp; // 最后一次发现的时间戳
    public boolean needUpgrade; // 是否需要升级

    public BleCmdContant.ProductType productType; // 具体的产品类别

    public PeripheralState state = PeripheralState.NONE;

    public String[] uuids;

    public String getKey() {
        return id;
    }

    public String getRssi() {
        return rssi;
    }

    public boolean isConnected() {
        if (isFacFirm()) {
            return state == PeripheralState.DIDCONNECT; // 工厂固件不需要鉴权
        }
        return state == PeripheralState.DIDINIT;
    }

    public boolean isFacFirm() {
        String uuidStr = uuids[0].toLowerCase();
        String WT2_Edge_Fac_UUID = BleCmdContant.ScanUUIDFilter.WT2_Edge_Fac.getUuidString().toLowerCase();
        String W3Pro_Fac_UUID = BleCmdContant.ScanUUIDFilter.W3Pro_Fac.getUuidString().toLowerCase();
        List<String> facUuidList = Arrays.asList(WT2_Edge_Fac_UUID, W3Pro_Fac_UUID);
        return (facUuidList.contains(uuidStr));
    }

    public boolean isConnecting() {
        return state == PeripheralState.WILLCONNECT || state == PeripheralState.DIDCONNECT;
    }

    RawBlePeripheral(Device device) {
        this.device = device;
        this.id = device.getAddress();
        this.name = device.getName();
        this.rssi = device.getRssi() + "";
        this.needUpgrade = false;
        this.productType = BleUtil.shared.getProductType(device);
        List<String> uuids = new ArrayList<>();
        ScanResult result = device.getScanResult();
        ScanRecord record = result == null ? null : result.getScanRecord();
        if (record != null) {
            for (ParcelUuid serviceUuid : record.getServiceUuids()) {
                uuids.add(UUIDHelper.uuidToString(serviceUuid.getUuid()));
            }
        }
        this.uuids = uuids.toArray(new String[0]);
    }

    @Override
    public String toString() {
        return "RawBlePeripheral{" +
                "device=" + device +
                ", id='" + id + '\'' +
                ", name='" + name + '\'' +
                ", rssi='" + rssi + '\'' +
                ", discoverTimestamp=" + discoverTimestamp +
                ", needUpgrade=" + needUpgrade +
                ", state=" + state +
                '}';
    }

    public void startDfu(String filePath) {

    }
}
