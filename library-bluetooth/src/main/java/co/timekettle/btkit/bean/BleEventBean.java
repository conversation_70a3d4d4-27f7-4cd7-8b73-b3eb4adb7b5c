package co.timekettle.btkit.bean;

import co.timekettle.btkit.BleUtil;

/**
 * <AUTHOR>
 * @date 2022/11/7 18:40
 * @email <EMAIL>
 * @desc EventBus的使用对象，用来向外部广播事件
 */
public class BleEventBean {
    public static final String TAG = "BleEventBean";
    public BleUtil.BleEventName eventName;
    public RawBlePeripheral blePeripheral;

    public BleEventBean(BleUtil.BleEventName eventName, RawBlePeripheral blePeripheral) {
        this.eventName = eventName;
        this.blePeripheral = blePeripheral;
    }

    public BleUtil.BleEventName getEventName() {
        return eventName;
    }

    public void setEventName(BleUtil.BleEventName eventName) {
        this.eventName = eventName;
    }

    public RawBlePeripheral getBlePeripheral() {
        return blePeripheral;
    }

    public void setBlePeripheral(RawBlePeripheral blePeripheral) {
        this.blePeripheral = blePeripheral;
    }

    @Override
    public String toString() {
        return "BleEventBean{" +
                "eventName=" + eventName +
                ", blePeripheral=" + blePeripheral +
                '}';
    }
}
