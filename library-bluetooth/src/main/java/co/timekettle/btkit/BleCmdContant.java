package co.timekettle.btkit;

import androidx.annotation.NonNull;


/**
 * Ble 指令集合和定义
 */
public class BleCmdContant {
    public static String UUIDWith(String channel) {
        return "592e" + channel + "-432f-4669-84b1-ddc45cc40dd9";
    }

    public static String StandardUUIDWith(String channel) {
        return "0000" + channel + "-0000-1000-8000-00805f9b34fb";
    }

    public static enum ScanUUIDFilter {
        WT2(UUIDWith("6f25")), // WT2
        WT2_BK(UUIDWith("6f26")), // TMK001-BK
        M2(UUIDWith("6f27")), // M2
        WT2_Edge(UUIDWith("6f28")), // WT2 Edge
        WT2_Edge_Fac(UUIDWith("6f20")), // 厂测 WT2 Edge
        W_UPGRADE(StandardUUIDWith("FE59")), // W系列的升级固件
        M2P(UUIDWith("6f29")), // M2P
        M3(UUIDWith("6f2a")), // M3
        W3Pro(UUIDWith("6f31")), // W3Pro用户模式
        W3Pro_Fac(UUIDWith("6f32")); // W3Pro厂测模式

        private final String uuidString;

        ScanUUIDFilter(final String name) {
            uuidString = name;
        }

        public String getUuidString() {
            return uuidString;
        }
    }

    public enum ProductType {
        ZERO("zero"),
        WT2("wt2"),
        M2("m2"),
        M2P("m2_plus"),
        M3("m3"),
        WT2_Edge("wt2_edge"), // 可能会被修改成 w3
        W3_Pro("w3_pro"),
        NONE("none");

        private final String mName;

        ProductType(final String name) {
            mName = name;
        }

        @NonNull
        @Override
        public String toString() {
            return mName;
        }
    }

    ;

    // 需要监听的服务 >>>>
    static BleServiceDesc ElectricDesc = new BleServiceDesc(BleServiceDesc.Type.ELECTRIC, StandardUUIDWith("180F"), StandardUUIDWith("2A19"));
    // wt2 指令数据更新的服务特征, 如通过向特征写入数据, 会从此特征收到更新的响应
    static BleServiceDesc Wt2CmdDesc = new BleServiceDesc(BleServiceDesc.Type.CMD, UUIDWith("0004"), UUIDWith("0006"));
    // wt2 录音数据更新的特征, 通过此响应获取录音数据
    static BleServiceDesc Wt2DataDesc = new BleServiceDesc(BleServiceDesc.Type.DATA, UUIDWith("0001"), UUIDWith("0003"));
    // wt2 播放写入语音数据的服务
    static BleServiceDesc Wt2PlayDesc = new BleServiceDesc(BleServiceDesc.Type.NONE, UUIDWith("0001"), UUIDWith("0002"));
    // m2 指令数据更新的服务特征, 如通过向特征写入数据, 会从此特征收到更新的响应
    static BleServiceDesc M2CmdDesc = new BleServiceDesc(BleServiceDesc.Type.CMD, UUIDWith("ff00"), UUIDWith("ff01"));
    // 需要监听的服务 <<<<

    public static BleCmd[] WSeriesCmds = {
            // 读指令, 发送读取指令后, 通过回调 onCharacteristicRead 会收到 下方定义 的特征更新
            BleCmd.ofRead(AppCmdId.OEM, StandardUUIDWith("180A"), StandardUUIDWith("2a29")),
            BleCmd.ofRead(AppCmdId.SerialNumber, StandardUUIDWith("180A"), StandardUUIDWith("2a25")),
            BleCmd.ofRead(AppCmdId.HardwareVersion, StandardUUIDWith("180A"), StandardUUIDWith("2a27")),
            BleCmd.ofRead(AppCmdId.FirmwareVersion, StandardUUIDWith("180A"), StandardUUIDWith("2a26")),
            BleCmd.ofRead(AppCmdId.Electric, StandardUUIDWith("180F"), StandardUUIDWith("2a19")),

            // 监听指令
            BleCmd.ofNotify(AppCmdId.TouchDownButton, new byte[]{0x25, 0x01}), // 监听指令的响应
            BleCmd.ofNotify(AppCmdId.TouchUpButton, new byte[]{0x25, 0x02}), // 监听指令的响应

            // 写指令, 发送写指令后, 通过回调 onCharacteristicWrite 会收到 UUIDWith("0005") 的特征更新
            BleCmd.ofWrite(AppCmdId.DepluxOpen, new byte[]{0x34, 0x03}),
            BleCmd.ofWrite(AppCmdId.DepluxClose, new byte[]{0x34, 0x04}),
            BleCmd.ofWrite(AppCmdId.RecordStart, new byte[]{0x11, 0x01}),
            BleCmd.ofWrite(AppCmdId.RecordStop, new byte[]{0x11, 0x02}),
            BleCmd.ofWrite(AppCmdId.Play, UUIDWith("0001"), UUIDWith("0002")),
            BleCmd.ofWrite(AppCmdId.PlayStart, new byte[]{0x11, 0x03}, 4, new byte[]{0, 0, 0, 0}),
            BleCmd.ofWrite(AppCmdId.PlayStop, new byte[]{0x11, 0x04}, 4, new byte[]{0, 0, 0, 0}),
            BleCmd.ofWrite(AppCmdId.StopPlayAudio, new byte[]{0x11, 0x05}),
            BleCmd.ofWrite(AppCmdId.EnableButton, new byte[]{0x26, 0x01}),
            BleCmd.ofWrite(AppCmdId.DisableButton, new byte[]{0x26, 0x02}),

            BleCmd.ofWrite(AppCmdId.Auth, new byte[]{0x19, 0x01}, 6, new byte[6]),
            BleCmd.ofWrite(AppCmdId.DFU, new byte[]{0x20, 0x01}),
            BleCmd.ofWrite(AppCmdId.Volume, new byte[]{0x23, 0x01}, 1, new byte[1], new byte[]{0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33}),

            BleCmd.ofWrite(AppCmdId.OpusCompressionRatioW, new byte[]{0x22, 0x03}, 1, new byte[]{}, new byte[]{0x02, 0x03, 0x4}),
            BleCmd.ofWrite(AppCmdId.OpusCompressionRatioR, new byte[]{0x22, 0x04}),

            // 0x01 开启(降噪),  0x02 关闭(降噪), 0x03 保留, 0x04 升级模式, 0x05 打开 ST LED, 0x06 关闭 ST LED
            BleCmd.ofWrite(AppCmdId.StW, new byte[]{0x41, 0x01}, 1, new byte[]{}, new byte[]{1, 2, 3, 4, 5, 6}),
            // 查询工作状态，返回3字节。第一个字节定义如下，
            // 第0位：厂测校验模式；
            // 第1位：厂测切换通道工作反馈；
            // 第2位：厂测校准成功与否；
            // 第3位：调试数据读写；
            // 第4位：st灯启用状态；
            // 第5位：查询st版本
            // 第6位：关闭算法模式
            // 第7位：打开算法模式
            // 第二个字节为st校验次数统计，第
            // 三个字节为版本号
            BleCmd.ofWrite(AppCmdId.StR, new byte[]{0x41, 0x02}),

            // 0x01    0x01    设置功率，char型，只支持：-40dBm, -20dBm, -16dBm, -12dBm, -8dBm, -4dBm, 0dBm, +2dBm, +3dBm, +4dBm, +5dBm, +6dBm, +7dBm +8dBm，其他数值无效，只能在耳机连上后
            // 0x02    0x00    读功率，char型
            BleCmd.ofWrite(AppCmdId.PowerConsumptionW, new byte[]{0x27, 0x01}, 1, new byte[]{}, new byte[]{-40, -20, -16, -12, -8, -4, 0, +2, +3, +4, +5, +6, +7, +8}),
            BleCmd.ofWrite(AppCmdId.PowerConsumptionR, new byte[]{0x27, 0x02}),

            // LED 提示 灯
            BleCmd.ofWrite(AppCmdId.LedOpen, new byte[]{0x33, 0x05}),
            BleCmd.ofWrite(AppCmdId.LedClose, new byte[]{0x33, 0x06}),


            BleCmd.ofWrite(AppCmdId.SNCodeW, new byte[]{0x37, 0x01}, 8, new byte[]{}),
            BleCmd.ofWrite(AppCmdId.SNCodeR, new byte[]{0x37, 0x02}),

            BleCmd.ofWrite(AppCmdId.SpeakerVolumeW, new byte[]{0x6c, 0x01}, 3, new byte[]{}),
            BleCmd.ofWrite(AppCmdId.SpeakerVolumeR, new byte[]{0x6c, (byte) 0x80}),
            BleCmd.ofWrite(AppCmdId.FirmwareR, new byte[]{0x73, (byte) 0x80}),


            // W系列测试指令
            BleCmd.ofWrite(AppCmdId.ModeFactory, new byte[]{0x40, 0x01}),
            BleCmd.ofWrite(AppCmdId.ModeUser, new byte[]{0x40, 0x02}),
            BleCmd.ofWrite(AppCmdId.ModeFactoryW3Pro, new byte[]{0x60, 0x01}),
            BleCmd.ofWrite(AppCmdId.ModeUserW3Pro, new byte[]{0x60, 0x02}),

            BleCmd.ofWrite(AppCmdId.TestSettingsRoleLeft, new byte[]{0x64, 0x01}),
            BleCmd.ofWrite(AppCmdId.TestSettingsRoleRight, new byte[]{0x64, 0x02}),
            BleCmd.ofWrite(AppCmdId.TestSettingsRoleR, new byte[]{0x64, (byte) 0x80}),
            BleCmd.ofWrite(AppCmdId.TestSettingsNREnable, new byte[]{0x65, 0x01}),
            BleCmd.ofWrite(AppCmdId.TestSettingsNRDisable, new byte[]{0x65, 0x02}),
            BleCmd.ofWrite(AppCmdId.TestSettingsNRStatusR, new byte[]{0x65, (byte) 0x80}),
            BleCmd.ofWrite(AppCmdId.TestSettingsNRW, new byte[]{0x66, 0x01}, 8, new byte[]{}),
            BleCmd.ofWrite(AppCmdId.TestSettingsNRR, new byte[]{0x66, (byte) 0x80}),
            BleCmd.ofWrite(AppCmdId.TestSettingsShutdown, new byte[]{0x68, 0x01}),
            BleCmd.ofWrite(AppCmdId.TestSettingsSleep, new byte[]{0x68, 0x02}),
            BleCmd.ofWrite(AppCmdId.TestSettingsSoftRest, new byte[]{0x68, 0x03}),
            BleCmd.ofWrite(AppCmdId.TestSettingsMicW, new byte[]{0x6B, 0x01}, 2, new byte[]{}),
            BleCmd.ofWrite(AppCmdId.TestSettingsMicR, new byte[]{0x6B, (byte) 0x80}),
            BleCmd.ofWrite(AppCmdId.TestSettingsMacW, new byte[]{0x6D, 0x01}, 6, new byte[]{}),
            BleCmd.ofWrite(AppCmdId.TestSettingsMacR, new byte[]{0x6D, (byte) 0x80}),
            BleCmd.ofWrite(AppCmdId.TestSettingsElectricQP, new byte[]{0x6F, (byte) 0x80}),

    };


    /**
     * M2 和 M2P ble 指令, M3 指令查看 Spp 部分(@BluetoothCmd)
     */
    public static BleCmd[] MSeriesCmds = {
            // 监听指令的响应, 左耳点击
            BleCmd.ofNotify(AppCmdId.TouchDownLeft, UUIDWith("ff00"), UUIDWith("ff01"), new byte[]{0x1}),
            // 监听指令的响应, 右耳点击
            BleCmd.ofNotify(AppCmdId.TouchDownRight, UUIDWith("ff00"), UUIDWith("ff01"), new byte[]{0x2}),

            BleCmd.ofNotify(AppCmdId.WorkInHfpMode, UUIDWith("ff00"), UUIDWith("ff01"), new byte[]{0x3}),
            BleCmd.ofNotify(AppCmdId.WorkInTwsMode, UUIDWith("ff00"), UUIDWith("ff01"), new byte[]{0x4}),
            BleCmd.ofNotify(AppCmdId.WorkInNoneMode, UUIDWith("ff00"), UUIDWith("ff01"), new byte[]{0x5}),

            BleCmd.ofNotify(AppCmdId.SlaveDisconnect, UUIDWith("ff00"), UUIDWith("ff01"), new byte[]{0x9}),
            BleCmd.ofNotify(AppCmdId.SlaveReconnect, UUIDWith("ff00"), UUIDWith("ff01"), new byte[]{0xA}),
            BleCmd.ofNotify(AppCmdId.M3SlaveReconnect, UUIDWith("ff00"), UUIDWith("ff01"), new byte[]{0x10}),

            BleCmd.ofWrite(AppCmdId.ChangeRecordToLeft, UUIDWith("ff00"), UUIDWith("ff02"), new byte[]{0x01}),
            BleCmd.ofWrite(AppCmdId.ChangeRecordToRight, UUIDWith("ff00"), UUIDWith("ff02"), new byte[]{0x02}),

            // M2P 切换模式
//            BleCmd.ofWrite(AppCmdId.WorkInTransMode,   UUIDWith("ff00"), UUIDWith("ff04"), new byte[]{0x80}),
//            BleCmd.ofWrite(AppCmdId.WorkInTWSMode,     UUIDWith("ff00"), UUIDWith("ff04"), new byte[]{0x00}),

            // M3 模式指令(2个字节): 0x14 [0x00]
            // 0x00代表非翻译模式（tws状态），其他数值均代表翻译模式，1代表触控同传，2代表听译，3外放，4chat，5随身练，06 AI外教，即数字对应功能列表
            BleCmd.ofWrite(AppCmdId.TouchSimulMode, UUIDWith("ff00"), UUIDWith("ff02"), new byte[]{0x14, 0x02}),

            BleCmd.ofWrite(AppCmdId.LedOpen, UUIDWith("ff00"), UUIDWith("ff02"), new byte[]{0x11, 0x00}),
            BleCmd.ofWrite(AppCmdId.LedClose, UUIDWith("ff00"), UUIDWith("ff02"), new byte[]{0x11, 0x01}),
            BleCmd.ofWrite(AppCmdId.LedR, UUIDWith("ff00"), UUIDWith("ff02"), new byte[]{0x11, 0x02}),

    };

    public static class AppCmdId {
        public static final String OEM = "OEM"; // 读取OEM信息
        public static final String SerialNumber = "SerialNumber"; // 读取Serial number
        public static final String HardwareVersion = "HardwareVersion"; // 读取硬件版本号
        public static final String FirmwareVersion = "FirmwareVersion"; // 读取固件版本号
        public static final String Electric = "Electric"; // 读取电量值

        public static final String DepluxOpen = "DepluxOpen"; //  双工/单工
        public static final String DepluxClose = "DepluxClose"; //  双工/单工
        public static final String RecordStart = "RecordStart"; // 开始录音
        public static final String RecordStop = "RecordStop"; // 结束录音
        public static final String StopPlayAudio = "StopPlayAudio"; // 耳机停止播放
        public static final String Play = "Play"; // 播放
        public static final String PlayStart = "PlayStart"; // 开始播放
        public static final String PlayStop = "PlayStop"; // 开始结束
        public static final String EnableButton = "EnableButton"; // 设置按键有效
        public static final String DisableButton = "DisableButton"; // 设置按键无效
        public static final String TouchDownButton = "TouchDownButton"; // 按键按下
        public static final String TouchUpButton = "TouchUpButton"; // 按键抬起
        public static final String PowerConsumptionW = "PowerConsumptionW";
        public static final String PowerConsumptionR = "PowerConsumptionR";
        public static final String CacheSizeW = "CacheSizeW";
        public static final String CachePolicyW = "CachePolicyW";
        public static final String CacheSizeR = "CacheSizeR";
        public static final String CachePolicyR = "CachePolicyR";
        public static final String VoltageWarning = "VoltageWarning"; // 低电警报电压值
        public static final String VoltagePowerOff = "VoltagePowerOff"; // 低电关机电压值
        public static final String LedFrequency = "LedFrequency "; // LED闪烁频率
        public static final String ShutdownTime = "ShutdownTime"; // 无连接自动关机时间
        public static final String TouchSensitivity = "TouchSensitivity"; // 触摸灵敏度
        public static final String Auth = "Auth"; // app认证
        public static final String DFU = "DFU"; // 进入DFU模式
        public static final String Volume = "Volume"; // 调整音量
        public static final String Tone = "Tone"; // 播放提示音
        public static final String OpusCompressionRatio = "OpusCompressionRatio"; // opus 压缩比
        public static final String OpusCompressionRatioR = "OpusCompressionRatioR"; // opus 压缩比
        public static final String OpusCompressionRatioW = "OpusCompressionRatioW";
        public static final String StW = "StW"; // st芯片信息设置
        public static final String StR = "StR"; // st芯片信息读取
        public static final String STExtra = "StExtra"; // st芯片额外设置

        public static final String LedOpen = "LedOpen"; // 开启 led 显示灯
        public static final String LedClose = "LedClose"; // 关闭 led 显示灯

        public static final String SNCodeW = "SNCodeW"; // SNCode 写入, 预留给手机向耳机设备写入序列号
        public static final String SNCodeR = "SNCodeR"; // SNCode 读取, 预留给手机向耳机设备写入序列号

        public static final String SpeakerVolumeW = "SpeakerVolumeW"; // 设置SPEAK参数，3字节，第一字节为常规音量（5-50，默认27），第二字节为提示音音量（默认23，不调），第三字节通话音量（预留，不调）
        public static final String SpeakerVolumeR = "SpeakerVolumeR"; // 读取SPEAK参数，返回3字节
        public static final String FirmwareR = "FirmwareR"; // 读取耳机固件版本，返回10字节


        // W系列测试指令
        public static final String ModeFactory = "ModeFactory"; // 工厂模式
        public static final String ModeUser = "ModeUser"; // 用户模式
        public static final String ModeUserW3Pro = "ModeUserW3Pro";
        public static final String ModeFactoryW3Pro = "ModeFactoryW3Pro";
        public static final String TestSettingsRoleLeft = "TestSettingsRoleLeft"; // 设置耳机为左耳
        public static final String TestSettingsRoleRight = "TestSettingsRoleRight"; // 设置耳机为右耳
        public static final String TestSettingsRoleR = "TestSettingsRoleR"; // 读取耳机左右耳角色，返回1字节
        public static final String TestSettingsNREnable = "TestSettingsNREnable"; // 启用降噪算法
        public static final String TestSettingsNRDisable = "TestSettingsNRDisable"; // 关闭降噪算法（原始声音）
        public static final String TestSettingsNRStatusR = "TestSettingsNRStatusR"; // 读取算法使用状态，返回1字节
        public static final String TestSettingsNRW = "TestSettingsNRW"; // 设置算法参数（预留）
        public static final String TestSettingsNRR = "TestSettingsNRR"; // 读取算法参数，8字节
        public static final String TestSettingsShutdown = "TestSettingsShutdown"; // 通知关机，任何时刻可用
        public static final String TestSettingsSleep = "TestSettingsSleep"; // 通知休眠，只能在BT关闭时使用
        public static final String TestSettingsSoftRest = "TestSettingsSoftRest"; // 软复位系统
        public static final String TestSettingsMicW = "TestSettingsMicW"; // 设置MIC参数，2字节，第一字节为模拟增益0-13，默认6，第二字节为数字增益；0-63，默认0
        public static final String TestSettingsMicR = "TestSettingsMicR"; // 读取MIC参数，返回2字节
        public static final String TestSettingsMacW = "TestSettingsMacW"; // 设置 mac 地址
        public static final String TestSettingsMacR = "TestSettingsMacR"; // 查询 mac 地址
        public static final String TestSettingsElectricQP = "TestSettingsElectricQP"; // 读取耳机电量电压，返回6字节


        // M 系列
        public static final String ChangeRecordToLeft = "ChangeRecordToLeft"; // 开启左通道录音
        public static final String ChangeRecordToRight = "ChangeRecordToRight"; // 开启右通道录音

        public static final String TouchDownLeft = "TouchDownLeft"; // 点击了左耳
        public static final String TouchDownRight = "TouchDownRight"; // 点击了右耳

        public static final String SlaveDisconnect = "SlaveDisconnect"; // 从机断开
        public static final String SlaveReconnect = "SlaveReconnect"; // 从机回连
        public static final String M3SlaveReconnect = "M3SlaveReconnect"; // 从机回连, M3 从机回连为 0x10, M2 从机回连为 0xA

        // 0x03 耳机告知App成功开启HFP、A2DP关闭（通话）
        // 0x04 耳机告知App成功A2DP开启、HFP关闭（听歌）
        // 0x05 耳机告知App成功A2DP关闭、HFP关闭（连接）
        public static final String WorkInHfpMode = "WorkInHfpMode"; // 通话(hfp)模式
        public static final String WorkInTwsMode = "WorkInTwsMode"; // 听歌(Tws)模式
        public static final String WorkInNoneMode = "WorkInNoneMode"; // hfp tws 均关闭, 连接

        // M2P 的写入指令


        // M3 模式指令
        public static final String TouchSimulMode = "EnterTouchSimulMode"; // 进入触控同传模式
        public static final String ListenMode = "EnterListenMode"; // 进入听译模式
        public static final String SpeakerMode = "EnterSpeakerMode"; // 进入外放模式
        public static final String ChatMode = "EnterChatMode"; // 进入聊天模式
        public static final String PracticeMode = "EnterPracticeMode"; //
        public static final String AIPracticeMode = "EnterAIPracticeMode"; //
        public static final String TwsMode = "EnterTwsMode"; // 用于听歌等, tws 模式

        public static final String LedR = "LedR"; // 读取灯光值
        public static final String MacAddress = "MacAddress"; // 读取 mac 地址
        public static final String SubSppDisconnect = "SubSppDisconnect"; // 从机断开
        public static final String SubSppConnected = "SubSppConnected"; // 从机连上

    }

    ;

}
