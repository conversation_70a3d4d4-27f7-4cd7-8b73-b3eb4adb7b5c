package co.timekettle.btkit;

import androidx.annotation.NonNull;

/**
 * Tmk Ble 服务描述, 记录服务特征及读写类型
 */
public class BleServiceDesc {
   enum Type {
      CMD("cmd"),
      ELECTRIC("electric"),
      DATA("data"),
      NONE("none");

      private final String mName;
      Type(final String name) {
         mName = name;
      }

      @NonNull
      @Override
      public String toString() {
         return mName;
      }
   }
   Type type; // 读写类型
   String service; // 服务 uuid
   String characteristic; // 特征 uuid

   BleServiceDesc(Type type, String service, String characteristic) {
      this.type = type;
      this.service = service;
      this.characteristic = characteristic;
   }
}
