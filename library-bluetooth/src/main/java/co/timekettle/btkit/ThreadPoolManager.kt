package co.timekettle.btkit

import java.util.concurrent.Executors
import java.util.concurrent.Future
import java.util.concurrent.LinkedBlockingQueue
import java.util.concurrent.ThreadPoolExecutor
import java.util.concurrent.TimeUnit

/**
 * 线程池工具类
 *
 *
 * 根据CPU数量
 */
object ThreadPoolManager {
    private val executor = ThreadPoolExecutor(
        2,
        8,
        60,
        TimeUnit.SECONDS,
        LinkedBlockingQueue(),
        Executors.defaultThreadFactory(),
        ThreadPoolExecutor.AbortPolicy()
    )

    /**
     * 执行任务
     */
    fun execute(runnable: Runnable?) {
        if (runnable == null) return

        executor.execute(runnable)
    }

    /**
     * 移除任务
     *
     *
     * 注意：此方法起作用有一个必要的前提，就是这个任务还没有开始执行，
     * 如果已经开始执行了，就停止不了该任务了，这个方法就不会起作用
     */
    fun remove(runnable: Runnable?) {
        if (runnable == null) return

        executor.remove(runnable)
    }

    /**
     * 提交任务
     */
    fun submit(runnable: Runnable?): Future<*>? {
        if (runnable == null) return null

        return executor.submit(runnable)
    }
}