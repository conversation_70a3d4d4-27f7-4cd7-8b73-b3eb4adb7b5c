package co.timekettle.btkit;

import java.util.Arrays;

/**
 * Bt 指令集合和定义, 目前是 M3 产品的指令数据定义
 * M3 指令内容 协议结构：Head+Length+Cmd+Type+Data+Checksum（大端模式：先发高字节，后发低字节）
 *
 * 格式: Head + Length + Cmd + Type + Data + Checksum
 * 长度:   2  +    2   +  1  +  1   + Length +  2
 * Type 类型如下:
 * 0x01 耳机告知APP单击了左耳(仅翻译模式有效)
 *      注：sco通道开启后，耳机还应完成切换到左mic拾音的功能
 * 0x02 耳机告知APP单击了右耳(仅翻译模式有效)
 * 0x03 耳机告知App成功A2DP关闭、HFP开启（通话）
 * 0x04 耳机告知App成功A2DP开启、HFP关闭（听歌）
 * 0x05 耳机告知App成功A2DP关闭、HFP关闭（连接）
 * 0x06 耳机告知App剩余电量
 *      数据格式： 指令0x06 + 左耳电量(1字节) + 右耳电量(1字节)
 * 0x07 耳机告知App蓝牙Mac地址
 *      数据格式：指令0x07 + 左耳mac地址(6字节) + 右耳mac地址(6字节)
 *      注：耳机断联mac部分全为0
 * 0x08 耳机告知App当前固件版本号
 *      数据格式：指令0x08 + 版本高位(1字节) + 版本低位(1字节)
 * 0x09 耳机告知App：从机断开了与主机的连接
 * 0x10 耳机告知App：从机回连上了主机
 * 0x11 耳机告知App：当前LED灯的开关情况, Data 为 0x01表示关闭, 0x00表示开启
 * 0x12 耳机读手机系统的通话音量
 * 0x13 耳机读手机系统的多媒体音量；
 * 0x14：0代表非翻译模式（tws状态），其他数值均代表翻译模式，1代表触控同传，2代表听译，3外放，4chat，5随身练，6 AI外教，即数字对应功能列表
 */
public class BluetoothCmd {
    public static byte[] WriteTemplate = new byte[]{(byte)0xaa, (byte)0xbb, 0x00, 0x01, 0x02, 0x00, 0x00, 0x00, 0x00};
    public static byte[] NotifyTemplate = new byte[]{(byte)0xaa, (byte)0xcc, 0x00, 0x01, 0x01, 0x00, 0x00, 0x00, 0x00};
    public static class Write {
        public static final byte[] ChangeRecordToLeft      = BuildWriteCmd(new byte[]{0x01, 0x01, 0x00, 0x04}); // 开启左通道录音
        public static final byte[] ChangeRecordToRight     = BuildWriteCmd(new byte[]{0x02, 0x02, 0x00, 0x05}); // 开启右通道录音
        public static final byte[] MSeriesElectric         = BuildWriteCmd(new byte[]{0x06, 0x06, 0x00, 0x09}); // 查询电量
        public static final byte[] MacAddress              = BuildWriteCmd(new byte[]{0x07, 0x07, 0x00, 0x0a}); // 查询 mac 地址
        public static final byte[] FirmwareVersion         = BuildWriteCmd(new byte[]{0x08, 0x08, 0x00, 0x0b}); // 查询固件版本

        public static final byte[] EnterTwsMode            = BuildWriteCmd(new byte[]{0x14, 0x00, 0x00, 0x03}); // 用于听歌等, tws 模式
        public static final byte[] EnterTouchSimulMode     = BuildWriteCmd(new byte[]{0x14, 0x01, 0x00, 0x04}); // 进入触控同传模式
        public static final byte[] EnterListenMode         = BuildWriteCmd(new byte[]{0x14, 0x02, 0x00, 0x05}); // 进入听译模式
        public static final byte[] EnterSpeakerMode        = BuildWriteCmd(new byte[]{0x14, 0x03, 0x00, 0x06}); // 进入外放模式
        public static final byte[] EnterChatMode           = BuildWriteCmd(new byte[]{0x14, 0x04, 0x00, 0x07}); // 进入聊天模式
        public static final byte[] EnterPracticeMode       = BuildWriteCmd(new byte[]{0x14, 0x05, 0x00, 0x08}); // 进入随身练模式
        public static final byte[] EnterAIPracticeMode     = BuildWriteCmd(new byte[]{0x14, 0x06, 0x00, 0x09}); // 进入 Ai 外教模式

        public static final byte[] LedOpen                 = BuildWriteCmd(new byte[]{0x11, 0x00, 0x00, 0x03}); // 设置灯光开
        public static final byte[] LedClose                = BuildWriteCmd(new byte[]{0x11, 0x01, 0x00, 0x04}); // 设置灯光关
        public static final byte[] LedR                    = BuildWriteCmd(new byte[]{0x11, 0x02, 0x00, 0x05}); // 读取灯光值
    }

    public static class Notify {
        public static final byte[] ChangeRecordToLeft   =   BuildNotifyCmd(new byte[]{0x01, 0x01, 0x00, 0x03}); // 变更为左通道录音
        public static final byte[] ChangeRecordToRight  =   BuildNotifyCmd(new byte[]{0x02, 0x02, 0x00, 0x04}); // 变更为右通道录音
        public static final byte[] SubSppDisconnect     =   BuildNotifyCmd(new byte[]{0x09, 0x09, 0x00, 0x0b}); // 从机断开
        public static final byte[] SubSppConnected      =   BuildNotifyCmd(new byte[]{0x10, 0x10, 0x00, 0x12}); // 从机连接上
        public static final byte[] LedOn                =   BuildNotifyCmd(new byte[]{0x11, 0x00, 0x00, 0x02}); // 灯光开启
        public static final byte[] LedOff               =   BuildNotifyCmd(new byte[]{0x11, 0x01, 0x00, 0x03}); // 灯光关闭
    }

    static byte[] BuildWriteCmd(byte[] tail) {
        byte[] cData = Arrays.copyOf(WriteTemplate, WriteTemplate.length);
        System.arraycopy(tail, 0, cData, cData.length - tail.length, tail.length);
        return cData;
    }

    static byte[] BuildNotifyCmd(byte[] tail) {
        byte[] cData = Arrays.copyOf(WriteTemplate, WriteTemplate.length);
        System.arraycopy(tail, 0, cData, cData.length - tail.length, tail.length);
        return cData;
    }

    public static String getNotifyCmdName(byte[] data) {
        assert data != null && data.length > 5 : "BluetoothCmd 数据异常";
        byte ctype = data[5];
        switch (ctype) {
            case 0x01: return BleCmdContant.AppCmdId.ChangeRecordToLeft;
            case 0x02: return BleCmdContant.AppCmdId.ChangeRecordToRight;
            case 0x03: return BleCmdContant.AppCmdId.WorkInHfpMode;
            case 0x04: return BleCmdContant.AppCmdId.WorkInTwsMode;
            case 0x05: return BleCmdContant.AppCmdId.WorkInNoneMode;
            case 0x06: return BleCmdContant.AppCmdId.Electric;
            case 0x07: return BleCmdContant.AppCmdId.MacAddress;
            case 0x08: return BleCmdContant.AppCmdId.FirmwareVersion;
            case 0x09: return BleCmdContant.AppCmdId.SubSppDisconnect;
            case 0x10: return BleCmdContant.AppCmdId.SubSppConnected;
            case 0x11: return BleCmdContant.AppCmdId.LedR;
            default: return null;
        }
    }

    public static byte[] getNotifyCmdData(byte[] data) {
        assert data != null && data.length > 5 : "BluetoothCmd 数据异常";
        int len = (data[2] << 8) + data[3];
        int valueOffset = 6;
        return Arrays.copyOfRange(data, valueOffset, valueOffset + len);
    }
}