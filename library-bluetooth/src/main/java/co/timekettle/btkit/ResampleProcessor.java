package co.timekettle.btkit;

public class ResampleProcessor {
    private final static int BL = 64;
    private final double[] filterB = {
        -0.003153335084774,-0.003590152237582, 0.005704447325486,  0.01723833354592,
                0.01363995299636,-0.001609694569001,-0.004806437223483, 0.006115905028161,
                0.00635826431921, -0.00651006356064,-0.005637588362203, 0.008721218472434,
                0.005552611229619, -0.01103503425125,-0.005017464671399,  0.01394594834814,
                0.004063166812484, -0.01744868313164,-0.002432006634085,  0.02171642772436,
                -0.0001826939763624, -0.02705475902571,  0.00428516836776,  0.03407289039768,
                -0.010885227617, -0.04420867492187,  0.02238728264573,  0.06153798771986,
                -0.04666820676854,  -0.1033830397675,   0.1335943615354,   0.4651678008514,
                0.4651678008514,   0.1335943615354,  -0.1033830397675, -0.04666820676854,
                0.06153798771986,  0.02238728264573, -0.04420867492187,   -0.010885227617,
                0.03407289039768,  0.00428516836776, -0.02705475902571,-0.0001826939763624,
                0.02171642772436,-0.002432006634085, -0.01744868313164, 0.004063166812484,
                0.01394594834814,-0.005017464671399, -0.01103503425125, 0.005552611229619,
                0.008721218472434,-0.005637588362203, -0.00651006356064,  0.00635826431921,
                0.006115905028161,-0.004806437223483,-0.001609694569001,  0.01363995299636,
                0.01723833354592, 0.005704447325486,-0.003590152237582,-0.003153335084774
    };

    private double[] z = new double[BL];

    public ResampleProcessor() {
        for (int iz = 0; iz < BL; iz++)
        {
            z[iz] = 0;
        }
    }

    public void process(short []outdata, short []indata, int ifsSrc, int ifsTgt, int iframeLen)
    {
        float sum;
        float srcRate;
        float th = 0;
        int j = 0;
        srcRate = ifsTgt * 1.0f / ifsSrc;
        for (int i = 0; i < iframeLen; i++)
        {

            for (int iz = BL - 2; iz >=0; iz--)
            {
                z[iz + 1] = z[iz];
            }
            z[0] = (float)indata[i];
            th += srcRate;
            if (th >= 1.0f)
            {
                th = th - 1.0f;
                sum = 0;
                for (int iz = 0; iz < BL; iz++)
                {
                    sum += z[iz] * filterB[iz];
                }
                outdata[j] = (short)(sum * 32768.0f);
                j++;
            }
        }
    }
}
