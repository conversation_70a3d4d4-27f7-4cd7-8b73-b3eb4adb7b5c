package co.timekettle.btkit.sample.tools;

import java.util.ArrayList;
import java.util.List;

import co.timekettle.btkit.bean.RawBlePeripheral;

public class BleObserverManager implements BleObservable {

    public static BleObserverManager getInstance() {
        return ObserverManagerHolder.S_BLE_OBSERVER_MANAGER;
    }

    private static class ObserverManagerHolder {
        private static final BleObserverManager S_BLE_OBSERVER_MANAGER = new BleObserverManager();
    }

    private final List<BleObserver> bleObservers = new ArrayList<>();

    @Override
    public void addObserver(BleObserver obj) {
        bleObservers.add(obj);
    }

    @Override
    public void deleteObserver(BleObserver obj) {
        int i = bleObservers.indexOf(obj);
        if (i >= 0) {
            bleObservers.remove(obj);
        }
    }

    @Override
    public void notifyObserver(RawBlePeripheral bleDevice) {
        for (int i = 0; i < bleObservers.size(); i++) {
            BleObserver o = bleObservers.get(i);
            o.disConnected(bleDevice);
        }
    }

}
