package co.timekettle.btkit.sample

import android.content.pm.ActivityInfo
import android.os.Bundle
import android.util.Log
import android.view.MenuItem
import androidx.appcompat.app.AppCompatActivity
import co.timekettle.btkit.LogUtil
import co.timekettle.btkit.R
import co.timekettle.btkit.databinding.ActivityBleSettingBinding
import co.timekettle.btkit.sample.MyUtil.isX1


class BleSettingActivity : AppCompatActivity() {
    lateinit var mBinding: ActivityBleSettingBinding
    override fun onCreate(savedInstanceState: Bundle?) {
        if(isX1()){
            requestedOrientation =  ActivityInfo.SCREEN_ORIENTATION_PORTRAIT
        }
        super.onCreate(savedInstanceState)
        mBinding = ActivityBleSettingBinding.inflate(layoutInflater)
        setContentView(mBinding.root)
        supportActionBar?.apply {
            setDisplayHomeAsUpEnabled(true)
            title = "设置"
        }
        supportFragmentManager.beginTransaction()
            .replace(R.id.preferences_container, BleSettingFragment())
            .commit()
    }

    override fun onOptionsItemSelected(item: MenuItem): Boolean {
        when (item.itemId) {
            android.R.id.home -> {
                finish()
                return true
            }
        }
        return super.onOptionsItemSelected(item)
    }



}