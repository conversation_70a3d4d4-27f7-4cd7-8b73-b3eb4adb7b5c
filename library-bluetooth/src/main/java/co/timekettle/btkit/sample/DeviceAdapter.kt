package co.timekettle.btkit.sample

import android.content.Context
import android.view.View
import android.view.ViewGroup
import android.widget.BaseAdapter
import android.widget.Button
import android.widget.LinearLayout
import android.widget.TextView
import co.timekettle.btkit.R
import co.timekettle.btkit.bean.M2BlePeripheral
import co.timekettle.btkit.bean.RawBlePeripheral
import co.timekettle.btkit.bean.WT2BlePeripheral

class DeviceAdapter(private val context: Context) : BaseAdapter() {
    private val bleDeviceList: MutableList<RawBlePeripheral> = ArrayList()
    fun addDevice(bleDevice: RawBlePeripheral) {
        removeDevice(bleDevice)
        bleDeviceList.add(bleDevice)
    }

    fun removeDevice(bleDevice: RawBlePeripheral) {
        for (i in bleDeviceList.indices) {
            val device = bleDeviceList[i]
            if (bleDevice.id == device.id) {
                bleDeviceList.removeAt(i)
            }
        }
    }

    fun clearConnectedDevice() {
        bleDeviceList.removeIf {
            it.isConnected
        }
    }

    fun clearScanDevice() {
        bleDeviceList.removeIf {
            !it.isConnected
        }
    }

    fun clear() {
        clearConnectedDevice()
        clearScanDevice()
    }

    override fun getCount(): Int {
        return bleDeviceList.size
    }

    override fun getItem(position: Int): RawBlePeripheral {
        return bleDeviceList[position]
    }

    override fun getItemId(position: Int): Long {
        return 0
    }

    override fun getView(position: Int, convertView: View?, parent: ViewGroup): View {
        var convertView = convertView
        val holder: ViewHolder
        if (convertView != null) {
            holder = convertView.tag as ViewHolder
        } else {
            convertView = View.inflate(context, R.layout.adapter_device, null)
            holder = ViewHolder()
            convertView.tag = holder
            holder.tv_mode_tag = convertView.findViewById(R.id.img_blue)
            holder.txt_name = convertView.findViewById<View>(R.id.txt_name) as TextView
            holder.txt_mac = convertView.findViewById<View>(R.id.txt_mac) as TextView
            holder.txt_rssi = convertView.findViewById<View>(R.id.txt_rssi) as TextView
            holder.layout_idle = convertView.findViewById<View>(R.id.layout_idle) as LinearLayout
            holder.layout_connected =
                convertView.findViewById<View>(R.id.layout_connected) as LinearLayout
            holder.btn_disconnect = convertView.findViewById<View>(R.id.btn_disconnect) as Button
            holder.btn_connect = convertView.findViewById<View>(R.id.btn_connect) as Button
            holder.btn_detail = convertView.findViewById<View>(R.id.btn_detail) as Button
        }
        val bleDevice = getItem(position)
        if (bleDevice != null) {
            val isConnected = bleDevice.isConnected
            var name = ""
            var mac = ""
            if (bleDevice is WT2BlePeripheral) {
                val wt2 = bleDevice
                name = wt2.name + "(" + wt2.macSuffix4 + ")" + " " + wt2.state
                mac = wt2.mac
                if (bleDevice.isFacFirm()){
                    holder.tv_mode_tag!!.text = "厂测固件"
                }else{
                    holder.tv_mode_tag!!.text = "用户固件"
                }
            } else if (bleDevice is M2BlePeripheral) {
                val m2 = bleDevice
                name =
                    m2.name + "(" + m2.macSuffix4[0] + " " + m2.macSuffix4[1] + ")" + " " + m2.state
                mac = "(" + m2.mac[0] + " " + m2.mac[1] + ")"
            }
            val rssi = bleDevice.getRssi()
            holder.txt_name!!.text = name
            holder.txt_mac!!.text = mac
            holder.txt_rssi!!.text = rssi
            if (isConnected) {
                holder.tv_mode_tag!!.setTextColor(-0xe2164a)
                holder.txt_name!!.setTextColor(-0xe2164a)
                holder.txt_mac!!.setTextColor(-0xe2164a)
                holder.layout_idle!!.visibility = View.GONE
                holder.layout_connected!!.visibility = View.VISIBLE
            } else {
                holder.tv_mode_tag!!.setTextColor(-0x1000000)
                holder.txt_name!!.setTextColor(-0x1000000)
                holder.txt_mac!!.setTextColor(-0x1000000)
                holder.layout_idle!!.visibility = View.VISIBLE
                holder.layout_connected!!.visibility = View.GONE
            }
        }
        holder.btn_connect!!.setOnClickListener {
            mListener?.onConnect(bleDevice)
        }
        holder.btn_disconnect!!.setOnClickListener {
            mListener?.onDisConnect(bleDevice)
        }
        holder.btn_detail!!.setOnClickListener {
            mListener?.onDetail(bleDevice)
        }
        return convertView!!
    }

    internal class ViewHolder {
        var tv_mode_tag: TextView? = null
        var txt_name: TextView? = null
        var txt_mac: TextView? = null
        var txt_rssi: TextView? = null
        var layout_idle: LinearLayout? = null
        var layout_connected: LinearLayout? = null
        var btn_disconnect: Button? = null
        var btn_connect: Button? = null
        var btn_detail: Button? = null
    }

    interface OnDeviceClickListener {
        fun onConnect(bleDevice: RawBlePeripheral)
        fun onDisConnect(bleDevice: RawBlePeripheral)
        fun onDetail(bleDevice: RawBlePeripheral)
    }

    private var mListener: OnDeviceClickListener? = null
    fun setOnDeviceClickListener(listener: OnDeviceClickListener?) {
        mListener = listener
    }
}

