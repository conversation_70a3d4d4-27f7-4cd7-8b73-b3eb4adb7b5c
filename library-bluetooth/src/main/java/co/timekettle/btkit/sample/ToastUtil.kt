import android.content.Context
import android.os.Handler
import android.os.Looper
import android.view.Gravity
import android.view.View
import android.widget.Toast
import androidx.appcompat.app.AlertDialog
import com.google.android.material.snackbar.Snackbar

object ToastUtil {
    private var prevToast: Toast? = null
    private val handler = Handler(Looper.getMainLooper())

    fun showToast(context: Context, message: String) {
        doToast(context, message, Toast.LENGTH_SHORT)
    }


    private fun doToast(context: Context, message: String, duration: Int) {
        handler.post {
            prevToast?.cancel()
            val toast = Toast.makeText(context, message, duration)
            toast.setGravity(Gravity.BOTTOM or Gravity.CENTER_HORIZONTAL, 0, 0)
            prevToast = toast
            toast.show()
        }
    }

    fun showDialog(context: Context, text: String = "这是一句提示") {
        val builder = AlertDialog.Builder(context)
        builder.setTitle("提示")
        builder.setMessage(text)
        builder.setPositiveButton("知道了") { _, _ ->
        }

        builder.show()
    }
}