package co.timekettle.btkit.sample;

import android.bluetooth.BluetoothGattCharacteristic;
import android.bluetooth.BluetoothGattService;
import android.content.pm.ActivityInfo;
import android.os.Bundle;
import android.view.KeyEvent;
import android.view.MenuItem;
import android.view.View;
import android.widget.Toast;

import androidx.annotation.NonNull;
import androidx.appcompat.app.ActionBar;
import androidx.appcompat.app.AppCompatActivity;
import androidx.appcompat.widget.Toolbar;
import androidx.fragment.app.Fragment;
import androidx.fragment.app.FragmentTransaction;

import com.google.gson.Gson;

import java.util.ArrayList;
import java.util.List;

import co.timekettle.btkit.BleCmdContant;
import co.timekettle.btkit.bean.M2BlePeripheral;
import co.timekettle.btkit.bean.RawBlePeripheral;
import co.timekettle.btkit.R;
import co.timekettle.btkit.bean.WT2BlePeripheral;
import co.timekettle.btkit.sample.tools.BleObserver;
import co.timekettle.btkit.sample.tools.BleObserverManager;

public class OperationActivity extends AppCompatActivity implements BleObserver {
    public static final String KEY_DATA = "key_data";
    private RawBlePeripheral bleDevice;
    private BluetoothGattService bluetoothGattService;
    private BluetoothGattCharacteristic characteristic;
    private int charaProp;

    private List<Fragment> fragments = new ArrayList<>();
    private int currentPage = 0;
    private String[] titles = new String[3];

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);

        setContentView(R.layout.activity_operation);
        initData();
        initView();
        initPage();
    }

    @Override
    protected void onDestroy() {
        super.onDestroy();
//        BleManager.getInstance().clearCharacterCallback(bleDevice);
        BleObserverManager.getInstance().deleteObserver(this);
    }

    @Override
    public void disConnected(RawBlePeripheral device) {
        if (device != null && bleDevice != null && device.id.equals(bleDevice.id)) {
            finish();
        }
    }

    @Override
    public boolean onKeyDown(int keyCode, KeyEvent event) {
        if (keyCode == KeyEvent.KEYCODE_BACK) {
            if (currentPage != 0) {
                currentPage--;
                changePage(currentPage);
                return true;
            } else {
                finish();
                return true;
            }
        }
        return super.onKeyDown(keyCode, event);
    }

    private void initView() {
        ActionBar toolbar = getSupportActionBar();
        toolbar.setTitle(titles[0]);
        toolbar.setDisplayHomeAsUpEnabled(true);
        if (MyUtil.INSTANCE.isX1()) {
            // 设为竖屏
//            Toast.makeText(this, "设为竖屏", Toast.LENGTH_SHORT).show();
//            setRequestedOrientation(ActivityInfo.SCREEN_ORIENTATION_PORTRAIT);
            toolbar.hide();
        }
    }

    @Override
    public boolean onOptionsItemSelected(@NonNull MenuItem item) {
        if (item.getItemId() == android.R.id.home) {
            if (currentPage != 0) {
                currentPage--;
                changePage(currentPage);
            } else {
                finish();
            }
            return true;
        }
        return super.onOptionsItemSelected(item);
    }

    private void initData() {
        bleDevice = new Gson().fromJson(getIntent().getStringExtra(KEY_DATA), RawBlePeripheral.class);
        if (bleDevice.productType == BleCmdContant.ProductType.WT2 || bleDevice.productType == BleCmdContant.ProductType.WT2_Edge || bleDevice.productType == BleCmdContant.ProductType.W3_Pro) {
            bleDevice = new Gson().fromJson(getIntent().getStringExtra(KEY_DATA), WT2BlePeripheral.class);
        } else if (bleDevice.productType == BleCmdContant.ProductType.M2 || bleDevice.productType == BleCmdContant.ProductType.M2P || bleDevice.productType == BleCmdContant.ProductType.M3) {
            bleDevice = new Gson().fromJson(getIntent().getStringExtra(KEY_DATA), M2BlePeripheral.class);
        }
        if (bleDevice == null)
            finish();

        titles = new String[]{
                getString(R.string.service_list),
                getString(R.string.characteristic_list),
                getString(R.string.console)};
    }

    private void initPage() {
        prepareFragment();
        changePage(0);
    }

    public void changePage(int page) {
        currentPage = page;
        getSupportActionBar().setTitle(titles[page]);
        updateFragment(page);
        if (currentPage == 1) {
            ((CharacteristicListFragment) fragments.get(1)).showData();
        } else if (currentPage == 2) {
            ((CharacteristicOperationFragment) fragments.get(2)).showData();
        }
    }

    private void prepareFragment() {
        fragments.add(new CmdListFragment());
//        fragments.add(new ServiceListFragment());
//        fragments.add(new CharacteristicListFragment());
//        fragments.add(new CharacteristicOperationFragment());
        for (Fragment fragment : fragments) {
            getSupportFragmentManager().beginTransaction().add(R.id.fragment, fragment).hide(fragment).commit();
        }
    }

    private void updateFragment(int position) {
        if (position > fragments.size() - 1) {
            return;
        }
        for (int i = 0; i < fragments.size(); i++) {
            FragmentTransaction transaction = getSupportFragmentManager().beginTransaction();
            Fragment fragment = fragments.get(i);
            if (i == position) {
                transaction.show(fragment);
            } else {
                transaction.hide(fragment);
            }
            transaction.commit();
        }
    }

    public RawBlePeripheral getBleDevice() {
        return bleDevice;
    }

    public BluetoothGattService getBluetoothGattService() {
        return bluetoothGattService;
    }

    public void setBluetoothGattService(BluetoothGattService bluetoothGattService) {
        this.bluetoothGattService = bluetoothGattService;
    }

    public BluetoothGattCharacteristic getCharacteristic() {
        return characteristic;
    }

    public void setCharacteristic(BluetoothGattCharacteristic characteristic) {
        this.characteristic = characteristic;
    }

    public int getCharaProp() {
        return charaProp;
    }

    public void setCharaProp(int charaProp) {
        this.charaProp = charaProp;
    }


}
