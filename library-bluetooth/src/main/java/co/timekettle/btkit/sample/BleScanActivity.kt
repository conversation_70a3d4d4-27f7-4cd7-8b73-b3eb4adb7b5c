package co.timekettle.btkit.sample

import ToastUtil
import ToastUtil.showToast
import android.app.ProgressDialog
import android.content.Context
import android.content.Intent
import android.content.SharedPreferences
import android.content.pm.ActivityInfo
import android.os.Bundle
import android.text.TextUtils
import android.util.Log
import android.view.Menu
import android.view.MenuItem
import android.view.View
import android.view.animation.Animation
import android.view.animation.AnimationUtils
import android.view.animation.LinearInterpolator
import android.widget.EditText
import android.widget.ImageView
import android.widget.LinearLayout
import android.widget.ListView
import android.widget.TextView
import androidx.appcompat.app.AlertDialog
import androidx.appcompat.app.AppCompatActivity
import androidx.lifecycle.lifecycleScope
import androidx.preference.PreferenceManager
import co.timekettle.btkit.BleCmdContant
import co.timekettle.btkit.BleCmdContant.AppCmdId
import co.timekettle.btkit.BleUtil
import co.timekettle.btkit.BleUtil.BleEventName
import co.timekettle.btkit.LogUtil
import co.timekettle.btkit.R
import co.timekettle.btkit.bean.BleEventBean
import co.timekettle.btkit.bean.M2BlePeripheral
import co.timekettle.btkit.bean.RawBlePeripheral
import co.timekettle.btkit.bean.RawBlePeripheral.PeripheralState
import co.timekettle.btkit.bean.WT2BlePeripheral
import co.timekettle.btkit.bluetoothlib.BluetoothClassicUtil
import co.timekettle.btkit.bluetoothlib.bluetooth.BluetoothException
import co.timekettle.btkit.bluetoothlib.bluetooth.device.NativeDevice
import co.timekettle.btkit.databinding.ActivityBlescanBinding
import co.timekettle.btkit.sample.DeviceAdapter.OnDeviceClickListener
import co.timekettle.btkit.sample.MyUtil.isX1
import com.google.gson.Gson
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.delay
import kotlinx.coroutines.launch
import kotlinx.coroutines.withContext
import org.greenrobot.eventbus.EventBus
import org.greenrobot.eventbus.Subscribe
import org.greenrobot.eventbus.ThreadMode
import java.util.UUID


class BleScanActivity : AppCompatActivity(), View.OnClickListener {

    lateinit var mBinding: ActivityBlescanBinding
    lateinit var context: Context
    lateinit var sp: SharedPreferences
    private var layout_setting: LinearLayout? = null
    lateinit var RECORD_FILE_NAME: String // 录音文件路径
    var mMenu: Menu? = null
    var isPlaying: Boolean = false // 录音文件路径
    var isForFac = false  // 是否是给工厂用的

    // 配置相关
    private lateinit var pvProduct: String  // 搜索的产品
    private var pvAutoConnect: Boolean = false  // 是否自动连接
    private var pvMaxConnCount: Int = 6  // 最大允许连接数
    private var pvAutoSwitch: Boolean = false  // 是否自动切换
    private var pvSignalStrength: Int = -90  // 信号强度，大于这个值的才会被连接
    private var pvSwitchDirection: Int = 0  // 切换方向，取值0不切换,1工厂->用户,2用户->工厂
    private var recordingRole: HashMap<String, Boolean> =
        hashMapOf("left" to false, "right" to false)

    // end 配置
    private var txt_setting: TextView? = null
    private var et_name: EditText? = null
    private var et_mac: EditText? = null
    private var et_uuid: EditText? = null
    private var img_loading: ImageView? = null
    private var operatingAnim: Animation? = null
    lateinit var mDeviceAdapter: DeviceAdapter
    private var progressDialog: ProgressDialog? = null
    var bleListener: BleUtil.Listener = object : BleUtil.Listener {
        override fun dispatchEvent(type: BleEventName, value: RawBlePeripheral?) {
            when (type) {
                BleEventName.BleDidStatusUpdate -> //                    Log.d(TAG, "状态更新, 获取设备列表: " + BleUtil.shared.searchedPeripherals.toString());
                    runOnUiThread {
                        doAutoConnect()
                        mDeviceAdapter.clear()
                        for (peripheral in BleUtil.shared.sortedSearchedPeripherals) {
                            if (peripheral.rssi.toInt() > pvSignalStrength)
                                mDeviceAdapter.addDevice(peripheral)
                        }
                        mDeviceAdapter.notifyDataSetChanged()
                    }

                BleEventName.BleConnectStandby -> {}
                BleEventName.BleDisconnectedPeripheral -> runOnUiThread { mDeviceAdapter.notifyDataSetChanged() }
                BleEventName.BleDisconnectedSubPeripheral -> {}
                BleEventName.BleConnectedSubPeripheral -> {}
                BleEventName.BleProtocolChanged -> {}
                BleEventName.BleStError -> {}
                else -> {}
            }
        }

        override fun dispatchCmdEvent(
            perip: RawBlePeripheral,
            cmdId: String,
            values: ByteArray?
        ) {
            val desc = "$cmdId [${BleUtil.toHexStr(values, " ")}]第五个字节是长度";
            if(!isForFac) showToast(context, desc) // 工厂模式不显示这个Toast
        }

        override fun onBluetoothStateUpdate(state: Int) {
            Log.d(TAG, "系统蓝牙状态: $state")
        }
    }


    var btListener: BluetoothClassicUtil.Listener = object : BluetoothClassicUtil.Listener {
        override fun onDeviceDisconnect(device: NativeDevice, e: BluetoothException) {
            Log.d(TAG, "onDeviceDisconnect: " + device.name + (e?.toString() ?: ""))
        }

        override fun onDeviceRead(device: NativeDevice, data: String) {
            Log.d(TAG, "onDeviceRead: " + device.name + " data: " + data)
        }

        override fun onDiscovered(device: NativeDevice) {
            Log.d(TAG, "onDiscovered: " + device.name)
        }
    }


    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        if (isX1()) {
            requestedOrientation = ActivityInfo.SCREEN_ORIENTATION_PORTRAIT
        }
        context = this@BleScanActivity
        mBinding = ActivityBlescanBinding.inflate(layoutInflater)
        sp = PreferenceManager.getDefaultSharedPreferences(this)
        RECORD_FILE_NAME = context.applicationInfo.dataDir + "/record/record_fac.pcm"
        EventBus.getDefault().register(this)
        setContentView(mBinding.root)
        isForFac = intent.getBooleanExtra("isForFac", false)
        initView()
        initListener()
        BleUtil.shared.setLogLevel(2)
        BleUtil.shared.setLogCallback { level: Int, tag: String?, msg: String?, tr: Throwable? ->
            if (level == 1) {
                Log.e(tag, msg!!)
            } else if (level == 2) {
                Log.d(tag, msg!!)
            }
        }
        BleUtil.shared.init(this, false)
        //        BleUtil.shared.setListener(this.bleListener);
        BleUtil.shared.addListener(bleListener)
        BluetoothClassicUtil.shared.init(this)
        BluetoothClassicUtil.shared.setListener(btListener)
    }

    override fun onClick(v: View) {
        val id = v.id
        if (id == R.id.btn_scan) {

        } else if (id == R.id.txt_setting) {
            if (layout_setting!!.visibility == View.VISIBLE) {
                layout_setting!!.visibility = View.GONE
                txt_setting!!.text = getString(R.string.expand_search_settings)
            } else {
                layout_setting!!.visibility = View.VISIBLE
                txt_setting!!.text = getString(R.string.retrieve_search_settings)
            }
        }
    }

    private fun initView() {
        mBinding.btnScan.text = getString(R.string.start_scan)
        mBinding.btnScan.setOnClickListener(this)
        et_name = findViewById<View>(R.id.et_name) as EditText
        et_mac = findViewById<View>(R.id.et_mac) as EditText
        et_uuid = findViewById<View>(R.id.et_uuid) as EditText
        layout_setting = findViewById<View>(R.id.layout_setting) as LinearLayout
        txt_setting = findViewById<View>(R.id.txt_setting) as TextView
        txt_setting!!.setOnClickListener(this)
        layout_setting!!.visibility = View.GONE
        txt_setting!!.text = getString(R.string.expand_search_settings)
        img_loading = findViewById<View>(R.id.img_loading) as ImageView
        operatingAnim = AnimationUtils.loadAnimation(this, R.anim.rotate)
        operatingAnim!!.interpolator = LinearInterpolator()
        progressDialog = ProgressDialog(this)
        mDeviceAdapter = DeviceAdapter(this)
        mDeviceAdapter.setOnDeviceClickListener(object : OnDeviceClickListener {
            override fun onConnect(bleDevice: RawBlePeripheral) {
                if (!(bleDevice.isConnected || bleDevice.isConnecting)) {
                    if (bleDevice.productType == BleCmdContant.ProductType.M3) {
                        val m3 = bleDevice as M2BlePeripheral
                        val op: HashMap<*, *> = object : HashMap<Any?, Any?>() {
                            init {
                                put("CONNECTION_TYPE", "binary")
                            }
                        }
                        BluetoothClassicUtil.shared.connectToDevice(
                            m3.mac[0],
                            op,
                            object : BluetoothClassicUtil.Promise {
                                override fun resolve(var1: Any) {
                                    Log.d(TAG, "已连接 m3: $var1")
                                }

                                override fun reject(var1: String, var2: String) {}
                                override fun reject(var1: Throwable) {}
                            })
                    } else {
                        if (BleUtil.shared.connectedPeripherals.size >= pvMaxConnCount) {
                            showToast(
                                context,
                                "已经超过最大允许连接设备数(${pvMaxConnCount}只)\n如需连接更多，请往设置页设置"
                            )
                        } else {
                            BleUtil.shared.connect(bleDevice)
                        }
                    }
                }
            }

            override fun onDisConnect(bleDevice: RawBlePeripheral) {
                if (bleDevice.isConnected || bleDevice.isConnecting) {
                    BleUtil.shared.disconnect(bleDevice)
                }
            }

            override fun onDetail(bleDevice: RawBlePeripheral) {
                if(isForFac) return
                if (bleDevice.isConnected) {
                    val intent = Intent(this@BleScanActivity, OperationActivity::class.java)
                    intent.putExtra(OperationActivity.KEY_DATA, Gson().toJson(bleDevice))
                    startActivity(intent)
                }
            }
        })
        val listView_device = findViewById<View>(R.id.list_device) as ListView
        listView_device.adapter = mDeviceAdapter
    }

    private fun setScanRule() {
        var uuids: Array<String>?
        val str_uuid = et_uuid!!.text.toString()
        uuids = if (TextUtils.isEmpty(str_uuid)) {
            null
        } else {
            str_uuid.split(",".toRegex()).dropLastWhile { it.isEmpty() }.toTypedArray()
        }
        val uuid = "592e" + "6f28" + "-432f-4669-84b1-ddc45cc40dd9"
        if (uuids == null) uuids = arrayOf(uuid)
        var serviceUuids: Array<UUID?>? = null
        if (uuids != null && uuids.size > 0) {
            serviceUuids = arrayOfNulls(uuids.size)
            for (i in uuids.indices) {
                val name = uuids[i]
                val components =
                    name.split("-".toRegex()).dropLastWhile { it.isEmpty() }.toTypedArray()
                if (components.size != 5) {
                    serviceUuids[i] = null
                } else {
                    serviceUuids[i] = UUID.fromString(uuids[i])
                }
            }
        }
        val names: Array<String>?
        val str_name = et_name!!.text.toString()
        names = if (TextUtils.isEmpty(str_name)) {
            null
        } else {
            str_name.split(",".toRegex()).dropLastWhile { it.isEmpty() }.toTypedArray()
        }
        val mac = et_mac!!.text.toString()

//        boolean isAutoConnect = sw_auto.isChecked();

//        BleScanRuleConfig scanRuleConfig = new BleScanRuleConfig.Builder()
//                .setServiceUuids(serviceUuids)      // 只扫描指定的服务的设备，可选
//                .setDeviceName(true, names)   // 只扫描指定广播名的设备，可选
//                .setDeviceMac(mac)                  // 只扫描指定mac的设备，可选
//                .setAutoConnect(isAutoConnect)      // 连接时的autoConnect参数，可选，默认false
//                .setScanTimeOut(10000)              // 扫描超时时间，可选，默认10秒
//                .build();
//        BleUtil.shared.initScanRule(scanRuleConfig);
    }


    // 判断是否需要自动扫描
    private fun judgeAutoScan() {
        lifecycleScope.launch {
            delay(300)
            val autoScan = sp.getBoolean("pref_auto_search", false)
            if (autoScan) startScan()
        }
    }

    private fun startScan() {
        Log.d(TAG, "开始扫描...")
        //        BleUtil.shared.startScan(BleCmdContant.ScanUUIDFilter);
//        BleUtil.shared.startScan(new String[]{ScanUUIDFilter[3]}); // 测试 ble 4.x 扫描, ble 4.x 只能过滤 scanRecord.getServiceUuids(), 具体看 startLeScan 源码
//        BleUtil.shared.startScan(BleCmdContant.ProductType.WT2);
        val productOptionsValues: Array<String> =
            resources.getStringArray(R.array.product_options_values)
        if (pvProduct == productOptionsValues[0])
            BleUtil.shared.startScan(BleCmdContant.ProductType.WT2_Edge)
        else if (pvProduct == productOptionsValues[1])
            BleUtil.shared.startScan(BleCmdContant.ProductType.W3_Pro)
        else if (pvProduct == productOptionsValues[2])
            BleUtil.shared.startScan(BleCmdContant.ProductType.WT2)

        mMenu?.findItem(R.id.action_scan)?.title = getString(R.string.stop_scan)
    }

    companion object {
        private const val TAG = "BleScan"
    }


    private fun initListener() {
        with(mBinding) {
            btnStartTest.setOnClickListener {
                val connectDevices = BleUtil.shared.connectedPeripherals
                if (connectDevices.size < 2) {
                    showToast(context, "请先连接两只耳机！")
                } else if(isSameDirection()){
                    ToastUtil.showDialog(this@BleScanActivity,"请连接两只不同方向的耳机后，再进行测试（检查是否连接了两只左耳/两只右耳）")
                } else {
                    startTest()
                }
            }
            btnQuestion.setOnClickListener {
                AlertDialog.Builder(context).apply {
                    setTitle("提示")
                    setMessage(
                        "1、再次点击提示区域，可以停止录音测试\n" +
                                "2、测试时尽量选择左耳+右耳的组合"
                    )
                    setPositiveButton("关闭") { _, _ -> }
                }.show()
            }
            llBottomContent.setOnClickListener {
                AlertDialog.Builder(context).apply {
                    setTitle("提示")
                    setMessage("是否停止测试？")
                    setPositiveButton("确定") { _, _ -> stopTest() }
                    setNegativeButton("取消") { _, _ -> }
                }.show()
            }
        }
    }


    private fun startTest() {
        BleUtil.shared.setTouchEventCallback { perip, role ->
            Log.d(TAG, "startTest:  $role $perip")
            doOnTouchHeadset(perip, role)

        }


//        Log.d(TAG, "录音文件路径:  $RECORD_FILE_NAME")
        lifecycleScope.launch {
            val connectDevices = BleUtil.shared.connectedPeripherals
            connectDevices.forEach {
                if(it is WT2BlePeripheral) {
                    it.writeButtonEnabled(true)
                }
            }
            mBinding.btnStartTest.visibility = View.GONE
            mBinding.llBottomContent.visibility = View.VISIBLE
            updateTipText("请点击任意一只耳机", "左耳右耳都可以")
        }
    }

    private fun stopTest() {
        lifecycleScope.launch {
            BleUtil.shared.connectedPeripherals.forEach {
                if(it !is WT2BlePeripheral) return@forEach
                it.writeButtonEnabled(false)
                it.writeRecordEnabled(false)
            }
            mBinding.btnStartTest.visibility = View.VISIBLE
            mBinding.llBottomContent.visibility = View.GONE
            recordingRole["left"] = false
            recordingRole["right"] = false
            isPlaying = false
        }
    }

    private fun doOnTouchHeadset(device: RawBlePeripheral, role: RawBlePeripheral.Role) {
        if (isPlaying) {
            showToast(context, "正在播放，点击无效");return
        }
        if(device !is WT2BlePeripheral) return
        if (role == RawBlePeripheral.Role.Left) {
            if (recordingRole["left"] == true) { // 左耳在录音
                device.writeRecordEnabled(false)
                recordingRole["left"] = false
                updateTipText("双耳正在播放", "请在播放完成后再点击左/右耳进行录音")
                doPlay()
            } else if (recordingRole.getValue("right")) { // 右耳在录音
                showToast(context, "右耳正在录音，点击无效")
            } else {
                FileUtils.delete(RECORD_FILE_NAME)
                BleUtil.shared.sendBleStartCmds(arrayOf(device), true)
                recordingRole["left"] = true
                updateTipText("左耳正在录音", "再次点击左耳进行播放")
            }
        } else if (role == RawBlePeripheral.Role.Right) {
            if (recordingRole["left"] == true) { // 左耳在录音
                showToast(context, "左耳正在录音，点击无效")
            } else if (recordingRole.getValue("right")) { // 右耳在录音
                device.writeRecordEnabled(false)
                updateTipText("双耳正在播放", "请在播放完成后再点击左/右耳进行录音")
                recordingRole["right"] = false
                doPlay()
            } else {
                FileUtils.delete(RECORD_FILE_NAME)
                BleUtil.shared.sendBleStartCmds(arrayOf(device), true)
                recordingRole["right"] = true
                updateTipText("右耳正在录音", "再次点击右耳进行播放")
            }
        }
    }

    private fun doPlay() {
        lifecycleScope.launch {
            isPlaying = true
            delay(500)
//            val filePath = context.applicationInfo.dataDir + "/record/16ktts(zh-CN).pcm"
//            val bytes = FileUtils.read(filePath)
            val bytes = FileUtils.read(RECORD_FILE_NAME)
            BleUtil.shared.connectedPeripherals.forEach { peripheral ->
                lifecycleScope.launch {
                    withContext(Dispatchers.IO) {
                        if (peripheral is WT2BlePeripheral && bytes != null) {
                            Log.d(TAG, "向耳机写入录音数据 $peripheral")
                            BleUtil.shared.sendSound(peripheral, bytes, 44, 18)
                            delay(100)
                            isPlaying = false
                            updateTipText("播放完成", "再次点击耳机说话")
//                            peripheral.enableSpeaker()
//                            peripheral.testPlaySound(bytes)
                        }
                    }
                }
            }
        }
    }

    private fun updateTipText(str: String, str2: String = "") {
        lifecycleScope.launch(Dispatchers.Main) {
            mBinding.tvTipTitle.text = str
            mBinding.tvTipContent.text = str2
        }
    }


    // 重写获取PV的值
    private fun updatePreferenceValues() {
        pvProduct = sp.getString("pref_search_product", "W3").toString()
        pvAutoConnect = sp.getBoolean("pref_auto_connect", false)
        pvMaxConnCount = sp.getString("pref_max_conn_count", "6")!!.toInt()
        pvAutoSwitch = sp.getBoolean("pref_auto_switch", false)
        pvSwitchDirection = sp.getString("pref_switch_direction", "0")!!.toInt()
        pvSignalStrength = sp.getInt("pref_signal_strength", -90)
        Log.d(
            "配置",
            "pvProduct：$pvProduct pvAutoConnect:$pvAutoConnect  pvMaxConnCount:$pvMaxConnCount  pvAutoSwitch:$pvAutoSwitch  pvSwitchDirection:$pvSwitchDirection"
        )
    }

    override fun onResume() {
        super.onResume()
        updatePreferenceValues()
        judgeAutoScan()

        BleUtil.shared.setLogCallback { level: Int, tag: String?, msg: String?, tr: Throwable? ->
            if (level == 1) {
                Log.e(tag, msg!!)
            } else if (level == 2) {
                Log.d(tag, msg!!)
            }
        }
    }


    @Synchronized
    private fun doAutoConnect() {
        lifecycleScope.launch {
            var count = 0 // 正在连接、已连接的设备数
            BleUtil.shared.searchedPeripherals.forEach {
                if (it.state == PeripheralState.NONE)
                    ++count
            }
            if (count >= pvMaxConnCount) {
                Log.e(
                    TAG,
                    "已经超过最大允许连接设备数($pvMaxConnCount 只)，如需连接更多，请往设置页设置"
                )
                return@launch
            }
            if (pvAutoConnect) {
                BleUtil.shared.notConnectPeripherals.forEach {
                    if (it.state == PeripheralState.NONE) {
                        if (pvSwitchDirection == 0) {
                            delay(50)
                            BleUtil.shared.connect(it)
                        } else if (pvSwitchDirection == 1 && it.isFacFirm) {
                            delay(50)
                            BleUtil.shared.connect(it)
                        } else if (pvSwitchDirection == 2 && !it.isFacFirm) {
                            delay(50)
                            BleUtil.shared.connect(it)
                        }
                    }
                }
            }

        }
    }

    @Synchronized
    private fun doAutoSwitch(device: RawBlePeripheral?) {
        if (!pvAutoSwitch) return
        if (pvSwitchDirection == 0) return
        lifecycleScope.launch {
            delay(2000)
            if (device is WT2BlePeripheral) {
                if (pvSwitchDirection == 1 && device.isFacFirm) {  // 工厂->用户
                    Log.d(TAG, "切换固件：工厂->用户 $device")
                    device.writeModeUser()
                } else if (pvSwitchDirection == 2 && !device.isFacFirm) { //  用户->工厂
                    Log.d(TAG, "切换固件： 用户->工厂  $device")
                    device.writeModeFactory()
                }
            }
        }
    }






    override fun onDestroy() {
        super.onDestroy()
        EventBus.getDefault().unregister(this)
    }


    @Subscribe(threadMode = ThreadMode.POSTING)
    fun onMessageEvent(event: HashMap<String, Object>) {
        if (!event.containsKey("BLEAudioRecordData")) return
        val value = event.getValue("BLEAudioRecordData") as Array<Object>
        val byteArray: ByteArray = value[1] as ByteArray
        FileUtils.write(RECORD_FILE_NAME, byteArray)
    }

    @Subscribe(threadMode = ThreadMode.POSTING)
    fun onMessageEvent(event: BleEventBean) {
        if (event.eventName == BleEventName.BleConnectStandby) {
            val device = event.getBlePeripheral()
            doAutoSwitch(device)
        }
    }

    override fun onCreateOptionsMenu(menu: Menu?): Boolean {
        menuInflater.inflate(R.menu.menu_scan_page, menu);
        mMenu = menu
        return super.onCreateOptionsMenu(menu)
    }

    override fun onOptionsItemSelected(item: MenuItem): Boolean {
        when (item.itemId) {
            R.id.action_settings -> {
                startActivity(Intent(this, BleSettingActivity::class.java))
                return true
            }

            R.id.action_scan -> {
                if (item.title == getString(R.string.start_scan)) {
                    BleUtil.shared.enableBluetooth(this)
                    BleUtil.shared.requestPermission(this) { success: Boolean ->
                        if (success) startScan()
                        null
                    }
                    item.title = getString(R.string.stop_scan)
                } else if (item.title == getString(R.string.stop_scan)) {
                    Log.d(
                        TAG,
                        "停止扫描, 当前扫描到: " + BleUtil.shared.connectedPeripherals.toString()
                    )
                    BleUtil.shared.stopScan()
                    item.title = getString(R.string.start_scan)
                }
            }
        }
        return super.onOptionsItemSelected(item)
    }


    // 判断耳机是否是相同的方向（两只左耳/两只右耳）
    private fun isSameDirection(): Boolean {
        val connectDevices = BleUtil.shared.connectedPeripherals
        val devices = connectDevices.map {
            it as WT2BlePeripheral
        }
        if (devices.size < 2) return false
        val first = devices[0]
        val second = devices[1]
        return first.role == second.role
    }


}


