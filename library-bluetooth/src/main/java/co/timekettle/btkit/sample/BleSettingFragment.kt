package co.timekettle.btkit.sample

import android.content.SharedPreferences
import android.os.Bundle
import android.view.LayoutInflater
import android.widget.SeekBar
import android.widget.TextView
import androidx.appcompat.app.AlertDialog
import androidx.preference.ListPreference
import androidx.preference.Preference
import androidx.preference.PreferenceFragmentCompat
import androidx.preference.PreferenceGroup
import androidx.preference.PreferenceManager
import co.timekettle.btkit.R

class BleSettingFragment : PreferenceFragmentCompat() {
    lateinit var sp: SharedPreferences
    var pref_signal_strength: Int = -90
    lateinit var prefSignalStrength: Preference
    lateinit var prefMaxCount: Preference
    lateinit var prefDirection: ListPreference

    override fun onCreate(savedInstanceState: Bundle?) {
        sp = PreferenceManager.getDefaultSharedPreferences(requireContext())
        super.onCreate(savedInstanceState)
    }

    override fun onCreatePreferences(savedInstanceState: Bundle?, rootKey: String?) {
        setPreferencesFromResource(R.xml.settingpreference, rootKey)
        preferenceScreen.removeIconSpace()
        pref_signal_strength = sp.getInt("pref_signal_strength", -90)
        prefSignalStrength = findPreference("pref_signal_strength")!!
        prefMaxCount = findPreference("pref_max_conn_count")!!
        prefSignalStrength.summary = "${pref_signal_strength}dBm"
        prefMaxCount.summary = sp.getString("pref_max_conn_count", "6")
        prefSignalStrength.setOnPreferenceClickListener { preference ->
            val builder = AlertDialog.Builder(requireContext())
            val inflater = LayoutInflater.from(requireContext())
            val dialogView = inflater.inflate(R.layout.dialog_signal_strength, null)
            val seekBar = dialogView.findViewById<SeekBar>(R.id.dialog_seekbar)
            val textView = dialogView.findViewById<TextView>(R.id.tv_value)
            textView.text = prefSignalStrength?.summary
            seekBar.progress = pref_signal_strength
            builder.setView(dialogView).setNegativeButton("关闭") { _, _ -> }
            val dialog = builder.create()
            seekBar.setOnSeekBarChangeListener(object : SeekBar.OnSeekBarChangeListener {
                override fun onProgressChanged(
                    seekBar: SeekBar?,
                    progress: Int,
                    fromUser: Boolean
                ) {
                    updateSpKV("pref_signal_strength", progress)
                    textView.text = "${progress}dBm"
                    prefSignalStrength.summary = "${progress}dBm"
                }

                override fun onStartTrackingTouch(seekBar: SeekBar?) {}

                override fun onStopTrackingTouch(seekBar: SeekBar?) {}
            })
            dialog.show()
            true
        }

        prefMaxCount.onPreferenceChangeListener =
            Preference.OnPreferenceChangeListener { preference, newValue ->
                val selectedValue = newValue.toString()
                prefMaxCount?.summary = selectedValue
                true
            }

        // 切换方向
        prefDirection = findPreference("pref_switch_direction")!!
        prefDirection.onPreferenceChangeListener =
            Preference.OnPreferenceChangeListener { _, newValue ->
                prefDirection?.summary = prefDirection.entries[(newValue as String).toInt()]
                true
            }
        prefDirection?.summary = prefDirection.entries[(prefDirection.value).toInt()]


        val prefSearchProduct = findPreference<ListPreference>("pref_search_product")
        prefSearchProduct?.summary = sp.getString("pref_search_product", "W3")
        prefSearchProduct?.onPreferenceChangeListener =
            Preference.OnPreferenceChangeListener { preference, newValue ->
                val selectedValue = newValue.toString()
                prefSearchProduct?.summary = selectedValue
                true // 返回 true 表示已经处理了监听器事件
            }
    }


    private fun Preference.removeIconSpace() {
        isIconSpaceReserved = false
        if (this is PreferenceGroup) {
            for (i in 0 until preferenceCount) {
                getPreference(i).removeIconSpace()
            }
        }
    }

    private fun updateSpKV(key: String, value: String) {
        val editor = sp.edit()
        editor.putString(key, value)
        editor.apply()
    }

    private fun updateSpKV(key: String, value: Int) {
        val editor = sp.edit()
        editor.putInt(key, value)
        editor.apply()
    }

}