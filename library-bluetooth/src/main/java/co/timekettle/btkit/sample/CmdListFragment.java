package co.timekettle.btkit.sample;

import android.annotation.TargetApi;
import android.app.AlertDialog;
import android.bluetooth.BluetoothGatt;
import android.content.Context;
import android.content.DialogInterface;
import android.os.Build;
import android.os.Bundle;
import android.os.Handler;
import android.os.Looper;
import android.text.InputType;
import android.util.Log;
import android.view.Gravity;
import android.view.LayoutInflater;
import android.view.Menu;
import android.view.MenuInflater;
import android.view.MenuItem;
import android.view.View;
import android.view.ViewGroup;
import android.widget.Adapter;
import android.widget.AdapterView;
import android.widget.ArrayAdapter;
import android.widget.BaseAdapter;
import android.widget.CompoundButton;
import android.widget.EditText;
import android.widget.ListView;
import android.widget.SimpleAdapter;
import android.widget.Switch;
import android.widget.TextView;

import androidx.annotation.NonNull;
import androidx.appcompat.app.AppCompatActivity;
import androidx.appcompat.widget.Toolbar;
import androidx.fragment.app.Fragment;

import java.io.File;
import java.io.FileInputStream;
import java.io.FileNotFoundException;
import java.io.FileOutputStream;
import java.io.IOException;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.function.Function;

import co.timekettle.btkit.BleCmd;
import co.timekettle.btkit.BleCmdContant;
import co.timekettle.btkit.BleUtil;
import co.timekettle.btkit.bean.M2BlePeripheral;
import co.timekettle.btkit.bean.RawBlePeripheral;
import co.timekettle.btkit.bean.WT2BlePeripheral;
import co.timekettle.btkit.R;
import co.timekettle.btkit.bluetoothlib.BluetoothClassicUtil;


/**
 * CMD指令的Fragment，服务列表Fragment
 */
@TargetApi(Build.VERSION_CODES.JELLY_BEAN_MR2)
public class CmdListFragment extends Fragment {

    private static final String TAG = "CmdListFragment";
    private TextView txt_name, txt_mac;
    private ArrayAdapter<String> mMsgListAdapter;
    private ResultAdapter mResultAdapter;
    private List<String> logDescs = new ArrayList<>();

    @Override
    public View onCreateView(LayoutInflater inflater, ViewGroup container, Bundle savedInstanceState) {
        View v = inflater.inflate(R.layout.fragment_service_list, null);
        initView(v);
        showData();
        return v;
    }

    private void initView(View v) {

        txt_name = (TextView) v.findViewById(R.id.txt_name);
        txt_mac = (TextView) v.findViewById(R.id.txt_mac);
        ListView msgListView = (ListView) v.findViewById(R.id.list_msg);
        mMsgListAdapter = new ArrayAdapter<>(v.getContext(), R.layout.adapter_oneline_msg);
        msgListView.setAdapter(mMsgListAdapter);
        msgListView.setTranscriptMode(ListView.TRANSCRIPT_MODE_ALWAYS_SCROLL);

        mResultAdapter = new ResultAdapter(getActivity());
        ListView cmdListView = (ListView) v.findViewById(R.id.list_service);
        cmdListView.setAdapter(mResultAdapter);
        cmdListView.setOnItemClickListener(new AdapterView.OnItemClickListener() {
            @Override
            public void onItemClick(AdapterView<?> parent, View view, int position, long id) {
                Log.d("CmdListFragment", "onItemClick: " + position);
                BleCmd cmd = mResultAdapter.getItem(position);
                RawBlePeripheral bleDevice = ((OperationActivity) getActivity()).getBleDevice();
                if (bleDevice.productType == BleCmdContant.ProductType.M3) {
                    byte[] data = BleUtil.shared.getM3CmdData(cmd.id);
                    BluetoothClassicUtil.shared.sendCmdData(bleDevice.id, data);
                } else {
                    assert cmd != null;

                    if (cmd.getParamLen() > 0) {
                        showAlert(v.getContext(), cmd, (byte[] params) -> {
                            Log.d(TAG, "onItemClick: " + Arrays.toString(params));
//                            BleUtil.shared.sendCmdToQueue(bleDevice, cmd.id, params);
                            return null;
                        });
                        return;
                    }
                    if (cmd.id.equals(BleCmdContant.AppCmdId.DFU)) {
                        String path = getContext().getExternalCacheDir() + "/nrf52840_app_tmk004_V37.zip";
//                    String path = getContext().getExternalCacheDir() + "/nrf52840_app_01_v3005_0.5.zip";
                        bleDevice.startDfu(path);
                    } else if (cmd.id.equals(BleCmdContant.AppCmdId.Play)) {
//                        BleUtil.shared.sendCmd(bleDevice, cmd);
//                        ((WT2BlePeripheral)bleDevice).playSound(CmdListFragment.this.getSoundData());
                        ((WT2BlePeripheral)bleDevice).testPlaySound(CmdListFragment.this.getSoundData(),"", true);
                    } else if (cmd.id.equals(BleCmdContant.AppCmdId.SNCodeW)) {
                        // 测试写入自定义数据
                        // 这里先注释掉了，没有提供这个方法
//                        BleUtil.shared.sendCmdToQueue(bleDevice, cmd.id, "01010101".getBytes());
                    } else {
                        BleUtil.shared.sendCmdToQueue(bleDevice, cmd.id);
                    }
                }
            }
        });

        Switch switch_list = v.findViewById(R.id.switch_list);
        switch_list.setOnCheckedChangeListener((buttonView, isChecked) -> {
            msgListView.setVisibility(!isChecked ? View.GONE : View.VISIBLE);
            cmdListView.setVisibility(isChecked ? View.GONE : View.VISIBLE);
        });

        BleUtil.shared.setLogCallback((int level, String tag, String msg, Throwable tr) -> {
            if (level == 1) Log.d(tag, msg);
            if (level == 2) Log.e(tag, msg);

            runOnUiThread(() -> {
                this.mMsgListAdapter.add(msg);
                this.mMsgListAdapter.notifyDataSetChanged();
            });
        });

    }

    Handler uiHandler = new Handler(Looper.getMainLooper());
    void runOnUiThread(Runnable r) {
        uiHandler.post(r);
    }

    void showAlert(Context context, BleCmd cmd, Function<byte[], Object> callback) {
        int paramLen = cmd.getParamLen();

        AlertDialog.Builder builder = new AlertDialog.Builder(context);
        final EditText input = new EditText(context);
        input.setInputType(InputType.TYPE_CLASS_TEXT); // 设置输入类型
        builder.setView(input);
        builder.setTitle("输入指令参数(长度:" + paramLen + ")");
        builder.setMessage("例: 希望输入: 0x12 0x23, 则输入 1223");
        builder.setIcon(R.mipmap.ic_launcher);
        builder.setCancelable(true);            // 点击对话框以外的区域是否让对话框消失

        //设置正面按钮
        builder.setPositiveButton("确定", (dialog, which) -> {
            Log.d("==", "setPositiveButton: " + input.getText().toString());
            String cmdStr = input.getText().toString();
            if (cmdStr.length() == 0 || cmdStr.length() % 2 != 0) {
                Log.e("==", "showAlert: 非法输入: " + cmdStr);
                return;
            }
            byte[] values = new byte[cmdStr.length() / 2];
            // 将每两个字符数字转换为字节数组
            for (int i = 0; i < cmdStr.length(); i += 2) {
                String twoChars = cmdStr.substring(i, i + 2);
                int value = Integer.parseInt(twoChars, 16); // 将两个字符数字解析为整数
                values[i / 2] = (byte) value; // 将整数转换为字节并存储在字节数组中
            }

            if (callback != null) callback.apply(values);
            dialog.dismiss();
        });
        //设置反面按钮
        builder.setNegativeButton("取消", (dialog, which) -> {
            dialog.dismiss();
        });
//        //设置中立按钮
//        builder.setNeutralButton("保密", (dialog, which) -> dialog.dismiss());

        AlertDialog dialog = builder.create();      //创建AlertDialog对象
//        //对话框显示的监听事件
//        dialog.setOnShowListener(dialog12 -> Log.e("===", "对话框显示了"));
//        //对话框消失的监听事件
//        dialog.setOnCancelListener(dialog1 -> Log.e("===", "对话框消失了"));
        dialog.show();                              //显示对话框
    }

    @Override
    public void onCreateOptionsMenu(@NonNull Menu menu, @NonNull MenuInflater inflater) {
        inflater.inflate(R.menu.menu_scan_page, menu);
        super.onCreateOptionsMenu(menu, inflater);
    }

    @Override
    public void onDestroy() {
        super.onDestroy();

        BleUtil.shared.setLogCallback(null);
    }

    @Override
    public boolean onOptionsItemSelected(@NonNull MenuItem item) {
        return super.onOptionsItemSelected(item);

    }

    private byte[] getSoundData() {
        byte[] out = new byte[0];
        String path = getContext().getExternalCacheDir() + "/16ktts(en-US).pcm";
        try {
            FileInputStream inputStream = new FileInputStream(path);
//            out = new byte[inputStream.available()];
            out = new byte[16000 * 2 * 3];
            inputStream.read(out);
        } catch (IOException e) {
            e.printStackTrace();
        }
        return out;
    }

    private void showData() {
        RawBlePeripheral bleDevice = ((OperationActivity) getActivity()).getBleDevice();
        String name = "";
        String mac = "";
        String info = "";
        Log.d("TAG","指令 ："+ BleCmdContant.WSeriesCmds);
        Log.d("TAG","指令 bleDevice ："+ bleDevice);

        if (bleDevice instanceof WT2BlePeripheral) {
            WT2BlePeripheral wt2 = (WT2BlePeripheral)bleDevice;
            name = wt2.name + "(" + wt2.macSuffix4 + ")";
            mac = wt2.mac;
            info = wt2.oem + " " + wt2.serialNumber + " " + wt2.hardwareVersion + " " + wt2.firmwareVersion + " " + wt2.stVersion;


            for (BleCmd cmd: BleCmdContant.WSeriesCmds) {
                mResultAdapter.addResult(cmd);
            }
        } else if (bleDevice instanceof M2BlePeripheral) {
            M2BlePeripheral m2 = (M2BlePeripheral)bleDevice;
            name = m2.name + "(" + m2.macSuffix4[0] + " " + m2.macSuffix4[1] + ")";
            mac = "(" + m2.mac[0] + " " + m2.mac[1] + ")";

//            info = m2.;
            for (BleCmd cmd: BleCmdContant.MSeriesCmds) {
                mResultAdapter.addResult(cmd);
            }
        }

        BluetoothGatt gatt = BleUtil.shared.getDeviceGatt(bleDevice.id);

        txt_name.setText(String.valueOf(getActivity().getString(R.string.name) + name + "(" + info + ")"));
        txt_mac.setText(String.valueOf(getActivity().getString(R.string.mac) + mac));

        mResultAdapter.notifyDataSetChanged();
    }

    private class ResultAdapter extends BaseAdapter {

        private Context context;
        private final List<BleCmd> cmds;

        ResultAdapter(Context context) {
            this.context = context;
            cmds = new ArrayList<>();
        }

        void addResult(BleCmd cmd) {
            cmds.add(cmd);
        }

        void clear() {
            cmds.clear();
        }

        @Override
        public int getCount() {
            return cmds.size();
        }

        @Override
        public BleCmd getItem(int position) {
            if (position > cmds.size())
                return null;
            return cmds.get(position);
        }

        @Override
        public long getItemId(int position) {
            return 0;
        }

        @Override
        public View getView(int position, View convertView, ViewGroup parent) {
            ViewHolder holder;
            if (convertView != null) {
                holder = (ViewHolder) convertView.getTag();
            } else {
                convertView = View.inflate(context, R.layout.adapter_service, null);
                holder = new ViewHolder();
                convertView.setTag(holder);
                holder.txt_title = (TextView) convertView.findViewById(R.id.txt_title);
                holder.txt_uuid = (TextView) convertView.findViewById(R.id.txt_uuid);
                holder.txt_type = (TextView) convertView.findViewById(R.id.txt_type);
            }

            BleCmd cmd = cmds.get(position);
            String uuid = cmd.service;

            holder.txt_title.setText(cmd.id + "(" + position + ")");
            holder.txt_uuid.setText(uuid);
            holder.txt_type.setText(getActivity().getString(R.string.type));
            return convertView;
        }

        class ViewHolder {
            TextView txt_title;
            TextView txt_uuid;
            TextView txt_type;
        }
    }

}
