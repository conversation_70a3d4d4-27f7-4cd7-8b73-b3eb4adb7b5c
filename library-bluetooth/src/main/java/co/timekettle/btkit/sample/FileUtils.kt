package co.timekettle.btkit.sample

import java.io.DataOutputStream
import java.io.File
import java.io.FileInputStream
import java.io.FileOutputStream
import java.io.IOException
import java.io.InputStream
import java.nio.file.Files

object FileUtils {

    /**
     * 向指定路径的文件追加写入Bytearray，如果文件不存在则先创建再写入
     *
     * @param path 文件路径
     * @param content 待写入内容
     * @return 是否写入成功
     */
    fun write(path: String, content: ByteArray): Boolean {
        return try {
            val file = File(path)
            if (!file.exists()) {
                file.parentFile.mkdirs()
                file.createNewFile()
            }
            val fos = FileOutputStream(file, true)
            fos.write(content)
            fos.close()
            true
        } catch (e: Exception) {
            e.printStackTrace()
            false
        }
    }

    /**
     * 删除指定路径的文件
     *
     * @param path 文件路径
     * @return 是否删除成功
     */
    fun delete(path: String): Boolean {
        return try {
            val file = File(path)
            if (file.exists()) {
                file.delete()
            }
            true
        } catch (e: Exception) {
            e.printStackTrace()
            false
        }
    }

    /**
     * 以ByteArray形式读取指定文件内容，如果读取错误返回null
     *
     * @param path 文件路径
     * @return 文件内容byte数组或null
     */
    fun read(path: String): ByteArray? {
        return try {
            val file = File(path)
            if (!file.exists()) {
                null
            } else {
                file.readBytes()
            }
        } catch (e: Exception) {
            e.printStackTrace()
            null
        }
    }
}
