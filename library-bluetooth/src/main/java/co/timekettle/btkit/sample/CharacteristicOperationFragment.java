package co.timekettle.btkit.sample;

import android.annotation.TargetApi;
import android.bluetooth.BluetoothGattCharacteristic;
import android.os.Build;
import android.os.Bundle;
import android.text.TextUtils;
import android.text.method.ScrollingMovementMethod;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.Button;
import android.widget.EditText;
import android.widget.LinearLayout;
import android.widget.TextView;

import androidx.fragment.app.Fragment;

import java.io.IOException;
import java.util.ArrayList;
import java.util.List;

import co.timekettle.btkit.BleCmdContant;
import co.timekettle.btkit.BleUtil;
import co.timekettle.btkit.bean.RawBlePeripheral;
import co.timekettle.btkit.R;

@TargetApi(Build.VERSION_CODES.JELLY_BEAN_MR2)
public class CharacteristicOperationFragment extends Fragment {

    public static final int PROPERTY_READ = 1;
    public static final int PROPERTY_WRITE = 2;
    public static final int PROPERTY_WRITE_NO_RESPONSE = 3;
    public static final int PROPERTY_NOTIFY = 4;
    public static final int PROPERTY_INDICATE = 5;

    private LinearLayout layout_container;
    private final List<String> childList = new ArrayList<>();

    @Override
    public View onCreateView(LayoutInflater inflater, ViewGroup container, Bundle savedInstanceState) {
        View v = inflater.inflate(R.layout.fragment_characteric_operation, null);
        initView(v);
        return v;
    }

    private void initView(View v) {
        layout_container = (LinearLayout) v.findViewById(R.id.layout_container);
    }

    public void showData() {
        final RawBlePeripheral bleDevice = ((OperationActivity) getActivity()).getBleDevice();
        final BluetoothGattCharacteristic characteristic = ((OperationActivity) getActivity()).getCharacteristic();
        final int charaProp = ((OperationActivity) getActivity()).getCharaProp();
        String child = characteristic.getUuid().toString() + String.valueOf(charaProp);

        for (int i = 0; i < layout_container.getChildCount(); i++) {
            layout_container.getChildAt(i).setVisibility(View.GONE);
        }
        if (childList.contains(child)) {
            layout_container.findViewWithTag(bleDevice.getKey() + characteristic.getUuid().toString() + charaProp).setVisibility(View.VISIBLE);
        } else {
            childList.add(child);

            View view = LayoutInflater.from(getActivity()).inflate(R.layout.layout_characteric_operation, null);
            view.setTag(bleDevice.getKey() + characteristic.getUuid().toString() + charaProp);
            LinearLayout layout_add = (LinearLayout) view.findViewById(R.id.layout_add);
            final TextView txt_title = (TextView) view.findViewById(R.id.txt_title);
            txt_title.setText(String.valueOf(characteristic.getUuid().toString() + getActivity().getString(R.string.data_changed)));
            final TextView txt = (TextView) view.findViewById(R.id.txt);
            txt.setMovementMethod(ScrollingMovementMethod.getInstance());

            switch (charaProp) {
                case PROPERTY_READ: {
                    View view_add = LayoutInflater.from(getActivity()).inflate(R.layout.layout_characteric_operation_button, null);
                    Button btn = (Button) view_add.findViewById(R.id.btn);
                    btn.setText(getActivity().getString(R.string.read));
                    btn.setOnClickListener(new View.OnClickListener() {
                        @Override
                        public void onClick(View view) {
                            // FIXME: 2022/5/20 read
//                            BleUtil.shared.read(
//                                    bleDevice,
//                                    characteristic.getService().getUuid().toString(),
//                                    characteristic.getUuid().toString(),
//                                    new BleReadCallback() {
//
//                                        @Override
//                                        public void onReadSuccess(final byte[] data) {
//                                            runOnUiThread(new Runnable() {
//                                                @Override
//                                                public void run() {
//                                                    addText(txt, HexUtil.formatHexString(data, true));
//                                                }
//                                            });
//                                        }
//
//                                        @Override
//                                        public void onReadFailure(final BleException exception) {
//                                            runOnUiThread(new Runnable() {
//                                                @Override
//                                                public void run() {
//                                                    addText(txt, exception.toString());
//                                                }
//                                            });
//                                        }
//                                    });
                        }
                    });
                    layout_add.addView(view_add);
                }
                break;

                case PROPERTY_WRITE: {
                    View view_add = LayoutInflater.from(getActivity()).inflate(R.layout.layout_characteric_operation_et, null);
                    final EditText et = (EditText) view_add.findViewById(R.id.et);
                    Button btn = (Button) view_add.findViewById(R.id.btn);
                    btn.setText(getActivity().getString(R.string.write));
                    btn.setOnClickListener(new View.OnClickListener() {
                        @Override
                        public void onClick(View view) {
                            String hex = et.getText().toString();
                            if (TextUtils.isEmpty(hex)) {
                                return;
                            }
                            BleUtil.shared.sendCmdsToPeripherals(new RawBlePeripheral[]{bleDevice}, new String[]{BleCmdContant.AppCmdId.RecordStart});
//                            BleUtil.shared.write(
//                                    bleDevice,
//                                    characteristic.getService().getUuid().toString(),
//                                    characteristic.getUuid().toString(),
//                                    HexUtil.hexStringToBytes(hex),
//                                    new BleWriteCallback() {
//
//                                        @Override
//                                        public void onWriteSuccess(final int current, final int total, final byte[] justWrite) {
//                                            runOnUiThread(new Runnable() {
//                                                @Override
//                                                public void run() {
//                                                    addText(txt, "write success, current: " + current
//                                                            + " total: " + total
//                                                            + " justWrite: " + HexUtil.formatHexString(justWrite, true));
//                                                }
//                                            });
//                                        }
//
//                                        @Override
//                                        public void onWriteFailure(final BleException exception) {
//                                            runOnUiThread(new Runnable() {
//                                                @Override
//                                                public void run() {
//                                                    addText(txt, exception.toString());
//                                                }
//                                            });
//                                        }
//                                    });
                        }
                    });
                    layout_add.addView(view_add);
                }
                break;

                case PROPERTY_WRITE_NO_RESPONSE: {
                    View view_add = LayoutInflater.from(getActivity()).inflate(R.layout.layout_characteric_operation_et, null);
                    final EditText et = (EditText) view_add.findViewById(R.id.et);
                    Button btn = (Button) view_add.findViewById(R.id.btn);
                    btn.setText(getActivity().getString(R.string.write));
                    btn.setOnClickListener(new View.OnClickListener() {
                        @Override
                        public void onClick(View view) {
                            String hex = et.getText().toString();
                            if (TextUtils.isEmpty(hex)) {
                                return;
                            }
//                            BleUtil.shared.write(
//                                    bleDevice,
//                                    characteristic.getService().getUuid().toString(),
//                                    characteristic.getUuid().toString(),
//                                    HexUtil.hexStringToBytes(hex),
//                                    new BleWriteCallback() {
//
//                                        @Override
//                                        public void onWriteSuccess(final int current, final int total, final byte[] justWrite) {
//                                            runOnUiThread(new Runnable() {
//                                                @Override
//                                                public void run() {
//                                                    addText(txt, "write success, current: " + current
//                                                            + " total: " + total
//                                                            + " justWrite: " + HexUtil.formatHexString(justWrite, true));
//                                                }
//                                            });
//                                        }
//
//                                        @Override
//                                        public void onWriteFailure(final BleException exception) {
//                                            runOnUiThread(new Runnable() {
//                                                @Override
//                                                public void run() {
//                                                    addText(txt, exception.toString());
//                                                }
//                                            });
//                                        }
//                                    });
                        }
                    });
                    layout_add.addView(view_add);
                }
                break;

                case PROPERTY_NOTIFY: {
                    View view_add = LayoutInflater.from(getActivity()).inflate(R.layout.layout_characteric_operation_button, null);
                    final Button btn = (Button) view_add.findViewById(R.id.btn);
                    btn.setText(getActivity().getString(R.string.open_notification));
                    btn.setOnClickListener(new View.OnClickListener() {
                        @Override
                        public void onClick(View view) {
                            if (btn.getText().toString().equals(getActivity().getString(R.string.open_notification))) {
                                btn.setText(getActivity().getString(R.string.close_notification));
//                                BleUtil.shared.notify(
//                                        bleDevice,
//                                        characteristic.getService().getUuid().toString(),
//                                        characteristic.getUuid().toString(),
//                                        new BleNotifyCallback() {
//
//                                            @Override
//                                            public void onNotifySuccess() {
//                                                runOnUiThread(new Runnable() {
//                                                    @Override
//                                                    public void run() {
//                                                        addText(txt, "notify success");
//                                                    }
//                                                });
//                                            }
//
//                                            @Override
//                                            public void onNotifyFailure(final BleException exception) {
//                                                runOnUiThread(new Runnable() {
//                                                    @Override
//                                                    public void run() {
//                                                        addText(txt, exception.toString());
//                                                    }
//                                                });
//                                            }
//
//                                            @Override
//                                            public void onCharacteristicChanged(byte[] data) {
//                                                runOnUiThread(new Runnable() {
//                                                    @Override
//                                                    public void run() {
//                                                        addText(txt, HexUtil.formatHexString(characteristic.getValue(), true));
//                                                    }
//                                                });
//                                            }
//                                        });
                            } else {
                                btn.setText(getActivity().getString(R.string.open_notification));
//                                BleUtil.shared.stopNotify(
//                                        bleDevice,
//                                        characteristic.getService().getUuid().toString(),
//                                        characteristic.getUuid().toString());
                            }
                        }
                    });
                    layout_add.addView(view_add);
                }
                break;

                case PROPERTY_INDICATE: {
                    View view_add = LayoutInflater.from(getActivity()).inflate(R.layout.layout_characteric_operation_button, null);
                    final Button btn = (Button) view_add.findViewById(R.id.btn);
                    btn.setText(getActivity().getString(R.string.open_notification));
                    btn.setOnClickListener(new View.OnClickListener() {
                        @Override
                        public void onClick(View view) {
                            if (btn.getText().toString().equals(getActivity().getString(R.string.open_notification))) {
                                btn.setText(getActivity().getString(R.string.close_notification));
//                                BleUtil.shared.indicate(
//                                        bleDevice,
//                                        characteristic.getService().getUuid().toString(),
//                                        characteristic.getUuid().toString(),
//                                        new BleIndicateCallback() {
//
//                                            @Override
//                                            public void onIndicateSuccess() {
//                                                runOnUiThread(new Runnable() {
//                                                    @Override
//                                                    public void run() {
//                                                        addText(txt, "indicate success");
//                                                    }
//                                                });
//                                            }
//
//                                            @Override
//                                            public void onIndicateFailure(final BleException exception) {
//                                                runOnUiThread(new Runnable() {
//                                                    @Override
//                                                    public void run() {
//                                                        addText(txt, exception.toString());
//                                                    }
//                                                });
//                                            }
//
//                                            @Override
//                                            public void onCharacteristicChanged(byte[] data) {
//                                                runOnUiThread(new Runnable() {
//                                                    @Override
//                                                    public void run() {
//                                                        addText(txt, HexUtil.formatHexString(characteristic.getValue(), true));
//                                                    }
//                                                });
//                                            }
//                                        });
                            } else {
                                btn.setText(getActivity().getString(R.string.open_notification));
//                                BleUtil.shared.stopIndicate(
//                                        bleDevice,
//                                        characteristic.getService().getUuid().toString(),
//                                        characteristic.getUuid().toString());
                            }
                        }
                    });
                    layout_add.addView(view_add);
                }
                break;
            }

            layout_container.addView(view);
        }
    }

    private void runOnUiThread(Runnable runnable) {
        if (isAdded() && getActivity() != null)
            getActivity().runOnUiThread(runnable);
    }

    private void addText(TextView textView, String content) {
        textView.append(content);
        textView.append("\n");
        int offset = textView.getLineCount() * textView.getLineHeight();
        if (offset > textView.getHeight()) {
            textView.scrollTo(0, offset - textView.getHeight());
        }
    }


}
