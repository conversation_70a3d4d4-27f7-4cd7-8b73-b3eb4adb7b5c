<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:paddingHorizontal="12dp"
    android:paddingTop="16dp">

    <TextView
        android:id="@+id/tv_title"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginLeft="12dp"
        android:text="信号强度："
        android:textSize="16sp"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintTop_toTopOf="parent">

    </TextView>

    <TextView
        android:id="@+id/tv_value"
        app:layout_constraintBottom_toBottomOf="@+id/tv_title"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginLeft="3dp"
        android:text="-90dBm"
        android:textColor="@android:color/black"
        android:textSize="16sp"
        android:textStyle="bold"
        app:layout_constraintLeft_toRightOf="@+id/tv_title"
        app:layout_constraintTop_toTopOf="@+id/tv_title" />


    <SeekBar
        android:id="@+id/dialog_seekbar"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginTop="16dp"
        android:max="-30"
        android:min="-200"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/tv_value" />


    <TextView
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginLeft="12dp"
        android:text="越往右调，对设备信号限制越严格"
        android:textColor="#aa808080"
        android:layout_marginTop="12dp"
        android:textSize="13sp"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/dialog_seekbar" />

</androidx.constraintlayout.widget.ConstraintLayout>