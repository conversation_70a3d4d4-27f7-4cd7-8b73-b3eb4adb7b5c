<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent">


    <LinearLayout
        android:id="@+id/ll_device_content"
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:animateLayoutChanges="true"
        android:orientation="vertical"
        app:layout_constraintBottom_toTopOf="@+id/ll_bottom"
        app:layout_constraintTop_toTopOf="parent">

        <LinearLayout
            android:id="@+id/layout_setting"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:background="#eeeeee"
            android:orientation="vertical"
            android:padding="20dp">

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_gravity="center_horizontal"
                android:layout_marginBottom="10dp"
                android:focusable="true"
                android:focusableInTouchMode="true"
                android:text="@string/scan_setting"
                android:textColor="@android:color/black"
                android:textSize="12sp" />

            <EditText
                android:id="@+id/et_name"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:hint="@string/setting_name"
                android:paddingLeft="5dp"
                android:paddingRight="5dp"
                android:textSize="13sp" />

            <EditText
                android:id="@+id/et_mac"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:hint="@string/setting_mac"
                android:paddingLeft="5dp"
                android:paddingRight="5dp"
                android:textSize="13sp" />

            <EditText
                android:id="@+id/et_uuid"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:hint="@string/setting_uuid"
                android:paddingLeft="5dp"
                android:paddingRight="5dp"
                android:textSize="13sp" />


        </LinearLayout>

        <TextView
            android:id="@+id/txt_setting"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_gravity="center_horizontal"
            android:background="#eeeeee"
            android:visibility="gone"
            android:padding="10dp"
            android:text="@string/expand_search_settings" />

        <RelativeLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="horizontal"
            android:padding="10dp"
            android:visibility="gone">

            <Button
                android:id="@+id/btn_scan"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_centerInParent="true"
                android:text="@string/start_scan" />

            <ImageView
                android:id="@+id/img_loading"
                android:layout_width="30dp"
                android:layout_height="30dp"
                android:layout_alignParentRight="true"
                android:layout_centerVertical="true"
                android:layout_marginRight="10dp"
                android:src="@mipmap/ic_loading"
                android:visibility="invisible" />

        </RelativeLayout>

        <ListView
            android:id="@+id/list_device"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="10dp"
            android:divider="@android:color/darker_gray"
            android:dividerHeight="0.5dp"
            android:paddingStart="5dp"
            android:paddingEnd="5dp"
            android:scrollbars="none" />


    </LinearLayout>

    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/ll_bottom"
        android:layout_width="match_parent"
        android:layout_height="100dp"
        android:background="#FFDDEEFF"
        app:layout_constraintBottom_toBottomOf="parent">


        <androidx.constraintlayout.widget.ConstraintLayout
            android:id="@+id/ll_bottom_content"
            android:layout_width="match_parent"
            android:visibility="gone"
            android:layout_height="match_parent"
            android:background="#FFDDEEFF">

            <TextView
                android:id="@+id/tv_tip_title"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginTop="16dp"
                android:textSize="30sp"
                android:textStyle="bold"
                app:layout_constraintLeft_toLeftOf="parent"
                app:layout_constraintRight_toRightOf="parent"
                app:layout_constraintTop_toTopOf="parent"
                tools:text="请点击任意一只耳机" />

            <TextView
                android:id="@+id/tv_tip_content"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:textSize="13sp"
                app:layout_constraintLeft_toLeftOf="parent"
                app:layout_constraintRight_toRightOf="parent"
                app:layout_constraintTop_toBottomOf="@+id/tv_tip_title"
                tools:text="请点击任意一只耳机" />


            <ImageButton
                android:id="@+id/btn_question"
                android:layout_width="24dp"
                android:layout_height="24dp"
                android:layout_marginTop="8dp"
                android:layout_marginRight="8dp"
                android:background="@mipmap/question"
                app:layout_constraintRight_toRightOf="parent"
                app:layout_constraintTop_toTopOf="parent" />


        </androidx.constraintlayout.widget.ConstraintLayout>


        <Button
            android:id="@+id/btn_start_test"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="测试录音、播放功能"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintRight_toRightOf="parent"
            app:layout_constraintTop_toTopOf="parent" />



        <TextView
            android:id="@+id/tv_update_time"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="16dp"
            android:textSize="12sp"
            android:text="2024/05/15  V1.2"
            android:layout_marginRight="8dp"
            android:layout_marginBottom="4dp"
            app:layout_constraintRight_toRightOf="parent"
            app:layout_constraintBottom_toBottomOf="parent" />


    </androidx.constraintlayout.widget.ConstraintLayout>

</androidx.constraintlayout.widget.ConstraintLayout>


