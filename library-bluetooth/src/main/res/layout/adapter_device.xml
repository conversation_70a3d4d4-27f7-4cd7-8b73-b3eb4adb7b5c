<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:orientation="vertical">

    <RelativeLayout
        android:layout_width="match_parent"
        android:layout_height="56dp">

        <TextView
            android:id="@+id/img_blue"
            android:layout_width="30dp"
            android:layout_height="wrap_content"
            android:layout_centerVertical="true"
            android:layout_marginLeft="4dp"
            android:src="@mipmap/ic_blue_remote"
            android:text="用户固件"
            android:textColor="@android:color/black"
            android:textSize="13sp" />

        <TextView
            android:layout_width="0.5dp"
            android:layout_marginLeft="4dp"
            android:layout_marginVertical="10dp"
            android:layout_toRightOf="@+id/img_blue"
            android:layout_height="match_parent"
            android:background="#808080" />

        <LinearLayout
            android:layout_width="wrap_content"
            android:layout_height="match_parent"
            android:layout_marginLeft="10dp"
            android:layout_toRightOf="@id/img_blue"
            android:gravity="center_vertical"
            android:orientation="vertical">

            <TextView
                android:id="@+id/txt_name"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginBottom="2dp"
                android:textSize="14sp" />

            <TextView
                android:id="@+id/txt_mac"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginTop="2dp"
                android:textSize="12sp" />

        </LinearLayout>

        <LinearLayout
            android:id="@+id/layout_idle"
            android:layout_width="wrap_content"
            android:layout_height="match_parent"
            android:layout_alignParentRight="true"
            android:gravity="center"
            android:orientation="horizontal"
            android:visibility="gone">

            <TextView
                android:id="@+id/txt_rssi"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:textSize="14sp" />

            <ImageView
                android:layout_width="18dp"
                android:layout_height="18dp"
                android:layout_marginLeft="5dp"
                android:src="@mipmap/ic_rssi" />

            <Button
                android:id="@+id/btn_connect"
                android:layout_width="50dp"
                android:layout_height="wrap_content"
                android:layout_marginLeft="5dp"
                android:text="@string/connect"
                android:textSize="12sp" />

        </LinearLayout>

        <LinearLayout
            android:id="@+id/layout_connected"
            android:layout_width="wrap_content"
            android:layout_height="match_parent"
            android:layout_alignParentRight="true"
            android:gravity="center"
            android:orientation="horizontal">

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="@string/connected"
                android:textColor="@android:color/black"
                android:textSize="14sp" />

            <Button
                android:id="@+id/btn_disconnect"
                android:layout_width="50dp"
                android:layout_height="wrap_content"
                android:layout_marginLeft="5dp"
                android:text="@string/disconnect"
                android:textSize="12sp" />

            <Button
                android:id="@+id/btn_detail"
                android:layout_width="50dp"
                android:layout_height="wrap_content"
                android:layout_marginLeft="5dp"
                android:text="@string/enter"
                android:textSize="12sp" />

        </LinearLayout>


    </RelativeLayout>


</LinearLayout>