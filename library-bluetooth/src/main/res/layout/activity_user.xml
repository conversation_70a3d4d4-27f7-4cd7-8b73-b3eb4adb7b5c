<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:orientation="vertical">

    <androidx.appcompat.widget.Toolbar
        android:id="@+id/toolbar"
        android:layout_width="match_parent"
        android:layout_height="?attr/actionBarSize"
        android:background="?attr/colorPrimary"
        android:elevation="4dp"
        android:theme="@style/ThemeOverlay.AppCompat.ActionBar" />

<!--    <androidx.appcompat.widget.Toolbar-->
<!--        android:id="@+id/toolbar"-->
<!--        android:layout_width="match_parent"-->
<!--        android:layout_height="?attr/actionBarSize"-->
<!--        android:background="?attr/colorPrimary"-->
<!--        android:title="@string/app_name"-->
<!--        app:titleTextColor="@android:color/white" />-->

</LinearLayout>

