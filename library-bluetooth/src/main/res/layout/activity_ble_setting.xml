<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:animateLayoutChanges="true"
    android:orientation="vertical">

    <!--    <androidx.appcompat.widget.Toolbar-->
    <!--        app:layout_constraintTop_toTopOf="parent"-->
    <!--        app:layout_constraintLeft_toLeftOf="parent"-->
    <!--        app:layout_constraintRight_toRightOf="parent"-->
    <!--        android:id="@+id/toolbar"-->
    <!--        android:layout_width="match_parent"-->
    <!--        android:layout_height="40dp"-->
    <!--        app:titleMarginStart="16dp"-->
    <!--        app:popupTheme="@style/ThemeOverlay.AppCompat.Light"-->
    <!--        android:theme="@style/ThemeOverlay.AppCompat.Dark.ActionBar"-->
    <!--        app:title="扫描设置"-->
    <!--        app:titleTextColor="@android:color/black" />-->

    <FrameLayout
        android:id="@+id/preferences_container"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        app:layout_constraintTop_toTopOf="parent" />

</androidx.constraintlayout.widget.ConstraintLayout>

