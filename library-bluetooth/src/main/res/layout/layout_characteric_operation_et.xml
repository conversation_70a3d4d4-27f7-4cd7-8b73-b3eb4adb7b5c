<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:orientation="vertical">

    <RelativeLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content">

        <Button
            android:id="@+id/btn"
            android:layout_width="70dp"
            android:layout_height="wrap_content"
            android:layout_alignParentRight="true"
            android:layout_centerVertical="true"
            android:layout_marginLeft="10dp"
            android:gravity="center"
            android:textSize="15sp" />

        <EditText
            android:id="@+id/et"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_centerVertical="true"
            android:layout_toLeftOf="@id/btn"
            android:digits="0123456789ABCDEFabcdef"
            android:gravity="center_vertical"
            android:hint="@string/input_hex"
            android:inputType="text"
            android:textSize="15sp" />

    </RelativeLayout>

</LinearLayout>