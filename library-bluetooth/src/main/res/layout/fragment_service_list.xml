<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:orientation="vertical">


    <TextView
        android:id="@+id/txt_mac"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="4dp"
        android:paddingEnd="10dp"
        android:paddingStart="10dp"
        android:textStyle="bold"
        android:textColor="#ffffff"
        android:textSize="11sp" />

    <TextView
        android:id="@+id/txt_name"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="8dp"
        android:paddingEnd="10dp"
        android:paddingStart="10dp"
        android:textSize="10sp"
        tools:ignore="SmallSp" />


    <Switch
        android:id="@+id/switch_list"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:paddingEnd="10dp"
        android:paddingStart="10dp"
        android:textSize="12sp"
        android:visibility="visible"
        android:text="切换日志/指令视图" />

    <ListView
        android:id="@+id/list_service"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginTop="6dp"
        android:divider="@android:color/darker_gray"
        android:dividerHeight="0.5dp"
        android:paddingStart="10dp"
        android:paddingEnd="10dp"
        android:visibility="visible"
        android:scrollbars="none" />

    <ListView
        android:id="@+id/list_msg"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginTop="10dp"
        android:divider="@android:color/darker_gray"
        android:dividerHeight="0.5dp"
        android:visibility="gone"
        android:scrollbars="none" />

</LinearLayout>