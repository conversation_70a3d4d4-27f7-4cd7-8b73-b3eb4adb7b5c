plugins {
    id 'com.android.library'
}

android {
    namespace 'co.timekettle.speech'
    compileSdk 31

    defaultConfig {
        minSdk 24
        targetSdk 31

        testInstrumentationRunner "androidx.test.runner.AndroidJUnitRunner"
        consumerProguardFiles "consumer-rules.pro"

        externalNativeBuild {
            cmake {
                cppFlags '-std=c++11 -fexceptions'
            }
        }

        ndk {
            // 设置支持的SO库架构
            abiFilters 'armeabi-v7a', 'arm64-v8a'
        }
    }

    buildTypes {
        release {
            minifyEnabled false
            proguardFiles getDefaultProguardFile('proguard-android-optimize.txt'), 'proguard-rules.pro'
        }
    }
    compileOptions {
        sourceCompatibility JavaVersion.VERSION_1_8
        targetCompatibility JavaVersion.VERSION_1_8
    }

    lintOptions {
        abortOnError false
    }

    externalNativeBuild {
        cmake {
            path "src/main/cpp/CMakeLists.txt"
            version "3.10.2"
        }
    }
    aaptOptions {
        // 打包(release)忽略掉 assets下 audio_samples 目录，只会排除
        // assets 下的资源，res下的资源不会排除
//        ignoreAssets 'audio_samples'
        // 打包(release)忽略掉 png 后缀的图像，只会排除 res 下的资源，assets下的资源不会排除
        boolean result = false
        project.gradle.startParameter.taskNames.forEach {
            print("gradle task = " + it + "\n")
            if (it.contains("Release") || it.contains("release")) {
                result = true
            }
        }
        print("release = " + result + "\n")
        if (result) {
            ignoreAssetsPattern '!audio_samples:!model:!asr_samples'
        }
    }
}

dependencies {
    implementation fileTree(include: ['*.jar', '*.aar'], dir: 'libs')
    implementation fileTree(include: ['*.jar', '*.aar'], dir: 'src/main/jniLibs')
    implementation 'com.timekettle.aar:xzy-aikit:1.0'
    implementation 'net.java.dev.jna:jna:5.14.0'
    implementation 'io.netty:netty-all:4.1.36.Final'
    implementation 'com.google.code.gson:gson:2.9.0'
    implementation 'io.github.jeremyliao:live-event-bus-x:1.8.0'
    implementation 'org.greenrobot:eventbus:3.3.1'
    //    implementation 'org.tensorflow:tensorflow-lite:2.3.0'
//        implementation 'org.tensorflow:tensorflow-lite:2.11.0'
    implementation 'org.tensorflow:tensorflow-lite:2.14.0'
    implementation 'androidx.appcompat:appcompat:1.4.1'
    implementation 'com.google.android.material:material:1.6.0'
    testImplementation 'junit:junit:4.13.2'
    androidTestImplementation 'androidx.test.ext:junit:1.1.3'
    androidTestImplementation 'androidx.test.espresso:espresso-core:3.4.0'
}