package co.timekettle.speech;

import org.junit.Test;

import co.timekettle.speech.utils.VolumeUtil;

/**
 * Example local unit test, which will execute on the development machine (host).
 *
 * @see <a href="http://d.android.com/tools/testing">Testing documentation</a>
 */
public class ExampleUnitTest {
    @Test
    public void calculateDb() {
        short[] pcmData = {0x7FFF/15}; // 示例 PCM 数据
        double rms = VolumeUtil.calculateRMS(pcmData);
        double db = VolumeUtil.convertToDB(rms);
        System.out.println("PCM 数据的分贝值：" + db);
    }
}