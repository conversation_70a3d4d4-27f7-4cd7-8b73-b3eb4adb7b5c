<?xml version="1.0" encoding="utf-8"?>
<manifest xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    package="co.timekettle.speech">

    <uses-permission android:name="android.permission.READ_PRIVILEGED_PHONE_STATE"
        tools:ignore="ProtectedPermissions" />
    <uses-permission android:name="android.permission.RECORD_AUDIO" />
    <uses-permission android:name="android.permission.INTERNET" />

    <!-- 申请权限 MODIFY_AUDIO_SETTINGS setSpeakerphoneOn 才有效果 -->
    <uses-permission android:name="android.permission.MODIFY_AUDIO_SETTINGS" />
    <uses-permission android:name="android.permission.READ_PHONE_STATE" />

    <application android:networkSecurityConfig="@xml/network_security_config">
    <activity android:name="co.timekettle.example.TranslateExampleActivity"  android:theme="@style/Theme.AppCompat.Light.NoActionBar"/>
        <activity android:name="co.timekettle.example.TmkEngineTestActivity"  android:theme="@style/Theme.AppCompat.Light.NoActionBar"/>
    </application>
</manifest>