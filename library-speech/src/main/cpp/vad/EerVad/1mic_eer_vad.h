#ifndef _VAD_H_
#define _VAD_H_

#include "1mic_eer_def.h"
//
//typedef struct _wav_head_ {
//  char  cRiff[4];       /* Chunk id: "RIFF" */
//  int   iRiffSize;      /* chunk size */
//  char  vWave[4];       /* WAVE id: "WAVE" */
//  char  cFmt[4];        /* Chunk id: "fmt " */
//  int   iPcm;            /* Chunk size: 18 */
//  short nFormatTag;   /* format code */
//  short nChannel;      /* Number of interleaved channels */
//  int   iRate;           /* Sampling rate (blocks per second) */
//  int   iByteps;         /* Data rate: avg bytes per sec */
//  short iBytepe;       /* Data block size (bytes) */
//  short nQuent;        /* Bits per sample */
//  char  cData[4];       /* chunk id: "data" */
//  int   iDataSize;       /* chunk size: length of data */
//} ISpeech_stWavHead;

#ifdef __cplusplus    
extern "C" {          
#endif

  /************************************************************************/
  /* FUN                                                                  */
  /*    参数设置                                                          */
  /* IN:                                                                  */
  /*    p_stEerState: 对象结构体变量                                      */
  /* OUT:                                                                 */
  /*    p_stEerState: 对象结构体变量                                      */
  /* RTN:                                                                 */
  /*   -1 -- 不成功 (EER_RTMSG_KO)                                        */
  /*    1 -- 成功   (EER_RTMSG_OK)                                        */
  /************************************************************************/
  int ISpeech_InitEerVad(void **p_stEerState);


  /************************************************************************/
  /* FUN                                                                  */
  /*    参数设置                                                          */
  /* IN:                                                                  */
  /*    p_fVadOnThd  VAD激活状态的阈值                                    */
  /*    p_fVadLkThd  VAD疑似状态的阈值                                    */
  /* OUT:                                                                 */
  /*    NULL                                                              */
  /* RTN:                                                                 */
  /*   -1 -- 不成功 (EER_RTMSG_KO)                                        */
  /*    1 -- 成功   (EER_RTMSG_OK)                                        */
  /************************************************************************/
  int ISpeech_SetEerVad(void *p_stEerState, float p_fVadOnThd, float p_fVadLkThd);

  /************************************************************************/
  /* FUN                                                                  */
  /*    处理一帧数据的VAD状态                                             */
  /* IN:                                                                  */
  /*    p_nWavData  需要处理的音频数据(暂时固定长度为256short)            */
  /*    p_iWavSize  需要处理的音频数据长度（暂时固定为256）               */
  /* OUT:                                                                 */
  /*    p_iVadFlag  Vad标志位 0--静音 1--有效语音起始 2--有效语音结束     */
  /*    p_fVadProb  当前帧VAD的概率值                                     */
  /* RTN:                                                                 */
  /*   -1 -- 不成功 (EER_RTMSG_KO)                                        */
  /*    1 -- 成功   (EER_RTMSG_OK)                                        */
  /************************************************************************/
  int ISpeech_DealEerVad(void *p_stEerState, int *p_iVadFlag, float *p_fVadProb, short* p_nWavData, int p_iWavSize);

  /************************************************************************/
  /* FUN                                                                  */
  /*    资源释放                                                          */
  /* IN:                                                                  */
  /*    p_stEerState: 对象结构体变量                                      */
  /* OUT:                                                                 */
  /*    NULL                                                              */
  /* RTN:                                                                 */
  /*   -1 -- 不成功 (EER_RTMSG_KO)                                        */
  /*    1 -- 成功   (EER_RTMSG_OK)                                        */
  /************************************************************************/
  int ISpeech_FreeEerVad(void *p_stEerState);

#ifdef __cplusplus
}
#endif

void ISpeech_Eer_FFT(ISpeech_EerComplex *data, int order);	//FFT变换


#endif
