#ifndef _DEFINE_H_
#define _DEFINE_H_

#include <stdlib.h>
#include <stdio.h>
#include <math.h>

#define EER_RTMSG_OK 1
#define EER_RTMSG_KO -1

#define EER_VAD_SIL        0
#define EER_VAD_ON         1
#define EER_VAD_END        2

#ifndef M_PI 
#define M_PI 3.14159265358979323846
#endif

#define EER_SAMPLERATE   16000

#define EER_VAD_FFTORDER     9
#define EER_VAD_FRAMESIZE   (1<<EER_VAD_FFTORDER)
#define EER_VAD_FRAMEOVER   (EER_VAD_FRAMESIZE >> 1)
#define EER_VAD_FRAMEBUFF    125
#define EER_VAD_DELAYNUM     2


#define EER_MAX_VALUE (-1e10)
#define EER_MIN_VALUE (1e10)

typedef struct _err_complex_
{
  float m_dlReal;
  float m_dlImag;
}ISpeech_EerComplex;

typedef struct _eer_state_
{
  float   g_fEerData[EER_VAD_FRAMESIZE];
  float   g_fEerSpec[EER_VAD_FRAMEOVER + 1];
  ISpeech_EerComplex g_stEerComplex[EER_VAD_FRAMESIZE];
  //
  float   g_fEerVadOnThd;
  float   g_fEerVadLkThd;
  float   g_fPreEerRatio[2];
}ISpeech_EerState;

#endif
