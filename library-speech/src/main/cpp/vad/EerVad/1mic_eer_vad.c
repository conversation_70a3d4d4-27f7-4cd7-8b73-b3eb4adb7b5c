#include "1mic_eer_vad.h"
#include "1mic_eer_def.h"
#include "1mic_eer_par.h"
#include <string.h>

int ISpeech_InitEerVad(void **p_stEerState)
{
  ISpeech_EerState *stEerState;
  stEerState = (ISpeech_EerState *)malloc(sizeof(ISpeech_EerState));

  stEerState->g_fEerVadOnThd = 0.2f;
  stEerState->g_fEerVadLkThd = 0.1f;
  memset(stEerState->g_fEerData, 0, sizeof(float) * EER_VAD_FRAMESIZE);
  memset(stEerState->g_fEerSpec, 0, sizeof(short) * (EER_VAD_FRAMEOVER + 1));
  memset(stEerState->g_stEerComplex, 0, sizeof(ISpeech_EerComplex) * EER_VAD_FRAMESIZE);
  memset(stEerState->g_fPreEerRatio, 0, sizeof(short) * 2);

  *p_stEerState = stEerState;

  return EER_RTMSG_OK;
}

int ISpeech_SetEerVad(void *p_stEerState, float p_fVadOnThd, float p_fVadLkThd)
{
  int iRetCode = EER_RTMSG_OK;
  int iIndex;
  ISpeech_EerState *stEerState = p_stEerState;

  if (p_fVadOnThd > 0.0f && p_fVadOnThd < 1.0f)
  {
    stEerState->g_fEerVadOnThd = p_fVadOnThd;
  }
  if (p_fVadLkThd > 0.0f && p_fVadLkThd < 1.0f)
  {
    stEerState->g_fEerVadLkThd = p_fVadLkThd;
  }
 
__end:
  return iRetCode;
}

int ISpeech_DealEerVad(void *p_stEerState, int *p_iVadFlag, float *p_fVadProb, short* p_nWavData, int p_iWavSize)
{
  int iRetCode = EER_RTMSG_OK;
  int iIndex;
  int jIndex;

  float fEngSum  = 0.0f;
  float fSpecSum = 0.0f;
  float fProbSum = 0.0f;    // H
  float fProb    = 0.0f;
  float fEngEntropyRatio   = 0.0f;

  ISpeech_EerState *stEerState = p_stEerState;

  for(iIndex = 0; iIndex < EER_VAD_FRAMEOVER; iIndex++) 
  {
    stEerState->g_fEerData[iIndex] = stEerState->g_fEerData[iIndex + EER_VAD_FRAMEOVER];
    stEerState->g_fEerData[iIndex + EER_VAD_FRAMEOVER] = p_nWavData[iIndex] / 32767.0;
  }
  for (iIndex = 0; iIndex < EER_VAD_FRAMESIZE; iIndex++)
  {
    stEerState->g_stEerComplex[iIndex].m_dlReal = stEerState->g_fEerData[iIndex] * m_dlEerHann[iIndex];
    stEerState->g_stEerComplex[iIndex].m_dlImag = 0;
  }
  ISpeech_Eer_FFT(stEerState->g_stEerComplex, EER_VAD_FFTORDER);
  //=============================================================

  for (iIndex = 0; iIndex < EER_VAD_FRAMEOVER + 1; iIndex++)
  {
    stEerState->g_fEerSpec[iIndex] = sqrtf(stEerState->g_stEerComplex[iIndex].m_dlReal * stEerState->g_stEerComplex[iIndex].m_dlReal 
                             + stEerState->g_stEerComplex[iIndex].m_dlImag * stEerState->g_stEerComplex[iIndex].m_dlImag);

    fEngSum  += stEerState->g_fEerSpec[iIndex] * stEerState->g_fEerSpec[iIndex];
    fSpecSum += stEerState->g_fEerSpec[iIndex];
  }
  fEngSum = log10f(1.0f + fEngSum / 2.0f);
  for (iIndex = 0; iIndex < EER_VAD_FRAMEOVER + 1; iIndex++)
  {
    fProb = stEerState->g_fEerSpec[iIndex] / (fSpecSum + 1e-10);
    fProbSum += fProb * logf(fProb + 1e-10);
  }
  fEngEntropyRatio = sqrtf(1 + fabs(fEngSum / -(fProbSum + 1e-10)));

  if (fEngEntropyRatio > 2.0f)
  {
    fEngEntropyRatio = 1.0f + stEerState->g_fEerVadLkThd / 10.0f;
  }

  if (fEngEntropyRatio > 1 + stEerState->g_fEerVadOnThd)
  {
    *p_iVadFlag = EER_VAD_ON;
  }
  else
  {
    if (stEerState->g_fPreEerRatio[0] + stEerState->g_fPreEerRatio[1] + fEngEntropyRatio > 3 * (1 + stEerState->g_fEerVadLkThd))
    {
      *p_iVadFlag = EER_VAD_ON;
    } 
    else
    {
      *p_iVadFlag = EER_VAD_SIL;
    }
  }

  stEerState->g_fPreEerRatio[1] = stEerState->g_fPreEerRatio[0];
  stEerState->g_fPreEerRatio[0] = fEngEntropyRatio;
  *p_fVadProb    = fEngEntropyRatio -1.0f;

__end:
  return iRetCode;
}


int ISpeech_FreeEerVad(void *p_stEerState)
{

  ISpeech_EerState *stEerState = p_stEerState;
  if (stEerState)
  {
    free(stEerState);
    stEerState == NULL;
  }

  return 1;
}

void ISpeech_Eer_FFT(ISpeech_EerComplex *stComplex, int p_iOrder)
{
  int n , i , nv2 , j , k , le , l , le1 , ip , nm1 ;
  ISpeech_EerComplex t , u , w ;

  n = 1;
  for(i=0; i<p_iOrder; i++)
    n = n*2 ;

  nv2 = n / 2 ;
  nm1 = n - 1 ;
  j = 1 ;

  for (i = 1 ; i <= nm1 ; i ++)
  {
    if (i < j)
    {
      t.m_dlReal = stComplex[i - 1].m_dlReal ;
      t.m_dlImag = stComplex[i - 1].m_dlImag ;
      stComplex[i - 1].m_dlReal = stComplex[j - 1].m_dlReal ;
      stComplex[i - 1].m_dlImag = stComplex[j - 1].m_dlImag ;
      stComplex[j - 1].m_dlReal = t.m_dlReal ;
      stComplex[j - 1].m_dlImag = t.m_dlImag ;
    }

    k = nv2 ;

    while (k < j)
    {
      j -= k ;
      k /= 2 ;
    }
    j += k ;
  }

  le = 1 ;
  for (l= 1 ; l <= p_iOrder ; l ++)
  {
    le *= 2 ;
    le1 = le / 2 ;
    u.m_dlReal = 1 ;
    u.m_dlImag = 0 ;
    w.m_dlReal = (cos(M_PI / le1)) ;
    w.m_dlImag =(-sin(M_PI / le1)) ;

    for (j = 1 ; j <= le1 ; j ++)
    {
      for (i = j ; i <= n ; i += le)
      {
        ip = i + le1 ;
        t.m_dlReal = (stComplex[ip - 1].m_dlReal * u.m_dlReal - stComplex[ip - 1].m_dlImag * u.m_dlImag );
        t.m_dlImag = (stComplex[ip - 1].m_dlReal * u.m_dlImag + stComplex[ip - 1].m_dlImag * u.m_dlReal );
        stComplex[ip - 1].m_dlReal = stComplex[i - 1].m_dlReal - t.m_dlReal ;
        stComplex[ip - 1].m_dlImag = stComplex[i - 1].m_dlImag - t.m_dlImag ;
        stComplex[i - 1].m_dlReal = t.m_dlReal + stComplex[i - 1].m_dlReal ;
        stComplex[i - 1].m_dlImag = t.m_dlImag + stComplex[i - 1].m_dlImag ;
      }

      t.m_dlReal = u.m_dlReal * w.m_dlReal - u.m_dlImag * w.m_dlImag ;
      t.m_dlImag = u.m_dlImag * w.m_dlReal + u.m_dlReal * w.m_dlImag ;
      u.m_dlReal = t.m_dlReal;
      u.m_dlImag = t.m_dlImag ;
    }
  }
}
