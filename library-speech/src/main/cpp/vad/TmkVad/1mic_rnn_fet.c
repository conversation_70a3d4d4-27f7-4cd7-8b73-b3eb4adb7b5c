#include "1mic_rnn_fet.h"
#include "1mic_rnn_par.h"
#include "1mic_rnn_mas.h"

//FetState    g_stFetState;
//Complex     g_stComplex[RNN_FRAME_SIZE]    = {0};
//float       g_fTargtBuff[RNN_FRAME_OVER]   = {0};

short eband5ms[] = 
{
  0,  1,  2,  3,  4,  5,  6,  7,  8, 10, 12, 14, 16, 20, 24, 28, 34, 40, 48, 60, 78, 100
};

int Tmk_InitFeature(FetState *p_stFetState)
{
  int iRetCode = RTMSG_OK;
  int iIndex;
  int jIndex;
  float dlTmp;

  memset(p_stFetState->m_stComplex, 0, sizeof(Complex) * RNN_FRAME_SIZE);

  p_stFetState->m_iInitFlag = 0;
  p_stFetState->m_iCepIdx   = 0;

  for (iIndex = 0; iIndex < RNN_FRAME_OVER; iIndex++)
  {
    dlTmp = sin(0.5f * M_PI * (iIndex + 0.5f) / RNN_FRAME_OVER);
    p_stFetState->m_fHanning[iIndex] = sin(0.5f * M_PI * dlTmp * dlTmp);
    p_stFetState->m_fHanning[RNN_FRAME_SIZE - iIndex - 1] = p_stFetState->m_fHanning[iIndex];
  }
  //printf("g_stCommonState.m_fHanning=%f %f %f %f %f %f \r\n", g_stCommonState.m_fHanning[0], g_stCommonState.m_fHanning[4], g_stCommonState.m_fHanning[16], g_stCommonState.m_fHanning[64], g_stCommonState.m_fHanning[128], g_stCommonState.m_fHanning[RNN_FRAME_OVER-1]);


  for (iIndex = 0; iIndex < RNN_NB_BANDS; iIndex++)
  {
    for (jIndex = 0; jIndex < RNN_NB_BANDS; jIndex++)
    {
      p_stFetState->m_fDctTable[iIndex * RNN_NB_BANDS + jIndex] = cos((iIndex + 0.5f) * jIndex * M_PI / RNN_NB_BANDS);
    }
    p_stFetState->m_fDctTable[iIndex * RNN_NB_BANDS] *= sqrtf(0.5f);
  }
  //printf("g_stCommonState.m_fDctTable=%f %f %f %f %f %f \r\n", g_stCommonState.m_fDctTable[0], g_stCommonState.m_fDctTable[4], g_stCommonState.m_fDctTable[16], g_stCommonState.m_fDctTable[64], g_stCommonState.m_fDctTable[128], g_stCommonState.m_fDctTable[RNN_FRAME_OVER-1]);


  p_stFetState->m_iInitFlag = 1;
__end:
  return iRetCode;
}

int Tmk_CalcFeature(FetState *p_stFetState, short *p_nEngFlag, float *p_fFetData, float *p_fSrcData)
{
  int iRetCode = RTMSG_OK;
  int iIndex;
  int jIndex;
  int kIndex;
  //
  float fTmp = 0.0f;
  float fFrac = 0.0f;
  float fReal;
  float fImag;
  short nBandSize = 0;
  float fSumEng   = 0.0f;
  float fBandEx[RNN_NB_BANDS] = {0};

  //
  float fMaxLog = -2.0f;
  float fFollow = -2.0f;
  float fLy[RNN_NB_BANDS] = {0};
  float *ceps_0, *ceps_1, *ceps_2;
  float fMinSpec = 0.0f;
  float fTmpSpec = 0.0f;
  float fVarSpec = 0.0f;

  if (p_stFetState->m_iInitFlag != 1)
  {
    printf("Not Init Yeat!\r\n");
    iRetCode = RTMSG_OK;
    goto __end;
  }
  *p_nEngFlag = 1;
  //apply_window + forward_transform
  for (iIndex = 0; iIndex < RNN_FRAME_SIZE; iIndex++)
  {
    p_stFetState->m_stComplex[iIndex].m_dlReal = p_fSrcData[iIndex] * p_stFetState->m_fHanning[iIndex] / RNN_FRAME_SIZE;
    p_stFetState->m_stComplex[iIndex].m_dlImag = 0;
  }
  //printf("%f %f %f %f %f %f \r\n", g_stComplex[0].m_dlReal, g_stComplex[4].m_dlReal, g_stComplex[16].m_dlReal, g_stComplex[64].m_dlReal, g_stComplex[128].m_dlReal, g_stComplex[RNN_FRAME_OVER-1].m_dlReal);
  Tmk_FFT(p_stFetState->m_stComplex, RNN_FRAME_ORDER);
  //printf("%f %f %f %f %f %f \r\n", g_stComplex[0].m_dlReal, g_stComplex[4].m_dlReal, g_stComplex[16].m_dlReal, g_stComplex[64].m_dlReal, g_stComplex[128].m_dlReal, g_stComplex[RNN_FRAME_OVER-1].m_dlReal);


  //compute_band_energy
  for (iIndex = 0; iIndex < RNN_NB_BANDS - 1; iIndex++)
  {
    nBandSize = (eband5ms[iIndex + 1] - eband5ms[iIndex]) << RNN_FRAME_SIZE_SHIFT;
    for (jIndex = 0; jIndex < nBandSize; jIndex++)
    {
      fFrac = jIndex * 1.0f;
      fFrac /=  nBandSize;
      fReal = p_stFetState->m_stComplex[(eband5ms[iIndex] << RNN_FRAME_SIZE_SHIFT) + jIndex].m_dlReal;
      fImag = p_stFetState->m_stComplex[(eband5ms[iIndex] << RNN_FRAME_SIZE_SHIFT) + jIndex].m_dlImag;
      fTmp = fReal * fReal + fImag * fImag;

      fBandEx[iIndex] += (1.0f - fFrac) * fTmp;
      fBandEx[iIndex + 1] += fFrac * fTmp;
    }
  }
  fBandEx[0] *= 2;
  fBandEx[RNN_NB_BANDS - 1] *= 2;
  //printf("fBandEx=%f %f %f %f %f %f \r\n", fBandEx[0], fBandEx[2], fBandEx[4], fBandEx[8], fBandEx[16], fBandEx[RNN_NB_BANDS-1]);


  //=========================
  for (iIndex = 0; iIndex < RNN_NB_BANDS; iIndex++)
  {
    fLy[iIndex] = log10(1e-2 + fBandEx[iIndex]);
    fLy[iIndex] = MAX16(fMaxLog - 7.0f, MAX16(fFollow - 1.5f, fLy[iIndex]));
    fMaxLog = MAX16(fMaxLog, fLy[iIndex]);
    fFollow = MAX16(fFollow - 1.5f, fLy[iIndex]);
    fSumEng += fBandEx[iIndex];
  }
  if(fSumEng < 0.04f)
  {
    *p_nEngFlag = 0;
    memset(p_fFetData, 0, sizeof(float) * RNN_NB_FEATURES);
    iRetCode = RTMSG_KO;
    goto __end;
  }

  //DCT
  for (iIndex = 0; iIndex < RNN_NB_BANDS; iIndex++)
  {
    fTmp = 0.0f;
    for (jIndex = 0; jIndex < RNN_NB_BANDS; jIndex++)
    {
      fTmp += fLy[jIndex] * p_stFetState->m_fDctTable[jIndex * RNN_NB_BANDS + iIndex];
    }
    p_fFetData[iIndex] = fTmp * SQRT_2_22;
  }
  //printf("p_fFetData=%f %f %f %f %f %f \r\n", p_fFetData[0], p_fFetData[2], p_fFetData[4], p_fFetData[8], p_fFetData[16], p_fFetData[RNN_NB_BANDS-1]);
  p_fFetData[0] -= 12.0f;
  p_fFetData[1] -= 4.0f;

  //for (iIndex = 0; iIndex < 10; iIndex++)
  //{
  //  printf("%f - ", p_fFetData[iIndex]);
  //}
  //printf("%f %f %f %f %f %f \r\n", p_fFetData[0], p_fFetData[4], p_fFetData[16], p_fFetData[32], p_fFetData[48], p_fFetData[RNN_NB_FEATURES-1]);

  //
  ceps_0 = p_stFetState->m_fCepBuff[p_stFetState->m_iCepIdx];
  ceps_1 = (p_stFetState->m_iCepIdx < 1) ? p_stFetState->m_fCepBuff[RNN_CEPS_MEM + p_stFetState->m_iCepIdx - 1] : p_stFetState->m_fCepBuff[p_stFetState->m_iCepIdx - 1];
  ceps_2 = (p_stFetState->m_iCepIdx < 2) ? p_stFetState->m_fCepBuff[RNN_CEPS_MEM + p_stFetState->m_iCepIdx - 2] : p_stFetState->m_fCepBuff[p_stFetState->m_iCepIdx - 2];
  for (iIndex = 0; iIndex < RNN_NB_BANDS; iIndex++) 
    ceps_0[iIndex] = p_fFetData[iIndex];
  p_stFetState->m_iCepIdx = (p_stFetState->m_iCepIdx + 1) % RNN_CEPS_MEM;
  for (iIndex = 0; iIndex < RNN_NB_DELTA_CEPS; iIndex++) 
  {
    p_fFetData[iIndex] = ceps_0[iIndex] + ceps_1[iIndex] + ceps_2[iIndex];
    p_fFetData[RNN_NB_BANDS + iIndex] = ceps_0[iIndex] - ceps_2[iIndex];
    p_fFetData[RNN_NB_BANDS + RNN_NB_DELTA_CEPS+ iIndex] = ceps_0[iIndex] - 2*ceps_1[iIndex] + ceps_2[iIndex];
  }
  /* Spectral variability features. */
  for (iIndex = 0; iIndex < RNN_CEPS_MEM; iIndex++)
  {
    fMinSpec = 1e15f;
    for (jIndex = 0; jIndex < RNN_CEPS_MEM; jIndex++)
    {
      fTmpSpec = 0;
      for (kIndex = 0; kIndex < RNN_NB_BANDS; kIndex++)
      {
        fTmp = p_stFetState->m_fCepBuff[iIndex][kIndex] - p_stFetState->m_fCepBuff[jIndex][kIndex];
        fTmpSpec += fTmp * fTmp;
      }
      if (jIndex != iIndex)
        fMinSpec = MIN32(fMinSpec, fTmpSpec);
    }
    fVarSpec += fMinSpec;
  }
  p_fFetData[RNN_NB_BANDS + 2*RNN_NB_DELTA_CEPS] = fVarSpec / RNN_CEPS_MEM - 2.1f;

  //printf("features=%f %f %f %f %f %f %f \r\n", p_fFetData[0], p_fFetData[4], p_fFetData[8], p_fFetData[16], p_fFetData[24], p_fFetData[RNN_NB_BANDS], p_fFetData[RNN_NB_FEATURES - 1]);


__end:
  return iRetCode;
}


int Tmk_FreeFeature()
{
  int iRetCode = RTMSG_OK;

__end:
  return iRetCode;
}
