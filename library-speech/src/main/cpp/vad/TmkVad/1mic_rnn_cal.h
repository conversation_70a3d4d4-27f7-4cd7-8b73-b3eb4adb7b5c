#ifndef _1MIC_RNN_CAL_H_
#define _1MIC_RNN_CAL_H_

#include "1mic_rnn_def.h"

#define WEIGHTS_SCALE (1.f/256)
#define MAX_NEURONS 128
#define INPUT_SIZE 42

#define celt_isnan(x) ((x)!=(x))

void Tmk_compute_dense(const DenseLayer *layer, float *output, const float *input);

void Tmk_compute_gru(const GRULayer *gru, float *state, const float *input);

void compute_rnn(RNNState *rnn, float *gains, float *vad, const float *input);

/************************************************************************/
/*                                                                      */
/************************************************************************/
int Tmk_InitRnnoise(RNNState *p_stRnnState);

/************************************************************************/
/*                                                                      */
/************************************************************************/
int Tmk_CalcRnnoise(RNNState *p_stRnnState, float *p_fGainData, float *p_fVadProb, float *p_fFetData);

/************************************************************************/
/*                                                                      */
/************************************************************************/
int Tmk_FreeRnnoise();


#endif
