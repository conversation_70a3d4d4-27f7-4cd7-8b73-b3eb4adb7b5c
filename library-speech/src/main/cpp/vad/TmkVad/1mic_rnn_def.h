#ifndef _1MIC_RNN_DEF_H_
#define _1MIC_RNN_DEF_H_
#include <stdlib.h>
#include <string.h>
#include <stdio.h>
#include <math.h>
#include <time.h>

typedef signed char rnn_weight;
typedef short opus_int16;
typedef unsigned short opus_uint16;
typedef int opus_int32;
typedef unsigned int opus_uint32;
typedef float opus_val16;
typedef float opus_val32;
typedef float opus_val64;

#define TMK_RNN_VAD_VERSION "V1.0.0"
//=========================================================
#ifndef RTMSG_OK 
#define RTMSG_OK    1
#endif
#ifndef RTMSG_KO
#define RTMSG_KO    0
#endif
#ifndef M_PI 
#define M_PI 3.14159265358979323846
#endif
#ifndef SQRT_2_22
#define SQRT_2_22          0.30151134458f
#endif

#define VAD_SIL        0
#define VAD_ON         1
#define VAD_END        2

// Code
#define CODELEN         8

typedef struct _complex_
{
  float m_dlReal;
  float m_dlImag;
}Complex;
//=========================================================

//=========================================================
#define RNN_MIN_THD     3e-4
#define RNN_SAMPLE_RATE 16000
#define RNN_FRAME_ORDER 9
#define RNN_FRAME_SIZE (1 << 9)
#define RNN_FRAME_OVER (RNN_FRAME_SIZE >> 1)
#define RNN_FRAME_FREQ (RNN_FRAME_OVER + 1)

#define RNN_FRAME_SIZE_SHIFT 2
#define RNN_NB_BANDS      20
#define RNN_CEPS_MEM      8
#define RNN_NB_DELTA_CEPS 6
#define RNN_NB_FEATURES   (RNN_NB_BANDS+2*RNN_NB_DELTA_CEPS+1)

#define ACTIVATION_TANH    0
#define ACTIVATION_SIGMOID 1
#define ACTIVATION_RELU    2
//========================================================

#define RNN_VAD_GRU_SIZE        24
#define RNN_NOISE_GRU_SIZE      48
#define RNN_DENOISE_DENSE_SIZE  96
#define RNN_DENOISE_OUTPUT_SIZE 20
#define RNN_VAD_OUTPUT_SIZE     1
//=========================================================
typedef struct _dense_layer_
{
  const rnn_weight *bias;
  const rnn_weight *input_weights;
  int nb_inputs;
  int nb_neurons;
  int activation;
} DenseLayer;

typedef struct _gru_layer_
{
  const rnn_weight *bias;
  const rnn_weight *input_weights;
  const rnn_weight *recurrent_weights;
  int nb_inputs;
  int nb_neurons;
  int activation;
} GRULayer;

typedef struct _rnn_model_
{
  DenseLayer *m_stDenoiseInput;
  GRULayer   *m_stVadGru;
  //GRULayer   *m_stNoiseGru;
  //DenseLayer *m_stDenoiseDense;
  //DenseLayer *m_stDenoiseOutput;
  DenseLayer *m_stVadOutput;
}RNNModel;

typedef struct _rnn_state_
{
  RNNModel m_stRnnModel;
  float m_fVadGruState[RNN_VAD_GRU_SIZE];
  float m_fNisGruState[RNN_NOISE_GRU_SIZE];
}RNNState;

typedef struct _vad_state_
{
  float   m_fFrtThs;
  float   m_fEndThs;
  int     m_iFrtNum;
  int     m_iEndNum;
  int     m_iCurrVadStatus;
  int     m_iFrameDuration;

  float   m_iCurrVadProb;
}VADState;

typedef struct _common_state_
{
  Complex m_stComplex[RNN_FRAME_SIZE];

  int     m_iInitFlag;
  float   m_fHanning[RNN_FRAME_SIZE];
  float   m_fDctTable[RNN_NB_BANDS * RNN_NB_BANDS];
  int     m_iCepIdx;
  float   m_fCepBuff[RNN_CEPS_MEM][RNN_NB_BANDS];
} FetState;

typedef struct _model_state_
{
  RNNState m_stRnnState;
  VADState m_stVadState;
  FetState m_stFetState;

  short    m_nFrameEnd;
  short    m_nFrameBuff[RNN_FRAME_SIZE * 3];
  float    m_fFrameData[RNN_FRAME_SIZE];
}ModelState;

//======================================================


#endif