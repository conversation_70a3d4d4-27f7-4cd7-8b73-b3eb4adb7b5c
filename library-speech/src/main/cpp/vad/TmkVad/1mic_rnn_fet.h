#ifndef _1MIC_RNN_FET_H_
#define _1MIC_RNN_FET_H_

#include "1mic_rnn_def.h"


/************************************************************************/
/*                                                                      */
/************************************************************************/
int Tmk_InitFeature(FetState *p_stFetState);

/************************************************************************/
/*                                                                      */
/************************************************************************/
int Tmk_CalcFeature(FetState *p_stFetState, short *p_nEngFlag, float *p_fFetData, float *p_fSrcData);

/************************************************************************/
/*                                                                      */
/************************************************************************/
int Tmk_FreeFeature();

#endif
