#include "1mic_rnn_pro.h"
#include "1mic_rnn_fet.h"
#include "1mic_rnn_cal.h"


int Tmk_SetRnnVadPar(void *p_stVadState, float p_fFrtLen, float p_fFrtThs, float p_fEndLen, float p_fEndThs)
{
  int iRetCode = RTMSG_OK;

  ModelState *stModelState = p_stVadState;

  if (p_fFrtLen > 0)
  {
    stModelState->m_stVadState.m_iFrtNum = (int)(p_fFrtLen *  RNN_SAMPLE_RATE / RNN_FRAME_OVER);
  }
  if (p_fEndLen > 0)
  {
    stModelState->m_stVadState.m_iEndNum   = (int)(p_fEndLen *  RNN_SAMPLE_RATE / RNN_FRAME_OVER);
  }
  if (p_fFrtThs > 0 && p_fFrtThs <= 1)
  {
    stModelState->m_stVadState.m_fFrtThs = p_fFrtThs;
  }
  if (p_fEndThs > 0 && p_fEndThs <= 1)
  {
    stModelState->m_stVadState.m_fEndThs = p_fEndThs;
  }

  stModelState->m_stVadState.m_iCurrVadStatus = VAD_SIL;
  stModelState->m_stVadState.m_iFrameDuration = 0;
  stModelState->m_stVadState.m_iCurrVadProb    = 0.0f;

__end:
  return iRetCode;
}

int Tmk_GetVadFlag(VADState *p_stVadState, float p_fValue)
{
  switch(p_stVadState->m_iCurrVadStatus)
  {
  case VAD_SIL:
    if (p_fValue >= p_stVadState->m_fFrtThs)
    {
      p_stVadState->m_iFrameDuration++;
      if (p_stVadState->m_iFrameDuration >= p_stVadState->m_iFrtNum)
      {
        p_stVadState->m_iCurrVadStatus = VAD_ON;
        p_stVadState->m_iFrameDuration = 0;
      }
    }
    else
      p_stVadState->m_iFrameDuration = 0;
    break;
  case VAD_ON:
    if (p_fValue < p_stVadState->m_fEndThs)
    {
      p_stVadState->m_iFrameDuration++;
      if (p_stVadState->m_iFrameDuration >= p_stVadState->m_iEndNum)
      {
        p_stVadState->m_iCurrVadStatus = VAD_END;
        p_stVadState->m_iFrameDuration = 0;
      }
    }
    else
      p_stVadState->m_iFrameDuration = 0;

    break;
  case VAD_END:
    p_stVadState->m_iCurrVadStatus = VAD_SIL;
    p_stVadState->m_iFrameDuration = 0;
    break;
  }

  return p_stVadState->m_iCurrVadStatus;
}

char* Tmk_GetRnnVadVersion()
{
  return TMK_RNN_VAD_VERSION;
}


int Tmk_InitRnnVadPro(void **p_stModelState)
{
  int   iRetCode = RTMSG_OK;
  float fFrtLen = 0.3f;
  float fFrtThs = 0.95f;
  float fEndLen = 0.3f;
  float fEndThs = 0.85f;


  ModelState *stModelState;
  stModelState = (ModelState *)malloc(sizeof(ModelState));

  *p_stModelState = stModelState;

  stModelState->m_nFrameEnd = 0;
  memset(stModelState->m_fFrameData, 0, sizeof(float) * RNN_FRAME_SIZE);
  memset(stModelState->m_nFrameBuff, 0, sizeof(short) * RNN_FRAME_SIZE * 3);

  if ((iRetCode = Tmk_InitFeature(&stModelState->m_stFetState)) != RTMSG_OK)
  {
    iRetCode = RTMSG_KO;
    printf("InitFeature Error!\n");
    goto __end;
  } 

  if ((iRetCode = Tmk_InitRnnoise(&stModelState->m_stRnnState)) != RTMSG_OK)
  {
    iRetCode = RTMSG_KO;
    printf("InitRnnoise Error!\n");
    goto __end;
  } 

  if ((iRetCode = Tmk_SetRnnVadPar(stModelState, fFrtLen, fFrtThs, fEndLen, fEndThs)) != RTMSG_OK)
  {
    iRetCode = RTMSG_KO;
    printf("SetAudioVad Error!\n");
    goto __end;
  } 

__end:
  return iRetCode;
}

/*
int Tmk_CalcRnnVadPro(void *p_stModelState, int *p_iVadFlag, float *p_fVadProb, short *p_nRnnData, short *p_nSrcData, short p_nSrcSize)
{
  int iRetCode = RTMSG_OK;
  int iIndex;
  float fFetData[RNN_NB_FEATURES] = {0};
  float fGinData[RNN_NB_BANDS]    ={0};
  float fSumNrg = 0;
  ModelState *stModelState = p_stModelState;

  for (iIndex = 0; iIndex < RNN_FRAME_OVER; iIndex++) 
  {
    stModelState->m_fFrameData[iIndex] = stModelState->m_fFrameData[iIndex + RNN_FRAME_OVER];
    fSumNrg += fabs(stModelState->m_fFrameData[iIndex]);
    stModelState->m_fFrameData[iIndex + RNN_FRAME_OVER] = p_nSrcData[iIndex];
    fSumNrg += fabs(stModelState->m_fFrameData[iIndex + RNN_FRAME_OVER]);
  }
 
  if (fSumNrg < RNN_MIN_THD)
  {
    *p_fVadProb = RNN_MIN_THD;
  }
  else
  {
    if ((iRetCode = Tmk_CalcFeature(&stModelState->m_stFetState, fFetData, stModelState->m_fFrameData)) != RTMSG_OK)
    {
      iRetCode = RTMSG_KO;
      printf("CalcFeature Error!\n");
      goto __end;
    } 

    if ((iRetCode = Tmk_CalcRnnoise(&stModelState->m_stRnnState, fGinData, p_fVadProb, fFetData)) != RTMSG_OK)
    {
      iRetCode = RTMSG_KO;
      printf("CalcRnnoise Error!\n");
      goto __end;
    } 
  }
  *p_iVadFlag = Tmk_GetVadFlag(&stModelState->m_stVadState, *p_fVadProb);

__end:
  return iRetCode;
}
*/

// 16ms 处理
int Tmk_CalcRnnVadPro(void *p_stModelState, int *p_iVadFlag, float *p_fVadProb, short *p_nRnnData, short *p_nSrcData, short p_nSrcSize)
{
  int iRetCode = RTMSG_OK;
  int iIndex;
  float fFetData[RNN_NB_FEATURES] = {0};
  float fGinData[RNN_NB_BANDS]    ={0};
  float fSumNrg = 0;
  short nEngFlag = 1;
  ModelState *stModelState = p_stModelState;

  for (iIndex = 0; iIndex < RNN_FRAME_OVER; iIndex++)
  {
    stModelState->m_fFrameData[iIndex] = stModelState->m_fFrameData[iIndex + RNN_FRAME_OVER];
    fSumNrg += fabs(stModelState->m_fFrameData[iIndex]);
    stModelState->m_fFrameData[iIndex + RNN_FRAME_OVER] = p_nSrcData[iIndex];
    fSumNrg += fabs(stModelState->m_fFrameData[iIndex + RNN_FRAME_OVER]);
  }

  if (fSumNrg < RNN_MIN_THD)
  {
    *p_fVadProb = RNN_MIN_THD;
  }
  else
  {
    Tmk_CalcFeature(&stModelState->m_stFetState, &nEngFlag, fFetData, stModelState->m_fFrameData);
    if (nEngFlag == 0)
    {
      *p_fVadProb = stModelState->m_stVadState.m_iCurrVadProb;
      *p_iVadFlag = stModelState->m_stVadState.m_iCurrVadStatus;
    }
    else
    {
      if ((iRetCode = Tmk_CalcRnnoise(&stModelState->m_stRnnState, fGinData, p_fVadProb, fFetData)) != RTMSG_OK)
      {
        iRetCode = RTMSG_KO;
        printf("CalcRnnoise Error!\n");
        goto __end;
      }
    }
  }
  *p_iVadFlag = Tmk_GetVadFlag(&stModelState->m_stVadState, *p_fVadProb);
  stModelState->m_stVadState.m_iCurrVadProb   = *p_fVadProb;
  stModelState->m_stVadState.m_iCurrVadStatus = *p_iVadFlag;

__end:
  return iRetCode;
}

//
//int Tmk_CalcRnnVadPro(void *p_stModelState, int *p_iVadFlag, float *p_fVadProb, short *p_nRnnData, short *p_nSrcData, short p_nSrcSize)
//{
//  int iRetCode = RTMSG_OK;
//  int iIndex;
//  float fFetData[RNN_NB_FEATURES] = {0};
//  float fGinData[RNN_NB_BANDS]    ={0};
//  float fSumNrg = 0;
//  short nEngFlag = 1;
//  ModelState *stModelState = p_stModelState;
//
//  //==================Deal Data Buff=======================
//  if (stModelState->m_nFrameEnd + p_nSrcSize >= RNN_FRAME_SIZE * 3)
//  {
//    stModelState->m_nFrameEnd = 0;
//  }
//  for (iIndex = 0; iIndex < p_nSrcSize; iIndex++)
//  {
//    stModelState->m_nFrameBuff[stModelState->m_nFrameEnd + iIndex] = p_nSrcData[iIndex];
//  }
//  stModelState->m_nFrameEnd += p_nSrcSize;
//  //=======================================================
//
//  if (stModelState->m_nFrameEnd >= RNN_FRAME_OVER)
//  {
//
//    for (iIndex = 0; iIndex < RNN_FRAME_OVER; iIndex++)
//    {
//      stModelState->m_fFrameData[iIndex] = stModelState->m_fFrameData[iIndex + RNN_FRAME_OVER];
//      fSumNrg += fabs(stModelState->m_fFrameData[iIndex]);
//      stModelState->m_fFrameData[iIndex + RNN_FRAME_OVER] = stModelState->m_nFrameBuff[iIndex];
//      fSumNrg += fabs(stModelState->m_fFrameData[iIndex + RNN_FRAME_OVER]);
//    }
//    stModelState->m_nFrameEnd -= RNN_FRAME_OVER;
//    for (iIndex = 0; iIndex < stModelState->m_nFrameEnd; iIndex++)
//    {
//      stModelState->m_nFrameBuff[iIndex] = stModelState->m_nFrameBuff[iIndex + RNN_FRAME_OVER];
//    }
//
//    if (fSumNrg < RNN_MIN_THD)
//    {
//      *p_fVadProb = RNN_MIN_THD;
//    }
//    else
//    {
//      Tmk_CalcFeature(&stModelState->m_stFetState, &nEngFlag, fFetData, stModelState->m_fFrameData);
//      if (nEngFlag == 0)
//      {
//        *p_fVadProb = stModelState->m_stVadState.m_iCurrVadProb;
//      }
//      else
//      {
//        if ((iRetCode = Tmk_CalcRnnoise(&stModelState->m_stRnnState, fGinData, p_fVadProb, fFetData)) != RTMSG_OK)
//        {
//          iRetCode = RTMSG_KO;
//          printf("CalcRnnoise Error!\n");
//          goto __end;
//        }
//      }
//    }
//    *p_iVadFlag = Tmk_GetVadFlag(&stModelState->m_stVadState, *p_fVadProb);
//    stModelState->m_stVadState.m_iCurrVadProb   = *p_fVadProb;
//    stModelState->m_stVadState.m_iCurrVadStatus = *p_iVadFlag;
//  }
//  else
//  {
//    *p_iVadFlag = stModelState->m_stVadState.m_iCurrVadStatus;
//    *p_fVadProb = stModelState->m_stVadState.m_iCurrVadProb;
//  }
//
//__end:
//  //printf("=======> %d\n", *p_iVadFlag);
//  return iRetCode;
//}

int Tmk_FreeRnnVadPro(void *p_stModelState)
{
  int iRetCode = RTMSG_OK;
  ModelState *stModelState = p_stModelState;
  if (stModelState)
  {
    free(stModelState);
    stModelState == NULL;
  }

  if ((iRetCode = Tmk_FreeFeature()) != RTMSG_OK)
  {
    iRetCode = RTMSG_KO;
    printf("FreeFeature Error!\n");
    goto __end;
  } 
  if ((iRetCode = Tmk_FreeRnnoise()) != RTMSG_OK)
  {
    iRetCode = RTMSG_KO;
    printf("FreeRnnoise Error!\n");
    goto __end;
  } 

__end:
  return iRetCode;
}
