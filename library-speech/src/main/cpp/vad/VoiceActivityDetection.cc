#include <deque>
#include <map>
#include <array>
#include <string>
#include <vector>
#include <iostream>
#include "Define.h"
#include "VoiceActivityDetection.h"

#ifdef UseOldVad

#include "webrtc/common_audio/resampler/push_sinc_resampler.h"
#include "webrtc/modules/audio_processing/agc2/rnn_vad/common.h"
#include "webrtc/modules/audio_processing/agc2/rnn_vad/features_extraction.h"
#include "webrtc/modules/audio_processing/agc2/rnn_vad/rnn.h"

#endif

#ifdef __cplusplus
extern "C" {
#endif

#include "./TmkVad/1mic_rnn_pro.h"

#ifdef __cplusplus
}
#endif

//#define UseInternalCache 1
//#define UseOldVad 1

namespace tmkspeech {

VoiceActivityDetection::VoiceActivityDetection(int sampleRate, float thr) : threshold(0.9f), stModelState(nullptr) {
    this->threshold = thr;
    this->frtLen = 0.3f;
    this->frtThd = threshold;
    this->endLen = 0.3f;
    this->endThd = threshold - 0.05;

    this->maxHeadSize = 18;
    this->maxSilenceSize = 50;
    this->minVadEnergy = 100000;
    this->DefalutMinVadEnergy = 100000;

    int iRetCode = RTMSG_OK;
    if ((iRetCode = Tmk_InitRnnVadPro(&stModelState)) != RTMSG_OK)
    {
        iRetCode = RTMSG_KO;
        LOGE("初始化 Rnn vad 失败");
        return;
    }
    LOGD("初始化 Rnn vad, 参数 frtLen:%f, frtThd:%f, endLen:%f, endThd:%f", frtLen, frtThd, endLen, endThd);
}

VoiceActivityDetection::~VoiceActivityDetection() {
    Tmk_FreeRnnVadPro(stModelState);
    LOGD("反初始化 Rnn vad");
}

bool VoiceActivityDetection::UpdateOptions(float fFrtLen, float fFrtThd, float fEndLen, float fEndThd) {
    frtLen = fFrtLen;
    frtThd = fFrtThd;
    endLen = fEndLen;
    endThd = fEndThd;
    // 默认参数 0.9 0.3 0.85 0.3
    int iRetCode = Tmk_SetRnnVadPar(stModelState, frtLen, frtThd, endLen, endThd);
    LOGD("更新 Rnn vad 参数, frtLen:%f, frtThd:%f, endLen:%f, endThd:%f", frtLen, frtThd, endLen, endThd);
    return iRetCode == RTMSG_OK;
}

/// 检测是否是声音帧
/// @param samples 16k 16ms 声音采样数据, 即采样点个数 256, 字节数 512
/// @param length 16ms 声音数据长度
/// @param energy 此数据的能量, 缓存用于计算激活等
bool VoiceActivityDetection::IsVoice(const short *samples, size_t length, uint64_t &energy) {
    int iVadFlag = VAD_SIL;
    float fVadProb = 0;
    short nRnnData[RNN_FRAME_OVER] = {0};

    // 检测是否是声音帧
    Tmk_CalcRnnVadPro(stModelState, &iVadFlag, &fVadProb, nRnnData, (short *)samples, length);
    bool isVoice = fVadProb >= frtThd;

    // 计算能量
    uint64_t out_energy = 0;
    for (int i = 0; i < length; i++) {
        out_energy += abs(samples[i]);
    }
    energy = out_energy;
//    LOGD("Rnn vad %d(%f/%llu) isVoice: %d", iVadFlag, fVadProb, out_energy, isVoice);
    return isVoice;
};

uint64_t VoiceActivityDetection::GetEnergy(const short *samples, size_t length) {
    // 计算能量
    uint64_t out_energy = 0;
    for (int i = 0; i < length; i++) {
        out_energy += abs(samples[i]);
    }
    return out_energy;
}

};
