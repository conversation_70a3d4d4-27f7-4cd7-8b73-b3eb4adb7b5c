#ifndef __AUDIOOUTPUTSTREAM_H__
#define __AUDIOOUTPUTSTREAM_H__

#include <string>

/* 端点检测(Voice Activity Detection, VAD)
 * 音频端点检测就是从连续的语音流中检测出有效的语音段。它包括两个方面，检测出有效语音的起始点即前端点，检测出有效语音的结束点即后端点。
 * 好处:
 * 1.在存储或传输语音的场景下，从连续的语音流中分离出有效语音，可以降低存储或传输的数据量
 * 2.提供说话人是否在说话的交互指示
 *
 */
namespace tmkspeech {
    class VoiceActivityDetection {
    public:
        VoiceActivityDetection(int sampleRate, float thr);
        ~VoiceActivityDetection();

        bool UpdateOptions(float fFrtLen, float fFrtThd, float fEndLen, float fEndThd);
        /// 检测是否是声音帧
        /// @param samples 16k 16ms 声音采样数据, 即采样点个数 256, 字节数 512
        /// @param length 16ms 声音数据长度
        /// @param energy 此数据的能量, 缓存用于计算激活等
        bool IsVoice(const short *samples, size_t length, uint64_t &energy);
        
        uint64_t GetEnergy(const short *samples, size_t length);
        /*
         *  | ------------- |-----------------------------------------------------------
         *  | SetVadBegin   |前端点检测: 
         *  | ------------- |-----------------------------------------------------------
         *  |               |后断点检测: 后端点静音检测时间,即用户停止说话多长时间后, 表示激活失效
         *  | SetVadEnd     |单位: 帧, 16ms为一帧
         *  | ------------- |-----------------------------------------------------------
         */
        void SetMinVadEnergy(int energy);
        void SetVadBegin(int nFrames);
        int GetVadBegin();
        void SetVadEnd(int nFrames);
        int GetVadEnd();
        
        bool DetectActivityInvaild();
        bool DetectActivity();
        
    private:
        float threshold;
        void *stModelState;
        
        float frtLen; // 前端点检测时长, 单位 s
        float frtThd; // 算法阈值, 类似置信度
        float endLen; // 后端点检测时长, 单位 s
        float endThd; // 算法阈值, 类似置信度
        
        int maxHeadSize = 18;
        int maxSilenceSize = 50;
        int minVadEnergy = 100000;
        int DefalutMinVadEnergy = 100000;

    };

};

#endif
