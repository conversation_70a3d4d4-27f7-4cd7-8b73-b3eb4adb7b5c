#include "2mic_audio_mas.h"

void GCC_FFT(GccComplex *data, int order)
{
  int n, i, nv2, j, k, le, l, le1, ip, nm1;
  GccComplex t, u, w;

  n = 1;
  for (i = 0; i < order; i++)
  {
    n = n * 2;
  }

  nv2 = n / 2;
  nm1 = n - 1;
  j = 1;

  for (i = 1; i <= nm1; i++)
  {
    if (i < j)
    {
      t.m_dlReal = data[i - 1].m_dlReal;
      t.m_dlImag = data[i - 1].m_dlImag;
      data[i - 1].m_dlReal = data[j - 1].m_dlReal;
      data[i - 1].m_dlImag = data[j - 1].m_dlImag;
      data[j - 1].m_dlReal = t.m_dlReal;
      data[j - 1].m_dlImag = t.m_dlImag;
    }

    k = nv2;

    while (k < j)
    {
      j -= k;
      k /= 2;
    }
    j += k;
  }

  le = 1;
  for (l = 1; l <= order; l++)
  {
    le *= 2;
    le1 = le / 2;
    u.m_dlReal = 1;
    u.m_dlImag = 0;
    w.m_dlReal = (cos(M_PI / le1));
    w.m_dlImag =(-sin(M_PI / le1));
    // w.m_dlReal = g_fPiCos[l-1];
    // w.m_dlImag = -g_fPiSin[l-1];

    for (j = 1; j <= le1; j++)
    {
      for (i = j; i <= n; i += le)
      {
        ip = i + le1;
        t.m_dlReal = (data[ip - 1].m_dlReal * u.m_dlReal - data[ip - 1].m_dlImag * u.m_dlImag);
        t.m_dlImag = (data[ip - 1].m_dlReal * u.m_dlImag + data[ip - 1].m_dlImag * u.m_dlReal);
        data[ip - 1].m_dlReal = data[i - 1].m_dlReal - t.m_dlReal;
        data[ip - 1].m_dlImag = data[i - 1].m_dlImag - t.m_dlImag;
        data[i - 1].m_dlReal = t.m_dlReal + data[i - 1].m_dlReal;
        data[i - 1].m_dlImag = t.m_dlImag + data[i - 1].m_dlImag;
      }

      t.m_dlReal = u.m_dlReal * w.m_dlReal - u.m_dlImag * w.m_dlImag;
      t.m_dlImag = u.m_dlImag * w.m_dlReal + u.m_dlReal * w.m_dlImag;
      u.m_dlReal = t.m_dlReal;
      u.m_dlImag = t.m_dlImag;
    }
  }
}

void GCC_IFFT(GccComplex *data, int order)
{
  int n, i, nv2, j, k, le, l, le1, ip, nm1;
  GccComplex t, u, w;

  n = 1;
  for (i = 0; i < order; i++)
  {
    n = n*2;
  }

  nv2 = n / 2;
  nm1 = n - 1;
  j = 1;

  for (i = 1; i <= nm1; i++)
  {
    if (i < j)
    {
      t.m_dlReal = data[i - 1].m_dlReal;
      t.m_dlImag = data[i - 1].m_dlImag;
      data[i - 1].m_dlReal = data[j - 1].m_dlReal;
      data[i - 1].m_dlImag = data[j - 1].m_dlImag;
      data[j - 1].m_dlReal = t.m_dlReal;
      data[j - 1].m_dlImag = t.m_dlImag;
    }

    k = nv2;

    while (k < j)
    {
      j -= k;
      k /= 2;
    }
    j += k;
  }

  le = 1;
  for (l= 1; l <= order; l++)
  {
    le *= 2;
    le1 = le / 2;
    u.m_dlReal = 1;
    u.m_dlImag = 0;
    // w.m_dlReal = g_fPiCos[l-1];
    // w.m_dlImag = g_fPiSin[l-1];
    w.m_dlReal = (cos(M_PI / le1));
    w.m_dlImag = (sin(M_PI / le1));

    for (j = 1; j <= le1; j++)
    {
      for (i = j; i <= n; i += le)
      {
        ip = i + le1;
        t.m_dlReal = (data[ip - 1].m_dlReal * u.m_dlReal - data[ip - 1].m_dlImag * u.m_dlImag);
        t.m_dlImag = (data[ip - 1].m_dlReal * u.m_dlImag + data[ip - 1].m_dlImag * u.m_dlReal);
        data[ip - 1].m_dlReal = data[i - 1].m_dlReal - t.m_dlReal;
        data[ip - 1].m_dlImag = data[i - 1].m_dlImag - t.m_dlImag;
        data[i - 1].m_dlReal = t.m_dlReal + data[i - 1].m_dlReal;
        data[i - 1].m_dlImag = t.m_dlImag + data[i - 1].m_dlImag;
      }

      t.m_dlReal = u.m_dlReal * w.m_dlReal - u.m_dlImag * w.m_dlImag;
      t.m_dlImag = u.m_dlImag * w.m_dlReal + u.m_dlReal * w.m_dlImag;
      u.m_dlReal = t.m_dlReal;
      u.m_dlImag = t.m_dlImag;
    }
  }
  for (i = 0; i < n; i++)
  {
    data[i].m_dlReal = data[i].m_dlReal / n;
    data[i].m_dlImag = data[i].m_dlImag / n;
  }
}

float GetGccMax(float *p_dlSrcData, int p_iDataLen)
{
  int iIndex = 0;
  float dlMax = p_dlSrcData[0];
  for (iIndex = 1; iIndex < p_iDataLen; iIndex++)
  {
    if (p_dlSrcData[iIndex] > dlMax)
    {
      dlMax = p_dlSrcData[iIndex];
    }
  }

  return dlMax;
}

float GetGccMean(float *p_dlSrcData, int p_iDataLen)
{
  int iIndex = 0;
  float dlMean = 0;

  for (iIndex = 0; iIndex < p_iDataLen; iIndex++)
  {
    dlMean += p_dlSrcData[iIndex];
  }
  dlMean /= p_iDataLen;

  return dlMean;
}
