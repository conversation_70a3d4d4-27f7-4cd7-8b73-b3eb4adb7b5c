#ifndef _2MIC_AUDIO_DEF_H_
#define _2MIC_AUDIO_DEF_H_

#include <stdio.h>
#include <stdlib.h>
#include <math.h>

#ifndef GCC_RTMSG_OK
#define GCC_RTMSG_OK         1
#endif
#ifndef GCC_RTMSG_KO
#define GCC_RTMSG_KO        -1
#endif
#ifndef M_PI 
#define M_PI 3.14159265358979323846
#endif

//================GCC==================
#define GCC_CHANNELNUM  2
#define GCC_FFTORDER    9
#define GCC_FRAMESIZE   (1 << GCC_FFTORDER)
#define GCC_FRAMEOVER   (GCC_FRAMESIZE >> 1)
#define GCC_FRAMEUSED   ((GCC_FRAMESIZE >> 2) + 1)
//==================VAD====================
#define EER_VAD_SIL          0
#define EER_VAD_ON           1
#define EER_VAD_END          2

#define EER_VAD_FFTORDER     9
#define EER_VAD_FRAMESIZE   (1<<EER_VAD_FFTORDER)
#define EER_VAD_FRAMEOVER   (EER_VAD_FRAMESIZE >> 1)
#define EER_VAD_FRAMEBUFF    125
#define EER_VAD_DELAYNUM     2
#define EER_MAX_VALUE       (-1e10)
#define EER_MIN_VALUE       (1e10)

typedef struct _err_complex_
{
  float m_dlReal;
  float m_dlImag;
}GccComplex;

typedef struct _eer_state_
{
  float   m_fEerData[EER_VAD_FRAMESIZE];
  float   m_fEerSpec[EER_VAD_FRAMEOVER + 1];
  GccComplex m_stEerComplex[EER_VAD_FRAMESIZE];
  //
  float   m_fEerVadOnThd;
  float   m_fEerVadLkThd;
  float   m_fEerDamp;
  float   m_fPreEerRatio[2];
}EerState;


#endif
