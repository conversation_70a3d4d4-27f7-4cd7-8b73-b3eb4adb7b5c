#include "2mic_audio_gcc.h"
#include "2mic_audio_mas.h"
#include "2mic_audio_def.h"
#include "2mic_audio_vad.h"

float   g_fGccHan[GCC_FRAMESIZE]     = {0};
float   g_fGccFrame[GCC_CHANNELNUM][GCC_FRAMESIZE]   = {0};
float   g_fGccErg[GCC_CHANNELNUM][GCC_FRAMESIZE]     = {0};
float   g_fSumErg[GCC_CHANNELNUM]                    = {0};
short   g_nCorrFlagData[GCC_CHANNELNUM][3]           = {0};   // corr_flag_old

float   g_fPreCorrNf               = 1.0f;  // corr_nf_old
float   g_fInitG                   = 0.1f; // para.initG
float   g_fGainN                   = 0.0f;
float   g_fGainF                   = 0.0f;
float   g_fGccRatioTh              = 3.0f; // 两路音频音量的比值

float   g_fGccAlpha                = 0.5f;
float   g_fGccBeta                 = 0.2f;
float   g_fGccErgTH                = 0.0512f;
float   g_fGccCorrMeanTH           = 0.1f;
float   g_fGccHCorrTH              = 0.7f;
float   g_fGccLCorrTH              = 0.5f;
float   g_fGccInitGain             = 0.01f;

//=====VAD=======
void        *stEerStateL = NULL;
void        *stEerStateR = NULL;


int Init2MicGcc()
{
  int iRetCode = GCC_RTMSG_OK;
  int iIndex;

  for (iIndex = 0; iIndex < GCC_FRAMEOVER; iIndex++)
  {
    g_fGccHan[iIndex + 1] = sqrtf(0.5 - 0.5 * cos(2.0 * M_PI * (iIndex+1) / (GCC_FRAMESIZE)));
  }
  for (iIndex = 0; iIndex < GCC_FRAMEOVER - 1; iIndex++)
  {
    g_fGccHan[GCC_FRAMESIZE - iIndex - 1] = g_fGccHan[iIndex + 1];
  }

  g_fGccErgTH = 0.0001 * GCC_FRAMESIZE;
  g_fGccCorrMeanTH = 0.1f;
  g_fGccHCorrTH    = 0.7f;
  g_fGccLCorrTH    = 0.5f;

  InitEerVad(&stEerStateL);
  InitEerVad(&stEerStateR);

__end:
  return iRetCode;
}

int Set2MicGcc(float p_fHCorrTH, float p_fLCorrTH, float p_fVadOnThd, float p_fVadLkThd)
{
  int iRetCode = GCC_RTMSG_OK;

  if (p_fHCorrTH > 0.0f && p_fHCorrTH < 1.0f)
    g_fGccHCorrTH = p_fHCorrTH;
  if (p_fLCorrTH > 0.0f && p_fLCorrTH < 1.0f)
  {
    g_fGccLCorrTH = p_fLCorrTH;
  }

  SetEerVad(stEerStateL, p_fVadOnThd, p_fVadLkThd);
  SetEerVad(stEerStateR, p_fVadOnThd, p_fVadLkThd);

__end:
  return iRetCode;
}

int Deal2MicGcc(short *p_nTgtData, short* p_nVadFlagL, short* p_nVadFlagR, float *p_fCorrNF, short* p_nWavData)
{
  int iRetCode = GCC_RTMSG_OK;
  int iIndex, jIndex;
  float fTmp;
  short nCorrFlag[GCC_CHANNELNUM]                = {0};
  float fGccData[GCC_FRAMEOVER * GCC_CHANNELNUM] = {0};
  float fWavDataL[EER_VAD_FRAMEOVER] = {0};
  float fWavDataR[EER_VAD_FRAMEOVER] = {0};
  float fVadProb = 0;

  DealWav2Gcc(fGccData, nCorrFlag, p_fCorrNF, p_nWavData);
  for (iIndex = 0; iIndex < EER_VAD_FRAMEOVER; iIndex++)
  {
    fWavDataL[iIndex] = fGccData[iIndex * GCC_CHANNELNUM + 0];
    fWavDataR[iIndex] = fGccData[iIndex * GCC_CHANNELNUM + 1];
  }
  DealEerVad(stEerStateL, p_nVadFlagL, &fVadProb, fWavDataL, EER_VAD_FRAMEOVER, nCorrFlag[0]);
  DealEerVad(stEerStateR, p_nVadFlagR, &fVadProb, fWavDataR, EER_VAD_FRAMEOVER, nCorrFlag[1]);

  for (iIndex = 0; iIndex < GCC_CHANNELNUM; iIndex++) {
    for (jIndex = 0; jIndex < GCC_FRAMEOVER; jIndex++) {
      fTmp = fGccData[iIndex + jIndex * GCC_CHANNELNUM] * 32768.0f;
      if (fTmp > 32767)
      {
        fTmp = 32767;
      }
      if (fTmp < -32768)
      {
        fTmp = -32768;
      }
      p_nTgtData[iIndex + jIndex * GCC_CHANNELNUM] = (short)fTmp;
    }
  }

__end:
  return iRetCode;
}

int Free2MicGcc()
{
  int iRetCode = GCC_RTMSG_OK;

  FreeEerVad(stEerStateL);
  FreeEerVad(stEerStateR);

__end:
  return iRetCode;
}


int DealWav2Gcc(float *p_fGccData, short *p_nCorrFlag, float *p_fCorrNF, short* p_nWavData)
{
  int iRetCode = GCC_RTMSG_OK;
  int iIndex, jIndex;
  int iLen;
  float fFn;
  float fEn, fEf;
  float fTmp;
  float fTmpGainN, fTmpGainF;
  float fCorrNf = EER_MAX_VALUE;
  float fSumErg[GCC_CHANNELNUM]     = {0};   // En / Ef
  short nCorrFlag[GCC_CHANNELNUM]   = {0};   // nCorrFlag -- corr_flag

  fEn = 0.0f;
  fEf = 0.0f;
  for (iIndex = 0; iIndex < GCC_CHANNELNUM; iIndex++) {
    for (jIndex = 0; jIndex < GCC_FRAMEOVER; jIndex++) {
      g_fGccFrame[iIndex][jIndex] = g_fGccFrame[iIndex][jIndex + GCC_FRAMEOVER];
      g_fGccFrame[iIndex][jIndex + GCC_FRAMEOVER] = p_nWavData[iIndex + jIndex * GCC_CHANNELNUM] / 32768.0f;
    }
  }
  //时域互相关
  for (iIndex = 0; iIndex < GCC_CHANNELNUM; iIndex++)
  {
    fSumErg[iIndex] = 0.0f;
    for (jIndex = 0; jIndex < GCC_FRAMESIZE; jIndex++)
    {
      g_fGccErg[iIndex][jIndex] = g_fGccFrame[iIndex][jIndex] * g_fGccFrame[iIndex][jIndex];
      fSumErg[iIndex] += g_fGccErg[iIndex][jIndex];
    }
  }

  for (iIndex = 0; iIndex < GCC_FRAMEOVER; iIndex++)
  {
    iLen = GCC_FRAMESIZE - iIndex;
    fFn = 0.0f;
    for (jIndex = 0; jIndex < iLen; jIndex++)
    {
      fFn += g_fGccFrame[0][jIndex + iIndex] * g_fGccFrame[1][jIndex];
    }
    fTmp = fFn / sqrtf((fSumErg[0] - fEn) * (fSumErg[1] - fEf));
    fEn += g_fGccErg[0][iIndex];
    fEf += g_fGccErg[1][GCC_FRAMESIZE - iIndex -1];

    if (fTmp > fCorrNf)
    {
      fCorrNf = fTmp;
    }
  }
  *p_fCorrNF = fCorrNf;
  g_fSumErg[0] = (g_fGccAlpha) * g_fSumErg[0] + (1.0f - g_fGccAlpha) * fSumErg[0];
  g_fSumErg[1] = (g_fGccAlpha) * g_fSumErg[1] + (1.0f - g_fGccAlpha) * fSumErg[1];

  //计算增益
  fTmpGainN = 1.0f;
  fTmpGainF = 1.0f;
  if (fCorrNf > g_fGccHCorrTH)
  {
    if (g_fSumErg[0] > g_fGccRatioTh * g_fSumErg[1] && (g_nCorrFlagData[0][1] + g_nCorrFlagData[0][2]) == 0)
    {
      fTmpGainF = g_fInitG;
      if (fCorrNf / g_fPreCorrNf > 2.0f)
      {
        g_fGainF /= 2.0f;
      }
      nCorrFlag[1] = 1;
    } 
    if (g_fSumErg[1] > g_fGccRatioTh * g_fSumErg[0] && (g_nCorrFlagData[1][1] + g_nCorrFlagData[1][2]) == 0)
    {
      fTmpGainN = g_fInitG;
      if (fCorrNf / g_fPreCorrNf > 2.0f)
      {
        g_fGainN /= 2.0f;
      }
      nCorrFlag[0] = 1;
    }
  } 
  else
  {
    if (fCorrNf > g_fGccLCorrTH)
    {
      if (g_fSumErg[0] > g_fGccRatioTh * g_fSumErg[1] && (g_nCorrFlagData[0][1] + g_nCorrFlagData[0][2]) == 0)
      {
        fTmpGainF = g_fInitG;
        if (fCorrNf / g_fPreCorrNf > 2.0f)
        {
          g_fGainF /= 2.0f;
        }
        nCorrFlag[1] = 1;
      }
      if (g_fSumErg[1] > g_fGccRatioTh * g_fSumErg[0] && (g_nCorrFlagData[1][1] + g_nCorrFlagData[1][2]) == 0)
      {
        fTmpGainN = g_fInitG;
        if (fCorrNf / g_fPreCorrNf > 2.0f)
        {
          g_fGainN /= 2.0f;
        }
        nCorrFlag[0] = 1;
      }
    }
  }

  g_fGainN = g_fGccBeta * g_fGainN + (1.0f - g_fGccBeta) * fTmpGainN;
  g_fGainF = g_fGccBeta * g_fGainF + (1.0f - g_fGccBeta) * fTmpGainF;

  for (iIndex = 0; iIndex < GCC_FRAMEOVER; iIndex++)
  {
    p_fGccData[iIndex * GCC_CHANNELNUM + 0] = p_nWavData[iIndex * GCC_CHANNELNUM + 0] * g_fGainN / 32768.0f;
    p_fGccData[iIndex * GCC_CHANNELNUM + 1] = p_nWavData[iIndex * GCC_CHANNELNUM + 1] * g_fGainF / 32768.0f;
  }
  g_fPreCorrNf = fCorrNf;

  p_nCorrFlag[0] = g_nCorrFlagData[0][0] + g_nCorrFlagData[0][1] + g_nCorrFlagData[0][2] + nCorrFlag[0];
  p_nCorrFlag[1] = g_nCorrFlagData[1][0] + g_nCorrFlagData[1][1] + g_nCorrFlagData[1][2] + nCorrFlag[1];
  g_nCorrFlagData[0][0] = g_nCorrFlagData[0][1];
  g_nCorrFlagData[0][1] = g_nCorrFlagData[0][2];
  g_nCorrFlagData[0][2] = nCorrFlag[0];
  g_nCorrFlagData[1][0] = g_nCorrFlagData[1][1];
  g_nCorrFlagData[1][1] = g_nCorrFlagData[1][2];
  g_nCorrFlagData[1][2] = nCorrFlag[1];

__end:
  return iRetCode;
}
