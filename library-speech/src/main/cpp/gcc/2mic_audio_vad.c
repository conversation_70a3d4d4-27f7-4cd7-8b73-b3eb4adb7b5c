#include "2mic_audio_def.h"
#include <string.h>
#include "2mic_audio_mas.h"

float m_dlEerHann[] = 
{
  (float)0,(float)0.00613588464915602,(float)0.0122715382857193,(float)0.0184067299058042,(float)0.0245412285229122,(float)0.0306748031766369,(float)0.0368072229413589,(float)0.0429382569349409,(float)0.0490676743274178,(float)0.0551952443496899,(float)0.0613207363022087,(float)0.0674439195636641,(float)0.0735645635996673,(float)0.0796824379714300,(float)0.0857973123444400,(float)0.0919089564971329,(float)0.0980171403295607,(float)0.104121633872055,(float)0.110222207293883,(float)0.116318630911905,(float)0.122410675199216,(float)0.128498110793793,(float)0.134580708507126,(float)0.140658239332849,(float)0.146730474455362,(float)0.152797185258443,(float)0.158858143333861,(float)0.164913120489970,(float)0.170961888760301,(float)0.177004220412149,(float)0.183039887955141,(float)0.189068664149806,(float)0.195090322016128,(float)0.201104634842092,(float)0.207111376192219,(float)0.213110319916091,(float)0.219101240156870,(float)0.225083911359793,(float)0.231058108280671,(float)0.237023605994367,(float)0.242980179903264,(float)0.248927605745720,(float)0.254865659604515,(float)0.260794117915276,(float)0.266712757474898,(float)0.272621355449949,(float)0.278519689385053,(float)0.284407537211272,(float)0.290284677254462,(float)0.296150888243624,(float)0.302005949319228,(float)0.307849640041535,(float)0.313681740398891,(float)0.319502030816016,(float)0.325310292162263,(float)0.331106305759876,(float)0.336889853392220,(float)0.342660717311994,(float)0.348418680249435,(float)0.354163525420490,(float)0.359895036534988,(float)0.365612997804774,(float)0.371317193951838,(float)0.377007410216418,(float)0.382683432365090,(float)0.388345046698826,(float)0.393992040061048,(float)0.399624199845647,(float)0.405241314004990,(float)0.410843171057904,(float)0.416429560097637,(float)0.422000270799800,(float)0.427555093430282,(float)0.433093818853152,(float)0.438616238538528,(float)0.444122144570429,(float)0.449611329654607,(float)0.455083587126344,(float)0.460538710958240,(float)0.465976495767966,(float)0.471396736825998,(float)0.476799230063322,(float)0.482183772079123,(float)0.487550160148436,(float)0.492898192229784,(float)0.498227666972782,(float)0.503538383725718,(float)0.508830142543107,(float)0.514102744193222,(float)0.519355990165590,(float)0.524589682678469,(float)0.529803624686295,(float)0.534997619887097,(float)0.540171472729893,(float)0.545324988422047,(float)0.550457972936605,(float)0.555570233019602,(float)0.560661576197336,(float)0.565731810783613,(float)0.570780745886967,(float)0.575808191417845,(float)0.580813958095765,(float)0.585797857456439,(float)0.590759701858874,(float)0.595699304492433,(float)0.600616479383869,(float)0.605511041404326,(float)0.610382806276309,(float)0.615231590580627,(float)0.620057211763289,(float)0.624859488142387,(float)0.629638238914927,(float)0.634393284163646,(float)0.639124444863776,(float)0.643831542889791,(float)0.648514401022112,(float)0.653172842953777,(float)0.657806693297079,(float)0.662415777590172,(float)0.666999922303638,(float)0.671558954847018,(float)0.676092703575316,(float)0.680600997795453,(float)0.685083667772700,(float)0.689540544737067,(float)0.693971460889654,(float)0.698376249408973,(float)0.702754744457225,(float)0.707106781186548,(float)0.711432195745216,(float)0.715730825283819,(float)0.720002507961382,(float)0.724247082951467,(float)0.728464390448225,(float)0.732654271672413,(float)0.736816568877370,(float)0.740951125354959,(float)0.745057785441466,(float)0.749136394523459,(float)0.753186799043612,(float)0.757208846506485,(float)0.761202385484262,(float)0.765167265622459,(float)0.769103337645580,(float)0.773010453362737,(float)0.776888465673232,(float)0.780737228572095,(float)0.784556597155575,(float)0.788346427626606,(float)0.792106577300212,(float)0.795836904608884,(float)0.799537269107905,(float)0.803207531480645,(float)0.806847553543799,(float)0.810457198252595,(float)0.814036329705948,(float)0.817584813151584,(float)0.821102514991105,(float)0.824589302785025,(float)0.828045045257756,(float)0.831469612302545,(float)0.834862874986380,(float)0.838224705554838,(float)0.841554977436898,(float)0.844853565249707,(float)0.848120344803297,(float)0.851355193105265,(float)0.854557988365401,(float)0.857728610000272,(float)0.860866938637767,(float)0.863972856121587,(float)0.867046245515693,(float)0.870086991108711,(float)0.873094978418290,(float)0.876070094195407,(float)0.879012226428633,(float)0.881921264348355,(float)0.884797098430938,(float)0.887639620402854,(float)0.890448723244758,(float)0.893224301195515,(float)0.895966249756185,(float)0.898674465693954,(float)0.901348847046022,(float)0.903989293123443,(float)0.906595704514915,(float)0.909167983090522,(float)0.911706032005430,(float)0.914209755703531,(float)0.916679059921043,(float)0.919113851690058,(float)0.921514039342042,(float)0.923879532511287,(float)0.926210242138311,(float)0.928506080473216,(float)0.930766961078984,(float)0.932992798834739,(float)0.935183509938948,(float)0.937339011912575,(float)0.939459223602190,(float)0.941544065183021,(float)0.943593458161960,(float)0.945607325380521,(float)0.947585591017741,(float)0.949528180593037,(float)0.951435020969008,(float)0.953306040354194,(float)0.955141168305771,(float)0.956940335732209,(float)0.958703474895872,(float)0.960430519415566,(float)0.962121404269042,(float)0.963776065795440,(float)0.965394441697689,(float)0.966976471044852,(float)0.968522094274417,(float)0.970031253194544,(float)0.971503890986252,(float)0.972939952205560,(float)0.974339382785576,(float)0.975702130038529,(float)0.977028142657754,(float)0.978317370719628,(float)0.979569765685441,(float)0.980785280403230,(float)0.981963869109555,(float)0.983105487431216,(float)0.984210092386929,(float)0.985277642388941,(float)0.986308097244599,(float)0.987301418157858,(float)0.988257567730750,(float)0.989176509964781,(float)0.990058210262297,(float)0.990902635427780,(float)0.991709753669100,(float)0.992479534598710,(float)0.993211949234795,(float)0.993906970002356,(float)0.994564570734255,(float)0.995184726672197,(float)0.995767414467660,(float)0.996312612182778,(float)0.996820299291166,(float)0.997290456678690,(float)0.997723066644192,(float)0.998118112900149,(float)0.998475580573295,(float)0.998795456205172,(float)0.999077727752645,(float)0.999322384588350,(float)0.999529417501093,(float)0.999698818696204,(float)0.999830581795823,(float)0.999924701839145,(float)0.999981175282601,(float)1,(float)0.999981175282601,(float)0.999924701839145,(float)0.999830581795823,(float)0.999698818696204,(float)0.999529417501093,(float)0.999322384588350,(float)0.999077727752645,(float)0.998795456205172,(float)0.998475580573295,(float)0.998118112900149,(float)0.997723066644192,(float)0.997290456678690,(float)0.996820299291166,(float)0.996312612182778,(float)0.995767414467660,(float)0.995184726672197,(float)0.994564570734255,(float)0.993906970002356,(float)0.993211949234795,(float)0.992479534598710,(float)0.991709753669100,(float)0.990902635427780,(float)0.990058210262297,(float)0.989176509964781,(float)0.988257567730750,(float)0.987301418157858,(float)0.986308097244599,(float)0.985277642388941,(float)0.984210092386929,(float)0.983105487431216,(float)0.981963869109555,(float)0.980785280403230,(float)0.979569765685441,(float)0.978317370719628,(float)0.977028142657754,(float)0.975702130038529,(float)0.974339382785576,(float)0.972939952205560,(float)0.971503890986252,(float)0.970031253194544,(float)0.968522094274417,(float)0.966976471044852,(float)0.965394441697689,(float)0.963776065795440,(float)0.962121404269042,(float)0.960430519415566,(float)0.958703474895872,(float)0.956940335732209,(float)0.955141168305771,(float)0.953306040354194,(float)0.951435020969008,(float)0.949528180593037,(float)0.947585591017741,(float)0.945607325380521,(float)0.943593458161960,(float)0.941544065183021,(float)0.939459223602190,(float)0.937339011912575,(float)0.935183509938948,(float)0.932992798834739,(float)0.930766961078984,(float)0.928506080473216,(float)0.926210242138311,(float)0.923879532511287,(float)0.921514039342042,(float)0.919113851690058,(float)0.916679059921043,(float)0.914209755703531,(float)0.911706032005430,(float)0.909167983090522,(float)0.906595704514915,(float)0.903989293123443,(float)0.901348847046022,(float)0.898674465693954,(float)0.895966249756185,(float)0.893224301195515,(float)0.890448723244758,(float)0.887639620402854,(float)0.884797098430938,(float)0.881921264348355,(float)0.879012226428633,(float)0.876070094195407,(float)0.873094978418290,(float)0.870086991108711,(float)0.867046245515693,(float)0.863972856121587,(float)0.860866938637767,(float)0.857728610000272,(float)0.854557988365401,(float)0.851355193105265,(float)0.848120344803297,(float)0.844853565249707,(float)0.841554977436898,(float)0.838224705554838,(float)0.834862874986380,(float)0.831469612302545,(float)0.828045045257756,(float)0.824589302785025,(float)0.821102514991105,(float)0.817584813151584,(float)0.814036329705948,(float)0.810457198252595,(float)0.806847553543799,(float)0.803207531480645,(float)0.799537269107905,(float)0.795836904608884,(float)0.792106577300212,(float)0.788346427626606,(float)0.784556597155575,(float)0.780737228572095,(float)0.776888465673232,(float)0.773010453362737,(float)0.769103337645580,(float)0.765167265622459,(float)0.761202385484262,(float)0.757208846506485,(float)0.753186799043612,(float)0.749136394523459,(float)0.745057785441466,(float)0.740951125354959,(float)0.736816568877370,(float)0.732654271672413,(float)0.728464390448225,(float)0.724247082951467,(float)0.720002507961382,(float)0.715730825283819,(float)0.711432195745216,(float)0.707106781186548,(float)0.702754744457225,(float)0.698376249408973,(float)0.693971460889654,(float)0.689540544737067,(float)0.685083667772700,(float)0.680600997795453,(float)0.676092703575316,(float)0.671558954847018,(float)0.666999922303638,(float)0.662415777590172,(float)0.657806693297079,(float)0.653172842953777,(float)0.648514401022112,(float)0.643831542889791,(float)0.639124444863776,(float)0.634393284163646,(float)0.629638238914927,(float)0.624859488142387,(float)0.620057211763289,(float)0.615231590580627,(float)0.610382806276309,(float)0.605511041404326,(float)0.600616479383869,(float)0.595699304492433,(float)0.590759701858874,(float)0.585797857456439,(float)0.580813958095765,(float)0.575808191417845,(float)0.570780745886967,(float)0.565731810783613,(float)0.560661576197336,(float)0.555570233019602,(float)0.550457972936605,(float)0.545324988422047,(float)0.540171472729893,(float)0.534997619887097,(float)0.529803624686295,(float)0.524589682678469,(float)0.519355990165590,(float)0.514102744193222,(float)0.508830142543107,(float)0.503538383725718,(float)0.498227666972782,(float)0.492898192229784,(float)0.487550160148436,(float)0.482183772079123,(float)0.476799230063322,(float)0.471396736825998,(float)0.465976495767966,(float)0.460538710958240,(float)0.455083587126344,(float)0.449611329654607,(float)0.444122144570429,(float)0.438616238538528,(float)0.433093818853152,(float)0.427555093430282,(float)0.422000270799800,(float)0.416429560097637,(float)0.410843171057904,(float)0.405241314004990,(float)0.399624199845647,(float)0.393992040061048,(float)0.388345046698826,(float)0.382683432365090,(float)0.377007410216418,(float)0.371317193951838,(float)0.365612997804774,(float)0.359895036534988,(float)0.354163525420490,(float)0.348418680249435,(float)0.342660717311994,(float)0.336889853392220,(float)0.331106305759876,(float)0.325310292162263,(float)0.319502030816016,(float)0.313681740398891,(float)0.307849640041535,(float)0.302005949319228,(float)0.296150888243624,(float)0.290284677254462,(float)0.284407537211272,(float)0.278519689385053,(float)0.272621355449949,(float)0.266712757474898,(float)0.260794117915276,(float)0.254865659604515,(float)0.248927605745720,(float)0.242980179903264,(float)0.237023605994367,(float)0.231058108280671,(float)0.225083911359793,(float)0.219101240156870,(float)0.213110319916091,(float)0.207111376192219,(float)0.201104634842092,(float)0.195090322016128,(float)0.189068664149806,(float)0.183039887955141,(float)0.177004220412149,(float)0.170961888760301,(float)0.164913120489970,(float)0.158858143333861,(float)0.152797185258443,(float)0.146730474455362,(float)0.140658239332849,(float)0.134580708507126,(float)0.128498110793793,(float)0.122410675199216,(float)0.116318630911905,(float)0.110222207293883,(float)0.104121633872055,(float)0.0980171403295607,(float)0.0919089564971329,(float)0.0857973123444400,(float)0.0796824379714300,(float)0.0735645635996673,(float)0.0674439195636641,(float)0.0613207363022087,(float)0.0551952443496899,(float)0.0490676743274178,(float)0.0429382569349409,(float)0.0368072229413589,(float)0.0306748031766369,(float)0.0245412285229122,(float)0.0184067299058042,(float)0.0122715382857193,(float)0.00613588464915602
};

int InitEerVad(void **p_stEerState)
{
  EerState *stEerState;
  stEerState = (EerState *)malloc(sizeof(EerState));

  stEerState->m_fEerVadOnThd = 0.2f;
  stEerState->m_fEerVadLkThd = 0.1f;
  stEerState->m_fEerDamp     = 0.9f;
  memset(stEerState->m_fEerData, 0, sizeof(float) * EER_VAD_FRAMESIZE);
  memset(stEerState->m_fEerSpec, 0, sizeof(short) * (EER_VAD_FRAMEOVER + 1));
  memset(stEerState->m_stEerComplex, 0, sizeof(GccComplex) * EER_VAD_FRAMESIZE);
  memset(stEerState->m_fPreEerRatio, 0, sizeof(short) * 2);

  *p_stEerState = stEerState;

  return GCC_RTMSG_OK;
}

int SetEerVad(void *p_stEerState, float p_fVadOnThd, float p_fVadLkThd)
{
  int iRetCode = GCC_RTMSG_OK;
  int iIndex;
  EerState *stEerState = p_stEerState;

  if (p_fVadOnThd > 0.0f && p_fVadOnThd < 1.0f)
  {
    stEerState->m_fEerVadOnThd = p_fVadOnThd;
  }
  if (p_fVadLkThd > 0.0f && p_fVadLkThd < 1.0f)
  {
    stEerState->m_fEerVadLkThd = p_fVadLkThd;
  }

__end:
  return iRetCode;
}

int DealEerVad(void *p_stEerState, short *p_iVadFlag, float *p_fVadProb, float* p_fWavData, int p_iWavSize, short p_nCorrFlag)
{
  int iRetCode = GCC_RTMSG_OK;
  int iIndex;
  int jIndex;

  float fEngSum  = 0.0f;
  float fSpecSum = 0.0f;
  float fProbSum = 0.0f;    // H
  float fProb    = 0.0f;
  float fEngEntropyRatio   = 0.0f;

  EerState *stEerState = p_stEerState;

  for(iIndex = 0; iIndex < EER_VAD_FRAMEOVER; iIndex++) 
  {
    stEerState->m_fEerData[iIndex] = stEerState->m_fEerData[iIndex + EER_VAD_FRAMEOVER];
    stEerState->m_fEerData[iIndex + EER_VAD_FRAMEOVER] = p_fWavData[iIndex];
  }
  for (iIndex = 0; iIndex < EER_VAD_FRAMESIZE; iIndex++)
  {
    stEerState->m_stEerComplex[iIndex].m_dlReal = stEerState->m_fEerData[iIndex] * m_dlEerHann[iIndex];
    stEerState->m_stEerComplex[iIndex].m_dlImag = 0;
  }
  GCC_FFT(stEerState->m_stEerComplex, EER_VAD_FFTORDER);
  //=============================================================

  for (iIndex = 0; iIndex < EER_VAD_FRAMEOVER + 1; iIndex++)
  {
    stEerState->m_fEerSpec[iIndex] = sqrtf(stEerState->m_stEerComplex[iIndex].m_dlReal * stEerState->m_stEerComplex[iIndex].m_dlReal 
      + stEerState->m_stEerComplex[iIndex].m_dlImag * stEerState->m_stEerComplex[iIndex].m_dlImag);

    fEngSum  += stEerState->m_fEerSpec[iIndex] * stEerState->m_fEerSpec[iIndex];
    fSpecSum += stEerState->m_fEerSpec[iIndex];
  }
  fEngSum = log10f(1.0f + fEngSum / 2.0f);
  for (iIndex = 0; iIndex < EER_VAD_FRAMEOVER + 1; iIndex++)
  {
    fProb = stEerState->m_fEerSpec[iIndex] / (fSpecSum + 1e-10);
    fProbSum += fProb * logf(fProb + 1e-10);
  }
  fEngEntropyRatio = sqrtf(1 + fabs(fEngSum / -(fProbSum + 1e-10)));

  if (fEngEntropyRatio > 2.0f)
  {
    fEngEntropyRatio = 1.0f + stEerState->m_fEerVadLkThd / 10.0f;
  }

  if (p_nCorrFlag > 2)
  {
    fEngEntropyRatio = fEngEntropyRatio * stEerState->m_fEerDamp;
  }

  if (fEngEntropyRatio > 1 + stEerState->m_fEerVadOnThd)
  {
    *p_iVadFlag = EER_VAD_ON;
  }
  else
  {
    if (stEerState->m_fPreEerRatio[0] + stEerState->m_fPreEerRatio[1] + fEngEntropyRatio > 3 * (1 + stEerState->m_fEerVadLkThd))
    {
      *p_iVadFlag = EER_VAD_ON;
    } 
    else
    {
      *p_iVadFlag = EER_VAD_SIL;
    }
  }

  stEerState->m_fPreEerRatio[1] = stEerState->m_fPreEerRatio[0];
  stEerState->m_fPreEerRatio[0] = fEngEntropyRatio;
  *p_fVadProb    = fEngEntropyRatio -1.0f;

__end:
  return iRetCode;
}


int FreeEerVad(void *p_stEerState)
{

  EerState *stEerState = p_stEerState;
  if (stEerState)
  {
    free(stEerState);
    stEerState == NULL;
  }

  return 1;
}
