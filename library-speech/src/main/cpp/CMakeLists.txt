# For more information about using CMake with Android Studio, read the
# documentation: https://d.android.com/studio/projects/add-native-code.html

# Sets the minimum version of CMake required to build the native library.

cmake_minimum_required(VERSION 3.10.2)

set(CMAKE_CXX_FLAGS "${CMAKE_CXX_FLAGS} -std=gnu++11")

add_library( tmk_agc SHARED
        tmk_agc.cpp ./agc/compressor.cpp )

add_library( tmk_doa SHARED
        tmk_doa.cpp ./doa/audio_doa.c )

aux_source_directory(./soundstretch/src Sound_Stretch_SRC_SOURCE)
add_library( tmk_sound_stretch SHARED
        ./soundstretch/tmk_sound_stretch.cpp
        ${Sound_Stretch_SRC_SOURCE}
        )

#set(DIR_AEC_LIB_SRCS
#        aec/dios_ssp_aec/dios_ssp_aec_api.c
#        aec/dios_ssp_aec/dios_ssp_aec_common.c
#        aec/dios_ssp_aec/dios_ssp_aec_doubletalk.c
#        aec/dios_ssp_aec/dios_ssp_aec_erl_est.c
#        aec/dios_ssp_aec/dios_ssp_aec_firfilter.c
#        aec/dios_ssp_aec/dios_ssp_aec_res.c
#        aec/dios_ssp_aec/dios_ssp_aec_tde/dios_ssp_aec_tde.c
#        aec/dios_ssp_aec/dios_ssp_aec_tde/dios_ssp_aec_tde_delay_estimator.c
#        aec/dios_ssp_aec/dios_ssp_aec_tde/dios_ssp_aec_tde_delay_estimator_wrapper.c
#        aec/dios_ssp_aec/dios_ssp_aec_tde/dios_ssp_aec_tde_ring_buffer.c
#
#        aec/dios_ssp_share/dios_ssp_share_cinv.c
#        aec/dios_ssp_share/dios_ssp_share_complex_defs.c
#        aec/dios_ssp_share/dios_ssp_share_noiselevel.c
#        aec/dios_ssp_share/dios_ssp_share_rfft.c
#        aec/dios_ssp_share/dios_ssp_share_subband.c
#        )
add_library( tmk_aec SHARED
        tmk_aec.cpp
        aec/dios_ssp_aec/dios_ssp_aec_api.c
        aec/dios_ssp_aec/dios_ssp_aec_common.c
        aec/dios_ssp_aec/dios_ssp_aec_doubletalk.c
        aec/dios_ssp_aec/dios_ssp_aec_erl_est.c
        aec/dios_ssp_aec/dios_ssp_aec_firfilter.c
        aec/dios_ssp_aec/dios_ssp_aec_res.c
        aec/dios_ssp_aec/dios_ssp_aec_tde/dios_ssp_aec_tde.c
        aec/dios_ssp_aec/dios_ssp_aec_tde/dios_ssp_aec_tde_delay_estimator.c
        aec/dios_ssp_aec/dios_ssp_aec_tde/dios_ssp_aec_tde_delay_estimator_wrapper.c
        aec/dios_ssp_aec/dios_ssp_aec_tde/dios_ssp_aec_tde_ring_buffer.c
        aec/dios_ssp_share/dios_ssp_share_cinv.c
        aec/dios_ssp_share/dios_ssp_share_complex_defs.c
        aec/dios_ssp_share/dios_ssp_share_noiselevel.c
        aec/dios_ssp_share/dios_ssp_share_rfft.c
        aec/dios_ssp_share/dios_ssp_share_subband.c
        )
#aux_source_directory(./aec DIR_AEC_LIB_SRCS)
#include_directories(./aec/dios_ssp_aec)
#add_library( tmk_aec SHARED
#        tmk_aec.cpp ${DIR_AEC_LIB_SRCS} )

aux_source_directory(./vad/TmkVad TmkVad_SRC_SOURCE)
add_library( tmk_vad SHARED
        tmk_vad.cpp
        ./vad/VoiceActivityDetection.cc
        ${TmkVad_SRC_SOURCE}
        )

aux_source_directory(./gcc TmkGcc_SRC_SOURCE)
add_library( tmk_gcc SHARED
        tmk_gcc.cpp
        ${TmkGcc_SRC_SOURCE}
        )

target_link_libraries(
        tmk_agc
        tmk_doa
        tmk_sound_stretch
        tmk_aec
        tmk_gcc
        android
        log )

target_link_libraries(
        tmk_vad
        android
        log )

