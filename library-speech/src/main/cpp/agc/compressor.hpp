/************************************************************
Copyright (C) 2021 ShenZhen TMK . All rights reserved.
FileName : audio_compressor.h
Author : tangzhenhui
Version : V1.0 
Date: 2021-05-28  
Description: 压限器模块头文件
History: 
***********************************************************/

#ifndef _COMPRESSOR_IO_H_
#define _COMPRESSOR_IO_H_

#include <stdio.h>

namespace Audio {
class AudioCOM
{
private:
    float g_fRate;                //Sampling rate (blocks per second)
    float g_fThreshold;            //压限电平 dB
    float g_fClip;                //削波电平 dB
    float g_fRatio;                //compression ratio
    float g_fLookahead;            //lookahead
    float g_fAttack;                //attack time coefficient
    float g_fRelease;                //release  time coefficient
    int   g_iDataSize;             // data size: length of data
    float g_fGs;                    //平滑增益 dB
    float g_fGain;                // 固定增益
    //short g_nInDataOld[160];     //缓存信号
    short* g_nInDataOld;     //缓存信号


public:
    /****************************************************************/
    /*FUN                                                           */
    /*   初始化                                                     */
    /*IN：                                                          */
    /*   门限电平值，削波电平值，增益                               */
    /*OUT：                                                         */
    /*    NULL                                                      */
    /*RTN：                                                         */
    /*    -1 --不成功                                               */
    /*     1 --成功                                                 */
    /****************************************************************/
    int InitCompreLinear(float Threshold, float Clip, float Gain,int iDataSize);

    /****************************************************************/
    /*FUN                                                           */
    /*   处理一帧数据的压限                                         */
    /*IN：                                                          */
    /*   输入信号                                                   */
    /*OUT：                                                         */
    /*   输出信号                                                   */
    /*RTN：                                                         */
    /*    -1 --不成功                                               */
    /*     1 --成功                                                 */
    /****************************************************************/
    int DealCompreLinear(short *input, short *output);

    /****************************************************************/
    /*FUN                                                           */
    /*   压限器内存释放函数                                         */
    /*IN：                                                          */
    /*    NULL                                                      */
    /*OUT：                                                         */
    /*    NULL                                                      */
    /*RTN：                                                         */
    /*    NULL                                                      */
    /****************************************************************/
    int FreeCompreLinear(void);
};
};


#endif //_COMPRESSOR_IO_H_
