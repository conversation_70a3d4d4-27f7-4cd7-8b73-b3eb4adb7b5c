#include <math.h>
#include <stdio.h>
#include <stdlib.h>
#include <string.h>
#include "compressor.hpp"

namespace Audio {
int AudioCOM::InitCompreLinear(float Threshold, float Clip, float Gain,int iDataSize)
{
    if (Threshold > Clip) {
        return -1;
    }
    if (Clip*Gain > 1.0f) {
        return -1;
    }
    this->g_fRate = 16000;            //信号采样率
    this->g_fThreshold = Threshold;        //压限电平
    this->g_fClip = Clip;              //削波电平
    this->g_fRatio = 0.05f;            //压缩率的倒数
    this->g_fLookahead = 0.002f;       //时延 s 时延应该小于11ms
    this->g_fAttack = 0.2532785535f;   //平滑滤波器系数 exp((-log(9))/(fs*TA)
    this->g_fRelease = 0.9996567369f;  //平滑滤波器系数 exp((-log(9))/(fs*TR)
    this->g_fGs =0;                    //平滑增益 dB
    this->g_iDataSize = iDataSize;          //一帧数据长度
    this->g_fGain = Gain;                //固定增益
    short* InDataOld = (short *)malloc(iDataSize * sizeof(short));
    this->g_nInDataOld = InDataOld;
    memset(this->g_nInDataOld, 0, sizeof(short)*this->g_iDataSize);
    return 1;
}

int AudioCOM::DealCompreLinear(short *output,short *input)
{
    if (input == NULL) {
        return -1;
    }
    int iDSampe = 0;
    int iVpu = 0;
    int iFrame;
    float fInput;  //压限器控制部分输入信号
    float fInData;//输入信号dB表示
    float fInDataSC; //输入信号静态特征
    float fGlin[4] = { 0 };
    float fGlinTest = 0;
    float fGc=0; //增益差值 dB
    iDSampe = (int)(this->g_fLookahead*this->g_fRate);
    input[this->g_iDataSize + 1] = 3;
    for (iFrame = 0; iFrame < this->g_iDataSize; iFrame++)
    {
        fInput = input[iFrame] * 0.00003051757812f;
        fInData = fabsf(fInput);
        if (fInData < this->g_fThreshold)
        {
            fInDataSC = fInData;
        }
        else
        {
            fInDataSC = this->g_fThreshold + (fInData - this->g_fThreshold) * this->g_fRatio;
            if (fInDataSC > this->g_fClip)
                fInDataSC = this->g_fClip;
        }    //end of if (inData < this->tThreshold)
        if (fInData > 1e-15)
        {
            fGc = fInDataSC / (fInData+1e-8);
        }
        if (fGc < this->g_fGs)
        {
            this->g_fGs = this->g_fAttack * this->g_fGs + (1 - this->g_fAttack)*fGc;
        }
        else
        {
            this->g_fGs = this->g_fRelease * this->g_fGs + (1 - this->g_fRelease)*fGc;
        }
        fGlinTest = this->g_fGs * this->g_fGain;
        //输出结果
        if (iFrame < iDSampe)
        {
            output[iFrame] = (short)roundf(this->g_nInDataOld[this->g_iDataSize - iDSampe + iFrame]* fGlinTest);
        }
        else
        {
            output[iFrame] = (short)roundf(input[iFrame - iDSampe] * fGlinTest);
        }
        this->g_nInDataOld[iFrame] = input[iFrame];
    }
    return 1;
}//end of audio_compressor

int AudioCOM::FreeCompreLinear(void)
{
    free(this->g_nInDataOld);
    return 0;
}
};
