//
// Created by Decin on 6/21/21.
//

#include <jni.h>
#include <string>

#include "./vad/VoiceActivityDetection.h"

extern "C"
JNIEXPORT jlong JNICALL
Java_co_timekettle_speech_jni_AudioVadJni_createVad(JNIEnv *env, jobject thiz, jint sampleRate, jfloat threhold) {
    auto *vad = new tmkspeech::VoiceActivityDetection(sampleRate, threhold);
    auto ptr = reinterpret_cast<jlong>(vad);
    jclass clazz = env->GetObjectClass(thiz);
    jfieldID fieldID = env->GetFieldID(clazz, "mNativeVad", "J");
    env->SetLongField(thiz, fieldID, ptr);
    return (jlong)vad;
}

extern "C"
JNIEXPORT jboolean JNICALL
Java_co_timekettle_speech_jni_AudioVadJni_isVoise(JNIEnv *env, jobject thiz, jshortArray samples, jlongArray energys) {
    size_t len = env->GetArrayLength(samples);
    jshort *inBuf = env->GetShortArrayElements(samples, nullptr);
    jlong *tEnergys = env->GetLongArrayElements(energys, nullptr);
    auto tEnergy = (uint64_t)tEnergys[0];

    jboolean isVoice = false;
    {
        jclass clazz = env->GetObjectClass(thiz);
        jfieldID fieldID = env->GetFieldID(clazz, "mNativeVad", "J");
        jlong ptr = env->GetLongField(thiz, fieldID);
        auto vad = reinterpret_cast<tmkspeech::VoiceActivityDetection *>(ptr);
        isVoice = vad->IsVoice(inBuf, len, tEnergy);
    }

    tEnergys[0] = (jlong)tEnergy;
    env->ReleaseShortArrayElements((jshortArray)samples, inBuf, 0);
    env->ReleaseLongArrayElements((jlongArray)energys, tEnergys, 0);
    return isVoice;
}

extern "C"
JNIEXPORT void JNICALL
Java_co_timekettle_speech_jni_AudioVadJni_destoryVad(JNIEnv *env, jobject thiz) {
    jclass clazz = env->GetObjectClass(thiz);
    jfieldID fieldID = env->GetFieldID(clazz, "mNativeVad", "J");
    jlong ptr = env->GetLongField(thiz, fieldID);
    auto vad = reinterpret_cast<tmkspeech::VoiceActivityDetection *>(ptr);
    delete vad;
}