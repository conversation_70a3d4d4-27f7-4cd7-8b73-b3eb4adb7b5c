//
// Created by Decin on 1/20/23.
//

#include <jni.h>

#ifdef __cplusplus
extern "C" {
#endif

#include "gcc/2mic_audio_gcc.h"

#ifdef __cplusplus
}
#endif

extern "C"
JNIEXPORT jint JNICALL
Java_com_damly_ispeech_Mic2GccJni_Init2MicGcc(JNIEnv *env, jobject thiz) {
    return Init2MicGcc();
}

extern "C"
JNIEXPORT jint JNICALL
Java_com_damly_ispeech_Mic2GccJni_Set2MicGcc(JNIEnv *env, jobject thiz, jfloat jfHCorrTH, jfloat jfLCorrTH, jfloat jfVadOnTH, jfloat jfVadLkTH) {
    // 将 JNI 参数转换为本地数据类型
    float p_fHCorrTH = static_cast<float>(jfHCorrTH);
    float p_fLCorrTH = static_cast<float>(jfLCorrTH);
    float p_fVadOnThd = static_cast<float>(jfVadOnTH);
    float p_fVadLkThd = static_cast<float>(jfVadLkTH);

    // 调用本地方法 Set2MicGcc
    jint result = Set2MicGcc(p_fHCorrTH, p_fLCorrTH, p_fVadOnThd, p_fVadLkThd);

    return result;
}

extern "C"
JNIEXPORT jint JNICALL
Java_com_damly_ispeech_Mic2GccJni_Deal2MicGcc(JNIEnv *env, jobject thiz, jshortArray jnWaveOut, jshortArray jnVadFlag, jfloatArray jfCorrNF, jshortArray jnWaveIn) {
    // 获取 JNI 数组的指针和长度
    jshort *p_nWaveOut = env->GetShortArrayElements(jnWaveOut, NULL);
    jshort *p_nVadFlag = env->GetShortArrayElements(jnVadFlag, NULL);
    jfloat *p_fCorrNF = env->GetFloatArrayElements(jfCorrNF, NULL);
    jshort *p_nWaveIn = env->GetShortArrayElements(jnWaveIn, NULL);

    // 调用本地方法 Deal2MicGcc
    jint result = Deal2MicGcc(p_nWaveOut, p_nVadFlag, p_nVadFlag + 1, p_fCorrNF, p_nWaveIn);

    // 释放 JNI 数组
    env->ReleaseShortArrayElements(jnWaveOut, p_nWaveOut, 0);
    env->ReleaseShortArrayElements(jnVadFlag, p_nVadFlag, 0);
    env->ReleaseFloatArrayElements(jfCorrNF, p_fCorrNF, 0);
    env->ReleaseShortArrayElements(jnWaveIn, p_nWaveIn, 0);

    return result;
}

extern "C"
JNIEXPORT jint JNICALL
Java_com_damly_ispeech_Mic2GccJni_Free2MicGcc(JNIEnv *env, jobject thiz) {
    return Free2MicGcc();
}