#include <jni.h>
#include <string>
#include "./doa/audio_doa.h"
#include <android/log.h>

#define LOGI(fmt, ...) ((void)__android_log_print(ANDROID_LOG_INFO, "doa::", fmt, __VA_ARGS__))

doaPara doa_str; //DOA 结构体

extern "C"
JNIEXPORT jlong JNICALL
Java_co_timekettle_speech_ispeech_algorithm_AudioDoaJni_createDoa(JNIEnv *env, jobject thiz, jfloat fDoaTh) {
    if (fDoaTh == 0.0) fDoaTh = 0.01; // 门限值, 默认 0.01，需要暴露 取值在0~0.02
    doa_init(&doa_str, fDoaTh);
    return (jlong)&doa_str;
}

extern "C"
JNIEXPORT void JNICALL
Java_co_timekettle_speech_ispeech_algorithm_AudioDoaJni_processDoa(JNIEnv *env, jobject thiz, jlong addr, jshortArray input_data, jintArray output_dir) {
    short *inBuf = env->GetShortArrayElements(input_data, nullptr);
    int *outBuf = env->GetIntArrayElements(output_dir, nullptr);

    doa_process(inBuf, outBuf, &doa_str);
//    __android_log_print(ANDROID_LOG_INFO, "==", "====== %d", *outBuf);
    env->ReleaseShortArrayElements((jshortArray)input_data, inBuf, 0);
    env->ReleaseIntArrayElements((jintArray)output_dir, outBuf, 0);
}

extern "C"
JNIEXPORT void JNICALL
Java_co_timekettle_speech_ispeech_algorithm_AudioDoaJni_destoryDoa(JNIEnv *env, jobject thiz, jlong addr) {

}