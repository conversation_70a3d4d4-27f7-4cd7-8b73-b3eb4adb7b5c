//
// Created by Decin on 6/21/21.
//

#include <jni.h>
#include <string>

#include "./agc/compressor.hpp"

extern "C"
JNIEXPORT jlong JNICALL
Java_com_damly_ispeech_client_AgcJni_createAgc(JNIEnv *env, jobject thiz, jfloat fThreshold, jfloat fClip, jfloat fGain, jint iDataSize) {
    auto *agc = new Audio::AudioCOM();

//    float fThreshold = 0.2f;         // 压限电平
//    float fClip = 0.25f;              // 削波电平
//    float fGain = 4.0f;
    agc->InitCompreLinear(fThreshold, fClip, fGain, iDataSize);

    return (jlong)agc;
}

extern "C"
JNIEXPORT void JNICALL
Java_com_damly_ispeech_client_AgcJni_processAgc(JNIEnv *env, jobject thiz, jlong addr, jshortArray input_data, jshortArray output_data) {
    auto *agc = (Audio::AudioCOM *)addr;
    short *inBuf = env->GetShortArrayElements(input_data, nullptr);
    short *outBuf = env->GetShortArrayElements(output_data, nullptr);

    agc->DealCompreLinear(inBuf, outBuf);

    env->ReleaseShortArrayElements((jshortArray)input_data, inBuf, 0);
    env->ReleaseShortArrayElements((jshortArray)output_data, outBuf, 0);
}

extern "C"
JNIEXPORT void JNICALL
Java_com_damly_ispeech_client_AgcJni_destoryAgc(JNIEnv *env, jobject thiz, jlong addr) {
    auto *agc = (Audio::AudioCOM *)addr;
    agc->FreeCompreLinear();
}
