#include"audio_doa.h"
#include <string.h>
#include <stdio.h>

const float g_dlHamming[FFTSIZE] = {
0.08000000f,0.08003477f,0.08013909f,0.08031292f,0.08055626f,0.08086906f,0.08125127f,0.08170284f,0.08222370f,0.08281376f,0.08347295f,0.08420116f,0.08499828f,0.08586418f,0.08679875f,0.08780184f,
0.08887329f,0.09001294f,0.09122063f,0.09249617f,0.09383936f,0.09525001f,0.09672789f,0.09827280f,0.09988448f,0.10156271f,0.10330722f,0.10511775f,0.10699403f,0.10893578f,0.11094270f,0.11301448f,
0.11515082f,0.11735139f,0.11961586f,0.12194389f,0.12433512f,0.12678919f,0.12930573f,0.13188437f,0.13452471f,0.13722635f,0.13998888f,0.14281189f,0.14569495f,0.14863762f,0.15163946f,0.15470002f,
0.15781883f,0.16099542f,0.16422931f,0.16752001f,0.17086702f,0.17426984f,0.17772796f,0.18124085f,0.18480797f,0.18842880f,0.19210278f,0.19582935f,0.19960796f,0.20343803f,0.20731899f,0.21125024f,
0.21523120f,0.21926125f,0.22333980f,0.22746622f,0.23163990f,0.23586019f,0.24012646f,0.24443807f,0.24879437f,0.25319469f,0.25763837f,0.26212475f,0.26665313f,0.27122284f,0.27583319f,0.28048347f,
0.28517299f,0.28990103f,0.29466689f,0.29946984f,0.30430915f,0.30918410f,0.31409394f,0.31903793f,0.32401534f,0.32902539f,0.33406735f,0.33914043f,0.34424389f,0.34937694f,0.35453881f,0.35972872f,
0.36494588f,0.37018951f,0.37545881f,0.38075299f,0.38607124f,0.39141277f,0.39677676f,0.40216240f,0.40756889f,0.41299539f,0.41844110f,0.42390518f,0.42938682f,0.43488518f,0.44039943f,0.44592874f,
0.45147227f,0.45702919f,0.46259865f,0.46817981f,0.47377183f,0.47937386f,0.48498506f,0.49060458f,0.49623157f,0.50186517f,0.50750453f,0.51314881f,0.51879715f,0.52444870f,0.53010260f,0.53575799f,
0.54141402f,0.54706984f,0.55272459f,0.55837742f,0.56402747f,0.56967389f,0.57531582f,0.58095241f,0.58658281f,0.59220616f,0.59782163f,0.60342835f,0.60902548f,0.61461218f,0.62018760f,0.62575089f,
0.63130122f,0.63683774f,0.64235963f,0.64786604f,0.65335614f,0.65882910f,0.66428410f,0.66972031f,0.67513691f,0.68053307f,0.68590799f,0.69126085f,0.69659084f,0.70189716f,0.70717900f,0.71243557f,
0.71766606f,0.72286969f,0.72804568f,0.73319324f,0.73831159f,0.74339995f,0.74845757f,0.75348367f,0.75847749f,0.76343829f,0.76836530f,0.77325778f,0.77811501f,0.78293623f,0.78772072f,0.79246776f,
0.79717663f,0.80184662f,0.80647702f,0.81106714f,0.81561627f,0.82012373f,0.82458885f,0.82901093f,0.83338932f,0.83772336f,0.84201238f,0.84625575f,0.85045281f,0.85460293f,0.85870550f,0.86275987f,
0.86676546f,0.87072163f,0.87462781f,0.87848340f,0.88228781f,0.88604048f,0.88974082f,0.89338829f,0.89698234f,0.90052241f,0.90400798f,0.90743851f,0.91081349f,0.91413241f,0.91739477f,0.92060007f,
0.92374783f,0.92683757f,0.92986882f,0.93284114f,0.93575406f,0.93860715f,0.94139997f,0.94413211f,0.94680315f,0.94941269f,0.95196032f,0.95444568f,0.95686838f,0.95922805f,0.96152434f,0.96375690f,
0.96592540f,0.96802950f,0.97006889f,0.97204326f,0.97395231f,0.97579575f,0.97757331f,0.97928471f,0.98092969f,0.98250802f,0.98401944f,0.98546374f,0.98684068f,0.98815007f,0.98939171f,0.99056540f,
0.99167097f,0.99270826f,0.99367711f,0.99457736f,0.99540889f,0.99617157f,0.99686528f,0.99748992f,0.99804539f,0.99853161f,0.99894851f,0.99929602f,0.99957409f,0.99978268f,0.99992176f,0.99999131f,
0.99999131f,0.99992176f,0.99978268f,0.99957409f,0.99929602f,0.99894851f,0.99853161f,0.99804539f,0.99748992f,0.99686528f,0.99617157f,0.99540889f,0.99457736f,0.99367711f,0.99270826f,0.99167097f,
0.99056540f,0.98939171f,0.98815007f,0.98684068f,0.98546374f,0.98401944f,0.98250802f,0.98092969f,0.97928471f,0.97757331f,0.97579575f,0.97395231f,0.97204326f,0.97006889f,0.96802950f,0.96592540f,
0.96375690f,0.96152434f,0.95922805f,0.95686838f,0.95444568f,0.95196032f,0.94941269f,0.94680315f,0.94413211f,0.94139997f,0.93860715f,0.93575406f,0.93284114f,0.92986882f,0.92683757f,0.92374783f,
0.92060007f,0.91739477f,0.91413241f,0.91081349f,0.90743851f,0.90400798f,0.90052241f,0.89698234f,0.89338829f,0.88974082f,0.88604048f,0.88228781f,0.87848340f,0.87462781f,0.87072163f,0.86676546f,
0.86275987f,0.85870550f,0.85460293f,0.85045281f,0.84625575f,0.84201238f,0.83772336f,0.83338932f,0.82901093f,0.82458885f,0.82012373f,0.81561627f,0.81106714f,0.80647702f,0.80184662f,0.79717663f,
0.79246776f,0.78772072f,0.78293623f,0.77811501f,0.77325778f,0.76836530f,0.76343829f,0.75847749f,0.75348367f,0.74845757f,0.74339995f,0.73831159f,0.73319324f,0.72804568f,0.72286969f,0.71766606f,
0.71243557f,0.70717900f,0.70189716f,0.69659084f,0.69126085f,0.68590799f,0.68053307f,0.67513691f,0.66972031f,0.66428410f,0.65882910f,0.65335614f,0.64786604f,0.64235963f,0.63683774f,0.63130122f,
0.62575089f,0.62018760f,0.61461218f,0.60902548f,0.60342835f,0.59782163f,0.59220616f,0.58658281f,0.58095241f,0.57531582f,0.56967389f,0.56402747f,0.55837742f,0.55272459f,0.54706984f,0.54141402f,
0.53575799f,0.53010260f,0.52444870f,0.51879715f,0.51314881f,0.50750453f,0.50186517f,0.49623157f,0.49060458f,0.48498506f,0.47937386f,0.47377183f,0.46817981f,0.46259865f,0.45702919f,0.45147227f,
0.44592874f,0.44039943f,0.43488518f,0.42938682f,0.42390518f,0.41844110f,0.41299539f,0.40756889f,0.40216240f,0.39677676f,0.39141277f,0.38607124f,0.38075299f,0.37545881f,0.37018951f,0.36494588f,
0.35972872f,0.35453881f,0.34937694f,0.34424389f,0.33914043f,0.33406735f,0.32902539f,0.32401534f,0.31903793f,0.31409394f,0.30918410f,0.30430915f,0.29946984f,0.29466689f,0.28990103f,0.28517299f,
0.28048347f,0.27583319f,0.27122284f,0.26665313f,0.26212475f,0.25763837f,0.25319469f,0.24879437f,0.24443807f,0.24012646f,0.23586019f,0.23163990f,0.22746622f,0.22333980f,0.21926125f,0.21523120f,
0.21125024f,0.20731899f,0.20343803f,0.19960796f,0.19582935f,0.19210278f,0.18842880f,0.18480797f,0.18124085f,0.17772796f,0.17426984f,0.17086702f,0.16752001f,0.16422931f,0.16099542f,0.15781883f,
0.15470002f,0.15163946f,0.14863762f,0.14569495f,0.14281189f,0.13998888f,0.13722635f,0.13452471f,0.13188437f,0.12930573f,0.12678919f,0.12433512f,0.12194389f,0.11961586f,0.11735139f,0.11515082f,
0.11301448f,0.11094270f,0.10893578f,0.10699403f,0.10511775f,0.10330722f,0.10156271f,0.09988448f,0.09827280f,0.09672789f,0.09525001f,0.09383936f,0.09249617f,0.09122063f,0.09001294f,0.08887329f,
0.08780184f,0.08679875f,0.08586418f,0.08499828f,0.08420116f,0.08347295f,0.08281376f,0.08222370f,0.08170284f,0.08125127f,0.08086906f,0.08055626f,0.08031292f,0.08013909f,0.08003477f,0.08000000f,
};

const static float g_fPiSin[FFTSIZE] = {
  (float)-0.000000, (float)-1.000000, (float)-0.707107, (float)-0.382683, (float)-0.195090, (float)-0.098017, (float)-0.049068, (float)-0.024541, (float)-0.012272
};

const static float g_fPiCos[FFTSIZE] = {
  (float)-1.00000, (float)0.000000, (float)0.707107, (float)0.923880, (float)0.980785, (float)0.995185, (float)0.998795, (float)0.999699, (float)0.999925
};
void doa_init(doaPara *str, float th) {
    str->iFrameLen = FFTSIZE;
    str->pWin = (float *) &g_dlHamming;
    str->iFreqBand = (int)(FREQBAND * str->iFrameLen / FSHALF);
    str->fSRw = 0;
    str->fSRwd = 0;
    str->fAlpha = 0.15;
    str->fTh = th;
    memset(str->iFlag, 0, sizeof(int) * FLAGBUFF);
    str->iIndex = 0;
    str->iState = -1;
}

Complex doa_complex_divi(Complex D1, Complex D2) {
    float divi = D2.m_dlReal * D2.m_dlReal + D2.m_dlImag * D2.m_dlImag;
    Complex D3;
    D3.m_dlReal = (D1.m_dlReal * D2.m_dlReal + D1.m_dlImag * D2.m_dlImag) / divi;
    D3.m_dlImag = -(D1.m_dlReal * D2.m_dlImag - D1.m_dlImag * D2.m_dlReal) / divi;
    return D3;
}
Complex doa_complex_mult(Complex D1, Complex D2) {
    Complex D3;
    D3.m_dlReal = D1.m_dlReal * D2.m_dlReal - D1.m_dlImag * D2.m_dlImag;
    D3.m_dlImag = D1.m_dlReal * D2.m_dlImag + D1.m_dlImag * D2.m_dlReal;
    return D3;
}
void doa_fft(Complex *data, int order) {
    int n, i, nv2, j, k, le, l, le1, ip, nm1;
    Complex t, u, w;

    n = 1;
    for (i = 0; i < order; i++)
        n = n * 2;

    nv2 = n / 2;
    nm1 = n - 1;
    j = 1;

    for (i = 1; i <= nm1; i++) {
        if (i < j) {
            t.m_dlReal = data[i - 1].m_dlReal;
            t.m_dlImag = data[i - 1].m_dlImag;
            data[i - 1].m_dlReal = data[j - 1].m_dlReal;
            data[i - 1].m_dlImag = data[j - 1].m_dlImag;
            data[j - 1].m_dlReal = t.m_dlReal;
            data[j - 1].m_dlImag = t.m_dlImag;
        }

        k = nv2;

        while (k < j) {
            j -= k;
            k /= 2;
        }
        j += k;
    }

    le = 1;
    for (l = 1; l <= order; l++) {
        le *= 2;
        le1 = le / 2;
        u.m_dlReal = 1;
        u.m_dlImag = 0;
        //w.m_dlReal = (cos(PI / le1)) ;
        //w.m_dlImag =(-sin(PI / le1)) ;
        w.m_dlReal = g_fPiCos[l - 1];
        w.m_dlImag = g_fPiSin[l - 1];

        for (j = 1; j <= le1; j++) {
            for (i = j; i <= n; i += le) {
                ip = i + le1;
                t.m_dlReal = (data[ip - 1].m_dlReal * u.m_dlReal - data[ip - 1].m_dlImag * u.m_dlImag);
                t.m_dlImag = (data[ip - 1].m_dlReal * u.m_dlImag + data[ip - 1].m_dlImag * u.m_dlReal);
                data[ip - 1].m_dlReal = data[i - 1].m_dlReal - t.m_dlReal;
                data[ip - 1].m_dlImag = data[i - 1].m_dlImag - t.m_dlImag;
                data[i - 1].m_dlReal = t.m_dlReal + data[i - 1].m_dlReal;
                data[i - 1].m_dlImag = t.m_dlImag + data[i - 1].m_dlImag;
            }

            t.m_dlReal = u.m_dlReal * w.m_dlReal - u.m_dlImag * w.m_dlImag;
            t.m_dlImag = u.m_dlImag * w.m_dlReal + u.m_dlReal * w.m_dlImag;
            u.m_dlReal = t.m_dlReal;
            u.m_dlImag = t.m_dlImag;
        }
    }
}
void doa_process(const short *indata, int *doa_dir, doaPara *str) {
    int iIndex = 0;
    int jIndex = 0;
    Complex g_stComplex[2][FFTSIZE];
    Complex R21;
    Complex ctemp1;
    float normAngle;
    float sum[2];
    int freq;
    float ftemp2;
    Complex normR21;
    float Rw;
    float itemp;
    float doa;
    for (jIndex = 0; jIndex < 2; jIndex++) {
        for (iIndex = 0; iIndex < FFTSIZE; iIndex++) {
            g_stComplex[jIndex][iIndex].m_dlReal = (float)indata[iIndex * 2 + jIndex] * str->pWin[iIndex];
            g_stComplex[jIndex][iIndex].m_dlImag = 0;
        }
    }
    doa_fft(&g_stComplex[0][0], FFTORDER);
    doa_fft(&g_stComplex[1][0], FFTORDER);
    sum[0] = 0;
    sum[1] = 0;
    for (iIndex = 1; iIndex < str->iFreqBand; iIndex++) {
        R21 = doa_complex_divi(g_stComplex[1][iIndex], g_stComplex[0][iIndex]);
        ctemp1 = doa_complex_mult(g_stComplex[1][iIndex], g_stComplex[0][iIndex]);
        ftemp2 = ctemp1.m_dlReal * ctemp1.m_dlReal +
                 ctemp1.m_dlImag * ctemp1.m_dlImag;
        freq = iIndex + 1;
        Rw = ftemp2 * freq * freq * freq * freq * freq * freq;
        itemp = sqrt(R21.m_dlReal * R21.m_dlReal + R21.m_dlImag * R21.m_dlImag);
        normR21.m_dlReal = R21.m_dlReal / itemp;
        normR21.m_dlImag = R21.m_dlImag / itemp;
        normAngle = atan2(normR21.m_dlImag, normR21.m_dlReal) / freq;
        sum[0] += Rw;
        sum[1] += Rw * normAngle;
    }
    str->fSRw = (1 - str->fAlpha) * str->fSRw + str->fAlpha * sum[0];
    str->fSRwd = (1 - str->fAlpha) * str->fSRwd + str->fAlpha * sum[1];

    doa = str->fSRwd / str->fSRw;
    if (doa > str->fTh) {
        str->iFlag[str->iIndex] = 1;
    } else {
        if (doa < -str->fTh) {
            str->iFlag[str->iIndex] = -1;
        } else {
            str->iFlag[str->iIndex] = 0;
        }
    }
    str->iIndex += 1;
    if (str->iIndex == FLAGBUFF) {
        str->iIndex = 0;
    }
    sum[0] = 0;
    for (iIndex = 0; iIndex < FLAGBUFF; iIndex++) {
        sum[0] += str->iFlag[iIndex];
    }
    if (str->iState == 1) {
        if (sum[0] <= -FLAGTH) {
            str->iState = -1;
        }
    } else {
        if (sum[0] >= FLAGTH) {
            str->iState = 1;
        }
    }
    *doa_dir = str->iState;
    printf("========= %d", *doa_dir);


}
