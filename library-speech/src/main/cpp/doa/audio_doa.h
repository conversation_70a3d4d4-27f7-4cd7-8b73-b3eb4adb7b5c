#ifndef _AUDIO_DOA_H_
#define _AUDIO_DOA_H_

#include <math.h>

#define FFTSIZE 512
#define FFTORDER 9
#define FS 16000
#define FSHALF 8000
#define FREQBAND 500
#define FLAGBUFF 6
#define FLAGTH 4

#ifdef __cplusplus
extern "C" {
#endif

typedef struct _complex_ {
    float m_dlReal;
    float m_dlImag;
} Complex;
typedef struct {
    int iFrameLen;
    float *pWin;
    int iFreqBand;
    float fAlpha;
    float fTh;
    float fSRw;
    float fSRwd;
    int iFlag[FLAGBUFF];
    int iIndex;
    int iState;
} doaPara;

//门限值，需要暴露 取值在0~0.02
void doa_init(doaPara *str, float th);

//readDoa,输入的双通道数据，奇偶交替，每个通道512个点 short类型
//doa_dir,输出的标志信息，'1'表示通道1,'-1'表示通道2 int类型
//doa_str,doa结构体 doaPara类型
void doa_process(const short *indata, int *doa_dir, doaPara *str);

#ifdef __cplusplus
}
#endif

#endif
