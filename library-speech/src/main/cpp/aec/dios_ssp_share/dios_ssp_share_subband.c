/* Copyright (C) 2017 Beijing Didi Infinity Technology and Development Co.,Ltd.
All rights reserved.

Licensed under the Apache License, Version 2.0 (the "License");
you may not use this file except in compliance with the License.
You may obtain a copy of the License at

	http://www.apache.org/licenses/LICENSE-2.0

Unless required by applicable law or agreed to in writing, software
distributed under the License is distributed on an "AS IS" BASIS,
WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
See the License for the specific language governing permissions and
limitations under the License.

Description: Using FFT for subband analysis and subband synthesis.
==============================================================================*/

#include "dios_ssp_share_subband.h"

objSubBand* dios_ssp_share_subband_init(int frm_len)
{
	int i;
	float subband_filter_coef[] = {
		-0.0000407034f,	-0.0000476284f,	-0.0000497470f,	-0.0000516895f,	-0.0000537149f,	-0.0000557572f,
		-0.0000577796f,	-0.0000598974f,	-0.0000620104f,	-0.0000642049f,	-0.0000664541f,	-0.0000687551f,
		-0.0000711183f,	-0.0000735340f,	-0.0000760097f,	-0.0000785414f,	-0.0000811338f,	-0.0000837814f,
		-0.0000864739f,	-0.0000892298f,	-0.0000920551f,	-0.0000949393f,	-0.0000978817f,	-0.0001008754f,
		-0.0001039352f,	-0.0001070252f,	-0.0001101995f,	-0.0001134358f,	-0.0001167281f,	-0.0001200761f,
		-0.0001234684f,	-0.0001269258f,	-0.0001304267f,	-0.0001339901f,	-0.0001376061f,	-0.0001412707f,
		-0.0001449862f,	-0.0001487494f,	-0.0001525574f,	-0.0001564102f,	-0.0001603067f,	-0.0001642468f,
		-0.0001682265f,	-0.0001722442f,	-0.0001762978f,	-0.0001803851f,	-0.0001845040f,	-0.0001886533f,
		-0.0001928243f,	-0.0001970209f,	-0.0002012436f,	-0.0002054872f,	-0.0002097476f,	-0.0002140225f,
		-0.0002183098f,	-0.0002226066f,	-0.0002269103f,	-0.0002312180f,	-0.0002355272f,	-0.0002398348f,
		-0.0002441381f,	-0.0002484342f,	-0.0002527203f,	-0.0002569898f,	-0.0002612470f,	-0.0002654888f,
		-0.0002697054f,	-0.0002738975f,	-0.0002780619f,	-0.0002821959f,	-0.0002862966f,	-0.0002903610f,
		-0.0002943862f,	-0.0002983695f,	-0.0003023079f,	-0.0003061987f,	-0.0003100395f,	-0.0003138266f,
		-0.0003175586f,	-0.0003212337f,	-0.0003248456f,	-0.0003283952f,	-0.0003318758f,	-0.0003352870f,
		-0.0003386259f,	-0.0003418907f,	-0.0003450785f,	-0.0003481871f,	-0.0003512177f,	-0.0003541617f,
		-0.0003570143f,	-0.0003597890f,	-0.0003624651f,	-0.0003650493f,	-0.0003675341f,	-0.0003699217f,
		-0.0003722254f,	-0.0003744058f,	-0.0003765010f,	-0.0003784697f,	-0.0003803282f,	-0.0003820732f,
		-0.0003837052f,	-0.0003852663f,	-0.0003866714f,	-0.0003879524f,	-0.0003891103f,	-0.0003901412f,
		-0.0003910433f,	-0.0003918200f,	-0.0003924589f,	-0.0003929717f,	-0.0003933405f,	-0.0003935787f,
		-0.0003936720f,	-0.0003936296f,	-0.0003934486f,	-0.0003931095f,	-0.0003926260f,	-0.0003919914f,
		-0.0003911182f,	-0.0003902105f,	-0.0003890564f,	-0.0003878771f,	-0.0003867614f,	-0.0003854122f,
		-0.0003849944f,	-0.0003850045f,	-0.0003766406f,	-0.0003743459f,	-0.0003723628f,	-0.0003694077f,
		-0.0003662166f,	-0.0003627268f,	-0.0003588938f,	-0.0003549745f,	-0.0003507469f,	-0.0003463883f,
		-0.0003418302f,	-0.0003370620f,	-0.0003321004f,	-0.0003269213f,	-0.0003215370f,	-0.0003159339f,
		-0.0003101215f,	-0.0003040791f,	-0.0002978212f,	-0.0002913365f,	-0.0002846206f,	-0.0002776732f,
		-0.0002704903f,	-0.0002630723f,	-0.0002554096f,	-0.0002474674f,	-0.0002393277f,	-0.0002309411f,
		-0.0002223036f,	-0.0002134137f,	-0.0002042521f,	-0.0001948524f,	-0.0001851768f,	-0.0001752566f,
		-0.0001650749f,	-0.0001546259f,	-0.0001439137f,	-0.0001329293f,	-0.0001216807f,	-0.0001101574f,
		-0.0000983624f,	-0.0000862956f,	-0.0000739531f,	-0.0000613341f,	-0.0000484370f,	-0.0000352609f,
		-0.0000218045f,	-0.0000080682f,	0.0000059360f,	0.0000202317f,	0.0000348167f,	0.0000496844f,
		0.0000648370f,	0.0000802742f,	0.0000959961f,	0.0001120027f,	0.0001282941f,	0.0001448702f,
		0.0001617307f,	0.0001788755f,	0.0001963041f,	0.0002140161f,	0.0002320109f,	0.0002502843f,
		0.0002688426f,	0.0002876848f,	0.0003068031f,	0.0003261999f,	0.0003458742f,	0.0003658246f,
		0.0003860499f,	0.0004065487f,	0.0004273195f,	0.0004483607f,	0.0004696708f,	0.0004912480f,
		0.0005130901f,	0.0005351961f,	0.0005575627f,	0.0005801874f,	0.0006030709f,	0.0006262083f,
		0.0006496005f,	0.0006732436f,	0.0006971353f,	0.0007212725f,	0.0007456531f,	0.0007702743f,
		0.0007951306f,	0.0008202241f,	0.0008455396f,	0.0008711046f,	0.0008968879f,	0.0009228942f,
		0.0009491236f,	0.0009755701f,	0.0010022204f,	0.0010290952f,	0.0010561655f,	0.0010834532f,
		0.0011109420f,	0.0011386302f,	0.0011665121f,	0.0011945562f,	0.0012228163f,	0.0012512591f,
		0.0012798846f,	0.0013086866f,	0.0013376615f,	0.0013668007f,	0.0013961072f,	0.0014255711f,
		0.0014551972f,	0.0014849719f,	0.0015148979f,	0.0015449648f,	0.0015751686f,	0.0016055137f,
		0.0016359873f,	0.0016665871f,	0.0016973527f,	0.0017281769f,	0.0017591629f,	0.0017901930f,
		0.0018212500f,	0.0018524304f,	0.0018833181f,	0.0019134220f,	0.0019483039f,	0.0019792359f,
		0.0020108511f,	0.0020427436f,	0.0020747197f,	0.0021068004f,	0.0021389836f,	0.0021711825f,
		0.0022034559f,	0.0022357439f,	0.0022680591f,	0.0023003988f,	0.0023327520f,	0.0023651201f,
		0.0023974939f,	0.0024298709f,	0.0024622425f,	0.0024946105f,	0.0025269630f,	0.0025592953f,
		0.0025915997f,	0.0026238724f,	0.0026561081f,	0.0026883053f,	0.0027204544f,	0.0027525643f,
		0.0027846034f,	0.0028165786f,	0.0028484850f,	0.0028803144f,	0.0029120664f,	0.0029437284f,
		0.0029753004f,	0.0030067702f,	0.0030381355f,	0.0030693910f,	0.0031005313f,	0.0031315480f,
		0.0031624412f,	0.0031931983f,	0.0032238162f,	0.0032542880f,	0.0032846087f,	0.0033147723f,
		0.0033447730f,	0.0033746050f,	0.0034042624f,	0.0034337392f,	0.0034630309f,	0.0034921303f,
		0.0035210312f,	0.0035497284f,	0.0035782165f,	0.0036064895f,	0.0036345418f,	0.0036623658f,
		0.0036899511f,	0.0037172999f,	0.0037444065f,	0.0037712657f,	0.0037978705f,	0.0038242198f,
		0.0038502967f,	0.0038761262f,	0.0039016649f,	0.0039269074f,	0.0039518867f,	0.0039765675f,
		0.0040009559f,	0.0040250427f,	0.0040488245f,	0.0040722959f,	0.0040954523f,	0.0041182889f,
		0.0041407933f,	0.0041629652f,	0.0041848021f,	0.0042062996f,	0.0042274533f,	0.0042482590f,
		0.0042687123f,	0.0042888083f,	0.0043085435f,	0.0043279137f,	0.0043469148f,	0.0043655429f,
		0.0043837943f,	0.0044016650f,	0.0044191513f,	0.0044362500f,	0.0044529592f,	0.0044692684f,
		0.0044851818f,	0.0045006956f,	0.0045158045f,	0.0045305054f,	0.0045447967f,	0.0045586711f,
		0.0045721317f,	0.0045851709f,	0.0045977851f,	0.0046099752f,	0.0046217384f,	0.0046330775f,
		0.0046439836f,	0.0046544538f,	0.0046644926f,	0.0046740903f,	0.0046832450f,	0.0046919527f,
		0.0047002143f,	0.0047080299f,	0.0047154004f,	0.0047223234f,	0.0047287954f,	0.0047348169f,
		0.0047403842f,	0.0047454978f,	0.0047501543f,	0.0047543527f,	0.0047581060f,	0.0047613896f,
		0.0047642393f,	0.0047666143f,	0.0047685628f,	0.0047700344f,	0.0047710324f,	0.0047717806f,
		0.0047717806f,	0.0047710324f,	0.0047700344f,	0.0047685628f,	0.0047666143f,	0.0047642393f,
		0.0047613896f,	0.0047581060f,	0.0047543527f,	0.0047501543f,	0.0047454978f,	0.0047403842f,
		0.0047348169f,	0.0047287954f,	0.0047223234f,	0.0047154004f,	0.0047080299f,	0.0047002143f,
		0.0046919527f,	0.0046832450f,	0.0046740903f,	0.0046644926f,	0.0046544538f,	0.0046439836f,
		0.0046330775f,	0.0046217384f,	0.0046099752f,	0.0045977851f,	0.0045851709f,	0.0045721317f,
		0.0045586711f,	0.0045447967f,	0.0045305054f,	0.0045158045f,	0.0045006956f,	0.0044851818f,
		0.0044692684f,	0.0044529592f,	0.0044362500f,	0.0044191513f,	0.0044016650f,	0.0043837943f,
		0.0043655429f,	0.0043469148f,	0.0043279137f,	0.0043085435f,	0.0042888083f,	0.0042687123f,
		0.0042482590f,	0.0042274533f,	0.0042062996f,	0.0041848021f,	0.0041629652f,	0.0041407933f,
		0.0041182889f,	0.0040954523f,	0.0040722959f,	0.0040488245f,	0.0040250427f,	0.0040009559f,
		0.0039765675f,	0.0039518867f,	0.0039269074f,	0.0039016649f,	0.0038761262f,	0.0038502967f,
		0.0038242198f,	0.0037978705f,	0.0037712657f,	0.0037444065f,	0.0037172999f,	0.0036899511f,
		0.0036623658f,	0.0036345418f,	0.0036064895f,	0.0035782165f,	0.0035497284f,	0.0035210312f,
		0.0034921303f,	0.0034630309f,	0.0034337392f,	0.0034042624f,	0.0033746050f,	0.0033447730f,
		0.0033147723f,	0.0032846087f,	0.0032542880f,	0.0032238162f,	0.0031931983f,	0.0031624412f,
		0.0031315480f,	0.0031005313f,	0.0030693910f,	0.0030381355f,	0.0030067702f,	0.0029753004f,
		0.0029437284f,	0.0029120664f,	0.0028803144f,	0.0028484850f,	0.0028165786f,	0.0027846034f,
		0.0027525643f,	0.0027204544f,	0.0026883053f,	0.0026561081f,	0.0026238724f,	0.0025915997f,
		0.0025592953f,	0.0025269630f,	0.0024946105f,	0.0024622425f,	0.0024298709f,	0.0023974939f,
		0.0023651201f,	0.0023327520f,	0.0023003988f,	0.0022680591f,	0.0022357439f,	0.0022034559f,
		0.0021711825f,	0.0021389836f,	0.0021068004f,	0.0020747197f,	0.0020427436f,	0.0020108511f,
		0.0019792359f,	0.0019483039f,	0.0019134220f,	0.0018833181f,	0.0018524304f,	0.0018212500f,
		0.0017901930f,	0.0017591629f,	0.0017281769f,	0.0016973527f,	0.0016665871f,	0.0016359873f,
		0.0016055137f,	0.0015751686f,	0.0015449648f,	0.0015148979f,	0.0014849719f,	0.0014551972f,
		0.0014255711f,	0.0013961072f,	0.0013668007f,	0.0013376615f,	0.0013086866f,	0.0012798846f,
		0.0012512591f,	0.0012228163f,	0.0011945562f,	0.0011665121f,	0.0011386302f,	0.0011109420f,
		0.0010834532f,	0.0010561655f,	0.0010290952f,	0.0010022204f,	0.0009755701f,	0.0009491236f,
		0.0009228942f,	0.0008968879f,	0.0008711046f,	0.0008455396f,	0.0008202241f,	0.0007951306f,
		0.0007702743f,	0.0007456531f,	0.0007212725f,	0.0006971353f,	0.0006732436f,	0.0006496005f,
		0.0006262083f,	0.0006030709f,	0.0005801874f,	0.0005575627f,	0.0005351961f,	0.0005130901f,
		0.0004912480f,	0.0004696708f,	0.0004483607f,	0.0004273195f,	0.0004065487f,	0.0003860499f,
		0.0003658246f,	0.0003458742f,	0.0003261999f,	0.0003068031f,	0.0002876848f,	0.0002688426f,
		0.0002502843f,	0.0002320109f,	0.0002140161f,	0.0001963041f,	0.0001788755f,	0.0001617307f,
		0.0001448702f,	0.0001282941f,	0.0001120027f,	0.0000959961f,	0.0000802742f,	0.0000648370f,
		0.0000496844f,	0.0000348167f,	0.0000202317f,	0.0000059360f,	-0.0000080682f,	-0.0000218045f,
		-0.0000352609f,	-0.0000484370f,	-0.0000613341f,	-0.0000739531f,	-0.0000862956f,	-0.0000983624f,
		-0.0001101574f,	-0.0001216807f,	-0.0001329293f,	-0.0001439137f,	-0.0001546259f,	-0.0001650749f,
		-0.0001752566f,	-0.0001851768f,	-0.0001948524f,	-0.0002042521f,	-0.0002134137f,	-0.0002223036f,
		-0.0002309411f,	-0.0002393277f,	-0.0002474674f,	-0.0002554096f,	-0.0002630723f,	-0.0002704903f,
		-0.0002776732f,	-0.0002846206f,	-0.0002913365f,	-0.0002978212f,	-0.0003040791f,	-0.0003101215f,
		-0.0003159339f,	-0.0003215370f,	-0.0003269213f,	-0.0003321004f,	-0.0003370620f,	-0.0003418302f,
		-0.0003463883f,	-0.0003507469f,	-0.0003549745f,	-0.0003588938f,	-0.0003627268f,	-0.0003662166f,
		-0.0003694077f,	-0.0003723628f,	-0.0003743459f,	-0.0003766406f,	-0.0003850045f,	-0.0003849944f,
		-0.0003854122f,	-0.0003867614f,	-0.0003878771f,	-0.0003890564f,	-0.0003902105f,	-0.0003911182f,
		-0.0003919914f,	-0.0003926260f,	-0.0003931095f,	-0.0003934486f,	-0.0003936296f,	-0.0003936720f,
		-0.0003935787f,	-0.0003933405f,	-0.0003929717f,	-0.0003924589f,	-0.0003918200f,	-0.0003910433f,
		-0.0003901412f,	-0.0003891103f,	-0.0003879524f,	-0.0003866714f,	-0.0003852663f,	-0.0003837052f,
		-0.0003820732f,	-0.0003803282f,	-0.0003784697f,	-0.0003765010f,	-0.0003744058f,	-0.0003722254f,
		-0.0003699217f,	-0.0003675341f,	-0.0003650493f,	-0.0003624651f,	-0.0003597890f,	-0.0003570143f,
		-0.0003541617f,	-0.0003512177f,	-0.0003481871f,	-0.0003450785f,	-0.0003418907f,	-0.0003386259f,
		-0.0003352870f,	-0.0003318758f,	-0.0003283952f,	-0.0003248456f,	-0.0003212337f,	-0.0003175586f,
		-0.0003138266f,	-0.0003100395f,	-0.0003061987f,	-0.0003023079f,	-0.0002983695f,	-0.0002943862f,
		-0.0002903610f,	-0.0002862966f,	-0.0002821959f,	-0.0002780619f,	-0.0002738975f,	-0.0002697054f,
		-0.0002654888f,	-0.0002612470f,	-0.0002569898f,	-0.0002527203f,	-0.0002484342f,	-0.0002441381f,
		-0.0002398348f,	-0.0002355272f,	-0.0002312180f,	-0.0002269103f,	-0.0002226066f,	-0.0002183098f,
		-0.0002140225f,	-0.0002097476f,	-0.0002054872f,	-0.0002012436f,	-0.0001970209f,	-0.0001928243f,
		-0.0001886533f,	-0.0001845040f,	-0.0001803851f,	-0.0001762978f,	-0.0001722442f,	-0.0001682265f,
		-0.0001642468f,	-0.0001603067f,	-0.0001564102f,	-0.0001525574f,	-0.0001487494f,	-0.0001449862f,
		-0.0001412707f,	-0.0001376061f,	-0.0001339901f,	-0.0001304267f,	-0.0001269258f,	-0.0001234684f,
		-0.0001200761f,	-0.0001167281f,	-0.0001134358f,	-0.0001101995f,	-0.0001070252f,	-0.0001039352f,
		-0.0001008754f,	-0.0000978817f,	-0.0000949393f,	-0.0000920551f,	-0.0000892298f,	-0.0000864739f,
		-0.0000837814f,	-0.0000811338f,	-0.0000785414f,	-0.0000760097f,	-0.0000735340f,	-0.0000711183f,
		-0.0000687551f,	-0.0000664541f,	-0.0000642049f,	-0.0000620104f,	-0.0000598974f,	-0.0000577796f,
		-0.0000557572f,	-0.0000537149f,	-0.0000516895f,	-0.0000497470f,	-0.0000476284f,	-0.0000407034f
		
		};

    objSubBand *srv = NULL;
    srv = (objSubBand *)calloc(1, sizeof(objSubBand));

	/*allocation memory to struct param.*/
	srv->frm_len = frm_len;
    srv->Ppf_tap = AEC_WIN_LEN / AEC_FFT_LEN;  /* 768 / 256 = 3 */
    srv->Ppf_decm = AEC_WIN_LEN / srv->frm_len; /* 768 / 128 = 6 */
    srv->scale = 1.0f;

    srv->p_in = (int *)calloc(srv->Ppf_decm, sizeof(int));
    srv->p_h0 = (int *)calloc(srv->Ppf_decm, sizeof(int));

    for (i = 0; i < AEC_WIN_LEN / srv->frm_len; i++)
    {
		srv->p_in[i] = i * srv->frm_len;
		srv->p_h0[i] = i * srv->frm_len;
    }
    srv->ana_cxout = (xcomplex *)calloc(AEC_SUBBAND_NUM, sizeof(xcomplex));
    srv->comp_in = (float *)calloc(AEC_FFT_LEN, sizeof(float));
    srv->comp_out = (float *)calloc(AEC_WIN_LEN, sizeof(float));
    srv->lpf_coef = (float *)calloc(AEC_WIN_LEN, sizeof(float));
    
	
    memcpy(srv->lpf_coef, subband_filter_coef, AEC_WIN_LEN * sizeof(float));

    srv->ana_xin = (float *)calloc(AEC_WIN_LEN, sizeof(float));
    srv->ana_xout = (float *)calloc(AEC_WIN_LEN, sizeof(float));

    for (i = 0; i < AEC_WIN_LEN; i++)
    {
		srv->comp_out[i] = 0.0;
    }
    srv->rfft_param = dios_ssp_share_rfft_init(AEC_FFT_LEN);

    srv->fftout_buffer = (float*)calloc(AEC_FFT_LEN, sizeof(float));
    srv->fftin_buffer = (float*)calloc(AEC_FFT_LEN, sizeof(float));
    return srv;
}

int dios_ssp_share_subband_reset(objSubBand* srv)
{
	int i;
    for (i = 0; i < AEC_WIN_LEN; i++)
    {
	    srv->comp_out[i] = 0.0;
	    srv->ana_xin[i] = 0.0;
    }
    for (i = 0; i < AEC_WIN_LEN / srv->frm_len; i++)
    {
	    srv->p_in[i] = i * srv->frm_len;
	    srv->p_h0[i] = i * srv->frm_len;
    }
    return 0;
}

// subband analysis
int dios_ssp_share_subband_analyse(objSubBand* srv, float* in_buf, xcomplex* out_buf)
{
    int i, j;
    for (i = srv->frm_len - 1; i >= 0; i--)
    {
		srv->ana_xin[i + srv->p_in[0]] = (in_buf[srv->frm_len - i - 1]);
    }
    float r0 = 0.0;
    for (i = 0; i < AEC_FFT_LEN; i++) 
    {
		r0 = 0.0;
		if (i < srv->frm_len)
		{
			for (j = 0; j < srv->Ppf_tap; j++) 
			{
				r0 += srv->lpf_coef[srv->p_h0[2 * j] + i] * srv->ana_xin[srv->p_in[2 * j] + i];
			}
		}
		else 
		{
			for (j = 0; j < srv->Ppf_tap; j++) 
			{
				r0 += srv->lpf_coef[srv->p_h0[2 * j + 1] + i - srv->frm_len] * srv->ana_xin[srv->p_in[2 * j + 1] + i - srv->frm_len];
			}
		}
		srv->ana_xout[i] = r0;
    }

    int itmp = srv->p_in[srv->Ppf_decm - 1];
    for (i = srv->Ppf_decm - 1; i > 0; i--) 
    {
		srv->p_in[i] = srv->p_in[i - 1];
    }
    srv->p_in[0] = itmp;

	dios_ssp_share_rfft_process(srv->rfft_param, srv->ana_xout, srv->fftout_buffer);
    
	for (i = 0; i < AEC_SUBBAND_NUM; i++)
    {
	    out_buf[i].r = srv->fftout_buffer[i];
    }
    out_buf[0].i = out_buf[AEC_SUBBAND_NUM - 1].i = 0.0;
    for (i = 1; i < AEC_SUBBAND_NUM - 1; i++)
    {
	    out_buf[i].i = -srv->fftout_buffer[AEC_FFT_LEN - i];
    }
    return(0);
}

// subband synthesis
int dios_ssp_share_subband_compose(objSubBand* srv, xcomplex* in_buf, float* out_buf)
{
    int i, j;
	srv->fftin_buffer[0] = in_buf[0].r;
	srv->fftin_buffer[srv->frm_len] = in_buf[srv->frm_len].r;
	for (i = 1; i < srv->frm_len; i++) {
		srv->fftin_buffer[i] = in_buf[i].r;
		srv->fftin_buffer[AEC_FFT_LEN - i] = -in_buf[i].i;
	}

	dios_ssp_share_irfft_process(srv->rfft_param, srv->fftin_buffer, srv->fftout_buffer);

	for (i = 0; i < AEC_FFT_LEN; i++)
	{
		srv->comp_in[i] = srv->fftout_buffer[i];
	}
	for (i = 0; i < srv->Ppf_tap; i++)
	{
		for (j = 0; j < AEC_FFT_LEN; j++)
		{
			int k = i * AEC_FFT_LEN + j;
			srv->comp_out[k] += srv->lpf_coef[k] * srv->comp_in[AEC_FFT_LEN - j - 1];
		}
	}

	for (i = 0; i < srv->frm_len; i++)
	{
		out_buf[i] = srv->comp_out[i] * srv->frm_len * srv->scale;
	}

	/* shift, add  for compostion */
	for (i = 0; i < AEC_WIN_LEN - srv->frm_len; i++)
	{
		srv->comp_out[i] = srv->comp_out[i + srv->frm_len];
	}
	for (i = AEC_WIN_LEN - srv->frm_len; i < AEC_WIN_LEN; i++)
	{
		srv->comp_out[i] = 0.0;
	}
	return(0);
}

int dios_ssp_share_subband_uninit(objSubBand* srv)
{
	int ret = 0;
	if (NULL == srv)
	{
		return -1;
	}
    free(srv->p_in);
    free(srv->p_h0);
    free(srv->ana_cxout);
    free(srv->comp_in);
    free(srv->comp_out);
    free(srv->lpf_coef);
    free(srv->ana_xin);
    free(srv->ana_xout);
    free(srv->fftout_buffer);
    free(srv->fftin_buffer);
	ret = dios_ssp_share_rfft_uninit(srv->rfft_param);
	if (0 != ret)
	{
		return -1;
	}
    free(srv);
    return 0;
}

