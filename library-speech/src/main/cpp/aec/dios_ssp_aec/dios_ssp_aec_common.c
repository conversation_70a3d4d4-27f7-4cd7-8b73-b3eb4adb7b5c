/* Copyright (C) 2017 Beijing Didi Infinity Technology and Development Co.,Ltd.
All rights reserved.

Licensed under the Apache License, Version 2.0 (the "License");
you may not use this file except in compliance with the License.
You may obtain a copy of the License at

	http://www.apache.org/licenses/LICENSE-2.0

Unless required by applicable law or agreed to in writing, software
distributed under the License is distributed on an "AS IS" BASIS,
WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
See the License for the specific language governing permissions and
limitations under the License.

Description: The file contains one function, the main function of which is to 
calculate the average of the data. 
==============================================================================*/

#include "dios_ssp_aec_common.h"

int dios_ssp_aec_average_track(float *input_time, int frm_len, float *ret_avg)
{
	int i;
	float avg_abs = 0.0f;

	if (NULL == input_time)
	{
		return ERR_AEC;
	}

	for (i = 0; i < frm_len; i++)
	{
		avg_abs += xabs(input_time[i]);
	}

	*ret_avg = avg_abs / frm_len;

	return 0;
}

