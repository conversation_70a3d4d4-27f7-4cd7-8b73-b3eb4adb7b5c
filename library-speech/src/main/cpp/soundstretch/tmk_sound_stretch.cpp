//
// Created by <PERSON><PERSON> on 2023/5/25.
//
#include <jni.h>
#include <string>

#include <stdexcept>
#include <stdio.h>
#include <string.h>
#include <time.h>
#include "./src/SoundTouch.h"

#define JavaFieldID "mNativeSoundStretch"

extern "C"
JNIEXPORT jlong JNICALL
Java_co_timekettle_speech_jni_SoundStretchJni_create(JNIEnv *env, jobject thiz, jint sampleRate, jint nChannel) {
    auto soundTouch = new soundtouch::SoundTouch();
    soundTouch->setSampleRate(sampleRate);
    soundTouch->setChannels(nChannel);
    soundTouch->setTempoChange(0.0); // 在不影响音调的情况下，改变声音以比原来更快或更慢的速度播放。如: 100 指 2 倍速, 150 指 3 倍速, -50 指 0.5 倍速
    soundTouch->setPitchSemiTones(0.0); // 在保持原始节奏（速度）的同时改变声音音高或音调。音高 -60 .. +60 半音（+- 5 个八度） 范围内调节。
    soundTouch->setRateChange(0.0); // 一起改变速度和音调，就好像以不同的 RPM 速率播放黑胶唱片一样。速度和回放速率在-95% .. +5000%范围内可调
    soundTouch->setSetting(SETTING_USE_QUICKSEEK, false);
    soundTouch->setSetting(SETTING_USE_AA_FILTER, !(false));

    auto ptr = reinterpret_cast<jlong>(soundTouch);
    jclass clazz = env->GetObjectClass(thiz);
    jfieldID fieldID = env->GetFieldID(clazz, JavaFieldID, "J");
    env->SetLongField(thiz, fieldID, ptr);
    return (jlong)soundTouch;
}

extern "C"
JNIEXPORT void JNICALL
Java_co_timekettle_speech_jni_SoundStretchJni_updateTempo(JNIEnv *env, jobject thiz, jfloat factor) {
    jclass clazz = env->GetObjectClass(thiz);
    jfieldID fieldID = env->GetFieldID(clazz, JavaFieldID, "J");
    jlong ptr = env->GetLongField(thiz, fieldID);
    auto soundTouch = reinterpret_cast<soundtouch::SoundTouch *>(ptr);

    soundTouch->setTempo(factor);
}

extern "C"
JNIEXPORT void JNICALL
Java_co_timekettle_speech_jni_SoundStretchJni_putSamples(JNIEnv *env, jobject thiz, jfloatArray samples, jint len) {
    jfloat *inBuf = env->GetFloatArrayElements(samples, nullptr);

    {
        jclass clazz = env->GetObjectClass(thiz);
        jfieldID fieldID = env->GetFieldID(clazz, JavaFieldID, "J");
        jlong ptr = env->GetLongField(thiz, fieldID);
        auto soundTouch = reinterpret_cast<soundtouch::SoundTouch *>(ptr);

        soundTouch->putSamples(inBuf, len);
    }

    env->ReleaseFloatArrayElements((jfloatArray)samples, inBuf, 0);
}

extern "C"
JNIEXPORT void JNICALL
Java_co_timekettle_speech_jni_SoundStretchJni_flush(JNIEnv *env, jobject thiz) {
    {
        jclass clazz = env->GetObjectClass(thiz);
        jfieldID fieldID = env->GetFieldID(clazz, JavaFieldID, "J");
        jlong ptr = env->GetLongField(thiz, fieldID);
        auto soundTouch = reinterpret_cast<soundtouch::SoundTouch *>(ptr);

        soundTouch->flush();
    }
}

extern "C"
JNIEXPORT void JNICALL
Java_co_timekettle_speech_jni_SoundStretchJni_clear(JNIEnv *env, jobject thiz) {
    {
        jclass clazz = env->GetObjectClass(thiz);
        jfieldID fieldID = env->GetFieldID(clazz, JavaFieldID, "J");
        jlong ptr = env->GetLongField(thiz, fieldID);
        auto soundTouch = reinterpret_cast<soundtouch::SoundTouch *>(ptr);

        soundTouch->clear();
    }
}

extern "C"
JNIEXPORT jint JNICALL
Java_co_timekettle_speech_jni_SoundStretchJni_receiveSamples(JNIEnv *env, jobject thiz, jfloatArray samples) {
    size_t len = env->GetArrayLength(samples);
    jfloat *inBuf = env->GetFloatArrayElements(samples, nullptr);

    uint nSamples = 0;
    {
        jclass clazz = env->GetObjectClass(thiz);
        jfieldID fieldID = env->GetFieldID(clazz, JavaFieldID, "J");
        jlong ptr = env->GetLongField(thiz, fieldID);
        auto soundTouch = reinterpret_cast<soundtouch::SoundTouch *>(ptr);
        nSamples = soundTouch->receiveSamples(inBuf, len);
    }

    env->ReleaseFloatArrayElements((jfloatArray)samples, inBuf, 0);
    return (jint)nSamples;
}

extern "C"
JNIEXPORT void JNICALL
Java_co_timekettle_speech_jni_SoundStretchJni_destory(JNIEnv *env, jobject thiz) {
    jclass clazz = env->GetObjectClass(thiz);
    jfieldID fieldID = env->GetFieldID(clazz, JavaFieldID, "J");
    jlong ptr = env->GetLongField(thiz, fieldID);
    auto soundTouch = reinterpret_cast<soundtouch::SoundTouch *>(ptr);
    delete soundTouch;
}