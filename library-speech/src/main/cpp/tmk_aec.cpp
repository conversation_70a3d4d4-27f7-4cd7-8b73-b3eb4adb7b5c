//
// Created by Decin on 2023/3/2.
//

#include <jni.h>
#include <string>
#include <android/log.h>

#ifdef __cplusplus
extern "C" {
#endif

#include "./aec/dios_ssp_aec/dios_ssp_aec_api.h"

#ifdef __cplusplus
}
#endif


extern "C"
JNIEXPORT void JNICALL
Java_co_timekettle_speech_ispeech_algorithm_AudioAecJni_init(JNIEnv *env, jobject thiz) {
    int mic_num = 1;
    int ref_num = 1;
    int cfg_frame_len = 128;
    void *ptr = dios_ssp_aec_init_api(mic_num, ref_num, cfg_frame_len);
}

extern "C"
JNIEXPORT jlong JNICALL
Java_co_timekettle_speech_ispeech_algorithm_AudioAecJni_createAec(JNIEnv *env, jobject thiz) {
    // TODO: implement createAec()
    int mic_num = 1;
    int ref_num = 1;
    int cfg_frame_len = 128;
    void *ptr = dios_ssp_aec_init_api(mic_num, ref_num, cfg_frame_len);
    return (jlong)ptr;
}

extern "C"
JNIEXPORT void JNICALL
Java_co_timekettle_speech_ispeech_algorithm_AudioAecJni_processAec(JNIEnv *env, jobject thiz,
                                                                   jlong aec,
                                                                   jfloatArray recoder_pcm,
                                                                   jfloatArray speaker_pcm) {
    // TODO: implement processAec()
    float *micBuf = env->GetFloatArrayElements(recoder_pcm, nullptr);
    float *speakerBuf = env->GetFloatArrayElements(speaker_pcm, nullptr);

    void *ptr = (void *)aec;
    int dt_st;
    int ret = dios_ssp_aec_process_api(ptr, micBuf, speakerBuf, &dt_st);
    if (ret != 0) {
//        __android_log_print(ANDROID_LOG_ERROR, "AEC", "处理失败 %d", ret);
    }
    env->ReleaseFloatArrayElements((jfloatArray)recoder_pcm, micBuf, 0);
    env->ReleaseFloatArrayElements((jfloatArray)speaker_pcm, speakerBuf, 0);
}

extern "C"
JNIEXPORT void JNICALL
Java_co_timekettle_speech_ispeech_algorithm_AudioAecJni_resetAec(JNIEnv *env, jobject thiz,
                                                                 jlong aec) {
    // TODO: implement resetAec()
    void *ptr = (void *)aec;
    int ret = dios_ssp_aec_reset_api(ptr);
    if (ret != 0) {
//        __android_log_print(ANDROID_LOG_ERROR, "AEC", "dios_ssp_aec_reset_api %d", ret);
    }
}

extern "C"
JNIEXPORT void JNICALL
Java_co_timekettle_speech_ispeech_algorithm_AudioAecJni_destoryAec(JNIEnv *env, jobject thiz,
                                                                   jlong aec) {
    // TODO: implement destoryAec()
    void *ptr = (void *)aec;
    int ret = dios_ssp_aec_uninit_api(ptr);
    if (ret != 0) {
//        __android_log_print(ANDROID_LOG_ERROR, "AEC", "dios_ssp_aec_uninit_api %d", ret);
    }
}
