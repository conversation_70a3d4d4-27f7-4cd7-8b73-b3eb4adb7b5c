<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:animateLayoutChanges="true"
    android:background="#000000"
    tools:ignore="Suspicious0dp"
    android:orientation="vertical">

    <ScrollView
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:layout_weight="1"
        app:layout_constraintTop_toTopOf="parent"
        >

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical">
            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:background="#eeeeee"
                android:orientation="horizontal">
                <Button
                    android:id="@+id/btn_asr"
                    android:layout_width="94dp"
                    android:layout_height="48dp"
                    android:layout_weight="1"
                    android:text="开始识别(手机拾音)" />

                <Button
                    android:id="@+id/btn_asr_file"
                    android:layout_width="140dp"
                    android:layout_height="48dp"
                    android:layout_weight="1"
                    android:text="开始识别文件" />
            </LinearLayout>

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:background="#eeeeee"
                android:orientation="horizontal">
                <Button
                    android:id="@+id/btn_asr_t1"
                    android:layout_width="150dp"
                    android:layout_height="48dp"
                    android:layout_weight="1"
                    android:backgroundTint="#DF5353"
                    android:text="开始识别(T1)" />

                <Button
                    android:id="@+id/btn_asr_file_t1"
                    android:layout_width="190dp"
                    android:layout_height="48dp"
                    android:layout_weight="1"
                    android:backgroundTint="#DF5353"
                    android:text="开始识别文件(T1)" />
            </LinearLayout>

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:background="#eeeeee"
                android:orientation="horizontal">
                <Button
                    android:id="@+id/btn_mt_offline"
                    android:layout_width="0dp"
                    android:layout_height="80dp"
                    android:layout_weight="1"
                    android:text="开始离线翻译\n(所有语言对)" />

                <Button
                    android:id="@+id/btn_toggle_offline"
                    android:layout_width="0dp"
                    android:layout_height="80dp"
                    android:layout_weight="1"
                    android:text="开启离线" />

                <Button
                    android:id="@+id/btn_offline_tryauth"
                    android:layout_width="0dp"
                    android:layout_height="80dp"
                    android:layout_weight="1"
                    android:text="测试鉴权离线" />
            </LinearLayout>

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:background="#eeeeee"
                android:orientation="horizontal">
                <Button
                    android:id="@+id/btn_test_alltask"
                    android:layout_width="0dp"
                    android:layout_height="48dp"
                    android:layout_weight="1"
                    android:text="跑所有单元测试" />

            </LinearLayout>

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:background="#eeeeee"
                android:orientation="horizontal">

                <Button
                    android:id="@+id/btn_vad_file"
                    android:layout_width="250dp"
                    android:layout_height="48dp"
                    android:layout_weight="1"
                    android:text="开始 vad 处理(file)" />

            </LinearLayout>

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:background="#eeeeee"
                android:orientation="horizontal">
                <Button
                    android:id="@+id/btn_mt"
                    android:layout_width="94dp"
                    android:layout_height="48dp"
                    android:layout_weight="1"
                    android:text="开始翻译" />
                <Button
                    android:id="@+id/btn_switch_voicename"
                    android:layout_width="150dp"
                    android:layout_height="48dp"
                    android:layout_weight="1"
                    android:text="切换音色"/>
                <Button
                    android:id="@+id/btn_tts"
                    android:layout_width="150dp"
                    android:layout_height="48dp"
                    android:layout_weight="1"
                    android:text="开始合成并播放"/>
                <Button
                    android:id="@+id/btn_play"
                    android:layout_width="94dp"
                    android:layout_height="48dp"
                    android:layout_weight="1"
                    android:text="开始播放" />
            </LinearLayout>

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:background="#eeeeee"
                android:orientation="horizontal">
                <Button
                    android:id="@+id/btn_asr_mutil_channel"
                    android:layout_width="0dp"
                    android:layout_height="48dp"
                    android:layout_weight="1"
                    android:text="开始手动识别" />

                <Button
                    android:id="@+id/btn_asr_pick1"
                    android:layout_width="0dp"
                    android:layout_height="48dp"
                    android:layout_weight="1"
                    android:text="开始通道1拾音" />
                <Button
                    android:id="@+id/btn_asr_pick2"
                    android:layout_width="0dp"
                    android:layout_height="48dp"
                    android:layout_weight="1"
                    android:text="开始通道2拾音" />
            </LinearLayout>

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:background="#eeeeee"
                android:orientation="horizontal">
                <Button
                    android:id="@+id/btn_custom_mt"
                    android:layout_width="0dp"
                    android:layout_height="48dp"
                    android:layout_weight="1"
                    android:text="测试自定义翻译" />

            </LinearLayout>

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:background="#eeeeee"
                android:orientation="horizontal">
                <Button
                    android:id="@+id/btn_update_host"
                    android:layout_width="0dp"
                    android:layout_height="48dp"
                    android:layout_weight="1"
                    android:text="更新下一个服务器" />

            </LinearLayout>

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:background="#eeeeee"
                android:orientation="horizontal">
                <Button
                    android:id="@+id/btn_switch_offline_tts"
                    android:layout_width="0dp"
                    android:layout_height="48dp"
                    android:layout_weight="1"
                    android:text="切换在线离线tts" />
            </LinearLayout>

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:background="#eeeeee"
                android:orientation="horizontal">
                <Button
                    android:id="@+id/btn_test_punctuation_segmentation"
                    android:layout_width="0dp"
                    android:layout_height="48dp"
                    android:layout_weight="1"
                    android:text="测试标点断句" />
            </LinearLayout>

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:background="#eeeeee"
                android:orientation="horizontal">
                <Switch
                    android:id="@+id/sw_opus"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:paddingLeft="5dp"
                    android:paddingRight="5dp"
                    android:text="Opus压缩传输?"
                    android:textSize="13sp" />
            </LinearLayout>

        </LinearLayout>
    </ScrollView>

    <SeekBar
        android:id="@+id/sb_volume"
        android:layout_width="match_parent"
        android:layout_height="30dp"
        android:background="#ffffff" />

    <co.timekettle.example.SpeechMsgListView
        android:id="@+id/list_msg"
        tools:listitem="@layout/tmkengine_adapter_msg"
        android:layout_width="match_parent"
        android:background="#ffffff"
        android:layout_height="0dp"
        android:layout_weight="1"
        android:layout_marginVertical="1dp"
        android:divider="@android:color/darker_gray"
        android:dividerHeight="0.5dp"
        android:paddingStart="5dp"
        android:paddingEnd="5dp"
        android:scrollbars="none" />

</LinearLayout>