<?xml version="1.0" encoding="utf-8"?>

<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:animateLayoutChanges="true"
    tools:ignore="Suspicious0dp"
    android:orientation="vertical">

    <ScrollView
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical">

            <LinearLayout
                android:id="@+id/layout_setting"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:background="#eeeeee"
                android:orientation="vertical"
                android:padding="20dp">

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_gravity="center_horizontal"
                    android:layout_marginBottom="10dp"
                    android:focusable="true"
                    android:focusableInTouchMode="true"
                    android:text="设置"
                    android:textColor="@android:color/black"
                    android:textSize="12sp" />

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:background="#eeeeee"
                    android:orientation="horizontal">

                    <Spinner
                        android:id="@+id/spin_langcode"
                        android:layout_width="wrap_content"
                        android:layout_weight="1"
                        android:layout_height="wrap_content" />

                    <EditText
                        android:id="@+id/et_langcode"
                        android:layout_width="wrap_content"
                        android:layout_weight="2"
                        android:layout_height="40dp"
                        android:hint="设置语言code(如: en-US)"
                        android:paddingLeft="5dp"
                        android:paddingRight="5dp"
                        android:textSize="13sp" />

                </LinearLayout>

                <LinearLayout
                    android:id="@+id/layout_langcode_other"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:visibility="gone"
                    android:background="#eeeeee"
                    android:orientation="horizontal">

                    <Spinner
                        android:id="@+id/spin_langcode_other"
                        android:layout_width="wrap_content"
                        android:layout_weight="1"
                        android:layout_height="wrap_content"/>

                    <EditText
                        android:id="@+id/et_langcode_other"
                        android:layout_width="wrap_content"
                        android:layout_weight="2"
                        android:layout_height="40dp"
                        android:hint="设置目标语言code(如: zh-CN)"
                        android:paddingLeft="5dp"
                        android:paddingRight="5dp"
                        android:textSize="13sp" />

                </LinearLayout>

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:background="#eeeeee"
                    android:orientation="horizontal">

                    <Spinner
                        android:id="@+id/spin_type"
                        android:layout_width="wrap_content"
                        android:layout_weight="1"
                        android:layout_height="wrap_content"
                        />

                    <EditText
                        android:id="@+id/et_type"
                        android:layout_width="wrap_content"
                        android:layout_weight="2"
                        android:layout_height="40dp"
                        android:hint="设置任务类型(如: Recognize)"
                        android:paddingLeft="5dp"
                        android:paddingRight="5dp"
                        android:textSize="13sp" />

                </LinearLayout>


                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:background="#eeeeee"
                    android:orientation="horizontal">

                    <Spinner
                        android:id="@+id/spin_engine"
                        android:layout_width="wrap_content"
                        android:layout_weight="1"
                        android:layout_height="wrap_content"
                        />

                    <EditText
                        android:id="@+id/et_engine"
                        android:layout_width="wrap_content"
                        android:layout_weight="2"
                        android:layout_height="40dp"
                        android:hint="设置引擎(如: Microsoft)"
                        android:paddingLeft="5dp"
                        android:paddingRight="5dp"
                        android:textSize="13sp" />
                </LinearLayout>

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:background="#eeeeee"
                    android:orientation="horizontal">

                    <EditText
                        android:id="@+id/et_sample_text"
                        android:layout_width="match_parent"
                        android:layout_height="40dp"
                        android:hint="设置源文本(只用于翻译和合成)"
                        android:paddingLeft="5dp"
                        android:paddingRight="5dp"
                        android:textSize="13sp" />
                </LinearLayout>

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:background="#eeeeee"
                    android:orientation="horizontal">
                    <Spinner
                        android:id="@+id/spin_host"
                        android:layout_width="wrap_content"
                        android:layout_weight="1"
                        android:layout_height="wrap_content"
                        />

                    <EditText
                        android:id="@+id/et_host"
                        android:layout_width="wrap_content"
                        android:layout_weight="2"
                        android:layout_height="40dp"
                        android:hint="设置引擎IP"
                        android:paddingLeft="5dp"
                        android:paddingRight="5dp"
                        android:textSize="13sp" />
                </LinearLayout>

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:background="#eeeeee"
                    android:orientation="horizontal">
                    <Switch
                        android:id="@+id/sw_opus"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:paddingLeft="5dp"
                        android:paddingRight="5dp"
                        android:text="Opus压缩传输?"
                        android:textSize="13sp" />
                </LinearLayout>

            </LinearLayout>

            <TextView
                android:id="@+id/txt_setting"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_gravity="center_horizontal"
                android:background="#eeeeee"
                android:padding="10dp" />

            <TextView
                android:id="@+id/txt_tip"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_gravity="center_horizontal"
                android:padding="10dp" />

            <RelativeLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="horizontal"
                android:padding="10dp">

                <Button
                    android:id="@+id/btn_test"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_centerInParent="true"
                    android:text="处理任务" />

                <ImageView
                    android:id="@+id/img_loading"
                    android:layout_width="40dp"
                    android:layout_height="40dp"
                    android:layout_alignParentRight="true"
                    android:layout_centerVertical="true"
                    android:layout_marginRight="10dp"
                    android:visibility="invisible" />

            </RelativeLayout>

        </LinearLayout>

    </ScrollView>

    <ListView
        android:id="@+id/list_msg"
        tools:listitem="@layout/tmkengine_adapter_msg"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginTop="10dp"
        android:divider="@android:color/darker_gray"
        android:dividerHeight="0.5dp"
        android:paddingStart="5dp"
        android:paddingEnd="5dp"
        android:scrollbars="none" />

</LinearLayout>