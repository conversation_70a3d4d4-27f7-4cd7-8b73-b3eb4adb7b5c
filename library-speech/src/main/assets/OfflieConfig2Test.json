{"readme": "1、「识别」全部是用的xzy，「翻译」用的是niu_trans，总用有13个语言对 2、转换成下载链接时，需要把「baseUrlXzy」和「baseUrlNiu」替换成相应的链接", "version": "2", "baseUrlXzy": "https://cdn.timekettle.co/TMK-APP/ClientOffline2/asr/xzy_test", "baseUrlNiu": "https://cdn.timekettle.co/TMK-APP/ClientOffline2/mt/niu_trans_test", "commonUrls": ["baseUrlXzy/cnen.zip"], "languagePairUrl": [{"code": "zh<->en", "resCodes": ["zh", "en", "zhen", "enzh"], "urls": ["baseUrlNiu/zh2en.zip", "baseUrlNiu/en2zh.zip"]}, {"code": "zh<->ja", "resCodes": ["zh", "ja", "zhja", "jazh"], "urls": ["baseUrlXzy/ja.zip", "baseUrlNiu/zh2ja.zip", "baseUrlNiu/ja2zh.zip"]}, {"code": "zh<->fr", "resCodes": ["zh", "fr", "zhfr", "frzh"], "urls": ["baseUrlXzy/fr.zip", "baseUrlNiu/zh2fr.zip", "baseUrlNiu/fr2zh.zip"]}, {"code": "zh<->es", "resCodes": ["zh", "es", "zhes", "eszh"], "urls": ["baseUrlXzy/es.zip", "baseUrlNiu/zh2es.zip", "baseUrlNiu/es2zh.zip"]}, {"code": "zh<->ru", "resCodes": ["zh", "ru", "zhru", "ruzh"], "urls": ["baseUrlXzy/ru.zip", "baseUrlNiu/zh2ru.zip", "baseUrlNiu/ru2zh.zip"]}, {"code": "zh<->de", "resCodes": ["zh", "de", "zhde", "dezh"], "urls": ["baseUrlXzy/de.zip", "baseUrlNiu/zh2de.zip", "baseUrlNiu/de2zh.zip"]}, {"code": "zh<->ko", "resCodes": ["zh", "ko", "zhko", "kozh"], "urls": ["baseUrlXzy/ko.zip", "baseUrlNiu/zh2ko.zip", "baseUrlNiu/ko2zh.zip"]}, {"code": "en<->ja", "resCodes": ["en", "ja", "enja", "jaen"], "urls": ["baseUrlXzy/ja.zip", "baseUrlNiu/en2ja.zip", "baseUrlNiu/ja2en.zip"]}, {"code": "en<->fr", "resCodes": ["en", "fr", "enfr", "fren"], "urls": ["baseUrlXzy/fr.zip", "baseUrlNiu/en2fr.zip", "baseUrlNiu/fr2en.zip"]}, {"code": "en<->es", "resCodes": ["en", "es", "enes", "esen"], "urls": ["baseUrlXzy/es.zip", "baseUrlNiu/en2es.zip", "baseUrlNiu/es2en.zip"]}, {"code": "en<->ru", "resCodes": ["en", "ru", "enru", "ruen"], "urls": ["baseUrlXzy/ru.zip", "baseUrlNiu/en2ru.zip", "baseUrlNiu/ru2en.zip"]}, {"code": "en<->de", "resCodes": ["en", "de", "ende", "deen"], "urls": ["baseUrlXzy/de.zip", "baseUrlNiu/en2de.zip", "baseUrlNiu/de2en.zip"]}, {"code": "en<->ko", "resCodes": ["en", "ko", "enko", "koen"], "urls": ["baseUrlXzy/ko.zip", "baseUrlNiu/en2ko.zip", "baseUrlNiu/ko2en.zip"]}], "totalSizes": {"baseUrlXzy/cnen.zip": 172, "baseUrlXzy/de.zip": 168, "baseUrlXzy/es.zip": 168, "baseUrlXzy/fr.zip": 168, "baseUrlXzy/ja.zip": 168, "baseUrlXzy/ko.zip": 168, "baseUrlXzy/ru.zip": 168, "baseUrlXzy/th.zip": 168, "baseUrlNiu/de2en.zip": 110, "baseUrlNiu/de2zh.zip": 110, "baseUrlNiu/en2de.zip": 110, "baseUrlNiu/en2es.zip": 110, "baseUrlNiu/en2fr.zip": 110, "baseUrlNiu/en2ja.zip": 110, "baseUrlNiu/en2ko.zip": 110, "baseUrlNiu/en2ru.zip": 110, "baseUrlNiu/en2zh.zip": 110, "baseUrlNiu/es2en.zip": 110, "baseUrlNiu/es2zh.zip": 110, "baseUrlNiu/fr2en.zip": 110, "baseUrlNiu/fr2zh.zip": 110, "baseUrlNiu/ja2en.zip": 110, "baseUrlNiu/ja2zh.zip": 110, "baseUrlNiu/ko2en.zip": 110, "baseUrlNiu/ko2zh.zip": 110, "baseUrlNiu/ru2en.zip": 110, "baseUrlNiu/ru2zh.zip": 110, "baseUrlNiu/zh2de.zip": 110, "baseUrlNiu/zh2en.zip": 110, "baseUrlNiu/zh2es.zip": 110, "baseUrlNiu/zh2fr.zip": 110, "baseUrlNiu/zh2ja.zip": 110, "baseUrlNiu/zh2ko.zip": 110, "baseUrlNiu/zh2ru.zip": 110}}