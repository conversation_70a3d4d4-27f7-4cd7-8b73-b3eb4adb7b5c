{"readme": "1、「识别」全部是用的xzy，「翻译」用的是niu_trans，总用有13个语言对 2、转换成下载链接时，需要把「baseUrlXzy」和「baseUrlNiu」替换成相应的链接", "version": "2", "baseUrlXzy": "https://cdn.timekettle.co/TMK-APP/ClientOffline2/xzy/asr", "baseUrlNiu": "https://cdn.timekettle.co/TMK-APP/ClientOffline2/mt/niu_trans", "baseUrlXzyTts": "https://cdn.timekettle.co/TMK-APP/ClientOffline2/xzy/tts", "commonUrls": ["baseUrlXzy/cnen.zip"], "languagePairUrl": [{"code": "zh<->en", "resCodes": ["zh", "en", "zhen", "enzh"], "urls": ["baseUrlNiu/zh2en.zip", "baseUrlNiu/en2zh.zip", "baseUrlXzyTts/zh.zip", "baseUrlXzyTts/en.zip"]}, {"code": "zh<->ja", "resCodes": ["zh", "ja", "zhja", "jazh"], "urls": ["baseUrlXzy/ja.zip", "baseUrlNiu/zh2ja.zip", "baseUrlNiu/ja2zh.zip", "baseUrlXzyTts/zh.zip", "baseUrlXzyTts/ja.zip"]}, {"code": "zh<->fr", "resCodes": ["zh", "fr", "zhfr", "frzh"], "urls": ["baseUrlXzy/fr.zip", "baseUrlNiu/zh2fr.zip", "baseUrlNiu/fr2zh.zip", "baseUrlXzyTts/zh.zip", "baseUrlXzyTts/fr.zip"]}, {"code": "zh<->es", "resCodes": ["zh", "es", "zhes", "eszh"], "urls": ["baseUrlXzy/es.zip", "baseUrlNiu/zh2es.zip", "baseUrlNiu/es2zh.zip", "baseUrlXzyTts/zh.zip", "baseUrlXzyTts/es.zip"]}, {"code": "zh<->ru", "resCodes": ["zh", "ru", "zhru", "ruzh"], "urls": ["baseUrlXzy/ru.zip", "baseUrlNiu/zh2ru.zip", "baseUrlNiu/ru2zh.zip", "baseUrlXzyTts/zh.zip", "baseUrlXzyTts/ru.zip"]}, {"code": "zh<->de", "resCodes": ["zh", "de", "zhde", "dezh"], "urls": ["baseUrlXzy/de.zip", "baseUrlNiu/zh2de.zip", "baseUrlNiu/de2zh.zip", "baseUrlXzyTts/zh.zip", "baseUrlXzyTts/de.zip"]}, {"code": "zh<->ko", "resCodes": ["zh", "ko", "zhko", "kozh"], "urls": ["baseUrlXzy/ko.zip", "baseUrlNiu/zh2ko.zip", "baseUrlNiu/ko2zh.zip", "baseUrlXzyTts/zh.zip", "baseUrlXzyTts/ko.zip"]}, {"code": "en<->ja", "resCodes": ["en", "ja", "enja", "jaen"], "urls": ["baseUrlXzy/ja.zip", "baseUrlNiu/en2ja.zip", "baseUrlNiu/ja2en.zip", "baseUrlXzyTts/en.zip", "baseUrlXzyTts/ja.zip"]}, {"code": "en<->fr", "resCodes": ["en", "fr", "enfr", "fren"], "urls": ["baseUrlXzy/fr.zip", "baseUrlNiu/en2fr.zip", "baseUrlNiu/fr2en.zip", "baseUrlXzyTts/en.zip", "baseUrlXzyTts/fr.zip"]}, {"code": "en<->es", "resCodes": ["en", "es", "enes", "esen"], "urls": ["baseUrlXzy/es.zip", "baseUrlNiu/en2es.zip", "baseUrlNiu/es2en.zip", "baseUrlXzyTts/en.zip", "baseUrlXzyTts/es.zip"]}, {"code": "en<->ru", "resCodes": ["en", "ru", "enru", "ruen"], "urls": ["baseUrlXzy/ru.zip", "baseUrlNiu/en2ru.zip", "baseUrlNiu/ru2en.zip", "baseUrlXzyTts/en.zip", "baseUrlXzyTts/ru.zip"]}, {"code": "en<->de", "resCodes": ["en", "de", "ende", "deen"], "urls": ["baseUrlXzy/de.zip", "baseUrlNiu/en2de.zip", "baseUrlNiu/de2en.zip", "baseUrlXzyTts/en.zip", "baseUrlXzyTts/de.zip"]}, {"code": "en<->ko", "resCodes": ["en", "ko", "enko", "koen"], "urls": ["baseUrlXzy/ko.zip", "baseUrlNiu/en2ko.zip", "baseUrlNiu/ko2en.zip", "baseUrlXzyTts/en.zip", "baseUrlXzyTts/ko.zip"]}], "totalSizes": {"baseUrlXzy/cnen.zip": 57806857, "baseUrlXzy/de.zip": 71978179, "baseUrlXzy/es.zip": 73203431, "baseUrlXzy/fr.zip": 72656091, "baseUrlXzy/ja.zip": 45369651, "baseUrlXzy/ko.zip": 65062411, "baseUrlXzy/ru.zip": 77523563, "baseUrlXzy/th.zip": 72101373, "baseUrlXzyTts/zh.zip": 20614665, "baseUrlXzyTts/de.zip": 9587011, "baseUrlXzyTts/en.zip": 14023066, "baseUrlXzyTts/es.zip": 8844354, "baseUrlXzyTts/fr.zip": 9057329, "baseUrlXzyTts/ja.zip": 58020369, "baseUrlXzyTts/ko.zip": 14702092, "baseUrlXzyTts/ru.zip": 11095014, "baseUrlXzyTts/th.zip": 18074808, "baseUrlNiu/de2en.zip": 52107244, "baseUrlNiu/de2zh.zip": 57233261, "baseUrlNiu/en2de.zip": 52191528, "baseUrlNiu/en2es.zip": 52685512, "baseUrlNiu/en2fr.zip": 50815004, "baseUrlNiu/en2ja.zip": 59844788, "baseUrlNiu/en2ko.zip": 58023171, "baseUrlNiu/en2ru.zip": 49510060, "baseUrlNiu/en2zh.zip": 72375687, "baseUrlNiu/es2en.zip": 52664259, "baseUrlNiu/es2zh.zip": 55781749, "baseUrlNiu/fr2en.zip": 50799729, "baseUrlNiu/fr2zh.zip": 57952503, "baseUrlNiu/ja2en.zip": 55256362, "baseUrlNiu/ja2zh.zip": 61364843, "baseUrlNiu/ko2en.zip": 56525112, "baseUrlNiu/ko2zh.zip": 64295398, "baseUrlNiu/ru2en.zip": 49458911, "baseUrlNiu/ru2zh.zip": 55908093, "baseUrlNiu/zh2de.zip": 54115551, "baseUrlNiu/zh2en.zip": 71870704, "baseUrlNiu/zh2es.zip": 52805587, "baseUrlNiu/zh2fr.zip": 54291111, "baseUrlNiu/zh2ja.zip": 62630115, "baseUrlNiu/zh2ko.zip": 62235361, "baseUrlNiu/zh2ru.zip": 52858680}}