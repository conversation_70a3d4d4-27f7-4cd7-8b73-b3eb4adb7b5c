package co.timekettle.speech;

import android.content.Context;
import android.os.Handler;
import android.os.Looper;
import android.text.TextUtils;
import android.util.Log;

import androidx.annotation.NonNull;

import co.timekettle.speech.jni.TmkCustomTranslationJni;
import co.timekettle.speech.recorder.AudioRecorderBase;
import co.timekettle.speech.utils.BytesTrans;
import co.timekettle.speech.utils.AiSpeechLogUtil;
import co.timekettle.speech.utils.HttpsConnection;
import co.timekettle.tmkengine.JsonResponse;
import co.timekettle.tmkengine.TmkSpeechClient;
import co.timekettle.tmkengine.utils.DNSUtil;

import com.google.gson.Gson;

import org.json.JSONArray;
import org.json.JSONException;
import org.json.JSONObject;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.concurrent.ConcurrentHashMap;
import java.util.function.BiFunction;
import java.util.function.Function;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * 主要用于通道任务管理, 管理录音, 播放, 识别, 翻译, 合成
 */
public class AiSpeechManager {
    private static final String TAG = "AiSpeechManager";
    private static final int defaultSampleRateInHz = 16000;

    private static AiSpeechManager instance = null;
    private boolean singleton = false;
    private boolean enableSpeechTranslation = false;
    private boolean enableLID = false;
    private boolean enableCustomTranslation = false;
    private boolean enablePunctuationSegmentation = false;
    private boolean recognizeDisabled = false;
    private boolean synthesizeDisabled = false;
    private boolean translateDisabled = false;
    private boolean playDisabled = false;
    private boolean networkIsConnect = false;
    private boolean onlyOffline = false;
    private Listener listener = null;
    private ConcurrentHashMap<String, AudioChannel> channelMap = new ConcurrentHashMap<>();
    private AudioChannel singletonChannel = null;

    private Context context = null;

    private boolean isExistSession = false;
    private boolean useOpus = true;
    private boolean streamTtsEnbled = true;
    private ArrayList<EngineHost> cacheHosts; // Tmk引擎主机列表
    @Deprecated
    public static String HttpApiBaseUrl; // ip 列表等接口
    public static final String Version = "3.2";

    // 所有调用都切到单一线程调用, 默认为主线程
    private Handler mDelegateHandler;

    private Config cacheConfig;
    public class Config {
        public boolean enableSpeechTranslation = false;
        public boolean enableOpusTransmission = false;
        public boolean enableKafkaLog = false;
        public boolean enablePunctuationSegmentation = false;
        public JSONArray noOpusTTSCodeArray = null;
    }

    private AudioChannel.Listener audioChannelListener = new AudioChannel.Listener() {
        @Override
        public void onVadBegin(AudioChannel channel, long session) {
            if (singleton) {
                AudioChannel sChannel = getSingletonChannel();
                if (sChannel != null) {
                    AiSpeechLogUtil.d(TAG, "onVadBegin : " + channel.getKey() + "["  + session + "], 当前 " + sChannel.getKey() + " 已在工作");
                    return;
                }
            }
            AiSpeechLogUtil.d(TAG, "onVadBegin : " + channel.getKey() + "[" + session);
            RecordManager.shareInstance().openSaveVadData(channel.getKey());

            if (singleton) {
                AudioChannel maxEnergyChannel = null;
                long averageEnergy = 0;

                for (AudioChannel p : channelMap.values()) {
                    // 被禁用的通道不比较能量值
                    if (!p.enabled) {
                        AiSpeechLogUtil.d(TAG, p.getKey() + " 此通道被禁用, 不进行能量比较");
                        continue;
                    }
                    long value = p.getAverageEnergy();
                    AiSpeechLogUtil.d(TAG, "通道 " + p.getKey() + " " + value);
                    if (averageEnergy < value) {
//                        pauseAudioChannel(maxEnergyChannel);
                        maxEnergyChannel = p;
                        averageEnergy = value;
                    }

//                    // 测试另一个耳机能量更高的情况
//                    if (!channel.equals(p)) {
//                        maxEnergyChannel = p;
//                    }
                }
                if(maxEnergyChannel == null){
                    AiSpeechLogUtil.d(TAG, "响应是 : [" + channel.getKey() + "] 但最大能量通道是: NULL" );
                    return;
                }
                if (!channel.equals(maxEnergyChannel)) {
                    AiSpeechLogUtil.d(TAG, "响应是 : [" + channel.getKey() + "]" + " 但最大能量通道是: " + maxEnergyChannel.getKey() + ", 等待唤醒");
                    return;
                }

                setSingletonChannel(maxEnergyChannel);
//                sChannel.setTriggered(); // 防止能量更高但是 vad 未起来的情况

                String srcCode = maxEnergyChannel.getSrcCode();
                String dstCode = maxEnergyChannel.getDstCode();
                AiSpeechLogUtil.d(TAG, maxEnergyChannel.getKey() + " 开始识别: " + srcCode + " " + dstCode);
                doRecognizeTranslateSynthesizeAndPlay(maxEnergyChannel, session);
                AudioChannel finalMaxEnergyChannel = maxEnergyChannel;
                doVadBeginCallback(finalMaxEnergyChannel, session);

            } else {
                String srcCode = channel.getSrcCode();
                String dstCode = channel.getDstCode();

//                long energy = channel.getOutputStream().getMaxSampleEnergy();
//                AiSpeechLogUtil.e(TAG, channel.getKey() + "开始识别: " + srcCode + " " + dstCode + " " + energy);
                AiSpeechLogUtil.d(TAG, channel.getKey() + "开始识别: " + srcCode + " " + dstCode);
//                long session = RecognizeManager.shareInstance().start(chkey, srcCode, translateDisabled ? null : dstCode);
                doRecognizeTranslateSynthesizeAndPlay(channel, session);

                doVadBeginCallback(channel, session);
            }
        }

        @Override
        public void onVadEnd(AudioChannel channel, long session) {
            // 特殊情况: 当某通道事件 onVadBegin 来临, 但是能量却比 vad 未激活的通道小, 导致结束时判断为通道不一致
            //2021-06-30 15:08:26.507 17709-17830/com.translation666 E/AiSpeechManager: onVadBegin : [FF:C5:69:B5:E7:12]
            //2021-06-30 15:08:28.021 17709-17830/com.translation666 E/AiSpeechManager: onVadEnd   单实例下当前工作通道: E6:A7:BC:91:94:80, 不允许 FF:C5:69:B5:E7:12 通道处理
            //2021-06-30 15:09:06.475 17709-17830/com.translation666 E/AiSpeechManager: onVadBegin 当前 E6:A7:BC:91:94:80 已在工作
            //2021-06-30 15:09:06.508 17709-17709/com.translation666 E/AiSpeechManager: onVadEnd   : [E6:A7:BC:91:94:80]
            if (singleton) {
                AudioChannel sChannel = getSingletonChannel();
                if (sChannel == null) {
                    AiSpeechLogUtil.d(TAG, "onVadEnd   : [" + channel.getKey() + "[" + session + "], 单实例下当前无工作通道");
                    return;
                } else {
                    if (!channel.equals(sChannel)) {
                        AiSpeechLogUtil.d(TAG, "onVadEnd   : " + channel.getKey() + "[" + session + "], 单实例下当前已有其他工作通道: " + sChannel.getKey());
                        return;
                    }
                }
            }
            AiSpeechLogUtil.d(TAG, "onVadEnd : " + channel.getKey() + "[" + session);
            RecordManager.shareInstance().stopSaveVadData();

            if (singleton) {
                AudioChannel sChannel = getSingletonChannel();
                if (sChannel != null) {
                    RecognizeManager.shareInstance().stop(sChannel.getKey());
                    // FIXME: 2023/4/22 唤醒结束时, 根据当前唤醒任务确定是任务类型, 来做相应的处理
                    doStopRecognize(sChannel, session);
                    doActivityCallback(channel, session,  0.0f);
                    doVadEndCallback(channel, session);
                    resetSingletonChannel();
                } else {
                    AiSpeechLogUtil.d(TAG, "单实例下当前无工作通道: [" + channel.getKey() + "]");
                }

                for (AudioChannel p : channelMap.values()) {
                    p.setUnTriggered();
                }
//                channel.setUnTriggered();
//                resumeAllAudioChannel();
            } else {
                doStopRecognize(channel, session);
                if (session == channel.getCurPickSession()) channel.setUnTriggered();

                doActivityCallback(channel, session,  0.0f);
                doVadEndCallback(channel, session);
            }
        }

        @Override
        public void onActivity(AudioChannel channel, long session, short[] data, float volume) {
            String chkey = channel.getKey();

            doActivityCallback(channel, session, data, volume);
            RecordManager.shareInstance().saveVadData(chkey, BytesTrans.getInstance().Shorts2Bytes(data));
//            RecognizeManager.shareInstance().writeAudio(chkey, BytesTrans.getInstance().Shorts2Bytes(data));
            doWriteRecognize(channel, session, data);
        }
    };

    private RecognizeManager.Listener recognizeListener = new RecognizeManager.Listener() {
        @Override
        public void onRecognizeResult(SpeechTask<Object, String> task, SpeechError error) {
            String chkey = task.chkey;
            long session = task.session;
            boolean isLast = task.response.isLast;
            String text = task.response.data;
            String engine = task.response.engine;
            AiSpeechLogUtil.e(TAG, "recognize chkey:" + chkey + " session:" + session + " isLast:" + isLast + " text:" + text + " engine:" + engine);

            AudioChannel channel = getAudioChannel(chkey);
            if (channel != null) {
                doRecognizeResultCallback(chkey, session, channel.getSrcCode(), channel.getDstCode(), isLast, text, engine, null);
            }

            if (isLast) {
                channel.writeAsrTextToFileHandle("(" + session + ") " + engine + "(" + channel.getSrcCode() + "): " + text + "\n\n");

                resetVadState(chkey, session);
            } else {
                channel.writeAsrTextToFileHandle("(" + session + ") " + text + "...\n");
            }
        }

        @Override
        public void onTranslateResult(SpeechTask<String, String> task, SpeechError error) {
            String chkey = task.chkey;
            long session = task.session;
            boolean isLast = task.response.isLast;
            String text = task.response.data;
            String engine = task.response.engine;
            AiSpeechLogUtil.e(TAG, "translate chkey:" + chkey + " session:" + session + " text:" + text + " engine:" + engine);
            doTranslateResultCallback(chkey, session, isLast, text, engine, null);
            AudioChannel channel = getAudioChannel(chkey);
            if (!synthesizeDisabled && channel != null) {
                doSynthesizeAndPlay(channel, session, text);
            }
        }
    };

    public static AiSpeechManager shareInstance() {
        if (instance == null) {
            instance = new AiSpeechManager();
        }
        return instance;
    }

//    /** 获得实例, 此情况可用于每次生成实例, 以防止前后环境干扰
//     * @return AiSpeechManager 实例
//     */
//    public static AiSpeechManager getInstance() {
//        return new AiSpeechManager();
//    }

    /**
     * 获取 ip 列表
     */
    public String[] getAllRemoteHost() {
        return new String[0];
    }

    /**
     * 配置订阅码, 获取 ip 池, 并开启 looper 处理服务器事件; 一般用于进入某一个模式时初始化环境
     *
     * @param context 上下文
     * @param subscriptionKey 授权码
     */
    public void create(Context context, String subscriptionKey) {
        this.create(context.getApplicationContext(), subscriptionKey, null);
    }

    /**
     * 配置订阅码, 获取 ip 池, 并开启 looper 处理服务器事件; 一般用于进入某一个模式时初始化环境
     *
     * @param context 上下文
     * @param subscriptionKey 授权码
     * @param host 使用指定的服务器, 如局域网下的服务器 "************"
     */
    public void create(Context context, String subscriptionKey, EngineHost host) {
        if (isExistSession) {
            AiSpeechLogUtil.e(TAG, "create: 当前已存在会话, 不重复创建");
            return;
        }
        isExistSession = true;
        singleton = false;

//        TmkSpeechClient.shareInstance().setSpecificEngine(JsonRequest.Engine.Microsoft.name);
        TmkSpeechClient.shareInstance().createUtility(context, subscriptionKey);
        TmkSpeechClient.shareInstance().setSpecificHost(host); // 重置, 值为 null 时才能通过列表等查找
        if (host == null) {
            this.fetchHosts();
            this.fetchConfig();
        }
        initContext(context.getApplicationContext());
        reset();
    }

    public void reset() {
        this.resetSingletonChannel();
        this.setRecognizeDisabled(false);
        this.setTranslateDisabled(false);
        this.setSynthesizeDisabled(false);
        this.setPlayDisabled(false);
        this.enableOpusTransmission(false);
    }

    private void initContext(Context context) {
        this.context = context.getApplicationContext();
        mDelegateHandler = new Handler(Looper.getMainLooper());

        RecognizeManager.shareInstance().setContext(this.context);
        SynthesizeManager.shareInstance().setContext(this.context);
        TranslateManager.shareInstance().setContext(this.context);

        RecognizeManager.shareInstance().setListener(recognizeListener);

        SpeakManager.shareInstance().createSpeakers(this.context);
        RecordManager.shareInstance().setContext(this.context);
        AiSpeechLogUtil.startFileLogger();
    }

    /**
     * 销毁通道配置, 工作器等, 一般用于退出某一个模式/场景
     */
    public void destroy() {
        this.stopAllWorker();
        RecordManager.shareInstance().stop2();
        RecordManager.shareInstance().clear(); // 清空通道
        SpeakManager.shareInstance().reset(); // 重置所有 speaker 参数
        removeAllChannel();
        resetSingletonChannel();

        TranslateManager.shareInstance().disableCustomTranslate(null);
        TmkSpeechClient.shareInstance().destoryUtility();
        AiSpeechLogUtil.stopFileLogger();

        isExistSession = false;
//        this.context = null;
        this.streamTtsEnbled = true;
        this.enableLID(false);
        this.enableCustomTranslation(false);
        this.enablePunctuationSegmentation(false);
    }

    /**
     * 指定引擎服务器
     * @param host 指定服务器 ip, 端口
     */
    public void setSpecificHost(EngineHost host) {
        TmkSpeechClient.shareInstance().setSpecificHost(host);
    }

    @Deprecated
    public void reconnect(String subscriptionKey) {
        this.stopAllWorker();
        resetSingletonChannel();

        TranslateManager.shareInstance().disableCustomTranslate(null);

        isExistSession = false;

        new Handler(Looper.getMainLooper()).postDelayed(() -> {
            this.create(context, subscriptionKey);
        }, 1000);
    }

    /**
     * 如
     * 2001 "授权失败"           "Authorization failed"
     * 2002 "缺少参数"           "Missing parameter"
     *
     * @param code 错误码
     * @returns String 错误具体描述
     */
    String getDescriptionByCode(int code) {
        JsonResponse.Status engineStatus = JsonResponse.Status.get(code);
        return engineStatus.getDesc();
    }

    /**
     * 单例模式, 多通道无法同时识别; 多支通道同时工作(enable)时, 同一时间只会唤醒一个通道; 目前用于tmk 001同传模式下的抢麦
     * @param singleton 是否单实例
     */
    public void setSingleWakeup(boolean singleton) {
        AiSpeechLogUtil.e(TAG, "设置 singleton: " + singleton);
        this.singleton = singleton;
    }

    /**
     * 需要强制停止通道(chkey)某次(session)"唤醒"状态, 主要是使用在识别过程中网络报错或者提前中断的情况
     * @param chkey 通道(chkey)
     * @param session 某个任务 id
     */
    private void resetVadState(String chkey, long session) {
        if (RecognizeManager.shareInstance().findWorker(session) || SpeechTranslationManager.shareInstance().findWorker(session)) {
            AudioChannel channel = getAudioChannel(chkey);
            if (channel != null) {
                channel.resetVadState(session);
            }
            else {
                AiSpeechLogUtil.e(TAG, chkey + " 找不到对应通道, " + session);
            }
        } else {
            AiSpeechLogUtil.e(TAG, chkey + " 不强制 vad 结束 " + session);
        }
    }

    /**
     * 通道(chkey)需要强制停止当前"唤醒"状态
     * @param chkey 通道(chkey)
     */
    private void forceResetVadState(String chkey) {
        AudioChannel channel = getAudioChannel(chkey);
        if (channel != null) {
            channel.forceResetVadState();
        }
        else {
            AiSpeechLogUtil.e(TAG, chkey + " 找不到对应通道");
        }
    }

    private synchronized void resetSingletonChannel() {
        AiSpeechLogUtil.d(TAG, "resetSingletonChannel: 重置当前工作通道");
        singletonChannel = null;
    }

    private synchronized void setSingletonChannel(AudioChannel p) {
        singletonChannel = p;
    }

    /** 单实例(singleton=true)模式下, 当前工作通道
     * @return 返回当前工作的单实例通道
     */
    private synchronized AudioChannel getSingletonChannel() {
        return singletonChannel;
    }

    /**
     * 是否开启语音翻译
     * @param enable 是否开启
     */
    public void enableSpeechTranslation(boolean enable) {
        this.enableSpeechTranslation = enable;
    }

    /**
     * 多通道(多语言)时是否开启语言检测(LanguageIdentification, LID), 注意需要在 create() 后调用开启
     * @param enable 是否开启
     */
    public void enableLID(boolean enable) {
        this.enableLID = enable;
    }
    /**
     * 是否自定义翻译
     * @param enable 是否启用
     */
    public void enableCustomTranslation(boolean enable) {
        this.enableCustomTranslation = enable;
    }

    public boolean isCustomTranslationEnable() {
        return this.enableCustomTranslation;
    }

    /**
     * 更新模型和语言词表
     *
     * @param code 语言代码, 如 en 或 en-US
     * @param modePath 模型路径
     * @throws Exception 异常信息
     */
    public void updateCustomTranslation(String code, String modePath) throws Exception {
        CustomTranslationManager.getInstance().updateCustomTranslation(code, modePath, null);
    }

    /**
     * 更新模型和语言词表
     *
     * @param code 语言代码, 如 en 或 en-US
     * @param words 改语言的词表
     * @throws Exception 异常信息
     */
    public void updateCustomTranslation(String code, Map<String, String> words) throws Exception {
        CustomTranslationManager.getInstance().updateCustomTranslation(code, null, words);
    }

    /**
     * 是否开启标点符号切分
     * @param enable 是否启用
     */
    public void enablePunctuationSegmentation(boolean enable) {
        this.enablePunctuationSegmentation = enable;
        AiSpeechLogUtil.d(TAG, "是否启用标点断句功能: " + enable);
    }

    public boolean isPunctuationSegmentation() {
        return this.enablePunctuationSegmentation;
    }

    /**
     * 是否禁用识别, 用于只录音和 vad 处理打标记等
     * @param disabled 是否禁用
     */
    public void setRecognizeDisabled(boolean disabled) {
        this.recognizeDisabled = disabled;
    }

    /**
     * 是否禁用翻译, 禁用的话也不会有自动合成和播放, 用于只识别的情况
     * @param disabled 是否禁用
     */
    public void setTranslateDisabled(boolean disabled) {
        this.translateDisabled = disabled;
    }

    /** 
     * 是否禁用合成, 禁用的话也不会有播放, 用于只识别和翻译的情况
     * @param disabled 是否禁用
     */
    public void setSynthesizeDisabled(boolean disabled) {
        this.synthesizeDisabled = disabled;
    }

    public boolean isSynthesizeDisabled() {
        return synthesizeDisabled;
    }

    /**
     * 是否禁用播放
     * @param disabled 是否禁用
     */
    public void setPlayDisabled(boolean disabled) {
        this.playDisabled = disabled;
    }

    public boolean isPlayDisabled() {
        return playDisabled;
    }

    /**
     * 设置网络是否连接, 未连接的话, 只会使用离线的识别/翻译/合成器
     * 使用 setOnlyOffline 代替
     * @param connected 网络连接情况
     */
    @Deprecated
    public void setNetworkIsConnected(boolean connected) {
        networkIsConnect = connected;

        RecognizeManager.shareInstance().setOnlyOffline(!connected);
        SynthesizeManager.shareInstance().setOnlyOffline(!connected);
        TranslateManager.shareInstance().setOnlyOffline(!connected);
    }

    /**
     * 使用 isOnlyOffline 代替
     */
    @Deprecated
    public boolean networkIsConnected() {
        return networkIsConnect;
    }


    /**
     * 设置是否只允许使用离线, 若是 true, 则只会使用离线的识别/翻译/合成器
     * 使用 setOnlyOffline 代替
     * @param offline 是否只允许离线模块工作
     */
    public void setOnlyOffline(boolean offline) {
        onlyOffline = offline;

        RecognizeManager.shareInstance().setOnlyOffline(onlyOffline);
        SynthesizeManager.shareInstance().setOnlyOffline(onlyOffline);
        TranslateManager.shareInstance().setOnlyOffline(onlyOffline);
    }

    /**
     * @return 是否只允许离线模块工作
     */
    public boolean isOnlyOffline() {
        return onlyOffline;
    }

    /**
     * 与服务器的数据传输是否使用 opus 压缩后传输的方式, 设置实时有效无需再 create 后设置
     * @param useOpus 是否使用
     * @deprecated 外部暂时不再使用此接口控制
     */
    public void setUseOpus(boolean useOpus) {
        ISpeechConstant.useOpus = useOpus; // 记录是否使用 opus, 解耦
        this.useOpus = useOpus;
    }

    /**
     * 与服务器的数据传输是否使用 opus 压缩后传输的方式, 设置实时有效无需再 create 后设置
     * @param useOpus 是否使用
     */
    public void enableOpusTransmission(boolean useOpus) {
        ISpeechConstant.useOpus = useOpus; // 记录是否使用 opus, 解耦
        this.useOpus = useOpus;
    }

    /**
     * 服务端返回不用opus解压的语言列表
     * @param noOpusTTSCodeArray 是否使用
     */
    public void setNoOpusTTSCodeArray(JSONArray noOpusTTSCodeArray) {
        TmkSpeechClient.noOpusTTSCodeArray = noOpusTTSCodeArray; // 记录是否使用 opus, 解耦
        AiSpeechLogUtil.d(TAG,"不使用opus解码语言列表：" + noOpusTTSCodeArray);
    }

    /**
     * 是否启用流式 tts, 在单个会话(单个气泡)中会收到多句话(isLast), 若开启, 则收到isLast则合成播放, 否则等会话结束才合成播放
     * @param enable 是否启用
     */
    public void setStreamTts(boolean enable) {
        streamTtsEnbled = enable;
    }

    /**
     * 音量回调周期, 默认是3(即 3*16ms); 根据需要设定来控制音量回调周期, 默认为 3*16ms 计算一次音量并返回
     * @param size 大小
     */
    public void setActivityVolumeDuration(int size) {
        for (AudioChannel channel : channelMap.values()) {
            channel.setVolumeCacheSize(size);
        }
    }

    /**
     * 设置监听器, 监听当前某个通道的状态
     * @param listener 监听器
     */
    public void setListener(Listener listener) {
        this.listener = listener;
    }

    /**
     * @param sqListener 录音质量监听, 监听是否丢包等
     */
    public void setSqListener(RecordManager.ServiceQualityListener sqListener) {
        RecordManager.shareInstance().setSqListener(sqListener);
    }

    /**
     * 开启录音, 录音数据会直接流向通道进行识别任务处理, 事件响应通过 Listener 回调
     * @param recorder 录音器名称, 查看 ISpeechConstant.RECORDER
     * @param recorder_options 录音器选项, 查看 AudioRecordOptions
     */
    public void startRecorder(String recorder, Map<String, Object> recorder_options) {
        RecordManager.shareInstance().start2(recorder, recorder_options, new AudioRecorderBase.Listener() {
            @Override
            public void onSuccess() {

            }

            @Override
            public void onRecording(String key, short[] data, boolean needVad, Boolean isVoice) {
                if (needVad) {
                    writeAudioToChannel(key, data);
                } else {
                    writeAudioToChannel(key, data, isVoice);
                }
            }

            @Override
            public void onError(String reason) {

            }
        });
    }

    /**
     * 停止所有的录音器
     */
    public void stopAllRecorder() {
        RecordManager.shareInstance().stop();
    }

    /**
     * 停止某个录音器
     * @param recorder 录音器名称
     */
    public void stopRecorder(String recorder) {
        RecordManager.shareInstance().stop(recorder);
    }

    /**
     * 静音所有录音器
     */
    public void muteAllRecorder() {
        RecordManager.shareInstance().muteAllRecorder();
    }

    /**
     * 解除静音所有录音器
     */
    public void unmuteAllRecorder() {
        RecordManager.shareInstance().unmuteAllRecorder();
    }

    /**
     * 录音暂停, 数据流不会流入通道
     * @param name 录音器名称
     */
    public void pauseRecorder(String name) {
        RecordManager.shareInstance().pauseRecorder(name);
    }

    /**
     * 录音恢复, 数据流会流入通道
     * @param name 录音器名称
     */
    public void resumeRecorder(String name) {
        RecordManager.shareInstance().resumeRecorder(name);
    }


//    public long recognize(final String chkey, final String text, final String code, Function<String, Object> callback) {
//        SpeechTask<Object, String> task = new SpeechTask<>(SpeechTask.TaskType.ASR, chkey, System.currentTimeMillis());
//        task.request.code = code;
////        task.request.dstCode = dstCode;
//        task.request.data = text;
//        this.recognize(task, (SpeechTask<Object, String> t) -> {
//            if (callback != null) callback.apply(task.response.data);
//            return null;
//        });
//        return task.session;
//    }

    /**
     * 翻译接口, 任何场景
     * @param chkey 通道 key, 只作为标识用, 可以为 ""
     * @param text 需要翻译的文本
     * @param code 需要翻译的文本的语言code
     * @param dstCode 目标语言 code
     * @param needCustomMt 已无效, 选择是否使用自定义翻译
     * @param customMtTableId 自定义翻译表 id
     * @param callback 翻译回调
     * @return 返回当前任务的 id, 可用于取消
     */
    public long translate(final String chkey, final String text, final String code, final String dstCode, boolean needCustomMt, int customMtTableId, Function<String, Object> callback) {
        SpeechTask<String, String> task = new SpeechTask<>(SpeechTask.TaskType.MT, chkey, System.currentTimeMillis());
        task.request.code = code;
        task.request.dstCode = dstCode;
        task.request.data = text;
        this.translate(task, (SpeechTask<String, String> t) -> {
            if (callback != null) callback.apply(t.response.data);
            return null;
        });
        return task.session;
    }

    /**
     * 翻译接口, 任何场景
     * @param text 需要翻译的文本
     * @param code 需要翻译的文本的语言code
     * @param dstCode 目标语言 code
     * @param callback 翻译回调
     * @return 返回当前任务的 id, 可用于取消
     */
    public long translate(final String text, final String code, final String dstCode, Function<String, Object> callback) {
        return this.translate("temp", text, code, dstCode, false, -1, callback);
    }

    /**
     * 翻译接口, 任何场景
     * @param text 需要翻译的文本
     * @param code 需要翻译的文本的语言code
     * @param dstCode 目标语言 code
     * @param callback 翻译回调
     * @return 返回当前任务的 id, 可用于取消
     */
    public long translate(final String chkey, final String text, final String code, final String dstCode, String module, Function<String, Object> callback) {
        SpeechTask<String, String> task = new SpeechTask<>(SpeechTask.TaskType.MT, chkey, System.currentTimeMillis());
        task.request.code = code;
        task.request.dstCode = dstCode;
        task.request.data = text;
        task.request.module = module;
        this.translate(task, (SpeechTask<String, String> t) -> {
            if (callback != null) callback.apply(t.response.data);
            return null;
        });
        return task.session;
    }

    /**
     * 翻译接口, 一般使用在模式页中, 根据通道做具体任务
     * @param channel 通道
     * @param text 需要翻译的文本
     * @param code 需要翻译的文本的语言code
     * @param dstCode 目标语言 code
     * @param callback 翻译回调
     * @return 返回当前任务的 id, 可用于取消
     */
    public long translate(final AudioChannel channel, final String text, final String code, final String dstCode, Function<String, Object> callback) {
        SpeechTask<String, String> task = new SpeechTask<>(SpeechTask.TaskType.MT, channel.getKey(), System.currentTimeMillis());
        task.request.code = code;
        task.request.dstCode = dstCode;
        task.request.data = text;

        this.translate(task, (SpeechTask<String, String> t) -> {
            if (callback != null) callback.apply(t.response.data);
            return null;
        });
        return task.session;
    }

    /**
     * 播放某个文本, 实际为合成后播放
     * @param chkey 通道 key, 会找到对应通道
     * @param text 需要合成后播放的文本
     * @param code 合成文本的语言 code
     * @param callback 播放完成的回调
     * @return 返回当前任务的 id, 可用于取消
     */
    public long playText(final String chkey, final String text, final String code, Function<Long, Object> callback) {
        long timestamp = System.currentTimeMillis();
        AudioChannel channel = getAudioChannel(chkey);
        if (channel != null) {
            return this.playText(channel, text, code, callback);
        } else {
            AiSpeechLogUtil.e(TAG, "play: 找不到通道 : " + chkey);
        }
        return timestamp;
    }

    /**
     * 播放某个文本, 实际为合成后播放
     * @param channel 通道
     * @param text 需要合成后播放的文本
     * @param code 合成文本的语言 code
     * @param callback 播放完成的回调
     * @return 返回当前任务的 id, 可用于取消
     */
    public long playText(final AudioChannel channel, final String text, final String code, Function<Long, Object> callback) {
        long timestamp = System.currentTimeMillis();
        return this.playText(channel, timestamp, text, code, callback);
    }

    /**
     * 播放某个文本, 实际为合成后播放
     * @param channel 通道
     * @param session 指定任务 id
     * @param text 需要合成后播放的文本
     * @param code 合成文本的语言 code
     * @param callback 播放完成的回调
     * @return 返回当前任务的 id(session), 可用于取消
     */
    public long playText(final AudioChannel channel, final long session, final String text, final String code, Function<Long, Object> callback) {
        long timestamp = session;
        String speakerType = channel.getSpeakerType();
        Object extData = channel.isCanHearEcho() ? null : channel.getRole();

        SpeechTask<String, byte[]> ttsTask = new SpeechTask<>(SpeechTask.TaskType.TTS, channel.getKey(), timestamp);
        ttsTask.request.code = code;
        ttsTask.request.data = text;

        // 其他参数, 类似tts合成声音参数
        if (channel.optsForEngine != null && channel.optsForEngine.size() > 0) {
            if (ttsTask.request.opts != null) {
                Map<String, Object> _opts = new HashMap<>(ttsTask.request.opts);
                _opts.putAll(channel.optsForEngine);
                ttsTask.request.opts = _opts;
            } else {
                ttsTask.request.opts = channel.optsForEngine;
            }
        }

        // 合成
        this.synthesize(ttsTask, (SpeechTask<String, byte[]> task) -> {
            SpeechTask<byte[], Object> playTask = new SpeechTask<>(SpeechTask.TaskType.PLAY, channel.getKey(), timestamp);
            playTask.request.module = speakerType;
            playTask.request.extDevice = extData;
            playTask.request.data = task.response.data;
            // 播放
            this.play(playTask, null, (pctx) -> {
                if (callback != null) callback.apply(timestamp);
                return null;
            });
            return null;
        });
        return timestamp;
    }

    public long synthesizeText(final Context context, final String code, final String text, Function<Long, Object> callback) {
        long timestamp = System.currentTimeMillis();
        SpeechTask<String, byte[]> ttsTask = new SpeechTask<>(SpeechTask.TaskType.TTS, "temp", timestamp);
        ttsTask.request.code = code;
        ttsTask.request.data = text;

        // 合成
        this.synthesize(ttsTask, (SpeechTask<String, byte[]> task) -> {
            if (callback != null) {
                callback.apply(timestamp);
            }
            return null;
        });
        return timestamp;
    }


    public long synthesizeTextBytes(final Context context, final String code, final String text, final AudioChannel channel, Function<byte[], Object> callback) {
        long timestamp = System.currentTimeMillis();
        SpeechTask<String, byte[]> ttsTask = new SpeechTask<>(SpeechTask.TaskType.TTS, "temp", timestamp);
        ttsTask.request.code = code;
        ttsTask.request.data = text;
        // 其他参数, 类似tts合成声音参数
        if (channel.optsForEngine != null && channel.optsForEngine.size() > 0) {
            if (ttsTask.request.opts != null) {
                Map<String, Object> _opts = new HashMap<>(ttsTask.request.opts);
                _opts.putAll(channel.optsForEngine);
                ttsTask.request.opts = _opts;
            } else {
                ttsTask.request.opts = channel.optsForEngine;
            }
        }
//        ttsTask.request.module = text;

        // 合成
        this.synthesize(ttsTask, (SpeechTask<String, byte[]> task) -> {
            if (callback != null) {
                callback.apply(task.response.data);
            }
            return null;
        });
        return timestamp;
    }


    /**
     * 使用默认 speaker(PHONE) 播放某个文本, 实际为合成后播放
     * @param text 需要合成后播放的文本
     * @param code 合成文本的语言 code
     * @param callback 播放完成的回调
     * @return 返回当前任务的 id, 可用于取消
     */
    public long playText(final String text, final String code, Function<Long, Object> callback) {
        String speakerType = ISpeechConstant.SPEAKER.PHONE.toString();
        return this.playText(text, code, speakerType, null, callback);
    }

    /**
     * 使用 speakerType 播放某个文本, 实际为合成后播放
     * @param text 需要合成后播放的文本
     * @param code 合成文本的语言 code
     * @param speakerType speaker 类型
     * @param extData 排除某个角色(目前只用于某类角色不播放)
     * @param callback 播放完成的回调
     * @return 返回当前任务的 id, 可用于取消
     */
    public long playText(final String text, final String code, final String speakerType, final Object extData, Function<Long, Object> callback) {
        long timestamp = System.currentTimeMillis();
        return this.playText(timestamp, text, code, speakerType, null, callback);
    }

    /**
     * 使用 speakerType 播放某个文本, 实际为合成后播放
     * @param session 指定任务 id
     * @param text 需要合成后播放的文本
     * @param code 合成文本的语言 code
     * @param speakerType speaker 类型
     * @param extData 排除某个角色(目前只用于某类角色不播放)
     * @param callback 播放完成的回调
     * @return 返回当前任务的 id, 可用于取消
     */
    public long playText(final long session, final String text, final String code, final String speakerType, final Object extData, Function<Long, Object> callback) {
        long timestamp = session;
        final String chkey = "temp-" + (int)(Math.random() * 10000);
        SpeechTask<String, byte[]> ttsTask = new SpeechTask<>(SpeechTask.TaskType.TTS, chkey, timestamp);
        ttsTask.request.code = code;
        ttsTask.request.data = text;

        // 合成
        this.synthesize(ttsTask, (SpeechTask<String, byte[]> task) -> {
            SpeechTask<byte[], Object> playTask = new SpeechTask<>(SpeechTask.TaskType.PLAY, chkey, timestamp);
            playTask.request.module = speakerType == null ? ISpeechConstant.SPEAKER.PHONE.toString() : speakerType;
            playTask.request.extDevice = extData;
            playTask.request.data = task.response.data;
            // 播放
            this.play(playTask, null, (pctx) -> {
                if (callback != null) callback.apply(timestamp);
                return null;
            });
            return null;
        });
        return timestamp;
    }

    /**
     * 使用 speakerType 播放 sound
     * @param sound 声音数据
     * @param speakerType speaker 类型, 查看 ISpeechConstant.SPEAKER
     * @param callback 播放完成的回调
     * @return 返回当前任务的 id, 可用于取消
     */
    public long play(final byte[] sound, final String speakerType, Function<Long, Object> callback) {
        final String chkey = "temp-" + (int)(Math.random() * 10000);
        SpeechTask<byte[], Object> task = new SpeechTask<>(SpeechTask.TaskType.PLAY, chkey, System.currentTimeMillis());
        task.request.data = sound;
        task.request.module = speakerType;
        task.request.extDevice = null;

        this.play(task, null, (SpeechTask<byte[], Object> t) -> {
            if (callback != null) callback.apply(t.session);
            return null;
        });
        return task.session;
    }

    /**
     * 使用 speakerType 播放 sound
     * @param sound 声音数据
     * @param speakerType speaker 类型, 查看 ISpeechConstant.SPEAKER
     * @param extDevice 排除此设备(角色)的播放
     * @param callback 播放完成的回调
     * @return 返回当前任务的 id, 可用于取消
     */
    public long play(final byte[] sound, final String speakerType, String extDevice, Function<Long, Object> callback) {
        final String chkey = "temp-" + (int)(Math.random() * 10000);
        SpeechTask<byte[], Object> task = new SpeechTask<>(SpeechTask.TaskType.PLAY, chkey, System.currentTimeMillis());
        task.request.data = sound;
        task.request.module = speakerType;
        task.request.extDevice = extDevice;

        this.play(task, null, (SpeechTask<byte[], Object> t) -> {
            if (callback != null) callback.apply(t.session);
            return null;
        });
        return task.session;
    }

    private long recognize(SpeechTask<Object, String> task, Function<SpeechTask<Object, String>, Object> callback) {
        if (!isExistSession) {
            AiSpeechLogUtil.e(TAG, "isExistSession is false, 不进行识别");
            return 0L;
        }

        return RecognizeManager.shareInstance().start(task, (SpeechTask<Object, String> t) -> {
            if (callback != null) callback.apply(t);
            return null;
        });
    }

    private long translate(SpeechTask<String, String> task, Function<SpeechTask<String, String>, Object> callback) {
        if (!isExistSession) {
            AiSpeechLogUtil.e(TAG, "isExistSession is false, 不进行翻译");
            return 0L;
        }

        return TranslateManager.shareInstance().start(task, (SpeechTask<String, String> t) -> {
            if (callback != null) callback.apply(t);
            return null;
        });
    }

    private long synthesize(SpeechTask<String, byte[]> task, Function<SpeechTask<String, byte[]>, Object> callback) {
        if (!isExistSession) {
            AiSpeechLogUtil.e(TAG, "isExistSession is false, 不进行合成");
            return 0L;
        }

        return SynthesizeManager.shareInstance().start(task, (SpeechTask<String, byte[]> t) -> {
            if (callback != null) callback.apply(t);
            return null;
        });
    }

    private long speechtranslation(SpeechTranslationTask<Object, String> task, Function<SpeechTranslationTask<Object, String>, Object> callback) {
        return SpeechTranslationManager.shareInstance().start(task, (SpeechTranslationTask<Object, String> t) -> {
            if (callback != null) callback.apply(t);
            return null;
        });
    }

    private void play(SpeechTask<byte[], Object> task, Function<SpeechTask<byte[], Object>, Object> sCallback, Function<SpeechTask<byte[], Object>, Object> eCallback) {
        if (!isExistSession) {
            AiSpeechLogUtil.e(TAG, "isExistSession is false, 不进行播放");
            return;
        }

        SpeakManager.shareInstance().play(task, (SpeechTask<byte[], Object> t) -> {
            RecordManager.shareInstance().doSpeakStart();
            if (sCallback != null) sCallback.apply(t);
            return null;
        }, (SpeechTask<byte[], Object> t) -> {
            RecordManager.shareInstance().doSpeakEnd();
            if (eCallback != null) eCallback.apply(t);
            return null;
        });
    }

    // 语音翻译不吃支持的 code
    List<String> SpeechTranslationExcludeLangCodes = Arrays.asList("ur-IN", "ur-PK", "ta-SG", "ta-LK", "ta-MY");
    boolean isSpeechTranslationExclude(String code, String dstcode) {
        return SpeechTranslationExcludeLangCodes.contains(code) || SpeechTranslationExcludeLangCodes.contains(dstcode);
    }

    void doWriteRecognize(AudioChannel channel, long session, short[] data) {
        // FIXME: 2023/4/22 唤醒时, 根据当前唤醒任务确定是任务类型, 来做相应的处理
        // FIXME: 2023/9/27 应该需要把 session 转变成 task
        String code = channel.getSrcCode();
        String dstcode = channel.getDstCode();
        boolean isEnalbeOffline = OfflineManager.getInstance().isEnable(code, dstcode);
        boolean needTranslate = channel.getDstCode() != null && !translateDisabled;
        boolean needSpeechTranslation = enableSpeechTranslation && !isSpeechTranslationExclude(code, dstcode);
        if (!needTranslate || isEnalbeOffline || !enableLID || (this.streamTtsEnbled && this.isPunctuationSegmentation())) {
            RecognizeManager.shareInstance().writeAudio(session, BytesTrans.getInstance().Shorts2Bytes(data));
        } else {
            SpeechTranslationManager.shareInstance().writeAudio(session, BytesTrans.getInstance().Shorts2Bytes(data));
        }
    }

    void doStopRecognize(AudioChannel channel, long session) {
        // FIXME: 2023/4/22 唤醒结束时, 根据当前唤醒任务确定是任务类型, 来做相应的处理
        // FIXME: 2023/9/27 应该需要把 session 转变成 task
        String code = channel.getSrcCode();
        String dstcode = channel.getDstCode();
        boolean isEnalbeOffline = OfflineManager.getInstance().isEnable(code, dstcode);
        boolean needTranslate = channel.getDstCode() != null && !translateDisabled;
        boolean needSpeechTranslation = enableSpeechTranslation && !isSpeechTranslationExclude(code, dstcode);
        if (!needTranslate || isEnalbeOffline || !enableLID || (this.streamTtsEnbled && this.isPunctuationSegmentation())) {
            RecognizeManager.shareInstance().stop(channel.getKey(), session);
        } else {
            SpeechTranslationManager.shareInstance().stop(channel.getKey(), session);
        }
    }

    private long doSpeechTranslationAndPlay(AudioChannel channel, long timestamp, boolean twostep) {
        AiSpeechLogUtil.e(TAG, "开启语音翻译, chkey:" + channel.getKey() + " session:" + timestamp);

        String chkey = channel.getKey();
        String code = channel.getSrcCode();
        String dstcode = channel.getDstCode();
        String speakerType = channel.getSpeakerType();
        Object extData = channel.isCanHearEcho() ? null : channel.getRole();
        SpeechSessionContext context = new SpeechSessionContext(timestamp, chkey, code, dstcode, null);
        context.setTaskType(new SpeechTask.TaskType[]{SpeechTask.TaskType.ASR, SpeechTask.TaskType.MT, SpeechTask.TaskType.TTS, SpeechTask.TaskType.PLAY});
        context.speakerType = speakerType;
        context.extDevice = extData;
        SpeechTranslationTask<Object, String> task = new SpeechTranslationTask<>(SpeechTranslationTask.TaskType.ASR, timestamp, chkey);
        task.request.code = code;
        task.request.dstCode = dstcode;
        task.request.module = twostep ? ISpeechConstant.SPEECHTRANSLATION.SPEECHTRANSLATION_TWOSTEP.getName() : ISpeechConstant.SPEECHTRANSLATION.SPEECHTRANSLATION.getName();
        if (enableLID) task.request.module = ISpeechConstant.SPEECHTRANSLATION.SPEECHTRANSLATION_LID.getName();

        this.speechtranslation(task, (SpeechTranslationTask<Object, String> t) -> {
            if (t.response.error != null) {
                AiSpeechLogUtil.e(TAG, "识别错误返回, chkey:" + t.userData + " session:" + t.workId + " 当前拾音:" + channel.getCurPickSession());
//                if (channel.getCurPickSession() == task.session) {
                resetVadState(chkey, timestamp);
//                }

                channel.writeAsrTextToFileHandle("(" + t.workId + ") " + t.response.engine + "(" + channel.getSrcCode() + "): " + t.response.error.desc + "\n\n");
                doErrorCallback(t.userData, t.workId, t.response.error.code, "asr_err:" + t.response.error.desc);
                return null;
            }
            // 拷贝本次响应结果, 并异步回调
            boolean isFinished = t.response.isFinished;
            boolean isLast = t.response.isLast;
            String asrText = t.response.data;
            String mtText = t.response.tdata;
            String engine = t.response.engine;
            if (isLast) {
                channel.writeAsrTextToFileHandle("(" + t.workId + ") " + engine + "(" + channel.getSrcCode() + "): " + asrText + "\n\n");
            } else {
                channel.writeAsrTextToFileHandle("(" + t.workId + ") " + asrText + "...\n");
            }

            // 自定义翻译
            if (!isFinished && isLast && isCustomTranslationEnable()) {
                SpeechTask mtTask = new SpeechTask(SpeechTask.TaskType.MT, chkey, t.workId);
                mtTask.request.code = code;
                mtTask.request.dstCode = dstcode;
                mtTask.request.data = asrText;
                mtTask.isLast = isLast; // 标记翻译是否到最终结果
                mtTask.isFinished = isFinished; // 标记任务是否已完结
                boolean ret = doCustomTranslate(channel, context, mtTask, mtText, engine);
                if (ret) return null;
            }

            if (!isFinished) {
                String returnCode;
                String returnDstcode;
                if (t.response.codeOfData != null) {
                    returnCode = t.response.codeOfData;
                    returnDstcode = returnCode.equals(t.request.code) ? t.request.dstCode : t.request.code;
                } else {
                    returnCode = t.request.code;
                    returnDstcode = t.request.dstCode;
                }
                doRecognizeResultCallback(t.userData, t.workId, returnCode, returnDstcode, isLast, asrText, engine, null);
                doTranslateResultCallback(t.userData, t.workId, isLast, mtText, engine, null);
            } else {
                doFinishedCallback(t.userData, t.workId, SpeechTranslationTask.TaskType.ASR.value()|SpeechTranslationTask.TaskType.MT.value(), engine);
            }

            if (!isFinished && isLast) {
                if (context.finalMtResults == null) context.finalMtResults = new ArrayList<>();
                boolean ret = context.finalMtResults.add(mtText);
            }

            if (streamTtsEnbled) {
                if (!isFinished && isLast) doSynthesizeAndPlay(mtText, context);
            } else {
                if (isFinished) {
                    if (context.finalMtResults != null) {
                        StringBuilder allRe = new StringBuilder();
                        for (String re : context.finalMtResults) {
                            allRe.append(re);
                        }
                        doSynthesizeAndPlay(String.valueOf(allRe), context);
                    }
                }
            }
            return null;
        });
        return timestamp;
    }

    private long doRecognizeTranslateSynthesizeAndPlay(AudioChannel channel, long timestamp) {
        String code = channel.getSrcCode();
        String dstcode = channel.getDstCode();
        boolean isEnalbeOffline = OfflineManager.getInstance().isEnable(code, dstcode);
        boolean needTranslate = channel.getDstCode() != null && !translateDisabled;
        boolean needSpeechTranslation = enableSpeechTranslation && !isSpeechTranslationExclude(code, dstcode);
        if (!needTranslate || isEnalbeOffline || !enableLID || (this.streamTtsEnbled && this.isPunctuationSegmentation())) {
        } else {
            return doSpeechTranslationAndPlay(channel, timestamp, !needSpeechTranslation);
        }

        if (recognizeDisabled) {
            AiSpeechLogUtil.e(TAG, "doRecognizeTranslateSynthesizeAndPlay: 禁用了 asr");
            return -1;
        }
        String chkey = channel.getKey();
        String speakerType = channel.getSpeakerType();
        Object extData = channel.isCanHearEcho() ? null : channel.getRole();

        boolean isStreamMt = channel.isStreamMt;

        SpeechSessionContext context = new SpeechSessionContext(timestamp, chkey, code, dstcode, null);
        context.setTaskType(new SpeechTask.TaskType[]{SpeechTask.TaskType.ASR, SpeechTask.TaskType.MT, SpeechTask.TaskType.TTS, SpeechTask.TaskType.PLAY});
        context.speakerType = speakerType;
        context.extDevice = extData;
        SpeechTask asrTask = context.getTaskInstance(SpeechTask.TaskType.ASR);
        context.setTask(SpeechTask.TaskType.ASR, asrTask);

        channel.curSpeechContext = context;

        this.recognize(asrTask, (SpeechTask<Object, String> task) -> {
            if (task.response.error != null) {
                AiSpeechLogUtil.e(TAG, "识别错误返回, chkey:" + task.chkey + " session:" + task.session + " 当前拾音:" + channel.getCurPickSession());
//                if (channel.getCurPickSession() == task.session) {
                    resetVadState(chkey, timestamp);
//                }

                channel.writeAsrTextToFileHandle("(" + task.session + ") " + task.response.engine + "(" + channel.getSrcCode() + "): " + task.response.error.desc + "\n\n");
                doErrorCallback(task.chkey, task.session, task.response.error.code, "asr_err:" + task.response.error.desc);
                return null;
            }

            // 拷贝本次响应结果, 并异步回调
            boolean isLast = task.response.isLast;
            boolean isFinished = task.response.isFinished;
            String asrText = task.response.data;
            String asrEngine = task.response.engine;

            if (isLast) {
                channel.writeAsrTextToFileHandle("(" + task.session + ") " + task.response.engine + "(" + channel.getSrcCode() + "): " + task.response.data + "\n\n");
            } else {
                channel.writeAsrTextToFileHandle("(" + task.session + ") " + task.response.data + "...\n");
            }

            // 自定义翻译
            if (!isFinished && isLast && isCustomTranslationEnable()) {
                SpeechTask mtTask = new SpeechTask(SpeechTask.TaskType.MT, chkey, task.session);
                mtTask.request.code = code;
                mtTask.request.dstCode = dstcode;
                mtTask.request.data = asrText;
                mtTask.isLast = isLast; // 标记翻译是否到最终结果
                mtTask.isFinished = isFinished; // 标记任务是否已完结
                boolean ret = doCustomTranslate(channel, context, mtTask, null, asrEngine);
                if (ret) return null;
            }

            if (!isFinished) {
                doRecognizeResultCallback(task.chkey, task.session, code, dstcode, isLast, asrText, asrEngine, null);
            } else {
                doFinishedCallback(task.chkey, task.session, SpeechTranslationTask.TaskType.ASR.value(), asrEngine);
            }
            if (this.translateDisabled || dstcode == null) return null;

            if (isFinished) {
                if (!streamTtsEnbled) {
                    // 此时翻译结果还没出来, 则把最后一个翻译任务的结果当做结束任务
                    SpeechTask<String, String> lastMtTask = context.getLastTask(SpeechTask.TaskType.MT);
                    if (lastMtTask == null) return null;

                    if (lastMtTask.response.data == null) {
                        lastMtTask.isFinished = true;
                    } else {
                        if (context.finalMtResults != null) {
                            StringBuilder allRe = new StringBuilder();
                            for (String re : context.finalMtResults) {
                                allRe.append(re);
                            }
                            doSynthesizeAndPlay(String.valueOf(allRe), context);
                        }
                    }
                }
                return null;
            }

            boolean allowStreamMt = false;
            if (isStreamMt) {
                int preAsrResultLen = 0;
                if (context.hasTask(SpeechTask.TaskType.MT)) {
                    // FIXME: 2023/5/9 取上一个有响应的结果
                    SpeechTask<String, String> lastMtTask = context.getLastTask(SpeechTask.TaskType.MT);
                    if (lastMtTask != null && lastMtTask.request.data != null) {
                        if (lastMtTask.isLast) preAsrResultLen = 0;
                        else preAsrResultLen = lastMtTask.request.data.getBytes().length;
                    }
                }
                if (asrText != null && asrText.getBytes().length - preAsrResultLen > channel.streamMtSection) {
                    allowStreamMt = true;
                }
            }
            if (allowStreamMt || isLast) {
                if (context.invalid) {
                    AiSpeechLogUtil.d(TAG, "doRecognizeTranslateSynthesizeAndPlay: context 已无效, 不翻译");
                    return timestamp;
                }

                if (this.isPunctuationSegmentation() && this.streamTtsEnbled) {
                    context.sentenceParser.hasNewSentence(task, (String newSentence) -> {
                        SpeechTask<String, String> mtTask = context.getTaskInstance(SpeechTask.TaskType.MT);
                        context.setTask(SpeechTask.TaskType.MT, mtTask);
                        mtTask.request.data = newSentence;
                        mtTask.isLast = true; // 标记翻译任务是否到最终任务, 决定是否需要进入合成播放流程
                        mtTask.isFinished = isFinished; // 标记翻译任务是否到最终任务, 决定是否需要进入合成播放流程
                        doTranslate(mtTask, context);
                    });
                    return timestamp;
                }

                SpeechTask mtTask = context.getTaskInstance(SpeechTask.TaskType.MT);
                context.setTask(SpeechTask.TaskType.MT, mtTask);
                mtTask.request.data = asrText;
                mtTask.isLast = isLast; // 标记翻译任务是否到最终任务, 决定是否需要进入合成播放流程
                mtTask.isFinished = isFinished; // 标记翻译任务是否到最终任务, 决定是否需要进入合成播放流程
                this.translate(mtTask, (SpeechTask<String, String> t) -> {
                    if (t.response.error != null) {
                        if (t.isLast) {
                            AiSpeechLogUtil.e(TAG, "last识别结果翻译失败, chkey:" + t.chkey + " session:" + t.session + " code:" + t.response.error.code + " desc:" + t.response.error.desc);
                        }
                        doErrorCallback(t.chkey, t.session, t.response.error.code, t.response.error.desc);
                        return null;
                    }

                    if (t.response.data == null) {
                        doErrorCallback(t.chkey, t.session, -1, "");
                        return null;
                    }

                    doTranslateResultCallback(t.chkey, t.session, t.isLast, t.response.data, t.response.engine, null);
                    if (t.isFinished)
                        doFinishedCallback(t.chkey, t.session, SpeechTranslationTask.TaskType.MT.value(), t.response.engine);
                    if (context.invalid) {
                        AiSpeechLogUtil.d(TAG, "doRecognizeTranslateSynthesizeAndPlay: context 已无效, 不合成和播放");
                        return timestamp;
                    }

//                    if (!t.isFinished && t.isLast) {
//                        if (context.finalMtResults == null) context.finalMtResults = new ArrayList<>();
//                        boolean ret = context.finalMtResults.add(t.response.data);
//                    }
                    // FIXME: 10/14/21 isCompleted 容易在识别得到最终结果后, 多个翻译中间态仍没有返回, 导致回调时多次回调
                    if (streamTtsEnbled) {
                        if (t.isLast) doSynthesizeAndPlay(t.response.data, context);
                    } else {
                        if (t.isFinished) {
                            StringBuilder allRe = new StringBuilder();
                            SpeechTask tempTask = context.getFirstTask(SpeechTask.TaskType.MT);
                            while (tempTask != null) {
                                if (tempTask.isLast) allRe.append(tempTask.response.data);
                                tempTask = tempTask.next;
                            }
                            doSynthesizeAndPlay(String.valueOf(allRe), context);
//                            if (context.finalMtResults != null) {
//                                StringBuilder allRe = new StringBuilder();
//                                for (String re : context.finalMtResults) {
//                                    allRe.append(re);
//                                }
//                                doSynthesizeAndPlay(String.valueOf(allRe), context);
//                            }
                        }
                    }
                    return null;
                });
            }
            return null;
        });

        return timestamp;
    }

    private void doTranslate(SpeechTask<String, String> mtTask, SpeechSessionContext context) {
        this.translate(mtTask, (SpeechTask<String, String> t) -> {
            if (t.response.error != null) {
                if (t.isLast) {
                    AiSpeechLogUtil.e(TAG, "last识别结果翻译失败, chkey:" + t.chkey + " session:" + t.session + " code:" + t.response.error.code + " desc:" + t.response.error.desc);
                }
                doErrorCallback(t.chkey, t.session, t.response.error.code, t.response.error.desc);
                return null;
            }

            if (t.response.data == null) {
                doErrorCallback(t.chkey, t.session, -1, "");
                return null;
            }

            String mtText = t.response.data;
            doTranslateResultCallback(t.chkey, t.session, t.isLast, mtText, t.response.engine, null);
            if (t.isFinished)
                doFinishedCallback(t.chkey, t.session, SpeechTranslationTask.TaskType.MT.value(), t.response.engine);
            if (context.invalid) {
                AiSpeechLogUtil.d(TAG, "doRecognizeTranslateSynthesizeAndPlay: context 已无效, 不合成和播放");
                return 0;
            }

//                    if (!t.isFinished && t.isLast) {
//                        if (context.finalMtResults == null) context.finalMtResults = new ArrayList<>();
//                        boolean ret = context.finalMtResults.add(t.response.data);
//                    }
            // FIXME: 10/14/21 isCompleted 容易在识别得到最终结果后, 多个翻译中间态仍没有返回, 导致回调时多次回调
            AiSpeechLogUtil.d(TAG, "语义断句 合成并播放: " + mtText);
            doSynthesizeAndPlay(mtText, context);
//            if (streamTtsEnbled) {
//                if (t.isLast) doSynthesizeAndPlay(t.response.data, context);
//            } else {
//                if (t.isFinished) {
//                    StringBuilder allRe = new StringBuilder();
//                    SpeechTask tempTask = context.getFirstTask(SpeechTask.TaskType.MT);
//                    while (tempTask != null) {
//                        if (tempTask.isLast) allRe.append(tempTask.response.data);
//                        tempTask = tempTask.next;
//                    }
//                    doSynthesizeAndPlay(String.valueOf(allRe), context);
////                            if (context.finalMtResults != null) {
////                                StringBuilder allRe = new StringBuilder();
////                                for (String re : context.finalMtResults) {
////                                    allRe.append(re);
////                                }
////                                doSynthesizeAndPlay(String.valueOf(allRe), context);
////                            }
//                }
//            }
            return null;
        });
    }

    private void doSynthesizeAndPlay(AudioChannel channel, long session, String text) {
        long timestamp = session;
        String chkey = channel.getKey();
        String speakerType = channel.getSpeakerType();
        Object extData = channel.isCanHearEcho() ? null : channel.getRole();
        String dstcode = channel.getDstCode();

        SpeechSessionContext context = new SpeechSessionContext(timestamp, chkey, null, dstcode, null);
        context.setTaskType(new SpeechTask.TaskType[]{SpeechTask.TaskType.TTS, SpeechTask.TaskType.PLAY});
        SpeechTask ttsTask = context.getTaskInstance(SpeechTask.TaskType.TTS);
        ttsTask.request.data = text;

        SpeechTask playTask = context.getTaskInstance(SpeechTask.TaskType.PLAY);
        playTask.request.module = speakerType;
        playTask.request.extDevice = extData;

        this.doSynthesizeAndPlay(context, ttsTask, playTask);
    }

    private void doSynthesizeAndPlay(String text, SpeechSessionContext context) {
        assert context != null : "context 不能为空";
        String speakerType = context.speakerType;
        Object extData = context.extDevice;

        SpeechTask ttsTask = context.getTaskInstance(SpeechTask.TaskType.TTS);
        ttsTask.request.data = text;

        SpeechTask playTask = context.getTaskInstance(SpeechTask.TaskType.PLAY);
        playTask.request.module = speakerType;
        playTask.request.extDevice = extData;

        this.doSynthesizeAndPlay(context, ttsTask, playTask);
    }

    private void doSynthesizeAndPlay(SpeechSessionContext context, SpeechTask ttsTask, SpeechTask playTask) {
        if (synthesizeDisabled) {
            AiSpeechLogUtil.d(TAG, "doSynthesizeAndPlay: 禁用了 tts 不在合成: " + ttsTask.session);
            return;
        }
        if (context.invalid) {
            AiSpeechLogUtil.d(TAG, "doSynthesizeAndPlay: context 已无效, 不合成: " + context.timestamp);
            return;
        }

        long timestamp = ttsTask.session;
        String chkey = ttsTask.chkey;
        AudioChannel channel = getAudioChannel(chkey);
        if (channel == null) {
            AiSpeechLogUtil.d(TAG, "doSynthesizeAndPlay: 通道为空[" + chkey + "] 不在合成: " + ttsTask.session);
            return;
        }
        if (!channel.isSynthesizeEnabled()) {
            AiSpeechLogUtil.d(TAG, "doSynthesizeAndPlay: 通道[" + chkey + "]禁用了 tts 不在合成: " + ttsTask.session);
            return;
        }

        // 其他参数, 类似tts合成声音参数
        if (channel.optsForEngine != null && channel.optsForEngine.size() > 0) {
            if (ttsTask.request.opts != null) {
                Map<String, Object> _opts = new HashMap<>(ttsTask.request.opts);
                _opts.putAll(channel.optsForEngine);
                ttsTask.request.opts = _opts;
            } else {
                ttsTask.request.opts = channel.optsForEngine;
            }
        }
        // 合成
        this.synthesize(ttsTask, (SpeechTask<String, byte[]> t) -> {
            if (t.response.error != null) {
                doErrorCallback(t.chkey, t.session, t.response.error.code, t.response.error.desc);
                return null;
            }
            doSynthesizeCompletedCallback(chkey, timestamp, t.response.data, t.response.engine);
            if (context.invalid) {
                AiSpeechLogUtil.d(TAG, "doSynthesizeAndPlay: context 已无效, 不播放");
                return null;
            }
            playTask.request.data = t.response.data;

            if (playDisabled) {
                AiSpeechLogUtil.d(TAG, "doSynthesizeAndPlay: 禁用了 tts 播放: " + ttsTask.session);
                return null;
            }
            if (channel != null && !channel.isPlaybackEnabled()) {
                AiSpeechLogUtil.d(TAG, "doSynthesizeAndPlay: 通道[" + chkey + "]禁用了 tts 播放: " + ttsTask.session);
                return null;
            };
            final String ttsText = (String) t.request.data;
            // 播放合成音频时, 记录tts文本
            if (playTask.request.opts != null) {
                playTask.request.opts.put("TtsTextOfSound", ttsText);
            } else {
                playTask.request.opts = new HashMap(){{ put("TtsTextOfSound", ttsText); }};
            }
            // 播放
            this.play(playTask, (pctx) -> {
                String sTtsText = (String) pctx.request.opts.get("TtsTextOfSound");
                doSpeakStartCallback(chkey, timestamp, sTtsText, playTask.request.module, playTask.request.extDevice);
                return null;
            }, (pctx) -> {
                String eTtsText = (String) pctx.request.opts.get("TtsTextOfSound");
                doSpeakEndCallback(chkey, timestamp, eTtsText, playTask.request.module, playTask.request.extDevice);
                return null;
            });
            return null;
        });
    }

//    void doCallback(SpeechTask.TaskType type, AudioChannel channel, SpeechSessionContext context, SpeechResponse.ResponseMessage msg) {
//        runOnDelegateThread(() -> {
//            if (listener != null) listener.onSpeechTranslationResult(channel, context, msg);
//        });
//    }

    void doVadBeginCallback(@NonNull AudioChannel channel, long session) {
        runOnDelegateThread(() -> {
            if (listener != null) listener.onVadBegin(channel, session);
        });
    }

    void doVadEndCallback(@NonNull AudioChannel channel, long session) {
        runOnDelegateThread(() -> {
            if (listener != null) listener.onVadEnd(channel, session);
        });
    }

    void doActivityCallback(@NonNull AudioChannel channel, long session, float volume) {
        runOnDelegateThread(() -> {
            if (listener != null) listener.onActivity(channel, session, volume);
        });
    }

    void doActivityCallback(@NonNull AudioChannel channel, long session, short[] data, float volume) {
        runOnDelegateThread(() -> {
            if (listener != null) listener.onActivityData(channel, session, data, volume);
        });
    }

    void doRecognizeResultCallback(String chkey, long session, String srcCode, String dstCode, boolean isLast, String text, String engine, TmkCustomTranslationJni.TmkCustomTranslationResult ct) {
        runOnDelegateThread(() -> {
            if (listener != null) listener.onRecognizeResult(chkey, session, srcCode, dstCode, isLast, text, engine, ct);
        });

        SpeechResponse.ResponseMessage msg = new SpeechResponse.ResponseMessage();
        msg.srcCode = srcCode;
        msg.dstCode = dstCode;
        msg.asrText = text;
        msg.mtText = null;
        msg.isCustomTranslation = ct != null;
        msg.isLast = isLast;
        msg.engine = engine;
        msg.type = SpeechTask.TaskType.ASR.value();
        AudioChannel channel = getAudioChannel(chkey);
        doSpeechTranslationResultCallback(channel, null, msg);
    }

    void doTranslateResultCallback(String chkey, long session, boolean isLast, String text, String engine, TmkCustomTranslationJni.TmkCustomTranslationResult ct) {
        runOnDelegateThread(() -> {
            if (listener != null) listener.onTranslateResult(chkey, session, isLast, text, engine, ct);
        });

        SpeechResponse.ResponseMessage msg = new SpeechResponse.ResponseMessage();
        msg.asrText = null;
        msg.mtText = text;
        msg.isCustomTranslation = ct != null;
        msg.isLast = isLast;
        msg.engine = engine;
        msg.type = SpeechTask.TaskType.ASR.value();
        AudioChannel channel = getAudioChannel(chkey);
        doSpeechTranslationResultCallback(channel, null, msg);
    }

    void doSynthesizeCompletedCallback(String chkey, long session, byte[] data, String engine) {
        runOnDelegateThread(() -> {
            if (listener != null) listener.onSynthesizeCompleted(chkey, session, data, engine);
        });
    }

    void doErrorCallback(String chkey, long session, int code, String message) {
        runOnDelegateThread(() -> {
            if (listener != null) listener.onError(chkey, session, code, message);
        });
    }

    void doSpeakStartCallback(final String chkey, long session, String text, String speakerType, Object extData) {
        runOnDelegateThread(() -> {
            if (listener != null) listener.onSpeakStart(chkey, session, text, speakerType, extData);
        });
    }

    void doSpeakEndCallback(final String chkey, long session, String text, String speakerType, Object extData) {
        runOnDelegateThread(() -> {
            if (listener != null) listener.onSpeakEnd(chkey, session, text, speakerType, extData);
        });
    }

    void doFinishedCallback(String chkey, long session, int type, String engine) {
        runOnDelegateThread(() -> {
            if (listener != null) listener.onFinished(chkey, session, type, engine);
        });
    }

    void doSpeechTranslationResultCallback(AudioChannel channel, SpeechSessionContext context, SpeechResponse.ResponseMessage msg) {
        runOnDelegateThread(() -> {
            if (listener != null) listener.onSpeechTranslationResult(channel, context, msg);
        });
    }

    public boolean doCustomTranslate(AudioChannel channel, SpeechSessionContext context, SpeechTask mtTask, String mtText, String engine) {
        String chkey = channel.getKey();
        String code = channel.getSrcCode();
        String dstcode = channel.getDstCode();

        boolean isLast = mtTask.isLast;
        String asrText = (String) mtTask.request.data;
        long workId = mtTask.session;

        // 自定义翻译 1.替换识别结果
        TmkCustomTranslationJni.TmkCustomTranslationResult ctr = CustomTranslationManager.getInstance().process(asrText, code, dstcode);
        String oriAsrText = ctr != null ? ctr.crText : null;
        String preMtText = ctr != null ? ctr.ctText : null;
        if (preMtText == null || preMtText.isEmpty()) {
            AiSpeechLogUtil.e(TAG,"自定义翻译, 无匹配: [" + asrText + "] -> [" + preMtText + "]");
            return false;
        } else {
            AiSpeechLogUtil.d(TAG,"自定义翻译1.替换识别结果: [" + asrText + "] -> [" + preMtText + "]");
            doRecognizeResultCallback(chkey, workId, code, dstcode, isLast, oriAsrText, engine, ctr);
            if (mtText != null) {
                doTranslateResultCallback(chkey, workId, false, mtText, engine, null);
            }

            // 转换空格等 >>>
            Map<String, String> map = new HashMap<>();
            for (TmkCustomTranslationJni.TmkCustomTranslationMatchResult ctmr : ctr.mrs) {
                String output = ctmr.ctWord.replaceAll("\\s+", "-"); // 使用正则表达式替换空格为连字符
                preMtText = preMtText.replaceAll(Pattern.quote(ctmr.ctWord), Matcher.quoteReplacement(output));
                map.put(ctmr.ctWord, output);
            }
            // <<<< 转换空格等

            // 自定义翻译 2.翻译替换后的识别结果
            mtTask.request.data = preMtText;
            String finalPreMtText = preMtText;
            this.translate(mtTask, (SpeechTask<String, String> t) -> {
                if (t.response.error != null) {
                    if (t.isLast) {
                        AiSpeechLogUtil.e(TAG, "last识别结果翻译失败, chkey:" + t.chkey + " session:" + t.session + " code:" + t.response.error.code + " desc:" + t.response.error.desc);
                    }
                    doErrorCallback(t.chkey, t.session, t.response.error.code, t.response.error.desc);
                    return null;
                }
                String ctMtText = t.response.data;
                AiSpeechLogUtil.d(TAG, "自定义翻译2.替换识别结果后翻译: [" + asrText + "] -> [" + finalPreMtText + "] -> [" + ctMtText + "]");
                // 转换空格等 >>>
                ctMtText = this.replaceHyphensWithSingleHyphen(ctMtText);
                for (Map.Entry<String, String> entry : map.entrySet()) {
                    ctMtText = ctMtText.replace(entry.getValue(), entry.getKey());
                }
                // <<<< 转换空格等

                AiSpeechLogUtil.d(TAG, "自定义翻译3.替换识别结果后翻译: [" + asrText + "] -> [" + finalPreMtText + "] -> [" + ctMtText + "]");
                // 更新自定义翻译文本和词偏移信息
                ctr.ctText = ctMtText;
                for (TmkCustomTranslationJni.TmkCustomTranslationMatchResult ctmr : ctr.mrs) {
                    int index = ctr.ctText.indexOf(ctmr.ctWord);
                    if (index != -1) {
                        ctmr.ctStart = index;
                    }
                }
                doTranslateResultCallback(t.chkey, t.session, t.isLast, ctMtText, t.response.engine, ctr);

                if (!t.isFinished && t.isLast) {
                    if (context.finalMtResults == null) context.finalMtResults = new ArrayList<>();
                    boolean ret = context.finalMtResults.add(t.response.data);
                }

                if (streamTtsEnbled) {
                    if (!t.isFinished && t.isLast) {
                        if (context != null) {
                            doSynthesizeAndPlay(t.response.data, context);
                        } else {
                            doSynthesizeAndPlay(channel, workId, t.response.data);
                        }
                    }
                } else {
                    if (t.isFinished && context.finalMtResults != null) {
                        StringBuilder allRe = new StringBuilder();
                        for (String re : context.finalMtResults) {
                            allRe.append(re);
                        }
                        if (context != null) {
                            doSynthesizeAndPlay(allRe.toString(), context);
                        } else {
                            doSynthesizeAndPlay(channel, workId, allRe.toString());
                        }
                    }
                }
                return null;
            });
            return true;
        }
    }

    public String replaceHyphensWithSingleHyphen(String input) {
        // 使用正则表达式匹配零或多个空格，后跟一个连字符，再跟零或多个空格
        String pattern = "\\s*-\\s*";
        Pattern regex = Pattern.compile(pattern);
        Matcher matcher = regex.matcher(input);

        // 替换匹配到的连字符为一个连字符
        return matcher.replaceAll("-");
    }

    /**
     * 停止所有任务(asr, mt, tts, play)
     */
    public void stopAllWorker() {
        RecognizeManager.shareInstance().stopAllWorker();
        TranslateManager.shareInstance().stopAllWorker();
        SynthesizeManager.shareInstance().stopAllWorker();
        SpeakManager.shareInstance().stop();
    }

    /**
     * 移除所有的通道
     */
    public void removeAllChannel() {
        RecordManager.shareInstance().clear();
        channelMap.clear();
    }

    /** 添加通道, 如果 chkey 已存在 channelMap 中, 会移除再添加, 相当于更新
     * @param options 通过选项生成通道
     * @return 通道名
     */
    public String addChannel(final Map<String, Object> options) {
        if (options == null) {
            AiSpeechLogUtil.e(TAG, "addChannel: options 为 null");
            return null;
        }

        String chkey;
        int sampleRate = defaultSampleRateInHz;
        boolean writeToFile = false;
        if (options.containsKey(AudioChannelOptions.KEY)) {
            chkey = (String) options.get(AudioChannelOptions.KEY);
        } else {
            return null;
        }
        if (options.containsKey("sampleRate")) {
            sampleRate = (int) options.get("sampleRate");
        }
        if (options.containsKey(AudioChannelOptions.WRITE_TO_FILE)) {
            writeToFile = (boolean) options.get(AudioChannelOptions.WRITE_TO_FILE);
        }

        AudioChannel channel = new AudioChannel(this.context, chkey, sampleRate, writeToFile);
        // 存在的话先移除
        if (channelMap.containsKey(chkey)) channelMap.remove(chkey);
        channel.setListener(audioChannelListener);
        channel.openVad(0.9f, 0);

        AiSpeechLogUtil.d(TAG, "添加channel: " + channel.getKey() + " " + options.toString());
        channelMap.put(channel.getKey(), channel);

        this.updateChannel(options);

        return channel.getKey();
    }

    /** 暂时不用, 通过 addChannel(final HashMap<String, Object> options)
     * @param channel 通道
     * @return 通道名
     */
    public String addChannel(AudioChannel channel) {
        if (channelMap.containsKey(channel.getKey())) {
            channelMap.remove(channel.getKey());
        }
        channelMap.put(channel.getKey(), channel);
        channel.setListener(audioChannelListener);
        channel.openVad(0.9f, 0);

        AiSpeechLogUtil.d(TAG, "添加channel: " + channel);
        return channel.getKey();
    }

    /** 更新通道, 必须有参数 AudioChannelOptions.KEY, 参数列表查看 AudioChannelOptions
     * @param options 选项
     */
    public void updateChannel(final Map<String, Object> options) {
        String chkey = (String) options.get(AudioChannelOptions.KEY);
        if (chkey == null ||  !channelMap.containsKey(chkey)) {
            return;
        }
        AudioChannel channel = channelMap.get(chkey);
        this.updateChannel(channel, options);
    }

    /** 更新通道, 参数列表查看 AudioChannelOptions
     * @param options 选项
     */
    public void updateChannel(final String chkey, final Map<String, Object> options) {
        if (chkey == null) {
            return;
        }

        AudioChannel channel = channelMap.get(chkey);
        this.updateChannel(channel, options);
    }

    /** 更新通道, 参数列表查看 AudioChannelOptions
     * @param options 选项
     */
    public void updateChannel(final AudioChannel channel, final Map<String, Object> options) {
        if (options.containsKey(AudioChannelOptions.BYTE_PER_PACKET)) {
            channel.nBytePerPacket = (int) options.get(AudioChannelOptions.BYTE_PER_PACKET);
        }

        // 是否打上 vad 标签, 用于调试
        if (options.containsKey(AudioChannelOptions.SHOULD_GEN_VAD_TAG)) {
            channel.setMarkVadDataTag((boolean) options.get(AudioChannelOptions.SHOULD_GEN_VAD_TAG));
        }
        if (options.containsKey(AudioChannelOptions.VAD_DetectPattern)) {
            channel.setVadDetectPattern((int) options.get(AudioChannelOptions.VAD_DetectPattern));
        }
        if (options.containsKey(AudioChannelOptions.GainToEngine)) {
            channel.setGain((float) options.get(AudioChannelOptions.GainToEngine));
        }

        if (options.containsKey(AudioChannelOptions.SRC_CODE)) {
            channel.setSrcCode((String) options.get(AudioChannelOptions.SRC_CODE));
        }

        if (options.containsKey(AudioChannelOptions.DST_CODE)) {
            channel.setDstCode((String) options.get(AudioChannelOptions.DST_CODE));
        }

        if (options.containsKey(AudioChannelOptions.ROLE)) {
            channel.setRole((String) options.get(AudioChannelOptions.ROLE));
        }

        if (options.containsKey(AudioChannelOptions.CAN_HEAR_ECHO)) {
            channel.setCanHearEcho((boolean) options.get(AudioChannelOptions.CAN_HEAR_ECHO));
        }

//        if (options.containsKey(AudioChannelOptions.CAN_RECORD_WHEN_SPEAKING)) {
//            channel.setCanRecordWhenSpeaking((boolean) options.get(AudioChannelOptions.CAN_RECORD_WHEN_SPEAKING));
//        }

        if (options.containsKey(AudioChannelOptions.SPEAKER_TYPE)) {
            channel.setSpeakerType((String) options.get(AudioChannelOptions.SPEAKER_TYPE));
            channel.setSpeaker((String) options.get(AudioChannelOptions.SPEAKER_TYPE));
        }
        if (options.containsKey(AudioChannelOptions.RECORDER)) {
            channel.setRecorder((String) options.get(AudioChannelOptions.RECORDER));

            RecordManager.shareInstance().addChannel2(channel.getRecorder(), channel.getKey()); // 通过 options 方式添加
        }

        if (options.containsKey(AudioChannelOptions.IS_STREAMMT)) {
            channel.isStreamMt = ((boolean) options.get(AudioChannelOptions.IS_STREAMMT));
        }
        if (options.containsKey("streamMtSection")) {
            channel.streamMtSection = ((int) options.get("streamMtSection"));
        }

        if (options.containsKey(AudioChannelOptions.MIN_VAD_ENERGY)) {
            long minVadEnergy = ((Number) options.get(AudioChannelOptions.MIN_VAD_ENERGY)).longValue();
            channel.setMinVadEnergy(minVadEnergy);
            channel.writeAsrTextToFileHandle("\n==========================\n");
            channel.writeAsrTextToFileHandle("参数添加: 16ms幅值绝对值之和: " + minVadEnergy + "(" + AudioChannel.defalutMinVadEnergy * 1.0 / minVadEnergy  + ")\n");
            channel.writeAsrTextToFileHandle("==========================\n\n");
        }
        if (options.containsKey(AudioChannelOptions.VAD_BEGIN)) {
            channel.setVadBegin(((Number) options.get(AudioChannelOptions.VAD_BEGIN)).intValue());
        }
        if (options.containsKey(AudioChannelOptions.VAD_END)) {
            channel.setVadEnd(((Number) options.get(AudioChannelOptions.VAD_END)).intValue());
        }
        if (options.containsKey("blevolume") && options.containsKey("bleagc")) {
            int blevolume = ((Number)options.get("blevolume")).intValue();
            int bleagc = ((Number)options.get("bleagc")).intValue();
            channel.writeAsrTextToFileHandle("\n==========================\n");
            channel.writeAsrTextToFileHandle("参数添加: 音量 " + blevolume + ", 增益 " + bleagc + "\n");
            channel.writeAsrTextToFileHandle("==========================\n\n");
        }
        /////////////////////////////////////////////////////////////////////////////
        AiSpeechLogUtil.d(TAG, "更新channel: " + channel.getKey() + " " + options.toString());
    }

    /** 向通道写入声音数据, 进行识别任务处理, 事件响应通过 Listener 回调
     * @param chkey 通道名
     * @param data short 数组
     */
    public void writeAudioToChannel(String chkey, short[] data) {
        this.writeAudioToChannelNoCopy(chkey, Arrays.copyOf(data, data.length));
    }

    /** 向通道写入声音数据, 进行识别任务处理, 事件响应通过 Listener 回调
     * @param chkey 通道名
     * @param data byte 数组
     */
    public void writeAudioToChannel(String chkey, byte[] data) {
        this.writeAudioToChannelNoCopy(chkey, BytesTrans.getInstance().Bytes2Shorts(data));
    }

    /** 向通道写入声音数据, 并指定该组声音数据是否是"声音帧", 内部不在进行 vad 检测, 进行识别任务处理, 事件响应通过 Listener 回调
     * @param chkey 通道名
     * @param data short 数组
     * @param isVoice 是否是"声音帧"
     */
    public void writeAudioToChannel(String chkey, short[] data, boolean isVoice) {
        this.writeAudioToChannelNoCopy(chkey, Arrays.copyOf(data, data.length), isVoice);
    }

    /** 向通道写入声音数据, 并指定该组声音数据是否是"声音帧", 进行识别任务处理, 事件响应通过 Listener 回调
     * @param chkey 通道名
     * @param data byte 数组
     * @param isVoice 是否是"声音帧"
     */
    public void writeAudioToChannel(String chkey, byte[] data, boolean isVoice) {
        this.writeAudioToChannelNoCopy(chkey, BytesTrans.getInstance().Bytes2Shorts(data), isVoice);
    }

    public void writeAudioToChannelNoCopy(String chkey, short[] data) {
        assert data.length == 256 : "写入数据为每包 256/512 大小的 short/byte";
        if (channelMap.containsKey(chkey)) {
            AudioChannel channel = channelMap.get(chkey);
            assert channel != null;
            channel.write(data);
        }
    }

    public void writeAudioToChannelNoCopy(String chkey, short[] data, boolean isVoice) {
        assert data.length == 256 : "写入数据为每包 256/512 大小的 short/byte";
        if (channelMap.containsKey(chkey)) {
            AudioChannel channel = channelMap.get(chkey);
            assert channel != null;
            channel.write(data, isVoice);
        }
    }
//    public void enableChannelVad(String chkey, float threshold) {
//        if (channelMap.containsKey(chkey)) {
//            channelMap.get(chkey).openVad(threshold, 0);
//        }
//    }
//
//    public void disableChannelVad(String chkey) {
//        if (channelMap.containsKey(chkey)) {
//            channelMap.get(chkey).closeVad();
//        }
//    }


    /**
     * 最小能量, 能量越低越容易 "唤醒", 默认值 AudioChannel.defalutMinVadEnergy
     * @param minVadEnergy 数值
     */
    public void setMinVadEnergy(Integer minVadEnergy) {
        for (AudioChannel channel : channelMap.values()) {
            channel.setMinVadEnergy(minVadEnergy);
        }
    }

    /**
     * 设置前端点检测时长, 前端点声音检测时间, 即用户说话多长时间, 认为是唤醒
     * @param duration 单位:ms;
     */
    public void setVadBegin(Integer duration) {
        for (AudioChannel channel : channelMap.values()) {
            channel.setVadBegin(duration / 16); // 每包数据 16ms
        }
    }

    /**
     * 设置后端点检测时长, 后端点静音检测时间, 即用户停止说话多长时间
     * @param duration 单位:ms;
     */
    public void setVadEnd(Integer duration) {
        for (AudioChannel channel : channelMap.values()) {
            channel.setVadEnd(duration / 16); // 每包数据 16ms
        }
    }


    public AudioChannel getAudioChannel(String chkey) {
        return chkey == null ? null : channelMap.get(chkey);
    }

    public List<AudioChannel> getAudioChannels(String role) {
        List<AudioChannel> roles = new ArrayList<>();
        for (AudioChannel channel : channelMap.values()) {
            if (Objects.equals(channel.role, role)) {
                roles.add(channel);
            }
        }
        return roles;
    }

    public String[] getAudioChannelNames() {
        return channelMap.keySet().toArray(new String[0]);
    }

    public AudioChannel[] getAudioChannels() {
        return channelMap.values().toArray(new AudioChannel[0]);
    }

//    @Deprecated
//    public void pauseAudioChannel(String chkey) {
//        if (channelMap.containsKey(chkey)) {
//            channelMap.get(chkey).pause();
//        }
//    }
//
//    @Deprecated
//    public void resumeAudioChannel(String chkey) {
//        if (channelMap.containsKey(chkey)) {
//            channelMap.get(chkey).resume();
//        }
//    }
//
//    @Deprecated
//    public void pauseAllAudioChannel() {
//        for (String chkey : channelMap.keySet()) {
//            this.pauseAudioChannel(chkey);
//        }
//    }
//
//    @Deprecated
//    public void resumeAllAudioChannel() {
//        for (String chkey : channelMap.keySet()) {
//            this.resumeAudioChannel(chkey);
//        }
//    }

    /** 启用名为 chkey 的通道; 注意: 单实例(singleton=true)模式下, 会禁用其他全部通道, 再开启当前通道
     * @param chkey 通道名
     */
    public void enableAudioChannel(String chkey) {
        AudioChannel p = channelMap.get(chkey);
        if (p == null) {
            AiSpeechLogUtil.e(TAG, "enableAudioChannel !!! 找不到 channel: [" + chkey + "]");
            return;
        }
        AiSpeechLogUtil.e(TAG, "enableAudioChannel: " + chkey);

        // FIXME: 4/12/21 后续需要直接做成不使用 vad 直接开始识别的功能
        if (this.singleton) {
            disableAllAudioChannel();
            resetSingletonChannel();
        }

        if (channelMap.containsKey(chkey)) {
            channelMap.get(chkey).enable();
//            channelMap.get(chkey).setUnTriggered(); // 此处不宜调用, 以免正在"唤醒"的通道, 被重置后不能触发 vadend
            channelMap.get(chkey).resume();
        } else {
            AiSpeechLogUtil.e(TAG, "enableAudioChannel 当前通道不存在: " + chkey);
        }
    }

    /** 禁用名为 chkey 的通道, 同时强制重置当前"唤醒"状态, 若当前通道处在"唤醒", 会结束"唤醒"停止识别获得识别结果
     * @param chkey 通道名
     */
    public void disableAudioChannel(String chkey) {
        AudioChannel channel = channelMap.get(chkey);
        if (channel == null) {
            AiSpeechLogUtil.e(TAG, "disableAudioChannel !!! 找不到 channel: [" + chkey + "]");
            return;
        }
        AiSpeechLogUtil.e(TAG, "disableAudioChannel: " + chkey);

        forceResetVadState(chkey); // FIXME: 4/2/21 重置不带有任务 id(timestamp), 会容易导致重置了其他任务, 存在隐患
        if (channelMap.containsKey(chkey)) {
            channelMap.get(chkey).disable();
        } else {
            AiSpeechLogUtil.e(TAG, "disableAudioChannel 当前通道不存在: " + chkey);
        }
    }

    /**
     * 启用所有通道
     */
    public void enableAllAudioChannel() {
        for (AudioChannel channel : channelMap.values()) {
            channel.enable();
        }
    }

    /**
     * 禁用所有通道; 常用在触控摸下, 一开始禁用全部通道, 后续选择开启需要的通道
     */
    public void disableAllAudioChannel() {
        for (AudioChannel channel : channelMap.values()) {
            channel.disable();
        }
    }

    /**
     * 通道(chkey)是否已开启
     * @param chkey 通道名
     * @return 是否已开启
     */
    public boolean isEnableAudioChannel(String chkey) {
        AudioChannel channel = channelMap.get(chkey);
        return channel != null && channel.isEnabled();
    }

    /** 停止通道(chkey)的任务(workerId)
     * @param workerId 任务 id
     * @param chkey 通道key
     */
    public void stopWorker(long workerId, String chkey) {
        // 停止当前进行中的任务
        AudioChannel channel = channelMap.get(chkey);
        if (channel != null) {
            // 设置当前上下文无效
            if (channel.curSpeechContext != null && channel.curSpeechContext.timestamp == workerId) {
                channel.curSpeechContext.invalid = true;
            }

            String speakerType = channel.getSpeakerType();
            SpeakManager.shareInstance().stopWorker(speakerType, workerId);
        }

        RecognizeManager.shareInstance().stopWorker(workerId);
        TranslateManager.shareInstance().stopWorker(workerId);
        SynthesizeManager.shareInstance().stopWorker(workerId);

        AiSpeechLogUtil.e(TAG, "stopWorker: " + chkey + workerId);
    }

    /**
     * 停止某个任务, 若只需停止播放任务, 可直接调用 {@link co.timekettle.speech.SpeakManager#stopWorker(String, long)}
     * @param workerId
     */
    // FIXME: 2023/6/10 应该停止某任务的上下文
    public void stopWorker(long workerId) {
        SpeakManager.shareInstance().stopWorker(workerId);
        RecognizeManager.shareInstance().stopWorker(workerId);
        TranslateManager.shareInstance().stopWorker(workerId);
        SynthesizeManager.shareInstance().stopWorker(workerId);

        AiSpeechLogUtil.d(TAG, "stopWorker: " + workerId);
    }

    /**
     * 设置代理/监听器handler
     */
    public void setDelegateHandler(final Handler handler) {
        runOnDelegateThread(() -> {
            if (handler != null) {
                mDelegateHandler = handler;
            }
        });
    }

    private void runOnDelegateThread(Runnable runnable) {
        Handler handler = mDelegateHandler;
        if (handler != null) {
            if (handler.getLooper() == Looper.myLooper()) {
                runnable.run();
            } else {
                handler.post(runnable);
            }
        } else {
            runnable.run();
        }
    }

    public void fetchHosts() {
        assert !TextUtils.isEmpty(HttpApiBaseUrl) : "HttpApiBaseUrl 不能为空";
        /* { "success": true, "code": 200, "desc": "Success.", "reasonCode": null, "reason": null,
             "data": {
                 "defaultRoute": [ ],
                 "routes": [
                   { "ip": "***********", "port": 5050, "type": "TCP", "code": "111_JP", "enabled": true, "country": "CN" }
                 ]
              },
              "total": null
           }
        */
        String params = ""; // "?param1=0";
        String BaseURL = HttpApiBaseUrl != null ? HttpApiBaseUrl : "https://internal-apis-and-pages2.timekettle.co:30013";
        String Api = "/terptrans/user/mediate/routes";
        String url = BaseURL + Api + params;
        this.fetchHostsWithCache(url, (hosts, error) -> {
            TmkSpeechClient.shareInstance().setSpecificHosts(hosts);
            return null;
        });
    }

    public void fetchHosts(String url) {
        this.fetchHosts(url, (hosts, error) -> {
            TmkSpeechClient.shareInstance().setSpecificHosts(hosts);
            return null;
        });
    }

    public void fetchHostsWithCache(String url, BiFunction<List<EngineHost>, SpeechError, Void> callback) {
        if (cacheHosts != null && cacheHosts.size() > 0) {
            if (callback != null) callback.apply(cacheHosts, null);
            return;
        }
        this.fetchHosts(url, callback);
    }

    public void fetchHosts(String url, BiFunction<List<EngineHost>, SpeechError, Void> callback) {
        new Thread(() -> {
            ArrayList<EngineHost> hosts = new ArrayList<>();
            SpeechError err = null;
            try {
                byte[] resBytes = HttpsConnection.get(url);
                if (resBytes != null && resBytes.length > 0) {
                    String resString = new String(resBytes);
                    AiSpeechLogUtil.d(TAG, url + " 响应结果: " + resString);
                    JSONObject result = new JSONObject(resString);
                    JSONObject data = result.getJSONObject("data");
                    JSONArray routes = data.getJSONArray("routes");
                    Gson gson = new Gson();
                    for (int i = 0; i < routes.length(); i++) {
                        JSONObject host = (JSONObject)routes.get(i);
                        EngineHost eh = gson.fromJson(host.toString(), EngineHost.class);
                        hosts.add(eh);
                    }
                } else {
                    AiSpeechLogUtil.e(TAG, url + " 请求/响应异常");
                    err = new SpeechError(-1, url + " 请求/响应异常");
                }
            } catch (JSONException e) {
                AiSpeechLogUtil.e(TAG, url + " 响应异常");
                err = new SpeechError(-1, e.getMessage());
                e.printStackTrace();
            } finally {
                this.cacheHosts = hosts;
                // 测试连接延时
                DNSUtil.testConnectCost(hosts);
                // 延时从小到大排序
                hosts.sort((f1, f2) -> (int) (f1.connect_cost - f2.connect_cost));
                if (callback != null) callback.apply(hosts, err);
            }
        }).start();
    }

    public void fetchConfig() {
        assert !TextUtils.isEmpty(HttpApiBaseUrl) : "HttpApiBaseUrl 不能为空";
        /* { "success": true, "code": 200, "desc": "Success.", "reasonCode": null, "reason": null,
            "data": { speechTranslation: true, log: true, opus: true }
        } */
        String params = "?deviceType=app"; // deviceType 参数值为 app(时空壶APP), t1, t1mini
//        String BaseURL = "http://*************:30013";
        String BaseURL = HttpApiBaseUrl != null ? HttpApiBaseUrl : "https://internal-apis-and-pages2.timekettle.co:30013";
        String Api = "/terptrans/user/mediate/switch";
        String url = BaseURL + Api + params;
        fetchConfig(url, ((config, speechError) -> {
            if (speechError == null) {
                AiSpeechManager.shareInstance().enableSpeechTranslation(config.enableSpeechTranslation);
                AiSpeechManager.shareInstance().enableOpusTransmission(config.enableOpusTransmission);
                AiSpeechManager.shareInstance().enablePunctuationSegmentation(config.enablePunctuationSegmentation);
                AiSpeechManager.shareInstance().setNoOpusTTSCodeArray(config.noOpusTTSCodeArray);
                TmkSpeechClient.setShouldUploadKafkaLog(config.enableKafkaLog);
            }
            return null;
        }));
    }

    public void fetchConfig(String url) {
        fetchConfig(url, ((config, speechError) -> {
            if (speechError == null) {
                AiSpeechManager.shareInstance().enableSpeechTranslation(config.enableSpeechTranslation);
                AiSpeechManager.shareInstance().enableOpusTransmission(config.enableOpusTransmission);
                AiSpeechManager.shareInstance().enablePunctuationSegmentation(config.enablePunctuationSegmentation);
                AiSpeechManager.shareInstance().setNoOpusTTSCodeArray(config.noOpusTTSCodeArray);
                TmkSpeechClient.setShouldUploadKafkaLog(config.enableKafkaLog);
            }
            return null;
        }));
    }

    public void fetchConfig(String url, BiFunction<Config, SpeechError, Void> callback) {
//        if (cacheConfig != null) {
//            if (callback != null) callback.apply(cacheConfig, null);
//            return;
//        }

        new Thread(() -> {
            Config config = null;
            SpeechError err = null;
            try {
                byte[] resBytes = HttpsConnection.get(url);
                if (resBytes != null && resBytes.length > 0) {
                    String resString = new String(resBytes);
                    AiSpeechLogUtil.d(TAG, url + " 响应结果: " + resString);
                    JSONObject result = new JSONObject(resString);
                    boolean success = result.getBoolean("success");
                    if (!success) err = new SpeechError(result.getInt("reasonCode"), result.getString("reason"));
                    else {
                        JSONObject data = result.getJSONObject("data");
                        config = new Config();
                        if (data.has("speechTranslation")) {
                            config.enableSpeechTranslation = data.getBoolean("speechTranslation");
                        }
                        if (data.has("onePackageOpus")) {
                            config.enableOpusTransmission = data.getBoolean("onePackageOpus");
                        }
                        if (data.has("log")) {
                            config.enableKafkaLog = data.getBoolean("log");
                        }
                        if (data.has("streamingTts")) {
                            config.enablePunctuationSegmentation = data.getBoolean("streamingTts");
                        }
                        if (data.has("noOpusTTSCode")) {
                            //noOpusTTSCode == null ，表示所有的TTS都不使用opus
                            //noOpusTTSCode = [] ，标识所有TTS都使用opus
                            //noOpusTTSCode = ["1","2"] ，标识1和2的TTS不使用opus
                            config.noOpusTTSCodeArray = data.getJSONArray("noOpusTTSCode");
                        }
                        cacheConfig = config;
                    }
                } else {
                    AiSpeechLogUtil.e(TAG, url + " 请求/响应异常");
                    err = new SpeechError(-1, url + " 请求/响应异常");
                }
            } catch (JSONException e) {
                AiSpeechLogUtil.e(TAG, url + " 响应异常");
                err = new SpeechError(-1, e.getMessage());
                e.printStackTrace();
            } finally {
                if (callback != null) callback.apply(config, err);
            }
        }).start();
    }

    public interface Listener {
        void onVadBegin(@NonNull AudioChannel channel, long session);

        void onVadEnd(@NonNull AudioChannel channel, long session);

        /**
         * 语音唤醒数据回调
         * @param channel 唤醒通道
         * @param session 唤醒任务 id
         * @param volume 当前时刻音量
         */
        void onActivity(@NonNull AudioChannel channel, long session, float volume);
        default void onActivityData(@NonNull AudioChannel channel, long session, short[] bytes, float volume){};

        void onRecognizeResult(String chkey, long session, String srcCode, String dstCode, boolean isLast, String text, String engine, TmkCustomTranslationJni.TmkCustomTranslationResult ct);

        void onTranslateResult(String chkey, long session, boolean isLast, String text, String engine, TmkCustomTranslationJni.TmkCustomTranslationResult ct);

        void onSynthesizeCompleted(String chkey, long session, byte[] data, String engine);

        void onError(String chkey, long session, int code, String message);  // FIXME: 2022/5/31 添加引擎返回

        void onSpeakStart(final String chkey, long session, String text, String speakerType, Object extData);

        void onSpeakEnd(final String chkey, long session,  String text, String speakerType, Object extData);

        /**
         * 阶段任务完成的回调(目前支持asr和mt阶段的完成回调)
         * @param chkey 通道
         * @param session 任务 id
         * @param type 任务类型, 值为 TaskType 的 value, 可能是多个任务集合统一 {@link co.timekettle.speech.SpeechTask.TaskType }
         *             如 asr 或 mt 阶段同时完成, 则 type = TaskType.ASR.getValue() | TaskType.MT.getValue(),
         *             可通过 type & TaskType.ASR.getValue() 判断是否是 asr 的完成回调
         * @param engine 引擎名称
         */
        void onFinished(String chkey, long session, int type, String engine);

        void onSpeechTranslationResult(AudioChannel channel, SpeechSessionContext context, SpeechResponse.ResponseMessage msg);

        // 后续修改目标
//        void onRecognizeResult(@NonNull AudioChannel channel, SpeechTask task);
//        void onTranslateResult(@NonNull AudioChannel channel, SpeechTask task);
//        void onSynthesizeCompleted(@NonNull AudioChannel channel, SpeechTask task);
//        void onPlayStart(@NonNull AudioChannel channel, SpeechTask task);
//        void onPlayEnd(@NonNull AudioChannel channel, SpeechTask task);
    }

    public static void main(String[] args) {

    }
}
