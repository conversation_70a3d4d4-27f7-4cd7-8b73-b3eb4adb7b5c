package co.timekettle.speech;

import co.timekettle.speech.speech_translation.ISpeechTranslation;
import co.timekettle.speech.speech_translation.ISpeechTranslationLid;
import co.timekettle.speech.speech_translation.ISpeechTranslationTwoStep;
import co.timekettle.speech.speech_translation.TmkWorkerBase;
import co.timekettle.speech.translator.TranslatorBase;
import co.timekettle.speech.utils.AiSpeechLogUtil;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.Iterator;
import java.util.Vector;
import java.util.function.Function;

public class SpeechTranslationManager {
    private static final String TAG = "SpeechTranslationManager";

    private static SpeechTranslationManager instance = null;

    private final ArrayList<RecognizerClass> mClassList = new ArrayList<>();
    private final HashMap<Integer, SpeechTranslationTask<?, ?>> contexts = new HashMap<>();
    private final HashMap<Integer, Function<?, ?>> callbacks = new HashMap<>();
    private final Vector<TmkWorkerBase> workerList = new Vector<>();

    private TmkWorkerBase.Listener workerBaseListener = new TmkWorkerBase.Listener() {
        @Override
        public void onTranslateResult(TmkWorkerBase worker, boolean isLast, String srcCode, String rtext, String ttext, String engine) {
            long session = worker.getWorkerId();
            String key = worker.getUserData();
            String name = worker.getName();
            AiSpeechLogUtil.d(TAG, "onTranslateResult: " + name + "[" + engine + "] " + key + " " + session + " " + isLast + " " + rtext + " | " + ttext);

            int tag = worker.hashCode();
            SpeechTranslationTask task = contexts.get(tag);
            if (task == null) AiSpeechLogUtil.e(TAG, key + "通道任务: [" + session + "]" + name + " 任务为空(onRecognizeResult)");
            if (task == null) return; // 防止超时和 error 的两次回调
            task.response.data = rtext;
            task.response.tdata = ttext;
            task.response.engine = engine;
            task.response.isLast = isLast;
            task.response.codeOfData = srcCode;
            if (doCallback(tag, task)) return;
        }

        @Override
        public void onSynthesizeResult(TmkWorkerBase worker, byte[] data, String engine) {

        }

        @Override
        public void onFinished(TmkWorkerBase worker, String engine, SpeechError error) {
            long session = worker.getWorkerId();
            String key = worker.getUserData();
            String name = worker.getName();
//            AiSpeechLogUtil.d(TAG, "onRecognizeResult: " + name + "[" + engine + "] " + key + " " + session + " " + isLast + " " + text + (error != null ?  " error: " + error.desc : ""));

            int tag = worker.hashCode();
            SpeechTranslationTask<?, ?> task = contexts.get(tag);
            if (task == null) AiSpeechLogUtil.e(TAG, key + "通道任务: [" + session + "]" + name + " 任务为空(onRecognizeResult)");
            if (task == null) return; // 防止超时和 error 的两次回调
            if (error == null && task.response.data == null) error = new SpeechError(-1, "识别为空");
            task.response.error = error;
            task.response.engine = name;
            task.response.isFinished = true;
            if (doCallback(tag, task)) return;
        }
    };

    private SpeechTranslationManager() {
        addRecognizer("ispeech_translation_twostep", ISpeechTranslationTwoStep.class);
        addRecognizer("ispeech_translation", ISpeechTranslation.class);
        addRecognizer("ispeech_translation_lid", ISpeechTranslationLid.class);
    }

    public static SpeechTranslationManager shareInstance() {
        if (instance == null) {
            instance = new SpeechTranslationManager();
        }
        return instance;
    }

    public void destroy() {
        synchronized(workerList) {
            for (TmkWorkerBase worker : workerList) {
                worker.stop();
                worker.getName();
            }

            workerList.clear();
        }
    }

    public void addRecognizer(String name, Class<?> objectClass) {
        removeRecognizer(name); // 先尝试移除, 后添加
        mClassList.add(new RecognizerClass(name, objectClass));
    }

    public void addRecognizer(int index, String name, Class<?> objectClass)
    {
        removeRecognizer(name);
        mClassList.add(index, new RecognizerClass(name, objectClass));
    }

    public void removeRecognizer(String name) {
        Iterator<RecognizerClass> iterator = mClassList.iterator();
        while (iterator.hasNext()) {
            RecognizerClass s = iterator.next();
            if (s.name.equals(name)) {
                iterator.remove();
            }
        }
    }

    public long start(SpeechTranslationTask<Object, String> task, Function<SpeechTranslationTask<Object, String>, Object> callback) {

        final long session = task.workId;
        final String chkey = task.userData;
        final String srcCode = task.request.code;
        final String dstCode = task.request.dstCode;
//        final String text = task.request.data;
        final String module = task.request.module;

        TmkWorkerBase recognizer = findRecognizer(module, chkey, srcCode, dstCode);
        if (recognizer == null) {
            AiSpeechLogUtil.e(TAG, chkey + " 错误未找到识别器 " + srcCode);

            task.response.error = new SpeechError(-1, "未找到识别器");
            task.response.isLast = true;

            if (callback != null) callback.apply(task);
            return 0;
        }
        recognizer.setWorkerId(session);
        AiSpeechLogUtil.d(TAG, recognizer.getName() + " " + chkey + " 开始识别 " + srcCode + " " + session);

        contexts.put(recognizer.hashCode(), task);
        callbacks.put(recognizer.hashCode(), callback);

        recognizer.start(srcCode, dstCode);

        synchronized(workerList) {
            workerList.add(recognizer);
        }
        AiSpeechLogUtil.d(TAG, chkey + "通道任务: [" + session + "]" + recognizer.getName() + " 识别: [" + srcCode + "]");
        return recognizer.getWorkerId();
    }

    public void stop(String key) {
        synchronized(workerList) {
            TmkWorkerBase w = null;
            for (TmkWorkerBase worker : workerList) {
                if (worker.getUserData().equals(key)) {
                    if (!worker.isStopped()) {
                        AiSpeechLogUtil.e(TAG, "SpeechTranslationManager 停止识别 " + key + " " + w.getWorkerId());
                        w.setStopped(true);
                        w.stop();
                    }
                    w = worker;
                }
            }

            if (w == null) {
                AiSpeechLogUtil.e(TAG, "SpeechTranslationManager 停止识别 " + key + ", 但未找到 worker");
            }
        }
    }

    public void stop(String key, long taskId) {
        /* FIXME: 基于一个通道同时只存在一个工作中(向服务器写数据)的识别器, 则是停止某一通道(key)识别工作;  */
        synchronized(workerList) {
            TmkWorkerBase w = null;
            for (TmkWorkerBase worker : workerList) {
                if (worker.getWorkerId() == taskId) {
                    w = worker;
                    break;
                }
            }

            if (w != null) {
                if (!w.isStopped()) {
                    AiSpeechLogUtil.d(TAG, "SpeechTranslationManager 停止识别 " + w.getUserData() + " " + w.getWorkerId());
                    w.setStopped(true);
                    w.stop();
//                workerList.remove(w);
                } else {
                    AiSpeechLogUtil.d(TAG, "SpeechTranslationManager 早已停止识别 " + w.getUserData() + " " + w.getWorkerId());
                }
            } else {
                AiSpeechLogUtil.d(TAG, "SpeechTranslationManager 停止识别 " + key + ", 但未找到 worker");
            }
        }
    }

    public void stopAllWorker() {
        synchronized(workerList) {
            AiSpeechLogUtil.e(TAG, "stopAllWorker: 正在识别的worker: " + workerList.size());
            for (TmkWorkerBase worker : workerList) {
                worker.setStopped(true);
                worker.stop();
            }

            workerList.clear();
        }
    }

    public void clearAllWorker() {
        synchronized(workerList) {
            AiSpeechLogUtil.e(TAG, "stopAllWorker: 正在识别的worker: " + workerList.size());
            for (TmkWorkerBase worker : workerList) {
                worker.setStopped(true);
                worker.stop();
            }

            workerList.clear();
        }
    }

    public void stopWorker(long workerId) {
        synchronized(workerList) {
            TmkWorkerBase w = null;
            for (TmkWorkerBase worker : workerList) {
                if (worker.getWorkerId() == workerId) {
                    w = worker;
                    w.setStopped(true);
                    w.stop();
                    AiSpeechLogUtil.e(TAG, "SpeechTranslationManager 停止任务 " + w.getUserData() + " " + w.getWorkerId());
                }
            }

            if (w != null) {
                workerList.remove(w);
            }
        }
    }

    public void writeAudio(long workId, byte[] data) {
        synchronized(workerList) {
            for (TmkWorkerBase w : workerList) {
                if (w.getWorkerId() == workId) {
                    if (!w.isStopped()) w.writeAudio(data);
                    break;
                }
            }
        }
    }

    private boolean isCanRecognize(String name) {
        synchronized(workerList) {
            for (TmkWorkerBase worker : workerList) {
                if (worker.getName().equals(name) && worker.isSingleton()) { // 若识别器仅支持单实例, 则识别器在数组中只能存在一个, 此时此类工作器不能用作识别
                    if (!worker.isStopped() && !worker.isOfflineModule()) { // 未停止的在线工作器(如讯飞在线), 讯飞离线识别允许队列处理
                        AiSpeechLogUtil.e(TAG, worker.getName() +" 单例识别器已存在, 不能进行新的识别任务 " + worker.getWorkerId());
                        return false;
                    } else {
                        AiSpeechLogUtil.e(TAG, worker.getName() +" 单例识别器[" + worker.getWorkerId() +"]已存在[但已停止工作], 能进行新的识别任务");
                    }
                }
            }
        }

        return true;
    }

    public TmkWorkerBase findRecognizer(String preferredWorker, String key, String srcCode, String dstCode) {
        AiSpeechLogUtil.d(TAG, "findWorker: [key=" + key + "][srcCode=" + srcCode + "][dstCode=" + dstCode + "]" + " onlyUseOffline:" + onlyUseOffline);
        if (preferredWorker != null) {
            try {
                for (RecognizerClass s : mClassList) {
                    Class<?> obj = s.className;
                    TmkWorkerBase base = (TmkWorkerBase) obj.newInstance();
                    if (!s.name.equals(preferredWorker)) continue;

                    /* FIXME: 当开启离线时, 在此处判断离线语言对 */
                    if (onlyUseOffline) {
                        if (!base.isOfflineModule()) {
                            AiSpeechLogUtil.e(TAG, base.getName() + " 是在线模块, 网络未开启, 当前只能用离线模块");
                            continue;
                        }
                    }

                    boolean isSupport = base.isSupport(srcCode, dstCode);
                    boolean isCanRecognize = isCanRecognize(s.name);
                    boolean isEnalbe = !base.isOfflineModule() || OfflineManager.getInstance().isEnable(srcCode, dstCode); // 非离线是一定启用的
                    AiSpeechLogUtil.d(TAG, "findRecognizer: 找到指定识别器[" + base.getName() + "] 条件 [isSupport=" + isSupport + "][isCanRecognize=" + isCanRecognize + "]" + "][isEnalbe=" + isEnalbe + "]");
                    if (isSupport && isCanRecognize && isEnalbe) {
//                    base.setContext(context);
                        base.setUserData(key);
                        base.setListener(workerBaseListener);
                        return base;
                    }
                }
            } catch (IllegalAccessException e) {
                e.printStackTrace();
            } catch (InstantiationException e) {
                e.printStackTrace();
            }
        }

        try {
            for (RecognizerClass s : mClassList) {
                Class<?> obj = s.className;
                TmkWorkerBase base = (TmkWorkerBase) obj.newInstance();

                /* FIXME: 当开启离线时, 在此处判断离线语言对 */
                if (onlyUseOffline) {
                    if (!base.isOfflineModule()) {
                        AiSpeechLogUtil.e(TAG, base.getName() + " 是在线模块, 网络未开启, 当前只能用离线模块");
                        continue;
                    }
                }

                boolean isSupport = base.isSupport(srcCode, dstCode);
                boolean isCanRecognize = isCanRecognize(s.name);
                boolean isEnalbe = !base.isOfflineModule() || OfflineManager.getInstance().isEnable(srcCode, dstCode); // 非离线是一定启用的
                AiSpeechLogUtil.d(TAG, "findRecognizer: 识别器[" + base.getName() + "] 条件 [isSupport=" + isSupport + "][isCanRecognize=" + isCanRecognize + "]" + "][isEnalbe=" + isEnalbe + "]");
                if (isSupport && isCanRecognize && isEnalbe) {
//                    base.setContext(context);
                    base.setUserData(key);
                    base.setListener(workerBaseListener);
                    return base;
                }
            }
        } catch (IllegalAccessException e) {
            e.printStackTrace();
        } catch (InstantiationException e) {
            e.printStackTrace();
        }
        return null;
    }

    private TmkWorkerBase findRecognizer(String key, String srcCode, String dstCode) {
        AiSpeechLogUtil.d(TAG, "findWorker: [key=" + key + "][srcCode=" + srcCode + "][dstCode=" + dstCode + "]" + " onlyUseOffline:" + onlyUseOffline);
        try {
            for (RecognizerClass s : mClassList) {
                Class<?> obj = s.className;
                TmkWorkerBase base = (TmkWorkerBase) obj.newInstance();

                /* FIXME: 当开启离线时, 在此处判断离线语言对 */
                if (onlyUseOffline) {
                    if (!base.isOfflineModule()) {
                        AiSpeechLogUtil.e(TAG, base.getName() + " 是在线模块, 网络未开启, 当前只能用离线模块");
                        continue;
                    }
                }
                
                boolean isSupport = base.isSupport(srcCode, dstCode);
                boolean isCanRecognize = isCanRecognize(s.name);
                boolean isEnalbe = !base.isOfflineModule() || OfflineManager.getInstance().isEnable(srcCode, dstCode); // 非离线是一定启用的
                AiSpeechLogUtil.d(TAG, "findRecognizer: 识别器[" + base.getName() + "] 条件 [isSupport=" + isSupport + "][isCanRecognize=" + isCanRecognize + "]" + "][isEnalbe=" + isEnalbe + "]");
                if (isSupport && isCanRecognize && isEnalbe) {
//                    base.setContext(context);
                    base.setUserData(key);
                    base.setListener(workerBaseListener);
                    return base;
                }
            }
        } catch (IllegalAccessException e) {
            e.printStackTrace();
        } catch (InstantiationException e) {
            e.printStackTrace();
        }
        return null;
    }

    public void removeWorker(long workId) {
        synchronized(workerList) {
            for (TmkWorkerBase worker : workerList) {
                if (worker.getWorkerId() == workId) {
                    workerList.remove(worker);
                    break;
                }
            }
        }
    }

    public boolean findWorker(long workId) {
        synchronized(workerList) {
            for (TmkWorkerBase worker : workerList) {
                if (worker.getWorkerId() == workId) {
                    return true;
                }
            }
        }
        return false;
    }

    private boolean doCallback(int tag, SpeechTranslationTask<?, ?> task) {
        Function callback = callbacks.get(tag);
        if (callback != null) {
            if (task.response.isFinished) {
                callback.apply(task);
                // 最后若有 error, 则回调一次 error
//                if (task.response.error != null) callback.apply(task);
                callbacks.remove(tag);
                contexts.remove(tag);

                removeWorker(task.workId);
            } else {
                callback.apply(task);
            }
            return true;
        }
        return false;
    }

    private boolean onlyUseOffline = false;
    public void setOnlyOffline(boolean offline) {
        onlyUseOffline = offline;
    }

    static class RecognizerClass {
        String name;
        Class<?> className;
        public RecognizerClass(String name, Class<?> className) {
            this.name = name;
            this.className = className;
        }
    }
}
