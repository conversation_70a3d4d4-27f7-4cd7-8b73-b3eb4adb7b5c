package co.timekettle.speech;

import android.content.Context;

import co.timekettle.speech.translator.CustomTranslator;
import co.timekettle.speech.translator.ISpeechTranslator;
import co.timekettle.speech.translator.IflytekOfflineTranslator;
import co.timekettle.speech.translator.NiuOfflineTranslator;
import co.timekettle.speech.translator.TranslatorBase;
import co.timekettle.speech.utils.Language;
import co.timekettle.speech.utils.AiSpeechLogUtil;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.Iterator;
import java.util.Map;
import java.util.function.Function;

public class TranslateManager {

    class TranslatorClass
    {
        public String name;
        public Class<?> className;

        public TranslatorClass(String name, Class<?> className)
        {
            this.name = name;
            this.className = className;
        }
    }

    private static final String TAG = "TranslateManager";

    private static TranslateManager instance = null;
    private Context context = null;
    private Listener listener;

    private HashMap<Integer, SpeechTask> contexts = new HashMap();
    private HashMap<Integer, Function> callbacks = new HashMap();

    private ArrayList<TranslatorClass> mClassList = new ArrayList<>();
    private ArrayList<TranslatorBase> workers = new ArrayList();

    private HashMap<String, Boolean> customTranslatChkeys = new HashMap<>();

    private TranslatorBase.Listener translatorBaseListener = new TranslatorBase.Listener() {
        @Override
        public void onTranslateResult(TranslatorBase translator, String text, String engine, SpeechError error) {
            long session = translator.getSession();
            String key = translator.getKey();
            String name = translator.getName();
            int tag = translator.hashCode();
//            removeWorker(session);
            removeWorker(translator);

            SpeechTask task = contexts.get(tag);
            if (task == null) AiSpeechLogUtil.e(TAG, key + "通道任务: [" + session + "]" + name + " 任务为空(onError)");
            if (task == null) return; // 防止超时和 error 的两次回调
            if (error != null) {
                task.response.error = error;
            } else {
                task.response.data = text;
            }
            task.response.engine = engine;
            task.response.isFinished = true;
            if (doCallback(tag, task)) return;

            if (listener != null) {
                listener.onTranslateResult(task, error);
            }
        }
    };

    public static TranslateManager shareInstance() {
        if (instance == null) {
            instance = new TranslateManager();
        }
        return instance;
    }

    private TranslateManager() {
//        addTranslator(ISpeechConstant.TRANSLATOR.CUSTOM.getName(), CustomTranslator.class);
        addTranslator(ISpeechConstant.TRANSLATOR.NIU_OFFLINE_MT.getName(), NiuOfflineTranslator.class);
        addTranslator(ISpeechConstant.TRANSLATOR.IFLY_OFFLINE_MT.getName(), IflytekOfflineTranslator.class);
        addTranslator(ISpeechConstant.TRANSLATOR.ISPEECH.getName(), ISpeechTranslator.class);
    }

    public void addTranslator(String name, Class<?> objectClass) {
        removeTranslator(name);
        mClassList.add(new TranslatorClass(name, objectClass));
    }

    public void addTranslator(int index, String name, Class<?> objectClass) {
        removeTranslator(name);
        mClassList.add(index, new TranslatorClass(name, objectClass));
    }

    public void removeTranslator(String name)
    {
        Iterator<TranslatorClass> iterator = mClassList.iterator();
        while (iterator.hasNext()) {
            TranslatorClass s = iterator.next();
            if (s.name.equals(name)) {
                iterator.remove();
            }
        }
    }

    public void setContext(Context context) {
        this.context = context;
    }

    public void setListener(Listener listener) {
        this.listener = listener;
    }

    public long start(SpeechTask<String, String> task, Function<SpeechTask<String, String>, Object> callback) {
        final String chkey = task.chkey;
        final long session = task.session;
        final String srcCode = task.request.code;
        final String dstCode = task.request.dstCode;
        final String text = task.request.data;
        final String module = task.request.module;
        final Map<String, Object> opts = task.request.opts;

//        TranslatorBase translator = findTranslator(chkey, srcCode, dstCode);
        TranslatorBase translator = findTranslator(module, chkey, srcCode, dstCode);

        if (translator == null) {
            return 0;
        }
        synchronized (this) {
            workers.add(translator);
        }
        translator.setSession(session);
        contexts.put(translator.hashCode(), task);
        callbacks.put(translator.hashCode(), callback);

        AiSpeechLogUtil.d(TAG, chkey + "通道任务: [" + session + "]" + translator.getName() + " 将翻译[" + srcCode + "<->" + dstCode + "]: " + text);

        Language srcLanguage = new Language(srcCode, srcCode);
        Language dstLanguage = new Language(dstCode, dstCode);
        translator.start(srcLanguage, dstLanguage, text, opts);
        return session;
    }

    private boolean isCanTranslate(String name) {
        synchronized (this) {
            for (TranslatorBase translator : workers) {
                if (translator.getName().equals(name) && translator.isSingleton()) { // 当前还有任务, 由于是单例所以不允许处理
                    AiSpeechLogUtil.e(TAG, name + " isCanTranslate: false");
                    return false;
                }
            }
        }
        return true;
    }

    public void enableCustomTranslate(String chkey) {
        if (customTranslatChkeys == null) {
            customTranslatChkeys = new HashMap<>();
        }
        customTranslatChkeys.put(chkey, true);
    }

    // chkey为空, 则清空
    public void disableCustomTranslate(String chkey) {
        if (chkey != null) {
            if (customTranslatChkeys != null) customTranslatChkeys.put(chkey, false);
        } else {
            customTranslatChkeys = null;
        }
    }

    private void removeWorker(long session) {
        synchronized (this) {
            for (TranslatorBase translator : workers) {
                if (translator.getSession() == session) { // 当前还有任务, 由于是单例所以不允许处理
                    workers.remove(translator);
                    break;
                }
            }
        }
    }

    private void removeWorker(TranslatorBase translator) {
        synchronized (this) {
            workers.remove(translator);
        }
    }

    public void stopAllWorker() {
        synchronized(this) {
            for (TranslatorBase translator : workers) {
                translator.stop();
            }
            workers.clear();
        }
    }

    public void stopWorker(long workerId) {
        synchronized(this) {
            TranslatorBase w = null;
            for (TranslatorBase translator : workers) {
                if (translator.getSession() == workerId) {
                    w = translator;
                    w.stop();
                    AiSpeechLogUtil.e(TAG, "TranslateManager 停止任务 " + w.getKey() + " " + w.getSession());
                }
            }

            if (w != null) {
                workers.remove(w);
            }
        }
    }

    public TranslatorBase findTranslator(String preferredWorker, String key, String srcCode, String dstCode) {
        AiSpeechLogUtil.d(TAG, "findWorker: [key=" + key + "][srcCode=" + srcCode + "][dstCode=" + dstCode + "]" + " onlyUseOffline:" + onlyUseOffline);
        try {
            AiSpeechLogUtil.d(TAG, "findTranslator: 是否优先查找模块: " + preferredWorker);
            if (preferredWorker != null) {
                TranslatorClass prefClass = null; // 第一选择
                TranslatorClass extraClass = null; // 第二选择

                for (TranslatorClass s : mClassList) {
                    if (s.name.equals(preferredWorker)) {
                        prefClass = s;
                        AiSpeechLogUtil.d(TAG, "findTranslator: 找到偏好的模块: " + s.name);
                        break;
                    }
                    if (extraClass == null) extraClass = s;
                }

                TranslatorClass dstClass = prefClass != null ? prefClass : extraClass;
                if (dstClass == null) return null;

                TranslatorBase base = (TranslatorBase) dstClass.className.newInstance();
                if (onlyUseOffline && !base.isOfflineModule) {
                    AiSpeechLogUtil.e(TAG, base.getName() + " 是在线模块, 网络未开启, 当前只能用离线模块");
                    return null;
                }

                boolean isSupport = base.isSupport(srcCode, dstCode);
                boolean isCanTranslate = isCanTranslate(dstClass.name);
                // 非离线模块是一定启用的, 离线模块则根据语言是否开启离线判断
                boolean isEnalbe = !base.isOfflineModule || OfflineManager.getInstance().isEnable(srcCode, dstCode);
                isEnalbe = true;
                AiSpeechLogUtil.d(TAG, "findTranslator: 翻译器[" + base.getName() + "] 条件 [isSupport=" + isSupport + "][isCanRecognize=" + isCanTranslate + "]" + "][isEnalbe=" + isEnalbe + "]");
                if (isSupport && isCanTranslate && isEnalbe) {
                    base.setContext(context);
                    base.setKey(key);
                    base.setListener(translatorBaseListener);
                    return base;
                }
                return null;

            }

            for (TranslatorClass s : mClassList) {
                Class<?> obj = s.className;
                TranslatorBase base = (TranslatorBase) obj.newInstance();

                if (onlyUseOffline) {
                    if (!base.isOfflineModule) {
                        AiSpeechLogUtil.e(TAG, base.getName() + " 是在线模块, 网络未开启, 当前只能用离线模块");
                        continue;
                    }
                }

                if (base.isCustomModule) { // 自定义模块(自定义翻译等)
                    continue;
                }

                boolean isSupport = base.isSupport(srcCode, dstCode);
                boolean isCanTranslate = isCanTranslate(s.name);
                // 非离线模块是一定启用的, 离线模块则根据语言是否开启离线判断
                boolean isEnalbe = !base.isOfflineModule || OfflineManager.getInstance().isEnable(srcCode, dstCode);
                AiSpeechLogUtil.d(TAG, "findTranslator: 翻译器[" + base.getName() + "] 条件 [isSupport=" + isSupport + "][isCanTranslate=" + isCanTranslate + "]" + "][isEnalbe=" + isEnalbe + "]");
                if (isSupport && isCanTranslate && isEnalbe) {
                    base.setContext(context);
                    base.setKey(key);
                    base.setListener(translatorBaseListener);
                    return base;
                }
            }
        } catch (IllegalAccessException e) {
            e.printStackTrace();
        } catch (InstantiationException e) {
            e.printStackTrace();
        }
        return null;
    }

    public TranslatorBase findTranslator(String key, String srcCode, String dstCode) {
        AiSpeechLogUtil.d(TAG, "findWorker: [key=" + key + "][srcCode=" + srcCode + "][dstCode=" + dstCode + "]" + " onlyUseOffline:" + onlyUseOffline);
        try {
            for (TranslatorClass s : mClassList) {
                Class<?> obj = s.className;
                TranslatorBase base = (TranslatorBase) obj.newInstance();

                if (onlyUseOffline) {
                    if (!base.isOfflineModule) {
                        AiSpeechLogUtil.e(TAG, base.getName() + " 是在线模块, 网络未开启, 当前只能用离线模块");
                        continue;
                    }
                }

                if (base.isCustomModule) { // 自定义模块(自定义翻译等)
                    if (customTranslatChkeys != null) {
                        Boolean isEnableCustomMt = customTranslatChkeys.get(key);
                        AiSpeechLogUtil.d(TAG, base.getName() + " 是否开启了自定义翻译: " + isEnableCustomMt);
                        if (!(isEnableCustomMt != null && isEnableCustomMt)) {
                            continue;
                        }
                    }
                }

                boolean isSupport = base.isSupport(srcCode, dstCode);
                boolean isCanTranslate = isCanTranslate(s.name);
                AiSpeechLogUtil.d(TAG, "findTranslator: 翻译器[" + base.getName() + "] 条件 [isSupport=" + isSupport + "][isCanRecognize=" + isCanTranslate + "]");
                if (isSupport && isCanTranslate) {
                    base.setContext(context);
                    base.setKey(key);
                    base.setListener(translatorBaseListener);
                    return base;
                }
            }
        } catch (IllegalAccessException e) {
            e.printStackTrace();
        } catch (InstantiationException e) {
            e.printStackTrace();
        }
        return null;
    }

    private boolean doCallback(int tag, SpeechTask task) {
        Function callback = callbacks.get(tag);
        if (callback != null) {
            callback.apply(task);
            callbacks.remove(tag);
            contexts.remove(tag);
            return true;
        }
        return false;
    }

    private boolean onlyUseOffline = false;
    public void setOnlyOffline(boolean offline) {
        onlyUseOffline = offline;
    }

    public interface Listener {
        void onTranslateResult(SpeechTask task, SpeechError error);
    }
}
