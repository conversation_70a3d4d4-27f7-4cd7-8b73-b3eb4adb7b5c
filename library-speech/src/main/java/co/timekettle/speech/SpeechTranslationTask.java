package co.timekettle.speech;

/*
 * 包括 asr 和 mt, 也可能会有 tts
 * */
public class SpeechTranslationTask<Q, R> {
    public long workId; // 任务 id
    public TaskType type; // 配置任务类型, 识别, 翻译或合成
    public boolean isLast; // 存在多个子任务时, 是否是最终的任务, 收到响应后进行下一个阶段, 如 mt -> tts

    public SpeechRequest<Q> request; // 原始数据
    public SpeechResponse<R> response; // 结果数据

    public String userData; // 如: 记录通道名称

    public enum TaskType
    {
        //添加枚举的指定常量
        PICK(1 << 1),
        ASR(1 << 2),
        MT(1 << 3),
        TTS(1 << 4),
        PLAY(1 << 5);

        // 必须增加一个构造函数,变量,得到该变量的值
        private int mState = 0;
        private TaskType(int value)
        {
            mState = value;
        }
        public int value() {
            return mState;
        }

        public static int getValue(TaskType type) {
            switch (type) {
                case PICK: return 1 << 1;
                case ASR: return 1 << 2;
                case MT: return 1 << 3;
                case TTS: return 1 << 4;
                case PLAY: return 1 << 5;
            }
            return 1 << 1;
        }
    }

    public SpeechTranslationTask() {
        assert true: "请使用 SpeechTranslationTask(TaskType type, long workId, String userData)";
    }

    public SpeechTranslationTask(TaskType type, long workId, String userData) {
        request = new SpeechRequest<Q>();
        response = new SpeechResponse<R>();

        this.type = type;
        this.userData = userData;
        this.workId = workId;
    }
}