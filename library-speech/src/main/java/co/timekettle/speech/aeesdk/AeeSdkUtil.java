package co.timekettle.speech.aeesdk;

import android.content.Context;

import androidx.annotation.NonNull;

import com.iflytek.aikit.core.AiHelper;
import com.iflytek.aikit.core.AiResponse;
import com.iflytek.aikit.core.AiResponseListener;
import com.iflytek.aikit.core.CoreListener;
import com.iflytek.aikit.core.ErrType;
import com.iflytek.aikit.core.JLibrary;
import com.iflytek.aikit.core.LogLvl;

import java.util.HashMap;
import java.util.List;
import java.util.Objects;
import java.util.concurrent.Semaphore;

import co.timekettle.speech.utils.AiSpeechLogUtil;


public class AeeSdkUtil {
    private static volatile AeeSdkUtil instance;
    private static final String TAG = "AeeSdkUtil";

    public static final String ASR_WORK_DIR = "";  // 这里统一解压后放到 /data/data/com.xxx.yyy/files 目录下，不可指定路径
    public static final String TTS_WORK_DIR = "";


    public static final String DIR_SUB_V2_XZY = "/ClientOffline2/";
    public static final String DIR_SUB_ASR = "/ClientOffline2/xzy/asr/";
    public static final String DIR_SUB_TTS = "/ClientOffline2/xzy/tts/";

    private static final String APP_ID = "34331f67";
    private static final String APP_KEY = "a63f6ab5d35b2354f85b10724517ff80";
    private static final String APP_SECRET = "ZTRhN2ZlY2IyNjFkMWU5ZWVkNTBiM2Fm";

    public static final String ASR_ABILITY = "e0e26945b";
    public static final String TTS_ABILITY = "e09712bcb";

    private HashMap<String, AiResponseListener> mapListeners = new HashMap<>(); // 保存外部add进来的监听器
    private CoreListener outCoreListener; // 给外部回调用的

    public boolean isSdkInit = false;
    private final Semaphore authSemaphore = new Semaphore(0);

    public static AeeSdkUtil getInstance() {
        if (instance == null) {
            synchronized (AeeSdkUtil.class) {
                if (instance == null) {
                    instance = new AeeSdkUtil();
                }
            }
        }
        return instance;
    }

    //授权结果回调
    private CoreListener coreListener = new CoreListener() {
        @NonNull
        @Override
        protected Object clone() throws CloneNotSupportedException {
            return super.clone();
        }

        @Override
        public void onAuthStateChange(final ErrType type, final int code) {
            switch (type) {
                case AUTH:
                    AiSpeechLogUtil.e(TAG, "SDK状态更新，授权结果码：" + code);
                    if (code == 0) isSdkInit = true;
                    authSemaphore.release();
                    break;
                case HTTP:
                    AiSpeechLogUtil.e(TAG, "SDK状态更新，HTTP码：" + code);
                    break;
                default:
                    authSemaphore.release();
                    AiSpeechLogUtil.e(TAG, "SDK状态更新：未知错误，code：" + code);
            }
            AiSpeechLogUtil.d(TAG, "设备指纹:" + JLibrary.getInst().getDeviceId());
            if (outCoreListener != null) outCoreListener.onAuthStateChange(type, code);
        }
    };

    // 语音识别、翻译、TTS结果回调，都是在onResult里面
    private final AiResponseListener aiResponseListener = new AiResponseListener() {

        @Override
        public void onResult(String ability, int handleID, List<AiResponse> list, Object o) {
//            AiSpeechLogUtil.i(TAG, "onResult:" + ability + ", handleID:" + handleID);
            AiResponseListener outListener = mapListeners.get(ability);
            if(outListener != null){
                outListener.onResult(ability, handleID, list, o);
            }
        }

        @Override
        public void onEvent(String ability, int handleID, int event, List<AiResponse> eventData, Object usrContext) {
            AiSpeechLogUtil.d(TAG, "onEvent:" + ability + ",event:" + event);
            AiResponseListener outListener = mapListeners.get(ability);
            if(outListener != null){
                outListener.onEvent(ability, handleID, event, eventData, usrContext);
            }
        }

        //错误通知，能力执行终止
        @Override
        public void onError(String ability, int handleID, int err, String msg,
                            Object usrContext) {
            AiSpeechLogUtil.e(TAG, "错误通知，能力执行终止,Ability " + ability + " ERROR::" + msg + ",err code:" + err);
            AiResponseListener outListener = mapListeners.get(ability);
            if(outListener != null){
                outListener.onError(ability, handleID, err, msg, usrContext);
            }
        }
    };


    /**
     * 初始化 SDK，此方法只能调用一次
     *
     * @param workDir 资源包的存放目录，现在暂时写死，设置了也不生效
     */
    public synchronized void initSdk(Context context, String workDir, CoreListener outCoreListener) {
        AiSpeechLogUtil.d(TAG, "初始化AEE SDK 开始，workDir:" + workDir);
        if (context == null) {
            AiSpeechLogUtil.e(TAG, "context 为空，无法初始化AEE SDK！！！");
            return;
        }
        if (isSdkInit) {
            AiSpeechLogUtil.d(TAG, "AEE SDK 已经初始化了，直接使用即可");
            return;
        }
        if (outCoreListener != null) this.outCoreListener = outCoreListener;
        if (workDir == null || workDir.isEmpty()) {  // 为空，给一个默认路径
            String ROOT_WORK_DIR = context.getFilesDir().getAbsolutePath();
            workDir = (ROOT_WORK_DIR + DIR_SUB_V2_XZY);
        }
        workDir = workDir.replace("/asr", ""); // 为了兼容asr和tts的路径
        workDir = workDir.replace("//", "/");
        AiSpeechLogUtil.d(TAG, "初始化AEE SDK，资源路径：" + workDir);

        final JLibrary.Params params = JLibrary.Params.builder()
                .appId(APP_ID)
                .apiKey(APP_KEY)
                .apiSecret(APP_SECRET)
                .workDir(workDir)
                .iLogMaxCount(1)
                .authInterval(555)
                .build();
        //初始化SDK
        new Thread(() -> JLibrary.getInst().initEntry(context, params)).start();
        //注册鉴权监听
        JLibrary.getInst().registerListener(coreListener);
        //注册能力回调监听
        JLibrary.getInst().registerListener(aiResponseListener);
        // 日志模式（stadout:0 logcat:1 file:2）
        AiHelper.getInst().setLogInfo(LogLvl.WARN, 1, "./");

        // 等待授权结果
        try {
            authSemaphore.acquire(); // 等待授权结果
        } catch (Exception e) {
            Thread.currentThread().interrupt();
            AiSpeechLogUtil.e(TAG, "等待授权结果时被中断", e);
        }
    }

    public void addAeeSdkListener(String ability, AiResponseListener aiResponseListener) {
        mapListeners.put(ability, aiResponseListener);
        AiSpeechLogUtil.d(TAG, "AEE SDK 添加监听，监听器数量：" + mapListeners.size());
    }

    public void removeAeeSdkListener(String ability, AiResponseListener aiResponseListener) {
        mapListeners.remove(ability);
    }


    // 学之友的鉴权不需要Code，可以直接鉴权，鉴权通过之后asr、mt、tts都可以使用
    public synchronized boolean tryAuth(Context context,String workDir) {
        if (isSdkInit) {
            AiSpeechLogUtil.d(TAG, "AEE SDK 已经鉴权+初始化成功了，可以直接使用");
            return true;
        }
        initSdk(context, workDir, outCoreListener);
        return isSdkInit;
    }
}
