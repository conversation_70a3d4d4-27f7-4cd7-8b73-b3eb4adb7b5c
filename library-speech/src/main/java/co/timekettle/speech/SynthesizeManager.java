package co.timekettle.speech;

import android.annotation.SuppressLint;
import android.content.Context;
import android.util.Log;

import co.timekettle.example.AudioFileHandler;
import co.timekettle.speech.synthesizer.HoyaSynthesizer;
import co.timekettle.speech.synthesizer.ISpeechSynthesizer;
import co.timekettle.speech.synthesizer.SynthesizerBase;
import co.timekettle.speech.synthesizer.XzySynthesizer;
import co.timekettle.speech.utils.Language;
import co.timekettle.speech.utils.AiSpeechLogUtil;

import java.io.IOException;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.HashMap;
import java.util.Iterator;
import java.util.List;
import java.util.Locale;
import java.util.Map;
import java.util.function.Function;

public class SynthesizeManager {

    private static final String TAG = "SynthesizerManager";

    private static SynthesizeManager instance = null;

    private Context context = null;
    private Listener listener;

    private final ArrayList<SynthesizerClass> mClassList = new ArrayList<>();

    private final HashMap<Long, SynthesizerBase> objectMap = new HashMap<>();

    private final HashMap<String, SpeechTask> contexts = new HashMap();
    private final HashMap<String, Function> callbacks = new HashMap();
    private AudioFileHandler audioWriter = null;
    private boolean writeToFile = false; // 是否写入文件
    private final Map<String, List<TkSynthesizeBlockOperation>> pendingCallbackQueues = new HashMap<>();

    private SynthesizerBase.Listener synthesizerBaseListener = new SynthesizerBase.Listener() {
        @Override
        public void onCompleted(SynthesizerBase synthesizer, byte[] data, String engine, SpeechError error) {
            long session = synthesizer.getSession();
            String key = synthesizer.getKey();
            String name = synthesizer.getName();
            String tag = synthesizer.getUuid();
            AiSpeechLogUtil.e(TAG, synthesizer.getName() + " 合成完成: " + key + " " + session + " " + data.length);

//            objectMap.remove(session);
            removeWorker(synthesizer);
            if (context != null && writeToFile) writeToFileHandler(data);

            if (data != null && data.length > 44) {
                byte[] wavHeader = {'R', 'I', 'F', 'F', 0x00, 0x00, 0x00, 0x00, 'W', 'A', 'V', 'E'};
                byte[] dstHeader = Arrays.copyOf(data, wavHeader.length);
                Arrays.fill(dstHeader, 4, 8, (byte) 0);
                if (wavHeader.equals(dstHeader)) {
                    byte[] temp = new byte[data.length - 44];
                    System.arraycopy(data, 44, temp, 0, data.length - 44);
                    data = temp;
                }
            }

            SpeechTask task = contexts.get(tag);
            if (task == null) AiSpeechLogUtil.e(TAG, key + "]通道任务: [" + session + "-" + tag + "]" + name + " 任务为空(onCompleted)");
            if (task == null) return; // 防止超时和 error 的两次回调
            if (error != null) {
                task.response.error = error;
            } else {
                task.response.data = data;
            }
            task.response.engine = engine;
            task.response.isLast = true;

            if (doCallback(tag, task)) return;

            if (listener != null) {
                listener.onSynthesizeResult(task, error);
            }
        }
    };

    private SynthesizeManager() {
//        addSynthesizer("iflytek", IflytekSynthesizer.class);
        addSynthesizer(ISpeechConstant.SYNTHESIZER.SYNTHESIZER_ISPEECH.name(), ISpeechSynthesizer.class);
        addSynthesizer(ISpeechConstant.SYNTHESIZER.SYNTHESIZER_HOYA.name(), HoyaSynthesizer.class);
        addSynthesizer(ISpeechConstant.SYNTHESIZER.SYNTHESIZER_XZY.name(), XzySynthesizer.class);
    }

    public static SynthesizeManager shareInstance() {
        if (instance == null) {
            instance = new SynthesizeManager();
        }
        return instance;
    }

    public void setContext(Context context) {
        this.context = context;
    }

    public void setListener(Listener listener) {
        this.listener = listener;
    }

    public void addSynthesizer(String name, Class<?> objectClass) {
        removeSynthesizer(name);
        mClassList.add(new SynthesizerClass(name, objectClass));
    }

    public void addSynthesizer(int index, String name, Class<?> objectClass) {
        removeSynthesizer(name);
        mClassList.add(index, new SynthesizerClass(name, objectClass));
    }

    public void addSynthesizer(int index, String name) {
        Class<?> objectClass = null;
        if (ISpeechConstant.SYNTHESIZER.SYNTHESIZER_HOYA.name().equals(name)) {
            objectClass = HoyaSynthesizer.class;
        } else if (ISpeechConstant.SYNTHESIZER.SYNTHESIZER_ISPEECH.name().equals(name)) {
            objectClass = ISpeechSynthesizer.class;
        } else if (ISpeechConstant.SYNTHESIZER.SYNTHESIZER_XZY.name().equals(name)) {
            objectClass = XzySynthesizer.class;
        }
        assert objectClass != null : "找不到对应类型: "+name;
        addSynthesizer(index, name, objectClass);
    }

    public void removeSynthesizer(String name)
    {
        Iterator<SynthesizerClass> iterator = mClassList.iterator();
        while (iterator.hasNext()) {
            SynthesizerClass s = iterator.next();
            if (s.name.equals(name)) {
                iterator.remove();
            }
        }
    }

    public String getFirstSynthesizerName() {
        if (mClassList.size() > 0) {
            return  mClassList.get(0).name;
        }
        return null;
    }

    public void setWriteToFile(boolean writeToFile) {
        this.writeToFile = writeToFile;
    }

    private void writeToFileHandler(byte[] data) {
        if (data != null && data.length > 0) {
            String WorkDir = context.getExternalCacheDir().getAbsolutePath();
            try {
                audioWriter = new AudioFileHandler("synthesize", WorkDir + "/audio/");
                audioWriter.writeAndFlush(data);
                audioWriter.close();
                audioWriter = null;
            } catch (IOException e) {
                throw new RuntimeException(e);
            }
        }
    }

    /* 闭包方式回调 */
    public long start(SpeechTask<String, byte[]> task, Function<SpeechTask<String, byte[]>, Object> callback) {
        final String chkey = task.chkey;
        final String code = task.request.code;
        final String text = task.request.data;
        final Map<?, ?> opts = task.request.opts;
        final long session = task.session;

        SynthesizerBase synthesizer = findSynthesizer(chkey, code);
        if (synthesizer == null) {
            return 0;
        }
        synthesizer.setSession(session);
        Language srcLanguage = new Language(code, synthesizer.platformCode(code));
        String voiceName = null;
        if (opts != null) {
            voiceName = (String) opts.get(SpeechRequest.OptsKeyTtsVoiceName);
        }
        synthesizer.start(srcLanguage, text, voiceName); // FIXME: 2020/11/20 如果内部是同步返回回调结果, 导致回调后任务从objectMap移除, 然后下方又添加了此任务

        contexts.put(synthesizer.getUuid(), task);
        callbacks.put(synthesizer.getUuid(), callback);

        synchronized (pendingCallbackQueues) {
            List<TkSynthesizeBlockOperation> pendingCallbackQueue = pendingCallbackQueues.get(chkey);
            if (pendingCallbackQueue == null) {
                pendingCallbackQueue = new ArrayList<>();
                pendingCallbackQueues.put(chkey, pendingCallbackQueue);
            }
            synchronized (pendingCallbackQueue) {
                TkSynthesizeBlockOperation op = new TkSynthesizeBlockOperation(synthesizer.getUuid(), task, callback);
                pendingCallbackQueue.add(op);
                AiSpeechLogUtil.d(TAG, String.format(Locale.getDefault(), "合成队列 [%s]添加了任务[%d-%s], 将处理", op.getTask().chkey, op.getTask().session, op.tag));
            }
        }
        objectMap.put(synthesizer.getSession(), synthesizer);

        AiSpeechLogUtil.d(TAG, chkey + "通道任务: [" + session + "]" + synthesizer.getName() + " 合成: [" + code + "]text: " + text);

        return session;
    }

    public void stopAllWorker() {
        synchronized(this) {
            for (SynthesizerBase synthesizer : objectMap.values()) {
                synthesizer.stop();
            }
            objectMap.clear();
        }
        synchronized (pendingCallbackQueues) {
            for (String chkey : pendingCallbackQueues.keySet()) {
                List<TkSynthesizeBlockOperation> pendingCallbackQueue = pendingCallbackQueues.get(chkey);
                if (pendingCallbackQueue != null) {
                    synchronized (pendingCallbackQueue) {
                        pendingCallbackQueue.clear();
                    }
                }
            }
        }
    }

    public void stopWorker(long workerId) {
        synchronized(this) {
            SynthesizerBase w = null;
            for (SynthesizerBase synthesizer : objectMap.values()) {
                if (synthesizer.getSession() == workerId) {
                    w = synthesizer;
                    w.stop();
                    AiSpeechLogUtil.e(TAG, "SynthesizeManager 停止任务 " + w.getKey() + " " + w.getSession());
                }
            }

            if (w != null) {
                objectMap.remove(w.getSession());
                List<TkSynthesizeBlockOperation> pendingCallbackQueue = pendingCallbackQueues.get(w.getKey());
                if (pendingCallbackQueue != null) {
                    synchronized (pendingCallbackQueue) {
                        pendingCallbackQueue.clear();
                    }
                }
            }
        }
    }

    private boolean isCanSynthesize(String name) {

        for (Long session : objectMap.keySet()) {
            SynthesizerBase base = objectMap.get(session);
            if (base.getName().equals(name) && base.isSingleton()) { // 当前还有任务, 由于是单例所以不允许处理
                return false;
            }
        }

        return true;
    }

    private void removeWorker(SynthesizerBase worker) {
        synchronized (this) {
            AiSpeechLogUtil.d(TAG,"移除合成器，移除前的大小："+objectMap.size());
            objectMap.remove(worker.getSession(), worker);
            AiSpeechLogUtil.d(TAG,"移除合成器，移除后的大小："+objectMap.size());
        }
    }

    private SynthesizerBase findSynthesizer(String key, String srcCode) {
        AiSpeechLogUtil.d(TAG, "findWorker: [key=" + key + "][srcCode=" + srcCode + "]" + " onlyUseOffline:" + onlyUseOffline);
        try {
            for (SynthesizerClass s : mClassList) {
                Class<?> obj = s.className;
                SynthesizerBase base = (SynthesizerBase) obj.newInstance();

                if (onlyUseOffline) {
                    if (!base.isOfflineModule) {
                        AiSpeechLogUtil.e(TAG, base.getName() + " 是在线模块, 网络未开启, 当前只能用离线模块");
                        continue;
                    }
                }

                if (base instanceof HoyaSynthesizer) {
                    boolean isEnalbe = !base.isOfflineModule || OfflineManager.getInstance().isEnable(null, srcCode);
                    ((HoyaSynthesizer)base).setIgnoreAccent(isEnalbe);
                }
                boolean isSupport = base.isSupport(srcCode);
                boolean isCanSynthesize = isCanSynthesize(s.name);
                AiSpeechLogUtil.e(TAG, "findSynthesizer: 合成器[" + base.getName() + "] 条件 [isSupport=" + isSupport + "][isCanRecognize=" + isCanSynthesize + "]");
                if (isSupport && isCanSynthesize) {
                    base.setContext(context);
                    base.setKey(key);
                    base.setListener(synthesizerBaseListener);
                    base.setSession(System.currentTimeMillis());
                    return base;
                }
            }
        } catch (IllegalAccessException e) {
            e.printStackTrace();
        } catch (InstantiationException e) {
            e.printStackTrace();
        }
        return null;
    }

    private boolean doCallback(String tag, SpeechTask task) {
        List<TkSynthesizeBlockOperation> pendingCallbackQueue = pendingCallbackQueues.get(task.chkey);
        if (pendingCallbackQueue != null) {
            synchronized (pendingCallbackQueue) {
                // 标记已完成, 可回调, 等待合适时机回调
                for (int i = 0; i < pendingCallbackQueue.size(); i++) {
                    TkSynthesizeBlockOperation op = pendingCallbackQueue.get(i);
                    if (op.getTask().equals(task)) {
                        AiSpeechLogUtil.d(TAG, String.format(Locale.getDefault(), "合成队列 [%s]任务结束[%d-%s], 等待回调", op.getTask().chkey, op.getTask().session, tag));
                        op.setFinished(true);
                        break;
                    }
                }

                // 从头往后遍历, 有任务还未完成时就等待下次处理回调, 即遇到 isFinished == NO 就退出处理
                int removeCount = 0;
                for (int i = 0; i < pendingCallbackQueue.size(); i++) {
                    TkSynthesizeBlockOperation op = pendingCallbackQueue.get(i);
                    if (!op.isFinished()) {
                        AiSpeechLogUtil.d(TAG, String.format(Locale.getDefault(),"合成队列 [%s]存在还未完成的任务[%d-%s], 等待完成后才回调[%s]", op.getTask().chkey, op.getTask().session, op.tag, tag));
                        break; // 超时如何处理
                    }

                    op.getCallback().apply(op.getTask());
                    contexts.remove(op.getTag());
                    callbacks.remove(op.getTag());

                    removeCount++;
                }
                if (removeCount > 0) {
                    pendingCallbackQueue.subList(0, removeCount).clear();
                }
            }
        }
        return true;
    }

    private boolean onlyUseOffline = false;
    public void setOnlyOffline(boolean offline) {
        onlyUseOffline = offline;
    }

    public interface Listener {
        void onSynthesizeResult(SpeechTask task, SpeechError error);
    }

    class SynthesizerClass
    {
        public String name;
        public Class<?> className;

        public SynthesizerClass(String name, Class<?> className)
        {
            this.name = name;
            this.className = className;
        }
    }

    public static class TkSynthesizeBlockOperation {
        private final String tag;
        private final SpeechTask task;
        private final Function<SpeechTask<String, byte[]>, Object> callback;
        private final long createTimestamp;
        private  boolean isFinished;

        public TkSynthesizeBlockOperation(String tag, SpeechTask task, Function<SpeechTask<String, byte[]>, Object> callback) {
            this.tag = tag;
            this.task = task;
            this.callback = callback;
            this.createTimestamp = System.currentTimeMillis();
            this.isFinished = false;
        }

        public String getTag() {
            return tag;
        }

        public SpeechTask getTask() {
            return task;
        }

        public Function<SpeechTask<String, byte[]>, Object> getCallback() {
            return callback;
        }

        public boolean isFinished() {
            return isFinished;
        }

        public void setFinished(boolean isFinished) {
            this.isFinished = isFinished;
        }
    }
}
