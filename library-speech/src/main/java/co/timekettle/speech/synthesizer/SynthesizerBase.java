package co.timekettle.speech.synthesizer;

import android.content.Context;

import co.timekettle.speech.SpeechError;
import co.timekettle.speech.utils.Language;

import java.util.HashMap;
import java.util.UUID;

public abstract class SynthesizerBase {
    protected String uuid; // 唯一标识符

    protected String name;
    protected String key;
    protected int ttsSpeed = 100;

    protected long session = 0;
    protected Language srcCode;
    protected boolean singleton;
    protected Context context;
    protected Listener listener;
    protected HashMap<String, String> support = new HashMap<>();
    public boolean isOfflineModule = false;
    protected String voiceName;

    public String getUuid() {
        if (uuid == null) {
            uuid = UUID.randomUUID().toString();
        }
        return uuid;
    }

    public String getName() {
        return name;
    }

    public void setKey(String key) {
        this.key = key;
    }

    public String getKey() {
        return this.key;
    }

    public void setContext(Context context) {
        this.context = context;
    }

    public boolean isSingleton() {
        return singleton;
    }

    public long getSession() {
        return session;
    }

    public void setSession(long s) {
        this.session = s;
    }

    public void setTtsSpeed(int ttsSpeed) {
        this.ttsSpeed = ttsSpeed;
    }

    public void setListener(Listener listener) {
        this.listener = listener;
    }

    public Listener getListener() {
        return this.listener;
    }

    public abstract boolean isSupport(String srcCode);

    public abstract String platformCode(String srcCode);

    public String getVoiceName() {
        return voiceName;
    }

    public void setVoiceName(String voiceName) {
        this.voiceName = voiceName;
    }

    public void start(final Language srcCode, final String text) {
        this.srcCode = srcCode;
    }
    public void start(Language srcCode, String text, String voiceName) {
        this.srcCode = srcCode;
        this.voiceName = voiceName;
    }

    public void stop() {}

    public interface Listener {
        void onCompleted(SynthesizerBase synthesizer, byte[] data, String engine, SpeechError error);
    }
}
