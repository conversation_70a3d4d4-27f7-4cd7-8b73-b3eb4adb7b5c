package co.timekettle.speech.synthesizer;

import android.content.Context;
import android.util.Log;

import com.iflytek.voice.Constant;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;

import co.timekettle.speech.synthesizer.xzy.ParamInfo;
import co.timekettle.speech.synthesizer.xzy.XttsHelper;
import co.timekettle.speech.synthesizer.xzy.XttsParams;
import co.timekettle.speech.utils.FantiUtil;
import co.timekettle.speech.utils.Language;
import co.timekettle.speech.utils.AiSpeechLogUtil;


// 这个类不是单例，每次合成都会去创建一个这个类的对象
public class XzySynthesizer extends SynthesizerBase {
    private static final String TAG = "XzySynthesizer";
    private boolean ignoreAccent = false;
    static String WORK_DIR = "";   // 此目录不生效，xzy的工作目录，设置为OfflineV2，在识别器里面已经设置了，xzy会自动检索目录下的文件

    private List<String> supportLans = new ArrayList<>(); // key：lan  value:path

    public XzySynthesizer() {
        AiSpeechLogUtil.d(TAG, "XzySynthesizer 构造函数");
        name = "XzySynthesizer";
        singleton = true;
        this.isOfflineModule = true;
        initSupport();
    }

    @Override
    public void setTtsSpeed(int ttsSpeed) {
        AiSpeechLogUtil.d(TAG, "设置TTS语速：" + ttsSpeed);
        super.setTtsSpeed(ttsSpeed);
    }

    public void setIgnoreAccent(boolean ignoreAccent) {
        this.ignoreAccent = ignoreAccent;
    }


    private void initSupport() {
        addSupport("zh");
        addSupport("ja");
        addSupport("en");
        addSupport("fr");
        addSupport("es");
        addSupport("ru");
        addSupport("de");
        addSupport("ko");
    }

    private void addSupport(String lan) {
        supportLans.add(lan);
    }


    public static void load(Context ctt) {
        AiSpeechLogUtil.d(TAG, "初始化 XzySynthesizer（不耗时）");
    }

    //
    @Override
    public boolean isSupport(String srcCode) {
        if(srcCode.isEmpty()) return false;
        String bigCode = srcCode.toLowerCase().split("-")[0];
        return supportLans.contains(bigCode);
    }

    @Override
    public String platformCode(String srcCode) {
        return srcCode;
    }

    @Override
    public void start(Language srcCode, String text) {
        AiSpeechLogUtil.d(TAG, "start: 合成" + " srcCode: " + srcCode.standardCode + " text: " + text);
        if (text == null || text.isEmpty()) return;

        ///////////// 兼容繁体 tts
        if (srcCode.standardCode.toLowerCase().endsWith("zh-tw")) {
            srcCode.standardCode = "zh-CN";
            text = FantiUtil.simplized(text);
            AiSpeechLogUtil.d(TAG, "start: 繁体 code: zh-TW 转换为 zh-CN");
        }
        ///////////// 兼容繁体 tts

        String languageCode = srcCode.standardCode;
        if (languageCode.contains("-")) {
            languageCode = languageCode.split("-")[0];
        }
        String code = languageCode;

        ///////////// 兼容未找到具体口音时使用默认口音的 tts
        // FIXME: 2020/11/9 开启线程去做合成的耗时操作, 以免阻塞返回 合成session

        String finalText = text;

        XttsParams xttsParams = new XttsParams();
        List<ParamInfo> xttsLanguageList = XttsParams.getXTTSLanguageList();
        xttsLanguageList.stream().filter(paramInfo -> paramInfo.languageCode.startsWith(code)).findFirst().ifPresent(paramInfo -> {
            xttsParams.vcn = paramInfo.personName;
            xttsParams.volume = paramInfo.volume;
            xttsParams.language = Integer.parseInt(paramInfo.value);
        });

        new Thread(() -> {
            XttsHelper.getInstance().doSynTTS(xttsParams, finalText, (output, error) -> {
                if (listener != null) {
                    listener.onCompleted(this, output, name, null);
                }
            });
        }).start();
    }

    @Override
    public void start(Language srcCode, String text, String voiceName) {
        this.start(srcCode, text);
    }

    // 停止TTS的合成
    @Override
    public void stop() {
        super.stop();
    }
}

