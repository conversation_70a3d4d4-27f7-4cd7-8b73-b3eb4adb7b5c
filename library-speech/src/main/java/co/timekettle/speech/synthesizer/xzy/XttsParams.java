package co.timekettle.speech.synthesizer.xzy;


import java.util.ArrayList;
import java.util.List;

public class XttsParams {

    public String vcn = "xiaoyan";
    public int language = 1;
    public String encode = "UTF-8";
    public int pitch = 50;
    public int speed = 50;
    public int volume = 50;

    /**
     * 测试文本资源，一下资源顺序与发音人、语种相对应
     *
     * @return
     */
    public static List<String> testTxt() {
        List<String> txtList = new ArrayList<>();
        txtList.add("各族人民亲如一家，是中华民族伟大复兴必定要实现的根本保证。");
        txtList.add("The weather is good for going out today");
        txtList.add("兄はきょうはいい天気だから遊びに行くにはよい");
        txtList.add("오빠 오늘 날씨가 너무 좋아서 밖에 나가 놀기 좋아요");
        txtList.add("Hermano hace un buen día para salir");
        txtList.add("Il fait beau aujourd’hui pour sortir.");
        txtList.add("Сегодня прекрасный день для прогулок.");
        txtList.add("Es ist ein schöner tag zum ausgehen.");
        txtList.add("วันนี้อากาศดีเหมาะที่จะออกไปเที่ยว");    //泰语
        txtList.add("اليوم هو يوم جميل ، هو مناسبة لأنّ الخروج للعب");  //阿拉伯
        txtList.add("今天天气很好，很适合出去玩");  //粤语
        return txtList;
    }

    public static List<ParamInfo> getVCN() {
        List<ParamInfo> vcnList = new ArrayList<>();
        vcnList.add(new ParamInfo("yilin", "yilin(中文)"));
        vcnList.add(new ParamInfo("catherine", "catherine(英文)"));
        vcnList.add(new ParamInfo("zhongcun", "zhongcun（日语）"));
        vcnList.add(new ParamInfo("miya", "miya（韩语）"));
        vcnList.add(new ParamInfo("aurora", "aurora（西班牙语）"));
        vcnList.add(new ParamInfo("lisa", "lisa（法语)"));
        vcnList.add(new ParamInfo("keshu", "keshu（俄语）"));
        vcnList.add(new ParamInfo("christiane", "christiane（德语）"));
        vcnList.add(new ParamInfo("suparut", "suparut（泰语）"));
        vcnList.add(new ParamInfo("rania", "rania（阿拉伯语）"));
        vcnList.add(new ParamInfo("xiaoyue", "xiaoyue（粤语）"));
        return vcnList;
    }

    public static List<ParamInfo> getXTTSLanguageList() {
        List<ParamInfo> languageList = new ArrayList<>();
        languageList.add(new ParamInfo("1", "中文", "zh-CN", "yilin", 50));
        languageList.add(new ParamInfo("2", "英文", "en-US", "catherine", 20));
        languageList.add(new ParamInfo("5", "日语", "ja-JP", "zhongcun", 20));
        languageList.add(new ParamInfo("97", "法语", "fr-FR", "lisa", 40));
        languageList.add(new ParamInfo("23", "西班牙语", "es-ES", "aurora", 30));
        languageList.add(new ParamInfo("6", "俄语", "ru-RU", "keshu", 20));
        languageList.add(new ParamInfo("93", "德语", "de-DE", "christiane", 50));
        languageList.add(new ParamInfo("16", "韩语", "ko-KR", "miya", 100));
        languageList.add(new ParamInfo("27", "泰语", "th-TH", "suparut", 50));
        return languageList;
    }

    public static List<ParamInfo> getCodeTypes() {
        List<ParamInfo> encodeList = new ArrayList<>();
        encodeList.add(new ParamInfo("UTF-8", "UTF-8"));
        encodeList.add(new ParamInfo("GBK", "GBK"));
        return encodeList;
    }
}
