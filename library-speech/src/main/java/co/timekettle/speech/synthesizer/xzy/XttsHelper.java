package co.timekettle.speech.synthesizer.xzy;

import static co.timekettle.speech.aeesdk.AeeSdkUtil.TTS_ABILITY;

import com.iflytek.aikit.core.AeeEvent;
import com.iflytek.aikit.core.AiHandle;
import com.iflytek.aikit.core.AiHelper;
import com.iflytek.aikit.core.AiInput;
import com.iflytek.aikit.core.AiResponse;
import com.iflytek.aikit.core.AiResponseListener;
import com.iflytek.aikit.core.CoreListener;

import java.util.List;

import co.timekettle.speech.aeesdk.AeeSdkUtil;
import co.timekettle.speech.ispeech.algorithm.ResampleProcessor;
import co.timekettle.speech.utils.AiSpeechLogUtil;
import co.timekettle.speech.utils.ByteArray;
import co.timekettle.speech.utils.BytesTrans;


/**
 * 还未改成单例，最好是单例调用
 */
import java.util.concurrent.BlockingQueue;
import java.util.concurrent.LinkedBlockingQueue;
import java.util.concurrent.CountDownLatch;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;

public class XttsHelper {
    private static final String TAG = "XzyTtsHelper";
    private static volatile XttsHelper instance;
    private ConcurrentHashMap<AiHandle, ByteArray> mapTTS = new ConcurrentHashMap<>();
    private final ResampleProcessor resampleProcessor = new ResampleProcessor();
    private final BlockingQueue<TtsTask> taskQueue = new LinkedBlockingQueue<>();  // 任务队列
    private final ExecutorService executorService = Executors.newSingleThreadExecutor();  // 单线程while循环，从任务队列里取任务并执行
    private CountDownLatch latch; // 用于线程同步的信号量
    private volatile boolean running = true; // 控制线程运行状态的标志位
    private TtsTask curRunningTask; // 正在执行的Task

    private XttsHelper() {
        initSdk("", null);
        executorService.submit(new TaskProcessor());
    }

    public static XttsHelper getInstance() {
        if (instance == null) {
            synchronized (XttsHelper.class) {
                if (instance == null) {
                    instance = new XttsHelper();
                }
            }
        }
        return instance;
    }

    // AI响应监听器，处理合成结果和事件
    private final AiResponseListener aiResponseListener = new AiResponseListener() {
        @Override
        public void onResult(String ability, int handleID, List<AiResponse> list, Object o) {
            AiHandle aiHandler = mapTTS.keySet().stream().filter(aiHandle -> aiHandle.getId() == handleID).findFirst().orElse(null);
            if (null != list && !list.isEmpty()) {
                for (int i = 0; i < list.size(); i++) {
                    byte[] bytes = list.get(i).getValue();
                    ByteArray ba = mapTTS.get(aiHandler);
                    if (ba != null && bytes != null) ba.cat(bytes);
                }
            }
        }

        @Override
        public void onEvent(String ability, int handleID, int event, List<AiResponse> eventData, Object usrContext) {
            AiHandle aiHandler = mapTTS.keySet().stream().filter(aiHandle -> aiHandle.getId() == handleID).findFirst().orElse(null);
            if (event == AeeEvent.AEE_EVENT_END.getValue()) {
                AiSpeechLogUtil.d(TAG, "合成结束 handleID:" + handleID);
                handleSynthesisEnd(aiHandler);
            } else if (event == AeeEvent.AEE_EVENT_START.getValue()) {
                AiSpeechLogUtil.d(TAG, "合成开始 handleID:" + handleID);
            }
        }

        @Override
        public void onError(String s, int i, int i1, String s1, Object o) {
            AiSpeechLogUtil.e(TAG, "错误：");
            latch.countDown();  // 即使发生错误也通知任务处理线程继续
        }
    };

    // 处理合成结束事件
    private void handleSynthesisEnd(AiHandle aiHandler) {
        byte[] result = mapTTS.get(aiHandler).getArray();
        short[] inputShorts = BytesTrans.getInstance().Bytes2Shorts(result);
        int outputLength = (int) (inputShorts.length * 16.0 / 24.0);
        short[] outputShorts = new short[outputLength];
        resampleProcessor.resampleDownGPT(outputShorts, inputShorts, 24_000, 16000);
        curRunningTask.xttsListener.onCompleted(BytesTrans.getInstance().Shorts2Bytes(outputShorts), null);
        StreamEnd(aiHandler);
        latch.countDown();  // 通知任务处理线程当前任务已完成
    }


    // 初始化SDK
    public void initSdk(String workDir, CoreListener coreListener) {
        AiSpeechLogUtil.d(TAG, "初始化 XTTSHelper");
        AeeSdkUtil.getInstance().removeAeeSdkListener(TTS_ABILITY, aiResponseListener);
        AeeSdkUtil.getInstance().addAeeSdkListener(TTS_ABILITY, aiResponseListener);
        if (AeeSdkUtil.getInstance().isSdkInit) return;
    }

    // 提交合成任务
    public void doSynTTS(XttsParams params, String text, XTTSListener xttsListener) {
//        this.xttsListener = xttsListener;
        TtsTask task = new TtsTask(params, text, xttsListener);
        taskQueue.offer(task); // 将任务加入队列
    }

    // 停止任务处理
    public void stopAllTasks() {
        running = false; // 设置标志位为false，停止线程
        executorService.shutdownNow(); // 立即关闭线程池
        curRunningTask = null;
        taskQueue.clear();
    }

    // 任务处理器，负责从队列中取出任务并处理
    private class TaskProcessor implements Runnable {
        @Override
        public void run() {
            while (running) { // 使用running标志位控制循环
                try {
                    curRunningTask = taskQueue.take(); // 从队列中获取任务
                    latch = new CountDownLatch(1);
                    processTask(curRunningTask);
                    latch.await();  // 等待任务完成信号
                } catch (InterruptedException e) {
                    Thread.currentThread().interrupt();
                    break; // 退出循环
                }
            }
        }

        // 处理单个合成任务
        private void processTask(TtsTask task) {
            AiInput.Builder paramBuilder = AiInput.builder();
            paramBuilder.param("vcn", task.params.vcn)
                    .param("language", task.params.language)
                    .param("textEncoding", task.params.encode)
                    .param("pitch", task.params.pitch)
                    .param("volume", task.params.volume)
                    .param("reg", 0)
                    .param("rdn", 0)
                    .param("speed", task.params.speed);
            AiSpeechLogUtil.d(TAG, "开始合成 text:" + task.text);
            AiHandle aiHandle = AiHelper.getInst().start(AeeSdkUtil.TTS_ABILITY, paramBuilder.build(), null);
            if (!aiHandle.isSuccess()) {
                handleTaskFailure(aiHandle);
                return;
            }
            if (!mapTTS.containsKey(aiHandle)) {
                mapTTS.put(aiHandle, new ByteArray());
            }
            AiInput.Builder dataBuilder = AiInput.builder();
            dataBuilder.text("text", task.text);
            int ret = AiHelper.getInst().write(dataBuilder.build(), aiHandle);
            if (ret != 0) {
                handleTaskFailure(aiHandle);
                return;
            }
            AiSpeechLogUtil.d(TAG, "write成功 aiHandle Id:" + aiHandle.getId());
        }

        private void handleTaskFailure(AiHandle aiHandle) {
            AiSpeechLogUtil.e(TAG, "任务处理失败: " + aiHandle.getCode());
            mapTTS.remove(aiHandle);
            latch.countDown();  // 即使失败也通知任务处理线程继续
        }
    }

    // 结束流处理  必须要和start成对调用 否则会报错
    public int StreamEnd(AiHandle aiHandle) {
        if (aiHandle == null) {
            AiSpeechLogUtil.e(TAG, "StreamEnd失败: aiHandle is null");
            return -1;
        } else {
            int ret = AiHelper.getInst().end(aiHandle);
            mapTTS.remove(aiHandle);
            AiSpeechLogUtil.d(TAG, "StreamEnd, mapTTS.size:" + mapTTS.size());
            return ret;
        }
    }

    // 合成任务类
    private static class TtsTask {
        XttsParams params;
        String text;
        XTTSListener xttsListener;

        TtsTask(XttsParams params, String text, XTTSListener xttsListener) {
            this.params = params;
            this.text = text;
            this.xttsListener = xttsListener;
        }
    }

    // 合成完成监听器接口
    public interface XTTSListener {
        void onCompleted(byte[] output, String error);
    }
}
