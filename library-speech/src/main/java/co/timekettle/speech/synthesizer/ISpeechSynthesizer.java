package co.timekettle.speech.synthesizer;

import android.util.Log;

import co.timekettle.speech.SpeechError;
import co.timekettle.speech.utils.AiSpeechLogUtil;
import co.timekettle.speech.utils.Language;
import co.timekettle.tmkengine.NetSessionContext;
import co.timekettle.tmkengine.TmkSpeechClient;

import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.nio.ByteBuffer;
import java.util.Arrays;
import java.util.Locale;

public class ISpeechSynthesizer extends SynthesizerBase {
    private ByteArrayOutputStream cache;
    private NetSessionContext synthesizer = null;
    private static final String TAG = "ISpeechSynthesizer";

    private long startTime = 0L; // 任务开始时间
    public ISpeechSynthesizer() {
        name = "ispeech";
        cache = new ByteArrayOutputStream();
    }

    @Override
    public boolean isSupport(String srcCode) {
        return true;
    }

    @Override
    public String platformCode(String srcCode) {
        return srcCode;
    }


    private  NetSessionContext.ContextListener netListener = new NetSessionContext.ContextListener() {
        @Override
        public void onRecognizeResult(NetSessionContext context, long session, boolean isLast, String srcCode, String rtext, String ttext, String engine) {
        }

        @Override
        public void onTranslateResult(NetSessionContext context, long session, String result, String engine) {
        }

        @Override
        public void onSynthesizeBuffer(NetSessionContext context, long session, byte[] output, int outputSize) {
            try {
                cache.write(output);
            } catch (IOException e) {
                e.printStackTrace();
            }
        }

        @Override
        public void onCompleted(NetSessionContext context, long session, String engine) {
            if (listener != null) {
                byte[] soundData = cache.toByteArray();
                long costTime = System.currentTimeMillis() - startTime;
                AiSpeechLogUtil.e(TAG, String.format(Locale.getDefault(), "onCompleted [%d]合成音频长度(%dms) : %d", session, costTime, soundData.length));
//                if (soundData.length > 0) {
//                    soundData = removeConsecutiveZeros(soundData, 100);
//                }
                listener.onCompleted(ISpeechSynthesizer.this, soundData, engine, null);
                Log.d(TAG,"合成器结束："+context);
            }
            clear();
        }

        @Override
        public void onError(NetSessionContext context, long session, String engine, int code, String message) {
            if (listener != null) {
                byte[] soundData = cache.toByteArray();
                long costTime = System.currentTimeMillis() - startTime;
                AiSpeechLogUtil.e(TAG, String.format(Locale.getDefault(), "onError [%d]合成音频长度(%dms) : %d", session, costTime, soundData.length));
//                if (soundData.length > 0) {
//                    soundData = removeConsecutiveZeros(soundData, 100);
//                }
                listener.onCompleted(ISpeechSynthesizer.this, soundData, engine, new SpeechError(code, message));
            }
            clear();
        }
    };
    
    @Override
    public void start(Language srcCode, String text) {
        startTime = System.currentTimeMillis();
        synthesizer = TmkSpeechClient.shareInstance().createSynthesizer(srcCode.standardCode, text, netListener);
        synthesizer.setSession(getSession());
        synthesizer.start();
    }

    @Override
    public void start(Language srcCode, String text, String voiceName) {
        startTime = System.currentTimeMillis();
        synthesizer = TmkSpeechClient.shareInstance().createSynthesizer(srcCode.standardCode,voiceName, text, netListener);
        synthesizer.setSession(getSession());
        synthesizer.start();
    }

    private void clear(){
        if (synthesizer != null) {
            synthesizer.setListener(null);
            synthesizer = null;
        }
        try {
            cache.reset();
            cache.close();
        } catch (IOException e) {
            e.printStackTrace();
        }
    }

    @Override
    public void stop() {
        if (synthesizer != null) {
            synthesizer.stop();
            synthesizer.setListener(null);
            synthesizer = null;
        }
        clear();
    }

    private byte[] removeConsecutiveZeros(byte[] bytes, int zeroCount) {
        if (bytes == null) return new byte[0];

        int length = bytes.length / 2;
        ByteBuffer resultData = ByteBuffer.allocate(bytes.length);
        int zeroSequenceCount = 0;

        for (int i = 0; i < length; i++) {
            if (bytes[i * 2] == 0 && bytes[i * 2 + 1] == 0) {
                zeroSequenceCount++;
            } else {
                zeroSequenceCount = 0;
            }
            resultData.put(bytes, i * 2, 2);
            if (zeroSequenceCount == zeroCount) {
                // Remove the zero sequence from resultData
                resultData.position(resultData.position() - zeroCount * 2);
                zeroSequenceCount = 0;
            }
        }

        // Trim the resultData buffer to the correct size
        byte[] resultArray = Arrays.copyOf(resultData.array(), resultData.position());
        return resultArray;
    }
}
