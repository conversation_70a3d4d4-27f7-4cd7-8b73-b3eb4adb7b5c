package co.timekettle.speech.synthesizer;

import android.content.Context;
import android.content.pm.PackageManager;

import co.timekettle.speech.SpeechError;
import co.timekettle.speech.utils.ByteArray;
import co.timekettle.speech.utils.FantiUtil;
import co.timekettle.speech.utils.HttpsConnection;
import co.timekettle.speech.utils.Language;
import co.timekettle.speech.utils.AiSpeechLogUtil;
import co.timekettle.speech.utils.ZipUtils;

import java.io.BufferedReader;
import java.io.File;
import java.io.FileInputStream;
import java.io.FileNotFoundException;
import java.io.FileReader;
import java.io.IOException;
import java.io.InputStream;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.CountDownLatch;
import java.util.concurrent.Semaphore;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.locks.Lock;
import java.util.concurrent.locks.ReentrantLock;

import kr.co.voiceware.java.vtapi.Constants;
import kr.co.voiceware.java.vtapi.EngineInfo;
import kr.co.voiceware.java.vtapi.Options;
import kr.co.voiceware.java.vtapi.SyncMarkInfo;
import kr.co.voiceware.java.vtapi.SyncWordInfo;
import kr.co.voiceware.java.vtapi.VoiceText;
import kr.co.voiceware.java.vtapi.VoiceTextListener;
import kr.co.voiceware.vtlicensemodule.LicenseDownloadListener;
import kr.co.voiceware.vtlicensemodule.VtLicenseSetting;

public class HoyaSynthesizer extends SynthesizerBase {
    private static final String TAG = "HoyaSynthesizer";
    private boolean ignoreAccent = false;
    public HoyaSynthesizer() {
        name = "hoyaSynthesizer";
        singleton = false;
        this.isOfflineModule = true;
        HoyaUtil.getInstance().initEngine();
    }

    @Override
    public void setTtsSpeed(int ttsSpeed) {
        AiSpeechLogUtil.d(TAG,"设置TTS语速："+ttsSpeed);
        super.setTtsSpeed(ttsSpeed);
        HoyaUtil.getInstance().setTtsSpeed(ttsSpeed);
    }

    public void setIgnoreAccent(boolean ignoreAccent) {
        this.ignoreAccent = ignoreAccent;
    }

    @Override
    public boolean isSupport(String srcCode) {
        ///////////// 兼容繁体 tts
        if (srcCode.toLowerCase().endsWith("zh-tw")) {
            srcCode = "zh-CN";
            AiSpeechLogUtil.d(TAG, "isSupport: 繁体 code: zh-TW 转换为 zh-CN");
        }
        ///////////// 兼容繁体 tts

        ///////////// 兼容未找到具体口音时使用默认口音的 tts
        boolean isSupport = HoyaUtil.getInstance().isSupportKey(srcCode);
        if (!isSupport) {
            // FIXME: 2022/8/26 开启离线开关时, 才使用默认口音 tts
            if (ignoreAccent) {
                //            AiSpeechLogUtil.d(TAG, "isSupport: 当前语言对已开启离线, 尝试查找支持的语种(忽略口音)");
                String ccode = srcCode.split("-")[0].toLowerCase();
                String trueCode = HoyaUtil.getInstance().getSupportKey(ccode);
                if (trueCode != null) {
                    AiSpeechLogUtil.d(TAG, "isSupport: Hoya 未找到 " + srcCode + " 合成, 使用默认合成: " + trueCode);
                    return true;
                }
            }
        }
        ///////////// 兼容未找到具体口音时使用默认口音的 tts

//        AiSpeechLogUtil.d(TAG, "isSupport: " + srcCode + ": " + isSupport);
        return isSupport;
    }

    @Override
    public String platformCode(String srcCode) {
        return srcCode;
    }

    @Override
    public void start(Language srcCode, String text) {
        AiSpeechLogUtil.d(TAG, "start: 合成" + " srcCode: " + srcCode.standardCode + " text: " + text);
        if (text == null || text == "") return;

        ///////////// 兼容繁体 tts
        if (srcCode.standardCode.toLowerCase().endsWith("zh-tw")) {
            srcCode.standardCode = "zh-CN";
            text = FantiUtil.simplized(text);
            AiSpeechLogUtil.d(TAG, "start: 繁体 code: zh-TW 转换为 zh-CN");
        }
        ///////////// 兼容繁体 tts

        String code = srcCode.standardCode;
        ///////////// 兼容未找到具体口音时使用默认口音的 tts
        boolean isSupport = HoyaUtil.getInstance().isSupportKey(code);
        if (!isSupport) {
            if (ignoreAccent) {
                // FIXME: 2022/8/26 开启离线开关时, 才使用默认口音 tts
                String ccode = code.split("-")[0].toLowerCase();
                String trueCode = HoyaUtil.getInstance().getSupportKey(ccode);
                if (trueCode != null) {
                    AiSpeechLogUtil.d(TAG, "start: Hoya 未找到 " + code + " 合成, 使用默认合成: " + trueCode);
                    srcCode.standardCode = trueCode;
                    code = trueCode;
                }
            }
        }
        ///////////// 兼容未找到具体口音时使用默认口音的 tts

        // FIXME: 2020/11/9 开启线程去做合成的耗时操作, 以免阻塞返回 合成session
        String finalText = text;
        String finalCode = code;
        new Thread(new Runnable() {
            @Override
            public void run() {
                final ByteArray data = new ByteArray();
                HoyaUtil.getInstance().synthesize(finalText, finalCode, new HoyaUtil.HoyaListener() {
                    @Override
                    public void onBuffering(byte[] output, int outputSize) {
                        data.cat(output);
                    }

                    @Override
                    public void onCompleted(String errordesc) {
                        if (listener != null) {
                            SpeechError error = null;
                            if (errordesc != null) error = new SpeechError(-1, errordesc);
                            listener.onCompleted(HoyaSynthesizer.this, data.getArray(), name, error);
                        }
                    }
                });
            }
        }).start();
    }

    @Override
    public void start(Language srcCode, String text, String voiceName) {
        this.start(srcCode, text);
    }

    /**
     * hoya 初始化, 若资源不存在, 最初会解压资源; 后续只会检测 license 文件, 不存在则下载
     */
    public static void load(Context context) {
        HoyaUtil.getInstance().loadResources(context);
    }

    public static void downloadOfflineResource() {
        HoyaUtil.getInstance().downloadOfflineZipResource();
    }

    public static void synDownloadLicenseFile(Context context) {
        HoyaUtil.getInstance().synDownloadLicenseFile(context);
    }

//    public static boolean initResources() {
//        return HoyaUtil.getInstance().initResources();
//    }
}

class HoyaUtil {
    // 原始 db 文件大小, 以供检测是否合法, 以解决解压途中被打断的情况(严格来说 md5 更好)
    private Map<String, Integer> dbSizes = new HashMap<String, Integer>() {{
        put("Ashley" 	, 5982985);
        put("Bridget"   , 5785718);
        put("Carolina"  , 3772575);
        put("Elisa" 	, 6418813);
        put("Hong" 	    , 6206040);
        put("Hyeryun"   , 6615235);
        put("Lena" 	    , 11956478);
        put("Lola" 	    , 4662133);
        put("Risa" 	    , 9622197);
        put("Roxane" 	, 2153961);
        put("SOMSI" 	, 9037639);
        put("Vera" 	    , 5393860);
    }};
    class VtengineObj {
        EngineInfo info;
        long eHandle;
        String dirName;
        String dbPath;
        File file;
        long size; // 原始 db 文件大小, 比较 db 文件是否完整(严格来说 md5 更好)
    }

    private static final String TAG = "HoyaUtil";

    private static HoyaUtil instance = null;
    //    private static Context mContext;
    //下面这三个的存储路径改到沙箱内
    public static String rootPath = null;
    public static String workingDir = null; // 必须指定此路径为/HoyaSpeech/D16, 否则会错误
    public static String LICNESE_DIRPATH = null;
    public static String LICNESE_FILENAME = "verification.txt";
    private static String cacheWorkingDir = null; // 解压缓存目录, 解压完成后移动至 workingDir
    private static String hoyaZipPath = null; // 目标大小: 42935255

    private VoiceText voicetext = null;
    private Options options = null;
    private String licenseKey = "OOHE-TU45-TVKQ-P2SQ-B83L";
    private VtLicenseSetting licenseModule = null;

    private Context mContext;
    private int ttsSpeed = 100;

    private HashMap<String, VtengineObj> engineInfos = null;
    private HashMap<String, ReentrantLock> locks = new HashMap<>();
    private Semaphore semaphore = new Semaphore(2);

    static CountDownLatch countDown = new CountDownLatch(1); // 可废弃

    public static HoyaUtil getInstance() {
        if (instance == null) {
            instance = new HoyaUtil();
        }
        return instance;
    }

    public void setTtsSpeed(int ttsSpeed) {
        this.ttsSpeed = ttsSpeed;
    }

    public void loadResources(final Context context) {
// /data/data/包名/files
//        rootPath = Environment.getExternalStorageDirectory().getAbsolutePath(); // context.getFilesDir().getAbsolutePath();
//        File f = context.getExternalFilesDir(ACTION_OPEN_DOCUMENT);
//        AiSpeechLogUtil.d(TAG, "initResources: " + f.getAbsolutePath());

        mContext = context.getApplicationContext();
        rootPath = mContext.getFilesDir().getAbsolutePath();
        workingDir = rootPath + "/HoyaSpeech/D16";
        LICNESE_DIRPATH = rootPath + "/HoyaSpeech/verify/";
        cacheWorkingDir = mContext.getCacheDir().getAbsolutePath();
        hoyaZipPath = mContext.getFilesDir().getAbsolutePath() + "/HoyaSpeech.zip";

        new Thread(new Runnable() {
            @Override
            public void run() {
                initResources();
            }
        }).start();
    }

    public static InputStream getAssetsFilePath(Context context) {
        try {
            return context.getAssets().open("HoyaSpeech.zip");
        } catch (IOException e) {
            AiSpeechLogUtil.e(TAG, "asset目录下不存在: HoyaSpeech.zip");
            return null;
        }
    }

    public static String getObbFilePath(Context context) {
        try {
            // 路径: /storage/emulated/0/Android/obb/com.translation666/main.44.com.translation666.obb
            return context.getObbDir()
                    + File.separator
                    + "main."
                    + context.getPackageManager().getPackageInfo(context.getPackageName(), 0).versionCode
                    + "."
                    + context.getPackageName()
                    + ".obb";
        } catch (PackageManager.NameNotFoundException e) {
            AiSpeechLogUtil.e(TAG, "obb目录下不存在: HoyaSpeech.zip");
            return null;
        }
    }

    private boolean licenseIsExist() {
        // 检查是否存在子文件, 以及子文件内容是否包含 licenseKey
        File f = new File(LICNESE_DIRPATH);
        if (f.exists()) { // FIXME: 2020/9/14 环境: S9 android 10, f.exists 文件夹名大写判断存在(实际是小写), 但f.listFiles() 却为 null
            File subFiles[] = f.listFiles();
            if (subFiles != null) {
                for (File txt : f.listFiles()) {
                    if (txt.getName().endsWith(".txt")) {
                        try {
                            BufferedReader reader = new BufferedReader(new FileReader(txt));
                            StringBuffer sbf = new StringBuffer();
                            String tempStr;
                            while ((tempStr = reader.readLine()) != null) {
                                sbf.append(tempStr);
                            }
                            reader.close();

                            // 包含 licenseKey 就退出
                            if (sbf.toString().contains(this.licenseKey)) {
                                AiSpeechLogUtil.d(TAG, "合法的license文件: " + txt.getName() + ", " + "license 内容: " + sbf);
                                LICNESE_FILENAME = txt.getName();
                                return true;
                            } else {
                                // 不包含 key 则删除重新下载
                                AiSpeechLogUtil.d(TAG, "不合法license文件: " + txt.getName() + ", " + "license 内容: " + sbf);
                                f.delete();
                            }
                        } catch (FileNotFoundException e) {
                            e.printStackTrace();
                        } catch (IOException e) {
                            e.printStackTrace();
                        }
                        break;
                    }
                }
            }
        }

        return false;
    }

    private void synDownloadLicenseTxt(Context context) {
        AiSpeechLogUtil.d(TAG, "synDownloadLicenseTxt: 准备下载 License(verification.txt) " + this.licenseKey);

        licenseModule = new VtLicenseSetting(context);
        if (licenseModule.getLicensed()) {
            AiSpeechLogUtil.d(TAG, "synDownloadLicenseTxt: getLicensed 已经下载过License");

            if (this.licenseIsExist()) return;
            else {
                AiSpeechLogUtil.e(TAG, "synDownloadLicenseTxt: 异常(getLicensed()为 true 但 license 文件不存在), 重置后重新下载");
                licenseModule.resetLicenseFileDownload(); //retry download license
            }
        }

        AiSpeechLogUtil.d(TAG, "synDownloadLicenseTxt: 下载 License(verification.txt) " + this.licenseKey);
        // FIXME: 2020/11/9 不宜频繁下载, 由于 hoya 后台设置每个 手机mac 只能下载若干数量的verification.txt,
        //  所以有时 vtLicenseDownload() 会不返回响应结果, 通过 resetLicenseFileDownload()重置下载
        licenseModule.vtLicenseDownload(this.licenseKey, LICNESE_DIRPATH, new LicenseDownloadListener() {
            @Override
            public void onSuccess() {
                AiSpeechLogUtil.d(TAG, "licenseModule.vtLicenseDownload() ---> onSuccess");
                printLicenseContent(); // 打印 license 内容
                countDown.countDown();
            }

            @Override
            public void onFailure(String s) {
                AiSpeechLogUtil.e(TAG, "licenseModule.vtLicenseDownload() ---> onFailure : " + s);
                countDown.countDown();
            }

            @Override
            public void onError(String s) {
                AiSpeechLogUtil.e(TAG, "licenseModule.vtLicenseDownload() ---> onError : " + s);
                countDown.countDown();
            }
        });

        try {
            AiSpeechLogUtil.e(TAG, "synDownloadLicenseTxt: 等待license下载完成");
            boolean await = countDown.await(2, TimeUnit.SECONDS);
            AiSpeechLogUtil.e(TAG, "synDownloadLicenseTxt: license已下载完成 " + await);
        } catch (InterruptedException e) {
            e.printStackTrace();
        }

        countDown = new CountDownLatch(1); // 重置
    }

    private int downloadTryCount = 0;
    private void downloadLicenseFile() {
        if (downloadTryCount >= 3) {
            AiSpeechLogUtil.e(TAG, "downloadLicenseFile: 已经尝试过下载三次 hoya licnese 文件, 不再下载");
            return;
        }
        downloadTryCount++;

        AiSpeechLogUtil.d(TAG, "downloadLicenseFile: 重新下载 license txt(verification.txt)");
        licenseModule.resetLicenseFileDownload(); //retry download license
        licenseModule.vtLicenseDownload(this.licenseKey, LICNESE_DIRPATH, new LicenseDownloadListener() {
            @Override
            public void onSuccess() {
                AiSpeechLogUtil.e(TAG, "downloadLicenseFile: licenseModule.vtLicenseDownload() ---> onSuccess");
                printLicenseContent(); // 打印 license 内容
            }

            @Override
            public void onFailure(String s) {
                AiSpeechLogUtil.e(TAG, "downloadLicenseFile:licenseModule.vtLicenseDownload() ---> onFailure : " + s);
            }

            @Override
            public void onError(String s) {
                AiSpeechLogUtil.e(TAG, "downloadLicenseFile:licenseModule.vtLicenseDownload() ---> onError : " + s);
            }
        });
    }

    private void printLicenseContent() {
        try {
            File f = new File(LICNESE_DIRPATH + LICNESE_FILENAME);
            if (f.exists()) { // FIXME: 2020/9/14 环境: S9 android 10, f.exists 文件夹名大写判断存在(实际是小写), 但f.listFiles() 却为 null
                BufferedReader reader = new BufferedReader(new FileReader(f.getAbsolutePath()));
                StringBuilder sbf = new StringBuilder();
                String tempStr;
                while ((tempStr = reader.readLine()) != null) {
                    sbf.append(tempStr);
                }
                reader.close();
                AiSpeechLogUtil.d(TAG, "hoya licnese 文件内容:  " + sbf);
            } else {
                AiSpeechLogUtil.e(TAG, "许可证不存在");
            }
        } catch (IOException e) {
            e.printStackTrace();
        }
    }

    private void deleteLicenseFile() {
        File f = new File(LICNESE_DIRPATH + LICNESE_FILENAME);
        if (f.exists()) {
            boolean success = f.delete();
            AiSpeechLogUtil.d(TAG, "hoya licnese 文件(" + f.getAbsolutePath() + ") 删除是否成功: " + success);
        }
    }

    private boolean resourceIsExsit() {
        final String vt_path = workingDir + "/Bridget"; // 检查一下其中一种 hoya 资源目录有没有
        boolean isExsit = fileIsExists(vt_path);
//        AiSpeechLogUtil.e(TAG, "resourceIsExsit: hoya离线资源是否存在: " + isExsit + " workingDir: " + workingDir);
        return isExsit;
    }

    private void unzipResource(Context context) {
        if (resourceIsExsit()) return;
        AiSpeechLogUtil.e(TAG, "unzipResource: hoya 离线文件不存在, need to unzip files ");

        // 解压
        // 目前支持 11 种
        // ru-RU fr-FR ja-JP es-ES de-DE ko-KR zh-CN it-IT pt-PT en-GB en-US
        try {
            long ts = new Date().getTime();

            InputStream in;
            if (fileIsExists(getObbFilePath(context))) {
                AiSpeechLogUtil.e(TAG, "unzipResource: 解压obb文件：" + getObbFilePath(context));
                in = new FileInputStream(getObbFilePath(context));

                //解压到内部目录
                ZipUtils.UnZipFolder(in, rootPath); // 解压后的文件夹存在则不创建(合并)
            } else if ((in = getAssetsFilePath(context)) != null) {
                AiSpeechLogUtil.e(TAG, "unzipResource: 解压asset目录下的zip文件");
                in = context.getAssets().open("HoyaSpeech.zip");

                //解压到内部目录
                ZipUtils.UnZipFolder(in, rootPath); // 解压后的文件夹存在则不创建(合并)
            } else {
                // 下载并解压
                this.downloadOfflineZipResource();
            }

            AiSpeechLogUtil.e(TAG, "unzipResource: cost: " + ((new Date().getTime()) - ts) + "ms");
        } catch (Exception e) {
            AiSpeechLogUtil.e(TAG, "unzipResource: hoya zip解压失败，" + e.getMessage());
            e.printStackTrace();
        }
    }

    public void downloadOfflineZipResource() {
        File f = new File(hoyaZipPath);
        // 文件是否合法
        boolean valid = f.exists() && f.length() == 42935255;
        if (!valid) {
            // 若是文件存在但缺损, 则先删除
            if (f.exists() && f.length() != 42935255) f.delete();
        }

        // 下载并解压
        boolean needDownload = !valid;
        new Thread(() -> {
            long ts = new Date().getTime();

            // 1.下载, 如 zip 资源存在则不下载
            String path = hoyaZipPath;
            if (needDownload) {
                String hoyaUrl = "https://cdn.timekettle.co/tts/HoyaSpeech.zip";
                path = HttpsConnection.download(hoyaUrl, hoyaZipPath);
                AiSpeechLogUtil.d(TAG, "下载结束: " + (path != null ? "成功" : "失败") + ", cost: " + ((new Date().getTime()) - ts) + "ms");
            }
            if (path != null && fileIsExists(path)) {
                AiSpeechLogUtil.d(TAG, "开始 hoya zip 文件解压: " + path);
                // 2.解压, 文件存在则解压
                // 目前支持 11 种
                // ru-RU fr-FR ja-JP es-ES de-DE ko-KR zh-CN it-IT pt-PT en-GB en-US
                try {
                    InputStream in = new FileInputStream(path);
                    ZipUtils.UnZipFolder(in, cacheWorkingDir); // 解压后的文件夹存在则不创建(合并)

                    boolean success = new File(cacheWorkingDir + "/HoyaSpeech").renameTo(new File(workingDir));
                    AiSpeechLogUtil.e(TAG, "完成 hoya zip 文件解压: " + success + ", cost: " + ((new Date().getTime()) - ts) + "ms");
                } catch (Exception e) {
                    AiSpeechLogUtil.e(TAG, "hoya zip 文件解压失败，" + e.getMessage());
                    e.printStackTrace();
                }
            } else {
                AiSpeechLogUtil.e(TAG, "下载失败 hoya资源: " + path);
            }
        }).start();
    }

    private void unzipSpecificResource(Context context, String specific) {
        try {
            InputStream in;
            if (fileIsExists(getObbFilePath(context))) {
                AiSpeechLogUtil.e(TAG, "unzipResource: 解压obb文件：" + getObbFilePath(context));
                in = new FileInputStream(getObbFilePath(context));

                ZipUtils.UnZipFolder(in, rootPath, specific); // 解压后的文件夹存在则不创建(合并)
            } else if ((in = getAssetsFilePath(context)) != null) {
                AiSpeechLogUtil.e(TAG, "unzipResource: 解压asset目录下的zip文件");
                in = context.getAssets().open("HoyaSpeech.zip");

                long ts = new Date().getTime();
                ZipUtils.UnZipFolder(in, rootPath, specific); // 解压后的文件夹存在则不创建(合并)
                AiSpeechLogUtil.e(TAG, "unzipResource: cost: " + ((new Date().getTime()) - ts) + "ms");
            } else {
                in = new FileInputStream("HoyaSpeech.zip");
                ZipUtils.UnZipFolder(in, rootPath, specific); // 解压后的文件夹存在则不创建(合并)
            }
        } catch (Exception e) {
            AiSpeechLogUtil.e(TAG, "unzipResource: hoya zip解压失败，" + e.getMessage());
            e.printStackTrace();
        }
    }

    public boolean initResources() {
        if (mContext == null) {
            AiSpeechLogUtil.e(TAG, "initResources: 异常, mContext 为空");
            return false;
        }
        // unzip Resource
        unzipResource(mContext);

        // download license txt by license key

        //因为下载license文件的时候会计费，所以这里不再下载，转到用户下载离线包的时候调用
//        synDownloadLicenseTxt(mContext);

        if (!(this.licenseIsExist() && this.resourceIsExsit())) {
            AiSpeechLogUtil.e(TAG, "initResources: license/资源 不存在, 不进行初始化");
            return false;
        }

        return true;
    }

    public boolean initResourcesAndDownload() {
        if (mContext == null) {
            AiSpeechLogUtil.e(TAG, "initResources: 异常, mContext 为空");
            return false;
        }
        // unzip Resource
        unzipResource(mContext);

        // download license txt by license key
        synDownloadLicenseTxt(mContext);

        if (!(this.licenseIsExist() && this.resourceIsExsit())) {
            AiSpeechLogUtil.e(TAG, "initResources: license/资源 不存在, 不进行初始化");
            return false;
        }

        return true;
    }

    public void synDownloadLicenseFile(Context context) {
        synDownloadLicenseTxt(context);
    }

    public boolean initEngine() {
        if (voicetext != null) {
            AiSpeechLogUtil.e(TAG, "initEngine: 已进行初始化, 不在重复初始化");
            return true;
        }

        boolean success = initResourcesAndDownload();
        if (!success) return false;

        // FIXME: 2020-05-28 检查 LICNESE_DIRPATH 是否存在
        AiSpeechLogUtil.e(TAG, "initEngine: Hoya 初始化开始");
        voicetext = new VoiceText();
        if (licenseModule == null) {
            voicetext.vtapiSetLicenseFolder(LICNESE_DIRPATH);
        } else {
            // License Module
            voicetext.vtapiSetLicenseFolder(LICNESE_DIRPATH);
            voicetext.vtapiUsingLicensekey(true);
        }
        voicetext.vtapiSetCallbackForLogFilter(4); // 日志等级

        try {
            AiSpeechLogUtil.e(TAG, "initEngine: vtapiInit: " + workingDir);
            voicetext.vtapiInit(workingDir);

            if (engineInfos == null) {
                engineInfos = new HashMap<>();
            }
            for (Map.Entry<String, EngineInfo> e : voicetext.vtapiGetEngineInfo().entrySet()) {
                String dirName = e.getValue().getDb_path().replace("./", "");
                String dbPath = workingDir + "/" + dirName + "/tts_single_db.vtdb2";
                File file = new File(dbPath);
                AiSpeechLogUtil.e(TAG, "initEngine: hoya 支持: " + e.getValue().getIsoCode() + " dbPath:" + dbPath + " " + file.exists());

                long handle = voicetext.vtapiCreateHandle();
                VtengineObj obj = new VtengineObj();
                obj.info = e.getValue();
                obj.eHandle = handle;
                obj.dirName = dirName;
                obj.dbPath = dbPath;
                obj.file = file;
                obj.size = file.exists() ? dbSizes.get(dirName) : 0;
                engineInfos.put(e.getValue().getIsoCode(), obj);

                this.locks.put(e.getValue().getIsoCode(), new ReentrantLock());
            }

            options = new Options();
            options.setPitch(100);
            options.setSpeed(ttsSpeed);
            options.setVolume(100);

            AiSpeechLogUtil.e(TAG, "initEngine: Hoya 初始化完成");

        } catch (Exception e) {
            e.printStackTrace();
            return false;
        }

        return true;
    }

    public static boolean fileIsExists(String strFile) {
        try {
            File f = new File(strFile);
            if (!f.exists()) {
                return false;
            }

        } catch (Exception e) {
            return false;
        }

        return true;
    }


    public boolean isSupportKey(String key) {
        if (engineInfos == null) {
            AiSpeechLogUtil.e(TAG, "isSupportKey: engineInfos is null ");
            return false;
        }

        VtengineObj obj = engineInfos.get(key);
        if (obj == null || obj.info == null) {
            AiSpeechLogUtil.e(TAG, "isSupportKey: 未找到支持 " + key);
            return false;
        }
        return true;
    }

    public String getSupportKey(String ccode) {
        if (engineInfos == null) {
            AiSpeechLogUtil.e(TAG, "getSupportKey: engineInfos is null ");
            return null;
        }

        String trueCode = null;
        for (String key : engineInfos.keySet()) {
            if (key.equals("en-GB")) { // 只有英语有英式美式, 直接跳过英式, 暂不做配置
                continue;
            }
            if (key.toLowerCase().startsWith(ccode.toLowerCase())) {
                AiSpeechLogUtil.e(TAG, "getSupportKey: 找到 hoya 支持语种: " + ccode);
                trueCode = key;
                break;
            }
        }

        if (trueCode == null) {
            AiSpeechLogUtil.e(TAG, "getSupportKey: 未找到 hoya 支持语种: " + ccode);
        }
        return trueCode;
    }

    public void synthesize(final String text, final String langCode, final HoyaListener listener) {
        if (!this.initEngine()) {
            listener.onCompleted("初始化失败");
            return;
        }

        try {
            semaphore.acquire();
        } catch (InterruptedException e) {
            e.printStackTrace();
        }
        Lock lock = this.locks.get(langCode);
        lock.lock();

//        synchronized (lock) {
        VtengineObj obj = engineInfos.get(langCode);
        if (obj == null || obj.info == null) {
            listener.onCompleted("未找到相关引擎");
            // 异常流程退出时释放锁
            lock.unlock();
            semaphore.release();
            return;
        }

        EngineInfo engineInfo = obj.info;
        long handle = obj.eHandle;

        try {
            // 检测许可证是否合法
            int ret = voicetext.vtapiCheckLicenseFile(engineInfo, LICNESE_DIRPATH + LICNESE_FILENAME);
            AiSpeechLogUtil.e(TAG, "synthesize: 许可证是否合法: " + (ret == Constants.Errors.VTAPI_SUCCESS));
            if (ret != Constants.Errors.VTAPI_SUCCESS) {
                printLicenseContent(); // 打印内容
                this.downloadLicenseFile();
                // 异常流程退出时释放锁
                lock.unlock();
                semaphore.release();
                listener.onCompleted(langCode + " 许可证不否合法");
                return;
            }

            final String speaker = engineInfo.getSpeaker();
            AiSpeechLogUtil.e(TAG, "synthesize 合成开始: " + langCode + " speaker:" + speaker);

            if (!obj.file.exists()) {
                AiSpeechLogUtil.e(TAG, "synthesize db 文件 : " + obj.dbPath + " 不存在, 先解压文件");
                unzipSpecificResource(mContext, obj.dirName);

                // 更新文件大小
                obj.size = obj.file.length();
            } else {
                if (obj.file.length() != obj.size) {
                    AiSpeechLogUtil.e(TAG, "synthesize db 文件 : " + obj.dbPath + " 异常, 先解压文件");
                    unzipSpecificResource(mContext, obj.dirName);

                    // 更新文件大小
                    obj.size = obj.file.length();
                }
            }
            String dstText = text.replace(".", ",");
            dstText = dstText.replace("。", "，");
            voicetext.vtapiTextToBuffer(handle, dstText, false, 0, engineInfo.getSpeaker(), engineInfo.getSampling(), engineInfo.getType(), options, Constants.OutputFormat.FORMAT_16PCM, new VoiceTextListener() {
                @Override
                public void onReadBuffer(byte[] output, int outputSize) {
                    if (outputSize > 0) {
                        listener.onBuffering(output, outputSize);
                    } else {
                        AiSpeechLogUtil.e(TAG, "离线合成结束: " + langCode);
                        listener.onCompleted(null);
                    }
                }

                @Override
                public void onReadBufferWithWordInfo(byte[] output, int outputSize, List<SyncWordInfo> wordInfo) {
                    AiSpeechLogUtil.e(TAG, "onReadBufferWithWordInfo: ");
                }

                @Override
                public void onReadBufferWithMarkInfo(byte[] output, int outputSize, List<SyncMarkInfo> markInfo) {
                    AiSpeechLogUtil.e(TAG, "onReadBufferWithMarkInfo: ");
                }

                @Override
                public void onError(String reason) {
                    AiSpeechLogUtil.e(TAG, "离线合成结束: " + langCode + " error: " + reason + "(-133/-137为 db 加载错误/资源不存在等)");
                    AiSpeechLogUtil.e(TAG, "遇到 license 合法但使用却异常时, 可能 db 文件异常/不存在, 重新解压 " + obj.dirName + "(" + obj.info.getIsoCode());
                    unzipSpecificResource(mContext, obj.dirName);
                    listener.onCompleted(reason);
                }
            });
        } catch (Exception e) {
            e.printStackTrace();
        }
//    }

        semaphore.release();
        lock.unlock();
    }

    public interface HoyaListener {
        public void onBuffering(byte[] output, int outputSize);

        public void onCompleted(String error);
    }
}
