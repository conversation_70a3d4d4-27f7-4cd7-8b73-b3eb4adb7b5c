package co.timekettle.speech;

import android.content.Context;
import android.util.Log;

import java.util.HashMap;
import java.util.function.Function;

import co.timekettle.speech.speaker.AudioSpeakerBase;
import co.timekettle.speech.speaker.BleAudioSpeaker;
import co.timekettle.speech.speaker.HeadsetAudioSpeaker;
import co.timekettle.speech.speaker.MicAudioSpeaker;
import co.timekettle.speech.speaker.T1PhoneAudioSpeaker;
import co.timekettle.speech.utils.AiSpeechLogUtil;

public class SpeakManager {

    private static final String TAG = "SpeakManager";

    private static SpeakManager instance = null;
    private Listener speakListener = null;
    private final HashMap<String, AudioSpeakerBase> speakerMap = new HashMap<>();

    private final HashMap<String, Function> scallbacks = new HashMap();
    private final HashMap<String, Function> ecallbacks = new HashMap();

    private final AudioSpeakerBase.Listener speakerBaseListener = new AudioSpeakerBase.Listener() {
        @Override
        public void onSpeakStart(AudioSpeakerBase.Task<Long> playTask) {
            SpeechTask speechTask = (SpeechTask) playTask.userData;

            float duration = playTask.sound != null ? playTask.sound.length / 2.0f / 16000.0f : -1;
            AiSpeechLogUtil.d(TAG, "onSpeakStart: " + playTask.moduleName + " chkey:" + playTask.chkey + " [" + playTask.id + "] 播放时长: " + duration);
            String tag = genKeyOfCallback(playTask.hashCode(), true);
            Function callback = scallbacks.get(tag);
            if (callback != null) {
                callback.apply(speechTask);
                scallbacks.remove(tag);
                return;
            }
            if (speakListener != null) {
                speakListener.onSpeakStart(speechTask, null);
            }
        }

        @Override
        public void onSpeakEnd(AudioSpeakerBase.Task<Long> playTask) {
            SpeechTask speechTask = (SpeechTask) playTask.userData;

            float duration = playTask.sound != null ? playTask.sound.length / 2.0f / 16000.0f : -1;
            AiSpeechLogUtil.d(TAG, "onSpeakEnd: " + playTask.moduleName + " chkey:" + playTask.chkey + " [" + playTask.id + "] 播放时长: " + duration);
            String tag = genKeyOfCallback(playTask.hashCode(), false);
            Function callback = ecallbacks.get(tag);
            if (callback != null) {
                callback.apply(speechTask);
                ecallbacks.remove(tag);
                return;
            }

            if (speakListener != null) {
                speakListener.onSpeakEnd(speechTask, null);
            }
        }
    };

    public static SpeakManager shareInstance() {
        if (instance == null) {
            instance = new SpeakManager();
        }
        return instance;
    }

    // FIXME: 2023/6/10 后续通过外部配置添加
    public void createSpeakers(Context context) {
        if (speakerMap.size() > 0) {
            destroySpeaker();
        }

        speakerMap.put(ISpeechConstant.SPEAKER.PHONE.toString(), new MicAudioSpeaker(ISpeechConstant.SPEAKER.PHONE.toString(), context));
        speakerMap.put(ISpeechConstant.SPEAKER.BLE.toString(), new BleAudioSpeaker(ISpeechConstant.SPEAKER.BLE.toString(), context));
        speakerMap.put(ISpeechConstant.SPEAKER.HEADSET.toString(), new HeadsetAudioSpeaker(ISpeechConstant.SPEAKER.HEADSET.toString(), context));
        speakerMap.put(ISpeechConstant.SPEAKER.T1PHONE.toString(), new T1PhoneAudioSpeaker(ISpeechConstant.SPEAKER.T1PHONE.toString(), context));

        for (AudioSpeakerBase speaker : speakerMap.values()) {
            speaker.setListener(speakerBaseListener);
        }
    }

    public void addSpeaker(AudioSpeakerBase speaker) {
        speakerMap.put(speaker.getName(), speaker);
        speaker.setListener(speakerBaseListener);
    }


    /***
     * 停止播放器工作
     */
    public void destroySpeaker() {
        for (AudioSpeakerBase speaker : speakerMap.values()) {
            speaker.destroy();
        }
    }

    String genKeyOfCallback(long hashcode, boolean isStartCallback) {
        return (isStartCallback ? "s" : "e") + hashcode;
    }

    /**
     * 播放任务
     * @param task 语音播放任务
     * @param sCallback 开始播放的回调
     * @param eCallback 结束播放的回调
     * @return 任务 id
     */
    public long play(SpeechTask<byte[], Object> task, Function<SpeechTask<byte[], Object>, Object> sCallback, Function<SpeechTask<byte[], Object>, Object> eCallback) {
        long session = task.session; // 播放是串行的可以使用会话(识别)的 session
        byte[] sound = task.request.data;
        final String chkey = task.chkey;
        final String speakerType = task.request.module;
        final Object extData = task.request.extDevice;

        AudioSpeakerBase.Task<Long> playTask = new AudioSpeakerBase.Task<>(chkey, session, sound, extData);
        playTask.userData = task;
        scallbacks.put(genKeyOfCallback(playTask.hashCode(), true), sCallback);
        ecallbacks.put(genKeyOfCallback(playTask.hashCode(), false), eCallback);

        AudioSpeakerBase speaker = null;
        if (speakerMap.containsKey(speakerType)) {
            speaker = speakerMap.get(speakerType);
        }
        if (speaker == null) {
            task.response.error = new SpeechError(-1, "参数错误, 找不到speaker");
            if (eCallback != null) eCallback.apply(task);
            return session;
        }
        task.response.engine = speaker.getType();

        if (sound == null || sound.length == 0) {
            AiSpeechLogUtil.e(TAG, "play: error 播放错误, sound 不存在");
            if (eCallback != null) {
                eCallback.apply(task);
            }
            return session;
        }

        AiSpeechLogUtil.d(TAG, chkey + "通道任务: [" + session + "]" + speakerType + " 播放: " + sound.length + " except: " + extData);
        speaker.play(playTask);
        return session;
    }

    /**
     * 停止全部播放器全部播放任务
     */
    public void stop() {
        for (AudioSpeakerBase speaker : speakerMap.values()) {
            speaker.stop();
        }
    }

    /**
     * 停止全部播放器某个任务
     * @param workerId 任务 id
     */
    public void stopWorker(long workerId) {
        for (AudioSpeakerBase speaker : speakerMap.values()) {
            speaker.stopWorker(workerId);
        }
    }

    /**
     * 停止某播放器某个任务
     * @param speakerType 播放器名称
     * @param workerId 任务 id
     */
    public void stopWorker(String speakerType, long workerId) {
        if (speakerMap.containsKey(speakerType)) {
            speakerMap.get(speakerType).stopWorker(workerId);
        }
    }

    /**
     * 倍速播放, 支持 0.5 - 3.0
     * @param speakerType 扬声器类型
     * @param tempo 倍速, 值区间为 [0.5, 3.0]
     */
    public void update(String speakerType, float tempo) {
        AiSpeechLogUtil.d(TAG, speakerType + " 更新倍速为: " + tempo);
        if (speakerMap.containsKey(speakerType)) {
            speakerMap.get(speakerType).tempo = tempo;
        }
    }

    /**
     * 重置所有 speaker 参数
     */
    public void reset() {
        for (AudioSpeakerBase speaker : speakerMap.values()) {
            speaker.tempo = 1.0f;
        }
    }

    public void setListener(Listener listener) {
        speakListener = listener;
    }

    public interface Listener {
        void onSpeakStart(SpeechTask task, SpeechError error);

        void onSpeakEnd(SpeechTask task, SpeechError error);

        void onPlayResult(SpeechTask task, SpeechError error);
    }
}
