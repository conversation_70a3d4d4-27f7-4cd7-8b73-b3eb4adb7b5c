package co.timekettle.speech;

import android.content.Context;

import co.timekettle.speech.recorder.AudioRecorderBase;
import co.timekettle.speech.recorder.BleAudioRecorder;
import co.timekettle.speech.recorder.HeadsetRecorder;
import co.timekettle.speech.recorder.MicAudioRecorder;
import co.timekettle.speech.recorder.T1MicAudioRecorder;
import co.timekettle.speech.recorder.T1AecAudioRecorder;
import co.timekettle.speech.save.RecordClient;
import co.timekettle.speech.save.RecordWaveTextureView;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;

/**
 * 录音器管理类, 启动/停止录音器, 更新录音器参数等
 */
public class RecordManager {
    private static final String TAG = "RecordManager";
    private static RecordManager instance = null;
    private HashMap<String, AudioRecorderBase> recorderMap = new HashMap<>();
    public ServiceQualityListener sqListener = null;

    public static RecordManager shareInstance() {
        if (instance == null) {
            instance = new RecordManager();
        }
        return instance;
    }

    public void setContext(Context context) {

        if (!recorderMap.containsKey(ISpeechConstant.RECORDER.PHONE.toString())) {
            MicAudioRecorder micAudioRecorder = new MicAudioRecorder(context);
            addRecorder(micAudioRecorder);
        }
        if (!recorderMap.containsKey(ISpeechConstant.RECORDER.BLE.toString())) {
            BleAudioRecorder bleAudioRecorder = new BleAudioRecorder(context);
            addRecorder(bleAudioRecorder);
        }
        if (!recorderMap.containsKey(ISpeechConstant.RECORDER.HEADSET.toString())) {
            HeadsetRecorder headsetRecorder = new HeadsetRecorder(context);
            addRecorder(headsetRecorder);
        }
        if (!recorderMap.containsKey(ISpeechConstant.RECORDER.USB.toString())) {
            UsbAudioRecorder usbRecorder = new UsbAudioRecorder(context);
            addRecorder(usbRecorder);
        }
        if (!recorderMap.containsKey(ISpeechConstant.RECORDER.T1PHONE.toString())) {
            T1MicAudioRecorder T1PhoneRecorder = new T1MicAudioRecorder(context);
            addRecorder(T1PhoneRecorder);
        }
        if (!recorderMap.containsKey(ISpeechConstant.RECORDER.T1PHONEAEC.toString())) {
            T1AecAudioRecorder t1AecAudioRecorder = new T1AecAudioRecorder(context);
            addRecorder(t1AecAudioRecorder);
        }
    }

    public boolean isExist(String name) {
        return recorderMap.containsKey(name);
    }

    /**
     * 获得当前的录音器
     * @return
     */
    public List<String> getSupportRecorders() {
        return new ArrayList<>(recorderMap.keySet());
    }

    /**
     * 告知录音器播放正开始
     */
    public void doSpeakStart() {
        for (AudioRecorderBase recorder : recorderMap.values()) {
            recorder.doSpeakStart();
        }
    }

    /**
     * 告知录音器播放已结束
     */
    public void doSpeakEnd() {
        for (AudioRecorderBase recorder : recorderMap.values()) {
            recorder.doSpeakEnd();
        }
    }

    /**
     * 添加录音器
     */
    public void addRecorder(AudioRecorderBase recorder) {
        recorderMap.put(recorder.getName(), recorder);
    }

    public void setListener(String name, AudioRecorderBase.Listener listener) {
        if (recorderMap.containsKey(name)) {
            recorderMap.get(name).setListener(listener);
        }
    }

    /**
     * 启动录音器
     * @param name 录音器名称, 见 {@link ISpeechConstant.RECORDER}
     * @param options 见 {@link AudioRecordOptions}
     * @deprecated Call {@link #start2(String, HashMap, AudioRecorderBase.Listener)} )} instead.
     */
    @Deprecated // "通过 start2"
    public void start(String name, HashMap<String, Object> options) {
        if (recorderMap.containsKey(name)) {
            recorderMap.get(name).start(options);
        }

        if (recorderMap.containsKey(name)) {
            recorderMap.get(name).setListener(new AudioRecorderBase.Listener() {
                @Override
                public void onSuccess() {

                }

                @Override
                public void onRecording(String key, short[] data, boolean needVad, Boolean isVoice) {
                    if (needVad) {
                        AiSpeechManager.shareInstance().writeAudioToChannel(key, data);
                    } else {
                        AiSpeechManager.shareInstance().writeAudioToChannel(key, data, isVoice);
                    }
                }

                @Override
                public void onError(String reason) {

                }
            });
        }
    }

    /**
     * 启动录音器
     * @param name 录音器名称, 见 {@link ISpeechConstant.RECORDER}
     * @param options 见 {@link AudioRecordOptions}
     * @param listener 录音数据监听器
     */
    public void start2(String name, Map<String, Object> options, AudioRecorderBase.Listener listener) {
        if (recorderMap.containsKey(name)) {
            recorderMap.get(name).start(options);
            recorderMap.get(name).setListener(listener);
        }
    }


    public void startDual(String name, Map<String, Object> options, AudioRecorderBase.Listener callbackListener) {
        if (recorderMap.containsKey(name)) {
            recorderMap.get(name).start(options);
        }

        if (recorderMap.containsKey(name)) {
            recorderMap.get(name).setListener(new AudioRecorderBase.Listener() {
                @Override
                public void onSuccess() {

                }

                @Override
                public void onRecording(String key, short[] data, boolean needVad, Boolean isVoice) {

                }

                @Override
                public void onRecordingDual(String chkey0, short[] sound0, String chkey1, short[] sound1) {
                    AudioRecorderBase.Listener.super.onRecordingDual(chkey0, sound0, chkey1, sound1);
                    if(callbackListener != null)
                        callbackListener.onRecordingDual(chkey0, sound0, chkey1, sound1);
                }


                @Override
                public void onError(String reason) {

                }
            });
        }

    }

    public void setOptions(String name, HashMap<String, Object> options) {
        if (recorderMap.containsKey(name)) {
            recorderMap.get(name).setOptions(options);
        }
    }

    /**
     * 停止正在工作的录音器
     * @deprecated Call {@link #stop2(String)} )} instead.
     */
    @Deprecated
    public void stop() {
        for (AudioRecorderBase recorder : recorderMap.values()) {
            if (recorder.isWorking()) {
                recorder.stop();
            }
        }
    }

    /**
     * 停止某个录音器
     * @param name 录音器名称
     * @deprecated Call {@link #stop2(String)} )} instead.
     */
    @Deprecated
    public void stop(String name) {
        if (recorderMap.containsKey(name)) {
            recorderMap.get(name).stop();
        }
    }

    /**
     * 只停止录音器, 不清空通道
     * @param name 录音器名称
     */
    public void stop2(String name) {
        if (recorderMap.containsKey(name)) {
            recorderMap.get(name).stop2();
        }
    }

    /**
     * 只停止录音器, 不清空通道
     */
    public void stop2() {
        for (AudioRecorderBase recorder : recorderMap.values()) {
            if (recorder.isWorking()) {
                recorder.stop2();
            }
        }
    }

    /**
     * 暂停录音, 目前是丢弃数据流而不是暂停/停止录音器
     */
    public void pauseRecorder(String name) {
        Objects.requireNonNull(recorderMap.get(name)).setPaused(true);
    }

    /**
     * 恢复录音, 目前是恢复数据流而不是重启录音器
     */
    public void resumeRecorder(String name) {
        Objects.requireNonNull(recorderMap.get(name)).setPaused(false);
    }

    /**
     * 目前依赖数据流, 以免 vad 等缓存问题, 所以暂停录音置为了静音
     */
    public void muteAllRecorder() {
        for (AudioRecorderBase recorder : recorderMap.values()) {
            recorder.setIsMute(true);
        }
    }

    /**
     * 目前依赖数据流, 以免 vad 等缓存问题, 所以恢复录音置为了非静音
     */
    public void unmuteAllRecorder() {
        for (AudioRecorderBase recorder : recorderMap.values()) {
            recorder.setIsMute(false);
        }
    }

    /**
     * 向 录音器 添加通道
     * @param recoder  录音器的名称
     * @param options 通道选项
     * @deprecated Call {@link co.timekettle.speech.AiSpeechManager#addChannel(Map)} instead.
     */
    @Deprecated
    public void addChannel(String recoder, HashMap<String, Object> options) {
        if (recorderMap.containsKey(recoder)) {
            String chkey = AiSpeechManager.shareInstance().addChannel(options);
            this.addChannel2(recoder, chkey);
        }
    }

    /**
     * 向 录音器 添加通道
     * @param recoder  录音器的名称
     * @param chkey 通道名
     * @deprecated Call {@link co.timekettle.speech.AiSpeechManager#addChannel(Map)} instead.
     */
    @Deprecated
    public void addChannel2(String recoder, String chkey) {
        if (recorderMap.containsKey(recoder)) {
            recorderMap.get(recoder).addChannel2(chkey);
        }
    }


    /**
     * 设置通道, 并会更新 AiSpeechManager 通道(后续需要去除, 此处只绑定通道)
     * @param recoder
     * @param options
     * @deprecated Call {@link co.timekettle.speech.AiSpeechManager#updateChannel(Map)} instead.
     */
    @Deprecated
    public void setChannel(String recoder, final HashMap<String, Object> options) {
        if (recorderMap.containsKey(recoder)) {
            AiSpeechManager.shareInstance().updateChannel(options);
        }
    }

    /**
     * 清空指定录音器所有绑定的通道信息等
     * @param recoder 录音器名称
     */
    public void clear(String recoder) {
        if (recorderMap.containsKey(recoder)) {
            recorderMap.get(recoder).removeAllChannel();
        }
    }

    /**
     * 清空绑定的通道信息等
     */
    public void clear() {
        for (String name : recorderMap.keySet()) {
            recorderMap.get(name).removeAllChannel();
        }
    }

    public void openSaveVadData(String key) {
        for (String name : recorderMap.keySet()) {
            recorderMap.get(name).openSaveVadData(key);
        }
    }

    public void saveVadData(String key, byte[] shorts) {
        for (String name : recorderMap.keySet()) {
            recorderMap.get(name).saveVadData(key, shorts);
        }
    }

    public void stopSaveVadData() {
        for (String name : recorderMap.keySet()) {
            recorderMap.get(name).stopSaveVadData();
        }
    }

    /**
     * 获得波形图
     */
    public RecordWaveTextureView getWaveView() {
        return RecordClient.getInstance().getWaveView();
    }

    /**
     * 获得文件路径, 不检查是否存在, 可能已被外部删除
     */
    public String getRecordFilePath(String fileName) {
        return RecordClient.getInstance().getRecordFilePath(fileName);
    }

    public void setSqListener(ServiceQualityListener sqListener) {
        this.sqListener = sqListener;
    }

    public interface ServiceQualityListener {
        void onServiceQualityEvent(final ServiceQualityResult res);
    }
}
