package co.timekettle.speech.recognizer.aeesdk;

import static co.timekettle.speech.aeesdk.AeeSdkUtil.ASR_ABILITY;

import android.content.Context;
import android.content.pm.ApplicationInfo;

import com.iflytek.aikit.core.AiHandle;
import com.iflytek.aikit.core.AiHelper;
import com.iflytek.aikit.core.AiInput;
import com.iflytek.aikit.core.AiResponse;
import com.iflytek.aikit.core.AiResponseListener;
import com.iflytek.aikit.core.DataStatus;

import org.json.JSONArray;
import org.json.JSONObject;

import java.io.UnsupportedEncodingException;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.concurrent.LinkedBlockingQueue;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

import co.timekettle.speech.aeesdk.AeeSdkUtil;
import co.timekettle.speech.translator.IflytekOfflineTranslator;
import co.timekettle.speech.translator.NiuOfflineTranslator;
import co.timekettle.speech.translator.TranslatorBase;
import co.timekettle.speech.utils.AiSpeechLogUtil;

/**
 * 学之友的转写工具类
 */
public class AeeOfflineRecognizeUtil {
    private final static String TAG = "AeeOfflineUtil";
    private final Map<String, AsrParam> supports = new HashMap<>();
    private AsrParam lastEsrParam = null; // 由于 esr 存在切换, 故保存最后一次的配置
    private String srcCode;
    private IflyOfflineListener listener;
    private boolean ready = false;  // 是否资源已加载完毕
    private boolean isFirstData = true;
    private boolean isFinalData = false;
    private String resultText = "";
    private Context context;
    private boolean isDebug = false;

    // 保证在线任务顺序执行, 并且任务代码块顺序必须是 group(0)[start(0) write...write stop(0)] group(1)[start(1) write...write stop(1)]
    protected LinkedBlockingQueue<LinkedBlockingQueue<CodeBlock>> taskGroups = new LinkedBlockingQueue<>();
    // 持有任务组, 以便查询
    private final LinkedHashMap<Long, LinkedBlockingQueue<CodeBlock>> pandingBlocks = new LinkedHashMap<>();
    private Thread taskloop;

    private boolean isLoad = false;  // 引擎是否初始化了
    AiHandle aiHandle; // 每次开启一次识别会话，都会有一个新的handler

    private static AeeOfflineRecognizeUtil instance;

    public static AeeOfflineRecognizeUtil getInstance() {
        if (instance == null) {
            instance = new AeeOfflineRecognizeUtil();
        }
        return instance;
    }

    public AeeOfflineRecognizeUtil() {
        if (taskloop == null) {
            taskloop = new Thread(() -> {
                while (true) {
                    try {
                        LinkedBlockingQueue<CodeBlock> blocks = taskGroups.take();
                        while (true) {
                            CodeBlock block = blocks.take();
                            block.block.run();
//                            AiSpeechLogUtil.d(TAG, "代码块执行完 " + block.desc);
                            // 如果 block 是 结束的代码块, 则退出
                            if (block.isLast) {
                                break;
                            }
                        }
                    } catch (InterruptedException e) {
                        e.printStackTrace();
                    }
                }
            });
            taskloop.setName("Tmk-" + TAG);
            taskloop.start();
        }
    }


    private final AiResponseListener aiResponseListener = new AiResponseListener() {
        @Override
        public void onResult(String ability, int handleID, List<AiResponse> outputData, Object o) {
//            AiSpeechLogUtil.i(TAG, "onResult:" + ability + ",handleID:" + handleID);
            if (null == outputData || outputData.isEmpty()) return;

            for (int i = 0; i < outputData.size(); i++) {
                String key = outputData.get(i).getKey();
                byte[] valuebts = outputData.get(i).getValue();
//                AiSpeechLogUtil.d(TAG, "onResult key:" + outputData.get(i).getKey() + "  " + getEdgResult(valuebts));
                if (Objects.equals(key, "plain")) {  // 也可以是readable，最后实测plain效果比较好
                    try {
                        String ret = getEdgResult(valuebts);
//                        AiSpeechLogUtil.e(TAG, "有结果了原始>>>" + key + ": " + ret);
                        String result = parseJsonResult(ret);
                        AiSpeechLogUtil.e(TAG, "有结果了解析>>>" + key + ": " + "["+result+"]");
                        if (!result.trim().isEmpty() && !isFinalData) pollOfflineResult(result, false);
                    } catch (Exception e) {
                        e.printStackTrace();
                    }
                }
            }
        }

        @Override
        public void onEvent(String ability, int handleID, int event, List<AiResponse> eventData, Object usrContext) {
            AiSpeechLogUtil.d(TAG, "onEvent:" + ability + ",event:" + event);
        }

        @Override
        public void onError(String ability, int handleID, int err, String msg,
                            Object usrContext) {
            AiSpeechLogUtil.e(TAG, "错误通知，能力执行终止,Ability " + ability + " ERROR::" + msg + ",err code:" + err);
        }
    };


    private void initSdk(Context context, String workDir) {
        AeeSdkUtil.getInstance().removeAeeSdkListener(ASR_ABILITY, aiResponseListener);
        AeeSdkUtil.getInstance().addAeeSdkListener(ASR_ABILITY, aiResponseListener);

        if (AeeSdkUtil.getInstance().isSdkInit) {
            isLoad = true;
            return;
        }
        AiSpeechLogUtil.d(TAG, "AEE SDK 初始化，workDir" + workDir);
        AeeSdkUtil.getInstance().initSdk(context, workDir, (errType, i) -> {
            switch (errType) {
                case AUTH:
                    AiSpeechLogUtil.e(TAG, "AEE SDK状态更新，授权结果码：" + i);
                    if (i == 0) isLoad = true;
                    break;
                case HTTP:
                    AiSpeechLogUtil.e(TAG, "AEE SDK状态更新，HTTP code：" + i);
                    break;
                default:
                    AiSpeechLogUtil.e(TAG, "AEE SDK状态更新，其他错误，code：" + i);
            }
        });


    }


    public void initOffline2(Context context, String resPath, Map<String, String> customResPaths) {

        this.context = context;
        addSupport("zh", AsrParam.Type.edge, "cnen", resPath, customResPaths, 0);
        addSupport("en", AsrParam.Type.edge, "cnen", resPath, customResPaths, 0);
        addSupport("ja", AsrParam.Type.esr, "ja", resPath, customResPaths, 20);
        addSupport("fr", AsrParam.Type.esr, "fr", resPath, customResPaths, 23);
        addSupport("es", AsrParam.Type.esr, "es", resPath, customResPaths, 24);
        addSupport("ru", AsrParam.Type.esr, "ru", resPath, customResPaths, 22);
        addSupport("ko", AsrParam.Type.esr, "ko", resPath, customResPaths, 21);
        addSupport("de", AsrParam.Type.esr, "de", resPath, customResPaths, 25);

        setDebugFlag(context);
    }


    public boolean tryAuth(Context context, String code) {
        if (this.isSupport(code, null)) { // 应用首次使用时
            String key = code.split("-")[0];
            AsrParam param = supports.get(key);
            if (param == null) {
                AiSpeechLogUtil.d(TAG, "tryFetchAuth: 不支持此语种: " + code);
                return false;
            }

            // 正在使用的离线识别
            if (lastEsrParam != null) {
                AiSpeechLogUtil.d(TAG, "tryFetchAuth: 有在使用其他语种, 已鉴权过: " + code);
                return true;
            }
            boolean ret = AeeSdkUtil.getInstance().tryAuth(context, param.workDir);
            AiSpeechLogUtil.d(TAG, "tryFetchAuth: 学之友离线鉴权结果ret: " + ret);
            return ret;
        }
        AiSpeechLogUtil.d(TAG, "tryFetchAuth: 不通过，语种不支持：" + code);
        return false;
    }

    private void addSupport(String code, AsrParam.Type type, String resName, String workDir, Map<String, String> customResPaths, int aeeCode) {
        // 有自定义路径则使用自定义路径
        if (customResPaths != null && customResPaths.containsKey(code)) {
            workDir = customResPaths.get(code);
        }
        this.supports.put(code, new AsrParam(code, type, resName, workDir, aeeCode));
    }

    public boolean isSupport(String srcCode, String dstCode) {
        return this.supports.containsKey(srcCode.split("-")[0]);
    }

    public synchronized void start(String srcCode, long session, IflyOfflineListener listener) {
        try {
//            AiSpeechLogUtil.d(TAG, "start: 等待上一离线识别任务完成");
            if (pandingBlocks.containsKey(session)) {
                AiSpeechLogUtil.d(TAG, "start: 已存在当前离线识别任务 " + session);
//                    ConcurrentLinkedDeque<CodeBlock> blocks = pandingBlocks.get(session);
//                    blocks.add();
            } else {
                LinkedBlockingQueue<CodeBlock> blocks = new LinkedBlockingQueue<>();
                blocks.add(new CodeBlock(false, "start-" + session, () -> {
                    this.srcCode = srcCode;
                    this.listener = listener;

                    String key = srcCode.split("-")[0];
                    AsrParam param = supports.get(key);

                    this.startAsrInstance(param);
                }));
                pandingBlocks.put(session, blocks);
                taskGroups.put(blocks);
                AiSpeechLogUtil.d(TAG, "start: 添加当前离线识别任务组: " + taskGroups.size());
            }

        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    public synchronized void stop(long session) {
        LinkedBlockingQueue<CodeBlock> blocks = pandingBlocks.get(session);
        if (blocks != null) {
            blocks.add(new CodeBlock(true, "stop-" + session, () -> {
                this.stopAsrInstance();
            }));
        }
        AiSpeechLogUtil.d(TAG, "stop: 当前离线识别任务完成 " + session);
    }

    public synchronized void writeAudio(byte[] data, long session) {
//        AiSpeechLogUtil.d(TAG, "writeAudio  session:" + session + " data.length:" + data.length);
        LinkedBlockingQueue<CodeBlock> blocks = pandingBlocks.get(session);
        if (blocks != null) {
            blocks.add(new CodeBlock(false, "writeAudio-" + session, () -> {
                this.write(data);
            }));
        }


    }

    private boolean write(byte[] data) {
        if (!isLoad){
            AiSpeechLogUtil.e(TAG, "writeAudio: 引擎未初始化");
            return false;
        }
        if (lastEsrParam == null) {
            AiSpeechLogUtil.e(TAG, "writeAudio: 错误，当前 curParam 为空");
            return false;
        }
        if (!ready) {
            AiSpeechLogUtil.d(TAG, "writeAudio: " + lastEsrParam.getCode() + " ready:" + ready);
            return false;
        }
        if (data == null) {
            AiSpeechLogUtil.d(TAG, "writeAudio: 错误，数据为空");
            return false;
        }

        DataStatus status = DataStatus.BEGIN;
        if (this.isFirstData) {
            status = DataStatus.BEGIN;
            this.isFirstData = false;
        } else {
            status = DataStatus.CONTINUE;
        }
        if (this.isFinalData) {
            status = DataStatus.END;
        }


        AiInput.Builder dataBuilder = AiInput.builder();
        dataBuilder.dataStatus(status);
        dataBuilder.audio("input", data);
        int writeRet = AiHelper.getInst().write(dataBuilder.build(), aiHandle);
//        AiSpeechLogUtil.d(TAG, "往AEE写入音频 ret:" + writeRet + " length:" + data.length + " status:" + status);
        int readRet = AiHelper.getInst().read(ASR_ABILITY, aiHandle);
        if (readRet != 0) {
//            AiSpeechLogUtil.d(TAG, "读取结果失败 ret:" + readRet);
        }
        return true; // 真正写入

    }

    //////////////////////////////////////
//初始化中英识别引擎
    private void initEdgSdk(AsrParam param) {
        AiSpeechLogUtil.e(TAG, "initEdgSdk: lastEsrParam: " + (lastEsrParam == null ? null : lastEsrParam.getCode()) + " param: " + param.getCode());
        if (lastEsrParam != null && lastEsrParam.getResName().equals(param.getResName())) {
            AiSpeechLogUtil.e(TAG, "initEdgSdk 刚刚使用不需要再初始化");
            lastEsrParam = param; // 保存当前的参数
            return;
        }
        AiSpeechLogUtil.e(TAG, "initEdgSdk: lastEsrParam: " + (lastEsrParam == null ? null : lastEsrParam.getCode()) + " param: " + param.getCode());

        if (lastEsrParam != null) {
            try {
                synchronized (this) {
                    long now = new Date().getTime();
                    AiSpeechLogUtil.e(TAG, "initEdgSdk: start destroyEngine");
//                    mEdgIat.unloadResource();
                    int ret = AiHelper.getInst().engineUnInit(ASR_ABILITY);
                    if (ret != 0) {
                        String str = "engineUnInit Failed!" + ret;
                        AiSpeechLogUtil.e(TAG, str);
                    } else {
                        isLoad = false;
                    }
                    AiSpeechLogUtil.e(TAG, "initEdgSdk: end destroyEngine cost: " + (new Date().getTime() - now));
                }
            } catch (Exception e) {
                AiSpeechLogUtil.e(TAG, e.getLocalizedMessage());
            }
        }

        // 先初始化SDK
        initSdk(this.context, param.workDir);

        //引擎初始化，传入回调接口
        long now = new Date().getTime();
        AiSpeechLogUtil.e(TAG, "engineInit开始 " + param);
        int ret = AiHelper.getInst().engineInitNoParams(ASR_ABILITY);
        AiSpeechLogUtil.e(TAG, "engineInit结束: ret " + ret + ", 耗时: " + (new Date().getTime() - now) + "ms");

        lastEsrParam = param;
    }


    public void setDebugFlag(Context context) {
        isDebug = context.getApplicationInfo() != null && (context.getApplicationInfo().flags & ApplicationInfo.FLAG_DEBUGGABLE) != 0;
    }


    private void startAsrInstance(AsrParam param) {
        AiSpeechLogUtil.e(TAG, "startAsrInstance: 开启一个Asr的实例..." + param);
        if (param == null) {
            AiSpeechLogUtil.e(TAG, "startAsrInstance: 错误当前 curParam 为空");
            return;
        }
        this.isFirstData = true;
        this.isFinalData = false;
        this.ready = false;
        initEdgSdk(param);
        startEngine(param);
        this.ready = true;
    }

    private void startEngine(AsrParam param) {
        AiInput.Builder paramBuilder = AiInput.builder();
        // vad功能开关，建议true，否则会很久才能出结果
        paramBuilder.param("vadOn", true);
        // 输出词之间的分隔符，这里填写空白
        paramBuilder.param("rltSep", "");
        // vad是否开启vadLink功能，开启则将多个vad子句拼在一起解码，建议false
        paramBuilder.param("vadLinkOn", false);
        // vad模型阈值，建议0.1332  最小值:0, 最大值:1
        paramBuilder.param("vadThreshold", 0.1332);
        // vad的能量门限值，建议9   最小值:0, 最大值:12
        paramBuilder.param("vadEnergyThreshold", 9);
        // vad尾端点，建议120000  最小值:100000, 最大值:150000
        paramBuilder.param("vadSpeechEnd", 120000);
        // vad结束参数，建议150000  最小值:100000, 最大值:170000
        paramBuilder.param("vadResponsetime", 150000);
        // 语种类型 0:中英, 28:阿拉伯语, 23:法语, 25:德语, 20:日语, 21:韩语, 22:俄语, 24:西班牙语, 27:泰语, 50:维吾尔语
        paramBuilder.param("languageType", param.getAeeCode());
        //0:json输出, 1:普通文本  因为我们要手动拼接出结果，所以用json类型的
        paramBuilder.param("outputType", 0);
        //句尾标点是否为缓存模式 true:是, false:否
        paramBuilder.param("puncCache", false);
        AiSpeechLogUtil.e(TAG, "start System.currentTimeMillis()before:" + System.currentTimeMillis());
        long time1 = System.currentTimeMillis();
        aiHandle = AiHelper.getInst().start(ASR_ABILITY, paramBuilder.build(), null);
        AiSpeechLogUtil.e(TAG, "start after:" + System.currentTimeMillis());
        long time2 = System.currentTimeMillis() - time1;
        AiSpeechLogUtil.e(TAG, "start time:" + time2);
        if (aiHandle == null) {
            AiSpeechLogUtil.e(TAG, "start 启动会话失败, handle 为空");
            return;
        }
        if (aiHandle.getCode() != 0) {
            AiSpeechLogUtil.e(TAG, "start 启动会话失败，错误码: " + aiHandle.getCode());
        } else {
            AiSpeechLogUtil.e(TAG, "start 启动会话成功");
        }
    }

    private void stopAsrInstance() {
        AiSpeechLogUtil.e(TAG, "------ stopAsrInstance start , code:" + (lastEsrParam != null ? lastEsrParam.getCode() : null));
        if (lastEsrParam == null) {
            AiSpeechLogUtil.e(TAG, "stopAsrInstance: 错误当前 curParam 为空");
            return;
        }
        if (this.isFinalData) {
            AiSpeechLogUtil.e(TAG, "stopAsrInstance: 还没开始 asr 的实例");
            return;
        }
        this.isFirstData = false;
        this.isFinalData = true;
        write(new byte[0]);  // 写入空数据，触发最后一句的标点识别
        try {
            Thread.sleep(50);
        } catch (InterruptedException ignored) {
        }
        pollOfflineResult("", true); // 在这里手动触发isLast结果
        int ret = -99;
        if (aiHandle != null) {
            ret = AiHelper.getInst().end(aiHandle);
        }
        try {
            Thread.sleep(50);
        } catch (InterruptedException ignored) {
        }
        AiSpeechLogUtil.e(TAG, "--------stopAsrInstance end , ret: " + ret + "---------");

    }

    /**
     * 定义识别结果状态，
     * 为了保证 ordinal函数返回的数值正确，请务必保证 hasResult, noResult, resultOver的值为0,1,2
     */
    private enum ResultStatus {
        noResult, hasResult, resultOver
    }

    private String getEdgResult(byte[] edgResult) {
        ResultStatus status = ResultStatus.hasResult;
        String str = null;
        if (edgResult != null && edgResult.length > 0) {
            try {
                if (lastEsrParam.getType() == AsrParam.Type.edge) {
                    str = new String(edgResult, "gbk");
                } else {
                    str = new String(edgResult, "utf-8");
                }
            } catch (UnsupportedEncodingException e) {
                AiSpeechLogUtil.e(TAG, "获取结果失败: ", e);
            }
        }
//        AiSpeechLogUtil.e(TAG, "学之友离线识别: " + str + " " + status);
        return str;
    }

    private synchronized void pollOfflineResult(String newText, boolean isLast) {
//        if (isLast && resultText.isEmpty() && newText.isEmpty())
//            return;  // 防止onResul触发一次、又主动触发一次，两次回调
//        if (isOnlySpacesAndPunctuation(newText)) return;
        resultText = resultText + newText;
        sendResult(resultText, this.srcCode, isLast);
        // 在最后的时候给结果清空
        if (isLast) resultText = "";
    }

    private void sendResult(final String text, final String langCode, boolean isLast) {
        new Thread(() -> {
            if (listener != null) {
                listener.onRecognizeResult(langCode, isLast, text);
            }
        }).start();
    }


    // 判断是否只包含空格和标点
    private boolean isOnlySpacesAndPunctuation(String input) {
        // 正则表达式匹配空格和标点符号
        String regex = "^[\\p{Punct}\\s]*$";
        return Pattern.matches(regex, input);
    }

    /**
     * @param jsonString 来自plain字段，或者来自readable字段
     * @return 给用户返回的用于显示、带标点的识别结果
     */
    private String parseJsonResult(String jsonString) {
        String returnString = "";
        try {
            if (this.lastEsrParam.getCode().equals("en")) {  // 如果识别语言是英文，那么替换掉中文的标点符号
                jsonString = jsonString.replace("，", ",");
                jsonString = jsonString.replace("。", ".");
                jsonString = jsonString.replace("？", "?");
                jsonString = jsonString.replace("！", "!");
            }

            JSONObject jsonObject = new JSONObject(jsonString);

            String sc = jsonObject.getString("sc");
            int bg = jsonObject.optInt("bg", 0);
            int ed = jsonObject.optInt("ed", 0);

            JSONArray wsArray = jsonObject.getJSONArray("ws");
            List<EdReadEntity.WsItem> wsItems = new ArrayList<>();

            for (int i = 0; i < wsArray.length(); i++) {
                JSONObject wsObject = wsArray.getJSONObject(i);
                String w = wsObject.optString("w", "");
                String slot = wsObject.optString("slot", "");
                String pinyin = wsObject.optString("pinyin", "");
                String boundary = wsObject.optString("boundary", "");
                EdReadEntity.WsItem wsItem = new EdReadEntity.WsItem(w, slot, pinyin, boundary);
                wsItems.add(wsItem);
            }

            EdReadEntity edReadEntity = new EdReadEntity(sc, bg, ed, wsItems);
            StringBuilder sb = new StringBuilder();
            for (EdReadEntity.WsItem wsItem : edReadEntity.getWs()) {
                sb.append(wsItem.getW());
            }
            returnString = sb.toString();
        } catch (Exception e) {
            AiSpeechLogUtil.e(TAG, "结果Json解析失败了：" + e);
        }

        if (this.lastEsrParam.getCode().equals("en")) {
            // 如果识别语言是英文，就匹配键盘上能打出来的所有英文字符
            String regex = "[ -~]";
            Pattern pattern = Pattern.compile(regex);
            Matcher matcher = pattern.matcher(returnString);
            StringBuilder result = new StringBuilder();
            while (matcher.find()) {
                result.append(matcher.group());
            }
            returnString = result.toString();
        }
        // 替换连续的一个或多个噪声，忽略大小写
        returnString = returnString.replaceAll("(?i)(Noise[,.? ，。？]*)+", "");
        // 去除字符串前面的空格 + 标点
        returnString = returnString.replaceAll("^[\\p{Punct}\\s]+", "");
        String punctuationPattern = "^\\p{Punct}*$";
        if (returnString.matches(punctuationPattern)) return ""; // 如果只有标点，直接返回
        // 将字符串中间的连续标点符号替换为单个标点符号
        returnString = returnString.replaceAll("(\\p{Punct}){2,}", "\\$1");
        return returnString;
    }


    public interface IflyOfflineListener {
        void onRecognizeResult(String langCode, boolean isLast, String text);
    }

    // 根据语言对，选择对应的translator
    public TranslatorBase findOfflineTranslator(String langCouple) {
        AiSpeechLogUtil.e(TAG, "寻找翻译引擎，语言对：" + langCouple);
        TranslatorBase translatorBase = null;
        if (langCouple.contains("en") && ((langCouple.contains("ja") || langCouple.contains("fr") || langCouple.contains("es") || langCouple.contains("ru") || langCouple.contains("ko") || langCouple.contains("de")))) {
            translatorBase = new NiuOfflineTranslator();
        } else if (langCouple.contains("zh") && langCouple.contains("ko")) {
            translatorBase = new NiuOfflineTranslator();
        } else {
            translatorBase = new IflytekOfflineTranslator();
        }
        if (translatorBase != null)
            AiSpeechLogUtil.e(TAG, "找到了翻译引擎：" + translatorBase.getName());
        else
            AiSpeechLogUtil.e(TAG, "没有找到翻译引擎");
        return translatorBase;
    }


    static class CodeBlock {
        String desc; // 代码块描述
        boolean isLast;
        Runnable block;
        IflyOfflineListener listener;


        CodeBlock(boolean isLast, String desc, Runnable block) {
            this.isLast = isLast;
            this.block = block;
            this.desc = desc;
            this.listener = listener;
        }

        CodeBlock(boolean isLast, String desc, Runnable block, IflyOfflineListener listener) {
            this.isLast = isLast;
            this.block = block;
            this.desc = desc;
            this.listener = listener;
        }
    }


    static class AsrParam {
        private String code;
        private Type type;
        private String resName;
        private String workDir;
        private int aeeCode;  // aee识别的语言码

        public AsrParam(String code, Type type, String resName, String workDir, int aeeCode) {
            this.code = code;
            this.type = type;
            this.resName = resName;
            this.workDir = workDir;
            this.aeeCode = aeeCode;
        }

        public String getCode() {
            return code;
        }

        public Type getType() {
            return type;
        }

        public String getResName() {
            return resName;
        }

        public String getWorkDir() {
            return workDir;
        }

        public int getAeeCode() {
            return aeeCode;
        }

        public enum Type {
            edge,
            esr,
        }

        @Override
        public String toString() {
            return "AsrParam{" +
                    "code='" + code + '\'' +
                    ", aeeCode=" + aeeCode +
                    ", type=" + type +
                    ", resName='" + resName + '\'' +
                    ", workDir='" + workDir + '\'' +
                    '}';
        }
    }
}
