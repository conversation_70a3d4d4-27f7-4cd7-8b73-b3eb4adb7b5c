package co.timekettle.speech.recognizer.aeesdk;

import java.util.List;

public class EdReadEntity {
    private String sc;
    private int bg;
    private int ed;
    private List<WsItem> ws;

    // Constructor
    public EdReadEntity(String sc, int bg, int ed, List<WsItem> ws) {
        this.sc = sc;
        this.bg = bg;
        this.ed = ed;
        this.ws = ws;
    }

    // Getters and Setters
    public String getSc() {
        return sc;
    }

    public void setSc(String sc) {
        this.sc = sc;
    }

    public int getBg() {
        return bg;
    }

    public void setBg(int bg) {
        this.bg = bg;
    }

    public int getEd() {
        return ed;
    }

    public void setEd(int ed) {
        this.ed = ed;
    }

    public List<WsItem> getWs() {
        return ws;
    }

    public void setWs(List<WsItem> ws) {
        this.ws = ws;
    }

    // Inner class for WsItem
    public static class WsItem {
        private String w;
        private String slot;
        private String pinyin;
        private String boundary;

        // Constructor
        public WsItem(String w, String slot, String pinyin, String boundary) {
            this.w = w;
            this.slot = slot;
            this.pinyin = pinyin;
            this.boundary = boundary;
        }

        // Getters and Setters
        public String getW() {
            return w;
        }

        public void setW(String w) {
            this.w = w;
        }

        public String getSlot() {
            return slot;
        }

        public void setSlot(String slot) {
            this.slot = slot;
        }

        public String getPinyin() {
            return pinyin;
        }

        public void setPinyin(String pinyin) {
            this.pinyin = pinyin;
        }

        public String getBoundary() {
            return boundary;
        }

        public void setBoundary(String boundary) {
            this.boundary = boundary;
        }
    }
}