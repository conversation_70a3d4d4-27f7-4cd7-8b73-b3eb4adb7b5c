package co.timekettle.speech.recognizer;

import android.content.Context;

import co.timekettle.speech.SpeechError;
import co.timekettle.speech.SpeechTask;
import co.timekettle.speech.utils.Language;

import java.util.HashMap;

public abstract class RecognizerBase {

    protected String name;
    protected String key;
    protected long session = 0;
    protected boolean singleton = false;
    protected Context context;
    protected Listener listener;
    protected HashMap<String, String> support = new HashMap<>();
    public boolean isOfflineModule = false;

    public void setListener(Listener listener) {
        this.listener = listener;
    }

    public Listener getListener() {
        return this.listener;
    }

    public String getName() {
        return name;
    }

    public void setKey(String key) {
        this.key = key;
    }

    public String getKey() {
        return this.key;
    }

    public void setContext(Context context) {
        this.context = context;
    }

    /**
     * 工作器是否是单实例(不允许多实例同时处理)
     */
    public boolean isSingleton() {
        return singleton;
    }

    public long getSession() {
        return session;
    }

    public void setSession(long s) {
        this.session = s;
    }

    @Deprecated
    public abstract boolean isSupport(String srcCode, String dstCode);
//    public abstract boolean isSupport(String srcCode);

    public abstract String platformCode(String srcCode);

    public abstract void start(final Language srcCode, final Language dstCode);

    public abstract void start(final Language srcCode);

    public abstract void stop();

    public abstract void writeAudio(byte[] data);

    public interface Listener {
        void onRecognizeResult(RecognizerBase recognizer, boolean isLast, String text, String engine, SpeechError error);

        void onTranslateResult(RecognizerBase recognizer, String text, String engine, SpeechError error);

        void onFinished(RecognizerBase recognizer, String engine, SpeechError error);
    }
}
