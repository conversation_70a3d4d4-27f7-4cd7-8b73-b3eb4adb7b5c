package co.timekettle.speech.recognizer;

import android.content.Context;
import android.content.pm.ApplicationInfo;

import co.timekettle.speech.SpeechError;
import co.timekettle.speech.translator.IflytekOfflineTranslator;
import co.timekettle.speech.translator.NiuOfflineTranslator;
import co.timekettle.speech.translator.TranslatorBase;
import co.timekettle.speech.utils.FantiUtil;
import co.timekettle.speech.utils.Language;
import co.timekettle.speech.utils.AiSpeechLogUtil;

import com.iflytek.voice.Constant;
import com.iflytek.voice.VoiceEdgRecognizer;
import com.iflytek.voice.VoiceUtility;
import com.iflytek.voice.common.EsrSessionInfo;
import com.iflytek.voice.common.VoiceError;

import org.json.JSONArray;
import org.json.JSONObject;
import org.json.JSONTokener;

import java.io.File;
import java.io.UnsupportedEncodingException;
import java.util.Date;
import java.util.HashMap;
import java.util.LinkedHashMap;
import java.util.Map;
import java.util.concurrent.LinkedBlockingQueue;
import java.util.concurrent.Semaphore;


/**
 * 讯飞离线, 只支持单实例(singleton), 同一时间只支持一个识别任务
 */
public class IflytekOfflineRecognizer extends RecognizerBase {
    private final static String TAG = "IflytekOfflineRecognizer";

    private Language srcCode;
    private Language dstCode;

    public IflytekOfflineRecognizer() {
        this.name = "iflytekOfflineAsr";
        this.singleton = true;
        this.isOfflineModule = true;
    }

    public static void initOffline(Context context, String resPath) {
        IflytekOfflineUtil.getInstance().initOffline(context, resPath, null);
    }

    public static void initOffline(Context context, String resPath, Map<String, String> customResPaths) {
        IflytekOfflineUtil.getInstance().initOffline(context, resPath, customResPaths);
    }

    public static void tryFetchAuth(String code) {
        IflytekOfflineUtil.getInstance().tryFetchAuth(code);
    }

    public static boolean tryAuth(String code) {
        return IflytekOfflineUtil.getInstance().tryAuth(code);
    }

    public static boolean tryAuth2(Context context, String workDir) {
        return IflytekOfflineUtil.getInstance().tryAuth2(context, workDir);
    }

    @Override
    public boolean isSupport(String srcCode, String dstCode) {
        return IflytekOfflineUtil.getInstance().isSupport(srcCode, dstCode);
    }

    @Override
    public String platformCode(String srcCode) {
        return null;
    }

    @Override
    public void start(Language srcCode, Language dstCode) {
        AiSpeechLogUtil.d(TAG, "离线识别流程[start]: " + srcCode.standardCode + " " + session);
        this.srcCode = srcCode;
        this.dstCode = dstCode;

        IflytekOfflineUtil.getInstance().start(srcCode.standardCode, session, (langCode, isLast, text) -> {

            ///////////// 兼容繁体
            if (langCode.toLowerCase().endsWith("zh-tw")) {
                text = FantiUtil.traditionalized(text);
                AiSpeechLogUtil.d(TAG, "转换为繁体");
            }
            ///////////// 兼容繁体

            if (!isLast) {
                listener.onRecognizeResult(IflytekOfflineRecognizer.this, isLast, text, name, null);
            } else {
                if (text == null || text.length() == 0) {
                    listener.onFinished(IflytekOfflineRecognizer.this, name, new SpeechError(-1, "识别为空"));
                    return;
                }
                listener.onRecognizeResult(IflytekOfflineRecognizer.this, isLast, text, name, null);
                listener.onFinished(IflytekOfflineRecognizer.this, name, null);
            }
        });
    }


    @Override
    public void start(final Language srcCode) {
        start(srcCode, null);
    }

    @Override
    public void stop() {
        AiSpeechLogUtil.d(TAG, "离线识别流程[end]: " + srcCode.standardCode + " " + session);
        IflytekOfflineUtil.getInstance().stop(session);
    }

    @Override
    public void writeAudio(byte[] data) {
        IflytekOfflineUtil.getInstance().writeAudio(data, session);
    }
}

/**
 *
 */
class IflytekOfflineUtil {
    private final static String TAG = "IflytekOfflineRecognizer";

    private static boolean isLoad = false;

    private final Map<String, AsrParam> supports = new HashMap<>();

    private AsrParam lastEsrParam = null; // 由于 esr 存在切换, 故保存最后一次的配置

    private String srcCode;
//    private String dstCode;


    // 听写对象
    private VoiceEdgRecognizer mEdgIat = null;
    private EsrSessionInfo edgInfo = new EsrSessionInfo();

    private IflyOfflineListener listener;

    private boolean ready = false;  // 是否资源已加载完毕

    private boolean isFirstData = true;
    private boolean isFinalData = false;

    private int pollResultPeriod = 50; // 包数
    private String resultText;
    private int writedCount = 0;  // 当前写入包数计数

    private boolean isDebug = false;

    private final Semaphore semaphore = new Semaphore(1);

    // 保证在线任务顺序执行, 并且任务代码块顺序必须是 group(0)[start(0) write...write stop(0)] group(1)[start(1) write...write stop(1)]
    protected LinkedBlockingQueue<LinkedBlockingQueue<CodeBlock>> taskGroups = new LinkedBlockingQueue<>();
    // 持有任务组, 以便查询
    private final LinkedHashMap<Long, LinkedBlockingQueue<CodeBlock>> pandingBlocks = new LinkedHashMap<>();
    private Thread taskloop;

    private static IflytekOfflineUtil instance;

    public static IflytekOfflineUtil getInstance() {
        if (instance == null) {
            instance = new IflytekOfflineUtil();
        }
        return instance;
    }

    public IflytekOfflineUtil() {
        if (taskloop == null) {
            taskloop = new Thread(() -> {
                while (true) {
                    try {
                        LinkedBlockingQueue<CodeBlock> blocks = taskGroups.take();
                        while (true) {
                            CodeBlock block = blocks.take();

                            block.block.run();
//                            AiSpeechLogUtil.d(TAG, "代码块执行完 " + block.desc);
                            // 如果 block 是 结束的代码块, 则退出
                            if (block.isLast) {
                                break;
                            }
                        }
                    } catch (InterruptedException e) {
                        e.printStackTrace();
                    }
                }
            });
            taskloop.setName("Tmk-" + TAG);
            taskloop.start();
        }
    }

    public void initOffline(Context context, String resPath, Map<String, String> customResPaths) {
        addSupport("zh", AsrParam.Type.edge, Constant.LAN_CN,      "cnen", resPath, customResPaths);
        addSupport("en", AsrParam.Type.edge, Constant.LAN_EN,      "cnen", resPath, customResPaths);
        addSupport("fr", AsrParam.Type.esr,  Constant.LAN_FRENCH,  "fr",   resPath, customResPaths);
        addSupport("ja", AsrParam.Type.esr,  Constant.LAN_JAPAN,   "ja",   resPath, customResPaths);
        addSupport("es", AsrParam.Type.esr,  Constant.LAN_SPANISH, "es",   resPath, customResPaths);
        addSupport("ru", AsrParam.Type.esr,  Constant.LAN_RUSSIAN, "ru",   resPath, customResPaths);
        addSupport("ko", AsrParam.Type.esr,  Constant.LAN_KOREA,   "ko",   resPath, customResPaths);
        addSupport("de", AsrParam.Type.esr,  Constant.LAN_GERMAN,  "de",   resPath, customResPaths);

        setDebugFlag(context);
        createIflyUtility(context);
    }

    private void createIflyUtility(Context context) {
        if (!isLoad) {
            try {
                VoiceUtility.createUtility(context, "appid=5a2f382e" + "," + Constant.SHOW_LOG + "=true" + Constant.LOG_LEVEL + "=4");

                mEdgIat = VoiceEdgRecognizer.createRecognizer(context);

                isLoad = true;
            } catch (VoiceError voiceError) {
                voiceError.printStackTrace();
            }
        }
    }

    public void tryFetchAuth(String code) {
        this.tryAuth(code);
    }

    public boolean tryAuth(String code) {
        if (this.isSupport(code, null)) { // 应用首次使用时
            String key = code.split("-")[0];
            AsrParam param = supports.get(key);
            if (param == null) {
                AiSpeechLogUtil.d(TAG, "tryFetchAuth: 不支持此语种: " + code);
                return false;
            }
            if (param.authed) {
                AiSpeechLogUtil.d(TAG, "tryFetchAuth: 已鉴权过: " + code);
                return true;
            }

            // 正在使用的离线识别
            if (lastEsrParam != null) {
                AiSpeechLogUtil.d(TAG, "tryFetchAuth: 有在使用其他语种, 已鉴权过: " + code);
                return true;
            }

            // 设置进程级参数
            setProParams(param);

            // 引擎初始化，传入回调接口
            int ret = mEdgIat.createEngine();
            if (ret != 0) {
                AiSpeechLogUtil.e(TAG, "tryFetchAuth ---------> createEngine ret: " + ret);
                return false;
            }
            ret = mEdgIat.loadResource();
            if (ret != 0) {
                AiSpeechLogUtil.e(TAG, "tryFetchAuth ---------> loadResource ret: " + ret);
                return false;
            }

            // 加上这一句代码, 防止 T1 系统级别应用崩溃
            setParams(param); //设置会话级参数

            ret = mEdgIat.startInst();
            if (ret != 0) {
                AiSpeechLogUtil.e(TAG, "tryFetchAuth ---------> startInst ret: " + ret);
                return false;
            }
            mEdgIat.stopInst();

            mEdgIat.destroyEngine();

            // 讯飞单个鉴权成功则代表全部成功
            for (AsrParam para : supports.values()) {
                para.authed = true;
            }
            return true;
        }

        return false;
    }

    private boolean ifly_authed2 = false; // 记录 tryAuth2 的鉴权情况
    public boolean tryAuth2(Context context, String workDir) {
        if (ifly_authed2) {
            AiSpeechLogUtil.d(TAG, "tryAuth2 讯飞离线识别鉴权成功(此前已检测鉴权通过)");
            return true;
        }
        try {
            createIflyUtility(context);

            AsrParam param = new AsrParam("zh", AsrParam.Type.edge, Constant.LAN_CN, "cnen", workDir);
            // 设置进程级参数
            setProParams(param);

            // 引擎初始化，传入回调接口
            int ret = mEdgIat.createEngine();
            if (ret != 0) {
                AiSpeechLogUtil.e(TAG, "tryAuth2 ---------> createEngine ret: " + ret);
                return false;
            }
            ret = mEdgIat.loadResource();
            if (ret != 0) {
                AiSpeechLogUtil.e(TAG, "tryAuth2 ---------> loadResource ret: " + ret);
                return false;
            }

            // 加上这一句代码, 防止 T1 系统级别应用崩溃
            setParams(param); //设置会话级参数

            ret = mEdgIat.startInst();
            if (ret != 0) {
                AiSpeechLogUtil.e(TAG, "tryAuth2 ---------> startInst ret: " + ret);
                return false;
            }
            mEdgIat.stopInst();
            mEdgIat.destroyEngine();
        } catch (Exception e) {
            e.printStackTrace();
            return false;
        }
        AiSpeechLogUtil.d(TAG, "tryAuth2 讯飞离线识别鉴权成功(检测鉴权是否过期)");
        ifly_authed2 = true;
        return true;
    }

    private void addSupport(String code, AsrParam.Type type, String value, String resName, String workDir, Map<String, String> customResPaths) {
        // 有自定义路径则使用自定义路径
        if (customResPaths != null && customResPaths.containsKey(code)) {
            workDir = customResPaths.get(code);
        }
        this.supports.put(code, new AsrParam(code, type, value, resName, workDir));
    }

    public boolean isSupport(String srcCode, String dstCode) {
        return this.supports.containsKey(srcCode.split("-")[0]);
    }

    public synchronized void start(String srcCode, long session, IflyOfflineListener listener) {
        try {
//            AiSpeechLogUtil.d(TAG, "start: 等待上一离线识别任务完成");
            if (pandingBlocks.containsKey(session)) {
                AiSpeechLogUtil.d(TAG, "start: 已存在当前离线识别任务 " + session);
//                    ConcurrentLinkedDeque<CodeBlock> blocks = pandingBlocks.get(session);
//                    blocks.add();
            } else {
                LinkedBlockingQueue<CodeBlock> blocks = new LinkedBlockingQueue<>();
                blocks.add(new CodeBlock(false, "start-" + session, () -> {
                    this.srcCode = srcCode;
                    this.listener = listener;

                    String key = srcCode.split("-")[0];
                    AsrParam param = supports.get(key);

                    this.startAsrInstance(param);
                }));
                pandingBlocks.put(session, blocks);
                taskGroups.put(blocks);
                AiSpeechLogUtil.d(TAG, "start: 添加当前离线识别任务组: " + taskGroups.size());
            }

        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    public synchronized void stop(long session) {
        LinkedBlockingQueue<CodeBlock> blocks = pandingBlocks.get(session);
        if (blocks != null) {
            blocks.add(new CodeBlock(true, "stop-" + session, () -> {
                this.stopAsrInstance();
            }));
        }
        AiSpeechLogUtil.d(TAG, "stop: 当前离线识别任务完成 " + session);
    }

    public synchronized void writeAudio(byte[] data, long session) {
        LinkedBlockingQueue<CodeBlock> blocks = pandingBlocks.get(session);
        if (blocks != null) {
            blocks.add(new CodeBlock(false, "writeAudio-" + session, () -> {
                this.write(data);
            }));
        }
//
//        this.write(data);
    }

    private boolean write(byte[] data) {
        if (lastEsrParam == null) {
            AiSpeechLogUtil.e(TAG, "writeAudio: 错误当前 curParam 为空");
            return false;
        }

        if (!ready) {
            AiSpeechLogUtil.d(TAG, "writeAudio: " + lastEsrParam.getCode() + " ready:" + ready);
            return false;
        }

        Constant.AudioStatus status = Constant.AudioStatus.ESR_AUDIO_BEGIN;
        if (this.isFirstData) {
            status = Constant.AudioStatus.ESR_AUDIO_BEGIN;
            this.isFirstData = false;
        } else {
            status = Constant.AudioStatus.ESR_AUDIO_CONTINUE;
        }
        if (this.isFinalData) {
            status = Constant.AudioStatus.ESR_AUDIO_END;
        }

        if (mEdgIat != null && data != null && ready) {
            this.writedCount++;
            int ret = mEdgIat.writeAudio(data, data.length, status); //写入音频数据, 未授权时出现 10008 错误
            if (ret != 0) AiSpeechLogUtil.e(TAG, "mEdgIat writeAudio: " + ret + " length:" + data.length + " status:" + status);
//            Log.d(TAG, "write: curWroteCount: " + writedCount);
            if (this.writedCount % pollResultPeriod == 0) pollOfflineResult(false);
            return true; // 真正写入
        }

        return false;
    }

    //////////////////////////////////////
    //初始化中英识别引擎
    private void initEdgSdk(AsrParam param) {
        if (!param.authed) AiSpeechLogUtil.e(TAG, "讯飞离线识别未鉴权(警告): " + param.code);
        AiSpeechLogUtil.e(TAG, "initEdgSdk: lastEsrParam: " + (lastEsrParam == null ? null : lastEsrParam.getCode()) + " param: " + param.getCode());
        if (lastEsrParam != null && lastEsrParam.getResName().equals(param.getResName())) {
            AiSpeechLogUtil.e(TAG, "initEdgSdk 刚刚使用不需要再初始化");
            lastEsrParam = param; // 保存当前的参数
            return;
        }
        AiSpeechLogUtil.e(TAG, "initEdgSdk: lastEsrParam: " + (lastEsrParam == null ? null : lastEsrParam.getCode()) + " param: " + param.getCode());


        if (lastEsrParam != null) {
            try {
                synchronized (this) {
                    long now = new Date().getTime();
                    AiSpeechLogUtil.e(TAG, "initEdgSdk: start destroyEngine");
//                    mEdgIat.unloadResource();
                    mEdgIat.destroyEngine();
                    AiSpeechLogUtil.e(TAG, "initEdgSdk: end destroyEngine cost: " + (new Date().getTime() - now));
                }
            } catch (Exception e) {
                AiSpeechLogUtil.e(TAG, e.getLocalizedMessage());
            }
        }

        //设置进程级参数
        setProParams(param);
        //引擎初始化，传入回调接口
        mEdgIat.createEngine(); // 10007, Constant.EDGE_WORK_DIR 设置错误

        long now = new Date().getTime();
        AiSpeechLogUtil.e(TAG, "initEdgSdk: start loadResource " + param.getResName());
        int ret = mEdgIat.loadResource();
        AiSpeechLogUtil.e(TAG, "initEdgSdk: " + ret + ", end loadResource cost: " + (new Date().getTime() - now));

        lastEsrParam = param;
    }

    private boolean isEdgeLanguage(AsrParam param) {
        return param.getType() == AsrParam.Type.edge;
    }

    public void setDebugFlag(Context context) {
        isDebug = context.getApplicationInfo() != null && (context.getApplicationInfo().flags & ApplicationInfo.FLAG_DEBUGGABLE) != 0;
    }

    private void setProParams(AsrParam param) {
        if (param == null) {
            AiSpeechLogUtil.e(TAG, "setProParams: error param is null isDebug:" + isDebug);
            if (isDebug) {
                throw new NullPointerException("!!! 参数 param 为空");
            }
        }

        // 设置资源路径
        String mlp = param.getWorkDir() + "/asr/" + param.getResName() + "/MLP.bin";
        String word = param.getWorkDir() + "/asr/" + param.getResName() + "/Words.bin";
        String vad = param.getWorkDir() + "/asr/" + param.getResName() + "/VAD.bin";

        String workDir = vad.substring(0, vad.lastIndexOf("/"));
        AiSpeechLogUtil.e(TAG, "setProParams: " + workDir);
        AiSpeechLogUtil.e(TAG, "vad: " + vad);
        AiSpeechLogUtil.e(TAG, "word: " + word);
        AiSpeechLogUtil.e(TAG, "mlp: " + mlp);
        if (!new File(mlp).exists()) AiSpeechLogUtil.e(TAG, "setProParams: 不存在资源: " + mlp);

        mEdgIat.setEdgeProParameter(Constant.PARAMS, null);
        mEdgIat.setEdgeProParameter(Constant.LAN_TYPE, param.getValue());
        mEdgIat.setEdgeProParameter(Constant.EDGE_WORK_DIR, workDir);
        mEdgIat.setEdgeProParameter(Constant.VAD_RES_PATH, vad);
        mEdgIat.setEdgeProParameter(Constant.WORD_RES_PATH, word);
        mEdgIat.setEdgeProParameter(Constant.MLP_RES_PATH, mlp);
    }

    private void setParams(AsrParam param) {
        mEdgIat.setEdgeParameter(Constant.PARAMS, null);
        mEdgIat.setEdgeParameter(Constant.LAN_TYPE, param.getValue());
        mEdgIat.setEdgeParameter(Constant.VAD_LNK, "0");//分段返回，若设置为1，则结果一次性返回
        mEdgIat.setEdgeParameter(Constant.VAD_ENABLE, "1");
        mEdgIat.setEdgeParameter(Constant.POST_ON, "1");
        if (!isEdgeLanguage(param)) {
            mEdgIat.setEdgeParameter(Constant.RESULT_SEPARATOR, " ");
        }

        mEdgIat.setEdgeParameter(Constant.VAD_BOS, "150000");
        mEdgIat.setEdgeParameter(Constant.VAD_EOS, "120000");//默认20s
    }

    private void startAsrInstance(AsrParam param) {
        this.writedCount = 0;

        if (param == null) {
            AiSpeechLogUtil.e(TAG, "startAsrInstance: 错误当前 curParam 为空");
            return;
        }

        this.isFirstData = true;
        this.isFinalData = false;
        this.ready = false;

        initEdgSdk(param);
        setParams(param); //设置会话级参数
        int ret = mEdgIat.startInst();
        AiSpeechLogUtil.d(TAG, "startAsrInstance: code:" + param.getCode() + " ret:" + ret);
        if (ret == 0) param.authed = true;
        this.ready = true;
    }

    private void stopAsrInstance() {
        AiSpeechLogUtil.e(TAG, "stopAsrInstance: code:" + (lastEsrParam != null ? lastEsrParam.getCode() : null));

        if (lastEsrParam == null) {
            AiSpeechLogUtil.e(TAG, "stopAsrInstance: 错误当前 curParam 为空");
            return;
        }

        if (this.isFinalData) {
            AiSpeechLogUtil.e(TAG, "stopAsrInstance: 还没开始 asr 的实例");
            return;
        }

        this.isFirstData = false;
        this.isFinalData = true;

        int ret = mEdgIat.writeAudio(new byte[0], 0, Constant.AudioStatus.ESR_AUDIO_END); // 写入结束
        AiSpeechLogUtil.e(TAG, "mEdgIat writeAudio: " + ret + " status:" + Constant.AudioStatus.ESR_AUDIO_END);

        // 延迟结束, 处理结果后停止
        try {
            Thread.sleep(50);
        } catch (InterruptedException e) {
            e.printStackTrace();
        }

        pollOfflineResult(true);

        AiSpeechLogUtil.e(TAG, "stopAsrInstance: mEdgIat: " + (mEdgIat.isListening() ? "isListening" : "not isListening"));
        if (mEdgIat != null && mEdgIat.isListening()) {
            // 延迟结束, 处理结果后停止
            try {
                Thread.sleep(50);
            } catch (InterruptedException e) {
                e.printStackTrace();
            }
            mEdgIat.stopInst();
            AiSpeechLogUtil.e(TAG, "--------mEdgIat stopInst---------");
        }
    }

    /**
     * 定义识别结果状态，
     * 为了保证 ordinal函数返回的数值正确，请务必保证 hasResult, noResult, resultOver的值为0,1,2
     */
    private enum ResultStatus {
        noResult, hasResult, resultOver
    }

    private String getEdgResult() {
        byte[] edgResult = mEdgIat.getInstResult(edgInfo, "plain");
        ResultStatus status = ResultStatus.noResult;
        if (edgInfo.errorCode == 0) {
            switch (edgInfo.rsltStatus) {
                case 0: status = ResultStatus.noResult; break;
                case 1: status = ResultStatus.hasResult; break;
                case 2: status = ResultStatus.resultOver; break;
            }
        }

        String str = null;
        if (ResultStatus.noResult != status) {
            if (edgResult != null && edgResult.length > 0) {
                try {
                    if (isEdgeLanguage(lastEsrParam)) {
                        str = new String(edgResult, "gb2312");
                    } else {
                        str = new String(edgResult, "utf-8");
                    }
                } catch (UnsupportedEncodingException e) {
                    AiSpeechLogUtil.e(TAG, "pollOfflineResult: ", e);
                }
            }
        }
        AiSpeechLogUtil.e(TAG, "pollOfflineResult 讯飞离线识别: " + str + " " + status);
        return str;
    }

    private void pollOfflineResult(boolean isLast) {
        String result = getEdgResult();
        if (result != null && result.length() > 0) { // 有新增结果
            resultText = resultText == null ? parseResult(result) : resultText + parseResult(result);
            sendResult(resultText, this.srcCode, isLast);
        } else {
            if (isLast) {
                sendResult(resultText, this.srcCode, isLast);
            }
        }

        if (isLast) resultText = null;
    }

    private void sendResult(final String text, final String langCode, boolean isLast) {
        new Thread(new Runnable() {
            @Override
            public void run() {
                if (listener != null) {
                    listener.onRecognizeResult(langCode, isLast, text);
                }
            }
        }).start();
    }

    private String parseResult(String json) {

        if (!json.startsWith("{\"sc")) {
            // [NumSeq] [NumStr] [Date] [Time] [Fraction] [MathSymbol]
            json = json.replaceAll("\\[.+?]", "");
            json = json.replaceAll("Noise[,.? ，。？](noise[,.? ，。？])?", "");

            if (this.lastEsrParam.getCode().equals("en")) {
                json = json.replace("，", ",");
                json = json.replace("。", ".");
                json = json.replace("？", "?");
                json = json.replace("！", "!");
            }
            return json;
        }

        StringBuilder ret = new StringBuilder();
        try {
            JSONTokener tokener = new JSONTokener(json);
            JSONObject joResult = new JSONObject(tokener);
            JSONArray words = joResult.getJSONArray("ws");
            for (int i = 0; i < words.length(); i++) {
                JSONObject word = words.getJSONObject(i);
                ret.append(word.getString("w"));
            }
        } catch (Exception e) {
            e.printStackTrace();
        }

        json = ret.toString();
        json = json.replaceAll("\\[.+?]", "");
        json = json.replaceAll("Noise[,.? ，。？ ](noise[,.? ，。？])?", "");

        if (this.lastEsrParam.getCode().equals("en")) {
            json = json.replace("，", ",");
            json = json.replace("。", ".");
            json = json.replace("？", "?");
            json = json.replace("！", "!");
        }
        return json;
    }

    // FIXME: 2023/5/8 识别语言更换时需要卸载此单例对象, 新语言重新 createEngine, 会加载当前语言的资源文件
    void destroyEngine() {
        if (mEdgIat != null) mEdgIat.destroyEngine();
    }

    public interface IflyOfflineListener {
        void onRecognizeResult(String langCode, boolean isLast, String text);
    }

    // 根据语言对，选择对应的translator
    public TranslatorBase findOfflineTranslator(String langCouple) {
        AiSpeechLogUtil.e(TAG, "寻找翻译引擎，语言对：" + langCouple);
        TranslatorBase translatorBase = null;
        if (langCouple.contains("en") && ((langCouple.contains("ja") || langCouple.contains("fr") || langCouple.contains("es") || langCouple.contains("ru") || langCouple.contains("ko") || langCouple.contains("de")))) {
            translatorBase = new NiuOfflineTranslator();
        } else if (langCouple.contains("zh") && langCouple.contains("ko")) {
            translatorBase = new NiuOfflineTranslator();
        } else {
            translatorBase = new IflytekOfflineTranslator();
        }
        if (translatorBase != null)
            AiSpeechLogUtil.e(TAG, "找到了翻译引擎：" + translatorBase.getName());
        else
            AiSpeechLogUtil.e(TAG, "没有找到翻译引擎");
        return translatorBase;
    }

    static class CodeBlock {
        String desc; // 代码块描述
        boolean isLast;
        Runnable block;
        IflyOfflineListener listener;

//        CodeBlock(boolean isLast, Runnable block) {
//            this.isLast = isLast;
//            this.block = block;
//        }

        CodeBlock(boolean isLast, String desc, Runnable block) {
            this.isLast = isLast;
            this.block = block;
            this.desc = desc;
            this.listener = listener;
        }

        CodeBlock(boolean isLast, String desc, Runnable block, IflyOfflineListener listener) {
            this.isLast = isLast;
            this.block = block;
            this.desc = desc;
            this.listener = listener;
        }
    }

//    public interface CodeBlock {
//        void invoke();
//        default void setIsLast(boolean isLast) {
//            this.isLast = isLast;
//        }
//
//        boolean getIsLast() {
//            return this.isLast;
//        }
//    }

    static class AsrParam {
        private String code;
        private Type type;
        private String value;
        private String resName;
        private String workDir;
        private boolean authed;

        public AsrParam(String code, Type type, String value, String resName, String workDir) {
            this.code = code;
            this.type = type;
            this.value = value;
            this.resName = resName;
            this.workDir = workDir;
        }

        public String getCode() {
            return code;
        }

        public Type getType() {
            return type;
        }

        public String getValue() {
            return value;
        }

        public String getResName() {
            return resName;
        }

        public String getWorkDir() {
            return workDir;
        }

        public enum Type {
            edge,
            esr,
        }

        @Override
        public String toString() {
            return "AsrParam{" +
                    "code='" + code + '\'' +
                    ", type=" + type +
                    ", value='" + value + '\'' +
                    ", resName='" + resName + '\'' +
                    ", workDir='" + workDir + '\'' +
                    '}';
        }
    }
}
