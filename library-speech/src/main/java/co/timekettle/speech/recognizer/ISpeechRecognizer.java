package co.timekettle.speech.recognizer;

import android.text.TextUtils;

import co.timekettle.speech.ISpeechConstant;
import co.timekettle.speech.SpeechError;
import co.timekettle.speech.utils.Language;
import co.timekettle.speech.utils.AiSpeechLogUtil;
import co.timekettle.speech.utils.ThreadPoolManager;
import co.timekettle.tmkengine.NetSessionContext;
import co.timekettle.tmkengine.TmkSpeechClient;

public class ISpeechRecognizer extends RecognizerBase {
    private static final String TAG = "ISpeechRecognizer";

    private NetSessionContext netContext = null;

    public ISpeechRecognizer() {
        name = "ispeech";
    }

    @Override
    public void setSession(long workerId) {
        super.setSession(workerId);

        if (netContext != null) netContext.setSession(workerId);
    }

    @Override
    public boolean isSupport(String srcCode, String dstCode) {
        return true;
    }
    @Override
    public String platformCode(String srcCode) {
        return srcCode;
    }


    private NetSessionContext.ContextListener netListener = new NetSessionContext.ContextListener() {
        @Override
        public void onRecognizeResult(NetSessionContext context, long session, boolean isLast, String srcCode, String rtext, String ttext, String engine) { 
            if (listener != null) {
                listener.onRecognizeResult(ISpeechRecognizer.this, isLast, rtext, engine, null);
            }
        }

        @Override
        public void onTranslateResult(NetSessionContext context, long session, String result, String engine) {
            if (listener != null) {
                listener.onTranslateResult(ISpeechRecognizer.this, result, engine, null);
            }
        }

        @Override
        public void onSynthesizeBuffer(NetSessionContext context, long session, byte[] output, int outputSize) {

        }

        @Override
        public void onCompleted(NetSessionContext context, long session, String engine) {
            if (listener != null) {
                listener.onFinished(ISpeechRecognizer.this, engine, null);
            }
            clear();
        }

        @Override
        public void onError(NetSessionContext context, long session, String engine, int code, String message) {
            if (listener != null) {
                listener.onRecognizeResult(ISpeechRecognizer.this, true, null, name, new SpeechError(code, message));
            }
            clear();
        }
    };

    @Override
    public void start(final Language srcCode, final Language dstCode) {
        if (srcCode == null || TextUtils.isEmpty(srcCode.platformCode)) {
            AiSpeechLogUtil.e(TAG, "start : 语言没设置好--->");
            return;
        }
        String src = srcCode.standardCode;
        String dst = dstCode != null ? dstCode.standardCode : null;
        netContext = TmkSpeechClient.shareInstance().createRecognizer(src, ISpeechConstant.useOpus, netListener);
        netContext.setSession(getSession());
        netContext.start();
    }

    @Override
    public void start(final Language srcCode) {
        start(srcCode, null);
    }

    @Override
    public void stop() {
        if (netContext != null) {
            netContext.stop();
            netContext = null;
        }
    }

    @Override
    public void writeAudio(byte[] data) {
        if (netContext != null) {
            netContext.writeAudio(data);
        }
    }

    private void clear() {
        if (netContext != null) {
            netContext.setListener(null);
            netContext = null;
            netListener = null;
        }
    }
}
