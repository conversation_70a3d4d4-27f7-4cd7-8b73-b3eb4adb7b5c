package co.timekettle.speech.recognizer.aeesdk;

import android.content.Context;

import java.util.Map;

import co.timekettle.speech.SpeechError;
import co.timekettle.speech.recognizer.RecognizerBase;
import co.timekettle.speech.utils.AiSpeechLogUtil;
import co.timekettle.speech.utils.FantiUtil;
import co.timekettle.speech.utils.Language;


/**
 * 学之友离线, 只支持单实例(singleton), 同一时间只支持一个识别任务
 * 如果是双人对话的场景，每次都需要engineInit和engineUninit
 */
public class AeeOfflineRecognizer extends RecognizerBase {
    private final static String TAG = "AeeOfflineAsr";

    private Language srcCode;
    private Language dstCode;

    public AeeOfflineRecognizer() {
        this.name = "AeeOfflineAsr";
        this.singleton = true;
        this.isOfflineModule = true;
    }

    public static void initOffline2(Context context, String resPath) {
        AeeOfflineRecognizeUtil.getInstance().initOffline2(context, resPath, null);
    }

    public static void initOffline2(Context context, String resPath, Map<String, String> customResPaths) {
        AeeOfflineRecognizeUtil.getInstance().initOffline2(context, resPath, customResPaths);
    }

    public static boolean tryAuth(Context context,String code) {
        return AeeOfflineRecognizeUtil.getInstance().tryAuth(context, code);
    }

    @Override
    public boolean isSupport(String srcCode, String dstCode) {
        return AeeOfflineRecognizeUtil.getInstance().isSupport(srcCode, dstCode);
    }

    @Override
    public String platformCode(String srcCode) {
        return null;
    }

    @Override
    public void start(Language srcCode, Language dstCode) {
        AiSpeechLogUtil.d(TAG, "离线识别流程[start]: " + srcCode.standardCode + " " + session);
        this.srcCode = srcCode;
        this.dstCode = dstCode;

        AeeOfflineRecognizeUtil.getInstance().start(srcCode.standardCode, session, (langCode, isLast, text) -> {

            ///////////// 兼容繁体
            if (langCode.toLowerCase().endsWith("zh-tw")) {
                text = FantiUtil.traditionalized(text);
                AiSpeechLogUtil.d(TAG, "转换为繁体");
            }
            ///////////// 兼容繁体

            if (!isLast) {
                listener.onRecognizeResult(AeeOfflineRecognizer.this, isLast, text, name, null);
            } else {
                if (text == null || text.trim().isEmpty()) {
                    listener.onFinished(AeeOfflineRecognizer.this, name, new SpeechError(-1, "识别为空"));
                    return;
                }
                listener.onRecognizeResult(AeeOfflineRecognizer.this, isLast, text, name, null);
                listener.onFinished(AeeOfflineRecognizer.this, name, null);
            }
        });
    }


    @Override
    public void start(final Language srcCode) {
        start(srcCode, null);
    }

    @Override
    public void stop() {
        AiSpeechLogUtil.d(TAG, "离线识别流程[end]: " + srcCode.standardCode + " " + session);
        AeeOfflineRecognizeUtil.getInstance().stop(session);
    }

    @Override
    public void writeAudio(byte[] data) {
        AeeOfflineRecognizeUtil.getInstance().writeAudio(data, session);
    }
}

