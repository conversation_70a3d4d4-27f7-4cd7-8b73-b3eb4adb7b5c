package co.timekettle.speech;

import android.util.Log;

import java.nio.charset.StandardCharsets;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Locale;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

import co.timekettle.speech.utils.AiSpeechLogUtil;

/* 上下文绑定在 AiSpeechManager 中, 相当于一次对话(asr,mt和tts)的容器 */
public class SpeechSessionContext {
    private static final String TAG = "SpeechRealContext";

    public HashMap<SpeechTask.TaskType, SpeechTask<?,?>> tasks = new HashMap<>();
    public HashMap options;
    /////////////////////////////////////////////

    public long timestamp; /// 相当所有任务组(asr, mt, tts任务集合)的 id, 在识别器翻译器中只 session/workid
    public String chkey;
    public String code;
    public String dstCode; /// tts 时, 设置此值为合成 code

    private int type; /// 配置任务类型, 识别 或者 识别+翻译 或者 识别+翻译+合成

    public Object extDevice;
    public String speakerType;

    public boolean invalid = false; // 任务是否已无效
    public List<String> finalMtResults;

    // 语速控制
    // Speaker 类型

    /* 若只包括 tts 和 play 任务, 则设置 dstCode, code 为空即可 */
    public SpeechSessionContext(long timestamp, String chkey, String code, String dstCode, HashMap<?, ?> options) {
        this.timestamp = timestamp;
        this.chkey = chkey;
        this.code = code;
        this.dstCode = dstCode;
        this.options = options;
    }

    public void setTaskType(SpeechTask.TaskType[] types) {
        for (SpeechTask.TaskType type : types) {
            int tValue = SpeechTask.TaskType.getValue(type);
            this.type = this.type | tValue;

//            this.tasks.put(type, this.getTaskInstance(type));
        }

        AiSpeechLogUtil.d(TAG, "setTaskType: " + this.type);
    }

    public boolean hasTask(SpeechTask.TaskType type) {
        return this.tasks.get(type) != null;
    }

    public SpeechTask getFirstTask(SpeechTask.TaskType type) {
        return this.tasks.get(type);
    }

    public SpeechTask getLastTask(SpeechTask.TaskType type) {
        SpeechTask last = this.tasks.get(type);
        if (last != null) {
            SpeechTask task = last.next;
            while (task != null) {
                last = task;
                task = task.next;
            }
            return last;
        }
        return null;
    }

    public void setTask(SpeechTask.TaskType type, SpeechTask task) {
        SpeechTask last = this.getLastTask(type);
        if (last == null) {
            this.tasks.put(type, task);
        } else {
            last.next = task;
        }
    }

    public boolean checkTaskType(SpeechTask.TaskType type) {
        return (this.type & SpeechTask.TaskType.getValue(type)) != 0;
    }

    public SpeechTask getTaskInstance(SpeechTask.TaskType type) {
        SpeechTask task = new SpeechTask(type, this.chkey, this.timestamp);
        switch (type) {
            case PICK:
                break;

            case ASR: {
                task.request.code = this.code;
            } break;
            case MT: {
                task.request.code = this.code;
                task.request.dstCode = this.dstCode;
            } break;
            case TTS: {
                task.request.code = this.dstCode;
            } break;
            case PLAY:
                break;
        }
        return task;
    }

    public SentenceParser sentenceParser = new SentenceParser();

    public static class SentenceParser {
        private static final String BreakRegex = "(?!\\d+)[.!?;。！？；](?!\\d+)"; // 简单地排除小数点数字, 类似: 9.8
        private static final String CommaBreakRegex = "(?!\\d+)[,，](?!\\d+)"; // 简单地排除小数点数字, 类似: 9.8
        private static final String PrefixPuncRegex = "^[,.!?;，。！？；]+";

        public void hasNewSentence(SpeechTask<Object, String> task, NewSentenceCallback callback) {
            boolean isLast = task.response.isLast;
//            boolean isFinished = task.response.isFinished;
            String asrText = task.response.data;
//            this.hasNewSentence(asrText, callback);
            this.hasNewSentence3(asrText, isLast, callback);
        }

        String cacheText = "";
        int Step = 30; // 处理间隔, 间隔30字节, 即中文10个字, 英文30个字母
        int additionStep = 0; // 额外间隔, 若未找到标点时, 增加间隔
        public void hasNewSentence3(String text, boolean isLast, NewSentenceCallback callback) {
            if (isLast) {
                String new_sentence = getRemainText(text.toLowerCase(), cacheText);
                if (new_sentence.isEmpty()) return;
                cacheText = cacheText + new_sentence;
                AiSpeechLogUtil.d(TAG, "语义断句 产生新句子: " + new_sentence + " 缓存句子: " + cacheText + " 当前句子(isLast=true): " + text);
                if (callback != null) callback.produce(new_sentence);
                cacheText = "";
                return;
            }

            // text.getBytes(StandardCharsets.UTF_8).length > 30
            // 1.文本字节数超过30, 进行搜索标点
            // 2.找到标点, 进行断句
            int lenText = text.getBytes(StandardCharsets.UTF_8).length;
            int lenCacheText = cacheText.getBytes(StandardCharsets.UTF_8).length;
            if (lenText - lenCacheText < Step + additionStep) {
                return;
            }

            AiSpeechLogUtil.d(TAG, "语义断句 将处理句子(" + lenCacheText + "->" + lenText + "), 缓存句子: " + cacheText + " 当前句子: " + text);
            // 从 text 分离出还未标点断句部分(即语义上的最新一句)
            String remainText = getRemainText(text.toLowerCase(), cacheText);
            // 获得剩余文本中从开头到最后一个标点之前的内容, 作为新句子
            int pos = findLastPunctuation(remainText);
            if (pos <= 0) {
                additionStep += Step;
                Log.d(TAG, "语义断句 未找到新标点的句子, 当前句子: " + text);
                return;
            }
            String new_sentence = remainText.substring(0, pos);
            AiSpeechLogUtil.d(TAG, "语义断句 产生新句子: " + new_sentence + " 缓存句子: " + cacheText + " 当前句子: " + text);
            // 缓存新句子
            cacheText = cacheText + new_sentence;
            additionStep = 0;
            if (callback != null) callback.produce(new_sentence);
        }

        int findLastPunctuation(String text) {
            int lastIndex = findPeriodPunctuation(text);
            if (lastIndex > 0) return lastIndex;
            lastIndex = findMultilCommaPunctuation(text);
            return lastIndex;
        }

        int findPeriodPunctuation(String text) {
            Pattern p = Pattern.compile(BreakRegex);
            Matcher m = p.matcher(text); // 获取 matcher 对象
            int lastIndex = -1;
            int count = 0;
            while (m.find()) {
                count++;
                lastIndex = m.end();
            }
            return count > 0 ? lastIndex : -1;
        }

        int findMultilCommaPunctuation(String text) {
            Pattern p = Pattern.compile(CommaBreakRegex);
            Matcher m = p.matcher(text); // 获取 matcher 对象
            int lastIndex = -1;
            int count = 0;
            while (m.find()) {
                count++;
                lastIndex = m.end();
            }
            if (lastIndex < 0) return -1;
            if (count <= 2) return -1;

            // 平均长度需大于 Step / 2 字节
            String txt = text.substring(0, lastIndex);
            if (txt.getBytes().length / count > Step / 2) return lastIndex;
            return -1;
        }

        // 删除txt1中，与txt2中最相似的那部分，并返回剩余的字符串
        // (这里是将txt1先分割，然后按照0...i的顺序，找到与txt2最相似的那部分，然后将其删除)
        public String getRemainText(String txt1, String txt2) {
            long startTime = System.currentTimeMillis(); // 实际花的时间

            String text1 = txt1.toLowerCase();
            String text2 = txt2.toLowerCase();
            if (txt2.isEmpty()) return text1;
            if (txt1.equals(txt2)) return "";
            String mostSameText = "";
            float maxSameValue = 0f;
            int start = txt2.length() > Step ? txt2.length() - Step : 0; // 从前一个开始找
            int pos = 0;

            // 相似度: 0.35714287 if i can,
            // 相似度: 0.71428573 if i can, odin promi
            // 相似度: 0.8666667 if i can, odin promised. or yo
            // 相似度: 0.675 if i can, odin promised. or you can, we
            // 类似如上，需要从 0.71428573 -> 0.675 之间找到, 相似度的最大值就在这个区间

            // 往一个方向找到连续
            // 由大粒度到小粒度
            int section = 6; // 从一个大步伐中拆分出若干部分
            int step = Step / section;
            int end = start;
            for (int i = 1; i < section * 2; i++) { // 从一个大步伐中拆分出若干部分
                if (start + step * i > txt1.length()) {
                    end = txt1.length();
                    break;
                }
                String subText = txt1.substring(0, start + step * i);
                float sameValue = getSimilarity(subText, text2); //获取text1和text2的相似度
                if (maxSameValue <= sameValue) {
                    maxSameValue = sameValue;
                } else {
                    end = start + step * i;
                    break;
                }
            }

            int lianxuDown = 0;
            maxSameValue = 0.0f;
            float lastSameValue = maxSameValue;
            for (int i = Math.max(end - (step * 2), 0); i < end; i++) {
                String subText = txt1.substring(0, i);
                float sameValue = getSimilarity(subText, text2); //获取text1和text2的相似度
                if (sameValue > maxSameValue) {
                    maxSameValue = sameValue;
                    mostSameText = subText;
                    pos = i;
                } else {
                    if (lastSameValue > sameValue) {
                        lianxuDown++;
                    }
                }
                if (lianxuDown == 2) {
                    break;
                }
                lastSameValue = sameValue;
            }
//            String ret = text1.replace(mostSameText, "");
            String ret = text1.substring(pos);
            long costTime = System.currentTimeMillis() - startTime; // 实际花的时间
            Log.d(TAG, " 花费时间: " + costTime + " 剩余文本: " + ret);
            ret = deleteLeadingPunc(ret); // 去掉前置的差异标点
            return ret;
        }

        // 通过将两个text转小写，然后编辑距离进行比较，返回两个文本的相似度
        public float getSimilarity(String txt1, String txt2) {
            String text1 = txt1.toLowerCase(Locale.getDefault());
            String text2 = txt2.toLowerCase(Locale.getDefault());
            int editDistance = getEditDistance(text1, text2); //假设你已经实现了这个函数
            float similarity = 1 - (float) editDistance / Math.max(text1.length(), text2.length());
//            Log.d(TAG, "相似度: " + similarity);
            return similarity;
        }



        // 编辑距离
        private static int getEditDistance(String word1, String word2) {
            int len1 = word1.length();
            int len2 = word2.length();
            // dp[i][j] 表示 word1 的前 i 个字符转换成 word2 的前 j 个字符所使用的最少操作数
            int[][] dp = new int[len1 + 1][len2 + 1];
            // 初始化第一列，即 word1 的前 i 个字符转换成空字符串所使用的最少操作数，即删除操作
            for (int i = 0; i <= len1; i++) {
                dp[i][0] = i;
            }
            // 初始化第一行，即空字符串转换成 word2 的前 j 个字符所使用的最少操作数，即插入操作
            for (int j = 0; j <= len2; j++) {
                dp[0][j] = j;
            }
            for (int i = 1; i <= len1; i++) {
                for (int j = 1; j <= len2; j++) {
                    if (word1.charAt(i - 1) == word2.charAt(j - 1)) {
                        dp[i][j] = dp[i - 1][j - 1];
                    } else {
                        dp[i][j] = Math.min(dp[i - 1][j], Math.min(dp[i][j - 1], dp[i - 1][j - 1])) + 1;
                    }
                }
            }
            return dp[len1][len2];
        }

        int findFirstPunctuation(String text) {
//        Matcher m = text.matches("[\\\\p{P}]");
            Pattern p = Pattern.compile(BreakRegex);
            Matcher m = p.matcher(text); // 获取 matcher 对象
            if (m.find()) {
                return m.end();
            }
            return -1;
        }

        String deleteLeadingPunc(String inputString) {
            Pattern pattern = Pattern.compile(PrefixPuncRegex);
            Matcher matcher = pattern.matcher(inputString);

            // 替换匹配到的头尾空格和点号
            return matcher.replaceAll("");
        }

        public interface NewSentenceCallback {
            void produce(String sentence);
        }
    }

    public static void main(String[] args) {
        SpeechTask.TaskType type = SpeechTask.TaskType.PICK;
        System.out.println(type);
    }
}
