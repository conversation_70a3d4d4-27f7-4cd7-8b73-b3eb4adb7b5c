package co.timekettle.speech.service;


import android.app.Notification;
import android.app.NotificationChannel;
import android.app.NotificationManager;
import android.app.Service;
import android.content.Context;
import android.content.Intent;
import android.graphics.BitmapFactory;
import android.os.Build;
import android.os.IBinder;

import androidx.annotation.RequiresApi;
import androidx.core.app.NotificationCompat;

import co.timekettle.speech.RecordManager;
import co.timekettle.speech.R;
import co.timekettle.speech.utils.AiSpeechLogUtil;

import java.util.HashMap;


// 录音服务（前台），避免随身练的录音被中断
public class RecordService extends Service {

    public final static String ACTION_TYPE = "ACTION_TYPE";
    public final static int ACTION_START_RECORDING = 1;
    public final static int ACTION_STOP_RECORDING = 2;

    private final static String CHANNEL_NAME = "Timekettle Recording"; // 应用详情->通知->可以在通知列表里面看到这个
    private final static String CHANNEL_ID = "co.timekettle.speech.channelId";

    private static final int RECORD_NOTIFICATION_ID = 9001;
    private NotificationManager notificationManager; // 用来管理通知的显示、消失

    private static final String TAG = "RecordService";
    public static boolean serviceIsLive = false;// 标记服务是否开启

    private static String recorderName;
    private static HashMap<String, Object> recorderOptions;

    @Override

    public IBinder onBind(Intent intent) {
        return null;
    }

    @Override
    public void onCreate() {
        super.onCreate();
        AiSpeechLogUtil.e(TAG, "onCreate");
    }

    @RequiresApi(api = Build.VERSION_CODES.O)
    @Override
    public int onStartCommand(Intent intent, int flags, int startId) {
        AiSpeechLogUtil.e(TAG, "onStartCommand");
        if (serviceIsLive) return super.onStartCommand(intent, flags, startId);
        notificationManager = (NotificationManager) getSystemService(Context.NOTIFICATION_SERVICE);
        Notification notification = generateRecordNotification();
        startForeground(RECORD_NOTIFICATION_ID, notification);
        RecordService.serviceIsLive = true;
        RecordManager.shareInstance().start(recorderName, recorderOptions);
        return super.onStartCommand(intent, flags, startId);
    }

    @Override
    public void onDestroy() {
        AiSpeechLogUtil.e(TAG, "onDestroy");
        serviceIsLive = false;
        stopForeground(true);
        super.onDestroy();
    }


    // 参考：https://www.cnblogs.com/jqnl/p/12599905.html
    private Notification generateRecordNotification() {
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
            createNotificationChannel(CHANNEL_ID, CHANNEL_NAME);
        }
        // builder用来创建通知、设置通知的属性
        NotificationCompat.Builder builder = new NotificationCompat.Builder(this, CHANNEL_ID);
        builder.setWhen(System.currentTimeMillis());
        builder.setSmallIcon(R.mipmap.logo); // 小图标
        builder.setLargeIcon(BitmapFactory.decodeResource(getResources(), R.mipmap.logo)); //大图标
        builder.setContentTitle(getResources().getString(R.string.notification_title));
        builder.setContentText(getResources().getString(R.string.notification_content));
        builder.setSound(null);
        return builder.build();
    }

    @RequiresApi(Build.VERSION_CODES.O)
    private void createNotificationChannel(String channelId, String channelName) {
        if (notificationManager == null) return;
        NotificationChannel channel = new NotificationChannel(channelId, channelName, NotificationManager.IMPORTANCE_DEFAULT);
        channel.setSound(null,null);
        channel.setShowBadge(false);
        notificationManager.createNotificationChannel(channel);
    }


    public static void start(Context context, String name, HashMap<String, Object> options) {
        Intent intent = new Intent(context, RecordService.class);
        recorderName = name;
        recorderOptions = options;
//        intent.putExtra(ACTION_TYPE, ACTION_START_RECORDING);
        context.startService(intent);
    }

    public static void stop(Context context) {
        Intent intent = new Intent(context, RecordService.class);
//        intent.putExtra(ACTION_TYPE, ACTION_STOP_RECORDING);
        context.stopService(intent);
    }
}
