package co.timekettle.speech.jni;

import android.util.Log;

import co.timekettle.speech.ispeech.utils.ShortRingBuffer;

public class SoundStretchJni {
    private static final String TAG = "SoundStretchJni";

    static {
        System.loadLibrary("tmk_sound_stretch");
    }

    public SoundStretchJni(int sampleRate) {
        create(sampleRate, 1);
    }

    public SoundStretchJni() {
        create(16000, 1);
    }

    @Override
    protected void finalize() throws Throwable {
        super.finalize();
        destory();
    }

    public boolean checkEqual(float a, float b) {
        float epsilon = 0.1f; // 定义误差范围
        return Math.abs(a - b) <= epsilon;
    }

    private final int nSampleInBuff = 256;
    private final int BUFF_SIZE = 256 * 2;

    public short[] stretch(final short[] samples, float factor) {
        if (checkEqual(factor, 1.0f)) return samples;
        else if (factor > 3.0) factor = 3.0f;
        else if (factor < 0.5) factor = 0.5f;
        updateTempo(factor);

        ShortRingBuffer output = new ShortRingBuffer(samples.length);
        float[] sampleBuffer = new float[nSampleInBuff];

        short[] inputData = new short[nSampleInBuff];
        short[] outputData = new short[nSampleInBuff];

        int nCount = samples.length / nSampleInBuff;
        int nByteRemain = samples.length % nSampleInBuff;
        nCount = nCount + 1;
        for (int i = 0; i < nCount; i++) {
            System.arraycopy(samples, i * nSampleInBuff, inputData, 0, i == nCount - 1 ? nByteRemain : nSampleInBuff);

            for (int j = 0; j < nSampleInBuff; j++) sampleBuffer[j] = inputData[j] / 32768.0f;

            // Feed the samples into SoundTouch processor
            putSamples(sampleBuffer, sampleBuffer.length);


            // Read ready samples from SoundTouch processor & write them output file.
            // NOTES:
            // - 'receiveSamples' doesn't necessarily return any samples at all
            //   during some rounds!
            // - On the other hand, during some round 'receiveSamples' may have more
            //   ready samples than would fit into 'sampleBuffer', and for this reason
            //   the 'receiveSamples' call is iterated for as many times as it
            //   outputs samples.
            int nSamples = 0;
            do
            {
                nSamples = receiveSamples(sampleBuffer);
//                Log.d(TAG, "输出数据采样数: " + nSamples);

                for (int j = 0; j < nSamples; j++) outputData[j] = (short)(sampleBuffer[j] * 32768.0f);

                output.write(outputData, 0, nSamples);
            } while (nSamples != 0);
        }

        flush();
        int nSamples = 0;
        do
        {
            nSamples = receiveSamples(sampleBuffer);
            for (int j = 0; j < nSamples; j++) outputData[j] = (short)(sampleBuffer[j] * 32768.0f);
            output.write(outputData, 0, nSamples);
        } while (nSamples != 0);
        clear();
        return output.toShortArray();
    }

    private long mNativeSoundStretch; // 关联 c++ 指针
    native void updateTempo(float factor);
    native long create(int sampleRate, int nChannel);
    native void putSamples(float[] samples, int len);
    native int receiveSamples(float[] samples);
    native void flush();
    native void clear();
    native void destory();
}
