package co.timekettle.speech.jni;


public class AudioVadJni {
    static {
        System.loadLibrary("tmk_vad");
    }

    public AudioVadJni(int sampleRate) {
        createVad(sampleRate, 0.9f);
    }

    public AudioVadJni() {
        createVad(16000, 0.9f);
    }

    @Override
    protected void finalize() throws Throwable {
        super.finalize();
        destoryVad();
    }

    public boolean isVoice(final short[] samples, long[] energy) {
        return isVoise(samples, energy);
    }

    public long getEnergy(final short[] samples) {
        long out_energy = 0;
        for (short sample : samples) {
            out_energy += Math.abs(sample);
        }
        return out_energy;
    }

    private long mNativeVad; // 关联 c++ 指针
    native long createVad(int sampleRate, float threhold);
    native boolean isVoise(short[] samples, long[] energy);
    native void destoryVad();
}