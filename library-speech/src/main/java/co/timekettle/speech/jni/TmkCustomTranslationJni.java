package co.timekettle.speech.jni;

import android.util.Log;

import androidx.annotation.NonNull;

import java.io.File;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Vector;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

import com.sun.jna.Library;
import com.sun.jna.Native;
import com.sun.jna.Platform;
import com.sun.jna.Pointer;
import com.sun.jna.Structure;

import co.timekettle.speech.utils.AiSpeechLogUtil;

public class TmkCustomTranslationJni {
   static {
      System.loadLibrary("tmk_custom_translation");
   }
   final static String TAG = "TmkCustomTranslationJni";
   public static class MatchedTextAndPositionsJna extends Structure {
      public static class ByReference extends MatchedTextAndPositionsJna implements Structure.ByReference {}

      public static class ByValue extends MatchedTextAndPositionsJna implements Structure.ByValue {}

      public String ref_word;
      public int ref_pos;
      public String tgt_word;

      @Override
      protected List<String> getFieldOrder() {
         return Arrays.asList("ref_word", "ref_pos", "tgt_word");
      }
   }

   public static class MatchedResultJna extends Structure {
      public static class ByReference extends MatchedResultJna implements Structure.ByReference {}

      public static class ByValue extends MatchedResultJna implements Structure.ByValue {}

      public String original_text;
      public String result_text;
      public int matched_cnt;
      public MatchedTextAndPositionsJna.ByValue matched_results;  // Use Pointer for array

      @Override
      protected List<String> getFieldOrder() {
         return Arrays.asList("original_text", "result_text", "matched_cnt", "matched_results");
      }
   }

   public interface TmkCustomTranslationLibrary extends Library {
      TmkCustomTranslationLibrary INSTANCE = (TmkCustomTranslationLibrary) Native.load("tmk_custom_translation", TmkCustomTranslationLibrary.class);
      Pointer create_tcaf(String code, String uModelPath, String[] words, int words_length);
      MatchedResultJna.ByValue process_tcaf(Pointer tcaf, String text);
      void destroy_caf(Pointer caf);
   }

   public class TmkCustomTranslationMatchResult {
      public String oriWord; // 原始句中的词语
      public int oriStart; // 原始句子开始下标
      public int oriLength; // 长度

      public String crWord; // 词表中匹配的相同发音的词语
      public int crStart; // 自定义识别词开始下标
      public int crLength; // 自定义识别词长度

      public String ctWord; // 词表中匹配的目标词语
      public int ctStart; // 自定义翻译词开始下标
      public int ctLength; // 自定义翻译词长度
   }

   public class TmkCustomTranslationResult {
      public String oriText; // 原始文本
      public String crText; // 发音匹配文本, 自定义识别文本(CustomRecognize)
      public String ctText; // 自定义翻译文本(CustomTranslation)

      public List<TmkCustomTranslationMatchResult> mrs;

      @NonNull
      @Override
      public String toString() {
         return "TmkCustomTranslationResult{" +
                 "oriText='" + oriText + "'" +
                 ", crText='" + crText + "'(" + toCrOffsetString() +
                 "), ctText='" + ctText + "'(" + toCtOffsetString() +
                 '}';
      }

      public String toCrOffsetString() {
         StringBuilder offset = new StringBuilder();
         for (TmkCustomTranslationMatchResult ctmr : mrs) {
            offset.append(ctmr.crWord).append("(loc:").append(ctmr.crStart).append(", len:").append(ctmr.crLength).append("), ");
         }
         return offset.toString();
      }

      public String toCtOffsetString() {
         StringBuilder offset = new StringBuilder();
         for (TmkCustomTranslationMatchResult ctmr : mrs) {
            offset.append(ctmr.ctWord).append("(loc:").append(ctmr.ctStart).append(", len:").append(ctmr.ctLength).append("), ");
         }
         return offset.toString();
      }
   }

   public class TmkMatchedResultJnaStruct {
      private String oriText; // 原始文本
      private String crText; // 发音匹配文本, 自定义识别文本(CustomRecognize)
      private String ctText; // 自定义翻译文本(CustomTranslation)

      private List<TmkCustomTranslationMatchResult> mrs;
   }

   private final String code;
   private String modelPath;
   private Map<String, String> words;
   private Map<String, String> sdkWords; // 转换小写, 去除特殊符号"."等, 供发音匹配sdk使用

   public TmkCustomTranslationJni(String code) {
      this.code = code;
   }

   /**
    * 加载模型
    * @param modelPath 语言发音模型
    * @return YES 成功, NO 失败
    */
   public boolean load(String modelPath) {
      /* FIXME: 最好是通过模型先创建实例 */
      File file = new File(modelPath);
      if (file.exists()) {
         this.modelPath = modelPath;
      }
      return this.modelPath != null;
   }

   /**
    * 更新词表(由于库版本限制, 目前在此处根据模型创建实例)
    * @param words 词表不允许存在其他语言的词, 如 zh (code)词表不允许存在英文词
    * @return YES 成功, NO 失败
    */
   public boolean update(Map<String, String> words) throws Exception {
      if (words == null || words.isEmpty()) {
         throw new Exception("Words are empty");
      }
      if (this.code == null) {
         throw new Exception("Current code is null");
      }
      if (this.modelPath == null) {
         throw new Exception("Model file does not exist");
      }

      Map<String, String> _words = new HashMap<>();
      for (Map.Entry<String, String> entry : words.entrySet()) {
         String key = entry.getKey();
         String value = entry.getValue();
         key = deleteLeadingTrailingSpacesAndDots(key);
         value = deleteLeadingTrailingSpacesAndDots(value);
         _words.put(deleteExtraSpace(key), deleteExtraSpace(value));
      }
      this.words = _words;

      Map<String, String> temp = new HashMap<>();
      for (Map.Entry<String, String> entry : this.words.entrySet()) {
         String key = entry.getKey();
         temp.put(key.toLowerCase(), key);
      }
      this.sdkWords = temp;

      synchronized (this) {
         mNativeCaf = createCaf(this.code, this.modelPath, (this.sdkWords != null ? this.sdkWords : this.words).keySet().toArray(new String[]{}));
      }
      return true;
   }

   public boolean isValid() {
      synchronized (this) {
         return mNativeCaf != 0;
      }
   }

   private final boolean useSdkOffset = false;
   public TmkCustomTranslationResult process(String text) throws Exception {
      if (!isValid()) {
         throw new Exception("Check model file and words");
      }

      // 删除多余空格
      text = this.deleteExtraSpace(text);

      // 将字符串转换为小写形式
      String sdkText = this.sdkWords != null ? text.toLowerCase() : text;

      // 在标点符号附近添加空格
      sdkText = this.addSpaceNearPunctuation(sdkText);

      MatchedResultJni mr = processCaf(sdkText);
      if (mr.matched_cnt <= 0) {
         return null;
      }

      List<TmkCustomTranslationMatchResult> mrs = new ArrayList<>(); // 匹配结果
      TmkCustomTranslationResult ctr = new TmkCustomTranslationResult();
      ctr.oriText = text;
      ctr.crText = sdkText;
      ctr.ctText = sdkText;
      ctr.mrs = mrs;

      int crExtraOffset = 0;
      int ctExtraOffset = 0;
      int crSearchStartIndex = 0;
      int ctSearchStartIndex = 0;
      for (int i = 0; i < mr.matched_cnt; i++) {
         String refword = mr.matched_results[i].ref_word;
         String tgtword = mr.matched_results[i].tgt_word;
//         AiSpeechLogUtil.d(TAG, mr.matched_results[i].ref_pos + "(" + tgtword.length() + ")");

         TmkCustomTranslationMatchResult ctmr = new TmkCustomTranslationMatchResult();

         ctmr.oriWord = refword;
         ctmr.oriStart = mr.matched_results[i].ref_pos;
         ctmr.oriLength = refword.length();

         ctmr.crWord = this.sdkWords != null ? this.sdkWords.get(tgtword) : tgtword;
         ctmr.crStart = mr.matched_results[i].ref_pos;
         ctmr.crLength = ctmr.crWord != null ? ctmr.crWord.length() : 0;
         if (ctmr.crWord != null && crSearchStartIndex < ctr.crText.length()) {
            if (useSdkOffset) {
               ctr.crText = String.valueOf(new StringBuilder(ctr.crText).replace(ctmr.crStart + crExtraOffset, ctmr.crStart + crExtraOffset + ctmr.oriLength, ctmr.crWord));
               ctmr.crStart = ctmr.crStart + ctExtraOffset; // 更新下标
               crExtraOffset = crExtraOffset + ctmr.crLength - ctmr.oriLength;
            } else {
               // 查找计算下标
               ctr.crText = ctr.crText.replace(refword, !this.code.equals("zh") ? ctmr.crWord+ " " : ctmr.crWord);
               int index = ctr.crText.indexOf(ctmr.crWord, crSearchStartIndex);
               if (index != -1) {
                  ctmr.crStart = index; // 更新下标
                  crSearchStartIndex = index + ctmr.ctLength;
               }
            }
         }

         ctmr.ctWord = words.get(ctmr.crWord);
         ctmr.ctStart = mr.matched_results[i].ref_pos;
         ctmr.ctLength = ctmr.ctWord != null ? ctmr.ctWord.length() : 0;
         if (ctmr.ctWord != null && ctSearchStartIndex < ctr.ctText.length()) {
            if (useSdkOffset) {
               ctr.ctText = String.valueOf(new StringBuilder(ctr.ctText).replace(ctmr.ctStart + ctExtraOffset, ctmr.ctStart + ctExtraOffset + ctmr.oriLength, ctmr.ctWord + " "));   // 增加额外的空格
               ctmr.ctStart = ctmr.ctStart + ctExtraOffset; // 更新下标
               ctExtraOffset = ctExtraOffset + ctmr.ctLength - ctmr.oriLength + 1;
            } else {
               // 查找计算下标
               ctr.ctText = ctr.ctText.replace(refword, ctmr.ctWord + " ");
               int index = ctr.ctText.indexOf(ctmr.ctWord, ctSearchStartIndex);
               if (index != -1) {
                  ctmr.ctStart = index; // 更新下标
                  ctSearchStartIndex = index + ctmr.ctLength + 1;
               }
            }
         }
         mrs.add(ctmr);
      }
      ctr.crText = this.removeExtraSpacesNearPunctuation(ctr.crText);
      return ctr;
   }


   // Jni 层返回对象
   class MatchedTextAndPositionsJni {
      public String ref_word;
      public int ref_pos;
      public String tgt_word;
   }

   // Jni 层返回对象
   class MatchedResultJni {
      public String original_text;
      public String result_text;
      public int matched_cnt;
      public MatchedTextAndPositionsJni[] matched_results;
   }

   private String addSpaceNearPunctuation(String input) {
      // 使用正则表达式匹配连续多个空格、标点符号及连字符
      String pattern = "[\\p{P}-['-]]";
      Pattern regex = Pattern.compile(pattern);
      Matcher matcher = regex.matcher(input);

      // 在原始字符串中匹配标点后旁边加空格
      StringBuilder modifiedStringBuilder = new StringBuilder();
      int lastEnd = 0;
      while (matcher.find()) {
         // 在匹配到的标点之前添加空格
         modifiedStringBuilder.append(input, lastEnd, matcher.start()).append(" ").append(matcher.group()).append(" ");
         lastEnd = matcher.end();
      }
      modifiedStringBuilder.append(input.substring(lastEnd));
      return modifiedStringBuilder.toString();
   }

   private String deleteExtraSpace(String input) {
      // 删除头尾多余空格
      input = input.trim();

      // 使用正则表达式匹配连续多个空格
      String pattern = "\\s+";
      Pattern regex = Pattern.compile(pattern);
      Matcher matcher = regex.matcher(input);

      // 替换匹配到的空格为一个空格
      return matcher.replaceAll(" ");
   }

   private String removeExtraSpacesNearPunctuation(String input) {
      // 使用正则表达式匹配零或多个空格，后跟一个标点符号，再跟零或多个空格
      String pattern = "\\s*([\\p{P}-['-]])\\s*";
      Pattern regex = Pattern.compile(pattern);
      Matcher matcher = regex.matcher(input);

      // 替换匹配到的标点为它们自己和空格
      StringBuffer modifiedStringBuffer = new StringBuffer();

      while (matcher.find()) {
         matcher.appendReplacement(modifiedStringBuffer, matcher.group(1) + " ");
      }
      matcher.appendTail(modifiedStringBuffer);

      return modifiedStringBuffer.toString();
   }

   public static String deleteLeadingTrailingSpacesAndDots(String inputString) {
      String regex = "^[\\s.]+|[\\s.]+$";
      Pattern pattern = Pattern.compile(regex);
      Matcher matcher = pattern.matcher(inputString);

      // 替换匹配到的头尾空格和点号
      return matcher.replaceAll("");
   }
   private long mNativeCaf; // 关联 c++ 指针
   native long createCaf(String code, String modelPath, String[] words);
   native MatchedResultJni processCaf(String text);
   native void destoryCaf();

   public static void main(String[] args) {
//      String path = this.getExternalCacheDir().getAbsolutePath() + "/mandarin_2_4_4.fst";
//      Pointer pointer = TmkCustomTranslationJni.TmkCustomTranslationLibrary.INSTANCE.create_tcaf("zh", path, new String[]{"时空壶", "你好"}, 2);
//      TmkCustomTranslationJni.MatchedResultJna.ByValue mr = TmkCustomTranslationJni.TmkCustomTranslationLibrary.INSTANCE.process_tcaf(pointer, "时空虎我曾经有一次去时空胡公司参观过");
//      Log.d(TAG, "onCreate: 匹配: " + mr.original_text);
//      Log.d(TAG, "onCreate: 匹配: " + mr.result_text);
//      Log.d(TAG, "onCreate: 匹配: " + mr.matched_cnt);
////        TmkCustomTranslationJni.MatchedTextAndPositionsJna.ByValue[] matchedResultsArray = (TmkCustomTranslationJni.MatchedTextAndPositionsJna.ByValue[]) new TmkCustomTranslationJni.MatchedTextAndPositionsJna.ByValue(mr.matched_results).toArray(mr.matched_cnt);
////        for (int i = 0; i < mr.matched_cnt; i++) {
////            matchedResultsArray[i].read();
////            // Now you can access the fields of matchedResultsArray[i]
////            Log.d(TAG, "onCreate: 匹配: " + matchedResultsArray[i].ref_word);
////            Log.d(TAG, "onCreate: 匹配: " + matchedResultsArray[i].tgt_word);
////            Log.d(TAG, "onCreate: 匹配: " + matchedResultsArray[i].ref_pos);
////        }
//      if (mr.matched_results != null && mr.matched_cnt > 0) {
////            TmkCustomTranslationJni.MatchedTextAndPositionsJna[] matchedResultsArray =
////                    (TmkCustomTranslationJni.MatchedTextAndPositionsJna[]) new TmkCustomTranslationJni.MatchedTextAndPositionsJna(mr.matched_results).toArray(mr.matched_cnt);
//
////            for (int i = 0; i < mr.matched_cnt; i++) {
////                TmkCustomTranslationJni.MatchedTextAndPositionsJna matchedResult = matchedResultsArray[i];
//         TmkCustomTranslationJni.MatchedTextAndPositionsJna.ByValue matchedResult = mr.matched_results;
//         Log.d(TAG, "Ref word: " + matchedResult.ref_word);
//         Log.d(TAG, "Ref pos: " + matchedResult.ref_pos);
//         Log.d(TAG, "Tgt word: " + matchedResult.tgt_word);
////            }
//      }
//      TmkCustomTranslationJni.TmkCustomTranslationLibrary.INSTANCE.destroy_caf(pointer);
   }
}
