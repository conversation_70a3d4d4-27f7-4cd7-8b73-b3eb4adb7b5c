package co.timekettle.speech;

import java.util.Map;

public class SpeechRequest<T> {
    /*
    * 需要转换的实体, 如需要识别的音频, 需要翻译的文本, 或者需要合成的文本
    * */
    public T data;

    /*
    * 模块名称, 具体工作的模块, 识别, 翻译, 合成的模块/引擎, 如翻译使用讯飞离线, 或者后端聚合引擎; speaker 类型(mic, headset 等)
    * 可以指定也可以由管理类选择, 若指定的话, 则优先选择指定模块
    * */
    public String module;

    public String code; // 识别, 合成的 code, 翻译的源 code
    public String dstCode; // 只用于翻译任务, 翻译的 目标 code


    /*
    * 其他参数, 如ble播放的设备
    * {
    *   devices: [], // 播放的设备
    *   extDevice: x, // 是否除 extDevice 之外的设备播放
    * }
    * */
//    public HashMap options;
    public Object extDevice; // 用于播放, 是否除 extDevice 之外的设备播放

    public Map<String, Object> opts;

    /**
    * 值可以是: "Female", "Male"
    * */
    public static String OptsKeyTtsVoiceName = "OptsTtsVoiceName";
}
