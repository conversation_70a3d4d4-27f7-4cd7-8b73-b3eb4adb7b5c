package co.timekettle.speech;

public class AudioChannelOptions {
    public static final String KEY = "key"; // 通道 key
    public static final String SRC_CODE = "srcCode"; // 源语言 code
    public static final String DST_CODE = "dstCode"; // 目标语言 code
    public static final String ROLE = "role"; // 通道角色, 已方/他方, 假如设备比作翻译员的话, 如对于中<->英来说 self 角色相当于中文翻译员, 对于英<->西来说 self 角色相当于英文翻译员角色
    public static final String RECORDER = "recorder"; // 录音模块
    public static final String SPEAKER_TYPE = "speakerType"; // 播放模块
    public static final String MIN_VAD_ENERGY = "minVadEnergy"; //
    public static final String VAD_BEGIN = "vadBegin"; // 16ms/包
    public static final String VAD_END = "vadEnd"; // 16ms/包
    public static final String VAD_DetectPattern = "VAD_DetectPattern"; // vad 检测模式
    public static final String GainToEngine = "GainToEngine"; // 送引擎前加的增益
//    public static final String CAN_RECORD_WHEN_SPEAKING = "canRecordWhenSpeaking"; // 播放的时候是否允许录音
    public static final String CAN_HEAR_ECHO = "canHearEcho"; // 是否能听到回声, 如已方是否能听到目标语言合成的声音
    public static final String WRITE_TO_FILE = "writeToFile"; // 是否写成录音文件
    public static final String SHOULD_GEN_VAD_TAG = "shouldGenVadTag"; // 在通道录音文件基础上是否生成额外的 vad 标记文件

    public static final String IS_STREAMMT = "isStreamMt"; // 是否开启流式翻译

    public static final String BYTE_PER_PACKET = "bytePerPacket"; // 送入通道的每包数据的字节大小
}
