package co.timekettle.speech.utils;

import android.content.Context;
import android.util.Log;

import java.io.File;
import java.io.FileOutputStream;
import java.io.IOException;
import java.io.OutputStream;
import java.text.DateFormat;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Comparator;
import java.util.Date;
import java.util.Locale;

import co.timekettle.tmkengine.utils.TmkLogger;

public class AiSpeechLogUtil {
   static int logLevel = 0; // 0 表示关闭, 1 表示 error 级别, 2 表示 debug 级别
   public static SpeechLogCallback mCallback = null; // 根据设置等级进行回调

   /**
    * 日志级别, 调整开关
    * @param l 0 表示关闭, 1 表示 error 级别, 2 表示 debug 级别
    */
   public static void setLogLevel(int l) {
      logLevel = l;
      if (logLevel > 0) {
         TmkLogger.setLogCallback((int level, String tag, String msg) -> {
            if (level == 1) e(tag, msg);
            else if (level == 2) d(tag, msg);
         });
      } else {
         TmkLogger.setLogCallback(null);
      }
   }

   /**
    * 日志回调
    * @param callback 回调函数
    * @deprecated 不再回调日志, 日志已分层记录
    */
   @Deprecated
   public static void setLogCallback(SpeechLogCallback callback) {
//      mCallback = callback;
//
//      TmkLogger.setLogCallback((int level, String tag, String msg) -> {
//         if (filelogger != null) filelogger.writeLog(tag + " " + msg);
//         if (mCallback != null) {
//            mCallback.invoke(level, tag, msg, null);
//         }
//      });
   }

   public static int d(String tag, String msg) {
      if (logLevel < 2) return 0;
      if (filelogger != null) filelogger.writeLog(tag + " " + msg);
      if (mCallback != null) {
         mCallback.invoke(2, tag, msg, null);
         return 0;
      } else {
         return Log.d(tag, msg);
      }
   }

   public static int d(String tag, String msg, Throwable tr) {
      if (logLevel < 2) return 0;
      if (filelogger != null) filelogger.writeLog(tag + " " + msg);
      if (mCallback != null) {
         mCallback.invoke(2, tag, msg, tr);
         return 0;
      } else {
         return Log.d(tag, msg, tr);
      }
   }

   public static int e(String tag, String msg) {
      if (logLevel < 1) return 0;
      if (filelogger != null) filelogger.writeLog(tag + " " + msg);
      if (mCallback != null) {
         mCallback.invoke(1, tag, msg, null);
         return 0;
      } else {
         return Log.e(tag, msg);
      }
   }

   public static int e(String tag, String msg, Throwable tr) {
      if (logLevel < 1) return 0;
      if (filelogger != null) filelogger.writeLog(tag + " " + msg);
      if (mCallback != null) {
         mCallback.invoke(1, tag, msg, tr);
         return 0;
      } else {
         return Log.e(tag, msg, tr);
      }
   }

   static FileLogger filelogger;
   static String RootDirPath;
   static boolean EnableFileLogger;
   private static boolean cacheAll = false;

   /**
    * 是否启用日志文件记录, 存放位置为 cache/Log 下, 由 toExternal 决定是否在外部还是内部存储空间
    * @param context 上下文
    * @param toExternal 是否是外部存储空间
    */
   public synchronized static void enableFileLogger(Context context, boolean toExternal) {
      String logDir = "/Log";
      if (toExternal) logDir = "/userlog";
      cacheAll = toExternal;
      RootDirPath = (toExternal ? context.getExternalCacheDir() : context.getCacheDir()).getAbsolutePath() + logDir;
      Log.d(FileLogger.TAG, "FileLogger: rootDirPath = " + RootDirPath);
      EnableFileLogger = true;
   }

   public synchronized static void disableFileLogger() {
      RootDirPath = null;
      EnableFileLogger = false;
   }

   public synchronized static void startFileLogger() {
      if (RootDirPath == null) {
         e("AiSpeechLogUtil", "异常: 日志根目录为空!");
         return;
      }
      filelogger = new FileLogger(RootDirPath);
      try {
         filelogger.start(cacheAll);
      } catch (IOException e) {
         e.printStackTrace();
         filelogger = null;
      }
   }

   public synchronized static void stopFileLogger() {
      filelogger = null;
   }

   public static class FileLogger {
      private static final String TAG = "library-speech";
      String dirNameTag = TAG; // 目录名字标识, 目录和文件包含此字符串
      String dirPath; // 日志目录路径
      File logFile = null;
      private final DateFormat dateFormat = new SimpleDateFormat("yyyyMMdd-HHmmss", Locale.getDefault());

      public FileLogger(Context context) {
         this(context, false);
      }

      public FileLogger(Context context, boolean toExternal) {
         // 指定所处根目录
         String logDir = "/Log";
         if (toExternal) logDir = "/userlog";
         String rootDirPath = (toExternal ? context.getExternalCacheDir() : context.getCacheDir()).getAbsolutePath() + logDir;
         Log.d(TAG, "FileLogger: rootDirPath = " + rootDirPath);
         this.dirPath = rootDirPath + "/" + this.dirNameTag + "/";
         this.logFile = null;
      }

      public FileLogger(String rootDirPath) {
         this.dirPath = rootDirPath + "/" + this.dirNameTag + "/";
         this.logFile = null;
      }

      private void cleanOldFile(boolean cacheAll) {
         // 清理旧目录
         File f = new File(this.dirPath);
         assert f.isDirectory() : "this.dirPath 路径需要为目录路径";

         ArrayList<File> items = new ArrayList<>();
         File[] subFiles = f.listFiles();
         if (subFiles != null) {
            for (File item : subFiles) {
               // 清理大小为 0 字节的文件
               if ((item.isDirectory() && item.length() <= 4096) || (item.isFile() && item.length() == 0)) {
                  item.delete();
                  continue;
               }
               if (item.getName().contains(this.dirNameTag)) items.add(item);
            }
         }
         if (items.isEmpty()) return;

         Log.d(TAG, "当前 enginelog 文件夹数量: " + items.size());
         items.sort(Comparator.comparingLong(File::lastModified).reversed());

         Log.d(TAG, "缓存所有文件开关 " + cacheAll);
         if (cacheAll) return;

         int nLeave = 20;
         for (int index = 0; index < items.size(); index++) {
            if (index >= nLeave - 1) {
               File element = items.get(index);
               element.delete();
            }
         }
         Log.d(TAG, "清理后 enginelog 文件夹数量" + items.size());
      }

      public void start(boolean cacheAll) throws IOException {
         if (this.logFile != null) {
            Log.e(TAG, "当前存在日志文件");
            return;
         }
         // 创建 enginelog 根目录
         File dirPathFile = new File(this.dirPath);
         boolean isExsitsRootDirPath = dirPathFile.exists();
         if (isExsitsRootDirPath) {
            this.cleanOldFile(cacheAll);
         } else dirPathFile.mkdirs();

         // 创建当前目录
         String curDirPath = this.dirPath + "/";
         new File(curDirPath).mkdirs();

         // 根据每个文件是否达到一定大小去创建日志
         Date date = new Date();
         String dateName = dateFormat.format(date);
         this.logFile = new File(curDirPath + dateName + "-" + this.dirNameTag + ".txt");
         File enginelogFile = this.logFile;
         enginelogFile.createNewFile();
         enginelogFile.setLastModified(date.getTime());
      }

      public void appendFile(File f, String content) {
         try {
            OutputStream outputStream = new FileOutputStream(f, true);
            outputStream.write(content.getBytes());
            outputStream.close();
         } catch (Exception ex) {
            ex.printStackTrace();
         }
      }

      public void stop() {
         this.logFile = null;
      }

      public void writeLog(String contents) {
         if (this.logFile == null) {
            Log.e(TAG, "logFile 为空");
            return;
         }
         appendFile(this.logFile, dateFormat.format(new Date()) + " " + contents + "\n");
      }
   }

   public interface SpeechLogCallback {
      void invoke(int level, String tag, String msg, Throwable tr);
   }
}
