package co.timekettle.speech.utils;

import java.util.Arrays;

public class RingBuffer {
    private byte[] array;
    private int readPos; // 读的起始位置
    private int writePos; // 写的起始位置
    private int remain; // 空余可写大小
    private int capacity;
    public RingBuffer(int capacity) {
        this.capacity = capacity;
        this.remain = capacity;
        array = new byte[capacity];
    }

    public synchronized void clear() {
        readPos = writePos = remain = 0;
        array = new byte[capacity];
    }

    public synchronized int remaining() {
//        if (readPos <= writePos) {
//            return array.length - (writePos - readPos);
//        } else {
//            return readPos - writePos;
//        }
        return remain;
    }

    public synchronized int readable() {
        return array.length - remaining();
    }

    private synchronized int reCapacity(int need) {
        if (need <= array.length) {
            return array.length;
        }
        int newCap = array.length;
        while (newCap < need) {
            newCap *= 2;
        }
        int addition = newCap - array.length; // 添加多少空间
        byte[] temp = new byte[newCap];
        if (readPos == writePos) {
            if (remain == 0) {
                int length = array.length - readPos;
                System.arraycopy(array, readPos, temp, 0, length);
                System.arraycopy(array, 0, temp, length, writePos);
                writePos = length + writePos;
                readPos = 0;
                array = temp;
            } else {
                writePos = 0;
                readPos = 0;
                array = temp;
//                throw new IllegalStateException(String.format("state error, don't reCapacity when buffer is empty, readPos = %s, writePos = %s, remain = %s", readPos, writePos, remain));
            }
        } else if (readPos < writePos) {
            int length = readable();
            System.arraycopy(array, readPos, temp, 0, length);
            readPos = 0;
            writePos = readPos + length;
            array = temp;
        } else {
            int length = array.length - readPos;
            System.arraycopy(array, readPos, temp, 0, length);
            System.arraycopy(array, 0, temp, length, writePos);
            writePos = length + writePos;
            readPos = 0;
            array = temp;
        }
        remain = remain + addition;
        return array.length;
    }

    public synchronized int write(byte[] data) {
        return write(data, 0, data.length);
    }

    public synchronized int write(byte[] data, int offset, int length) {
        int pre = this.readable();
        if (data == null || data.length == 0 || length == 0) return 0;
        if (offset < 0 || length < 0 || (offset + length > data.length)) {
            throw new IllegalArgumentException(String.format("params error , offset = %s,length = %s,data.length = %s", offset, length, data.length));
        }
        int remaining = remaining();
        if (length > remaining) {
            reCapacity(array.length + (length - remaining));
        }
        if (readPos <= writePos) {
            int wrote = Math.min(array.length - writePos, length);
            System.arraycopy(data, offset, array, writePos, wrote);
            writePos += wrote;
            if (length > wrote) {
                System.arraycopy(data, offset + wrote, array, 0, length - wrote);
                writePos = (length - wrote);
            }
        } else {
            System.arraycopy(data, offset, array, writePos, length);
            writePos += length;
        }
        remain -= length;

        return length;
    }

    public synchronized int peek(byte[] buff) {
        return peek(buff, 0, buff.length);
    }

    public synchronized int peek(byte[] buff, int offset, int length) {
        return read(buff, offset, length, true);
    }

    public synchronized int read(byte[] buff) {
        return read(buff, 0, buff.length, false);
    }

    public synchronized int read(byte[] buff, int offset, int length, boolean peek) {
        if (buff == null || buff.length == 0 || length == 0) return 0;
        if (offset < 0 || length < 0 || offset >= buff.length) {
            throw new IllegalArgumentException(String.format("params error , offset = %s,length = %s,buff.length = %s", offset, length, buff.length));
        }
        length = Math.min(readable(), length);
        length = Math.min(buff.length - offset, length);
        if (length > 0) {
            if (readPos < writePos) {
                System.arraycopy(array, readPos, buff, offset, length);
                if (!peek) {
                    readPos += length;
                }
            } else {
                int read = Math.min(array.length - readPos, length);
                System.arraycopy(array, readPos, buff, offset, read);
                if (!peek) {
                    readPos += read;
                }
                if (read < length) {
                    System.arraycopy(array, 0, buff, offset + read, length - read);
                    if (!peek) {
                        readPos = (length - read);
                    }
                }
            }
        }
        if (!peek) {
            remain += length;
        }
        return length;
    }

    public synchronized String dump() {
        String msg = "array.length = " + array.length + ",";
        msg += ("readPos = " + readPos + ",");
        msg += ("writePos = " + writePos + ",");
        msg += ("readable = " + readable() + ",");
        msg += ("remaining = " + remaining());
        return msg;
    }

    public static void main(String[] args) {
        if (true) {
            final RingBuffer buffer = new RingBuffer(8);
            final byte[] data = new byte[]{0, 1, 2, 3, 4, 5, 6, 7};
            buffer.write(data);
            buffer.read(data, 0, 2, false);
            System.out.println("dump : " + buffer.dump());
            buffer.write(data, 0, 2);
            System.out.println("dump : " + buffer.dump());
            buffer.write(data, 0, 2);
            System.out.println("dump : " + buffer.dump());
            return;
        }

        final RingBuffer buffer = new RingBuffer(8);
        final byte[] data = new byte[]{0, 1, 2, 3, 4, 5, 6, 7, 8, 9};
        buffer.write(data);
        System.out.println("write data : " + Arrays.toString(data));
        System.out.println("dump : " + buffer.dump());
        for (int i = 0; i < 10; i++) {
            int readable = buffer.readable();
            System.out.println("i = " + i + ",readable = " + buffer.readable());
            if (i % 3 == 0) {
                byte[] readBuff = new byte[readable];
                buffer.read(readBuff);
                System.out.println("i = " + i + ",readBuff = " + Arrays.toString(readBuff));
                System.out.println("dump : " + buffer.dump());
            } else {
                byte[] temp = data.clone();
                if (i % 2 == 0) {
                    temp = data.clone().clone();
                }
                System.out.println("i = " + i + ",write data : " + Arrays.toString(temp));
                buffer.write(temp);
                System.out.println("dump : " + buffer.dump());
            }
        }
    }
}