package co.timekettle.speech.utils;

import java.util.ArrayList;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <h3>音量计算辅助类</h3>
 * <p>
 * 		用于辅助计算pcm音频音量等。
 * </p>
 * 
 * <AUTHOR>
 * @since version:1073
 * 
 */
public class VolumeUtil {
	private final static boolean UseDb = true;

	// 音量级别与能量值对应关系
	private final static int VOLUME_LEVEL_0 = 329;
	private final static int VOLUME_LEVEL_1 = 421;
	private final static int VOLUME_LEVEL_2 = 543;
	private final static int VOLUME_LEVEL_3 = 694;
	private final static int VOLUME_LEVEL_4 = 895;
	private final static int VOLUME_LEVEL_5 = 1146;
	private final static int VOLUME_LEVEL_6 = 1476;
	private final static int VOLUME_LEVEL_7 = 1890;
	private final static int VOLUME_LEVEL_8 = 2433;
	private final static int VOLUME_LEVEL_9 = 3118;
	private final static int VOLUME_LEVEL_10 = 4011;
	private final static int VOLUME_LEVEL_11 = 5142;
	private final static int VOLUME_LEVEL_12 = 6612;
	private final static int VOLUME_LEVEL_13 = 8478;
	private final static int VOLUME_LEVEL_14 = 10900;
	private final static int VOLUME_LEVEL_15 = 13982;
	private final static int VOLUME_LEVEL_16 = 17968;
	private final static int VOLUME_LEVEL_17 = 23054;
	private final static int VOLUME_LEVEL_18 = 29620;
	private final static int VOLUME_LEVEL_19 = 38014;
	private final static int VOLUME_LEVEL_20 = 48828;
	private final static int VOLUME_LEVEL_21 = 62654;
	private final static int VOLUME_LEVEL_22 = 80491;
	private final static int VOLUME_LEVEL_23 = 103294;
	private final static int VOLUME_LEVEL_24 = 132686;
	private final static int VOLUME_LEVEL_25 = 170366;
	private final static int VOLUME_LEVEL_26 = 218728;
	private final static int VOLUME_LEVEL_27 = 280830;
	
	/**
	 * <h4>计算pcm音频音量</h4>
	 * 
	 * @param pcmFrame 音频数据帧，每个采样值长16bit。
	 * @param length 数据长度，帧的字节数。
	 * @return 音量值[0-30]。
	 */
	public static int computeVolume(byte[] pcmFrame, int length) {
		if (null == pcmFrame || length <= 2) {
			return 0;
		}

		if (UseDb) {
			double rms = calculateRMS(pcmFrame);
			double db = convertToDB(rms);
			return (int)db;
		}
		
		// 采样长度，即有多少个16bit采样值
		int sampleLen = length / 2;
		// 采样平均值
		long meanVal = 0;
		
		for (int i = 0; i < 2 * sampleLen - 1; i += 2) {
			int sampleVal = pcmFrame[i] + pcmFrame[i + 1] * 256;
			meanVal += sampleVal;
		}
		meanVal /= sampleLen;
		
		// 帧能量值
		long frameEnergy = 0;
		for (int i = 0; i < 2 * sampleLen - 1; i += 2) {
			int sampleVal = pcmFrame[i] + pcmFrame[i + 1] * 256;
			int temp = (int) (sampleVal - meanVal);
			frameEnergy += ((temp * temp) >> 9);
		}
		frameEnergy /= sampleLen;
		
		if (frameEnergy < VOLUME_LEVEL_0) {
			return 0;
		} else if (frameEnergy < VOLUME_LEVEL_1) {
			return 1;
		} else if (frameEnergy < VOLUME_LEVEL_2) {
			return 2;
		} else if (frameEnergy < VOLUME_LEVEL_3) {
			return 3;
		} else if (frameEnergy < VOLUME_LEVEL_4) {
			return 4;
		} else if (frameEnergy < VOLUME_LEVEL_5) {
			return 5;
		} else if (frameEnergy < VOLUME_LEVEL_6) {
			return 6;
		} else if (frameEnergy < VOLUME_LEVEL_7) {
			return 7;
		} else if (frameEnergy < VOLUME_LEVEL_8) {
			return 8;
		} else if (frameEnergy < VOLUME_LEVEL_9) {
			return 9;
		} else if (frameEnergy < VOLUME_LEVEL_10) {
			return 10;
		} else if (frameEnergy < VOLUME_LEVEL_11) {
			return 11;
		} else if (frameEnergy < VOLUME_LEVEL_12) {
			return 12;
		} else if (frameEnergy < VOLUME_LEVEL_13) {
			return 13;
		} else if (frameEnergy < VOLUME_LEVEL_14) {
			return 14;
		} else if (frameEnergy < VOLUME_LEVEL_15) {
			return 15;
		} else if (frameEnergy < VOLUME_LEVEL_16) {
			return 16;
		} else if (frameEnergy < VOLUME_LEVEL_17) {
			return 17;
		} else if (frameEnergy < VOLUME_LEVEL_18) {
			return 18;
		} else if (frameEnergy < VOLUME_LEVEL_19) {
			return 19;
		} else if (frameEnergy < VOLUME_LEVEL_20) {
			return 20;
		} else if (frameEnergy < VOLUME_LEVEL_21) {
			return 21;
		} else if (frameEnergy < VOLUME_LEVEL_22) {
			return 22;
		} else if (frameEnergy < VOLUME_LEVEL_23) {
			return 23;
		} else if (frameEnergy < VOLUME_LEVEL_24) {
			return 24;
		} else if (frameEnergy < VOLUME_LEVEL_25) {
			return 25;
		} else if (frameEnergy < VOLUME_LEVEL_26) {
			return 26;
		} else if (frameEnergy < VOLUME_LEVEL_27) {
			return 27;
		} else {
			return 30;
		}
	}

	public static int computeVolume(short[] pcmFrame, int length) {
		if (null == pcmFrame || length <= 2) {
			return 0;
		}

		if (UseDb) {
			double rms = calculateRMS(pcmFrame);
			double db = convertToDB(rms);
			return (int)db;
		}

		// 采样长度，即有多少个16bit采样值
		int sampleLen = length;
		// 采样平均值
		long meanVal = 0;

		for (int i = 0; i < sampleLen; i++) {
			int sampleVal = pcmFrame[i];
			meanVal += sampleVal;
		}
		meanVal /= sampleLen;

		// 帧能量值
		long frameEnergy = 0;
		for (int i = 0; i < sampleLen; i++) {
			int sampleVal = pcmFrame[i];
			int temp = (int) (sampleVal - meanVal);
			frameEnergy += ((temp * temp) >> 9);
		}
		frameEnergy /= sampleLen;

		if (frameEnergy < VOLUME_LEVEL_0) {
			return 0;
		} else if (frameEnergy < VOLUME_LEVEL_1) {
			return 1;
		} else if (frameEnergy < VOLUME_LEVEL_2) {
			return 2;
		} else if (frameEnergy < VOLUME_LEVEL_3) {
			return 3;
		} else if (frameEnergy < VOLUME_LEVEL_4) {
			return 4;
		} else if (frameEnergy < VOLUME_LEVEL_5) {
			return 5;
		} else if (frameEnergy < VOLUME_LEVEL_6) {
			return 6;
		} else if (frameEnergy < VOLUME_LEVEL_7) {
			return 7;
		} else if (frameEnergy < VOLUME_LEVEL_8) {
			return 8;
		} else if (frameEnergy < VOLUME_LEVEL_9) {
			return 9;
		} else if (frameEnergy < VOLUME_LEVEL_10) {
			return 10;
		} else if (frameEnergy < VOLUME_LEVEL_11) {
			return 11;
		} else if (frameEnergy < VOLUME_LEVEL_12) {
			return 12;
		} else if (frameEnergy < VOLUME_LEVEL_13) {
			return 13;
		} else if (frameEnergy < VOLUME_LEVEL_14) {
			return 14;
		} else if (frameEnergy < VOLUME_LEVEL_15) {
			return 15;
		} else if (frameEnergy < VOLUME_LEVEL_16) {
			return 16;
		} else if (frameEnergy < VOLUME_LEVEL_17) {
			return 17;
		} else if (frameEnergy < VOLUME_LEVEL_18) {
			return 18;
		} else if (frameEnergy < VOLUME_LEVEL_19) {
			return 19;
		} else if (frameEnergy < VOLUME_LEVEL_20) {
			return 20;
		} else if (frameEnergy < VOLUME_LEVEL_21) {
			return 21;
		} else if (frameEnergy < VOLUME_LEVEL_22) {
			return 22;
		} else if (frameEnergy < VOLUME_LEVEL_23) {
			return 23;
		} else if (frameEnergy < VOLUME_LEVEL_24) {
			return 24;
		} else if (frameEnergy < VOLUME_LEVEL_25) {
			return 25;
		} else if (frameEnergy < VOLUME_LEVEL_26) {
			return 26;
		} else if (frameEnergy < VOLUME_LEVEL_27) {
			return 27;
		} else {
			return 30;
		}
	}

	static float getThreshold(int type) {
		if (type == 0) return 45.0f; // w3
		return 45.0f;
	}

	public static boolean judgeVolume(List<Float> volumes) {
		return judgeVolume(volumes, 0);
	}

	public static boolean judgeVolume(List<Float> volumes, int type) {
		float threshold = getThreshold(type);
		return judgeVolume(volumes, threshold);
	}

	/**
	 * <h4>计算 volumes 是否是超过 75db, 实际处理为: 计算大于 0 的部分的平均值是否大于 threshold </h4>
	 *
	 * @param volumes 通过此计算的音量值数组
	 * @param threshold 不同产品有不同的阈值, w3 默认经验值
	 * @return 是否大于阈值
	 */
	static boolean judgeVolume(List<Float> volumes, float threshold) {
//		double[] volumes = {41.0,53.0,56.0,59.0,62.0,62.0,60.0,50.0,31.0,51.0,57.0,56.0,51.0,36.0,13.0,6.0,42.0,54.0,57.0,56.0,52.0,49.0,50.0,49.0,54.0,61.0,58.0,53.0,41.0,26.0,20.0,49.0,52.0,41.0,56.0,58.0,58.0,45.0,30.0,52.0,52.0,49.0,40.0,30.0,20.0,24.0,41.0,46.0,45.0,31.0,13.0,1.0,5.0,16.0,23.0,20.0,5.0};
		// 20 个等级, 每隔 5db 一个等级
		int[] levels = new int[20];

		// 抽取有声音(时域幅值较大)的部分, 以统计说话时的音量
		for (int i = 0; i < volumes.size(); i++) {
//			if (i > 50) continue; // 只判断前 100(50 * 48ms)
			if (volumes.get(i) < 10) continue; // 值太小则忽略
			levels[(int) (volumes.get(i) / 5)]++;
		}

		// 存储每个等级对应数量
		Map<Integer, Integer> map = new HashMap<>();
		for (int i = 0; i < levels.length; i++) {
			map.put(i, levels[i]);
		}

		// 倒序
		List<Map.Entry<Integer, Integer>> mutilevels = new ArrayList<>(map.entrySet());
		mutilevels.sort((Map.Entry<Integer, Integer> o1, Map.Entry<Integer, Integer> o2) -> o2.getValue().compareTo(o1.getValue()));

		// 计算一定数量的音量等级
		float volume = 0.0f;
		int count = 0;
		for (int i = 0; i < mutilevels.size(); i++) {
			if (i < 3) {
				volume = volume + mutilevels.get(i).getKey() * 5 * mutilevels.get(i).getValue();
				count = count + mutilevels.get(i).getValue();
			}
		}
		float avgVolume = volume / count;
//		AiSpeechLogUtil.d("", "声音正常 avgVolume: " + avgVolume + ", count: " + count + " mutilevels:" + mutilevels);
		return avgVolume >= threshold;
	}

	/**
	 * 计算均方根（Root Mean Square, RMS）可以用于衡量一组数据的平均能量或振幅。在音频处理中，RMS常用于计算音频信号的音量。
	 * @param pcmData 音频序列
	 * @return rms 音量值
	 */
	public static double calculateRMS(short[] pcmData) {
		double sum = 0;
		for (int sample : pcmData) {
			sum += sample * sample;
		}
		double meanSquare = (double) sum / pcmData.length;
		double rms = Math.sqrt(meanSquare);
		return rms;
	}

	/**
	 * 计算均方根（Root Mean Square, RMS）可以用于衡量一组数据的平均能量或振幅。在音频处理中，RMS常用于计算音频信号的音量。
	 * @param pcmData 音频序列
	 * @return rms 音量值
	 */
	public static double calculateRMS(byte[] pcmData) {
		double sum = 0;
		for (int i = 0; i < pcmData.length - 1; i += 2) {
			int sample = (pcmData[i + 1] << 8) | (pcmData[i] & 0xFF);
			sum += sample * sample;
		}
		double meanSquare = (double) sum / (pcmData.length / 2);
		double rms = Math.sqrt(meanSquare);
		return rms;
	}

	/**
	 * 0 分贝（dB）：被认为是听觉阈值的最低限度，无声音或极微弱的声音。
	 * 30 分贝（dB）：非常安静的环境，类似于轻微的耳语声。
	 * 60 分贝（dB）：普通对话或安静的办公室环境。
	 * 90 分贝（dB）：一般的城市交通噪音、汽车喇叭声等。
	 * 120 分贝（dB）：音乐演唱会、爆炸声等极高强度的声音，可能对听力造成损害。
	 * @param rms 音量值
	 * @return 分贝
	 */
	public static double convertToDB(double rms) {
		if (rms < 1) rms = 1;
		double db = 20 * Math.log10(rms);
		return db;
	}

	public static void main(String[] args) {
		byte[] pcmData = {0x00, 0x7F, (byte) 0xFF, 0x00, (byte) 0xFF, 0x7F}; // 示例 PCM 数据
		double rms = calculateRMS(pcmData);
		double db = convertToDB(rms);
		System.out.println("PCM 数据的分贝值：" + db);
	}
	
}
