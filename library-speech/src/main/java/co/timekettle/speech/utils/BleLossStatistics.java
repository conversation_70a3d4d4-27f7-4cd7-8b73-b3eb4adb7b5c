package co.timekettle.speech.utils;

import android.content.Context;

import java.io.File;
import java.io.FileOutputStream;
import java.io.IOException;
import java.io.OutputStream;
import java.text.DateFormat;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Comparator;
import java.util.Date;
import java.util.HashMap;
import java.util.Locale;
import java.util.Map;

import co.timekettle.speech.ServiceQualityResult;

class Perip {
    Integer rssis = 0;
    Integer rssiCount = 0;
    Integer extloss = 0;
    String name;
    String firmwareVersion;
    String electric;
    String id;

    Perip(String id, String name) {
        this.id = id;
        this.name = name;
    }
}

public class BleLossStatistics {
    private static final String TAG = "BleLossStatistics";
    Integer count = 0;
    String dirName = "bleloss";
    String locDirPath; // 指定统计所处目录
    String dirPath;
    File blelossFile = null;
    Map<String, File> asrFiles = null;
    Map<String, File> asrErrorFiles = null;
    Map<String, Perip> keyAndPerips = null;
    File allStatisFile = null; // 所有统计结果文本
    Integer asrReqCount = 0; // 做成通道对应
    Integer asrReqErrCount = 0;
    Boolean enableAsrRecord = false; // 是否记录 asr 识别内容

    DateFormat dateFormat;

    public BleLossStatistics(Context context, boolean cacheAll) {
//        String dir = "/Log";
//        if (cacheAll) dir = "/userlog";

        if (cacheAll) {
            this.locDirPath = context.getExternalCacheDir() + "/userlog";
        } else {
            this.locDirPath = context.getCacheDir().getAbsolutePath() + "/Log";
        }

        this.dirPath = this.locDirPath + "/" + this.dirName + "/";
        this.blelossFile = null;
        this.asrFiles = null;
        this.asrErrorFiles = null;
        this.keyAndPerips = null;
        this.allStatisFile = null; // 所有统计结果文本

        this.asrReqCount = 0; // 做成通道对应
        this.asrReqErrCount = 0;

        this.enableAsrRecord = false; // 是否记录 asr 识别内容

        dateFormat = new SimpleDateFormat("yyyyMMdd-HHmmss", Locale.getDefault());
    }

    public void cleanOldFile(boolean cacheAll) {
        // 清理旧目录
        File f = new File(this.dirPath);
        assert f.isDirectory() : "this.dirPath 路径需要为目录路径";

        ArrayList<File> items = new ArrayList<>();
        File[] subFiles = f.listFiles();
        if (subFiles != null) {
            for (File item : subFiles) {
                if (item.isDirectory()) items.add(item);
            }
        }
        if (items.size() == 0) return;

        AiSpeechLogUtil.d(TAG, "当前 bleloss 文件夹数量: " + items.size());
        items.sort(Comparator.comparingLong(File::lastModified).reversed());

        AiSpeechLogUtil.d(TAG, "bleloss 缓存所有文件开关 " + cacheAll);
        if (cacheAll) return;

        int nLeave = 20;
        for (int index = 0; index < items.size(); index++) {
            if (index > nLeave - 1) {
                File element = items.get(index);
                boolean ret = DeleteDirectory(element);
            }
        }
        AiSpeechLogUtil.d(TAG, "清理后 bleloss 文件夹数量" + items.size());
    }

    public static boolean DeleteDirectory(File dir) {
        if (dir.isDirectory()) {
            File[] files = dir.listFiles();
            if (files != null) {
                for (File file : files) {
                    if (!file.delete()) return false;
                }
            }
        }
        return dir.delete();
    }


    public void start(String[] peripIds, String[] peripNames, String mode, boolean cacheAll) throws IOException {
        if (this.blelossFile != null) {
            AiSpeechLogUtil.e(TAG, "当前已在统计中");
            return;
        }
        // 创建 bleloss 根目录
        File dirPathFile = new File(this.dirPath);
        boolean isExsitsRootDirPath = dirPathFile.exists();
        if (isExsitsRootDirPath) {
            this.cleanOldFile(cacheAll);
        } else dirPathFile.mkdirs();

        // 创建当前目录
        Date date = new Date();
        String dateName = dateFormat.format(date);
        mode = mode.replace("/", "&");
        String curDirPath = this.dirPath + "/" + dateName + "-" + mode + "/";
        new File(curDirPath).mkdirs();

        this.blelossFile = new File(curDirPath + dateName + "-bleloss.txt");
        File blelossFile = this.blelossFile;
        blelossFile.createNewFile();
        blelossFile.setLastModified(date.getTime());

        this.allStatisFile = new File(this.dirPath + "all-records-bleloss.txt");
        File allStatisPathFile = this.allStatisFile;
        boolean isExsitsAllStatisFile = allStatisPathFile.exists();
        if (!isExsitsAllStatisFile) {
            allStatisPathFile.createNewFile();
            allStatisPathFile.setLastModified(date.getTime());
        }
        appendFile(allStatisPathFile, "\n" + dateName + "-" + mode + " 此时开始(结束时下方会出现本次会话每个设备的统计)\n");

        this.asrFiles = new HashMap<>();
        this.asrErrorFiles = new HashMap<>();
        this.keyAndPerips = new HashMap<>();

        for (int i = 0; i < peripIds.length; i++) {
            String name = peripNames[i];
            String chkey = peripIds[i];

            if (this.enableAsrRecord) {
                String fileAsrPath = curDirPath + dateName + "-" + name + ".txt";
                File f = new File(fileAsrPath);
                this.asrFiles.put(chkey, f);
                f.createNewFile();
                f.setLastModified(date.getTime());
                appendFile(f, "");
            }

            String fileAsrErrorPath = curDirPath + dateName + "-" + name + "-err.txt";
            File asrErrorPathFile = new File(fileAsrErrorPath);
            this.asrErrorFiles.put(chkey, asrErrorPathFile);
            asrErrorPathFile.createNewFile();
            asrErrorPathFile.setLastModified(date.getTime());
            appendFile(asrErrorPathFile, "");

            this.keyAndPerips.put(chkey, new Perip(peripIds[i], name));
        }
        this.asrReqCount = 0;
        this.asrReqErrCount = 0;
    }

    public void appendFile(File f, String content) {
//        AiSpeechLogUtil.d(TAG, "打印当前 ble 服务事件: " + content);
        try {
            OutputStream outputStream = new FileOutputStream(f, true);
            outputStream.write(content.getBytes());
            outputStream.close();
        } catch (Exception ex) {
            ex.printStackTrace();
        }
    }

    public void stop() {
        double err_percent = this.asrReqCount > 0 ? (this.asrReqErrCount * 1.0) / this.asrReqCount : 0;

//        String asr_service = "全部设备(channel) asr服务失败次数:"+this.asrReqErrCount+" asr服务总次数:"+this.asrReqCount+" asr失败率:"+Math.round(err_percent * 10000) / 10000.0;
//        this.writeLossLog(asr_service);

        String guide = "rssi:信号强度 electric:设备电量 v:实际速率(KB/s) loss:实时丢包数量(20ms/包)(play指播放导致) tloss:累积丢包数量(20ms/包)(播放导致丢包数) recv:实收数据 total:录音总包数 interval:测试时长 loss%:丢包率";
        this.writeLossLog(guide);
        this.blelossFile = null;
        this.asrFiles = null;
        this.asrErrorFiles = null;
        this.asrReqCount = 0;
        this.asrReqErrCount = 0;
    }

    // 写入识别记录
    public void writeAsrLog(String chkey, String contents) {
        if (!this.enableAsrRecord) return;

        if (this.asrFiles == null) {
            AiSpeechLogUtil.e(TAG, "asrFiles 为空");
            return;
        }
        File fileAsrPath = this.asrFiles.get(chkey);
        if (fileAsrPath != null) {
            appendFile(fileAsrPath, contents + "\n");
        }
    }

    public void updateAsrReqCount() {
        this.asrReqCount = this.asrReqCount + 1;
    }

    public void updateAsrReqErrCount() {
        this.asrReqErrCount = this.asrReqErrCount + 1;
    }

    // 写入识别失败记录
    public void writeAsrErrLog(String chkey, String contents) {
        if (this.asrErrorFiles == null) {
            AiSpeechLogUtil.e(TAG, "asrErrorFiles 为空");
            return;
        }
        File fileAsrErrorPath = this.asrErrorFiles.get(chkey);

        if (fileAsrErrorPath != null) {
            appendFile(fileAsrErrorPath, contents + "\n");
        }
    }

    public void writeLossLog(String contents) {
        if (this.blelossFile == null) {
            AiSpeechLogUtil.e(TAG, "blelossFile 为空");
            return;
        }
        appendFile(this.blelossFile, dateFormat.format(new Date()) + " " + contents + "\n");
    }

    public void writeAllLossLog(String contents) {
        if (this.allStatisFile == null) {
            AiSpeechLogUtil.e(TAG, "allStatisFile 为空");
            return;
        }
        appendFile(this.allStatisFile, dateFormat.format(new Date()) + " " + contents + "\n");
    }

    public String getDesc(ServiceQualityResult e) {
        // ble 服务质量回调, 进行丢包统计
        boolean isBleRecording = !e.isDisconnect && !e.isFinal; // ble 录音中
        boolean isBleRecordEnd = !e.isDisconnect && e.isFinal; // ble 录音结束
        boolean isBleDisconnect = e.isDisconnect;

        Perip p = this.keyAndPerips.get(e.chkey);
        if (p != null) {
            // 正在录音时更新名字和电量等, 进行保存
            if (isBleRecording) {
                p.name = e.name;
                p.firmwareVersion = e.firmwareVersion;
                p.electric = e.electric;
            } else {
                // 录音结束时, name, firmwareVersion 无值则读取名字和电量等
                e.name = p.name;
                e.firmwareVersion = p.firmwareVersion;
                e.rssi = p.rssiCount != 0 ? String.valueOf(p.rssis / p.rssiCount) : "";
                e.electric = p.electric;
            }
            // 更新每次的 rssi, 最后做统计
            if (isBleRecording) {
                p.rssis = p.rssis + Integer.parseInt(e.rssi);
                p.rssiCount++;
            }
        }
        String desc = "";
        String baseDesc = String.format(
                    Locale.getDefault(),
                    "%s(%s) rssi:%s electric:%s recv:%d total:%d v:%.1f",
                    e.name,
                    e.firmwareVersion,
                    e.rssi,
                    e.electric,
                    e.recv,
                    e.total,
                    e.velocity
        );

        if (isBleRecording) {
//            if (e.loss > 0 && e.loss < 10) perip.extloss = perip.extloss + parseInt(e.loss); // 001 单工模式下, 播放时不录音导致
            desc = String.format(
                    Locale.getDefault(),
                    "%s loss:%d%s tloss:%d",
                    baseDesc,
                    e.loss,
                    e.loss > 0 && e.loss < 10 ? "(play)" : "",
                    e.totalLoss
            );
        } else {
            if (isBleRecordEnd) {
//                    boolean didDisconnect = false;
//                    if (didDisconnect) {
//                        desc = String.format(
//                                "--------------------------------- %s(%s)  早已断开------------------------------------------",
//                                e.name,
//                                e.firmwareVersion
//                        );
//                    }
            } else if (isBleDisconnect) {
                // ble 录音断开
            }
            long interval = (e.destroyTime - e.createTime) / 1000;
            String intervalDesc = String.format(
                    Locale.getDefault(),
                    "%dm%ds",
                    interval / 60,
                    interval % 60
            );
            int extloss = 0; // 001 单工模式下, 播放导致
            float lossRate = (float) ((e.totalLoss - extloss) * 1.0 / e.total * 100);
            desc = String.format(
                    Locale.getDefault(),
                    "%s tloss:%d(%d) interval:%s loss%%:%.1f <-----",
                    baseDesc,
                    e.totalLoss,
                    extloss,
                    intervalDesc,
                    lossRate
            );
        }
        return desc;
    }
}
