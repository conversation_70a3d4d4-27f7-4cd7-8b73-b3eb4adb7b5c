package co.timekettle.speech.utils;

import android.content.Context;


import java.io.File;
import java.io.FileInputStream;
import java.io.FileNotFoundException;
import java.io.FileOutputStream;
import java.io.IOException;

// 用来保存本地pcm文件，以及将pcm转化为wav文件

public class SaveManager {
    private static final boolean ENABLE_RECORD = true;//是否保存录音文件到本地
    public static final String TAG = "SaveManager";
    protected int defaultSampleRateInHz = 16000;
    private static SaveManager instance = null;
    final float MULTIPLE = 1.4f; //手动调节声音大小的系数
    private FileOutputStream mFileOutputStream;
    private File mWavFile;
    private File mPcmFileName;
    private static Context context;

    int db = 40;
    private double factor = Math.pow(10, (double) db / 20);

    public static SaveManager getInstance(Context ct) {
        if (instance == null) {
            instance = new SaveManager();
        }
        context = ct;
        return instance;
    }


    public File open() {
        // context.getFilesDir() ： /data/user/0/com.translation666.debug/files
        close();
        try {
            mPcmFileName = new File(context.getFilesDir() + "/record.pcm");
            mWavFile = new File(context.getFilesDir() + "/record.wav");
            mFileOutputStream = new FileOutputStream(mPcmFileName);
            return mWavFile;
        } catch (FileNotFoundException e) {
            e.printStackTrace();
        }
        return null;
    }

    public void write(byte[] data) {
        if (mFileOutputStream != null) {
            try {
                mFileOutputStream.write(data);
//                mFileOutputStream.flush();
            } catch (IOException e) {
                e.printStackTrace();
            }
        } else
            AiSpeechLogUtil.e(TAG, "输出流为空，请先open");
    }


    public void close() {
        try {
            if (mFileOutputStream != null) {
                mFileOutputStream.close();
                mFileOutputStream = null;
//                final String pcmFileName = mPcmFileName.getAbsolutePath();
//                final String wavFileName = mWavFile.getAbsolutePath();
//                new Thread(new Runnable() {
//                    @Override
//                    public void run() {
//                        pcmToWave(pcmFileName, wavFileName);
//                    }
//                }).start();
            }
        } catch (IOException e) {
            e.printStackTrace();
        }
    }


    public void pcmToWave(String inFileName, String outFileName) {
        AiSpeechLogUtil.e(TAG, "pcm转wav文件，输出路径：" + outFileName);
        FileInputStream in = null;
        FileOutputStream out = null;
        long totalAudiolen = 0;
        long longSampleRate = defaultSampleRateInHz;
        long totalDataLen = totalAudiolen + 36;//由于不包括RIFF和WAV
        int channels = 1; //单通道
        long byteRate = 16 * longSampleRate * channels / 8;
        byte[] data = new byte[1024];
        try {
            in = new FileInputStream(inFileName);
            out = new FileOutputStream(outFileName);
            totalAudiolen = in.getChannel().size();
            totalDataLen = totalAudiolen + 36;
            writeWaveFileHeader(out, totalAudiolen, totalDataLen, longSampleRate, channels, byteRate);
            while (in.read(data) != -1) {
                out.write(data);
            }
            in.close();
            out.close();
        } catch (FileNotFoundException e) {
            e.printStackTrace();
        } catch (IOException e) {
            e.printStackTrace();
        }
    }

    // https://www.jianshu.com/p/ca2cb00418a7 手动调整pcm音量的大小 增强音频信号, 进行溢出兼容处理
    // http://blog.jianchihu.net/pcm-volume-control.html 主要看这个
    // https://www.jianshu.com/p/50c697bec409
    // 假如我们有2048bytesPCM数据，样本大小两个字节，那么就共有1024个样本

    //pData原始音频byte数组，nBitsPerSample采样率，multiple表示Math.pow()返回值
    public byte[] raisePcmVoice(byte[] pData, int nBitsPerSample, float multiple) {
        int nCur = 0;
        byte[] result = new byte[pData.length];
        if (16000 == nBitsPerSample) {
            while (nCur < pData.length) {
                short volum = getShort(pData, nCur);//获取short类型的数据
                //short 最小值是 -32768（-2^15）
                //最大值是 32767（2^15 - 1）
                volum = (short) (volum * multiple);
                if (volum > 32767) volum = (short) 32767;
                else if (volum < -32768) {
                    volum = (short) -32768;
                }
                //16位的样本大小为2个byte (所以2个字节为一个最小的元数据)
                result[nCur] = (byte) (volum & 0xFF);
                result[nCur + 1] = (byte) ((volum >> 8) & 0xFF);
                nCur += 2;
            }
        }
        return result;
    }

    private short getShort(byte[] data, int start) {
        return (short) ((data[start] & 0xFF) | (data[start + 1] << 8));
    }


    public void writeWaveFileHeader(FileOutputStream out, long totalAudioLen, long totalDataLen,
                                    long longSampleRate,
                                    int channels, long byteRate) {
        byte[] header = new byte[44];
        header[0] = 'R'; // RIFF
        header[1] = 'I';
        header[2] = 'F';
        header[3] = 'F';
        header[4] = (byte) (totalDataLen & 0xff);//数据大小
        header[5] = (byte) ((totalDataLen >> 8) & 0xff);
        header[6] = (byte) ((totalDataLen >> 16) & 0xff);
        header[7] = (byte) ((totalDataLen >> 24) & 0xff);
        header[8] = 'W';//WAVE
        header[9] = 'A';
        header[10] = 'V';
        header[11] = 'E';
        //FMT Chunk
        header[12] = 'f'; // 'fmt '
        header[13] = 'm';
        header[14] = 't';
        header[15] = ' ';//过渡字节
        //数据大小
        header[16] = 16; // 4 bytes: size of 'fmt ' chunk
        header[17] = 0;
        header[18] = 0;
        header[19] = 0;
        //编码方式 10H为PCM编码格式
        header[20] = 1; // format = 1
        header[21] = 0;
        //通道数
        header[22] = (byte) channels;
        header[23] = 0;
        //采样率，每个通道的播放速度
        header[24] = (byte) (longSampleRate & 0xff);
        header[25] = (byte) ((longSampleRate >> 8) & 0xff);
        header[26] = (byte) ((longSampleRate >> 16) & 0xff);
        header[27] = (byte) ((longSampleRate >> 24) & 0xff);
        //音频数据传送速率,采样率*通道数*采样深度/8
        header[28] = (byte) (byteRate & 0xff);
        header[29] = (byte) ((byteRate >> 8) & 0xff);
        header[30] = (byte) ((byteRate >> 16) & 0xff);
        header[31] = (byte) ((byteRate >> 24) & 0xff);
        // 确定系统一次要处理多少个这样字节的数据，确定缓冲区，通道数*采样位数
        header[32] = (byte) (channels * 16 / 8);
        header[33] = 0;
        //每个样本的数据位数
        header[34] = 16;
        header[35] = 0;
        //Data chunk
        header[36] = 'd';//data
        header[37] = 'a';
        header[38] = 't';
        header[39] = 'a';
        header[40] = (byte) (totalAudioLen & 0xff);
        header[41] = (byte) ((totalAudioLen >> 8) & 0xff);
        header[42] = (byte) ((totalAudioLen >> 16) & 0xff);
        header[43] = (byte) ((totalAudioLen >> 24) & 0xff);
        try {
            out.write(header, 0, 44);
        } catch (IOException e) {
            e.printStackTrace();
        }
    }


    public boolean isOpen() {
        if (mFileOutputStream != null) return true;
        else return false;
    }

}
