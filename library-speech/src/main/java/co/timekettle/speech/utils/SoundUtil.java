package co.timekettle.speech.utils;


import android.content.Context;
import android.net.Uri;

import co.timekettle.speech.R;

import java.io.ByteArrayOutputStream;
import java.io.File;
import java.io.FileInputStream;
import java.io.InputStream;

public class SoundUtil {

    public static final String DIDA = "dida";
    public static final String TRY_IT_AGAIN = "try_it_again";
    public static final String ONE_MORE_TIME = "one_more_time";
    public static final String NICE = "nice";
    public static final String PERFECT = "perfect";
    public static final String FOLLOW_ME = "follow_me";
    public static final String SOME_MISTAKES = "some_mistakes";
    public static final String SPEAK_IN_MOTHER = "speak_in_mother";
    public static final String SPEAK_IN_YOUR_MOTHER = "speak_in_your_mother";
    public static final String GOOD_JOB = "good_job";
    public static final String START_NEW_ROUND = "start_new_round";
    public static final String EXCISE_AGAIN = "excise_again";

    public static byte[] getRawSoundBytes(Context context, String rawPath) {
        // rawPath : android.resource://com.translation666/raw/didong.pcm
        byte[] buffer = null;
        InputStream inputStream;
        try {
            String filename_with_extension =  rawPath.substring(rawPath.lastIndexOf("/")+1);
            String filename_no_extension = filename_with_extension.substring(0,filename_with_extension.lastIndexOf("."));
            int resID = context.getResources().getIdentifier(filename_no_extension,
                    "raw", context.getPackageName());
            inputStream = context.getResources().openRawResource(resID);
            int len = inputStream.available();
            buffer = new byte[len];
            inputStream.read(buffer);
            inputStream.close();
        } catch (Exception e) {
            e.printStackTrace();
        }
        return buffer;
    }

    public static byte[] getSound(Context context, String soundName) {
        byte[] buffer = null;
        InputStream inputStream = null;

        try {

            switch (soundName) {
                case DIDA:
                    inputStream = context.getResources().openRawResource(R.raw.didong);
                    break;
                case TRY_IT_AGAIN:
                    inputStream = context.getResources().openRawResource(R.raw.a01_comeontryitagain);
                    break;
                case ONE_MORE_TIME:
                    inputStream = context.getResources().openRawResource(R.raw.a02_onemoretime);
                    break;
                case NICE:
                    inputStream = context.getResources().openRawResource(R.raw.a03_nice);
                    break;
                case PERFECT:
                    inputStream = context.getResources().openRawResource(R.raw.a04_perfect);
                    break;
                case FOLLOW_ME:
                    inputStream = context.getResources().openRawResource(R.raw.b04_followme);
                    break;
                case SOME_MISTAKES:
                    inputStream = context.getResources().openRawResource(R.raw.b05_mistakes);
                    break;
                case SPEAK_IN_MOTHER:
                    inputStream = context.getResources().openRawResource(R.raw.b00_speakinmother);
                    break;
                case SPEAK_IN_YOUR_MOTHER:
                    inputStream = context.getResources().openRawResource(R.raw.b01_speakinyourmother);
                    break;
                case GOOD_JOB:
                    inputStream = context.getResources().openRawResource(R.raw.c02_goodjob);
                    break;
                case START_NEW_ROUND:
                    inputStream = context.getResources().openRawResource(R.raw.b07_startnewround);
                    break;
                case EXCISE_AGAIN:
                    inputStream = context.getResources().openRawResource(R.raw.c03_taptoexciseagain);
                    break;
                default:
                    break;
            }

            int len = inputStream.available();
            buffer = new byte[len];
            inputStream.read(buffer);
            inputStream.close();
        } catch (Exception e) {

        }
        return buffer;
    }
}
