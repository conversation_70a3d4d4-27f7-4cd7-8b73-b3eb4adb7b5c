package co.timekettle.speech.utils;

import java.io.BufferedReader;
import java.io.BufferedWriter;
import java.io.File;
import java.io.FileOutputStream;
import java.io.FileReader;
import java.io.FileWriter;
import java.io.IOException;

public class FileUtils {

    // 读取文件内容
    public static String readFile(String filePath) throws IOException {
        StringBuilder content = new StringBuilder();
        try (BufferedReader reader = new BufferedReader(new FileReader(filePath))) {
            String line;
            while ((line = reader.readLine()) != null) {
                content.append(line).append("\n");
            }
        }
        return content.toString();
    }

    // 写入内容到文件
    public static void writeFile(String filePath, String content) throws IOException {
        ensureFileExists(filePath);
        try (BufferedWriter writer = new BufferedWriter(new FileWriter(filePath))) {
            writer.write(content);
        }
    }

    // 写入内容到文件（字节数组）
    public static void writeFile(String filePath, byte[] content) throws IOException {
        ensureFileExists(filePath);
        try (FileOutputStream fos = new FileOutputStream(filePath)) {
            fos.write(content);
        }
    }

    // 删除文件
    public static boolean deleteFile(String filePath) {
        File file = new File(filePath);
        return file.delete();
    }

    // 检查文件是否存在
    public static boolean fileExists(String filePath) {
        File file = new File(filePath);
        return file.exists();
    }

    // 创建文件
    public static boolean createFile(String filePath) throws IOException {
        File file = new File(filePath);
        return file.createNewFile();
    }

    // 创建目录
    public static boolean createDirectory(String dirPath) {
        File dir = new File(dirPath);
        return dir.mkdirs();
    }

    // 获取文件大小
    public static long getFileSize(String filePath) {
        File file = new File(filePath);
        return file.length();
    }

    // 重命名文件
    public static boolean renameFile(String oldFilePath, String newFilePath) {
        File oldFile = new File(oldFilePath);
        File newFile = new File(newFilePath);
        return oldFile.renameTo(newFile);
    }

    // 确保文件及其目录存在
    private static void ensureFileExists(String filePath) throws IOException {
        File file = new File(filePath);
        File parentDir = file.getParentFile();
        if (parentDir != null && !parentDir.exists()) {
            parentDir.mkdirs();
        }
        if (!file.exists()) {
            file.createNewFile();
        }
    }
}
