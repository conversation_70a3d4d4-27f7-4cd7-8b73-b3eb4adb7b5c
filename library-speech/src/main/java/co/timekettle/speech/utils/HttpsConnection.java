package co.timekettle.speech.utils;

import javax.net.ssl.HostnameVerifier;
import javax.net.ssl.HttpsURLConnection;
import javax.net.ssl.SSLContext;
import javax.net.ssl.SSLSession;
import javax.net.ssl.TrustManager;
import javax.net.ssl.X509TrustManager;

import java.io.ByteArrayOutputStream;
import java.io.FileOutputStream;
import java.io.InputStream;
import java.io.OutputStream;
import java.net.HttpURLConnection;
import java.net.ProtocolException;
import java.net.URL;
import java.security.cert.X509Certificate;
import java.util.HashMap;
import java.util.Map;

public class HttpsConnection {

    private static String userAgent = "Mozilla/5.0 (Windows NT 6.1) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/29.0.1547.66 Safari/537.36";
    private static int defaultTimeOut = 10000;

    private static HttpsURLConnection getHttpsConnection(String connectingUrl) throws Exception {

        URL url = new URL(connectingUrl);
        HttpsURLConnection webRequest = (HttpsURLConnection) url.openConnection();
        return webRequest;
    }

    public static byte[] get(String url) {
        return get(url, defaultTimeOut);
    }

    public static byte[] get(String urlStr, int timeout) {

        try {
            URL url = new URL(urlStr);
            HttpURLConnection urlConn = (HttpURLConnection) url.openConnection();
//            HttpsURLConnection urlConn = HttpsConnection.getHttpsConnection(url);
            urlConn.setConnectTimeout(timeout);
//            urlConn.setReadTimeout(35000);
//            urlConn.setRequestProperty("Content-Type", "application/json");
            urlConn.connect();

//            if (urlConn.getResponseCode() == 200) {}
            InputStream inSt = urlConn.getInputStream();
            ByteArray ba = new ByteArray();

            int rn2 = 0;
            int bufferLength = 4096;
            byte[] buf2 = new byte[bufferLength];
            while ((rn2 = inSt.read(buf2, 0, bufferLength)) > 0) {
                ba.cat(buf2, 0, rn2);
            }

            inSt.close();
            urlConn.disconnect();
            return ba.getArray();

        } catch (Exception e) {
            e.printStackTrace();
        }

        return null;
    }

    public static String download(String urlStr, String filePath) {
        return download(urlStr, filePath, defaultTimeOut);
    }

    public static String download(String urlStr, String filePath, int timeout) {
        try {
            URL url = new URL(urlStr);
            HttpURLConnection urlConn = (HttpURLConnection) url.openConnection();
//            HttpsURLConnection urlConn = HttpsConnection.getHttpsConnection(url);
            urlConn.setConnectTimeout(timeout);
//            urlConn.setReadTimeout(35000);
//            urlConn.setRequestProperty("Content-Type", "application/json");
            urlConn.connect();

//            if (urlConn.getResponseCode() == 200) {}
            InputStream inSt = urlConn.getInputStream();

            FileOutputStream fOutStream = new FileOutputStream(filePath);
            int rn2 = 0;
            int bufferLength = 4096;
            byte[] buf2 = new byte[bufferLength];
            while ((rn2 = inSt.read(buf2, 0, bufferLength)) > 0) {
                fOutStream.write(buf2, 0, rn2);
            }

            inSt.close();
            fOutStream.close();
            urlConn.disconnect();
            return filePath;
        } catch (Exception e) {
            e.printStackTrace();
        }

        return null;
    }

    public static byte[] post(String url, String body) {
        Map<String, String> headers = new HashMap<>();
        headers.put("Content-Type", "application/json");
        return post(url, defaultTimeOut, body.getBytes(), headers, false);
    }

    public static byte[] post(String url, byte[] body) {
        Map<String, String> headers = new HashMap<>();
        headers.put("Content-Type", "application/json");
        return post(url, defaultTimeOut, body, headers, false);
    }

    public static byte[] post(String url, byte[] body, Map<String, String> headers) {
        return post(url, defaultTimeOut, body, headers, false);
    }

    public static byte[] post(String url, String body, Map<String, String> headers, boolean doNtoVerify) {
        return post(url, defaultTimeOut, body.getBytes(), headers, doNtoVerify);
    }

    public static byte[] post(String url, byte[] body, Map<String, String> headers, boolean doNtoVerify) {
        return post(url, defaultTimeOut, body, headers, doNtoVerify);
    }



    public static byte[] post(String uri, int timeout, byte[] body, Map<String, String> headers, boolean doNtoVerify) {

        try {
            URL url = new URL(uri);

            if (doNtoVerify && url.getProtocol().toLowerCase().equals("https")) {
                trustAllHosts();
            }

            HttpURLConnection httpURLConnection = (HttpURLConnection) url.openConnection();

            if (doNtoVerify && url.getProtocol().toLowerCase().equals("https")) {
                HttpsURLConnection https = (HttpsURLConnection) httpURLConnection;
                https.setHostnameVerifier(DO_NOT_VERIFY);
            }

            httpURLConnection.setConnectTimeout(timeout);       //设置连接超时时间
            httpURLConnection.setDoInput(true);                 //打开输入流，以便从服务器获取数据
            httpURLConnection.setDoOutput(true);                //打开输出流，以便向服务器提交数据
            httpURLConnection.setRequestMethod("POST");         //设置以Post方式提交数据
            httpURLConnection.setUseCaches(false);              //使用Post方式不能使用缓存
            httpURLConnection.setRequestProperty("User-Agent", "Mozilla/5.0 (Macintosh; U; PPC; en-US; rv:1.3.1)");
            httpURLConnection.setRequestProperty("Accept-Charset", "UTF-8");

            for (String key : headers.keySet()) {
                String value = headers.get(key);
                if (value == null) {
                    continue;
                }
                httpURLConnection.setRequestProperty(key, value);
            }

            //设置请求体的长度
            httpURLConnection.setRequestProperty("Content-Length", String.valueOf(body.length));
            //获得输出流，向服务器写入数据
            OutputStream outputStream = httpURLConnection.getOutputStream();
            outputStream.write(body);
            outputStream.flush();
            outputStream.close();

            int response = httpURLConnection.getResponseCode();            //获得服务器的响应码

//            AiSpeechLogUtil.e("POST", uri + "返回状态:" + response);
            if (response == HttpURLConnection.HTTP_OK) {
                InputStream inputStream = httpURLConnection.getInputStream();
                ByteArrayOutputStream byteArrayOutputStream = new ByteArrayOutputStream();
                byte[] data = new byte[1024];
                int len = 0;

                while ((len = inputStream.read(data)) != -1) {
                    byteArrayOutputStream.write(data, 0, len);
                }
                return byteArrayOutputStream.toByteArray();
            } else {
                return null;
            }

        } catch (ProtocolException e) {
            e.printStackTrace();
        } catch (Exception e) {
            e.printStackTrace();
        }

        return null;
    }

    public static byte[] post(String uri, int timeout, int readTimeout, byte[] body, Map<String, String> headers, boolean doNtoVerify) {

        try {
            URL url = new URL(uri);

            if (doNtoVerify && url.getProtocol().toLowerCase().equals("https")) {
                trustAllHosts();
            }

            HttpURLConnection httpURLConnection = (HttpURLConnection) url.openConnection();

            if (doNtoVerify && url.getProtocol().toLowerCase().equals("https")) {
                HttpsURLConnection https = (HttpsURLConnection) httpURLConnection;
                https.setHostnameVerifier(DO_NOT_VERIFY);
            }

            httpURLConnection.setConnectTimeout(timeout);       //设置连接超时时间
            httpURLConnection.setReadTimeout(readTimeout);
            httpURLConnection.setDoInput(true);                 //打开输入流，以便从服务器获取数据
            httpURLConnection.setDoOutput(true);                //打开输出流，以便向服务器提交数据
            httpURLConnection.setRequestMethod("POST");         //设置以Post方式提交数据
            httpURLConnection.setUseCaches(false);              //使用Post方式不能使用缓存
            httpURLConnection.setRequestProperty("User-Agent", "Mozilla/5.0 (Macintosh; U; PPC; en-US; rv:1.3.1)");
            httpURLConnection.setRequestProperty("Accept-Charset", "UTF-8");

            for (String key : headers.keySet()) {
                String value = headers.get(key);
                if (value == null) {
                    continue;
                }
                httpURLConnection.setRequestProperty(key, value);
            }

            //设置请求体的长度
            httpURLConnection.setRequestProperty("Content-Length", String.valueOf(body.length));
            //获得输出流，向服务器写入数据
            OutputStream outputStream = httpURLConnection.getOutputStream();
            outputStream.write(body);
            outputStream.flush();
            outputStream.close();

            int response = httpURLConnection.getResponseCode();            //获得服务器的响应码

//            AiSpeechLogUtil.e("POST", uri + "返回状态:" + response);
            if (response == HttpURLConnection.HTTP_OK) {
                InputStream inputStream = httpURLConnection.getInputStream();
                ByteArrayOutputStream byteArrayOutputStream = new ByteArrayOutputStream();
                byte[] data = new byte[1024];
                int len = 0;

                while ((len = inputStream.read(data)) != -1) {
                    byteArrayOutputStream.write(data, 0, len);
                }
                return byteArrayOutputStream.toByteArray();
            } else {
                return null;
            }

        } catch (ProtocolException e) {
            e.printStackTrace();
        } catch (Exception e) {
            e.printStackTrace();
        }

        return null;
    }


    private static void trustAllHosts() {

        TrustManager[] trustAllCerts = new TrustManager[]{new X509TrustManager() {
            public X509Certificate[] getAcceptedIssuers() {
                return new X509Certificate[]{};
            }

            public void checkClientTrusted(X509Certificate[] chain, String authType) {
            }

            public void checkServerTrusted(X509Certificate[] chain, String authType) {
            }
        }};


        try {
            SSLContext sc = SSLContext.getInstance("TLS");
            sc.init(null, trustAllCerts, new java.security.SecureRandom());
            HttpsURLConnection.setDefaultSSLSocketFactory(sc.getSocketFactory());
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    private final static HostnameVerifier DO_NOT_VERIFY = new HostnameVerifier() {
        public boolean verify(String hostname, SSLSession session) {
            return true;
        }
    };

}
