package co.timekettle.speech;

import android.content.Context;

import org.concentus.OpusException;

import co.timekettle.opus.OpusCodec;
import co.timekettle.speech.recognizer.ISpeechRecognizer;
import co.timekettle.speech.recognizer.IflytekOfflineRecognizer;
import co.timekettle.speech.recognizer.RecognizerBase;
import co.timekettle.speech.recognizer.aeesdk.AeeOfflineRecognizer;
import co.timekettle.speech.utils.Language;
import co.timekettle.speech.utils.AiSpeechLogUtil;
import co.timekettle.speech.utils.RingBuffer;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.Iterator;
import java.util.Vector;
import java.util.function.Function;

public class RecognizeManager {

    private static final String TAG = "RecognizeManager";

    private static RecognizeManager instance = null;

    private final ArrayList<RecognizerClass> mClassList = new ArrayList<>();

    private final HashMap<Integer, SpeechTask<Object, String>> contexts = new HashMap<>();
    private final HashMap<Integer, Function<SpeechTask<Object, String>, Object>> callbacks = new HashMap<>();

    private Context context = null;
    private Listener listener;
    private final Vector<Worker> workerList = new Vector<>();

    private final RecognizerBase.Listener recognizerBaseListener = new RecognizerBase.Listener() {
        @Override
        public void onRecognizeResult(RecognizerBase recognizer, boolean isLast, String text, String engine, SpeechError error) {
            long session = recognizer.getSession();
            String key = recognizer.getKey();
            String name = recognizer.getName();
            AiSpeechLogUtil.d(TAG, "onRecognizeResult: " + name + "[" + engine + "] " + key + " " + session + " " + isLast + " " + text + (error != null ?  " error: " + error.desc : ""));

            int tag = recognizer.hashCode();
            SpeechTask<Object, String> task = contexts.get(tag);
            if (task == null) AiSpeechLogUtil.e(TAG, key + "通道任务: [" + session + "]" + name + " 任务为空(onRecognizeResult)");
            if (task == null) return; // 防止超时和 error 的两次回调
            if (error != null) {
                task.response.error = error;
                task.response.isFinished = true;
            } else {
                task.response.data = text;
            }
            task.response.engine = engine;
            task.response.isLast = isLast;
            if (doCallback(tag, task)) return;

            if (listener != null) {
                listener.onRecognizeResult(task, error);
            }
        }

        @Override
        public void onTranslateResult(RecognizerBase recognizer, String text, String engine, SpeechError error) {
            long session = recognizer.getSession();
            String key = recognizer.getKey();
            String name = recognizer.getName();
//            AiSpeechLogUtil.e(TAG, "onTranslateResult: " + name + "[" + engine + "] " + key + " " + session + " " + text + " " + " error: " + (error != null ? error.desc : ""));

            int tag = recognizer.hashCode();
            SpeechTask task = contexts.get(tag);
            if (task == null) AiSpeechLogUtil.e(TAG, key + "通道任务: [" + session + "]" + name + " 任务为空(onTranslateResult)");
            if (task == null) return; // 防止超时和 error 的两次回调
            if (error != null) {
                task.response.error = error;
            } else {
                task.response.data = text;
            }
            task.response.engine = engine;
            task.response.isLast = true; // 引擎端在识别结果为 isLast=true 时, 翻译识别结果并返回
            if (doCallback(tag, task)) return;

            if (listener != null) {
                listener.onTranslateResult(task, null);
            }
        }

        @Override
        public void onFinished(RecognizerBase recognizer, String engine, SpeechError error) {
            long session = recognizer.getSession();
            String key = recognizer.getKey();
            String name = recognizer.getName();
//            AiSpeechLogUtil.e(TAG, "onTranslateResult: " + name + "[" + engine + "] " + key + " " + session + " " + text + " " + " error: " + (error != null ? error.desc : ""));

            int tag = recognizer.hashCode();
            SpeechTask task = contexts.get(tag);
            if (task == null) AiSpeechLogUtil.e(TAG, key + "通道任务: [" + session + "]" + name + " 任务为空(onTranslateResult)");
            if (task == null) return; // 防止超时和 error 的两次回调
            if (error == null && task.response.data == null) error = new SpeechError(-1, "识别为空");
            task.response.error = error;
            task.response.engine = engine;
            task.response.isFinished = true; // 引擎端在识别结果为 isLast=true 时, 翻译识别结果并返回
            if (doCallback(tag, task)) return;

            if (listener != null) {
                listener.onTranslateResult(task, null);
            }
        }
    };

    private RecognizeManager() {
        addRecognizer("iflytekOfflineAsr", IflytekOfflineRecognizer.class);
        addRecognizer("AeeOfflineAsr", AeeOfflineRecognizer.class);
        addRecognizer("ispeech", ISpeechRecognizer.class);
    }

    public static RecognizeManager shareInstance() {
        if (instance == null) {
            instance = new RecognizeManager();
        }
        return instance;
    }

    public void destroy() {
        synchronized(workerList) {
            for (Worker worker : workerList) {
                worker.recognizer.stop();
            }

            workerList.clear();
        }
    }

    public void setContext(Context context) {
        this.context = context;
    }

    public void setListener(Listener listener) {
        this.listener = listener;
    }

    public void addRecognizer(String name, Class<?> objectClass) {
        removeRecognizer(name); // 先尝试移除, 后添加
        mClassList.add(new RecognizerClass(name, objectClass));
    }

    public void addRecognizer(int index, String name, Class<?> objectClass)
    {
        removeRecognizer(name);
        mClassList.add(index, new RecognizerClass(name, objectClass));
    }

    public void removeRecognizer(String name) {
        Iterator<RecognizerClass> iterator = mClassList.iterator();
        while (iterator.hasNext()) {
            RecognizerClass s = iterator.next();
            if (s.name.equals(name)) {
                iterator.remove();
            }
        }
    }

//    public long start(final String key, final String srcCode, final String dstCode) {
    public long start(SpeechTask<Object, String> task, Function<SpeechTask<Object, String>, Object> callback) {

        final long session = task.session;
        final String chkey = task.chkey;
        final String srcCode = task.request.code;
        final String dstCode = task.request.dstCode;
//        final String text = task.request.data;

        RecognizerBase recognizer = findRecognizer(chkey, srcCode, dstCode);
        if (recognizer == null) {
            AiSpeechLogUtil.e(TAG, chkey + " 错误未找到识别器 " + srcCode);

            task.response.error = new SpeechError(-1, "未找到识别器");
            task.response.isLast = true;

            if (callback != null) callback.apply(task);
            return 0;
        }
        AiSpeechLogUtil.d(TAG, recognizer.getName() + " " + chkey + " 开始识别 " + srcCode + " " + session);

        contexts.put(recognizer.hashCode(), task);
        callbacks.put(recognizer.hashCode(), callback);

        Language srcLanguage = new Language(srcCode, recognizer.platformCode(srcCode));
        Language dstLanguage = new Language(dstCode, dstCode);

        recognizer.setSession(session);
        if (dstCode != null) {
            recognizer.start(srcLanguage, dstLanguage);
        } else {
            recognizer.start(srcLanguage);
        }

        synchronized(workerList) {
            workerList.add(new Worker(recognizer, chkey, recognizer.getSession()));
        }
        AiSpeechLogUtil.d(TAG, chkey + "通道任务: [" + session + "]" + recognizer.getName() + " 识别: [" + srcCode + "]");
        return recognizer.getSession();
    }

    public void stop(String key) {
        /* FIXME: 基于一个通道同时只存在一个工作中(向服务器写数据)的识别器, 则是停止某一通道(key)识别工作;  */
        synchronized(workerList) {
            Worker w = null;
            for (Worker worker : workerList) {
                if (worker.key.equals(key) && !worker.stopped) {
                    w = worker;
                }
            }

            if (w != null) {
                AiSpeechLogUtil.e(TAG, "RecognizeManager 停止识别 " + key + " " + w.session);
                w.stopped = true;
                w.recognizer.stop();
//                workerList.remove(w);
            } else {
                AiSpeechLogUtil.e(TAG, "RecognizeManager 停止识别 " + key + ", 但未找到 worker");
            }
        }
    }

    public void stop(String key, long taskId) {
        /* FIXME: 基于一个通道同时只存在一个工作中(向服务器写数据)的识别器, 则是停止某一通道(key)识别工作;  */
        synchronized(workerList) {
            Worker w = null;
            for (Worker worker : workerList) {
                if (worker.session == taskId) {
                    w = worker;
                    break;
                }
            }

            if (w != null) {
                if (!w.stopped) {
                    AiSpeechLogUtil.d(TAG, "RecognizeManager 停止识别 " + w.key + " " + w.session);
                    w.stopped = true;
                    w.recognizer.stop();
//                workerList.remove(w);
                } else {
                    AiSpeechLogUtil.d(TAG, "RecognizeManager 早已停止识别 " + w.key + " " + w.session);
                }
            } else {
                AiSpeechLogUtil.d(TAG, "RecognizeManager 停止识别 " + key + ", 但未找到 worker");
            }
        }
    }

    public void stopAllWorker() {
        synchronized(workerList) {
            AiSpeechLogUtil.e(TAG, "stopAllWorker: 正在识别的worker: " + workerList.size());
            for (Worker worker : workerList) {
                worker.recognizer.stop();
            }

            workerList.clear();
        }
    }

    public void stopWorker(long workerId) {
        synchronized(workerList) {
            Worker w = null;
            for (Worker worker : workerList) {
                if (worker.session == workerId) {
                    w = worker;
                    w.stopped = true;
                    w.recognizer.stop();
                    AiSpeechLogUtil.e(TAG, "RecognizeManager 停止任务 " + w.key + " " + w.session);
                }
            }

            if (w != null) {
                workerList.remove(w);
            }
        }
    }

    public void writeAudio(String key, byte[] data) {
        synchronized(workerList) {
            for (Worker w : workerList) {
                if (w.key.equals(key) && !w.stopped) {
                    w.recognizer.writeAudio(data);
                }
            }
        }
    }

    public void writeAudio(long workId, byte[] data) {
        synchronized(workerList) {
            for (Worker w : workerList) {
                if (w.session == workId) {
                    if (!w.stopped) w.recognizer.writeAudio(data);
                    break;
                }
            }
        }
    }

    private boolean isCanRecognize(String name) {
        synchronized(workerList) {
            for (Worker worker : workerList) {
                RecognizerBase base = worker.recognizer;
                if (base.getName().equals(name) && base.isSingleton()) { // 若识别器仅支持单实例, 则识别器在数组中只能存在一个, 此时此类工作器不能用作识别
                    if (!worker.stopped && !base.isOfflineModule) { // 未停止的在线工作器(如讯飞在线), 讯飞离线识别允许队列处理
                        AiSpeechLogUtil.e(TAG, base.getName() +" 单例识别器已存在, 不能进行新的识别任务 " + base.getSession());
                        return false;
                    } else {
                        AiSpeechLogUtil.e(TAG, base.getName() +" 单例识别器[" + base.getSession() +"]已存在[但已停止工作], 能进行新的识别任务");
                    }
                }
            }
        }

        return true;
    }

    private RecognizerBase findRecognizer(String key, String srcCode, String dstCode) {
        AiSpeechLogUtil.d(TAG, "findWorker: [key=" + key + "][srcCode=" + srcCode + "][dstCode=" + dstCode + "]" + " onlyUseOffline:" + onlyUseOffline);
        try {
            for (RecognizerClass s : mClassList) {
                Class<?> obj = s.className;
                RecognizerBase base = (RecognizerBase) obj.newInstance();

                /* FIXME: 当开启离线时, 在此处判断离线语言对 */
                if (onlyUseOffline) {
                    if (!base.isOfflineModule) {
                        AiSpeechLogUtil.e(TAG, base.getName() + " 是在线模块, 网络未开启, 当前只能用离线模块");
                        continue;
                    }
                }
                
                boolean isSupport = base.isSupport(srcCode, dstCode);
                boolean isCanRecognize = isCanRecognize(s.name);
                boolean isEnalbe = !base.isOfflineModule || OfflineManager.getInstance().isEnable(srcCode, dstCode); // 非离线是一定启用的
                AiSpeechLogUtil.d(TAG, "findRecognizer: 识别器[" + base.getName() + "] 条件 [isSupport=" + isSupport + "][isCanRecognize=" + isCanRecognize + "]" + "][isEnalbe=" + isEnalbe + "]");
                if (isSupport && isCanRecognize && isEnalbe) {
                    base.setContext(context);
                    base.setKey(key);
                    base.setListener(recognizerBaseListener);
                    return base;
                }
            }
        } catch (IllegalAccessException e) {
            e.printStackTrace();
        } catch (InstantiationException e) {
            e.printStackTrace();
        }
        return null;
    }

    public void removeWorker(long session) {
        synchronized(workerList) {
            for (Worker worker : workerList) {
                if (worker.session == session) {
                    workerList.remove(worker);
                    break;
                }
            }
        }
    }

    public boolean findWorker(long session) {
        synchronized(workerList) {
            for (Worker worker : workerList) {
                if (worker.session == session) {
                    return true;
                }
            }
        }
        return false;
    }

    private boolean doCallback(int tag, SpeechTask<Object, String> task) {
        Function<SpeechTask<Object, String>, Object> callback = callbacks.get(tag);
        if (callback != null) {
            if (task.response.isFinished) {
                callback.apply(task);
                // 最后若有 error, 则回调一次 error
//                if (task.response.error != null) callback.apply(task);
                callbacks.remove(tag);
                contexts.remove(tag);

                removeWorker(task.session);
            } else {
                callback.apply(task);
            }
            return true;
        }
        return false;
    }

    private boolean onlyUseOffline = false;
    public void setOnlyOffline(boolean offline) {
        onlyUseOffline = offline;
    }

    class Worker {
        public RecognizerBase recognizer;
        public String key;
        public long session;
        public boolean stopped = false; // 已停止任务, 但不代表已结束, 需要等待最终识别结果后才真正结束

        private static final int bytesIn20msBleRecord = 640; //20ms 单通道数据
        private RingBuffer cache = new RingBuffer(bytesIn20msBleRecord * 2);

        OpusCodec encoder;
        OpusCodec getOpusEncoder() throws OpusException {
            if (this.encoder == null) {
                final OpusCodec encoder = new OpusCodec(16000, 1);
                this.encoder = encoder;
            }
            return this.encoder;
        }


        public Worker(RecognizerBase recognizer, String key, long session) {
            this.recognizer = recognizer;
            this.session = session;
            this.key = key;
        }

        public void writeAudioCache(byte[] data) {
            if (ISpeechConstant.useOpus) {
                cache.write(data);

                byte[] packet = new byte[bytesIn20msBleRecord];
                int encodeSize = 40;
                byte[] output = new byte[encodeSize];

                byte[] outputs = new byte[cache.readable() / bytesIn20msBleRecord * encodeSize];
                int offset = 0;

                while (cache.readable() >= bytesIn20msBleRecord) {
                    cache.read(packet);

                    int size = 0;
                    try {
                        size = getOpusEncoder().encode(packet, bytesIn20msBleRecord / 2, output);
                    } catch (OpusException e) {
                        e.printStackTrace();
                    }
                    assert size == output.length;

                    System.arraycopy(output, 0, outputs, offset, size);
                    offset = offset + size;
                }
                if (offset > 0) recognizer.writeAudio(outputs);
            } else {
                recognizer.writeAudio(data);
            }
        }
    }

    public interface Listener {
        void onRecognizeResult(SpeechTask<Object, String> task, SpeechError error);

        void onTranslateResult(SpeechTask<String, String> task, SpeechError error);
    }

    class RecognizerClass {
        String name;
        Class<?> className;
        public RecognizerClass(String name, Class<?> className) {
            this.name = name;
            this.className = className;
        }
    }
}
