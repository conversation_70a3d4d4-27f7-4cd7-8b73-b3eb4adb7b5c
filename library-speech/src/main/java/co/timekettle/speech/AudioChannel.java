package co.timekettle.speech;

import android.content.Context;

import co.timekettle.speech.utils.VolumeUtil;
import co.timekettle.speech.jni.AudioVadJni;
import co.timekettle.speech.utils.BytesTrans;
import co.timekettle.speech.utils.DateUtil;
import co.timekettle.speech.utils.AiSpeechLogUtil;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.Iterator;
import java.util.Locale;
import java.util.Map;
import java.util.concurrent.ConcurrentLinkedDeque;

/**
 *
 */
public class AudioChannel {
    class BufferPacket {
        short[] data;
        boolean isVoice;
        long energy;
        boolean isStart;
        float volume; // 声压级
        long session; // 任务 id

        BufferPacket(short[] data, boolean isVoice) {
            this.data = data;
            this.isVoice = isVoice;
        }

        BufferPacket(short[] data, boolean isVoice, long energy) {
            this.data = data;
            this.isVoice = isVoice;
            this.energy = energy;
        }
    }

    private  static final String TAG = "AudioChannel";

    private String key; // 通道名称, 也是通道key
    private int sampleRate;
    @Deprecated
    private String speakerType = ISpeechConstant.SPEAKER.PHONE.toString(); // 播放路由种类, 如通过手机, 耳机, ble 设备等, 通过枚举 ISpeechConstant.SPEAKER 获得

    public String recorder; // 录音路由种类, 如通过手机, 耳机, ble 设备等, 通过枚举 ISpeechConstant.RECORDER 获得
    public String speaker; // 播放路由种类, 如通过手机, 耳机, ble 设备等, 通过枚举 ISpeechConstant.SPEAKER 获得
    public String role; // 工作角色, 已方/他方, 假如设备比作翻译员的话, 如对于中<->英来说 self 角色相当于中文翻译员, 对于英<->西来说 self 角色相当于英文翻译员角色

    private boolean canHearEcho = true; // 能否听到自己讲话后通过识别翻译结果后的tts播放
    protected boolean canRecordWhenSpeaking = true; // 播放的时候不允许录音
    private String srcCode;
    private String dstCode;
    protected boolean paused = false;
    private AudioVadJni outputStream;

    protected boolean enabled = true; // 控制是否可用, 在触控模式等控制通道是否可以写入数据并识别
    protected boolean isStreamMt = true; // 是否是流式翻译
    protected int streamMtSection = 20; // 是否是流式翻译
    protected boolean writeToFile = false; // 是否写入文件

    private int customMtTableId = -1;
    /**
    * 一些其他参数, 具体查看 SpeechRequest, 类似地 {@link SpeechRequest#OptsKeyTtsVoiceName } 则是 { SpeechRequest.OptsKeyTtsVoiceName : ""}
    * */
    public Map<String, Object> optsForEngine;

    private int curWroteCount; // 当前写入包数计数
    private int wroteCount; // 数据写入数量
    private int handleWroteCount; // 文件写入数量

    private boolean useAgc; // 送入引擎前是否加增益, 像除 tmk 002 之外的设备录音需要增加增益
    protected AgcProcessor agc; // 送入引擎前一刻加增益处理
    private float gain; // 增益, 像除 tmk 002 之外的设备录音需要增加增益, 004: 4.0
    private boolean isSynthesizeEnabled = true; // 是否需要合成
    private boolean isPlaybackEnabled = true; // 是否需要播放

    protected Listener listener;

    protected TestRecorder fileHandle;
    protected TestRecorder tagFileHandle;
    protected TestRecorder agcFileHandle;
    protected TestRecorder textFileHandle;
    protected int nBytePerPacket = ISpeechConstant.DefaultBytesPerPacketInMono;

    /// vad 前向缓存, 包括前端点缓存
    protected ConcurrentLinkedDeque<BufferPacket> headCache;
    /// vad 后向缓存, 即是后端点缓存
    protected ConcurrentLinkedDeque<BufferPacket> tailCache;
    /// headCache 的最大长度, 默认 35(560ms<-35*16ms), 前向缓存大小, 此区间包括前端点检测区间
    protected int maxHeadSize;
    /// tailCache 的最大长度, 默认 60(960ms), 后向缓存大小, 目前后端点检测只需检测非声音条件, 不判断能量等条件, 因此 maxTailSize 和 vadEndSize 保持相等
    protected int maxTailSize;
    /// 前端点检测长度, 默认 15(240ms), 小于 maxHeadSize(35), 前端点检测区间一般小于前向缓存大小
    protected int vadBeginSize;
    /// 后端点检测长度, 默认 60(960ms), 目前后端点检测只需检测静音, 因此 maxTailSize 和 vadEndSize 是相等
    protected int vadEndSize;
    /// 最小能量, 一般小于 defalutMinVadEnergy
    protected long minVadEnergy;

    protected boolean isTriggered; // vad是否唤醒

    private long curPickSession; // 当前唤醒 id, 也将用于 SpeechSessionContext 任务组(asr, mt, tts)的 id;
    protected SpeechSessionContext curSpeechContext;

    protected boolean markVadDataTag = false;

    public static long defalutMinVadEnergy = 100000;
    protected int vadDetectPattern = 0; // 值为: VadDetectPattern_SingleEnergy, VadDetectPattern_SingleEnergy, VadDetectPattern_OnlyVoice
    public static int VadDetectPattern_SingleEnergy = 0;
    public static int VadDetectPattern_DoubleLevelEnergy = 1;
    public static int VadDetectPattern_OnlyVoice = 2; // 暂未使用

    protected ArrayList<short[]> volumeCache; // 音量缓存
    protected int volumeCacheSize = 3; // 默认是 3 包数据计算一次

    public enum Role {
        Self,
        Other;
    }

    enum VadTag {
        Begin_PRE((short)(Short.MAX_VALUE / 2)),
        Begin((short)(Short.MAX_VALUE * 3 / 4)),
        Begin_CheckPoint(Short.MAX_VALUE),
        END(Short.MIN_VALUE),
        END_FORCE((short)(Short.MIN_VALUE / 2));

        short value;
        VadTag(short s) {
            value = s;
        }
    }

    public AudioChannel(Context context, String key, int sampleRate, boolean writeToFile) {
        this.key = key;
        this.sampleRate = sampleRate;
        this.enabled = true;
        this.isStreamMt = true;
        this.outputStream = new AudioVadJni(sampleRate);

        this.headCache = new ConcurrentLinkedDeque<BufferPacket>();
        this.maxHeadSize = 32;
        this.tailCache = new ConcurrentLinkedDeque<BufferPacket>();
        this.maxTailSize = 60;
        this.vadBeginSize = 10;
        this.vadEndSize = 60;
//        this.outputStream.setListener(outputStreamListener);
        this.nBytePerPacket = ISpeechConstant.DefaultBytesPerPacketInMono;
        this.minVadEnergy = defalutMinVadEnergy;
        this.writeToFile = writeToFile;
        // FIXME: 2023/6/12 第一次写入数据时才进行创建
        if (writeToFile)
            this.createFileHandle(context, key);

        this.volumeCache = new ArrayList<>(this.volumeCacheSize);
    }

    public AudioChannel(Context context, String key, boolean writeToFile) {
        this.key = key;
        this.sampleRate = (int) ISpeechConstant.DefaultSampleRate;
        this.enabled = true;
        this.isStreamMt = true;
        this.outputStream = new AudioVadJni(sampleRate);

        this.headCache = new ConcurrentLinkedDeque<BufferPacket>();
        this.maxHeadSize = 32;
        this.tailCache = new ConcurrentLinkedDeque<BufferPacket>();
        this.maxTailSize = 60;
        this.vadBeginSize = 10;
        this.vadEndSize = 60;
//        this.outputStream.setListener(outputStreamListener);
        this.nBytePerPacket = ISpeechConstant.DefaultBytesPerPacketInMono;
        this.minVadEnergy = defalutMinVadEnergy;
        this.writeToFile = writeToFile;
        // FIXME: 2023/6/12 第一次写入数据时才进行创建
        if (writeToFile)
            this.createFileHandle(context, key);

        this.volumeCache = new ArrayList<>(this.volumeCacheSize);
    }

    @Deprecated
    public AudioChannel(Builder builder) {
        // 建议不用此方法
        key = builder.name;
        sampleRate = builder.sampleRate;
        canHearEcho = builder.canHearEcho;
        speakerType = builder.speakerType;
        srcCode = builder.srcCode;
        dstCode = builder.dstCode;
        outputStream = builder.outputStream;
    }

    @Override
    protected void finalize() throws Throwable {
        super.finalize();

        this.closeFileHandle();
    }

    @Override
    public String toString() {
        return "AudioChannel{" +
                "name='" + key + '\'' +
                ", sampleRate=" + sampleRate +
                ", speakerType='" + speakerType + '\'' +
                ", recorder='" + recorder + '\'' +
                ", speaker='" + speaker + '\'' +
                ", role='" + role + '\'' +
                ", canHearEcho=" + canHearEcho +
                ", canRecordWhenSpeaking=" + canRecordWhenSpeaking +
                ", srcCode='" + srcCode + '\'' +
                ", dstCode='" + dstCode + '\'' +
                ", paused=" + paused +
                ", enabled=" + enabled +
                ", isStreamMt=" + isStreamMt +
                ", streamMtSection=" + streamMtSection +
                ", writeToFile=" + writeToFile +
                ", customMtTableId=" + customMtTableId +
                ", wroteCount=" + wroteCount +
                ", useAgc=" + useAgc +
                ", agc=" + agc +
                ", gain=" + gain +
                ", nBytePerPacket=" + nBytePerPacket +
                ", maxHeadSize=" + maxHeadSize +
                ", maxTailSize=" + maxTailSize +
                ", vadBeginSize=" + vadBeginSize +
                ", vadEndSize=" + vadEndSize +
                ", minVadEnergy=" + minVadEnergy +
                ", isTriggered=" + isTriggered +
                ", curPickSession=" + curPickSession +
                ", markVadDataTag=" + markVadDataTag +
                ", vadDetectPattern=" + vadDetectPattern +
                '}';
    }

    public void setListener(final Listener listener) {
        this.listener = listener;
    }

    public Listener getListener() {
        return listener;
    }

    public void setKey(String key) {
        this.key = key;
    }

    public String getKey() {
        return key;
    }

    public void setSampleRate(int sampleRate) {
        this.sampleRate = sampleRate;
    }

    public long getCurPickSession() {
        return curPickSession;
    }

    public void enable() {
        AiSpeechLogUtil.d(TAG,this.getKey() + " 通道激活");
        this.enabled = true;
    }

    public void disable() {
        AiSpeechLogUtil.d(TAG,this.getKey() + " 通道禁用");
        this.enabled = false;
    }

    public boolean isEnabled() {
        return enabled;
    }

    public void resume() {
        AiSpeechLogUtil.d(TAG,this.getKey() + " 通道恢复");
        this.paused = false;
    }

    public void pause() {
        AiSpeechLogUtil.d(TAG,this.getKey() + " 通道暂停");
        this.paused = true;
    }

    public boolean isSynthesizeEnabled() {
        return isSynthesizeEnabled;
    }
    public void setSynthesizeEnabled(boolean enabled) {
        this.isSynthesizeEnabled = enabled;
    }

    public boolean isPlaybackEnabled() {
        return isPlaybackEnabled;
    }
    public void setPlaybackEnabled(boolean playbackEnabled) {
        this.isPlaybackEnabled = playbackEnabled;
    }


    public void openVad(float threshold) {
    }

    public void openVad(float threshold, int type) {
    }

    public void closeVad() {
    }

    /**
     * 设置前端点检测时长, 前端点声音检测时间, 即用户说话多长时间, 认为是唤醒
     * @param nPackets 指定包数, 16ms/包
     */
    public void setVadBegin(int nPackets) {
        assert nPackets <= this.maxHeadSize : "nSamples 必须小于 this.maxHeadSize";
        this.vadBeginSize = Math.min(nPackets, this.maxHeadSize);
    }

    /**
     * 设置后端点检测时长, 后端点静音检测时间, 即用户停止说话多长时间
     * @param nPackets 指定包数, 16ms/包
     */
    public void setVadEnd(int nPackets) {
        AiSpeechLogUtil.d(TAG, String.format(Locale.getDefault(), "%s setVadEnd : %dms", this.getKey(), nPackets * 16));
        this.vadEndSize = this.maxTailSize = nPackets;
    }

    public void setMinVadEnergy(long minVadEnergy) {
        AiSpeechLogUtil.d(TAG, String.format(Locale.getDefault(), "%s setMinVadEnergy : %d(%.2f)", this.getKey(), minVadEnergy, AudioChannel.defalutMinVadEnergy * 1.0 / minVadEnergy));
        this.minVadEnergy = minVadEnergy;
        this.writeAsrTextToFileHandle("\n==========================\n");
        this.writeAsrTextToFileHandle("参数添加: 16ms幅值绝对值之和: " + minVadEnergy + "(" + AudioChannel.defalutMinVadEnergy * 1.0 / minVadEnergy  + ")\n");
        this.writeAsrTextToFileHandle("==========================\n\n");
    }

    public long getMinVadEnergy() {
        return this.minVadEnergy;
    }

    public long getAverageEnergy() {
        int totalVoiceEnergy = 0;
        int voiceCount = 0;
        ConcurrentLinkedDeque<BufferPacket> cache = this.headCache;
        for (Iterator<BufferPacket> it = cache.descendingIterator(); it.hasNext(); ) {
            BufferPacket packet = it.next();
            if (packet.isVoice) {
                totalVoiceEnergy += packet.energy;
                voiceCount++;
            }
        }
        return voiceCount > 0 ? totalVoiceEnergy/voiceCount : 0;
    }

//    public long getMaxSampleEnergy() {
//        return this.outputStream.getMaxSampleEnergy();
//    }

    public int getSampleRate() {
        return sampleRate;
    }

    @Deprecated // 使用 getSpeaker
    public void setSpeakerType(String speakerType) {
        this.speakerType = speakerType;
    }

    public void setRecorder(String recorder) {
        this.recorder = recorder;
    }

    public String getRecorder() {
        return recorder;
    }

    public void setSpeaker(String speaker) {
        this.speaker = speaker;
        this.speakerType = speaker;
    }

    public String getSpeaker() {
        return speaker;
    }

    /**
     * 获取 specker 名字
     * @deprecated 使用 {@link #getSpeaker()}
     */
    @Deprecated
    public String getSpeakerType() {
        return speakerType;
    }

    public void setCanHearEcho(boolean canHearEcho) {
        this.canHearEcho = canHearEcho;
    }

    public boolean isCanHearEcho() {
        return canHearEcho;
    }

    public void setCanRecordWhenSpeaking(boolean canRecordWhenSpeaking) {
        this.canRecordWhenSpeaking = canRecordWhenSpeaking;
    }

    public boolean isCanRecordWhenSpeaking() {
        return canRecordWhenSpeaking;
    }

    public void setSrcCode(String srcCode) {
        this.srcCode = srcCode;
    }

    public String getSrcCode() {
        return srcCode;
    }

    public void setDstCode(String dstCode) {
        this.dstCode = dstCode;
    }

    public String getDstCode() {
        return dstCode;
    }

    public void setRole(String role) {
        this.role = role;
    }

    public String getRole() {
        return role;
    }

    public void setMarkVadDataTag(boolean mark) {
        markVadDataTag = mark;
    }

    public void setWriteToFile(boolean writeToFile) {
        this.writeToFile = writeToFile;
    }
    public boolean isWriteToFile() {
        return writeToFile;
    }

    public void setVadDetectPattern(int vadDetectPattern) {
        this.vadDetectPattern = vadDetectPattern;
    }

    public void setGain(float gain) {
        // 比较精确到小数点后一位的值是否相等
        if ((int)(this.gain * 10 + 0.5) - (int)(gain * 10 + 0.5) == 0) {
            AiSpeechLogUtil.d(TAG,  "通道[" + this.key + "] gain 值不变不做更新: " + this.gain);
            return;
        }
        if (this.agc == null) this.agc = new AgcProcessor(ISpeechConstant.DefaultSampleRate, nBytePerPacket / 2, gain);
        else this.agc.gain = gain;
        AiSpeechLogUtil.d(TAG, "设置 gain 值: " + gain);
        this.gain = gain;
        this.useAgc = (int)(this.gain * 10 + 0.5) != 1; // 等于 1 的话相等于不使用增益
    }

    /**
     * 音量缓存大小, 默认是3; 根据需要设定来控制音量回调周期, 默认为 3*16ms 计算一次音量并返回
     * @param size 大小
     */
    public void setVolumeCacheSize(int size) {
        this.volumeCacheSize = size;
    }

    private void createFileHandle(Context context, String name) {
        fileHandle = new TestRecorder(context, "TK_Record", null, name + "-raw-record", true); // 此处设置录音文件开关
        tagFileHandle = new TestRecorder(context, "TK_Record", null, name + "-raw-record-vadtag", true); // 此处设置录音文件开关
        agcFileHandle = new TestRecorder(context, "TK_Record", null, name + "-raw-record-agc", true); // 此处设置录音文件开关
        textFileHandle = new TestRecorder(context, "TK_Record", null, name + "-raw-record", true, true); // 此处设置录音文件开关
    }

    private void writeToFileHandle(byte[] data) {
        if (this.writeToFile && ++this.handleWroteCount % 300 == 0) AiSpeechLogUtil.e(TAG,this.getKey() + " handle write: " + this.handleWroteCount * 16 + "ms" + (!this.enabled ? ", but not enabled" : ""));

        if (fileHandle != null) fileHandle.write(data);
        if (tagFileHandle != null) tagFileHandle.write(data);
    }

    private void writeTagToFileHandle(VadTag tag) {
        if (markVadDataTag && tagFileHandle != null) {
            short[] tagData = new short[nBytePerPacket];
            Arrays.fill(tagData, tag.value);
            tagFileHandle.write(BytesTrans.getInstance().Shorts2Bytes(tagData));
        }
    }

    private void writeActivityToFileHandle(byte[] data, boolean isVoice) {
        for (int i = 0; i < data.length / nBytePerPacket; i++) {
            if (this.writeToFile && ++this.handleWroteCount % 300 == 0) AiSpeechLogUtil.e(TAG,this.getKey() + " handle write: " + this.handleWroteCount * 16 + "ms" + (!this.enabled ? ", but not enabled" : ""));
        }

        if (fileHandle != null) fileHandle.write(data);

        short vadTag[] = BytesTrans.getInstance().Bytes2Shorts(data);
        if (data.length == nBytePerPacket) {
            Arrays.fill(vadTag, 0, 1, isVoice ? VadTag.Begin_CheckPoint.value : VadTag.END.value);
        }
        if (tagFileHandle != null) tagFileHandle.write(BytesTrans.getInstance().Shorts2Bytes(vadTag));
    }

    public void writeAsrTextToFileHandle(String text) {
        if (textFileHandle != null) {
            String timeStr = DateUtil.getCurrentDateString();
            textFileHandle.write((timeStr + text).getBytes());
        }
    }

    private void closeFileHandle() {
        if (fileHandle != null) fileHandle.close();
        if (tagFileHandle != null) tagFileHandle.close();
        if (agcFileHandle != null) agcFileHandle.close();
        if (textFileHandle != null) textFileHandle.close();
    }

    private final VoiceDetector voiceDetector = new VoiceDetector();
    class VoiceDetector {
        public void onVadBegin(AudioChannel channel) {
            curWroteCount = 0;
            curPickSession = System.currentTimeMillis();
            listener.onVadBegin(channel, curPickSession);

            int nSamplePerFrame = nBytePerPacket / 2;
            writeTagToFileHandle(VadTag.Begin_PRE);

            // 补充前向缓存
            while (headCache.size() > 0) {
                BufferPacket packet = headCache.poll();
                if (packet != null) {
                    if (packet.isStart) writeTagToFileHandle(VadTag.Begin);
                    onActivity(channel, packet.data, packet.isVoice);
                }
            }

            writeTagToFileHandle(VadTag.Begin_CheckPoint);
        }

        public void onVadEnd(AudioChannel channel) {
            writeTagToFileHandle(VadTag.END);
            listener.onVadEnd(channel, curPickSession);
            curPickSession = 0;
//            Log.d(TAG, "onVadEnd: curWroteCount: " + curWroteCount);
        }

        public void onActivity(AudioChannel channel, short[] data, boolean isVoice) {
            if (data.length == 0) {
                AiSpeechLogUtil.e(TAG, channel.key + ", 错误 data 为空");
                return;
            }
            writeActivityToFileHandle(BytesTrans.getInstance().Shorts2Bytes(data), isVoice);

            final int nSamplePerFrame = nBytePerPacket / 2;
            final int frameCount = (data.length + nSamplePerFrame - 1) / nSamplePerFrame; // 计算帧数
            final short[] outputData = new short[data.length];
            for (int i = 0; i < frameCount; i++) {
                int startIndex = i * nSamplePerFrame;
                int endIndex = Math.min(startIndex + nSamplePerFrame, data.length);

                short[] input = Arrays.copyOfRange(data, startIndex, endIndex);
                short[] output = new short[input.length];
                if (useAgc && agc != null) agc.processAgc(output, input); // 增益处理
                else output = input;
                System.arraycopy(output, 0, outputData, startIndex, output.length);

                curWroteCount++;
//                AiSpeechLogUtil.d(TAG, "onActivity: curWroteCount: " + curWroteCount);
                volumeCache.add(input);
                while (volumeCache.size() > volumeCacheSize) {
                    short[] volumeData = new short[volumeCacheSize * nSamplePerFrame]; // 假定长度是 nSamplePerFrame 的倍数
                    int offset = 0;
                    int count = 0;
                    Iterator<short[]> iterator = volumeCache.iterator();
                    while (iterator.hasNext()) {
                        short[] v = iterator.next();
                        System.arraycopy(v, 0, volumeData, offset, Math.min(v.length, nSamplePerFrame));
                        offset = offset + v.length;
                        iterator.remove();
                        count++;
                        if (count == volumeCacheSize) break;
                    }
                    int volume = VolumeUtil.computeVolume(volumeData, volumeData.length);
                    listener.onActivity(AudioChannel.this, curPickSession, volumeData, volume);
                }
            }
            data = outputData;
            if (agcFileHandle != null) agcFileHandle.write(BytesTrans.getInstance().Shorts2Bytes(data));
        }

        public void onSilence(String name, short[] data, boolean isVoice) {
            headCache.add(new BufferPacket(data, isVoice));
            if (headCache.size() > maxHeadSize) {
                BufferPacket packet = headCache.poll();
                if (packet != null) {
                    writeActivityToFileHandle(BytesTrans.getInstance().Shorts2Bytes(packet.data), packet.isVoice);
                    if (agcFileHandle != null) agcFileHandle.write(BytesTrans.getInstance().Shorts2Bytes(packet.data));
                }
            }
        }
    };

    public void setOutputStream(final AudioVadJni outputStream) {
        this.outputStream = outputStream;
//        this.outputStream.setListener(outputStreamListener);
    }

    public void write(final short[] samples_data) {
        if (++this.wroteCount % 300 == 0) AiSpeechLogUtil.d(TAG,this.getKey() + " channel write: " + this.wroteCount * 16 + "ms" + (!this.enabled ? ", but not enabled" : ""));
        long[] energy = {0};
        if (this.enabled) {
            //        this.outputStream.write(samples_data);
            boolean isVoice = this.outputStream.isVoice(samples_data, energy);
//            AiSpeechLogUtil.d(TAG, "write: 是否声音 " + isVoice + "(" + energy[0] + ")");
            this.write(samples_data, isVoice, energy[0]);
        } else {
            Arrays.fill(samples_data, (short) 2); // 代表此通道不被激活
            this.write(samples_data, false, energy[0]);
        }
    }

    public void write(final short[] samples_data, boolean isVoice) {
        if (++this.wroteCount % 300 == 0) AiSpeechLogUtil.d(TAG,this.getKey() + " channel write: " + this.wroteCount * 16 + "ms"  + (!this.enabled ? ", but not enabled" : ""));
        long energy = 0;
        if (this.enabled) {
//        this.outputStream.write(samples_data, isVoice);

            energy = this.outputStream.getEnergy(samples_data);
//        AiSpeechLogUtil.d(TAG, "write: 是否声音 " + isVoice + "(" + energy + ")");
            this.write(samples_data, isVoice, energy);
        } else {
            Arrays.fill(samples_data, (short) 2); // 代表此通道不被激活
            this.write(samples_data, isVoice, energy);
        }
    }

    private void write(final short[] data, boolean isVoice, long energy) {
        // 入队, 溢出则删除
        BufferPacket packet = new BufferPacket(data, isVoice, energy);
        BufferPacket oldest_packet = null;

        if (this.isTriggered == false) {
            this.headCache.add(packet);
            if (this.headCache.size() > this.maxHeadSize) {
                oldest_packet = headCache.poll();
            }
        }

        this.tailCache.add(packet);
        if (this.tailCache.size() > this.maxTailSize) {
            tailCache.poll();
        }

        // 1.未激活时, 判断是否达到激活条件 2.激活时, 判断是否激活失效
        if (this.isTriggered) {
            if (this.detectTriggeredInvaild(this.tailCache)) {
                this.isTriggered = false;
                AiSpeechLogUtil.d(TAG, "write: vad 唤醒已结束 " + this.getKey());

                this.voiceDetector.onActivity(this, packet.data, packet.isVoice); // 发送完激活状态下的最后一包
                this.voiceDetector.onVadEnd(this);
            } else {
                this.voiceDetector.onActivity(this, packet.data, packet.isVoice);
            }
        } else {
            if (this.detectTriggered(this.headCache)) {
                this.isTriggered = true;
                AiSpeechLogUtil.d(TAG, "write: vad 唤醒已开始 " + this.getKey());

                this.voiceDetector.onVadBegin(this);
            } else {
                if (oldest_packet != null) {
                    writeActivityToFileHandle(BytesTrans.getInstance().Shorts2Bytes(oldest_packet.data), oldest_packet.isVoice);
                    if (agcFileHandle != null) agcFileHandle.write(BytesTrans.getInstance().Shorts2Bytes(oldest_packet.data));
                }
            }
        }
    }

    // 根据不同设备选择激活方式,
    // 像 004 等降噪处理后音量较小的音频源使用 detectTriggeredByDoubleLevelEnergy
    // Phone 和 TWS 耳机等音量正常有大量背景噪声的使用 detectTriggeredBySingleEnergy 或 detectTriggeredByOnlyVoice
    private boolean detectTriggered(ConcurrentLinkedDeque<BufferPacket> cache) {
        if (this.vadDetectPattern == VadDetectPattern_DoubleLevelEnergy) return detectTriggeredByDoubleLevelEnergy(cache);
        return detectTriggeredBySingleEnergy(cache);
//        return detectTriggeredByOnlyVoice(cache);
    }

    private boolean detectTriggeredByDoubleLevelEnergy(ConcurrentLinkedDeque<BufferPacket> cache) {
        int checkSize = this.vadBeginSize; // 倒序检测缓存
        if (cache.size() < this.vadBeginSize) {
            return false;
        }
        int voiceCount = 0; // 声音数量
        int hCount = 0; // 满足高能量的声音包数
        int lCount = 0; // 满足低能量的声音包数
        long maxSampleEnergy = 0;

        boolean isTriggered = true;

        for (Iterator<BufferPacket> it = cache.descendingIterator(); it.hasNext(); ) {
            BufferPacket packet = it.next();
            if (!packet.isVoice) {
//            isTriggered = false;
//            break;
            } else {
                if (packet.energy > defalutMinVadEnergy) {
                    hCount++;
                }
                if (packet.energy > this.minVadEnergy) {
                    lCount++;
                }
                if (maxSampleEnergy < packet.energy) {
                    maxSampleEnergy = packet.energy;
                }
                voiceCount++;
            }
            checkSize--;
            if (checkSize <= 0) {
                boolean isHigh = (voiceCount >= 4 && hCount > 0);
                boolean isLow = (voiceCount >= 10 && lCount > 5);
                isTriggered = isHigh || isLow;
                // 最小能量大于的情况, 直接使用默认能量
                if (this.minVadEnergy >= defalutMinVadEnergy) {
                    isTriggered = isHigh;
                }
                if (isTriggered) packet.isStart = true; // 此包数据必然是声音开始的包
                break;
            }
        }
        return isTriggered;
    }

    private boolean detectTriggeredBySingleEnergy(ConcurrentLinkedDeque<BufferPacket> cache) {
        int checkSize = this.vadBeginSize; // 倒序检测缓存
        if (cache.size() < this.vadBeginSize) {
            return false;
        }
        int voiceCount = 0; // 声音数量
        int lCount = 0; // 满足低能量的声音包数

        boolean isTriggered = true;
        for (Iterator<BufferPacket> it = cache.descendingIterator(); it.hasNext(); ) {
            BufferPacket packet = it.next();
            if (!packet.isVoice) {
//            isTriggered = NO;
//            break;
            } else {
                if (packet.energy > this.minVadEnergy) lCount++;
                voiceCount++;
            }
            checkSize--;
            if (checkSize <= 0) {
//                AiSpeechLogUtil.d(TAG, "detectTriggeredBySingleEnergy vad 识别 voiceCount:" + voiceCount + " lCount:" + lCount);
                isTriggered = voiceCount >= this.vadBeginSize && lCount > this.vadBeginSize / 2;
//                isTriggered = voiceCount >= 10 && lCount > 5;
                if (isTriggered) {
                    packet.isStart = true; // 此包数据必然是声音开始的包
                }
                break;
            }
        }
        return isTriggered;
    }

    private boolean detectTriggeredByOnlyVoice(ConcurrentLinkedDeque<BufferPacket> cache) {
        int checkSize = this.vadBeginSize; // 倒序检测缓存
        if (cache.size() < this.vadBeginSize) {
            return false;
        }

        boolean isTriggered = true;
        for (Iterator<BufferPacket> it = cache.descendingIterator(); it.hasNext(); ) {
            BufferPacket packet = it.next();
            if (!packet.isVoice) {
                isTriggered = false;
                break;
            }
            checkSize--;
            if (checkSize <= 0) {
                if (isTriggered) packet.isStart = true; // 此包数据必然是声音开始的包
                break;
            }
        }
        return isTriggered;
    }

    private boolean detectTriggeredInvaild(ConcurrentLinkedDeque<BufferPacket> cache) {
        // 数量未达到检测条件, 认为仍然处在激活状态
        if (cache.size() < this.vadEndSize) {
            return false;
        }
        int detectedCount = 0;
        boolean isTriggeredInvaild = true;
        for (Iterator<BufferPacket> it = cache.descendingIterator(); it.hasNext(); ) {
            // 检测指定数量
            detectedCount++;
            if (detectedCount > this.vadEndSize) {
                break;
            }
            BufferPacket packet = it.next();
            if (packet.isVoice) {
                isTriggeredInvaild = false;
                break;
            }
        }
        return isTriggeredInvaild;
    }


    /**
     * 注意: 正在"唤醒"的通道, 如果被重置后不能触发 vadend
     */
    public void setUnTriggered() {
        AiSpeechLogUtil.d(TAG,this.getKey() + " setUnTriggered vad 识别强制停止");
        this.isTriggered = false;

        this.headCache.clear();
        this.tailCache.clear();
    }

    public void setTriggered() {
        AiSpeechLogUtil.e(TAG,this.getKey() + " setTriggered vad 识别强制开始");
        this.isTriggered = true;
    }

    public void resetVadState(long taskId) {
//        if (taskId == curPickSession) {
            AiSpeechLogUtil.d(TAG, "resetVadState: " + taskId);
            writeTagToFileHandle(VadTag.END_FORCE);
            listener.onVadEnd(this, taskId);
//        } else {
//            AiSpeechLogUtil.d(TAG, "resetVadState: 不重置, taskId:" + taskId + " curPickSession:" + curPickSession);
//        }
    }

    public void forceResetVadState() {
        AiSpeechLogUtil.d(TAG, "forceResetVadState: " + curPickSession);
        writeTagToFileHandle(VadTag.END_FORCE);
        listener.onVadEnd(this, curPickSession);
    }

    public interface Listener {
        void onVadBegin(AudioChannel channel, long session);

        void onVadEnd(AudioChannel channel, long session);

        /**
         * 语音唤醒数据回调
         * @param channel 唤醒通道
         * @param session 唤醒任务 id
         * @param data 当前时刻音频
         * @param volume 当前时刻音量
         */
        void onActivity(AudioChannel channel, long session, short[] data, float volume);
        // FIXME: 2022/9/2 需修改成返回 BufferPacket
//        void onActivity(AudioChannel channel, long session, BufferPacket packet);
    }

    static class Builder {
        private String name;
        private int sampleRate;
        private String speakerType;
        private boolean canHearEcho;
        private String srcCode;
        private String dstCode;
        private AudioVadJni outputStream;

        public Builder name(String name) {
            this.name = name;
            return this;
        }

        public Builder sampleRate(int sampleRate) {
            this.sampleRate = sampleRate;
            return this;
        }

        public Builder canHearEcho(boolean canHearEcho) {
            this.canHearEcho = canHearEcho;
            return this;
        }

        public Builder speakerType(String speakerType) {
            this.speakerType = speakerType;
            return this;
        }

        public Builder srcCode(String srcCode) {
            this.srcCode = srcCode;
            return this;
        }

        public Builder dstCode(String dstCode) {
            this.dstCode = dstCode;
            return this;
        }

        public Builder outputStream(AudioVadJni outputStream) {
            this.outputStream = outputStream;
            return this;
        }

        public AudioChannel build() {
            return new AudioChannel(this);
        }

    }
}
