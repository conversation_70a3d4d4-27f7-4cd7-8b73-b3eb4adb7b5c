package co.timekettle.speech;

import android.media.MediaRecorder;

public class AudioRecordOptions {
   /* FIXME: 后期在通道上控制, 若通道发现正在处理播放任务, 通道暂停输入的语音数据 */
   public static final String CAN_RECORD_WHEN_SPEAKING = "canRecordWhenSpeaking"; // 播放的时候是否允许录音
   public static final String RECORD_AUDIO_FILENAME = "recordAudioFilename"; // 录音文件名(不带文件后缀)
   public static final String WRITE_TO_FILE = "recordWriteToFile"; // 是否写成录音文件, 调试使用
   public static final String USE_GCC = "useGCC"; // 是否是否使用 gcc 算法
   public static final String MEDIA_RECORDER_SOURCE = "media_recorder_source"; // 系统录音录音源
   public static final String BLE_NEED_MAKEUP = "bleNeedMakeup"; // 是否需要补齐 ble 丢包(gcc 需要对齐)
   public static final String AUDIO_GAIN = "recorder_audio_gain"; // 增益处理, 例如: 1.0表示不加增益, 1.5表示1.5倍增益
   public static final String ONLY_PROCESS_AS_DUAL = "ONLY_PROCESS_AS_DUAL"; // 仅仅作为立体声处理，不发送到speech进行识别
}
