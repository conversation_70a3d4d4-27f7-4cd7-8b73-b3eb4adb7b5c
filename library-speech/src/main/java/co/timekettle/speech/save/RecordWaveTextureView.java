package co.timekettle.speech.save;

import android.content.Context;
import android.graphics.Bitmap;
import android.graphics.Canvas;
import android.graphics.Color;
import android.graphics.Paint;
import android.graphics.PorterDuff;
import android.graphics.Rect;
import android.graphics.SurfaceTexture;
import android.util.AttributeSet;
import android.view.Surface;
import android.view.TextureView;
import android.view.View;
import android.view.ViewTreeObserver;

import androidx.annotation.NonNull;

import java.io.ByteArrayInputStream;
import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.io.ObjectInputStream;
import java.io.ObjectOutputStream;
import java.util.ArrayList;
import java.util.List;

import co.timekettle.speech.utils.AiSpeechLogUtil;

public class RecordWaveTextureView extends TextureView {


    public static final String MAX = "max_volume"; //map中的key
    public static final String MIN = "min_volume";//map中的key
    private static final String TAG = "RecordWaveTextureView";

    final protected Object mLock = new Object();

    private Context mContext;

    private Bitmap mBackgroundBitmap;

    private Paint mPaint;

    private Canvas mBackCanVans = new Canvas();

    private ArrayList<Short> mRecDataList = new ArrayList<>();

    private drawThread mInnerThread;

    private int mWidthSpecSize;

    private int mHeightSpecSize;

    private int mScale = 1;

    private int mBaseLine;

    private int mOffset = -11;//波形之间线与线的间隔

    private boolean mAlphaByVolume; //是否更具声音大小显示清晰度

    private boolean mIsDraw = true;

    private boolean mDrawBase = true;

    private boolean mDrawReverse = false;//反方向

    private boolean mDataReverse = false;//数据反方向

    private int mWaveCount = 2;

    private int mWaveColor = Color.WHITE;

    private int mColorPoint = 1;

    private int mPreFFtCurrentFrequency;

    private int mColorChangeFlag;

    private int mColorBack = Color.TRANSPARENT;

    private int mDrawStartOffset = 0;

    private Surface mSurface;

    private Rect mRect = new Rect();

    private int mMaxSize = 360;

    private int mWaveSpeed = 360;

    private String name = "-";

    public RecordWaveTextureView(Context context, String name) {
        super(context);
        init(context, null);
        this.name = name;
    }

    @Override
    protected void onDetachedFromWindow() {
        super.onDetachedFromWindow();

        mIsDraw = false;
        if (mBackgroundBitmap != null && !mBackgroundBitmap.isRecycled()) {
            mBackgroundBitmap.recycle();
        }
    }

    public void init(Context context, AttributeSet attrs) {
        mContext = context;
        if (isInEditMode())
            return;

        mWaveCount = 2;
        mOffset = dip2px(context, 1);

        if (mWaveCount < 1) {
            mWaveCount = 1;
        } else if (mWaveCount > 2) {
            mWaveCount = 2;
        }

        mPaint = new Paint();
        mPaint.setColor(mWaveColor);

        setSurfaceTextureListener(new SurfaceTextureListener() {
            @Override
            public void onSurfaceTextureAvailable(SurfaceTexture surface, int width, int height) {
                mSurface = new Surface(surface);
                updateBackgroundColor();
            }

            @Override
            public void onSurfaceTextureSizeChanged(SurfaceTexture surface, int width, int height) {
            }

            @Override
            public boolean onSurfaceTextureDestroyed(SurfaceTexture surface) {

                synchronized (mLock) {
                    mSurface = null;
                }
                return false;
            }

            @Override
            public void onSurfaceTextureUpdated(SurfaceTexture surface) {
            }
        });

    }

    @Override
    protected void onMeasure(int widthMeasureSpec, int heightMeasureSpec) {
        super.onMeasure(widthMeasureSpec, heightMeasureSpec);
        AiSpeechLogUtil.e(TAG, "onMeasure: " + this.name + " " + mBackgroundBitmap);
        createBackGroundBitmap();
    }

    @Override
    protected void onLayout(boolean changed, int left, int top, int right, int bottom) {
        super.onLayout(changed, left, top, right, bottom);
        mRect.top = top;
        mRect.left = left;
        mRect.right = right;
        mRect.bottom = bottom;
    }

    @Override
    protected void onVisibilityChanged(@NonNull View changedView, int visibility) {
        super.onVisibilityChanged(changedView, visibility);
        AiSpeechLogUtil.e(TAG, "onVisibilityChanged: " + visibility + " " + this.name + " " + mBackgroundBitmap);
        if (visibility == VISIBLE) {
            if (mBackgroundBitmap == null) createBackGroundBitmap();
        } else if (visibility == INVISIBLE) {
            // 销毁

        }
    }

    private void createBackGroundBitmap() {
        ViewTreeObserver vto = getViewTreeObserver();
        vto.addOnPreDrawListener(new ViewTreeObserver.OnPreDrawListener() {
            @Override
            public boolean onPreDraw() {
                if (getWidth() > 0 && getHeight() > 0) {
                    int w = getWidth();
                    int h = getHeight();
                    if (w != mWidthSpecSize || h != mHeightSpecSize) { // 尺寸有改变再更新
                        mWidthSpecSize = getWidth();
                        mHeightSpecSize = getHeight();
                        mBaseLine = mHeightSpecSize / 2;
                        mBackgroundBitmap = Bitmap.createBitmap(mWidthSpecSize, mHeightSpecSize, Bitmap.Config.ARGB_8888);
                        AiSpeechLogUtil.e(TAG, "createBitmap " + RecordWaveTextureView.this.name + " " + mBackgroundBitmap);
                        mBackCanVans.setBitmap(mBackgroundBitmap);
                    }
                    ViewTreeObserver vto = getViewTreeObserver();
                    vto.removeOnPreDrawListener(this);
                }
                return true;
            }
        });
    }

    private void updateBackgroundColor() {
        synchronized (mLock) {
            if (mSurface != null) {
                Canvas canvas = mSurface.lockCanvas(mRect);
                canvas.drawColor(mColorBack);
                mSurface.unlockCanvasAndPost(canvas);
            }
        }
    }


    //内部类的线程
    private class drawThread extends Thread {
        void waitAMoment() {
            //休眠暂停资源
            try {
                Thread.sleep(30);
                Thread.yield();
            } catch (InterruptedException e) {
                e.printStackTrace();
            }
        }

        @SuppressWarnings("unchecked")
        @Override
        public void run() {
            while (mIsDraw) {
                // mBackgroundBitmap 不存在则不处理数据
                if (mBackgroundBitmap == null) {
                    waitAMoment();
                    continue;
                }
                ArrayList<Short> dataList = new ArrayList<>();
                synchronized (mRecDataList) {
                    if (mRecDataList.size() != 0) {
                        try {
                            dataList = (ArrayList<Short>) deepCopy(mRecDataList);// 保存  接收数据
                        } catch (Exception e) {
                            e.printStackTrace();
                            waitAMoment();
                            continue;
                        }
                    }
                }
                resolveToWaveData(dataList);

                if (mBackCanVans != null) {
                    mBackCanVans.drawColor(mColorBack, PorterDuff.Mode.CLEAR);
                    mBackCanVans.drawColor(mColorBack);
                    int drawBufsize = dataList.size();
                    /*判断大小，是否改变显示的比例*/
                    int startPosition = (mDrawReverse) ? mWidthSpecSize - mDrawStartOffset : mDrawStartOffset;
                    int jOffset = (mDrawReverse) ? -mOffset : mOffset;
                    if (mDrawBase) {
                        if (mDataReverse) {
                            mBackCanVans.drawLine(startPosition, mBaseLine, 0, mBaseLine, mPaint);
                        } else {
                            mBackCanVans.drawLine(startPosition, mBaseLine, mWidthSpecSize, mBaseLine, mPaint);
                        }
                    }
                    if (mDataReverse) {
                        for (int i = drawBufsize - 1, j = startPosition; i >= 0; i--, j += jOffset) {
                            Short sh = dataList.get(i);
                            drawNow(sh, j);
                        }
                    } else {
                        for (int i = 0, j = startPosition; i < drawBufsize; i++, j += jOffset) {
                            Short sh = dataList.get(i);
                            drawNow(sh, j);
                        }
                    }

                    if (mSurface != null) {

                        synchronized (mLock) {
                            if (mSurface != null && mIsDraw) {
                                Canvas canvas = mSurface.lockCanvas(mRect);
                                canvas.drawColor(mColorBack, PorterDuff.Mode.CLEAR);
                                canvas.drawBitmap(mBackgroundBitmap, 0, 0, mPaint);
                                mSurface.unlockCanvasAndPost(canvas);
                            }
                        }
                    }
                }
                //休眠暂停资源
                waitAMoment();
            }
        }
    }

    private void drawNow(Short sh, int j) {
        int mScale = WaveScale.getInstance().get();
        if (sh != null) {
            short max = (short) (mBaseLine - sh / mScale);
            short min;
            if (mWaveCount == 2) {
                min = (short) (sh / mScale + mBaseLine);
            } else {
                min = (short) (mBaseLine);
            }
            mBackCanVans.drawLine(j, mBaseLine, j, max, mPaint);
            mBackCanVans.drawLine(j, min, j, mBaseLine, mPaint);
        }
    }

    /**
     * deepClone to avoid ConcurrentModificationException
     *
     * @param src list
     * @return dest
     */
    public List deepCopy(List src) throws IOException, ClassNotFoundException {
        ByteArrayOutputStream byteOut = new ByteArrayOutputStream();
        ObjectOutputStream out = new ObjectOutputStream(byteOut);
        out.writeObject(src);

        ByteArrayInputStream byteIn = new ByteArrayInputStream(byteOut.toByteArray());
        ObjectInputStream in = new ObjectInputStream(byteIn);
        List dest = (List) in.readObject();
        return dest;
    }


    /**
     * 更具当前块数据来判断缩放音频显示的比例
     *
     * @param list 音频数据
     */
    private void resolveToWaveData(ArrayList<Short> list) {
        short allMax = 0;
        for (int i = 0; i < list.size(); i++) {
            Short sh = list.get(i);
            if (sh != null && sh > allMax) {
                allMax = sh;
            }
        }
        int curScale = allMax / mBaseLine;
//        if (curScale > mScale) {
//            mScale = ((curScale == 0) ? 1 : curScale);
//        }

        WaveScale.getInstance().set(curScale);
    }

    /**
     * 开始绘制
     */
    public void startView() {

        if (mInnerThread != null && mInnerThread.isAlive()) {
            AiSpeechLogUtil.e("WAVE", "动画: mInnerThread != null && mInnerThread.isAlive()");
            mIsDraw = false;
            while (mInnerThread.isAlive()) ;
            mBackCanVans.drawColor(mColorBack, PorterDuff.Mode.CLEAR);
        }

        mIsDraw = true;
        mInnerThread = new drawThread();
        mInnerThread.start();
    }

    /**
     * 停止绘制
     */
    public void stopView() {
        mIsDraw = false;
        synchronized (mRecDataList) {
            mRecDataList.clear();
        }
        if (mInnerThread != null) {
            while (mInnerThread.isAlive()) ;
        }
        mBackCanVans.drawColor(mColorBack, PorterDuff.Mode.CLEAR);
    }

    public boolean isAlphaByVolume() {
        return mAlphaByVolume;
    }

    /**
     * 是否更具声音大小显示清晰度
     */
    public void setAlphaByVolume(boolean alphaByVolume) {
        this.mAlphaByVolume = alphaByVolume;
    }

    /**
     * 将这个list传到Record线程里，对其不断的填充
     * <p>
     * Map存有两个key，一个对应AudioWaveView的MAX这个key,一个对应AudioWaveView的MIN这个key
     *
     * @return 返回的是一个map的list
     */
    public ArrayList<Short> getRecList() {
        return mRecDataList;
    }

    /**
     * 设置线与线之间的偏移
     *
     * @param offset 偏移值 pix
     */
    public void setOffset(int offset) {
        this.mOffset = offset;
    }


    public int getWaveColor() {
        return mWaveColor;
    }

    /**
     * 设置波形颜色
     *
     * @param waveColor 音频颜色
     */
    public void setWaveColor(int waveColor) {
        this.mWaveColor = waveColor;
        if (mPaint != null) {
            mPaint.setColor(mWaveColor);
        }
    }

    /**
     * 设置波形数量
     *
     * @param waveCount 波形数量 1或者2
     */
    public void setWaveCount(int waveCount) {
        mWaveCount = waveCount;
        if (mWaveCount < 1) {
            mWaveCount = 1;
        } else if (mWaveCount > 2) {
            mWaveCount = 2;
        }
    }

    /**
     * 设置自定义的paint
     */
    public void setLinePaint(Paint paint) {
        if (paint != null) {
            mPaint = paint;
        }
    }

    /**
     * dip转为PX
     */
    private int dip2px(Context context, float dipValue) {
        float fontScale = context.getResources().getDisplayMetrics().density;
        return (int) (dipValue * fontScale + 0.5f);
    }

    /**
     * 是否画出基线
     *
     * @param drawBase
     */
    public void setDrawBase(boolean drawBase) {
        mDrawBase = drawBase;
    }

    /**
     * 绘制相反方向
     */
    public void setDrawReverse(boolean drawReverse) {
        this.mDrawReverse = drawReverse;
    }

    /**
     * 数据相反方向，可配合上面setDrawReverse一起使用
     */
    public void setDataReverse(boolean dataReverse) {
        this.mDataReverse = dataReverse;
    }

    /**
     * 绘制开始偏移量
     */
    public void setDrawStartOffset(int drawStartOffset) {
        this.mDrawStartOffset = drawStartOffset;
    }

    /**
     * 背景颜色
     */
    public void setColorBack(int colorBack) {
        this.mColorBack = colorBack;
        updateBackgroundColor();
    }

    public void setMaxSize(int maxSize) {
        mMaxSize = maxSize;
    }

    public void setWaveSpeed(int speed) {
        mWaveSpeed = speed;
    }

    public void setDataList(short[] shorts, int readSize) {
        synchronized (mRecDataList) {
            if (mRecDataList != null) {
                int length = readSize / mWaveSpeed;
                short resultMax = 0, resultMin = 0;
                for (short i = 0, k = 0; i < length; i++, k += mWaveSpeed) {
                    for (short j = k, max = 0, min = 1000; j < k + mWaveSpeed; j++) {
                        if (shorts[j] > max) {
                            max = shorts[j];
                            resultMax = max;
                        } else if (shorts[j] < min) {
                            min = shorts[j];
                            resultMin = min;
                        }
                    }
                    if (mRecDataList.size() > mMaxSize) {
                        mRecDataList.remove(0);
                    }
                    mRecDataList.add(resultMax);
                }
            }
        }
    }

}
