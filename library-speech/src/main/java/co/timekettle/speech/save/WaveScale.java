package co.timekettle.speech.save;

public class WaveScale {

    private static WaveScale instance = null;
    private int mScale = 1;

    public static WaveScale getInstance() {
        if (instance == null) {
            instance = new WaveScale();
        }
        return instance;
    }

    public void clean() {
        mScale = 1;
    }

    public void set(int curScale) {
        if (curScale > mScale) {
            mScale = ((curScale == 0) ? 1 : curScale);
        }
    }

    public int get() {
        return mScale;
    }
}
