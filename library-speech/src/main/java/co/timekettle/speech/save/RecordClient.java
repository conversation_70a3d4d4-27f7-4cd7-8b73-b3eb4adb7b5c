package co.timekettle.speech.save;

import android.content.Context;
import android.graphics.Color;

import co.timekettle.speech.utils.AiSpeechLogUtil;
import co.timekettle.speech.utils.WaveHeader;

import java.io.File;
import java.io.FileInputStream;
import java.io.FileNotFoundException;
import java.io.FileOutputStream;
import java.io.IOException;
import java.util.ArrayList;

public class RecordClient {

    private static RecordClient instance = null;

    protected ArrayList<RecordWaveTextureView> mRecordWaveViews = new ArrayList<>();
    private FileOutputStream mFileOutputStream;

    private String mWavFileName;
    private String mPcmFileName;
    private int mChannels;

    private String dirPath;

    public static RecordClient getInstance() {
        if (instance == null) {
            instance = new RecordClient();
        }
        return instance;
    }

    /**
     * 开始音频文件写入文件, 并产生波形图
     *
     * @param fileName 录音文件名(不带后缀), 内部生成 wav 文件提供播放
     * @param nChannel 录音通道数
     * @param isSaveToFile 是否产生录音文件
     */
    public void start(final Context context, final String fileName, final int nChannel, boolean isSaveToFile) {

        WaveScale.getInstance().clean();

        stop();

        mChannels = nChannel;
        if (mRecordWaveViews == null) mRecordWaveViews = new ArrayList<>();
        for (int i = 0; i < nChannel; i++) {
            RecordWaveTextureView view = new RecordWaveTextureView(context, fileName + "-" + i);
            view.setWaveColor(Color.parseColor("#0A85FF"));
            view.setColorBack(Color.WHITE);
            mRecordWaveViews.add(view);
            view.startView();
        }

        if (isSaveToFile) {
            this.open(fileName, context);
        }
    }

    public void stop() {
        if (mRecordWaveViews != null) {
            for (RecordWaveTextureView view : mRecordWaveViews) {
                view.stopView();
            }
        }
        mRecordWaveViews = null;
        close();
    }

    /**
     * 获得波形图
     */
    public RecordWaveTextureView getWaveView() {
        return this.mRecordWaveViews != null && this.mRecordWaveViews.size() > 0 ? this.mRecordWaveViews.get(0) : null;
    }

    /**
     * 获得文件路径, 不检查是否存在, 可能已被外部删除
     */
    public String getRecordFilePath(String fileName) {
        return this.dirPath + "/" + fileName + ".wav";
    }

    public void sendData(int index, short[] shorts, int readSize) {

        if (mRecordWaveViews == null || mRecordWaveViews.size() == 0
                || (mRecordWaveViews.size() > 1 && index >= mRecordWaveViews.size())) {
            return;
        }

        if (index >= mRecordWaveViews.size()) return;
        RecordWaveTextureView view = mRecordWaveViews.get(index);
        if (view != null) {
            view.setDataList(shorts, readSize);
        }
    }

    private String open(String fileName, final Context context) {
        this.dirPath = context.getExternalFilesDir(null).getAbsolutePath() + "/ZeroRecordFiles";

        File Folder = new File(this.dirPath);
        if (!Folder.exists()) {
            Folder.mkdirs();
        }

        mPcmFileName = this.dirPath + "/" + fileName + ".pcm";
        mWavFileName = this.dirPath + "/" + fileName + ".wav";

        try {
            mFileOutputStream = new FileOutputStream(mPcmFileName);
            return mWavFileName;
        } catch (FileNotFoundException e) {
            e.printStackTrace();
        }

        return null;
    }

    /**
     * 保存语音数据至文件
     */
    public void write(byte[] data) {
        if (mFileOutputStream != null) {
            try {
                mFileOutputStream.write(data);
                mFileOutputStream.flush();
            } catch (IOException e) {
                e.printStackTrace();
            }
        }
    }

    /**
     * 保存语音数据至文件
     */
    public void write(byte[] data, int pos, int length) {
        if (mFileOutputStream != null) {
            try {
                mFileOutputStream.write(data, pos, length);
                mFileOutputStream.flush();
            } catch (IOException e) {
                e.printStackTrace();
            }
        }
    }

    public int calculateRealVolume(short[] buffer, int readSize) {
        double sum = 0;
        for (int i = 0; i < readSize; i++) {
            // 这里没有做运算的优化，为了更加清晰的展示代码
            sum += buffer[i] * buffer[i];
        }
        if (readSize > 0) {
            double amplitude = sum / readSize;
            return (int) Math.sqrt(amplitude);
        }

        return 0;
    }

    private void close() {
        try {
            if (mFileOutputStream != null) {
                mFileOutputStream.close();
                mFileOutputStream = null;
                final String pcmFileName = mPcmFileName;
                final String wavFileName = mWavFileName;
                final int channels = mChannels;

                new Thread(new Runnable() {
                    @Override
                    public void run() {
                        convertWaveFile(pcmFileName, wavFileName, 16000, channels);
                        deleteSingleFile(pcmFileName);
                    }
                }).start();
            }
        } catch (IOException e) {
            e.printStackTrace();
        }
    }

    // 这里得到可播放的音频文件
    private void convertWaveFile(String pcmFileName, String wavFileName, int sampleRate, int channels) {

        FileInputStream in;
        FileOutputStream out;
        int totalAudioLen = 0;
        short byteRate = 16;
        byte[] data = new byte[3200];
        try {
            in = new FileInputStream(pcmFileName);
            out = new FileOutputStream(wavFileName);
            totalAudioLen = (int)in.getChannel().size();
            WaveHeader header = new WaveHeader(WaveHeader.FORMAT_PCM, (short) channels, sampleRate, byteRate, totalAudioLen);
            header.write(out);
            int size;
            while ((size = in.read(data)) != -1) {
                out.write(data, 0, size);
                out.flush();
            }

            in.close();
            out.close();

        } catch (FileNotFoundException e) {
            e.printStackTrace();
        } catch (IOException e) {
            e.printStackTrace();
        }
    }

    private boolean deleteSingleFile(String filePath) {
        File file = new File(filePath);
        if (file.exists() && file.isFile()) {
            if (file.delete()) {
                return true;
            } else {
                AiSpeechLogUtil.e("RecordBase", "删除文件:" + filePath + " 失败");
                return false;
            }
        } else {
            AiSpeechLogUtil.e("RecordBase", "删除文件:" + filePath + " 不存在");
            return false;
        }
    }
}
