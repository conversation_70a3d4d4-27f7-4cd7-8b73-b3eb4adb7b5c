package co.timekettle.speech;

import android.media.AudioFormat;
import android.media.MediaRecorder;

public class ISpeechConstant {

    public static float DefaultSampleRate = 16000.0f;
    public static int DefaultAudioSource = MediaRecorder.AudioSource.MIC;
    public static int DefaultChannelConfig = AudioFormat.CHANNEL_IN_MONO;
    public static int DefaultAudioFormat = AudioFormat.ENCODING_PCM_16BIT;
    public static int DefaultSamplesPerPacketInMono = 256; // 单通道 16000采样率 16位 16ms 数据
    public static int DefaultBytesPerPacketInMono = 512; // 单通道 16000采样率 16位 16ms 数据

    public static int BytesPerPacketInStereo = DefaultBytesPerPacketInMono * 2; // 单通道 16000采样率 16位 16ms 数据
    public static int SamplesPerPacketInStereo = DefaultSamplesPerPacketInMono * 2; // 单通道 16000采样率 16位 16ms 数据

    public static boolean useOpus = true;

    public enum RECORDER {
        PHONE("phone"),
        T1PHONE("t1phone"),
        T1PHONEAEC("t1phone_aec"),
        BLE("ble"),
        HEADSET("headset"),
        USB("usb");

        private String cname;
        RECORDER(String name) {
            cname = name;
        };
    }

    public enum SPEAKER {
        PHONE("phone"),
        T1PHONE("t1phone"), // 注意通道名暂时是 AudioChannel.Role.Self.toString();  AudioChannel.Role.Other.toString();
        BLE("ble"),
        HEADSET("headset");

        private String cname;
        SPEAKER(String name) {
            cname = name;
        };
    }

    public enum TRANSLATOR {
        CUSTOM("custom"),
        IFLY_OFFLINE_MT("iflytekOfflineMt"),
        NIU_OFFLINE_MT("NiuOfflineMt"),
        ISPEECH("ispeech");

        private String cname;
        TRANSLATOR(String name) {
            cname = name;
        };
        public String getName() {
            return cname;
        }
    }

    public enum SPEECHTRANSLATION {
        SPEECHTRANSLATION("ispeech_translation"),
        SPEECHTRANSLATION_TWOSTEP("ispeech_translation_twostep"),
        SPEECHTRANSLATION_LID("ispeech_translation_lid");

        private final String cname;
        SPEECHTRANSLATION(String name) {
            cname = name;
        };
        public String getName() {
            return cname;
        }
    }

    public enum SYNTHESIZER {
        SYNTHESIZER_HOYA("synthesize_hoya"),
        SYNTHESIZER_ISPEECH("synthesize_ispeech"),
        SYNTHESIZER_XZY("synthesize_xzy");

        private final String cname; // 暂不使用
        SYNTHESIZER(String name) {
            cname = name;
        };
    }
}
