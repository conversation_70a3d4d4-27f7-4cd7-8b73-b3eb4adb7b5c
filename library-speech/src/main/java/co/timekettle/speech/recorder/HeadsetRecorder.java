package co.timekettle.speech.recorder;


import android.Manifest;
import android.content.Context;
import android.content.pm.PackageManager;
import android.media.AudioDeviceInfo;
import android.media.AudioFormat;
import android.media.AudioManager;
import android.media.AudioRecord;
import android.media.MediaRecorder;
import android.os.Build;
import android.provider.Settings;

import androidx.core.content.ContextCompat;

import co.timekettle.speech.ISpeechConstant;
import co.timekettle.speech.utils.AiSpeechLogUtil;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.HashMap;
import java.util.Locale;
import java.util.Map;
import java.util.concurrent.Semaphore;

public class HeadsetRecorder extends AudioRecorderBase {

    public static final String TAG = "HeadsetRecorder";
    private AudioRecord mAudioRecord = null;
    private Context context;
    private ArrayList<String> DeviceModeList = new ArrayList<String>(Arrays.asList("asus_z01kd", "xxx", "xxxx")); //发现某些手机在设置mode了才能保证每次都能正常录音。
    private ArrayList<String> DeviceListMicSource = new ArrayList<String>(Arrays.asList("sm-e5260", "xxx", "xxxx")); //发现某些手机需要设置audioSourceMic，才能录音


    private Semaphore stopSema = null;

    public HeadsetRecorder(Context context) {
        super(context, ISpeechConstant.RECORDER.HEADSET.toString());
        this.context = context;

        /*
    *   AudioSource.DEFAULT:默认音频来源
        AudioSource.MIC:麦克风（常用）
        AudioSource.VOICE_UPLINK:电话上行 ----> 初始化会失败
        AudioSource.VOICE_DOWNLINK:电话下行 ----> 初始化会失败
        AudioSource.VOICE_CALL:电话、含上下行
        AudioSource.CAMCORDER:摄像头旁的麦克风
        AudioSource.VOICE_RECOGNITION:语音识别
        AudioSource.VOICE_COMMUNICATION:语音通信
    * */
        defaultAudioSource = MediaRecorder.AudioSource.VOICE_RECOGNITION;
        if (DeviceListMicSource.contains(Build.MODEL.toLowerCase())) {
            defaultAudioSource = MediaRecorder.AudioSource.MIC; // 从手机拾音，而不从蓝牙耳机拾音的机型，可以用MIC来试试，三星 sm-e5260 适用
        }
        dropSampleCount = 5;
    }

    @Override
    public void setDropSampleCount() {
        dropSampleCount = 5;
    }

    @Override
    public void start(final Map<String, Object> options) {
        super.start(options);
        AiSpeechLogUtil.d(TAG, "启动录音");
        if (isWorking) {
            if (listener != null) listener.onSuccess();
            return;
        }

        isWorking = true;
        paused = false;

        AiSpeechLogUtil.e(TAG, "HeadsetRecorder start,型号：" + Build.MODEL.toLowerCase());
        AudioManager audioManager = (AudioManager) context.getSystemService(Context.AUDIO_SERVICE);
        if (DeviceModeList.contains(Build.MODEL.toLowerCase())) {
            audioManager.setMode(AudioManager.MODE_IN_COMMUNICATION);
        }
        // 蓝牙录音的关键，启动SCO连接，耳机话筒才起作用
        HeadsetSco.shareInstance().startBluetoothSco(this.context, new HeadsetSco.Listener() {
            @Override
            public void onError(String error) {
                AiSpeechLogUtil.e(TAG, "sco启动失败");
                if (listener != null) {
                    listener.onError("sco启动失败");
                }
                if (needRecordAnyway()){
                    HeadsetSco.shareInstance().forceSetScoOn(context);
                    startRecord();
                }
            }

            @Override
            public void onSuccess() {
                AiSpeechLogUtil.e(TAG, "sco打开成功，开始录音");
                if (listener != null) {
                    listener.onSuccess();
                }
                startRecord();
            }
        });
    }

    @Override
    public void stop() {
        super.stop();

        AiSpeechLogUtil.e(TAG, "stop");
        try {
            if (stopSema != null) stopSema.acquire();
            stopSema = null;
        } catch (InterruptedException e) {
            e.printStackTrace();
        }

        HeadsetSco.shareInstance().setHeadsetRecording(false);

        removeAllChannel();
        HeadsetSco.shareInstance().stopBluetoothSco(this.context);
        if (DeviceModeList.contains(Build.MODEL.toLowerCase())) {
            AudioManager audioManager = (AudioManager) context.getSystemService(Context.AUDIO_SERVICE);
            audioManager.setMode(AudioManager.MODE_NORMAL);
        }
    }

    @Override
    public void stop2() {
        super.stop2();

        AiSpeechLogUtil.e(TAG, "stop");
        try {
            if (stopSema != null) stopSema.acquire();
            stopSema = null;
        } catch (InterruptedException e) {
            e.printStackTrace();
        }

        HeadsetSco.shareInstance().setHeadsetRecording(false);

//        removeAllChannel();
        HeadsetSco.shareInstance().stopBluetoothSco(this.context);
        if (DeviceModeList.contains(Build.MODEL.toLowerCase())) {
            AudioManager audioManager = (AudioManager) context.getSystemService(Context.AUDIO_SERVICE);
            audioManager.setMode(AudioManager.MODE_NORMAL);
        }
    }

    private void startRecord() {

        if (mAudioRecord != null) {
            mAudioRecord.release();
            mAudioRecord = null;
        }

        Thread recordThread = new Thread(new Runnable() {
            @Override
            public void run() {

                int bufferSize = AudioRecord.getMinBufferSize(defaultSampleRateInHz, AudioFormat.CHANNEL_IN_MONO, AudioFormat.ENCODING_PCM_16BIT);
                int frames = bufferSize / ISpeechConstant.DefaultBytesPerPacketInMono + 1;
                bufferSize = frames * ISpeechConstant.DefaultBytesPerPacketInMono;
                AiSpeechLogUtil.e(TAG, "bufferSize: " + bufferSize + "  frames:" + frames);

                byte[] srcBuffer = new byte[bufferSize];
                byte[] buffer = new byte[ISpeechConstant.DefaultBytesPerPacketInMono];

                try {
                    mAudioRecord = new AudioRecord(defaultAudioSource, defaultSampleRateInHz, defaultChannelConfig, defaultAudioFormat, bufferSize);
                } catch (IllegalArgumentException exception) {
                    AiSpeechLogUtil.e(TAG, " new AudioRecord 失败: " + exception.getMessage());
                }

                HeadsetSco.shareInstance().setPreferredDevice(context, mAudioRecord);

                if (ContextCompat.checkSelfPermission(context, Manifest.permission.RECORD_AUDIO) ==
                        PackageManager.PERMISSION_GRANTED &&
                        mAudioRecord != null &&
                        mAudioRecord.getState() == AudioRecord.STATE_INITIALIZED)
                    // audioRecord可能为空，所以要加一个非空判断
                    mAudioRecord.startRecording();
                else
                    AiSpeechLogUtil.e(TAG, " mAudioRecord 开启录音失败: " + "mAudioRecord为空");

                HeadsetSco.shareInstance().setHeadsetRecording(true);

                stopSema = new Semaphore(0);
                while (isWorking) {
                    int bufferReadResult = mAudioRecord.read(srcBuffer, 0, bufferSize);
                    if (bufferReadResult != bufferSize) {
                        AiSpeechLogUtil.e(TAG, "HeadSet 录音失败!");
                        break;
                    }

                    if (!isWorking) break; // 尽量快速退出
                    if (HeadsetSco.shareInstance().invaild) {
                        dropSampleCount = 5; // 有些手机(三星) sco 开启 M2 会有 50ms 杂音导致 vad 唤醒, 所以设置重新过滤掉前 50ms, 这里丢掉 5 次(每次 50ms)大概 250ms
                        AudioDeviceInfo routeInfo = mAudioRecord.getRoutedDevice();
                        AudioDeviceInfo prefInfo = mAudioRecord.getPreferredDevice();
                        AiSpeechLogUtil.e(TAG, "HeadsetSco 已关闭 ========" + (routeInfo != null ? routeInfo.getProductName() : "null") + " 首选设备: " + (prefInfo != null ? prefInfo.getProductName() : "null"));
//                        continue; // 直接 continue 也可以, 但怕影响原先流程 (paused处 continue)
                    }

                    if (paused) {
                        writed_count = writed_count + frames;
                        if (writed_count > 0 && writed_count % 300 == 0) AiSpeechLogUtil.e(TAG, "run: 暂停中 ");
                        continue;
                    }

                    if (dropSampleCount > 0) {
                        dropSampleCount--;
                        AiSpeechLogUtil.e(TAG, "startRecord thread: 丢弃 " + bufferReadResult);
                        continue;
                    }

                    for (int k = 0; k < frames; k++) {
                        writed_count++;
                        if (!isWorking) break; // 尽量快速退出
                        System.arraycopy(srcBuffer, k * ISpeechConstant.DefaultBytesPerPacketInMono, buffer, 0, ISpeechConstant.DefaultBytesPerPacketInMono);

                        synchronized (audioChannel) {
                            for (String key : audioChannel) {
                                // writeAudioToChannel是BaseRecorder里面的函数，现在的做法是，在录音开始的时候，就已经往Channel里面写数据了，直到录音结束
                                writeAudioToChannel(key, buffer);
                            }
                        }
                    }
                }

                if (mAudioRecord != null)
                    mAudioRecord.release();
                mAudioRecord = null;

                HeadsetSco.shareInstance().setHeadsetRecording(false);
                AiSpeechLogUtil.e(TAG, name + " 录音已停止");

                stopSema.release();
            }
        });
        recordThread.setName("Tmk-" + TAG);
        recordThread.start();
    }

    /**
     * 不管sco是否开启成功，都开始启动录音
     * 这样可以避免无法拾音的情况
     * ex：ChromeBook设备等
     *
     * @return
     */
    public boolean needRecordAnyway() {
        if (isChromeBook()) {
            return true;
        }
        return false;
    }


    public boolean isChromeBook() {
        try {
            String btName = Settings.Secure.getString(context.getContentResolver(), "bluetooth_name");
            if (btName.toLowerCase(Locale.ROOT).contains("chromebook") == true) {
                return true;
            } else {
                return false;
            }
        } catch (Exception e) {
            return false;
        }
    }

}
