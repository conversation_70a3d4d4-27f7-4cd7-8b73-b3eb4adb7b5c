package co.timekettle.speech.recorder;

import android.content.Context;

import com.damly.ispeech.Mic2GccJni;

import co.timekettle.opus.OpusCodec;
import co.timekettle.speech.AudioRecordOptions;
import co.timekettle.speech.ISpeechConstant;
import co.timekettle.speech.ServiceQualityResult;
import co.timekettle.speech.utils.BytesTrans;
import co.timekettle.speech.utils.AiSpeechLogUtil;
import co.timekettle.speech.utils.RingBuffer;
import org.concentus.OpusException;
import org.greenrobot.eventbus.EventBus;
import org.greenrobot.eventbus.Subscribe;
import org.greenrobot.eventbus.ThreadMode;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

public class BleAudioRecorder extends AudioRecorderBase {
    private static final String TAG = "BleAudioRecorder";

    private static final int bytesIn20msBleRecord = 640; //20ms 单通道数据
    private HashMap<String, Decoder> decoderMap = new HashMap<>();
    private HashMap<String, RingBuffer> inputCache = new HashMap<>();

    private boolean needMakeup = true;
    private int nBytesPerPacket = 44; // ble 默认录音大小

    private final String BleRecordEventName = "BLEAudioRecordData";

    public BleAudioRecorder(Context context) {
        super(context, ISpeechConstant.RECORDER.BLE.toString());

        EventBus.getDefault().register(this);
    }

    @Override
    public void setDropSampleCount() {
        dropSampleCount = 0;
    }

    @Override
    public void start(Map<String, Object> options) {
        super.start(options);

        if (isWorking) {
            return;
        }

        if (options.containsKey(AudioRecordOptions.USE_GCC)) {
            useGCC = (boolean) options.get(AudioRecordOptions.USE_GCC);
        } else {
            useGCC = false;
        }

        if (options.containsKey(AudioRecordOptions.BLE_NEED_MAKEUP)) {
            this.needMakeup = (boolean) options.get(AudioRecordOptions.BLE_NEED_MAKEUP);
        } else {
            this.needMakeup = false;
        }

        if (useGCC) {
            Mic2GccJni.getInstance().Init();
        }

        for (String key : audioChannel) {
            inputCache.put(key, new RingBuffer(bytesIn20msBleRecord * 2));
        }

        decoderMap = new HashMap<>();

        synchronized (audioChannel) {
            for (String key : audioChannel) {
                Decoder decoder = null;
                try {
                    decoder = new Decoder(defaultSampleRateInHz, key);
                } catch (OpusException e) {
                    e.printStackTrace();
                }
                decoder.preindex = -1;
                decoderMap.put(key, decoder);
            }
            AiSpeechLogUtil.d(TAG, "start: " + decoderMap.keySet().toString());
        }

        isWorking = true;
        paused = false;
    }

    @Override
    public void stop() {
        super.stop();

        AiSpeechLogUtil.d(TAG, name + " 录音已停止");

        removeAllChannel();

        for (Decoder decoder : decoderMap.values()) {
            decoder.release();
        }

        for (Map.Entry<String, Decoder> entry : decoderMap.entrySet()) {
            Decoder decoder = entry.getValue();
            long totalLoss = 0;
            for (Long loss : decoder.losses) {
                totalLoss = totalLoss + loss;
            }
            float velocity = decoder.recvcount * this.nBytesPerPacket / ((decoder.destroyTime - decoder.createTime) / 1000.0f) / 1000; // KB/s
            ServiceQualityResult sqr = new ServiceQualityResult.Builder()
                    .chkey(entry.getKey())
                    .velocity((long)((velocity * 100) / 100.0f))
                    .totalLoss(totalLoss)
                    .recv(decoder.recvcount)
                    .total(decoder.totalcount)
                    .createTime(decoder.createTime)
                    .destroyTime(decoder.destroyTime)
                    .isFinal(true)
                    .build();
            reportService(sqr);
        }

        decoderMap = new HashMap<>();

        if (this.useGCC) Mic2GccJni.getInstance().Free();
    }

    @Override
    public void stop2() {
        super.stop2();

        AiSpeechLogUtil.d(TAG, name + " 录音已停止");

        for (Decoder decoder : decoderMap.values()) {
            decoder.release();
        }

        for (Map.Entry<String, Decoder> entry : decoderMap.entrySet()) {
            Decoder decoder = entry.getValue();
            long totalLoss = 0;
            for (Long loss : decoder.losses) {
                totalLoss = totalLoss + loss;
            }
            float velocity = decoder.recvcount * this.nBytesPerPacket / ((decoder.destroyTime - decoder.createTime) / 1000.0f) / 1000; // KB/s
            ServiceQualityResult sqr = new ServiceQualityResult.Builder()
                    .chkey(entry.getKey())
                    .velocity((long)((velocity * 100) / 100.0f))
                    .totalLoss(totalLoss)
                    .recv(decoder.recvcount)
                    .total(decoder.totalcount)
                    .createTime(decoder.createTime)
                    .destroyTime(decoder.destroyTime)
                    .isFinal(true)
                    .build();
            reportService(sqr);
        }

        decoderMap = new HashMap<>();

        if (this.useGCC) Mic2GccJni.getInstance().Free();
    }

    @Subscribe(threadMode = ThreadMode.POSTING)
    public void onHandleEvent(Map<String, Object> e) {
        if (!e.containsKey(BleRecordEventName)) return;
        Object[] args = (Object[])e.get(BleRecordEventName);
        String peripId = (String) args[0];
        byte[] values = (byte[]) args[1];
        Map otherInfo = (Map) args[2]; // {rssi=-32, electric=97, name=28A3, id=E8:0D:49:35:28:A3, firmwareVersion=00.04.37}
        onNotification(peripId, values, otherInfo);
    }

    public void onNotification(String key, byte[] buffer, Map otherInfo) {
        // 更新录音数据包大小velocity
        if (buffer.length > 0 && this.nBytesPerPacket != buffer.length) {
            this.nBytesPerPacket = buffer.length;
        }

        if (!decoderMap.containsKey(key)) return;

        String peripRssi = (String) otherInfo.get("rssi");
        String peripElectric = (String) otherInfo.get("electric");
        String peripName = (String) otherInfo.get("name");
        String firmwareVersion = (String) otherInfo.get("firmwareVersion");
        Boolean needResetIndex = (Boolean) otherInfo.get("needResetIndex");

        int index = (((buffer[0] & 0xff) << 8) | (buffer[1] & 0xff));
        int total = (((buffer[2] & 0xff) << 8) | (buffer[3] & 0xff));
        // 测试丢包后续流程 ---->
//            if (index == 1) {
//                buffer[0] = (byte) 0x00;
//                buffer[1] = (byte) 0xff;
//                this.onNotification(key, buffer);
//            }
//            if ((index > 200 && index <= 250) || (index > 500 && index <= 550)) return;
        // <---- 测试丢包后续流程

        long nMakeup = 0;
        Decoder decoder = decoderMap.get(key);
        if (needResetIndex != null && needResetIndex) {
            AiSpeechLogUtil.d(TAG, key.substring(12) + ": 重置录音数据下标为 -1, 原始下标: " + decoder.preindex);
            decoder.reset();
        }
//            if(((decoder.preindex + 1) % 65536) == index) {
        if (index - decoder.preindex == 1 || (decoder.preindex == 65535 && index == 0)) {
//                AiSpeechLogUtil.d(TAG, "打印 ble 正常包序列 " + key.substring(12) + ": " + decoder.preindex + " -> " + index + "/" + total);
        }
        else {
            AiSpeechLogUtil.e(TAG, "打印 ble 异常包序列 " + key.substring(12) + ": " + decoder.preindex + " -> " + index + "/" + total);
            if (decoder.preindex != -1) {
                long diff = index - decoder.preindex;
                if (diff > 0) {
                    nMakeup = diff - 1;
                } else {
                    nMakeup = 65535 + diff + 1;
                }
            }
        }
        if (decoder.preindex == -1 && index != 1) { // 开始录音后第一包包序列错误的情况, 从非 1 开始(由于刚开始 ble 录音下标是从 1 开始而不是 0 异常情况经常是大于 0)
//                decoder.preindex = index;
        } else {
            decoder.preindex = index;
        }

        // 测试 ---->
        // 测试使用, 检测录音后的处理流程
//            short[] shortPacket = new short[ISpeechConstant.DefaultSamplesPerPacketInMono];
//            Arrays.fill(shortPacket, (short) index);
//            decodedBuffer = BytesTrans.getInstance().Shorts2Bytes(shortPacket);
        // <---- 测试

        decoder.totalcount++;
        decoder.recvcount++;
        // 修补丢包
        if (this.needMakeup) {
            if (nMakeup > 0) {
//                    byte[] makeupSound = new byte[(int) (bytesIn20msBleRecord * nMakeup)];
//                    for (int i = 0; i < makeupSound.length; i++) {
//                        if (i % 2 == 0) makeupSound[i] = 0x2; // 丢包标识
//                        else makeupSound[i] = 0x0;
//                    }
//                    writeFileHandleToChannel(key, makeupSound);
//                    AiSpeechLogUtil.d(TAG, key.substring(12) + " ble 异常包修补包数(包/20ms): " + nMakeup);
//
//                    synchronized (this) {
//                        RingBuffer channelCache = inputCache.get(key);
//                        assert channelCache != null;
//                        channelCache.write(makeupSound);
//                    }

                for (int i = 0; i < nMakeup; i++) {
                    if (++decoder.totalcount % 300 == 0) AiSpeechLogUtil.d(TAG, key.substring(12) + " ble write: " + decoder.totalcount * 20 + "ms, thread id:" + Thread.currentThread().getId() + " isMute:" + this.isMute);
                }

                // ble 丢包统计
                decoder.lastPeriodCount = 0;

                long ts = System.currentTimeMillis();
                float velocity = decoder.lastPeriodCount * this.nBytesPerPacket / ((ts - decoder.lastTime) / 1000.0f) / 1000; // KB/s
//                AiSpeechLogUtil.d(TAG, "onNotification: velocity: " + velocity + " lastPeriodCount:" + decoder.lastPeriodCount + " nMakeup:" + nMakeup);  // KB/s
                decoder.losses.add(nMakeup);
                long totalLoss = 0;
                for (Long loss : decoder.losses) {
                    totalLoss = totalLoss + loss;
                }
                ServiceQualityResult sqr = new ServiceQualityResult.Builder()
                        .chkey(key)
                        .name(peripName)
                        .firmwareVersion(firmwareVersion)
                        .rssi(peripRssi)
                        .electric(peripElectric)
                        .velocity((long)((velocity * 100) / 100.0f))
                        .loss(nMakeup)
                        .totalLoss(totalLoss)
                        .recv(decoder.recvcount)
                        .total(decoder.totalcount)
                        .build();
                reportService(sqr);

                decoder.lastTime = ts;
                // ble 丢包统计

            } else {
                // ble 丢包统计
                decoder.lastPeriodCount++;
                if (decoder.totalcount % 100 == 0) {
                    long ts = System.currentTimeMillis();
                    float velocity = decoder.lastPeriodCount * this.nBytesPerPacket / ((ts - decoder.lastTime) / 1000.0f) / 1000; // KB/s
//                    AiSpeechLogUtil.d(TAG, "onNotification: velocity: " + velocity + " lastPeriodCount:" + decoder.lastPeriodCount + " nMakeup:" + nMakeup);  // KB/s
                    long totalLoss = 0;
                    for (Long loss : decoder.losses) {
                        totalLoss = totalLoss + loss;
                    }
                    ServiceQualityResult sqr = new ServiceQualityResult.Builder()
                            .chkey(key)
                            .name(peripName)
                            .firmwareVersion(firmwareVersion)
                            .rssi(peripRssi)
                            .electric(peripElectric)
                            .velocity((long)((velocity * 100) / 100.0f))
                            .loss(nMakeup)
                            .totalLoss(totalLoss)
                            .recv(decoder.recvcount)
                            .total(decoder.totalcount)
                            .build();
                    reportService(sqr);
                    decoder.lastPeriodCount = 0;
                    decoder.lastTime = ts;
                }
                // ble 丢包统计
            }
        }
        if (decoder.totalcount % 300 == 0) AiSpeechLogUtil.d(TAG, key.substring(12) + " ble write: " + decoder.totalcount * 20 + "ms, thread id:" + Thread.currentThread().getId() + " isMute:" + this.isMute);

        if (!isWorking || paused) {
            return;
        }
        //            String bytesHex = BleManager.bytesToHex(buffer);
//            AiSpeechLogUtil.d(TAG, "RecordData: " + key.substring(12) + " " + bytesHex.substring(0, 8) + " " + bytesHex);
        int headerLen = 4;
        byte[] encodedBuffer = new byte[buffer.length - headerLen];
        System.arraycopy(buffer, headerLen, encodedBuffer, 0, encodedBuffer.length);
        byte[] decodedBuffer = new byte[bytesIn20msBleRecord];
        try {
            int sampleSize = decoder.decode(encodedBuffer, decodedBuffer, bytesIn20msBleRecord / 2); // decode 40 -> 640
//            AiSpeechLogUtil.d(TAG, "RecordData: " + key.substring(12) + " 解码的sample长度:" + sampleSize + " buffer长度:" + buffer.length);
        } catch (OpusException e) {
            e.printStackTrace();
        }

        // 补充丢失的数据
        if (this.needMakeup && nMakeup > 0) {
            byte[] makeupSound = new byte[(int) (bytesIn20msBleRecord * nMakeup)];
            for (int i = 0; i < makeupSound.length; i++) {
                if (i % 2 == 0) makeupSound[i] = 0x2; // 丢包标识
                else makeupSound[i] = 0x0;
            }
            writeFileHandleToChannel(key, makeupSound);
            AiSpeechLogUtil.d(TAG, key.substring(12) + " ble 异常包修补包数(包/20ms): " + nMakeup);

            synchronized (this) {
                RingBuffer channelCache = inputCache.get(key);
                assert channelCache != null;
                channelCache.write(makeupSound);
            }
        }

        // 写入通道 和 Handle
        writeFileHandleToChannel(key, decodedBuffer);
        if (useGCC && audioChannel.size() == 2) {
            // 多通道关联处理, 多线程需同步
            synchronized (this) {
                String ch0Name = audioChannel.get(0);
                RingBuffer inputCacheCh0 = inputCache.get(ch0Name);
                String ch1Name = audioChannel.get(1);
                RingBuffer inputCacheCh1 = inputCache.get(ch1Name);
                if (needResetIndex != null && needResetIndex) {
                    inputCacheCh0.clear();
                    inputCacheCh1.clear();
                }

                RingBuffer channelCache = inputCache.get(key);
                channelCache.write(decodedBuffer);

//                    if (channelCache.readable() >= ISpeechConstant.DefaultBytesPerPacketInMono) {
//                        byte[] bytePacket0 = new byte[ISpeechConstant.DefaultBytesPerPacketInMono];
//                        channelCache.read(bytePacket0);
//
//                        int gccindex = ((bytePacket0[1] & 0xff) << 8) | (bytePacket0[0] & 0xff);
//                        if (gccindex - decoder.pregccindex == 1 || (decoder.pregccindex == 65535 && gccindex == 0)) {
////                                    AiSpeechLogUtil.d(TAG, "打印 gcc 正常包序列 " + key.substring(12) + ": " + decoder.pregccindex + " -> " + gccindex);
//                        } else
//                            AiSpeechLogUtil.e(TAG, "打印 gcc 异常包序列 " + key.substring(12) + ": " + decoder.pregccindex + " -> " + gccindex);
//                        decoder.pregccindex = gccindex;
//
//                        try {
//                            Thread.sleep(200);
//                        } catch (InterruptedException e) {
//                            e.printStackTrace();
//                        }
//                        return;
//                    }

                while (inputCacheCh0.readable() >= ISpeechConstant.DefaultBytesPerPacketInMono && inputCacheCh1.readable() >= ISpeechConstant.DefaultBytesPerPacketInMono) {
                    byte[] bytePacket0 = new byte[ISpeechConstant.DefaultBytesPerPacketInMono];
                    int ch0ReadSize = inputCacheCh0.read(bytePacket0);

                    byte[] bytePacket1 = new byte[ISpeechConstant.DefaultBytesPerPacketInMono];
                    int ch1ReadSize = inputCacheCh1.read(bytePacket1);

//                        {
//                            {
//                                    int gccindex = ((bytePacket0[1] & 0xff) << 8) | (bytePacket0[0] & 0xff);
//                                    Decoder de = decoderMap.get(ch0Name);
//                                    if (gccindex - de.pregccindex == 1 || (de.pregccindex == 65535 && gccindex == 0)) {
////                                    AiSpeechLogUtil.d(TAG, "打印 gcc 正常包序列 " + ch0Name.substring(12) + ": " + de.pregccindex + " -> " + gccindex);
//                                    } else
//                                        AiSpeechLogUtil.e(TAG, "打印 gcc 异常包序列 " + ch0Name.substring(12) + ": " + de.pregccindex + " -> " + gccindex);
//                                    de.pregccindex = gccindex;
//                            }
//
//                            {
//                                    int gccindex = ((bytePacket1[1] & 0xff) << 8) | (bytePacket1[0] & 0xff);
//                                    Decoder de = decoderMap.get(ch1Name);
//                                    if (gccindex - de.pregccindex == 1 || (de.pregccindex == 65535 && gccindex == 0)) {
////                                    AiSpeechLogUtil.d(TAG, "打印 gcc 正常包序列 " + ch1Name.substring(12) + ": " + de.pregccindex + " -> " + gccindex);
//                                    } else
//                                        AiSpeechLogUtil.e(TAG, "打印 gcc 异常包序列 " + ch1Name.substring(12) + ": " + de.pregccindex + " -> " + gccindex);
//                                    de.pregccindex = gccindex;
//                            }
//                        }

                    short[] vadFlag = new short[2]; // 值为 0 是非声音帧, 值为 1 / 2 是声音帧
                    float[] corrNF = new float[2];
                    this.processGCC(bytePacket0, bytePacket1, vadFlag, corrNF);
                    if(onlyProcessAsDual){ // 仅处理立体声声音帧，不送往通道
                        writeAudioToChannelDual(ch0Name, bytePacket0, ch1Name, bytePacket1);
                    }else {
                        writeAudioToChannel(ch0Name, bytePacket0, vadFlag[0] > 0);
                        writeAudioToChannel(ch1Name, bytePacket1, vadFlag[1] > 0);
                    }

                }
            }
        } else {
            RingBuffer channelCache = inputCache.get(key);
            channelCache.write(decodedBuffer);

            while (channelCache.readable() >= ISpeechConstant.DefaultBytesPerPacketInMono) {
                byte[] packet = new byte[ISpeechConstant.DefaultBytesPerPacketInMono];
                channelCache.read(packet);
                writeAudioToChannel(key, packet);
            }
        }
    }

    void processGCC(byte[] bytePacket0, byte[] bytePacket1, short[] vadFlag, float[] corrNF) {

        short[] shortPacket0 = BytesTrans.getInstance().Bytes2Shorts(bytePacket0);
        short[] shortPacket1 = BytesTrans.getInstance().Bytes2Shorts(bytePacket1);
        short[] input = new short[ISpeechConstant.DefaultSamplesPerPacketInMono * 2]; // 输入双通道数据
        short[] output = new short[ISpeechConstant.DefaultSamplesPerPacketInMono * 2]; // 输出双通道数据

        for (int i = 0; i < input.length; i++) {
            input[i] = i % 2 == 0 ? shortPacket0[i / 2] : shortPacket1[i / 2];
        }
        Mic2GccJni.getInstance().Deal(output, vadFlag, corrNF, input);

        for (int i = 0; i < output.length; i++) {
            if (i % 2 == 0) {
                shortPacket0[i / 2] = output[i];
            } else{
                shortPacket1[i / 2] = output[i];
            }
        }

//        // 设置增益
//        int gain = 2;
//        for (int i = 0; i < shortPacket0.length; i++) {
//            int value = shortPacket0[i] * gain;
//            if (value > 0x7fff) {
//                value = 0x7fff;
//            } else if (value < -0x8000) {
//                value = -0x8000;
//            }
//            shortPacket0[i] = (short) value;
//        }
//        for (int i = 0; i < shortPacket1.length; i++) {
//            int value = shortPacket1[i] * gain;
//            if (value > 0x7fff) {
//                value = 0x7fff;
//            } else if (value < -0x8000) {
//                value = -0x8000;
//            }
//            shortPacket1[i] = (short) value;
//        }

        System.arraycopy(BytesTrans.getInstance().Shorts2Bytes(shortPacket0), 0, bytePacket0, 0, bytePacket0.length);
        System.arraycopy(BytesTrans.getInstance().Shorts2Bytes(shortPacket1), 0, bytePacket1, 0, bytePacket1.length);
    }

    public void setOptions(final HashMap<String, Object> options) {
        if (options.containsKey("threshold")) {
            Mic2GccJni.getInstance().fVadLkTH = (float) options.get("threshold");

            if (useGCC) {
                float jfHCorrTH = 0.7f;
                float jfLCorrTH = 0.5f;
                float jfVadOnTH = 0.2f;
                boolean ret = Mic2GccJni.getInstance().Set(jfHCorrTH, jfLCorrTH, jfVadOnTH, Mic2GccJni.getInstance().fVadLkTH);
                AiSpeechLogUtil.e(TAG, "Set2MicGcc: " + ret);
            }
        }
    }

    private class Decoder {
        OpusCodec engine;
        long preindex = 0;
        long totalcount = 0; // 需要收到的包数
        long recvcount = 0; // 实际收到的包数
        long lastTime = 0;
        long lastPeriodCount = 0; // 最后一个周期计数
        List<Long> losses = new ArrayList<>(10);
        long createTime = 0;
        long destroyTime = 0;
        long pregccindex = 0;
        String key = null;

        Decoder(int sampleRate, String key) throws OpusException {
            this.engine = new OpusCodec(sampleRate, 1);
            this.key = key;
            this.lastTime = System.currentTimeMillis();
            this.createTime = this.lastTime;
        }

        void reset() {
            this.preindex = -1;
            this.totalcount = 0;
            this.recvcount = 0;
            losses.clear();
        }

        public int decode(byte[] encodedBuffer, byte[] buffer, int frames) throws OpusException {
            // FIXME: 2020/9/17 decoder.decode() 返回原始数据采样数?
            int ret = this.engine.decode(encodedBuffer, buffer, frames); // decode 40 -> 640
            return ret;
        }

//        public int decode(byte[] encodedBuffer, short[] buffer, int frames) {
//            // FIXME: 2020/9/17 decoder.decode() 返回原始数据采样数?
//            int ret = this.engine.decode(encodedBuffer, buffer, frames); // decode 40 -> 640
//            return ret;
//        }

        public void release() {
            this.destroyTime = System.currentTimeMillis();
        }
    }
}
