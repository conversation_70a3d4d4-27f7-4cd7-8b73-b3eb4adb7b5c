package co.timekettle.speech.recorder;

import android.content.Context;
import android.media.AudioFormat;
import android.media.MediaRecorder;

import co.timekettle.speech.AgcProcessor;
import co.timekettle.speech.AudioChannelOptions;
import co.timekettle.speech.AudioRecordOptions;
import co.timekettle.speech.ISpeechConstant;
import co.timekettle.speech.RecordManager;
import co.timekettle.speech.TestRecorder;
import co.timekettle.speech.ServiceQualityResult;
import co.timekettle.speech.utils.BytesTrans;
import co.timekettle.speech.utils.AiSpeechLogUtil;

import java.util.Arrays;
import java.util.HashMap;
import java.util.Map;
import java.util.Vector;

public abstract class AudioRecorderBase {
    private static final String TAG = "BaseAudioRecorder";

    protected String name;
    protected Context context;
    // FIXME: 2023/6/8 后续此模块需要有通道解耦, 指输出单/多通道录音数据
    protected final Vector<String> audioChannel = new Vector<>();
    protected final HashMap<String, Integer> audioChannels = new HashMap<String, Integer>(); // 记录对应多声道音源中具体的通道 index
    protected int defaultSampleRateInHz = 16000;
    protected int defaultAudioSource = MediaRecorder.AudioSource.MIC;
    protected int defaultChannelConfig = AudioFormat.CHANNEL_IN_MONO;
    protected int defaultAudioFormat = AudioFormat.ENCODING_PCM_16BIT;
//    protected int defaultMonoFrameLen = 512; // 单通道 16000采样率 16位 16ms 数据

    protected int StereoChannel = AudioFormat.CHANNEL_IN_STEREO;

    protected int writed_count;

    protected boolean canRecordWhenSpeaking = false;
    protected boolean isWorking = false;
    protected boolean paused = false;
    protected boolean isMute = false; // 设置静音使 vad 停止以及促使录音数据驱动识别和翻译的完成
    protected boolean canRecordBackground = false; // 是否支持后台录音，如果不支持，则大部分手机在退出到后台1分钟左右，就不能录音了，回到前台的时候才能继续录音。
    protected boolean useGCC = false;
    protected int customAudioSource = -1; // 未定义为-1

    protected int dropSampleCount = 0;

    protected boolean writeToFile = false;
    protected boolean saveVadData = false; //只保存有效数据到本地（从vad开始到vad结束）
    protected Listener listener;

    protected final HashMap<String, TestRecorder> fileHandles = new HashMap();
    protected final HashMap<String, TestRecorder> testFileHandles = new HashMap();

    protected float gain = 1.0f; // 增益
    protected HashMap<String, AgcProcessor> agcs = new HashMap<>(); // 存储每个通道对应的 agc 处理器

    protected boolean onlyProcessAsDual = false;// 仅仅作为立体声处理，不发送到speech

    public AudioRecorderBase(Context context, String name) {
        this.context = context;
        this.name = name;
    }

    public boolean isWorking() {
        return isWorking;
    }

    public String getName() {
        return name;
    }

    public void setIsMute(boolean mute) {
        this.isMute = mute;
    }
    public void setPaused(boolean paused) {
        this.paused = paused;
    }

    public void setGain(float gain) {
        // 比较精确到小数点后一位的值是否相等
//        if ((int)(this.gain * 10 + 0.5) - (int)(gain * 10 + 0.5) == 0) {
//            AiSpeechLogUtil.d(TAG,  "录音器[" + this.name + "] gain 值不变不做更新: " + this.gain);
//            return;
//        }
        boolean useAgc = Math.abs(gain - 1.0) > 0.09;
        if (useAgc) {
            this.gain = gain;
            AiSpeechLogUtil.d(TAG, "设置 gain 值: " + gain);
            for (String chkey : audioChannel) {
                AgcProcessor agc = new AgcProcessor(ISpeechConstant.DefaultSampleRate, 256, gain);
                this.agcs.put(chkey, agc);
            }
        } else {
            for (String chkey : audioChannel) {
                this.agcs.put(chkey, null);
            }
        }
    }

    /**
     * 设置录音启动后丢弃若次录音数据
     */
    public void setDropSampleCount(int count) {
        this.dropSampleCount = count;
    }

    public abstract void setDropSampleCount();

    public void start(final Map<String, Object> options) {
        if (options != null) {
        if (options.containsKey(AudioRecordOptions.CAN_RECORD_WHEN_SPEAKING)) {
            canRecordWhenSpeaking = (boolean) options.get(AudioRecordOptions.CAN_RECORD_WHEN_SPEAKING);
        }
        if (options.containsKey(AudioRecordOptions.WRITE_TO_FILE)) {
            writeToFile = (boolean) options.get(AudioRecordOptions.WRITE_TO_FILE);
        }
        if (options.containsKey("saveVadData")) {
            saveVadData = (boolean) options.get("saveVadData");
        }

        if (options.containsKey("canRecordBackground")) {
            canRecordBackground = (boolean) options.get("canRecordBackground");
        }
        if (options.containsKey(AudioRecordOptions.MEDIA_RECORDER_SOURCE)) {
            customAudioSource = (int) options.get(AudioRecordOptions.MEDIA_RECORDER_SOURCE);
        }
        if (options.containsKey(AudioRecordOptions.ONLY_PROCESS_AS_DUAL)) {
            onlyProcessAsDual = (boolean) options.get(AudioRecordOptions.ONLY_PROCESS_AS_DUAL);
        } else {
            onlyProcessAsDual = false;
        }
        if (options.containsKey(AudioRecordOptions.AUDIO_GAIN)) {
            this.setGain((float) options.get(AudioRecordOptions.AUDIO_GAIN));
        } else {
            this.setGain(1.0f);
        }
        }

        AiSpeechLogUtil.d(TAG, "start: " + (options != null ? options.toString() : null) + " " + Arrays.toString(audioChannel.toArray()));
        synchronized (testFileHandles) {
            if (writeToFile) {
                AiSpeechLogUtil.d(TAG, "open: 创建文件: " + options.toString() + " " + Arrays.toString(audioChannel.toArray()));
                for (String chkey : audioChannel) {
                    TestRecorder handle = new TestRecorder(context, "TK_Record", null, chkey.replace(":", "_") + "-source", true); // 此处设置录音文件开关
                    testFileHandles.put(chkey, handle);
                }
            }
        }

        synchronized (fileHandles) {
            if (saveVadData) {
                for (String chkey : audioChannel) {
                    TestRecorder handle = new TestRecorder(context, "PracticeData", "record", "practice", true); // 此处设置录音文件开关
                    fileHandles.put(chkey, handle);
                }
            }
        }
    }

    public void setOptions(final HashMap<String, Object> options) {

    }

    public void stop() {
        isMute = false;
        isWorking = false;
        paused = false;

        synchronized (testFileHandles) {
            if (writeToFile) {
                for (TestRecorder handle : testFileHandles.values()) {
                    handle.close();
                }
                testFileHandles.clear();
            }
        }

        synchronized (fileHandles) {
            if (saveVadData) {
                for (TestRecorder handle : fileHandles.values()) {
                    handle.close();
                }
                fileHandles.clear();
            }
        }

        removeAllChannel();
        this.agcs.clear();
        // FIXME: 2023/6/10 清空 this.listener
        customAudioSource = -1;
    }

    public void stop2() {
        isMute = false;
        isWorking = false;
        paused = false;

        synchronized (testFileHandles) {
            if (writeToFile) {
                for (TestRecorder handle : testFileHandles.values()) {
                    handle.close();
                }
                testFileHandles.clear();
            }
        }

        synchronized (fileHandles) {
            if (saveVadData) {
                for (TestRecorder handle : fileHandles.values()) {
                    handle.close();
                }
                fileHandles.clear();
            }
        }
        customAudioSource = -1;
    }

    /**
     * 绑定录音通道
     * @param options 通道参数
     * @deprecated Call {@link #addChannel2(String)} )} instead.
     */
    @Deprecated
    public void addChannel(final Map<String, Object> options) {
        String chkey = null;
        if (options.containsKey(AudioChannelOptions.KEY)) {
            chkey = (String) options.get(AudioChannelOptions.KEY);
        }

        assert chkey != null;
        if (!chkey.isEmpty() && !audioChannel.contains(chkey)) {
            synchronized (testFileHandles) {
                if (writeToFile) {
                    TestRecorder handle = new TestRecorder(context, "TK_Record", null, chkey.replace(":", "_") + "-source", true); // 此处设置录音文件开关
                    testFileHandles.put(chkey, handle);
                }
            }

            synchronized (fileHandles) {
                if (saveVadData) {
                    TestRecorder handle = new TestRecorder(context, "PracticeData", "record", "practice", true); // 此处设置录音文件开关
                    fileHandles.put(chkey, handle);
                }
            }
            audioChannel.add(chkey); // 数组中存在如果两个相同的chkey, 即向相同通道写入了两边相同数据, 会导致 vad 判断一直是声音
        }
    }

    public void addChannel2(final String key) {
        if (!key.isEmpty() && !audioChannel.contains(key)) {
            synchronized (testFileHandles) {
                if (writeToFile) {
                    TestRecorder handle = new TestRecorder(context, "TK_Record", null, key.replace(":", "_") + "-source", true); // 此处设置录音文件开关
                    testFileHandles.put(key, handle);
                }
            }

            synchronized (fileHandles) {
                if (saveVadData) {
                    TestRecorder handle = new TestRecorder(context, "PracticeData", "record", "practice", true); // 此处设置录音文件开关
                    fileHandles.put(key, handle);
                }
            }
            audioChannel.add(key); // 数组中存在如果两个相同的chkey, 即向相同通道写入了两边相同数据, 会导致 vad 判断一直是声音
        }
    }

    public void removeAllChannel() {
        synchronized (audioChannel) {
            audioChannel.clear();
        }
        synchronized (audioChannels) {
            audioChannels.clear();
        }

        synchronized (testFileHandles) {
            for (TestRecorder handle : testFileHandles.values()) {
                handle.close();
            }
            testFileHandles.clear();
        }

        synchronized (fileHandles) {
            for (TestRecorder handle : fileHandles.values()) {
                handle.close();
            }
            fileHandles.clear();
        }
    }

    protected void writeAudioToChannel(String key, byte[] data) {
        this.writeAudioToChannel(key, BytesTrans.getInstance().Bytes2Shorts(data));
    }

    protected void writeAudioToChannel(String key, byte[] data, boolean isVoice) {
        this.writeAudioToChannel(key, BytesTrans.getInstance().Bytes2Shorts(data), isVoice);
    }

    protected void writeAudioToChannelDual(String chkey0, byte[] sound0, String chkey1, byte[] sound1) {
        short[] input0 = BytesTrans.getInstance().Bytes2Shorts(sound0);
        short[] input1 = BytesTrans.getInstance().Bytes2Shorts(sound1);
        short[] output0 = Arrays.copyOf(input0, input0.length);
        short[] output1 = Arrays.copyOf(input1, input1.length);
        AgcProcessor agc0 = this.agcs.get(chkey0);
        if (agc0 != null) {
            agc0.processAgc(output0, input0);
        }
        AgcProcessor agc1 = this.agcs.get(chkey1);
        if (agc1 != null) {
            agc1.processAgc(output1, input1);
        }
        if (this.listener != null)
            this.listener.onRecordingDual(chkey0, output0, chkey1, output1); // 这里直接callBack，不做处理
    }


    protected void writeAudioToChannel(String key, short[] data) {
        if (this.writed_count > 0 && this.writed_count % 300 == 0) AiSpeechLogUtil.d(TAG, key+ "[" + getName() + "] write: " + this.writed_count + " isMute:" + this.isMute);
        if (this.isMute) {
            Arrays.fill(data, (short) 1);
        }
        AgcProcessor agc = this.agcs.get(key);
        if (agc != null) {
            short[] input = Arrays.copyOf(data, data.length);
            agc.processAgc(data, input);
        }
        if (this.listener != null) this.listener.onRecording(key, data, true, null);
    }

    protected void writeAudioToChannel(String key, short[] data, boolean isVoice) {
        if (this.writed_count > 0 && this.writed_count % 300 == 0) AiSpeechLogUtil.d(TAG, key+ "[" + getName() + "] write: " + this.writed_count + " isMute:" + this.isMute);
        if (this.isMute) {
            isVoice = false;
            Arrays.fill(data, (short) 1);
        }
        AgcProcessor agc = this.agcs.get(key);
        if (agc != null) {
            short[] input = Arrays.copyOf(data, data.length);
            agc.processAgc(data, input);
        }
        if (this.listener != null) this.listener.onRecording(key, data, false, isVoice);
    }

    protected void writeFileHandleToChannel(String key, byte[] data) {
        synchronized (testFileHandles) {
            if (writeToFile) {
                TestRecorder handle = testFileHandles.get(key);
                if (handle != null) handle.write(data);
            }
        }
    }

    public void openSaveVadData(String key) {
        if (!this.saveVadData) return;
        synchronized (fileHandles) {
            TestRecorder handle = fileHandles.get(key);
            if (handle != null && !handle.isOpen()) {
                handle.open();
            } else {
                AiSpeechLogUtil.e(TAG, "file handle为空，找不到testRecorder");
            }
        }
    }

    public void saveVadData(String key, byte[] data) {
        if (this.isMute) {
            Arrays.fill(data, (byte) 0);
        }
        synchronized (fileHandles) {
            if (saveVadData) {
//                AiSpeechLogUtil.e(TAG, "保存VAD数据：" + Arrays.toString(data));
                TestRecorder handle = fileHandles.get(key);
                if (handle != null) handle.write(data);
            }
        }
    }

    public void stopSaveVadData() {
        // stop 的时候不清空，fileHandler一直存在
        if (!this.saveVadData) return;
        synchronized (fileHandles) {
            for (TestRecorder handle : fileHandles.values()) {
                handle.close();
            }
        }
    }

    public void doSpeakStart() {
        if (!canRecordWhenSpeaking) {
            AiSpeechLogUtil.e(TAG, "doSpeakStart: " + this.name + " 录音暂停开始 =======");
            paused = true;
        }
    }

    public void doSpeakEnd() {
        if (!canRecordWhenSpeaking) {
            AiSpeechLogUtil.e(TAG, "doSpeakEnd: " + this.name + " 录音暂停解除 =======");
            paused = false;
        }
    }

    protected void reportService(ServiceQualityResult sqr) {
        if (RecordManager.shareInstance().sqListener != null) {
            RecordManager.shareInstance().sqListener.onServiceQualityEvent(sqr);
        }
    }

    public interface Listener {
        void onSuccess(); //开启录音成功

        void onRecording(String key, short[] data, boolean needVad, Boolean isVoice); //录音数据

        default void onRecordingDual(String chkey0, short[] sound0, String chkey1, short[] sound1) {} //录音数据（双通道返回）

        void onError(String reason); // 开启录音失败
    }

    public void setListener(Listener listener){
        this.listener = listener;
    }
}

