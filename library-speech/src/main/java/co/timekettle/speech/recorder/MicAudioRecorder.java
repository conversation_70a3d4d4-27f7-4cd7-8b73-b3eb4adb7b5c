package co.timekettle.speech.recorder;

import android.Manifest;
import android.content.Context;
import android.content.pm.PackageManager;
import android.media.AudioRecord;
import android.media.MediaRecorder;

import androidx.core.content.ContextCompat;

import co.timekettle.speech.ISpeechConstant;
import co.timekettle.speech.utils.AiSpeechLogUtil;

import java.util.HashMap;
import java.util.Map;
import java.util.concurrent.Semaphore;

public class MicAudioRecorder extends AudioRecorderBase {

    private static final String TAG = "MicAudioRecorder";
    private Semaphore stopSema = null;

    public MicAudioRecorder(Context context) {
        super(context, ISpeechConstant.RECORDER.PHONE.toString());
    }

    @Override
    public void setDropSampleCount() {
        dropSampleCount = 5;
    }

    @Override
    public void start(final Map<String, Object> options) {
        super.start(options);

        if (isWorking) {
            return;
        }

        isWorking = true;
        paused = false;

        Thread recordThread = new Thread(new Runnable() {
            @Override
            public void run() {
                int bufferSize = AudioRecord.getMinBufferSize(defaultSampleRateInHz, defaultChannelConfig, defaultAudioFormat);
                int nFrame = bufferSize / ISpeechConstant.DefaultBytesPerPacketInMono + 1;
                bufferSize = nFrame * ISpeechConstant.DefaultBytesPerPacketInMono;
                byte[] srcBuffer = new byte[bufferSize];
                byte[] frame = new byte[ISpeechConstant.DefaultBytesPerPacketInMono];

                int audioSource = defaultAudioSource;
                if (customAudioSource > 0) audioSource = customAudioSource;
                AudioRecord audioRecord = new AudioRecord(audioSource, defaultSampleRateInHz, defaultChannelConfig, defaultAudioFormat, bufferSize);
                if (ContextCompat.checkSelfPermission(context, Manifest.permission.RECORD_AUDIO) !=
                        PackageManager.PERMISSION_GRANTED ||
                        audioRecord == null ||
                        audioRecord.getState() != AudioRecord.STATE_INITIALIZED) {
                    AiSpeechLogUtil.e(TAG, " new AudioRecord 初始化失败");
                    return;
                }

                audioRecord.startRecording(); // 若 mic 被占用会出现报错: AudioRecord: start() status -38, mic被占用是因为 headset录音还未停止

                stopSema = new Semaphore(0);
                AiSpeechLogUtil.d(TAG, name + " 开始录音，audioSource："+audioSource);
                while (isWorking) {
                    int bufferReadResult = audioRecord.read(srcBuffer, 0, bufferSize);
                    if (bufferReadResult != bufferSize) {
                        break;
                    }

                    if (paused) {
                        writed_count = writed_count + nFrame;
                        if (writed_count > 0 && writed_count % 300 == 0) AiSpeechLogUtil.e(TAG, "run: 暂停中 ");
                        continue;
                    }

                    if (dropSampleCount > 0) {
                        dropSampleCount--;
                        continue;
                    }

                    for (int k = 0; k < nFrame; k++) {
                        writed_count++;
                        System.arraycopy(srcBuffer, k * ISpeechConstant.DefaultBytesPerPacketInMono, frame, 0, ISpeechConstant.DefaultBytesPerPacketInMono);

                        synchronized (audioChannel) {
                            for (String key : audioChannel) {
                                writeFileHandleToChannel(key, frame);
                                writeAudioToChannel(key, frame);
                            }
                        }
                    }
                }

                audioRecord.release();
                AiSpeechLogUtil.e(TAG, name + " 录音已停止");

                if (stopSema != null) stopSema.release();
            }
        });
        recordThread.setName("Tmk-" + TAG);
        recordThread.start();
    }

    @Override
    public void stop() {
        super.stop();
        AiSpeechLogUtil.e(TAG, "stop");
        try {
            if (stopSema != null) stopSema.acquire();
            stopSema = null;
        } catch (InterruptedException e) {
            e.printStackTrace();
        }
        removeAllChannel();
    }

    @Override
    public void stop2() {
        super.stop2();
        AiSpeechLogUtil.e(TAG, "stop");
        try {
            if (stopSema != null) stopSema.acquire();
            stopSema = null;
        } catch (InterruptedException e) {
            e.printStackTrace();
        }
//        removeAllChannel();
    }
}
