package co.timekettle.speech.recorder;

import android.bluetooth.BluetoothAdapter;
import android.content.BroadcastReceiver;
import android.content.Context;
import android.content.Intent;
import android.content.IntentFilter;
import android.media.AudioDeviceInfo;
import android.media.AudioManager;
import android.media.AudioRecord;
import android.media.session.MediaSession;
import android.media.session.PlaybackState;
import android.os.Build;
import android.os.SystemClock;

import androidx.annotation.NonNull;

import static android.media.AudioDeviceInfo.TYPE_BLUETOOTH_SCO;

import java.util.ArrayList;
import java.util.Arrays;

import co.timekettle.speech.ISpeechConstant;
import co.timekettle.speech.utils.AiSpeechLogUtil;

/*
 * 注意:
 * 1. isBluetoothScoOn() 状态在一些手机不太准确, 有可能 sco 关了仍然是 true;
 * 2. setBluetoothScoOn(true) 会导致 isSpeakerphoneOn() 为 false, setSpeakerphoneOn(true) 会导致 isBluetoothScoOn() 为 false, 彼此互斥.
 * 3. 关于 setMode(), 调节系统通话/音乐播放音量, 需要设置对应的 mode, AudioManager.MODE_IN_COMMUNICATION 会调节系统通话音量
 * 4. AudioManager.MODE_IN_COMMUNICATION 下开启 sco, 有些手机并不能通过 hfp 模式录音
 * 5. setPreferredDevice() 会导致 hfp 下录不了音, 不设置的话, 华为mate 30pro/40pro手机外放模式路由切换又很慢, 此工程测试, 荣耀和华为 P30pro 都很慢
 * 6. AudioManager.STREAM_VOICE_CALL & setSpeakerphoneOn(true) 下外放声音, isBluetoothScoOn()会为 false 并 sco 会关闭, 此状况下播放完成 setSpeakerphoneOn(false), isBluetoothScoOn()会恢复为 true, 但 sco 仍然是关闭的
 * 7. 华为mate 30pro/40pro 需要设置首选项和录音过程不切换 sco, 荣耀和华为 P30pro 不管怎么样, 只要设置外放, 都切换很慢
 * */
public class HeadsetSco {
    private final static String TAG = "HeadSetSco";
    private static HeadsetSco instance = null;

    private static final int SCO_CONNECT_TIME = 5;
    private MediaSession mSession = null;
    private int audioManagerMode;//保存未申请焦点之前，audiomanager的状态，在释放焦点之后用来设置回初始状态。
    private BroadcastReceiver scoBroadcastReceiver = null;

    public boolean invaild = true;
    private boolean headsetRecording = true;

    private Listener listener = null;

    private static AudioManager.OnAudioFocusChangeListener onAudioFocusChangeListener = new AudioManager.OnAudioFocusChangeListener() {
        @Override
        public void onAudioFocusChange(int focusChange) {
            switch (focusChange) {
                case AudioManager.AUDIOFOCUS_GAIN:
                    AiSpeechLogUtil.d(TAG, "onAudioFocusChange: AUDIOFOCUS_GAIN");
                    break;
                case AudioManager.AUDIOFOCUS_LOSS:
                    AiSpeechLogUtil.d(TAG, "onAudioFocusChange: AUDIOFOCUS_LOSS");
                    break;
                case AudioManager.AUDIOFOCUS_LOSS_TRANSIENT:
                    AiSpeechLogUtil.d(TAG, "onAudioFocusChange: AUDIOFOCUS_LOSS_TRANSIENT");
                    break;
                case AudioManager.AUDIOFOCUS_LOSS_TRANSIENT_CAN_DUCK:
                    AiSpeechLogUtil.d(TAG, "onAudioFocusChange: AUDIOFOCUS_LOSS_TRANSIENT_CAN_DUCK");
                    break;
            }
        }
    };

    public static HeadsetSco shareInstance() {
        if (instance == null) {
            AiSpeechLogUtil.d(TAG, "sco单例对象为空，新建一个sco");
            instance = new HeadsetSco();
        }
        return instance;
    }

    public void setHeadsetRecording(boolean headsetRecording) {
        this.headsetRecording = headsetRecording;
    }

    /*
     * 在录音过程中重启 sco
     * */
    @NonNull
    public void restartBluetoothSco(final Context context, final Listener listener) {
        if (!headsetRecording) {
            AiSpeechLogUtil.d(TAG, "restartBluetoothSco: 录音器已停止录音, 不重新开启 sco");
            return;
        }
        String DeviceBrand = Build.BRAND.toLowerCase();
        String model = Build.MODEL.toLowerCase();
        ArrayList<String> modelList = new ArrayList(Arrays.asList("tet-an00", "tet-an10", "tet-an50", "els-an00")); // 在兼容性列表内的设备，需要重启
        // 华为有一些机型外放后需要重启，例如MateX2 和P40 Pro
        // 有一些机型不需要重启，例如P20 Lite，当然大部分机型应该是不需要重启的（推测）

        if (DeviceBrand.contains("huawei") && !modelList.contains(model)) {  // 不在兼容性列表内的设备，直接return
            return;
        }

        AiSpeechLogUtil.d(TAG, "restartBluetoothSco: 录音过程中重启 sco");

        this.startBluetoothSco(context, listener);
    }

    /*
     * 在录音过程中重启 sco
     * */
    @NonNull
    public void pauseBluetoothSco(final Context context) {
        String DeviceBrand = Build.BRAND.toLowerCase();
        if (DeviceBrand.contains("huawei")) {
            return;
        }

        AiSpeechLogUtil.d(TAG, "pauseBluetoothSco: 录音过程中停止 sco");
        this.stopBluetoothSco(context);
    }


    // FIXME: 2020/5/27 就是在这里，开启了SCO导致没有办法播放
    @NonNull
    public void startBluetoothSco(final Context context, final Listener listener) {
        final AudioManager audioManager = (AudioManager) context.getSystemService(Context.AUDIO_SERVICE);
        audioManagerMode = audioManager.getMode();

        this.listener = listener;

        if (!audioManager.isBluetoothScoAvailableOffCall()) {
            AiSpeechLogUtil.e(TAG, "系统不支持蓝牙录音");
            if (this.listener != null)
                this.listener.onError("Your device no support bluetooth record!");
            return;
        }
        AiSpeechLogUtil.d(TAG, "startBluetoothSco: " + "mode: " + audioManager.getMode() + " isSpeakerphoneOn: " + audioManager.isSpeakerphoneOn() + " isBluetoothScoOn: " + audioManager.isBluetoothScoOn() + " isBluetoothA2dpOn:" + audioManager.isBluetoothA2dpOn());

        AiSpeechLogUtil.d(TAG, "SCO 即将开启!");
        audioManager.startBluetoothSco(); // Start bluetooth SCO audio connection.

        this.startReceiveScoBroadcast(context);
    }

    private void startReceiveScoBroadcast(final Context context) {
        final AudioManager audioManager = (AudioManager) context.getSystemService(Context.AUDIO_SERVICE);

        if (scoBroadcastReceiver == null) {
            scoBroadcastReceiver = new BroadcastReceiver() {
                @Override
                public void onReceive(final Context context, Intent intent) {
                    int state = intent.getIntExtra(AudioManager.EXTRA_SCO_AUDIO_STATE, -1);
                    AiSpeechLogUtil.d(TAG, "onReceive: " + "mode: " + audioManager.getMode() + " isSpeakerphoneOn: " + audioManager.isSpeakerphoneOn() + " isBluetoothScoOn: " + audioManager.isBluetoothScoOn() + " isBluetoothA2dpOn: " + audioManager.isBluetoothA2dpOn() + " sco state: " + state);

                    if (AudioManager.SCO_AUDIO_STATE_CONNECTED == state) { // 判断值是否是：1
                        AiSpeechLogUtil.d(TAG, "SCO 开启成功!");
                        if (!audioManager.isBluetoothScoOn())
                            audioManager.setBluetoothScoOn(true);  // 打开SCO

                        invaild = false;

                        if (listener != null) listener.onSuccess();

                        stopReceiveScoBroadcast(context);

                    } else if (AudioManager.SCO_AUDIO_STATE_ERROR == state) { // 等待一秒后再尝试启动SCO
                        AiSpeechLogUtil.e(TAG, "onReceive: SCO 未获取到状态");
                    } else if (AudioManager.SCO_AUDIO_STATE_CONNECTING == state) {
                        AiSpeechLogUtil.d(TAG, "onReceive: SCO 正在连接……");
                    } else if (AudioManager.SCO_AUDIO_STATE_DISCONNECTED == state) {
                        AiSpeechLogUtil.d(TAG, "onReceive: SCO 已断开");
                        invaild = true;
                        if (listener != null){
                            listener.onError("Sco Disconnected!");
                        }
                        // 三星开启后(startBluetoothSco)会, 经过已断开状态后, 再变为已开启状态, 此时不应该停止 sco 而是等待开启成功
//                        audioManager.stopBluetoothSco();
//                        audioManager.setBluetoothScoOn(false);
                    }
                }
            };
            context.registerReceiver(scoBroadcastReceiver, new IntentFilter(AudioManager.ACTION_SCO_AUDIO_STATE_UPDATED));
        }
    }

    private void stopReceiveScoBroadcast(final Context context) {
        if (scoBroadcastReceiver != null) {
            context.unregisterReceiver(scoBroadcastReceiver);  // 取消广播，别遗漏
            scoBroadcastReceiver = null;
        }
    }

    public void stopBluetoothSco(Context context) {

        AudioManager audioManager = (AudioManager) context.getSystemService(Context.AUDIO_SERVICE);
        boolean bluetoothScoOn = audioManager.isBluetoothScoOn();
        AiSpeechLogUtil.d(TAG, "stopBluetoothSco: " + "mode: " + audioManager.getMode() + " isSpeakerphoneOn: " + audioManager.isSpeakerphoneOn() + " isBluetoothScoOn: " + audioManager.isBluetoothScoOn() + " isBluetoothA2dpOn: " + audioManager.isBluetoothA2dpOn());
//        if (bluetoothScoOn) { // 不判断直接强制关闭, 以免 isBluetoothScoOn()为 false 但 sco 是连接状态
        AiSpeechLogUtil.e(TAG, "stopBluetoothSco 关闭SCO通道");
        audioManager.setBluetoothScoOn(false);
        audioManager.stopBluetoothSco();
//        }

        invaild = true;

        if (scoBroadcastReceiver != null) {
            try {
                context.unregisterReceiver(scoBroadcastReceiver);
                scoBroadcastReceiver = null;
            } catch (Exception e) {
                //规避：java.lang.IllegalArgumentException:Receiver not registered
                AiSpeechLogUtil.e(TAG, "stopBluetoothSco: unregisterReceiver 异常 = " + e.getMessage());
            }
        }
    }

    public void setPreferredDevice(final Context context, final AudioRecord audioRecord) {
        AudioManager audioManager = (AudioManager) context.getSystemService(Context.AUDIO_SERVICE);

        for (AudioDeviceInfo device : audioManager.getDevices(AudioManager.GET_DEVICES_INPUTS)) {
                /*
                    2020-06-02 16:41:24.553 19259-19552/com.translation666 E/HeadsetRecorder: 当前所有的音频输入设备：Timekettle M2 type:7 id:188
                    2020-06-02 16:41:24.553 19259-19552/com.translation666 E/HeadsetRecorder: 当前所有的音频输入设备：FLA-AL20 type:15 id:6
                    2020-06-02 16:41:24.553 19259-19552/com.translation666 E/HeadsetRecorder: 当前所有的音频输入设备：FLA-AL20 type:15 id:7
                * */
            AiSpeechLogUtil.e(TAG, "当前所有的音频输入设备：" + device.getProductName() + " type:" + device.getType() + " id:" + device.getId());
        }

//        AiSpeechLogUtil.e(TAG, "setPreferredDevice: Build.MANUFACTURER: " + Build.MANUFACTURER);
        String DeviceBrand = Build.BRAND.toLowerCase();
        if (DeviceBrand.contains("huawei")) {
            // 华为系列手机设置首选项之后, 在 路由切换的时候会更快速的切换成功, 例如 setSpeakerphoneOn(false) setBluetoothScoOn(true)切换到 sco;
            // 否则录音过程中切换将在 3-5s 后才成功切换到对应路由, 具体可通过对耳机或手机说话测试, 暂未找到真正切换成功的状态标记
            for (AudioDeviceInfo device : audioManager.getDevices(AudioManager.GET_DEVICES_INPUTS)) {
                if (device.getType() == TYPE_BLUETOOTH_SCO) {
                    AiSpeechLogUtil.e(TAG, "设置SCO为首选音频输入设备");
                    if (audioRecord != null) {
                        boolean status = audioRecord.setPreferredDevice(device); // 设置首选设备为蓝牙sco的麦克风
                        if (status) AiSpeechLogUtil.e(TAG, "设置成功");
                        else AiSpeechLogUtil.e(TAG, "设置失败");
                    }
                    break;
                }
            }
        }
    }

    public boolean isBluetoothScoOn(Context context) {
        AudioManager audioManager = (AudioManager) context.getSystemService(Context.AUDIO_SERVICE);
        return audioManager.isBluetoothScoOn();
    }

    public boolean isBlueToothHeadsetConnected() {
        boolean retval = false;
        try {
            retval = BluetoothAdapter.getDefaultAdapter().getProfileConnectionState(android.bluetooth.BluetoothProfile.HEADSET) != android.bluetooth.BluetoothProfile.STATE_DISCONNECTED;

        } catch (Exception exc) {
            // nothing to do
        }
        return retval;
    }

    private void setMediaButtonEvent(Context context) {
        mSession = new MediaSession(context, ISpeechConstant.RECORDER.HEADSET.toString());
        mSession.setCallback(new MediaSession.Callback() {
            @Override
            public void onPlay() {
                super.onPlay();
                AiSpeechLogUtil.e(TAG, "========开始播放");
                updatePlaybackState(true);
            }

            @Override
            public void onPause() {
                super.onPause();
                AiSpeechLogUtil.e(TAG, "========暂停播放");
                updatePlaybackState(false);
            }

            @Override
            public void onSkipToNext() {
                super.onSkipToNext();
                AiSpeechLogUtil.e(TAG, "==========播放下一首");
            }

            @Override
            public void onSkipToPrevious() {
                super.onSkipToPrevious();
                AiSpeechLogUtil.e(TAG, "========播放上一首");
            }
        });
    }

    /*
     * update mediaCenter state
     */
    private void updatePlaybackState(boolean isPlaying) {
        PlaybackState.Builder stateBuilder = new PlaybackState.Builder().setActions(PlaybackState.ACTION_PLAY | PlaybackState.ACTION_PLAY_PAUSE | PlaybackState.ACTION_PLAY_FROM_MEDIA_ID | PlaybackState.ACTION_PAUSE | PlaybackState.ACTION_SKIP_TO_NEXT | PlaybackState.ACTION_SKIP_TO_PREVIOUS);
        if (isPlaying) {
            stateBuilder.setState(PlaybackState.STATE_PLAYING, PlaybackState.PLAYBACK_POSITION_UNKNOWN, SystemClock.elapsedRealtime());
        } else {
            stateBuilder.setState(PlaybackState.STATE_PAUSED, PlaybackState.PLAYBACK_POSITION_UNKNOWN, SystemClock.elapsedRealtime());
        }
        mSession.setPlaybackState(stateBuilder.build());
    }


    public interface Listener {
        void onError(String error);

        void onSuccess();
    }


    /**
     * 将sco强制设为开启状态（不管设置是否成功，只管调用）
     */
    public void forceSetScoOn(final Context context){
        AudioManager audioManager = (AudioManager) context.getSystemService(Context.AUDIO_SERVICE);
        audioManager.setBluetoothScoOn(true);
        invaild = false;
    }
}
