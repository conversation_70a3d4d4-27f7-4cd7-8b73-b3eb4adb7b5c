package co.timekettle.speech.recorder;

import android.Manifest;
import android.content.Context;
import android.content.pm.PackageManager;
import android.media.AudioRecord;

import androidx.core.content.ContextCompat;

import co.timekettle.speech.TestRecorder;
import co.timekettle.speech.ISpeechConstant;
import co.timekettle.speech.ispeech.algorithm.AudioDoaJni;
import co.timekettle.speech.utils.BytesTrans;
import co.timekettle.speech.utils.AiSpeechLogUtil;

import java.util.Arrays;
import java.util.HashMap;
import java.util.Map;
import java.util.concurrent.ConcurrentLinkedDeque;
import java.util.concurrent.Semaphore;

public class T1MicAudioRecorder extends AudioRecorderBase {

    private static final String TAG = "T1PhoneAudioRecorder";
    private Semaphore stopSema = null;
    private AudioDoaJni doa = new AudioDoaJni();
    TestRecorder tagFileHandle;
    TestRecorder fileHandle;

    public T1MicAudioRecorder(Context context) {
        super(context, ISpeechConstant.RECORDER.T1PHONE.toString());
    }

    @Override
    public void setDropSampleCount() {
        dropSampleCount = 5;
    }

    int nSampleDoaInPerChannel = 512; // 每通道 512 个采样点, 32ms
    int nSampleDoaInPerPacket = nSampleDoaInPerChannel * 2; // 双通道采样点个数
    int nSizeInBuffer = nSampleDoaInPerPacket;
    int nSizeInChannel = nSampleDoaInPerChannel / 2; // 送入通道的数据大小

    int nSizeDoaCache = 6; // doa 缓存包数

    void send(short[] doaPacket, AudioDoaJni.Channel mou, short[] bufferInChannel1, short[] bufferInChannel2) {

        if (this.writeToFile) {
            if (tagFileHandle != null) {
                byte[] firstTag = new byte[]{(byte) 0xff, (byte) 0x7f, (byte) 0x0, (byte) 0x0};
                byte[] SecondTag = new byte[]{(byte) 0x0, (byte) 0x0, (byte) 0x00, (byte) 0x80};
                tagFileHandle.write(mou == AudioDoaJni.Channel.First ? firstTag : SecondTag);
                tagFileHandle.write(BytesTrans.getInstance().Shorts2Bytes(doaPacket));
            }
            if (fileHandle != null) {
                fileHandle.write(BytesTrans.getInstance().Shorts2Bytes(doaPacket));
            }
        }

        for (int i = 0; i < nSampleDoaInPerChannel; i++) {
            bufferInChannel1[i % nSizeInChannel] = doaPacket[i * 2];
            bufferInChannel2[i % nSizeInChannel] = doaPacket[i * 2 + 1];

            if (mou != AudioDoaJni.Channel.First) {
                bufferInChannel1[i % nSizeInChannel] = 0;
            } else {
                bufferInChannel2[i % nSizeInChannel] = 0;
            }

            if (i % nSizeInChannel == nSizeInChannel - 1) {
                synchronized (audioChannel) {
                    if (audioChannel.size() >= 2) {
                        // copy 出新数据送入通道
                        // 注意通道添加顺序, 目前 audioChannel[0] 绑定的是左通道
                        writeAudioToChannel(audioChannel.get(0), Arrays.copyOf(bufferInChannel1, bufferInChannel1.length));
                        writeAudioToChannel(audioChannel.get(1), Arrays.copyOf(bufferInChannel2, bufferInChannel2.length));
//                            Arrays.fill(buffer1, (short) 0);
//                            Arrays.fill(buffer2, (short) 0);
                    }
                }
            }
        }
    }

    @Override
    public void start(final Map<String, Object> options) {
        super.start(options);

        if (isWorking) {
            return;
        }

        isWorking = true;
        paused = false;

        Thread recordThread = new Thread(() -> {
            ConcurrentLinkedDeque<short[]> doaCache = new ConcurrentLinkedDeque<>(); // doa 缓存 6 帧
            AudioDoaJni.Channel dir = AudioDoaJni.Channel.None;
            short[] doaPacket = new short[nSampleDoaInPerPacket]; // 32ms立体音, 512*2 个 short, 双通道数据, 送入算法
            short[] bufferInChannel = new short[nSizeInChannel]; // 16ms, 256 个 short, 送入通道的数据
            short[] bufferInChanne2 = new short[nSizeInChannel]; // 16ms, 256 个 short, 送入通道的数据

            int bufferSize = AudioRecord.getMinBufferSize(defaultSampleRateInHz, StereoChannel, defaultAudioFormat);
            while (nSizeInBuffer < bufferSize) {
                nSizeInBuffer += nSampleDoaInPerPacket;
            }
            short[] recorderBuffer = new short[nSizeInBuffer];

            AudioRecord audioRecord = new AudioRecord(defaultAudioSource, defaultSampleRateInHz, StereoChannel, defaultAudioFormat, nSizeInBuffer);
            if (ContextCompat.checkSelfPermission(context, Manifest.permission.RECORD_AUDIO) !=
                    PackageManager.PERMISSION_GRANTED ||
                    audioRecord.getState() != AudioRecord.STATE_INITIALIZED) {
                AiSpeechLogUtil.e(TAG, " new AudioRecord 初始化失败，未检测到录音权限！");
                return;
            }

            audioRecord.startRecording(); // 若 mic 被占用会出现报错: AudioRecord: start() status -38, mic被占用是因为 headset录音还未停止

            if (this.writeToFile) {
                fileHandle = new TestRecorder(context, "TK_Record", null, "t1" + "-raw-record", true); // 此处设置录音文件开关
                tagFileHandle = new TestRecorder(context, "TK_Record", null, "t1" + "-tag-raw-record", true); // 此处设置录音文件开关
            }
            stopSema = new Semaphore(0);
            AiSpeechLogUtil.d(TAG, name + " 开始录音");

//            //// >>>>>> 测试模块, 测试录音后的流程
//            InputStream in = null;
//            try {
//                in = context.getAssets().open(audio_samples/T1-2022-07-21-11-56.pcm");
//            } catch (IOException e) {
//                e.printStackTrace();
//            }
//            byte[] fileBuffer = new byte[nSizeInBuffer * 2]; // 32ms, 512*2 个 short, 双通道数据, 送入算法
//            //// <<<<<< 测试模块, 测试录音后的流程

            while (isWorking) {
                int bufferReadResult = audioRecord.read(recorderBuffer, 0, nSizeInBuffer);
                if (bufferReadResult != nSizeInBuffer) {
                    break;
                }
//                {
//                    // 测试模块, 测试录音后的流程
//                    assert in != null;
//                    try {
//                        if (-1 != in.read(fileBuffer)) {
//                            recorderBuffer = BytesTrans.getInstance().Bytes2Shorts(fileBuffer);
//                        } else {
//                            break;
//                        }
//                    } catch (IOException e) {
//                        e.printStackTrace();
//                    }
//                }
                writed_count++;

                if (paused) {
                    AiSpeechLogUtil.d(TAG, "run: 暂停中 ");
                    continue;
                }

                if (dropSampleCount > 0) {
                    dropSampleCount--;
                    continue;
                }

                for (int j = 0; j < nSizeInBuffer / nSampleDoaInPerPacket; j++) {
                    System.arraycopy(recorderBuffer, j * nSampleDoaInPerPacket, doaPacket, 0, nSampleDoaInPerPacket);

                    // T1 设备下方 mic 是通道 1, 上方 mic 是通道 2
                    AudioDoaJni.Channel mou = doa.processDoa(doaPacket);

                    // 方向为 Channel.None, 则是初始化阶段
                    if (dir == AudioDoaJni.Channel.None) {
                        dir = mou;
                    } else {
                        // 方向转变, 需要写完缓存的 nSizeDoaCache 帧
                        if (dir != mou) {
                            dir = mou; // 更新新方向
                            while (doaCache.size() > 0) {
                                short[] oldDoaPacket = doaCache.pop();
                                send(oldDoaPacket, dir, bufferInChannel, bufferInChanne2);
                            }
                        }
                    }

                    doaCache.offer(Arrays.copyOf(doaPacket, doaPacket.length));
                    if (doaCache.size() <= nSizeDoaCache) continue;
                    doaPacket = doaCache.pop();

                    send(doaPacket, mou, bufferInChannel, bufferInChanne2);
                }
            }

            audioRecord.release();
            AiSpeechLogUtil.d(TAG, name + " 录音已停止");

            if (stopSema != null) stopSema.release();
        });
        recordThread.setName("Tmk-" + TAG);
        recordThread.start();
    }

    @Override
    public void stop() {
        super.stop();
        AiSpeechLogUtil.d(TAG, "stop");
        try {
            if (stopSema != null) stopSema.acquire();
            stopSema = null;
        } catch (InterruptedException e) {
            e.printStackTrace();
        }
        removeAllChannel();

        if (tagFileHandle != null) {
            tagFileHandle.close();
        }
        if (fileHandle != null) {
            fileHandle.close();
        }
    }

    @Override
    public void stop2() {
        super.stop2();
        AiSpeechLogUtil.d(TAG, "stop");
        try {
            if (stopSema != null) stopSema.acquire();
            stopSema = null;
        } catch (InterruptedException e) {
            e.printStackTrace();
        }
//        removeAllChannel();
    }
}
