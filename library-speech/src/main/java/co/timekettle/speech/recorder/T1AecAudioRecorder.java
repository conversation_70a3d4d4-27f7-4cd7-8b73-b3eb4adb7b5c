package co.timekettle.speech.recorder;

import android.Manifest;
import android.content.Context;
import android.content.pm.PackageManager;
import android.media.AudioRecord;
import android.media.MediaRecorder;
import android.util.Log;

import androidx.core.content.ContextCompat;

import co.timekettle.speech.TestRecorder;
import co.timekettle.speech.ISpeechConstant;
import co.timekettle.speech.ispeech.algorithm.AudioDoaJni;
import co.timekettle.speech.ispeech.algorithm.TmkAec;
import co.timekettle.speech.utils.BytesTrans;
import co.timekettle.speech.utils.AiSpeechLogUtil;
import co.timekettle.speech.utils.ShortRingBuffer;

import java.io.InputStream;
import java.util.Arrays;
import java.util.HashMap;
import java.util.Map;
import java.util.concurrent.ConcurrentSkipListMap;
import java.util.concurrent.LinkedBlockingQueue;
import java.util.concurrent.Semaphore;
import java.util.concurrent.atomic.AtomicBoolean;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.concurrent.atomic.AtomicLong;

/**
 * 检测的通道标识
 */
enum ActiveDir {
    DOWN,
    UP,
    NONE
}

class RecordPacket {
    public RecordPacket(short[] buffer) {
        this.buffer = Arrays.copyOf(buffer, buffer.length);
        this.bufferInChannel1 = new short[buffer.length / 2];
        this.bufferInChannel2 = new short[buffer.length / 2];
        for (int i = 0; i < buffer.length / 2; i++) {
            this.bufferInChannel1[i] = buffer[i * 2];
            this.bufferInChannel2[i] = buffer[i * 2 + 1];
        }
        this.id = 0;
        this.state = -1;
    }

    public static RecordPacket Speaker(short[] buffer) {
        RecordPacket packet = new RecordPacket(buffer);

        boolean isVoiceCh1 = false; // Speaker 里的录音只要幅值 > 0 认为开始了播放
        int averageEnergyCh1 = 0;
        for (short b : packet.bufferInChannel1) {
            averageEnergyCh1 = averageEnergyCh1 + Math.abs(b);
            if (b != 0 && !isVoiceCh1) {
                isVoiceCh1 = true;
            }
        }
        packet.averageEnergyCh1 = averageEnergyCh1 / packet.bufferInChannel1.length;
        packet.isVoiceCh1 = isVoiceCh1;
//        if (packet.isVoiceCh1) Log.d("===", "start: speaker 是否有声音: " + true);

        boolean isVoiceCh2 = false; // Speaker 里的录音只要幅值 > 0 认为开始了播放
        int averageEnergyCh2 = 0;
        for (short b : packet.bufferInChannel2) {
            averageEnergyCh2 = averageEnergyCh2 + Math.abs(b);
            if (b != 0 && !isVoiceCh2) {
                isVoiceCh2 = true;
            }
        }
        packet.averageEnergyCh2 = averageEnergyCh2 / packet.bufferInChannel2.length;
        packet.isVoiceCh2 = isVoiceCh2;
//        if (packet.isVoiceCh2) Log.d("===", "start: speaker 是否有声音: " + true);

        return packet;
    }

    public static int GetAverageEnergy(short[] buffer) {
        int averageEnergy = 0;
        for (short b : buffer) {
            averageEnergy = averageEnergy + Math.abs(b);
        }
        return averageEnergy;
    }

    short[] buffer; // mic/speaker 的立体音录音 buffer
    short[] bufferInChannel1; // mic 1/speaker 1 的录音 buffer
    short[] bufferInChannel2; // mic 1/speaker 2 的录音 buffer
    long id; // 某次事件
    long index; // 包序号
    int state; // -1 默认状态, 0 启动, 1 正在处理, 2 结束
    ActiveDir ch = ActiveDir.NONE; // 用于 mic 立体音录音; 当前是 doa 检测的某通道数据, 0 代表通道 1, 1 代表通道 2
    int averageEnergyCh1 = -1;
    boolean isVoiceCh1; // 是否是有效录音
    int averageEnergyCh2 = -1; // 通道 2 能量
    boolean isVoiceCh2; // 通道 2 是否是有效录音
}

public class T1AecAudioRecorder extends AudioRecorderBase {

    private static final String TAG = "T1AecAudioRecorder";
    private Semaphore stopSema = null;

    public T1AecAudioRecorder(Context context) {
        super(context, ISpeechConstant.RECORDER.T1PHONEAEC.toString());

        aecProcessor = new TmkAec();
    }

    @Override
    public void setDropSampleCount() {
        dropSampleCount = 5;
    }

    TmkAec aecProcessor;

    ShortRingBuffer all_buffer0 = new ShortRingBuffer(TmkAec.getSizePerRoutine() * 10);
    ShortRingBuffer all_buffer1 = new ShortRingBuffer(TmkAec.getSizePerRoutine() * 10);
    ShortRingBuffer out_buffer  = new ShortRingBuffer(TmkAec.getSizePerRoutine() * 10);
    short[] channelPacket = new short[ISpeechConstant.DefaultSamplesPerPacketInMono];
    short[] empty = new short[ISpeechConstant.DefaultSamplesPerPacketInMono];
    short[] buffer0 = new short[TmkAec.getSizePerRoutine()];
    short[] buffer1 = new short[TmkAec.getSizePerRoutine()];
    TestRecorder speakerSourceHandle = null;
    TestRecorder micSourceHandle = null;
    TestRecorder speakerHandle = null;
    TestRecorder micHandle = null;
    TestRecorder aecHandle = null;
    TestRecorder doaHandle = null;

    // 512 samples
    private void ProcessAec(RecordPacket micPacket, RecordPacket speakerPacket) {
        short[] mic = speakerPacket.ch == ActiveDir.DOWN ? micPacket.bufferInChannel2 : micPacket.bufferInChannel1;
        short[] speaker = speakerPacket.ch == ActiveDir.DOWN ? speakerPacket.bufferInChannel2 : speakerPacket.bufferInChannel1;
        assert mic.length == 512 && speaker.length == 512 : "mic, speaker 的长度不等于 512";
        if (speakerPacket.state == 0) {
            if (this.writeToFile) {
                micHandle = new TestRecorder(context, "TK_Record", null, "source-mic", true); // 此处设置录音文件开关
                speakerHandle = new TestRecorder(context, "TK_Record", null, "source-speaker", true); // 此处设置录音文件开关
                aecHandle = new TestRecorder(context, "TK_Record", null, "source-aec", true); // 此处设置录音文件开关
            }

            Log.d(TAG, "ProcessAec: 处理开始");
            aecProcessor.reset();
        } else if (speakerPacket.state == 2) {
            Log.d(TAG, "ProcessAec: 处理完成");
        }
        if (micHandle != null) micHandle.write(BytesTrans.getInstance().Shorts2Bytes(mic));
        if (speakerHandle != null) speakerHandle.write(BytesTrans.getInstance().Shorts2Bytes(speaker));
        all_buffer0.write(mic);
        all_buffer1.write(speaker);

        while (TmkAec.getSizePerRoutine() <= all_buffer0.readable() && TmkAec.getSizePerRoutine() <= all_buffer1.readable()) {
            all_buffer0.peek(buffer0);
            all_buffer1.peek(buffer1);
//            short[] output = aecProcessor.process(buffer0, buffer1);
//            if (speakerPacket.state == 2 && TmkAec.getSizePerRoutine() == all_buffer0.readable()) {
//                Log.d(TAG, "ProcessAec: 预留数据 all_buffer1: " + all_buffer1.readable());
//                out_buffer.write(output);
//                all_buffer0.read(buffer0, 0, TmkAec.getSizePerRoutine(), false); // 删除
//                all_buffer1.read(buffer1, 0, TmkAec.getSizePerRoutine(), false); // 删除
//            } else {
//                out_buffer.write(output, 0 , TmkAec.getSizePerRoutine() / 4);
//                all_buffer0.read(buffer0, 0, TmkAec.getSizePerRoutine() / 4, false); // 删除
//                all_buffer1.read(buffer1, 0, TmkAec.getSizePerRoutine() / 4, false); // 删除
//            }
//
            short[] output;
            if (speakerPacket.state == 2 && TmkAec.getSizePerRoutine() == all_buffer0.readable()) {
                output = aecProcessor.process_end(buffer0, buffer1);
            } else {
                output = aecProcessor.process(buffer0, buffer1);
            }
            all_buffer0.read(buffer0, 0, TmkAec.getSizePerRoutine(), false); // 删除
            all_buffer1.read(buffer1, 0, TmkAec.getSizePerRoutine(), false); // 删除
            if (aecHandle != null) aecHandle.write(BytesTrans.getInstance().Shorts2Bytes(output));
            sendData(micPacket.ch, output);
        }
    }

    private void sendData(ActiveDir ch, short[] buffer) {
        out_buffer.write(buffer);
        while (out_buffer.readable() >= channelPacket.length) {
            out_buffer.read(channelPacket);

            synchronized (audioChannel) {
                if (audioChannel.size() >= 2) {
                    writeAudioToChannel(audioChannel.get(0), ch == ActiveDir.DOWN ? channelPacket : empty);
                    writeAudioToChannel(audioChannel.get(1), ch == ActiveDir.UP ? channelPacket : empty);
                }
            }
        }
    }

    void unittest() {
        new Thread(() -> {
            try {
                int nSamples = 512;
                int nSamplesStep = nSamples / 2; // 帧移的采样数
                InputStream in0 = context.getAssets().open( "audio_samples/aectest-source-mic.pcm");
                InputStream in1 = context.getAssets().open("audio_samples/aectest-source-speaker.pcm");
                if (in0.available() <= nSamples * 2 || in1.available() <= nSamples * 2) {
                    Log.e(TAG, "TmkAec: 可能文件数据缺失");
                    return;
                }
                byte[] all_buffer00 = new byte[in0.available()];
                in0.read(all_buffer00);
                byte[] all_buffer11 = new byte[in1.available()];
                in1.read(all_buffer11);

                short[] all_buffer0 = Bytes2Shorts(all_buffer00);
                short[] all_buffer1 = Bytes2Shorts(all_buffer11);

                short[] buffer0 = new short[nSamples]; // 512
                short[] buffer1 = new short[nSamples]; // 512
                short[] buffer0Stereo = new short[nSamples * 2];
                short[] buffer1Stereo = new short[nSamples * 2];
                int offset = 0;
                while (offset + nSamples <= all_buffer0.length && offset + nSamples <= all_buffer1.length) {
                    System.arraycopy(all_buffer0, offset, buffer0, 0, buffer0.length);
                    System.arraycopy(all_buffer1, offset, buffer1, 0, buffer1.length);

                    for (int j = 0; j < nSamples; j++) {
//                        buffer0Stereo[j * 2] = buffer0[j]; // mic 通道 1 有声音
//                        buffer1Stereo[j * 2 + 1] = buffer1[j]; // speaker 通道 2 有声音
                        buffer0Stereo[j * 2 + 1] = buffer0[j];
                        buffer0Stereo[j * 2] = 1;
                        buffer1Stereo[j * 2] = buffer1[j];
                    }
                    processSpeakerRecordData(buffer1Stereo);
                    processMicRecordData(buffer0Stereo);
                    Thread.sleep(20);

                    offset = offset + nSamples;
                }
            } catch (Exception e) {
                e.printStackTrace();
            }
        }).start();
    }

    class AecEvent {
        AtomicLong id = new AtomicLong(0); // > 0 即为有 aec 事件
        AtomicInteger state = new AtomicInteger(-1); // 0 启动, 1 正在处理, 2 结束, -1 默认
        Integer offset = 0; // 采样点偏移, 按照经验值需 80ms < aecOffset < 175ms, 160 * 8 个采样点
        AtomicLong packetLen = new AtomicLong(0);
        AtomicLong micPacketLen = new AtomicLong(0); // mic record thread 读写

        ActiveDir ch = ActiveDir.DOWN; // 当前播放的通道, 值为 0/1 指 speaker 1/2
        AtomicBoolean align = new AtomicBoolean(false); // 表示:正在发生, 数据是否在对齐中, align 的条件是 state == 2时, packetLen 与 micPacketLen 相等
        AecEvent(Long id) {
            this.id.set(id);
        }
    }

    // FIXME: 2023/3/7 aecEvents 目前未卸载存在一些泄漏, 找合适的时机释放
    ConcurrentSkipListMap<Long, AecEvent> aecEvents = new ConcurrentSkipListMap<Long, AecEvent>();
    LinkedBlockingQueue<RecordPacket> packetQueue; // 通过事件标记开始, 结束由 speakerPacketQueue 长度决定
    LinkedBlockingQueue<RecordPacket> speakerPacketQueue;

    /**
     * mic 录音处理(独立线程)
     */
    void processMicRecordData(short[] buffer) {
        if (micSourceHandle != null) micSourceHandle.write(BytesTrans.getInstance().Shorts2Bytes(buffer));
        RecordPacket rPacket = new RecordPacket(buffer);
        Map.Entry<Long,AecEvent> entry = aecEvents.lastEntry(); // 实时处理数据, 取最新的一个 aec 事件
        if (entry != null) {
            AecEvent aecEvent = entry.getValue();
            if (aecEvent != null) {
                // 查看当前的 偏移的延时
                double offset_delay = aecEvent.offset * rPacket.buffer.length / 2.0 / 16000.0 * 1000; // 向后推的时延
                // 找起始帧
                boolean started = aecEvent.micPacketLen.get() > 0;
                if (!started) {
                    if (offset_delay >= 175) { // 经验值最多偏移至 175ms
                        started = true;
                    } else if (offset_delay >= 0 && offset_delay < 175) {
                        if (RecordPacket.GetAverageEnergy(aecEvent.ch == ActiveDir.DOWN ? rPacket.bufferInChannel2 : rPacket.bufferInChannel1) > 32) { // 检测第一帧平均振幅>0.001, 真正的播放开始
                            started = true;
                        }
                    }
                }
                if (started) {
                    rPacket.id = aecEvent.id.get();
                    if (aecEvent.micPacketLen.get() == 0) { // 刚开始
                        rPacket.state = 0;
                    } else {
                        rPacket.state = 1;
                    }
                    aecEvent.micPacketLen.set(aecEvent.micPacketLen.get() + 1);
                    rPacket.index = aecEvent.micPacketLen.get();
                }
                aecEvent.offset = aecEvent.offset + 1;
            }

            // 实时处理数据, 取最新的一个正在发生的 aec 事件
            if (aecEvent != null && !aecEvent.align.get()) {
                if (doaHandle != null) doaHandle.write(aecEvent.ch == ActiveDir.DOWN ? "4".getBytes() : "3".getBytes());
                rPacket.ch = aecEvent.ch == ActiveDir.DOWN ? ActiveDir.UP : ActiveDir.DOWN;
                if (micSourceHandle != null) {
                    micSourceHandle.write(BytesTrans.getInstance().Shorts2Bytes(rPacket.ch == ActiveDir.DOWN ? new short[]{Short.MAX_VALUE / 4 * 3, 0} : new short[]{0, Short.MIN_VALUE / 4 * 3}));
                }
            }
        }

        // 无 aec 事件(未播放时), 通过 doa 区分方向
        if (rPacket.ch == ActiveDir.NONE) {
            // T1 设备下方 mic 是通道 Channel.First, 上方 mic 是通道 Channel.Second
            AudioDoaJni.Channel mou = doa.processDoa(buffer);
            if (mou == AudioDoaJni.Channel.None) Log.d(TAG, "processMicRecordData: AudioDoaJni.Channel.None");
            if (doaHandle != null) doaHandle.write(mou == AudioDoaJni.Channel.First ? "1".getBytes() : "2".getBytes());
            rPacket.ch = mou == AudioDoaJni.Channel.First ? ActiveDir.DOWN : ActiveDir.UP;
            if (micSourceHandle != null) {
                micSourceHandle.write(BytesTrans.getInstance().Shorts2Bytes(rPacket.ch == ActiveDir.DOWN ? new short[]{Short.MAX_VALUE / 2, 0} : new short[]{0, Short.MIN_VALUE / 2}));
            }
        }

        packetQueue.add(rPacket);
    }

    /**
     * speaker 录音处理(独立线程), 检测播放事件
     */
    void processSpeakerRecordData(short[] buffer) {
        if (speakerSourceHandle != null) speakerSourceHandle.write(BytesTrans.getInstance().Shorts2Bytes(buffer));
        RecordPacket packet = RecordPacket.Speaker(buffer);
        // 检测播放的开始和结束, 重新生成文件
        if (packet.isVoiceCh1 || packet.isVoiceCh2) {
            if (!speakerIsPlaying) { // 刚开始播放
                if (packet.averageEnergyCh1 > 16 || packet.averageEnergyCh2 > 16) { // 检测第一帧平均振幅>0.0005, 真正的播放开始)
                    Log.d(TAG, "start: speaker 开始播放");
                    speakerIsPlaying = true;
                    AecEvent aecEvent = new AecEvent(System.currentTimeMillis());
                    aecEvents.put(aecEvent.id.get(), aecEvent);
                    aecEvent.state.set(0);
                    aecEvent.packetLen.set(1);
                    aecEvent.ch = packet.averageEnergyCh1 > 16 ? ActiveDir.UP : ActiveDir.DOWN; // 左通道为 上方 speaker
                    packet.id = aecEvent.id.get();
                    packet.state = aecEvent.state.get();
                    packet.index = aecEvent.packetLen.get();
                    packet.ch = aecEvent.ch;
                    speakerPacketQueue.add(packet);
                }
            } else {
                Map.Entry<Long,AecEvent> entry = aecEvents.lastEntry();
                assert entry != null;
                AecEvent aecEvent = entry.getValue();
                aecEvent.state.set(1);
                aecEvent.packetLen.set(aecEvent.packetLen.get() + 1);
                packet.id = aecEvent.id.get();
                packet.state = aecEvent.state.get();
                packet.index = aecEvent.packetLen.get();
                packet.ch = aecEvent.ch;
                speakerPacketQueue.add(packet);
            }
        } else {
            if (speakerIsPlaying) { // 刚结束播放
                Log.d(TAG, "start: speaker 结束播放");
                speakerIsPlaying = false;
                Map.Entry<Long,AecEvent> entry = aecEvents.lastEntry();
                assert entry != null;
                AecEvent aecEvent = entry.getValue();
                aecEvent.state.set(2);
                aecEvent.packetLen.set(aecEvent.packetLen.get() + 1);
                packet.id = aecEvent.id.get();
                packet.state = aecEvent.state.get();
                packet.index = aecEvent.packetLen.get();
                packet.ch = aecEvent.ch;
                speakerPacketQueue.add(packet);
            }
        }
    }

    /**
     * 同步线程, 当 speaker 播放事件触发时, 同步 mic 和 speaker 数据进行 aec 处理
     */
    void synchronizeProcess() {
        try {
            RecordPacket packet = packetQueue.take();
            AecEvent aecEvent = aecEvents.get(packet.id);
            if (aecEvent != null && !aecEvent.align.get()) { // aec 事件
                // 此处根据 speakerPacket 计算 packet 的结束标记, 来对齐
                RecordPacket speakerPacket = speakerPacketQueue.take(); // speakerPacketQueue.size() > 0
                if (speakerPacket.state == 2) {
                    aecEvent.align.set(true);
                    aecEvents.remove(aecEvent.id.get());
                    packet.state = 2;
                }
                if (packet.state == 0) {
                    short[] tag = {Short.MAX_VALUE, Short.MAX_VALUE}; // 标记
                    if (micSourceHandle != null) micSourceHandle.write(BytesTrans.getInstance().Shorts2Bytes(tag));
                } else if (packet.state == 2) {
                    short[] tag = {Short.MIN_VALUE, Short.MIN_VALUE}; // 标记
                    if (micSourceHandle != null) micSourceHandle.write(BytesTrans.getInstance().Shorts2Bytes(tag));
                }
                ProcessAec(packet, speakerPacket);
//                if (writed_count % 100 == 0) Log.d(TAG, "start: 1111已在播放写入通道");
                return;
            }
            sendData(packet.ch, packet.bufferInChannel1);
//            if (writed_count % 100 == 0) Log.d(TAG, "start: 0000不在播放正常写入通道");
        } catch (InterruptedException e) {
            e.printStackTrace();
        }
    }

    public short[] Bytes2Shorts(byte[] buf) {
        byte bLength = 2;
        short[] s = new short[buf.length / bLength];
        for (int iLoop = 0; iLoop < s.length; iLoop++) {
            short value = 0; // 每个采样点的值
            for (int j = 0; j < bLength; j++) {
                value |= ((long) (buf[iLoop * bLength + j] & 0xff)) << (8 * j);
            }
            s[iLoop] = value;
        }
        return s;
    }

    private AudioDoaJni doa = new AudioDoaJni();
    int nSampleDoaInPerChannel = 512; // 每通道 512 个采样点, 32ms
    int nSampleDoaInPerPacket = nSampleDoaInPerChannel * 2; // 双通道采样点个数
    int nSizeInBuffer = nSampleDoaInPerPacket;
    int nSizeInPipe = nSampleDoaInPerChannel / 2; // 送入通道的数据大小
    @Override
    public void start(final Map<String, Object> options) {
        super.start(options);

        if (isWorking) {
            return;
        }

        isWorking = true;
        paused = false;
        micIsRecording = false;
        speakerIsRecording = false;

        all_buffer0 = new ShortRingBuffer(TmkAec.getSizePerRoutine() * 10);
        all_buffer1 = new ShortRingBuffer(TmkAec.getSizePerRoutine() * 10);
        out_buffer  = new ShortRingBuffer(TmkAec.getSizePerRoutine() * 10);
        channelPacket = new short[ISpeechConstant.DefaultSamplesPerPacketInMono];
        buffer0 = new short[TmkAec.getSizePerRoutine()];
        buffer1 = new short[TmkAec.getSizePerRoutine()];
        packetQueue = new LinkedBlockingQueue<RecordPacket>();
        speakerPacketQueue = new LinkedBlockingQueue<RecordPacket>();
        if (this.writeToFile) {
            speakerSourceHandle = new TestRecorder(context, "TK_Record", null, "source-speaker-all", true); // 此处设置录音文件开关
            micSourceHandle = new TestRecorder(context, "TK_Record", null, "source-mic-all", true); // 此处设置录音文件开关
            doaHandle = new TestRecorder(context, "TK_Record", null, "source-doa", true, true); // 此处设置录音文件开关
        }
        Thread synchronizeThread = new Thread(() -> {
            while (isWorking) {
                synchronizeProcess();
            }
        });
        synchronizeThread.setName("Tmk-" + TAG + "-AecSynchronize");
        synchronizeThread.start();

        // 测试
//        if (true){ unittest(); return; }

        int bufferSize = nSizeInBuffer;
        int samplesPerPacket = nSizeInBuffer;

        short[] micBuffer = new short[nSizeInBuffer];
        short[] bufferInChannel1 = new short[nSampleDoaInPerChannel];
        short[] bufferInChannel2 = new short[nSampleDoaInPerChannel];
        AudioRecord audioRecord = new AudioRecord(defaultAudioSource, defaultSampleRateInHz, StereoChannel, defaultAudioFormat, nSizeInBuffer);
        if (ContextCompat.checkSelfPermission(context, Manifest.permission.RECORD_AUDIO) !=
                PackageManager.PERMISSION_GRANTED ||
                audioRecord == null ||
                audioRecord.getState() != AudioRecord.STATE_INITIALIZED) {
            AiSpeechLogUtil.e(TAG, "mic Record 初始化失败");
            return;
        }

        Thread recordThread = new Thread(() -> {
            stopSema = new Semaphore(0);
            while (isWorking) {
                int bufferReadResult = audioRecord.read(micBuffer, 0, micBuffer.length);
                if (bufferReadResult < micBuffer.length) {
                    AiSpeechLogUtil.e(TAG, "读取大小错误: " + bufferReadResult);
                    break;
                }
                if (!micIsRecording) {
                    AiSpeechLogUtil.d(TAG, "mic 开始录音11 处理数据: " + bufferReadResult);
                    micIsRecording = true;
                }
                boolean micSpeakerIsRecording = micIsRecording && speakerIsRecording;
                if (!micSpeakerIsRecording) continue; // 等待 mic 和 speaker 录音都已经起来
//                AiSpeechLogUtil.d(TAG, name + " 开始录音11 同时处理数据");

                writed_count++;
                if (paused) {
                    if (writed_count % 100 == 0)  AiSpeechLogUtil.d(TAG, "暂停中 " + (writed_count * samplesPerPacket / 16000.0 * 1000) + " ms");
                    continue;
                }

                processMicRecordData(micBuffer);
            }

            audioRecord.release();
            AiSpeechLogUtil.e(TAG, name + " 录音已停止");

            if (stopSema != null) stopSema.release();
        });

        // 录取喇叭的声音, REMOTE_SUBMIX 依赖系统应用的权限
        short[] speakerBuffer = new short[micBuffer.length];
        AudioRecord speakerRecord = new AudioRecord(MediaRecorder.AudioSource.REMOTE_SUBMIX, defaultSampleRateInHz, StereoChannel,
                defaultAudioFormat, bufferSize);
        if (ContextCompat.checkSelfPermission(context, Manifest.permission.RECORD_AUDIO) !=
                PackageManager.PERMISSION_GRANTED ||
                speakerRecord.getState() != AudioRecord.STATE_INITIALIZED) {
            AiSpeechLogUtil.e(TAG, "speaker Record 初始化失败");
            return;
        }
//        MediaSyncEvent mse = MediaSyncEvent.createEvent(MediaSyncEvent.SYNC_EVENT_PRESENTATION_COMPLETE);
//        mse.setAudioSessionId(audioRecord.getAudioSessionId());
        speakerRecord.startRecording();
        audioRecord.startRecording(); // 若 mic 被占用会出现报错: AudioRecord: start() status -38, mic被占用是因为 headset录音还未停止
        Thread speakerRecordThread = new Thread(() -> {
            while (isWorking) {
                int bufferReadResult = speakerRecord.read(speakerBuffer, 0, speakerBuffer.length);
                if (bufferReadResult < speakerBuffer.length) {
                    AiSpeechLogUtil.e(TAG, "读取大小错误: " + bufferReadResult);
                    break;
                }
                if (!speakerIsRecording) {
                    AiSpeechLogUtil.d(TAG, "speaker 开始录音11 处理数据: " + bufferReadResult);
                    speakerIsRecording = true;
                }
                boolean micSpeakerIsRecording = micIsRecording && speakerIsRecording;
                if (!micSpeakerIsRecording) continue; // 等待 mic 和 speaker 录音都已经起来
//                AiSpeechLogUtil.d(TAG, name + " speaker 开始录音11 同时处理数据");

                if (paused) {
                    if (writed_count % 100 == 0)  AiSpeechLogUtil.d(TAG, "暂停中 " + (writed_count * samplesPerPacket / 16000.0 * 1000) + " ms");
                    continue;
                }
                processSpeakerRecordData(speakerBuffer);
            }
            Log.d(TAG, "start: 退出了 speaker 录音");
        });

        speakerRecordThread.setPriority(Thread.MAX_PRIORITY);
        speakerRecordThread.setName("Tmk-" + TAG + "-Speaker");
        speakerRecordThread.start();
        recordThread.setPriority(Thread.MAX_PRIORITY);
        recordThread.setName("Tmk-" + TAG);
        recordThread.start();
    }
    boolean micIsRecording = false;
    boolean speakerIsRecording = false;
    boolean speakerIsPlaying = false;

    @Override
    public void stop() {
        super.stop();
        AiSpeechLogUtil.e(TAG, "stop");
        try {
            if (stopSema != null) stopSema.acquire();
            stopSema = null;
        } catch (InterruptedException e) {
            e.printStackTrace();
        }
        removeAllChannel();
    }

    @Override
    public void stop2() {
        super.stop2();
        AiSpeechLogUtil.e(TAG, "stop");
        try {
            if (stopSema != null) stopSema.acquire();
            stopSema = null;
        } catch (InterruptedException e) {
            e.printStackTrace();
        }
//        removeAllPipe();
    }
}
