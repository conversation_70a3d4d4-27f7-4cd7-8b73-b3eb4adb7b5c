package co.timekettle.speech.ispeech.algorithm;

import android.util.Log;

import java.util.ArrayList;
import java.util.List;

public class AudioDoaJni {
   static {
      System.loadLibrary("tmk_doa");
   }


   /**
    * 检测的通道标识
    */
   public enum Channel {
      First,
      Second,
      None;
   }

   private final long doa;
   public AudioDoaJni() {
      doa = createDoa(0.01f);
   }

   List<Integer> tags = new ArrayList<Integer>();
   public Channel processDoa(short[] input) {
      int[] dir = { 0 };
      processDoa(doa, input, dir);
      if (tags.size() > (500 / 32)) {
         tags.remove(0);
      }
      tags.add(dir[0]);
      if (dir[0] == -1) return Channel.First;
      else if (dir[0] == 1) return Channel.Second;
      else return Channel.None;
   }

   @Override
   protected void finalize() throws Throwable {
      super.finalize();

      if (doa != 0) destoryDoa(doa);
   }

   native long createDoa(float fDoaTh);
   // dir 方向, '1'表示通道1, '-1'表示通道2
   native void processDoa(long doa, short[] input, int[] dir);
   native void destoryDoa(long doa);
}
