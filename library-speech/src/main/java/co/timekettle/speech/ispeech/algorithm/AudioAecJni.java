package co.timekettle.speech.ispeech.algorithm;

import java.util.Arrays;

public class AudioAecJni {
    static {
        System.loadLibrary("tmk_aec");
    }

    public static int SamplesInMode = 128;
    private final long aec;
    float[] mic;
    float[] speaker;
    public AudioAecJni() {
        aec = createAec();
    }

    @Override
    protected void finalize() throws Throwable {
        super.finalize();

        if (aec != 0) destoryAec(aec);
    }

    public short[] process(short[] recoderPcm, short[] speakerPcm) {
        assert recoderPcm.length == 128 && speakerPcm.length == 128 : "recoderPcm 或 speakerPcm 长度不为 128";
        if (mic == null) {
            mic = new float[recoderPcm.length];
            speaker = new float[speakerPcm.length];
        }
        for (int i = 0; i < recoderPcm.length; i++) {
            mic[i] = recoderPcm[i];
        }
        for (int i = 0; i < speakerPcm.length; i++) {
            speaker[i] = speakerPcm[i];
        }
        processAec(aec, mic, speaker);
        short[] output = Arrays.copyOf(recoderPcm, recoderPcm.length);
        for (int i = 0; i < mic.length; i++) {
            output[i] = (short) mic[i];
        }
        return output;
    }

    public void reset() {
        resetAec(aec);
    }

    native long createAec();
    native void processAec(long aec, float[] recoderPcm, float[] speakerPcm);
    native void resetAec(long aec);
    native void destoryAec(long aec);
}
