package co.timekettle.speech.ispeech.algorithm;

import android.content.Context;
import android.content.res.AssetFileDescriptor;
import android.content.res.AssetManager;
import android.util.Log;

import org.tensorflow.lite.Interpreter;
import org.tensorflow.lite.Tensor;

import java.io.File;
import java.io.FileInputStream;
import java.io.FileNotFoundException;
import java.io.FileOutputStream;
import java.io.IOException;
import java.nio.MappedByteBuffer;
import java.nio.channels.FileChannel;
import java.util.HashMap;
import java.util.Map;

public class TfLiteProcessor {
    private static final String TAG = TfLiteProcessor.class.getSimpleName();
    final Context mContext;
    Interpreter interpreter0;
    Interpreter interpreter1;
    public static int SamplesInMode = 512;
    public TfLiteProcessor(Context context) {
        mContext = context;

        try {
            final Interpreter.Options tfLiteOptions = new Interpreter.Options();

            // load mode 1
            MappedByteBuffer mode0 = TfLiteProcessor.loadModelFile(context.getAssets(), "model/model005_1.tflite");
            interpreter0 = new Interpreter(mode0, tfLiteOptions);

            // load mode 2
            MappedByteBuffer mode1 = TfLiteProcessor.loadModelFile(context.getAssets(), "model/model005_2.tflite");
            interpreter1 = new Interpreter(mode1, tfLiteOptions);
        } catch (IOException e) {
            e.printStackTrace();
        }
    }

    public static TfLiteProcessor Factory(Context context) {
        TfLiteProcessor tflitePro = new TfLiteProcessor(context);
        return tflitePro;
    }

    public byte[] getBytes(short s) {
        byte[] buf = new byte[2];
        for (int i = 0; i < buf.length; i++) {
            buf[i] = (byte) (s & 0x00ff);
            s >>= 8;
        }
        return buf;
    }

    /* 从下标 0 开始处理长度为 length 的数据 */
    public short[] Floats2Shorts(float[] buf, int length) {
        short[] s = new short[length];
        for (int i = 0; i < s.length; i++) {
            s[i] = (short) (buf[i] * Short.MAX_VALUE);
        }
        return s;
    }

    public byte[] Floats2Bytes(float[] s, int length) {
        byte bLength = 2;
        byte[] buf = new byte[length * bLength];
        for (int iLoop = 0; iLoop < length; iLoop++) {
            byte[] temp = getBytes((short) (s[iLoop] * 32768));
            for (int jLoop = 0; jLoop < bLength; jLoop++) {
                buf[iLoop * bLength + jLoop] = temp[jLoop];
            }
        }
        return buf;
    }

    public byte[] Doubles2Bytes(double[] s) {
        byte bLength = 2;
        byte[] buf = new byte[s.length * bLength];
        for (int iLoop = 0; iLoop < s.length; iLoop++) {
            byte[] temp = getBytes((short) (s[iLoop] * 32768));
            for (int jLoop = 0; jLoop < bLength; jLoop++) {
                buf[iLoop * bLength + jLoop] = temp[jLoop];
            }
        }
        return buf;
    }

    public double[] Bytes2Doubles(byte[] buf) {
        byte nBytesPerSample = 2;
        double[] s = new double[buf.length / nBytesPerSample];
        for (int i = 0; i < s.length; i++) {
            short value = 0; // 每个采样点的值
            for (int j = 0; j < nBytesPerSample; j++) {
                value |= ((long) (buf[i * nBytesPerSample + j] & 0xff)) << (8 * j);
            }
            s[i] = value / 32768.0;
        }
        return s;
    }

    public double[] Shorts2Doubles(short[] buf) {
        double[] s = new double[buf.length];
        for (int i = 0; i < s.length; i++) {
            s[i] = buf[i] / 32768.0;
        }
        return s;
    }

    public void reset() {
        output_overlap = new float[SamplesInMode];
        states0 = new float[1][2][256][2];
        states1 = new float[1][2][256][2];
    }

    // 计算耗时
    long averageCost = 0;
    long maxCost = 0;
    long minCost = Long.MAX_VALUE;
    int process_count = 0;
    float[] output_overlap = new float[SamplesInMode];
    public short[] process(short[] recoderPcm0, short[] speakerPcm0) {
        assert recoderPcm0.length == SamplesInMode && speakerPcm0.length == SamplesInMode : "recoderPcm, speakerPcm长度不等于 512";
        try {
            long begin = System.currentTimeMillis();
            // process mode 1
            double[] recoderPcm = Shorts2Doubles(recoderPcm0);
            double[] speakerPcm = Shorts2Doubles(speakerPcm0);
            float[] input = process0(interpreter0, recoderPcm, speakerPcm);

            // process mode 2
            float[] output = process1(interpreter1, input, speakerPcm);

            // overlap
            for (int j = 0; j < output_overlap.length; j++) {
                if (j + SamplesInMode / 4 < output_overlap.length) {
                    output_overlap[j] = output_overlap[j + SamplesInMode / 4];
                } else {
                    output_overlap[j] = 0;
                }
            }
            // add
            for (int j = 0; j < output_overlap.length; j++) {
                output_overlap[j] = output_overlap[j] + output[j];
            }

            // check error
//        for (int j = 0; j < output.length; j++) {
//            assertEquals(output[j], 0.0, 0.2);
//        }
            // 计算耗时
            long end = System.currentTimeMillis();
            long curCost = end - begin;
            if (curCost > maxCost) maxCost = curCost;
            if (curCost < minCost) minCost = curCost;
            averageCost = averageCost + curCost;
            process_count = process_count + 1;
            if (process_count % 100 == 0) Log.d(TAG, "处理 " + (process_count * SamplesInMode / 4 / 16000.0 * 1000) + "ms音频数据, 每 128 字节处理数据耗时(ms) average:" + (averageCost / process_count) + " max:" + maxCost + " min:" +  + minCost);
            return Floats2Shorts(output, output.length);
        } catch (IOException e) {
            e.printStackTrace();
            return null;
        }
    }

    public float[] process(double[] recoderPcm, double[] speakerPcm) throws IOException {
        assert recoderPcm.length == SamplesInMode && speakerPcm.length == SamplesInMode : "recoderPcm, speakerPcm长度不等于 512";
        // process mode 1
        float[] input = process0(interpreter0, recoderPcm, speakerPcm);

        // process mode 2
        float[] output = process1(interpreter1, input, speakerPcm);

        // check error
//        for (int j = 0; j < output.length; j++) {
//            assertEquals(output[j], 0.0, 0.2);
//        }
        return output;
    }

    public static class Data {
        public static final double[][] RecorderPcmIns = new double[][]{
                {0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000},
                {0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000},
                {0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000},
                {0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,-0.000916,-0.001617,-0.002319,-0.002014,-0.001801,-0.002136,-0.000671,0.000763,0.001740,0.001923,0.002441,0.000427,-0.000214,0.000275,0.000336,-0.000153,0.000702,0.002045,0.001465,0.000854,0.000366,-0.000427,-0.000549,-0.002686,-0.003265,-0.002991,-0.003204,-0.002045,-0.003235,-0.003906,-0.003082,-0.001953,0.001343,0.002960,0.003479,0.004852,0.003448,0.003143,0.003418,0.003998,0.006165,0.007812,0.007935,0.006195,0.003113,0.002930,0.002045,-0.000153,-0.001770,-0.003754,-0.004669,-0.003845,-0.002899,-0.001038,-0.000397,-0.002533,-0.004822,-0.004669,-0.003052,-0.001465,-0.001526,-0.001709,-0.001892,-0.000061,0.002686,0.004730,0.006531,0.005280,0.001740,0.000885,0.000580,0.000824,0.002441,0.002289,0.001404,-0.001129,-0.003723,-0.003723,-0.003448,-0.002930,-0.003937,-0.004364,-0.004822,-0.004578,-0.002625,-0.002045,-0.003174,-0.001984,-0.002136,-0.001221,-0.001129,-0.000549,-0.000854,-0.002136,-0.001312,-0.000458,0.000580,0.000519,-0.002014,-0.003204,-0.002075,-0.000732,0.001495,0.002563,-0.000122,-0.001709,-0.001862,-0.001404,-0.000275,0.000824,0.001556,0.001190,0.000671,0.000397,0.000610,0.001709,0.002136,0.001465,0.000854,0.001221,0.001312,0.001282,0.002441,0.002930,0.002014,0.001862,0.000275,-0.000916},
                {0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,-0.000916,-0.001617,-0.002319,-0.002014,-0.001801,-0.002136,-0.000671,0.000763,0.001740,0.001923,0.002441,0.000427,-0.000214,0.000275,0.000336,-0.000153,0.000702,0.002045,0.001465,0.000854,0.000366,-0.000427,-0.000549,-0.002686,-0.003265,-0.002991,-0.003204,-0.002045,-0.003235,-0.003906,-0.003082,-0.001953,0.001343,0.002960,0.003479,0.004852,0.003448,0.003143,0.003418,0.003998,0.006165,0.007812,0.007935,0.006195,0.003113,0.002930,0.002045,-0.000153,-0.001770,-0.003754,-0.004669,-0.003845,-0.002899,-0.001038,-0.000397,-0.002533,-0.004822,-0.004669,-0.003052,-0.001465,-0.001526,-0.001709,-0.001892,-0.000061,0.002686,0.004730,0.006531,0.005280,0.001740,0.000885,0.000580,0.000824,0.002441,0.002289,0.001404,-0.001129,-0.003723,-0.003723,-0.003448,-0.002930,-0.003937,-0.004364,-0.004822,-0.004578,-0.002625,-0.002045,-0.003174,-0.001984,-0.002136,-0.001221,-0.001129,-0.000549,-0.000854,-0.002136,-0.001312,-0.000458,0.000580,0.000519,-0.002014,-0.003204,-0.002075,-0.000732,0.001495,0.002563,-0.000122,-0.001709,-0.001862,-0.001404,-0.000275,0.000824,0.001556,0.001190,0.000671,0.000397,0.000610,0.001709,0.002136,0.001465,0.000854,0.001221,0.001312,0.001282,0.002441,0.002930,0.002014,0.001862,0.000275,-0.000916,0.000427,0.001526,0.002350,0.002380,0.001587,0.002075,0.001678,0.001740,0.002075,0.002411,0.002686,0.001129,0.000641,0.000244,0.000153,-0.000031,0.000885,0.000885,0.000000,0.000854,-0.000183,-0.000092,-0.000671,-0.002319,-0.002228,-0.002625,-0.002930,-0.004272,-0.004364,-0.003387,-0.003479,-0.003845,-0.003937,-0.004395,-0.004944,-0.004303,-0.003143,-0.002106,-0.001404,-0.001038,0.000183,0.000732,0.000427,0.001038,0.001404,0.001495,0.002289,0.002655,0.001404,0.000885,0.000092,-0.000183,0.000427,0.000244,0.000122,0.000732,0.000031,0.000031,-0.000122,-0.001587,-0.002472,-0.002747,-0.003174,-0.004333,-0.004089,-0.003052,-0.004486,-0.003662,-0.002136,-0.000122,0.000305,0.000977,0.001251,0.001190,0.000275,0.001526,0.001617,0.001434,0.000244,0.000000,0.001495,0.001587,0.001740,0.000732,0.000183,-0.000305,-0.000793,-0.001556,-0.002655,-0.001160,-0.000214,-0.000336,0.000061,0.001923,0.003235,0.002167,0.002472,0.003052,0.001892,0.001495,0.001282,0.001007,0.000336,-0.000732,-0.001007,-0.000061,0.000610,0.000824,-0.000366,-0.001312,-0.002106,-0.002075,-0.001190,-0.000458,0.000610,0.001007,0.002075,0.002350,0.001221,-0.000366,-0.002045,-0.002747,-0.002106,-0.001648,-0.000549,0.000854,0.001251,0.000031},
                {0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,-0.000916,-0.001617,-0.002319,-0.002014,-0.001801,-0.002136,-0.000671,0.000763,0.001740,0.001923,0.002441,0.000427,-0.000214,0.000275,0.000336,-0.000153,0.000702,0.002045,0.001465,0.000854,0.000366,-0.000427,-0.000549,-0.002686,-0.003265,-0.002991,-0.003204,-0.002045,-0.003235,-0.003906,-0.003082,-0.001953,0.001343,0.002960,0.003479,0.004852,0.003448,0.003143,0.003418,0.003998,0.006165,0.007812,0.007935,0.006195,0.003113,0.002930,0.002045,-0.000153,-0.001770,-0.003754,-0.004669,-0.003845,-0.002899,-0.001038,-0.000397,-0.002533,-0.004822,-0.004669,-0.003052,-0.001465,-0.001526,-0.001709,-0.001892,-0.000061,0.002686,0.004730,0.006531,0.005280,0.001740,0.000885,0.000580,0.000824,0.002441,0.002289,0.001404,-0.001129,-0.003723,-0.003723,-0.003448,-0.002930,-0.003937,-0.004364,-0.004822,-0.004578,-0.002625,-0.002045,-0.003174,-0.001984,-0.002136,-0.001221,-0.001129,-0.000549,-0.000854,-0.002136,-0.001312,-0.000458,0.000580,0.000519,-0.002014,-0.003204,-0.002075,-0.000732,0.001495,0.002563,-0.000122,-0.001709,-0.001862,-0.001404,-0.000275,0.000824,0.001556,0.001190,0.000671,0.000397,0.000610,0.001709,0.002136,0.001465,0.000854,0.001221,0.001312,0.001282,0.002441,0.002930,0.002014,0.001862,0.000275,-0.000916,0.000427,0.001526,0.002350,0.002380,0.001587,0.002075,0.001678,0.001740,0.002075,0.002411,0.002686,0.001129,0.000641,0.000244,0.000153,-0.000031,0.000885,0.000885,0.000000,0.000854,-0.000183,-0.000092,-0.000671,-0.002319,-0.002228,-0.002625,-0.002930,-0.004272,-0.004364,-0.003387,-0.003479,-0.003845,-0.003937,-0.004395,-0.004944,-0.004303,-0.003143,-0.002106,-0.001404,-0.001038,0.000183,0.000732,0.000427,0.001038,0.001404,0.001495,0.002289,0.002655,0.001404,0.000885,0.000092,-0.000183,0.000427,0.000244,0.000122,0.000732,0.000031,0.000031,-0.000122,-0.001587,-0.002472,-0.002747,-0.003174,-0.004333,-0.004089,-0.003052,-0.004486,-0.003662,-0.002136,-0.000122,0.000305,0.000977,0.001251,0.001190,0.000275,0.001526,0.001617,0.001434,0.000244,0.000000,0.001495,0.001587,0.001740,0.000732,0.000183,-0.000305,-0.000793,-0.001556,-0.002655,-0.001160,-0.000214,-0.000336,0.000061,0.001923,0.003235,0.002167,0.002472,0.003052,0.001892,0.001495,0.001282,0.001007,0.000336,-0.000732,-0.001007,-0.000061,0.000610,0.000824,-0.000366,-0.001312,-0.002106,-0.002075,-0.001190,-0.000458,0.000610,0.001007,0.002075,0.002350,0.001221,-0.000366,-0.002045,-0.002747,-0.002106,-0.001648,-0.000549,0.000854,0.001251,0.000031,-0.001556,-0.002380,-0.002319,-0.002716,-0.003021,-0.003479,-0.003204,-0.004089,-0.003662,-0.002625,-0.001587,0.000122,0.001831,0.000946,0.002441,0.002106,0.002228,0.002625,0.003967,0.003906,0.003021,0.002594,0.001587,0.000641,0.001587,0.001465,0.001343,0.002441,0.002075,0.002472,0.004150,0.004425,0.004028,0.003693,0.002991,0.003448,0.002747,0.002930,0.002777,0.003448,0.004028,0.003723,0.003296,0.003235,0.001862,0.001068,0.000336,0.000458,0.001862,0.001984,0.000671,0.000458,0.000763,-0.001251,-0.001495,-0.001831,-0.001129,-0.002045,-0.002167,-0.002960,-0.004028,-0.004089,-0.004120,-0.003143,-0.001892,-0.000092,0.001373,0.001862,0.001495,0.002350,0.002258,0.002350,0.001648,0.000977,0.001678,0.001404,0.000427,-0.000153,-0.000610,-0.000854,-0.001099,-0.001251,-0.003082,-0.003571,-0.002594,-0.003601,-0.003998,-0.004974,-0.005219,-0.004761,-0.003204,-0.002563,-0.001678,-0.000183,0.000549,0.000397,0.000366,-0.000092,0.000000,0.000519,0.000793,0.002106,0.003418,0.004456,0.003723,0.001587,0.000092,-0.000854,-0.001160,-0.000549,-0.001190,-0.000977,-0.002136,-0.002014,-0.001801,-0.002136,-0.001526,-0.001892,-0.001190,-0.002136,-0.002533,-0.001709,-0.001282,0.000549,0.001099,0.003510,0.004822,0.004150},
//            {-0.000916,-0.001617,-0.002319,-0.002014,-0.001801,-0.002136,-0.000671,0.000763,0.001740,0.001923,0.002441,0.000427,-0.000214,0.000275,0.000336,-0.000153,0.000702,0.002045,0.001465,0.000854,0.000366,-0.000427,-0.000549,-0.002686,-0.003265,-0.002991,-0.003204,-0.002045,-0.003235,-0.003906,-0.003082,-0.001953,0.001343,0.002960,0.003479,0.004852,0.003448,0.003143,0.003418,0.003998,0.006165,0.007812,0.007935,0.006195,0.003113,0.002930,0.002045,-0.000153,-0.001770,-0.003754,-0.004669,-0.003845,-0.002899,-0.001038,-0.000397,-0.002533,-0.004822,-0.004669,-0.003052,-0.001465,-0.001526,-0.001709,-0.001892,-0.000061,0.002686,0.004730,0.006531,0.005280,0.001740,0.000885,0.000580,0.000824,0.002441,0.002289,0.001404,-0.001129,-0.003723,-0.003723,-0.003448,-0.002930,-0.003937,-0.004364,-0.004822,-0.004578,-0.002625,-0.002045,-0.003174,-0.001984,-0.002136,-0.001221,-0.001129,-0.000549,-0.000854,-0.002136,-0.001312,-0.000458,0.000580,0.000519,-0.002014,-0.003204,-0.002075,-0.000732,0.001495,0.002563,-0.000122,-0.001709,-0.001862,-0.001404,-0.000275,0.000824,0.001556,0.001190,0.000671,0.000397,0.000610,0.001709,0.002136,0.001465,0.000854,0.001221,0.001312,0.001282,0.002441,0.002930,0.002014,0.001862,0.000275,-0.000916,0.000427,0.001526,0.002350,0.002380,0.001587,0.002075,0.001678,0.001740,0.002075,0.002411,0.002686,0.001129,0.000641,0.000244,0.000153,-0.000031,0.000885,0.000885,0.000000,0.000854,-0.000183,-0.000092,-0.000671,-0.002319,-0.002228,-0.002625,-0.002930,-0.004272,-0.004364,-0.003387,-0.003479,-0.003845,-0.003937,-0.004395,-0.004944,-0.004303,-0.003143,-0.002106,-0.001404,-0.001038,0.000183,0.000732,0.000427,0.001038,0.001404,0.001495,0.002289,0.002655,0.001404,0.000885,0.000092,-0.000183,0.000427,0.000244,0.000122,0.000732,0.000031,0.000031,-0.000122,-0.001587,-0.002472,-0.002747,-0.003174,-0.004333,-0.004089,-0.003052,-0.004486,-0.003662,-0.002136,-0.000122,0.000305,0.000977,0.001251,0.001190,0.000275,0.001526,0.001617,0.001434,0.000244,0.000000,0.001495,0.001587,0.001740,0.000732,0.000183,-0.000305,-0.000793,-0.001556,-0.002655,-0.001160,-0.000214,-0.000336,0.000061,0.001923,0.003235,0.002167,0.002472,0.003052,0.001892,0.001495,0.001282,0.001007,0.000336,-0.000732,-0.001007,-0.000061,0.000610,0.000824,-0.000366,-0.001312,-0.002106,-0.002075,-0.001190,-0.000458,0.000610,0.001007,0.002075,0.002350,0.001221,-0.000366,-0.002045,-0.002747,-0.002106,-0.001648,-0.000549,0.000854,0.001251,0.000031,-0.001556,-0.002380,-0.002319,-0.002716,-0.003021,-0.003479,-0.003204,-0.004089,-0.003662,-0.002625,-0.001587,0.000122,0.001831,0.000946,0.002441,0.002106,0.002228,0.002625,0.003967,0.003906,0.003021,0.002594,0.001587,0.000641,0.001587,0.001465,0.001343,0.002441,0.002075,0.002472,0.004150,0.004425,0.004028,0.003693,0.002991,0.003448,0.002747,0.002930,0.002777,0.003448,0.004028,0.003723,0.003296,0.003235,0.001862,0.001068,0.000336,0.000458,0.001862,0.001984,0.000671,0.000458,0.000763,-0.001251,-0.001495,-0.001831,-0.001129,-0.002045,-0.002167,-0.002960,-0.004028,-0.004089,-0.004120,-0.003143,-0.001892,-0.000092,0.001373,0.001862,0.001495,0.002350,0.002258,0.002350,0.001648,0.000977,0.001678,0.001404,0.000427,-0.000153,-0.000610,-0.000854,-0.001099,-0.001251,-0.003082,-0.003571,-0.002594,-0.003601,-0.003998,-0.004974,-0.005219,-0.004761,-0.003204,-0.002563,-0.001678,-0.000183,0.000549,0.000397,0.000366,-0.000092,0.000000,0.000519,0.000793,0.002106,0.003418,0.004456,0.003723,0.001587,0.000092,-0.000854,-0.001160,-0.000549,-0.001190,-0.000977,-0.002136,-0.002014,-0.001801,-0.002136,-0.001526,-0.001892,-0.001190,-0.002136,-0.002533,-0.001709,-0.001282,0.000549,0.001099,0.003510,0.004822,0.004150,0.003052,0.001770,0.000824,0.000031,-0.001465,-0.001312,-0.000305,0.001343,0.001770,0.001801,0.001648,0.001526,-0.000061,0.000275,0.000031,0.000153,-0.000275,0.000977,0.002014,0.000824,0.002441,0.001831,0.002380,0.002899,0.002686,0.002380,0.001984,0.002655,0.001434,0.001038,0.001007,0.000824,0.000580,0.001038,0.001007,0.001099,0.001434,-0.001343,-0.003296,-0.003113,-0.003174,-0.002991,-0.002045,-0.000916,-0.001495,-0.001862,-0.001587,-0.002289,-0.002533,-0.000977,-0.001953,-0.001373,-0.001892,-0.001740,0.000092,0.000946,0.001740,0.001007,0.001465,0.002655,0.003448,0.003937,0.004517,0.004822,0.003967,0.002533,0.003021,0.003204,0.001312,0.001007,0.001709,0.001343,0.001190,0.000824,0.000427,0.000702,-0.000275,-0.001007,-0.001862,-0.000702,-0.000671,-0.001465,-0.001373,0.000641,0.000977,0.000916,0.000610,0.000275,-0.000458,0.000244,0.000519,-0.000031,-0.000122,-0.001617,-0.002136,-0.001556,-0.001495,-0.001526,-0.002808,-0.003784,-0.004395,-0.003448,-0.003235,-0.002167,-0.002808,-0.001831,-0.001373,-0.002136,-0.002167,-0.002106,-0.002563,-0.001465,-0.001770,-0.002655,-0.001862,-0.001801,-0.002899,-0.003357,-0.002533,-0.001282,-0.000671,-0.000183,0.000793,0.000671,-0.000183,-0.001221,-0.001190,-0.001343},
//            {0.000427,0.001526,0.002350,0.002380,0.001587,0.002075,0.001678,0.001740,0.002075,0.002411,0.002686,0.001129,0.000641,0.000244,0.000153,-0.000031,0.000885,0.000885,0.000000,0.000854,-0.000183,-0.000092,-0.000671,-0.002319,-0.002228,-0.002625,-0.002930,-0.004272,-0.004364,-0.003387,-0.003479,-0.003845,-0.003937,-0.004395,-0.004944,-0.004303,-0.003143,-0.002106,-0.001404,-0.001038,0.000183,0.000732,0.000427,0.001038,0.001404,0.001495,0.002289,0.002655,0.001404,0.000885,0.000092,-0.000183,0.000427,0.000244,0.000122,0.000732,0.000031,0.000031,-0.000122,-0.001587,-0.002472,-0.002747,-0.003174,-0.004333,-0.004089,-0.003052,-0.004486,-0.003662,-0.002136,-0.000122,0.000305,0.000977,0.001251,0.001190,0.000275,0.001526,0.001617,0.001434,0.000244,0.000000,0.001495,0.001587,0.001740,0.000732,0.000183,-0.000305,-0.000793,-0.001556,-0.002655,-0.001160,-0.000214,-0.000336,0.000061,0.001923,0.003235,0.002167,0.002472,0.003052,0.001892,0.001495,0.001282,0.001007,0.000336,-0.000732,-0.001007,-0.000061,0.000610,0.000824,-0.000366,-0.001312,-0.002106,-0.002075,-0.001190,-0.000458,0.000610,0.001007,0.002075,0.002350,0.001221,-0.000366,-0.002045,-0.002747,-0.002106,-0.001648,-0.000549,0.000854,0.001251,0.000031,-0.001556,-0.002380,-0.002319,-0.002716,-0.003021,-0.003479,-0.003204,-0.004089,-0.003662,-0.002625,-0.001587,0.000122,0.001831,0.000946,0.002441,0.002106,0.002228,0.002625,0.003967,0.003906,0.003021,0.002594,0.001587,0.000641,0.001587,0.001465,0.001343,0.002441,0.002075,0.002472,0.004150,0.004425,0.004028,0.003693,0.002991,0.003448,0.002747,0.002930,0.002777,0.003448,0.004028,0.003723,0.003296,0.003235,0.001862,0.001068,0.000336,0.000458,0.001862,0.001984,0.000671,0.000458,0.000763,-0.001251,-0.001495,-0.001831,-0.001129,-0.002045,-0.002167,-0.002960,-0.004028,-0.004089,-0.004120,-0.003143,-0.001892,-0.000092,0.001373,0.001862,0.001495,0.002350,0.002258,0.002350,0.001648,0.000977,0.001678,0.001404,0.000427,-0.000153,-0.000610,-0.000854,-0.001099,-0.001251,-0.003082,-0.003571,-0.002594,-0.003601,-0.003998,-0.004974,-0.005219,-0.004761,-0.003204,-0.002563,-0.001678,-0.000183,0.000549,0.000397,0.000366,-0.000092,0.000000,0.000519,0.000793,0.002106,0.003418,0.004456,0.003723,0.001587,0.000092,-0.000854,-0.001160,-0.000549,-0.001190,-0.000977,-0.002136,-0.002014,-0.001801,-0.002136,-0.001526,-0.001892,-0.001190,-0.002136,-0.002533,-0.001709,-0.001282,0.000549,0.001099,0.003510,0.004822,0.004150,0.003052,0.001770,0.000824,0.000031,-0.001465,-0.001312,-0.000305,0.001343,0.001770,0.001801,0.001648,0.001526,-0.000061,0.000275,0.000031,0.000153,-0.000275,0.000977,0.002014,0.000824,0.002441,0.001831,0.002380,0.002899,0.002686,0.002380,0.001984,0.002655,0.001434,0.001038,0.001007,0.000824,0.000580,0.001038,0.001007,0.001099,0.001434,-0.001343,-0.003296,-0.003113,-0.003174,-0.002991,-0.002045,-0.000916,-0.001495,-0.001862,-0.001587,-0.002289,-0.002533,-0.000977,-0.001953,-0.001373,-0.001892,-0.001740,0.000092,0.000946,0.001740,0.001007,0.001465,0.002655,0.003448,0.003937,0.004517,0.004822,0.003967,0.002533,0.003021,0.003204,0.001312,0.001007,0.001709,0.001343,0.001190,0.000824,0.000427,0.000702,-0.000275,-0.001007,-0.001862,-0.000702,-0.000671,-0.001465,-0.001373,0.000641,0.000977,0.000916,0.000610,0.000275,-0.000458,0.000244,0.000519,-0.000031,-0.000122,-0.001617,-0.002136,-0.001556,-0.001495,-0.001526,-0.002808,-0.003784,-0.004395,-0.003448,-0.003235,-0.002167,-0.002808,-0.001831,-0.001373,-0.002136,-0.002167,-0.002106,-0.002563,-0.001465,-0.001770,-0.002655,-0.001862,-0.001801,-0.002899,-0.003357,-0.002533,-0.001282,-0.000671,-0.000183,0.000793,0.000671,-0.000183,-0.001221,-0.001190,-0.001343,-0.000244,0.001221,0.000122,0.001160,0.002106,0.002258,0.001312,0.000702,0.001251,0.002350,0.002258,0.002075,0.002075,0.003296,0.003754,0.002869,0.001923,0.003052,0.002563,0.002747,0.003082,0.002563,0.001312,0.000763,0.001099,0.001617,0.003082,0.003815,0.004150,0.004669,0.004517,0.004547,0.002655,0.001740,0.000549,-0.000153,-0.001190,-0.000092,0.000854,0.000000,0.000397,0.000092,0.000916,0.002075,0.002319,0.003204,0.003235,0.002167,0.001587,0.001984,0.002228,0.002289,0.002167,0.001404,0.000702,-0.001709,-0.003021,-0.003296,-0.004211,-0.003693,-0.004120,-0.004272,-0.002533,-0.001404,-0.001251,-0.000793,0.000305,-0.000305,-0.001709,-0.000824,0.000336,0.001495,0.001740,0.001740,0.000671,0.000397,-0.000946,-0.001312,-0.000916,-0.000763,-0.001038,-0.001129,-0.001373,-0.001831,-0.002747,-0.002686,-0.002380,-0.001526,-0.001068,-0.000885,-0.000031,-0.000488,-0.000580,-0.000397,-0.002472,-0.002411,-0.002075,-0.002075,-0.001617,-0.001221,-0.000305,0.000305,0.000732,0.001129,0.002289,0.002197,0.003815,0.004211,0.004639,0.004486,0.004639,0.003601,0.002289,0.001923,0.002197,0.001984,0.001038,0.000061,-0.000793,-0.000122,0.000458,0.001099,0.000549,0.000122,-0.000214,-0.000305,-0.000641,-0.000763},
        };

        public static final double[][] SpeakerPcmIns = new double[][]{
                {0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000},
                {0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000},
                {0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000},
                {0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,-0.000031,0.000000,-0.000031,0.000000,-0.000031,0.000000,-0.000031,0.000000,0.000000,-0.000031,0.000000,-0.000031,-0.000031,0.000000,-0.000031,0.000000,-0.000031,0.000000,-0.000031,0.000000,0.000000,0.000000,-0.000031,0.000000,0.000000,0.000000,0.000000,-0.000031,0.000000,-0.000031,0.000000,-0.000031,0.000000,0.000000,-0.000031,0.000000,-0.000031,0.000000,0.000000,0.000000,-0.000031,0.000000,-0.000031,-0.000031,-0.000031,0.000000,0.000000,-0.000031,0.000000,0.000000,-0.000031,0.000000,-0.000031,0.000000,0.000000,-0.000031,0.000000,-0.000031,0.000000,-0.000031,-0.000031,-0.000031,0.000000,-0.000031,0.000000,-0.000031,0.000000,-0.000031,0.000000,-0.000031,0.000000,-0.000031,0.000000,-0.000031,-0.000031,0.000000,-0.000031,0.000000,-0.000031,0.000000,-0.000031,0.000000,-0.000031,0.000000,-0.000031,0.000000,-0.000031,-0.000031,-0.000031,-0.000031,0.000000,-0.000031,0.000000,-0.000031,0.000000,-0.000031,0.000000,-0.000031,0.000000,0.000000,0.000000,0.000000,-0.000031,0.000000,-0.000031,0.000000,0.000000,-0.000031,0.000000,-0.000031,0.000000,-0.000031,0.000000,-0.000031,0.000000,0.000000,-0.000031,-0.000031,0.000000,-0.000031,-0.000031,0.000000,-0.000031,0.000000,-0.000031,0.000000,-0.000031,0.000000},
                {0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,-0.000031,0.000000,-0.000031,0.000000,-0.000031,0.000000,-0.000031,0.000000,0.000000,-0.000031,0.000000,-0.000031,-0.000031,0.000000,-0.000031,0.000000,-0.000031,0.000000,-0.000031,0.000000,0.000000,0.000000,-0.000031,0.000000,0.000000,0.000000,0.000000,-0.000031,0.000000,-0.000031,0.000000,-0.000031,0.000000,0.000000,-0.000031,0.000000,-0.000031,0.000000,0.000000,0.000000,-0.000031,0.000000,-0.000031,-0.000031,-0.000031,0.000000,0.000000,-0.000031,0.000000,0.000000,-0.000031,0.000000,-0.000031,0.000000,0.000000,-0.000031,0.000000,-0.000031,0.000000,-0.000031,-0.000031,-0.000031,0.000000,-0.000031,0.000000,-0.000031,0.000000,-0.000031,0.000000,-0.000031,0.000000,-0.000031,0.000000,-0.000031,-0.000031,0.000000,-0.000031,0.000000,-0.000031,0.000000,-0.000031,0.000000,-0.000031,0.000000,-0.000031,0.000000,-0.000031,-0.000031,-0.000031,-0.000031,0.000000,-0.000031,0.000000,-0.000031,0.000000,-0.000031,0.000000,-0.000031,0.000000,0.000000,0.000000,0.000000,-0.000031,0.000000,-0.000031,0.000000,0.000000,-0.000031,0.000000,-0.000031,0.000000,-0.000031,0.000000,-0.000031,0.000000,0.000000,-0.000031,-0.000031,0.000000,-0.000031,-0.000031,0.000000,-0.000031,0.000000,-0.000031,0.000000,-0.000031,0.000000,0.000000,-0.000031,-0.000031,-0.000031,-0.000031,0.000000,-0.000031,0.000000,0.000000,-0.000031,0.000000,0.000000,-0.000031,0.000000,-0.000031,0.000000,-0.000031,0.000000,0.000000,0.000000,0.000000,-0.000031,0.000000,0.000000,0.000000,-0.000031,0.000000,0.000000,-0.000031,0.000000,-0.000031,0.000000,-0.000031,0.000000,0.000000,-0.000031,0.000000,-0.000031,0.000000,-0.000031,0.000000,0.000000,-0.000031,0.000000,-0.000031,-0.000031,-0.000031,-0.000031,-0.000031,-0.000031,0.000000,-0.000031,0.000000,-0.000031,-0.000031,0.000000,-0.000031,0.000000,-0.000031,0.000000,-0.000031,-0.000031,0.000000,0.000000,-0.000031,0.000000,-0.000031,0.000000,-0.000031,-0.000031,0.000000,0.000000,-0.000031,-0.000031,-0.000031,0.000000,-0.000031,0.000000,-0.000031,0.000000,-0.000031,0.000000,-0.000031,0.000000,-0.000031,0.000000,0.000000,-0.000031,0.000000,-0.000031,0.000000,0.000000,0.000000,-0.000031,0.000000,0.000000,0.000000,-0.000031,0.000000,-0.000031,0.000000,-0.000031,0.000000,-0.000031,0.000000,-0.000031,0.000000,-0.000031,0.000000,-0.000031,-0.000031,0.000000,-0.000031,0.000000,-0.000031,-0.000031,0.000000,-0.000031,0.000000,-0.000031,0.000000,0.000000,-0.000031,0.000000,-0.000031,0.000000,-0.000031,-0.000031},
                {0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,-0.000031,0.000000,-0.000031,0.000000,-0.000031,0.000000,-0.000031,0.000000,0.000000,-0.000031,0.000000,-0.000031,-0.000031,0.000000,-0.000031,0.000000,-0.000031,0.000000,-0.000031,0.000000,0.000000,0.000000,-0.000031,0.000000,0.000000,0.000000,0.000000,-0.000031,0.000000,-0.000031,0.000000,-0.000031,0.000000,0.000000,-0.000031,0.000000,-0.000031,0.000000,0.000000,0.000000,-0.000031,0.000000,-0.000031,-0.000031,-0.000031,0.000000,0.000000,-0.000031,0.000000,0.000000,-0.000031,0.000000,-0.000031,0.000000,0.000000,-0.000031,0.000000,-0.000031,0.000000,-0.000031,-0.000031,-0.000031,0.000000,-0.000031,0.000000,-0.000031,0.000000,-0.000031,0.000000,-0.000031,0.000000,-0.000031,0.000000,-0.000031,-0.000031,0.000000,-0.000031,0.000000,-0.000031,0.000000,-0.000031,0.000000,-0.000031,0.000000,-0.000031,0.000000,-0.000031,-0.000031,-0.000031,-0.000031,0.000000,-0.000031,0.000000,-0.000031,0.000000,-0.000031,0.000000,-0.000031,0.000000,0.000000,0.000000,0.000000,-0.000031,0.000000,-0.000031,0.000000,0.000000,-0.000031,0.000000,-0.000031,0.000000,-0.000031,0.000000,-0.000031,0.000000,0.000000,-0.000031,-0.000031,0.000000,-0.000031,-0.000031,0.000000,-0.000031,0.000000,-0.000031,0.000000,-0.000031,0.000000,0.000000,-0.000031,-0.000031,-0.000031,-0.000031,0.000000,-0.000031,0.000000,0.000000,-0.000031,0.000000,0.000000,-0.000031,0.000000,-0.000031,0.000000,-0.000031,0.000000,0.000000,0.000000,0.000000,-0.000031,0.000000,0.000000,0.000000,-0.000031,0.000000,0.000000,-0.000031,0.000000,-0.000031,0.000000,-0.000031,0.000000,0.000000,-0.000031,0.000000,-0.000031,0.000000,-0.000031,0.000000,0.000000,-0.000031,0.000000,-0.000031,-0.000031,-0.000031,-0.000031,-0.000031,-0.000031,0.000000,-0.000031,0.000000,-0.000031,-0.000031,0.000000,-0.000031,0.000000,-0.000031,0.000000,-0.000031,-0.000031,0.000000,0.000000,-0.000031,0.000000,-0.000031,0.000000,-0.000031,-0.000031,0.000000,0.000000,-0.000031,-0.000031,-0.000031,0.000000,-0.000031,0.000000,-0.000031,0.000000,-0.000031,0.000000,-0.000031,0.000000,-0.000031,0.000000,0.000000,-0.000031,0.000000,-0.000031,0.000000,0.000000,0.000000,-0.000031,0.000000,0.000000,0.000000,-0.000031,0.000000,-0.000031,0.000000,-0.000031,0.000000,-0.000031,0.000000,-0.000031,0.000000,-0.000031,0.000000,-0.000031,-0.000031,0.000000,-0.000031,0.000000,-0.000031,-0.000031,0.000000,-0.000031,0.000000,-0.000031,0.000000,0.000000,-0.000031,0.000000,-0.000031,0.000000,-0.000031,-0.000031,0.000000,-0.000031,0.000000,-0.000031,0.000000,-0.000031,0.000000,0.000000,0.000000,0.000000,0.000000,-0.000031,-0.000031,0.000000,-0.000031,0.000000,-0.000031,0.000000,-0.000031,-0.000031,0.000000,0.000000,-0.000031,0.000000,-0.000031,-0.000031,-0.000031,-0.000031,0.000000,-0.000031,0.000000,-0.000031,-0.000031,-0.000031,-0.000031,-0.000031,-0.000031,-0.000031,-0.000031,-0.000031,0.000000,-0.000031,0.000000,-0.000031,0.000000,-0.000031,-0.000031,0.000000,-0.000031,-0.000031,0.000000,-0.000031,0.000000,-0.000031,0.000000,-0.000031,-0.000031,0.000000,-0.000031,0.000000,0.000000,-0.000031,-0.000031,-0.000031,-0.000031,0.000000,-0.000031,0.000000,-0.000031,0.000000,-0.000031,-0.000031,0.000000,-0.000031,0.000000,-0.000031,0.000000,-0.000031,0.000000,-0.000031,0.000000,-0.000031,0.000000,-0.000031,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,-0.000031,-0.000031,-0.000031,0.000000,-0.000031,0.000000,-0.000031,0.000000,-0.000031,0.000000,0.000000,0.000000,0.000000,0.000000,-0.000031,-0.000031,0.000000,-0.000031,0.000000,0.000000,0.000000,-0.000031,0.000000,-0.000031,0.000000,-0.000031,0.000000,-0.000031,0.000000,0.000000,-0.000031,0.000000,-0.000031,0.000000,0.000000,-0.000031,0.000000,-0.000031},
//            {-0.000031,0.000000,-0.000031,0.000000,-0.000031,0.000000,-0.000031,0.000000,0.000000,-0.000031,0.000000,-0.000031,-0.000031,0.000000,-0.000031,0.000000,-0.000031,0.000000,-0.000031,0.000000,0.000000,0.000000,-0.000031,0.000000,0.000000,0.000000,0.000000,-0.000031,0.000000,-0.000031,0.000000,-0.000031,0.000000,0.000000,-0.000031,0.000000,-0.000031,0.000000,0.000000,0.000000,-0.000031,0.000000,-0.000031,-0.000031,-0.000031,0.000000,0.000000,-0.000031,0.000000,0.000000,-0.000031,0.000000,-0.000031,0.000000,0.000000,-0.000031,0.000000,-0.000031,0.000000,-0.000031,-0.000031,-0.000031,0.000000,-0.000031,0.000000,-0.000031,0.000000,-0.000031,0.000000,-0.000031,0.000000,-0.000031,0.000000,-0.000031,-0.000031,0.000000,-0.000031,0.000000,-0.000031,0.000000,-0.000031,0.000000,-0.000031,0.000000,-0.000031,0.000000,-0.000031,-0.000031,-0.000031,-0.000031,0.000000,-0.000031,0.000000,-0.000031,0.000000,-0.000031,0.000000,-0.000031,0.000000,0.000000,0.000000,0.000000,-0.000031,0.000000,-0.000031,0.000000,0.000000,-0.000031,0.000000,-0.000031,0.000000,-0.000031,0.000000,-0.000031,0.000000,0.000000,-0.000031,-0.000031,0.000000,-0.000031,-0.000031,0.000000,-0.000031,0.000000,-0.000031,0.000000,-0.000031,0.000000,0.000000,-0.000031,-0.000031,-0.000031,-0.000031,0.000000,-0.000031,0.000000,0.000000,-0.000031,0.000000,0.000000,-0.000031,0.000000,-0.000031,0.000000,-0.000031,0.000000,0.000000,0.000000,0.000000,-0.000031,0.000000,0.000000,0.000000,-0.000031,0.000000,0.000000,-0.000031,0.000000,-0.000031,0.000000,-0.000031,0.000000,0.000000,-0.000031,0.000000,-0.000031,0.000000,-0.000031,0.000000,0.000000,-0.000031,0.000000,-0.000031,-0.000031,-0.000031,-0.000031,-0.000031,-0.000031,0.000000,-0.000031,0.000000,-0.000031,-0.000031,0.000000,-0.000031,0.000000,-0.000031,0.000000,-0.000031,-0.000031,0.000000,0.000000,-0.000031,0.000000,-0.000031,0.000000,-0.000031,-0.000031,0.000000,0.000000,-0.000031,-0.000031,-0.000031,0.000000,-0.000031,0.000000,-0.000031,0.000000,-0.000031,0.000000,-0.000031,0.000000,-0.000031,0.000000,0.000000,-0.000031,0.000000,-0.000031,0.000000,0.000000,0.000000,-0.000031,0.000000,0.000000,0.000000,-0.000031,0.000000,-0.000031,0.000000,-0.000031,0.000000,-0.000031,0.000000,-0.000031,0.000000,-0.000031,0.000000,-0.000031,-0.000031,0.000000,-0.000031,0.000000,-0.000031,-0.000031,0.000000,-0.000031,0.000000,-0.000031,0.000000,0.000000,-0.000031,0.000000,-0.000031,0.000000,-0.000031,-0.000031,0.000000,-0.000031,0.000000,-0.000031,0.000000,-0.000031,0.000000,0.000000,0.000000,0.000000,0.000000,-0.000031,-0.000031,0.000000,-0.000031,0.000000,-0.000031,0.000000,-0.000031,-0.000031,0.000000,0.000000,-0.000031,0.000000,-0.000031,-0.000031,-0.000031,-0.000031,0.000000,-0.000031,0.000000,-0.000031,-0.000031,-0.000031,-0.000031,-0.000031,-0.000031,-0.000031,-0.000031,-0.000031,0.000000,-0.000031,0.000000,-0.000031,0.000000,-0.000031,-0.000031,0.000000,-0.000031,-0.000031,0.000000,-0.000031,0.000000,-0.000031,0.000000,-0.000031,-0.000031,0.000000,-0.000031,0.000000,0.000000,-0.000031,-0.000031,-0.000031,-0.000031,0.000000,-0.000031,0.000000,-0.000031,0.000000,-0.000031,-0.000031,0.000000,-0.000031,0.000000,-0.000031,0.000000,-0.000031,0.000000,-0.000031,0.000000,-0.000031,0.000000,-0.000031,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,-0.000031,-0.000031,-0.000031,0.000000,-0.000031,0.000000,-0.000031,0.000000,-0.000031,0.000000,0.000000,0.000000,0.000000,0.000000,-0.000031,-0.000031,0.000000,-0.000031,0.000000,0.000000,0.000000,-0.000031,0.000000,-0.000031,0.000000,-0.000031,0.000000,-0.000031,0.000000,0.000000,-0.000031,0.000000,-0.000031,0.000000,0.000000,-0.000031,0.000000,-0.000031,0.000000,-0.000031,0.000000,0.000000,0.000000,-0.000031,-0.000031,-0.000031,0.000000,-0.000031,-0.000031,0.000000,0.000000,-0.000031,0.000000,-0.000031,0.000000,0.000000,-0.000031,0.000000,-0.000031,0.000000,-0.000031,0.000000,-0.000031,0.000000,-0.000031,-0.000031,-0.000031,0.000000,-0.000031,0.000000,0.000000,0.000000,0.000000,-0.000031,0.000000,-0.000031,0.000000,-0.000031,0.000000,-0.000031,0.000000,-0.000031,0.000000,-0.000031,0.000000,0.000000,-0.000031,0.000000,-0.000031,0.000000,-0.000031,0.000000,0.000000,-0.000031,-0.000031,0.000000,-0.000031,-0.000031,0.000000,-0.000031,-0.000031,-0.000031,0.000000,-0.000031,-0.000031,0.000000,-0.000031,-0.000031,-0.000031,0.000000,-0.000031,0.000000,-0.000031,0.000000,-0.000031,-0.000031,-0.000031,0.000000,-0.000031,0.000000,-0.000031,-0.000031,0.000000,-0.000031,0.000000,-0.000031,0.000000,-0.000031,0.000000,-0.000031,-0.000031,-0.000031,0.000000,-0.000031,0.000000,-0.000031,0.000000,-0.000031,-0.000031,0.000000,-0.000031,0.000000,-0.000031,-0.000031,0.000000,-0.000031,0.000000,-0.000031,-0.000031,-0.000031,-0.000031,0.000000,-0.000031,0.000000,-0.000031,-0.000031,-0.000031,0.000000,0.000000,0.000000,-0.000031,0.000000,0.000000,0.000000,0.000000,0.000000},
//            {0.000000,-0.000031,-0.000031,-0.000031,-0.000031,0.000000,-0.000031,0.000000,0.000000,-0.000031,0.000000,0.000000,-0.000031,0.000000,-0.000031,0.000000,-0.000031,0.000000,0.000000,0.000000,0.000000,-0.000031,0.000000,0.000000,0.000000,-0.000031,0.000000,0.000000,-0.000031,0.000000,-0.000031,0.000000,-0.000031,0.000000,0.000000,-0.000031,0.000000,-0.000031,0.000000,-0.000031,0.000000,0.000000,-0.000031,0.000000,-0.000031,-0.000031,-0.000031,-0.000031,-0.000031,-0.000031,0.000000,-0.000031,0.000000,-0.000031,-0.000031,0.000000,-0.000031,0.000000,-0.000031,0.000000,-0.000031,-0.000031,0.000000,0.000000,-0.000031,0.000000,-0.000031,0.000000,-0.000031,-0.000031,0.000000,0.000000,-0.000031,-0.000031,-0.000031,0.000000,-0.000031,0.000000,-0.000031,0.000000,-0.000031,0.000000,-0.000031,0.000000,-0.000031,0.000000,0.000000,-0.000031,0.000000,-0.000031,0.000000,0.000000,0.000000,-0.000031,0.000000,0.000000,0.000000,-0.000031,0.000000,-0.000031,0.000000,-0.000031,0.000000,-0.000031,0.000000,-0.000031,0.000000,-0.000031,0.000000,-0.000031,-0.000031,0.000000,-0.000031,0.000000,-0.000031,-0.000031,0.000000,-0.000031,0.000000,-0.000031,0.000000,0.000000,-0.000031,0.000000,-0.000031,0.000000,-0.000031,-0.000031,0.000000,-0.000031,0.000000,-0.000031,0.000000,-0.000031,0.000000,0.000000,0.000000,0.000000,0.000000,-0.000031,-0.000031,0.000000,-0.000031,0.000000,-0.000031,0.000000,-0.000031,-0.000031,0.000000,0.000000,-0.000031,0.000000,-0.000031,-0.000031,-0.000031,-0.000031,0.000000,-0.000031,0.000000,-0.000031,-0.000031,-0.000031,-0.000031,-0.000031,-0.000031,-0.000031,-0.000031,-0.000031,0.000000,-0.000031,0.000000,-0.000031,0.000000,-0.000031,-0.000031,0.000000,-0.000031,-0.000031,0.000000,-0.000031,0.000000,-0.000031,0.000000,-0.000031,-0.000031,0.000000,-0.000031,0.000000,0.000000,-0.000031,-0.000031,-0.000031,-0.000031,0.000000,-0.000031,0.000000,-0.000031,0.000000,-0.000031,-0.000031,0.000000,-0.000031,0.000000,-0.000031,0.000000,-0.000031,0.000000,-0.000031,0.000000,-0.000031,0.000000,-0.000031,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,-0.000031,-0.000031,-0.000031,0.000000,-0.000031,0.000000,-0.000031,0.000000,-0.000031,0.000000,0.000000,0.000000,0.000000,0.000000,-0.000031,-0.000031,0.000000,-0.000031,0.000000,0.000000,0.000000,-0.000031,0.000000,-0.000031,0.000000,-0.000031,0.000000,-0.000031,0.000000,0.000000,-0.000031,0.000000,-0.000031,0.000000,0.000000,-0.000031,0.000000,-0.000031,0.000000,-0.000031,0.000000,0.000000,0.000000,-0.000031,-0.000031,-0.000031,0.000000,-0.000031,-0.000031,0.000000,0.000000,-0.000031,0.000000,-0.000031,0.000000,0.000000,-0.000031,0.000000,-0.000031,0.000000,-0.000031,0.000000,-0.000031,0.000000,-0.000031,-0.000031,-0.000031,0.000000,-0.000031,0.000000,0.000000,0.000000,0.000000,-0.000031,0.000000,-0.000031,0.000000,-0.000031,0.000000,-0.000031,0.000000,-0.000031,0.000000,-0.000031,0.000000,0.000000,-0.000031,0.000000,-0.000031,0.000000,-0.000031,0.000000,0.000000,-0.000031,-0.000031,0.000000,-0.000031,-0.000031,0.000000,-0.000031,-0.000031,-0.000031,0.000000,-0.000031,-0.000031,0.000000,-0.000031,-0.000031,-0.000031,0.000000,-0.000031,0.000000,-0.000031,0.000000,-0.000031,-0.000031,-0.000031,0.000000,-0.000031,0.000000,-0.000031,-0.000031,0.000000,-0.000031,0.000000,-0.000031,0.000000,-0.000031,0.000000,-0.000031,-0.000031,-0.000031,0.000000,-0.000031,0.000000,-0.000031,0.000000,-0.000031,-0.000031,0.000000,-0.000031,0.000000,-0.000031,-0.000031,0.000000,-0.000031,0.000000,-0.000031,-0.000031,-0.000031,-0.000031,0.000000,-0.000031,0.000000,-0.000031,-0.000031,-0.000031,0.000000,0.000000,0.000000,-0.000031,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,-0.000031,0.000000,-0.000031,-0.000031,0.000000,-0.000031,0.000000,-0.000031,0.000000,-0.000031,-0.000031,-0.000031,0.000000,-0.000031,0.000000,-0.000031,0.000000,-0.000031,0.000000,0.000000,0.000000,0.000000,-0.000031,0.000000,-0.000031,-0.000031,-0.000031,-0.000031,0.000000,-0.000031,0.000000,-0.000031,0.000000,-0.000031,0.000000,-0.000031,-0.000031,0.000000,-0.000031,0.000000,-0.000031,0.000000,0.000000,0.000000,-0.000031,0.000000,-0.000031,-0.000031,-0.000031,0.000000,-0.000031,0.000000,-0.000031,0.000000,0.000000,0.000000,-0.000031,0.000000,-0.000031,0.000000,-0.000031,-0.000031,0.000000,-0.000061,-0.000031,-0.000092,-0.000031,-0.000061,-0.000061,-0.000122,-0.000061,-0.000061,-0.000061,-0.000061,-0.000061,-0.000061,-0.000061,-0.000061,-0.000061,0.000031,0.000000,0.000031,-0.000122,-0.000061,0.000000,-0.000031,-0.000092,-0.000061,-0.000092,-0.000061,-0.000092,-0.000061,-0.000092,-0.000061,-0.000092,-0.000061,-0.000061,-0.000092,-0.000092,-0.000092,-0.000031,-0.000183,-0.000092,-0.000153,-0.000122,-0.000153,-0.000122,-0.000122,-0.000244,-0.000153,-0.000214,-0.000183,-0.000214,-0.000183,-0.000183,-0.000183,-0.000183,-0.000183,-0.000183,-0.000183,-0.000183,-0.000275,-0.000244,-0.000244,-0.000244},
        };

        public static final double[][] Out = new double[][]{
                {0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000},
                {0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000},
                {0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000},
                {0.000007,0.000006,0.000000,0.000002,-0.000000,-0.000001,-0.000009,-0.000023,-0.000011,-0.000020,-0.000018,-0.000002,-0.000010,-0.000007,-0.000002,-0.000012,-0.000009,-0.000008,0.000005,0.000002,0.000010,0.000004,-0.000008,-0.000003,-0.000007,-0.000020,-0.000020,0.000008,0.000024,-0.000003,-0.000021,-0.000023,-0.000018,0.000005,0.000007,0.000008,0.000004,0.000001,-0.000022,-0.000017,-0.000008,0.000025,0.000035,0.000019,-0.000007,-0.000006,-0.000004,0.000019,0.000007,0.000021,0.000014,-0.000007,-0.000008,-0.000007,0.000003,0.000023,0.000017,0.000004,-0.000001,-0.000001,0.000005,0.000002,0.000010,0.000000,-0.000008,0.000010,0.000017,0.000010,0.000022,0.000022,0.000021,0.000022,0.000033,0.000025,0.000043,0.000037,0.000042,0.000043,0.000030,0.000031,0.000011,0.000009,-0.000001,-0.000007,0.000003,-0.000003,-0.000006,-0.000035,-0.000031,-0.000041,-0.000032,-0.000015,-0.000015,-0.000009,-0.000008,-0.000031,-0.000007,0.000013,0.000043,0.000034,0.000036,0.000025,0.000017,0.000036,0.000058,0.000042,0.000044,0.000028,0.000010,0.000011,0.000012,0.000012,-0.000013,-0.000026,-0.000032,-0.000034,-0.000031,-0.000029,-0.000007,0.000010,0.000009,0.000016,0.000007,0.000016,0.000021,0.000019,0.000043,0.000040,0.000022,0.000042,0.000055,0.000018,0.000012,-0.000031,-0.000039,0.000036,0.000003,-0.000040,-0.000004,-0.000049,-0.000048,-0.000067,0.000026,0.000014,-0.000065,-0.000104,-0.000089,-0.000049,0.000054,0.000006,0.000002,0.000035,0.000009,0.000015,-0.000013,0.000023,0.000028,0.000058,-0.000004,0.000003,0.000031,0.000029,0.000001,-0.000047,-0.000013,-0.000071,0.000005,0.000009,0.000004,-0.000006,-0.000029,-0.000045,-0.000028,0.000016,-0.000008,0.000004,0.000012,-0.000009,0.000049,0.000086,-0.000035,0.000067,0.000066,0.000076,0.000007,0.000043,0.000001,-0.000034,0.000036,-0.000060,0.000014,-0.000044,-0.000031,-0.000095,-0.000094,-0.000010,-0.000061,-0.000071,-0.000063,-0.000009,-0.000027,0.000027,-0.000044,-0.000012,-0.000012,-0.000043,-0.000020,0.000010,-0.000006,0.000003,0.000038,-0.000068,-0.000023,-0.000030,-0.000022,0.000048,-0.000010,0.000012,-0.000041,0.000025,-0.000030,-0.000134,-0.000089,-0.000051,-0.000097,-0.000103,-0.000072,-0.000034,-0.000095,-0.000023,-0.000046,-0.000028,0.000034,-0.000016,0.000031,0.000024,-0.000043,0.000028,0.000032,-0.000045,-0.000008,-0.000026,-0.000017,-0.000031,-0.000006,-0.000006,-0.000012,-0.000047,-0.000029,-0.000052,-0.000087,-0.000012,-0.000052,-0.000058,-0.000036,-0.000050,-0.000092,-0.000017,-0.000029,0.000043,0.000017,-0.000006,0.000016,-0.000036,0.000006,0.000017,0.000006,-0.000021,0.000024,0.000063,-0.000003,0.000028,0.000027,0.000043,-0.000059,-0.000010,0.000034,-0.000028,-0.000032,-0.000017,-0.000035,-0.000032,0.000030,-0.000014,-0.000049,0.000005,-0.000053,-0.000064,0.000014,-0.000012,-0.000000,-0.000032,-0.000038,-0.000017,0.000031,-0.000002,-0.000001,-0.000056,0.000007,0.000030,0.000060,0.000006,0.000019,0.000011,0.000030,0.000014,0.000002,0.000007,0.000001,0.000016,-0.000055,0.000013,-0.000036,-0.000035,-0.000014,-0.000030,-0.000031,-0.000009,-0.000028,0.000009,-0.000004,-0.000017,-0.000038,0.000010,0.000032,0.000061,0.000019,0.000048,0.000039,-0.000018,0.000007,0.000012,-0.000020,-0.000009,0.000009,0.000030,0.000030,-0.000017,-0.000012,0.000036,0.000020,-0.000022,-0.000013,0.000026,0.000031,0.000053,0.000067,0.000098,0.000108,0.000073,0.000086,0.000106,0.000079,0.000026,0.000028,0.000004,0.000005,0.000014,-0.000001,-0.000007,-0.000023,0.000016,-0.000004,-0.000019,-0.000011,-0.000037,0.000004,0.000015,-0.000017,0.000040,0.000024,0.000020,-0.000007,0.000015,0.000051,0.000024,0.000063,0.000051,0.000044,0.000028,0.000030,0.000030,0.000013,0.000036,-0.000088,-0.000034,-0.000075,-0.000062,-0.000046,-0.000100,-0.000055,-0.000009,0.000029,-0.000057,-0.000000,-0.000081,-0.000073,0.000006,-0.000000,-0.000041,0.000005,0.000041,-0.000026,-0.000050,-0.000036,-0.000090,-0.000030,-0.000080,-0.000096,-0.000024,-0.000065,0.000022,-0.000048,-0.000030,-0.000005,0.000028,0.000064,0.000109,0.000095,0.000166,0.000142,0.000157,0.000156,0.000128,0.000185,0.000165,0.000219,0.000166,0.000074,0.000117,0.000106,0.000063,0.000047,-0.000015,-0.000053,-0.000007,-0.000050,-0.000053,-0.000024,-0.000094,-0.000125,-0.000105,-0.000098,-0.000002,-0.000037,-0.000051,-0.000049,-0.000008,0.000007,-0.000017,0.000030,0.000055,-0.000002,0.000049,0.000003,0.000024,0.000014,0.000006,0.000008,-0.000012,-0.000079,-0.000064,-0.000100,-0.000023,-0.000113,-0.000079,-0.000078,-0.000137,-0.000093,-0.000079,-0.000172,-0.000092,-0.000140,-0.000121,-0.000096,-0.000104,-0.000114,-0.000144,-0.000088,-0.000083,-0.000069,-0.000064,-0.000100,-0.000048,-0.000064,-0.000086,-0.000054,-0.000010,-0.000084,-0.000035,-0.000028,-0.000037,-0.000029,-0.000034,-0.000006,-0.000007,-0.000009,-0.000016,-0.000022,-0.000026,-0.000000,-0.000045,-0.000016,-0.000016,-0.000003,-0.000031,-0.000003,-0.000011,0.000006,-0.000004,-0.000010,-0.000002},
                {0.000029,0.000008,0.000002,0.000027,0.000002,0.000042,0.000078,-0.000005,0.000026,0.000043,-0.000006,0.000029,0.000037,0.000071,0.000001,0.000003,0.000019,0.000057,0.000075,0.000022,-0.000023,0.000010,0.000029,-0.000028,0.000021,-0.000019,0.000020,-0.000007,0.000003,-0.000038,0.000013,-0.000004,-0.000032,-0.000015,0.000003,0.000026,-0.000026,0.000050,-0.000004,-0.000001,-0.000009,-0.000019,-0.000024,0.000017,0.000018,-0.000044,0.000016,0.000004,-0.000025,-0.000010,-0.000029,-0.000028,0.000036,-0.000024,-0.000027,-0.000040,0.000040,-0.000038,0.000034,0.000042,-0.000029,0.000056,0.000008,0.000034,-0.000018,0.000041,0.000049,-0.000006,0.000041,0.000050,0.000071,0.000018,0.000009,-0.000023,0.000009,0.000012,0.000015,0.000044,0.000020,0.000022,0.000019,0.000032,-0.000009,0.000092,0.000060,0.000045,0.000036,0.000004,0.000037,0.000001,0.000042,-0.000029,0.000053,0.000116,0.000058,0.000012,0.000049,0.000058,0.000015,0.000008,0.000051,-0.000003,0.000013,0.000044,-0.000024,0.000028,-0.000036,-0.000025,0.000036,0.000006,-0.000012,0.000054,-0.000005,-0.000004,-0.000008,0.000020,-0.000009,0.000001,-0.000024,0.000014,-0.000032,-0.000000,0.000038,-0.000030,-0.000006,-0.000006,-0.000001,0.000026,0.000092,-0.000021,0.000011,0.000013,0.000058,0.000088,-0.000019,0.000061,0.000081,0.000014,-0.000009,0.000092,0.000001,-0.000069,-0.000011,0.000039,-0.000038,-0.000017,0.000139,0.000074,-0.000019,0.000097,0.000070,0.000045,0.000088,0.000011,-0.000036,0.000010,0.000055,0.000005,0.000078,0.000171,0.000044,-0.000004,0.000011,-0.000006,0.000012,0.000040,-0.000131,0.000049,0.000083,0.000015,-0.000039,-0.000081,0.000034,-0.000049,-0.000020,0.000018,-0.000032,0.000066,0.000065,-0.000025,-0.000018,-0.000001,0.000079,-0.000021,0.000049,0.000007,-0.000038,0.000016,0.000016,-0.000065,0.000025,0.000028,-0.000027,-0.000054,-0.000021,0.000002,-0.000036,-0.000059,0.000017,-0.000049,0.000008,0.000002,0.000008,-0.000057,0.000000,-0.000082,0.000030,-0.000120,-0.000006,-0.000007,-0.000001,-0.000043,-0.000017,0.000029,-0.000003,0.000006,0.000056,-0.000061,0.000000,-0.000084,-0.000120,-0.000036,-0.000041,-0.000078,-0.000152,-0.000018,-0.000075,-0.000067,0.000027,-0.000155,0.000004,-0.000134,-0.000077,-0.000008,-0.000013,0.000015,0.000029,0.000014,-0.000085,0.000033,0.000057,-0.000094,0.000053,0.000054,-0.000077,0.000004,0.000042,-0.000022,-0.000050,0.000037,-0.000066,-0.000101,-0.000085,-0.000044,-0.000093,-0.000069,-0.000079,-0.000155,-0.000305,-0.000268,-0.000172,-0.000249,-0.000132,0.000026,0.000099,0.000134,0.000215,0.000119,0.000079,0.000050,0.000054,0.000036,0.000062,0.000238,0.000252,0.000115,0.000110,0.000093,0.000040,-0.000098,-0.000162,-0.000175,-0.000184,-0.000233,-0.000196,-0.000264,-0.000149,-0.000143,0.000148,0.000187,0.000187,0.000379,0.000234,0.000302,0.000237,0.000321,0.000358,0.000452,0.000464,0.000464,0.000236,0.000197,0.000123,0.000050,-0.000106,-0.000163,-0.000224,-0.000228,-0.000164,-0.000076,-0.000014,-0.000074,-0.000169,-0.000215,-0.000149,-0.000147,-0.000167,-0.000056,-0.000138,-0.000033,0.000077,0.000113,0.000214,0.000123,0.000008,-0.000096,-0.000083,-0.000065,0.000013,-0.000077,-0.000097,-0.000182,-0.000213,-0.000243,-0.000221,-0.000248,-0.000274,-0.000259,-0.000300,-0.000265,-0.000185,-0.000155,-0.000175,-0.000184,-0.000117,-0.000029,-0.000110,-0.000040,-0.000043,-0.000067,-0.000048,-0.000069,-0.000000,0.000023,-0.000076,-0.000175,-0.000049,-0.000008,0.000013,0.000008,-0.000067,-0.000097,-0.000057,-0.000024,-0.000031,0.000016,0.000040,0.000074,0.000007,0.000059,0.000022,0.000093,0.000091,0.000106,0.000058,0.000111,0.000111,0.000076,0.000083,0.000086,0.000056,0.000112,0.000031,-0.000016,0.000061,0.000075,0.000109,0.000089,0.000086,0.000075,0.000067,0.000055,0.000018,0.000058,0.000041,-0.000006,-0.000022,0.000036,-0.000033,-0.000005,-0.000053,0.000033,0.000023,-0.000023,-0.000063,-0.000039,-0.000021,-0.000099,-0.000076,-0.000100,-0.000043,-0.000167,-0.000121,-0.000102,-0.000125,-0.000115,-0.000124,-0.000092,-0.000148,-0.000122,-0.000115,-0.000056,-0.000075,-0.000069,-0.000043,-0.000062,-0.000084,-0.000086,-0.000035,-0.000088,-0.000053,-0.000045,-0.000072,-0.000077,-0.000064,-0.000092,-0.000086,-0.000055,-0.000104,-0.000059,-0.000061,-0.000057,-0.000015,-0.000105,-0.000098,-0.000094,-0.000071,-0.000057,-0.000064,0.000015,-0.000106,-0.000092,-0.000026,-0.000033,-0.000016,-0.000070,-0.000036,-0.000027,-0.000060,0.000002,-0.000027,0.000022,-0.000027,-0.000050,0.000013,-0.000043,-0.000034,-0.000038,-0.000011,-0.000024,-0.000023,-0.000016,-0.000018,-0.000026,-0.000013,-0.000010,0.000027,0.000007,0.000062,0.000003,0.000005,0.000033,0.000045,0.000019,0.000033,0.000006,0.000036,0.000009,-0.000006,0.000005,0.000012,0.000013,0.000019,0.000019,-0.000028,0.000006,0.000036,-0.000038,0.000044,-0.000008,0.000003,0.000029,-0.000025,0.000023,0.000000,-0.000000,0.000032,0.000015,0.000005,-0.000019,0.000011,0.000007},
                {-0.000010,0.000026,0.000025,-0.000002,-0.000004,-0.000026,0.000040,0.000015,-0.000011,-0.000002,0.000018,-0.000015,0.000006,0.000017,0.000012,-0.000022,0.000003,0.000014,-0.000029,0.000002,0.000007,-0.000033,-0.000010,0.000004,-0.000048,0.000008,0.000012,-0.000031,-0.000005,0.000003,-0.000016,-0.000044,-0.000013,-0.000005,-0.000034,0.000013,-0.000003,-0.000033,0.000038,-0.000016,-0.000034,0.000037,0.000022,0.000041,-0.000018,0.000039,0.000019,0.000021,0.000020,-0.000002,-0.000008,0.000028,0.000024,-0.000016,0.000025,0.000010,0.000007,0.000003,-0.000001,-0.000041,-0.000037,-0.000010,-0.000040,-0.000045,-0.000015,0.000016,0.000010,-0.000035,-0.000042,-0.000044,-0.000062,-0.000027,-0.000034,-0.000001,-0.000007,-0.000003,-0.000020,0.000016,-0.000052,-0.000007,-0.000047,-0.000036,-0.000039,-0.000016,-0.000029,-0.000023,-0.000008,0.000001,-0.000009,0.000022,-0.000027,-0.000016,-0.000011,-0.000035,-0.000039,-0.000041,0.000005,-0.000025,-0.000008,0.000005,-0.000033,0.000039,-0.000041,0.000019,0.000004,-0.000048,-0.000030,-0.000025,-0.000023,-0.000000,0.000009,-0.000025,-0.000046,0.000012,-0.000027,-0.000022,0.000040,-0.000005,-0.000037,0.000000,-0.000012,-0.000051,0.000005,0.000037,0.000045,0.000023,0.000063,0.000066,0.000015,-0.000078,-0.000105,-0.000107,-0.000147,-0.000094,0.000014,0.000017,0.000110,0.000187,0.000121,-0.000012,-0.000076,-0.000066,-0.000054,-0.000058,-0.000001,0.000092,0.000056,0.000139,0.000075,0.000043,0.000070,-0.000061,-0.000050,-0.000059,-0.000153,0.000019,-0.000147,-0.000189,-0.000145,-0.000112,0.000043,0.000066,0.000091,0.000087,-0.000071,-0.000109,-0.000060,0.000012,0.000091,0.000186,0.000293,0.000199,0.000019,0.000055,0.000008,-0.000166,-0.000190,-0.000204,-0.000186,-0.000011,0.000018,0.000085,0.000176,0.000088,-0.000062,-0.000046,-0.000098,-0.000094,-0.000127,-0.000206,-0.000122,-0.000008,0.000095,0.000207,0.000218,0.000153,-0.000011,-0.000022,-0.000070,-0.000064,0.000021,0.000111,0.000109,0.000026,-0.000215,-0.000130,-0.000129,-0.000098,-0.000097,-0.000137,-0.000076,-0.000068,-0.000034,0.000083,-0.000003,0.000067,0.000023,0.000041,0.000026,0.000047,0.000056,-0.000023,-0.000003,-0.000008,0.000010,0.000051,-0.000079,-0.000087,-0.000036,-0.000028,0.000067,0.000130,0.000047,-0.000059,-0.000058,-0.000081,-0.000040,0.000018,0.000051,-0.000040,-0.000005,-0.000013,-0.000002,0.000058,0.000014,0.000036,-0.000016,0.000015,0.000004,-0.000024,0.000037,0.000086,0.000010,-0.000018,-0.000061,-0.000087,0.000000,-0.000020,0.000003,0.000051,0.000007,0.000090,0.000055,0.000059,0.000048,0.000074,0.000079,0.000015,-0.000008,-0.000018,0.000033,-0.000049,0.000051,0.000026,-0.000047,0.000047,0.000081,0.000071,0.000018,0.000001,0.000025,0.000004,-0.000011,-0.000018,0.000016,0.000061,-0.000019,-0.000009,0.000002,-0.000046,-0.000014,-0.000014,0.000045,-0.000015,0.000058,0.000002,0.000009,0.000053,0.000022,0.000055,0.000022,0.000015,-0.000002,0.000015,-0.000069,-0.000050,-0.000039,-0.000043,-0.000036,-0.000029,-0.000049,0.000003,0.000030,-0.000004,0.000020,0.000038,0.000024,-0.000025,-0.000035,-0.000077,-0.000041,-0.000036,-0.000077,-0.000025,-0.000033,0.000017,-0.000036,0.000043,0.000001,0.000029,-0.000010,-0.000028,0.000036,-0.000016,-0.000012,-0.000035,-0.000030,0.000017,0.000008,-0.000010,-0.000025,-0.000050,-0.000036,-0.000049,-0.000057,-0.000039,-0.000029,-0.000037,-0.000020,-0.000007,0.000030,0.000015,0.000044,0.000023,-0.000010,0.000054,-0.000009,-0.000025,-0.000004,-0.000031,0.000002,-0.000003,-0.000008,-0.000002,-0.000018,-0.000033,0.000000,-0.000018,-0.000040,0.000029,-0.000021,0.000004,0.000045,0.000034,0.000015,0.000008,0.000003,-0.000011,-0.000040,-0.000007,0.000013,0.000041,0.000070,0.000045,-0.000016,0.000050,0.000038,0.000024,0.000037,0.000023,0.000013,0.000022,0.000031,0.000027,0.000022,0.000044,0.000023,0.000002,0.000009,0.000009,-0.000014,-0.000016,0.000029,-0.000013,0.000014,0.000003,-0.000014,-0.000014,-0.000040,-0.000022,-0.000032,0.000013,-0.000006,-0.000009,-0.000003,0.000011,-0.000012,0.000018,0.000035,-0.000032,0.000020,0.000023,0.000001,0.000006,0.000009,0.000021,-0.000012,0.000019,0.000008,0.000014,-0.000011,0.000027,0.000032,0.000008,0.000028,0.000025,0.000052,0.000021,0.000016,0.000004,0.000028,-0.000010,0.000015,0.000021,0.000003,0.000041,-0.000017,0.000001,0.000013,-0.000002,0.000006,0.000031,0.000010,0.000007,0.000018,0.000021,0.000027,-0.000018,0.000014,0.000002,0.000017,0.000010,-0.000006,0.000001,0.000003,0.000025,-0.000014,-0.000016,0.000020,-0.000009,0.000012,-0.000007,-0.000013,-0.000003,-0.000015,0.000019,-0.000019,0.000016,0.000024,0.000006,0.000006,0.000005,-0.000017,-0.000002,-0.000002,-0.000015,-0.000023,0.000011,0.000003,0.000012,-0.000009,-0.000006,-0.000010,-0.000015,-0.000017,-0.000023,-0.000005,-0.000002,-0.000014,-0.000022,-0.000007,-0.000033,-0.000015,-0.000024,-0.000018,-0.000011,-0.000039,-0.000008,-0.000015,-0.000021,-0.000017,-0.000018},
//            {-0.000027,0.000003,-0.000028,0.000039,0.000034,-0.000046,-0.000008,0.000149,0.000112,-0.000005,0.000120,-0.000046,-0.000111,-0.000023,-0.000049,-0.000115,-0.000074,-0.000033,-0.000025,-0.000159,-0.000110,-0.000136,-0.000135,-0.000179,-0.000267,-0.000166,-0.000024,-0.000016,-0.000039,-0.000115,-0.000102,-0.000032,0.000220,0.000373,0.000365,0.000446,0.000359,0.000284,0.000206,0.000145,0.000438,0.000613,0.000479,0.000315,0.000156,0.000169,0.000207,0.000146,0.000032,-0.000239,-0.000306,-0.000354,-0.000168,0.000105,0.000080,-0.000216,-0.000438,-0.000458,-0.000126,0.000076,0.000066,-0.000013,-0.000234,-0.000139,0.000154,0.000384,0.000616,0.000446,-0.000014,-0.000113,-0.000087,-0.000014,0.000217,0.000187,0.000040,-0.000271,-0.000380,-0.000447,-0.000359,-0.000293,-0.000397,-0.000438,-0.000638,-0.000600,-0.000310,-0.000419,-0.000511,-0.000395,-0.000427,-0.000322,-0.000277,-0.000244,-0.000279,-0.000440,-0.000319,-0.000137,0.000022,-0.000036,-0.000341,-0.000558,-0.000416,-0.000162,0.000175,0.000286,-0.000106,-0.000311,-0.000414,-0.000307,-0.000087,0.000028,0.000108,0.000147,0.000016,-0.000056,0.000030,0.000148,0.000325,0.000169,0.000145,0.000152,0.000216,0.000326,0.000471,0.000510,0.000443,0.000396,0.000167,-0.000046,-0.000011,0.000207,0.000299,0.000240,0.000145,0.000113,0.000082,0.000090,0.000203,0.000153,0.000237,0.000123,0.000035,-0.000008,-0.000066,0.000025,0.000100,0.000150,0.000140,0.000158,0.000028,-0.000067,-0.000001,-0.000186,-0.000183,-0.000266,-0.000268,-0.000411,-0.000549,-0.000449,-0.000360,-0.000322,-0.000439,-0.000512,-0.000561,-0.000499,-0.000450,-0.000260,-0.000231,-0.000128,0.000003,0.000032,0.000014,0.000083,0.000128,0.000127,0.000237,0.000189,0.000134,0.000011,-0.000063,-0.000170,-0.000004,-0.000052,0.000023,0.000024,-0.000046,0.000015,-0.000073,-0.000271,-0.000311,-0.000280,-0.000359,-0.000528,-0.000517,-0.000408,-0.000490,-0.000376,-0.000171,-0.000006,0.000125,0.000178,0.000348,0.000202,0.000181,0.000283,0.000135,0.000150,0.000032,0.000033,0.000252,0.000098,0.000133,-0.000038,-0.000006,-0.000049,-0.000228,-0.000285,-0.000345,-0.000262,-0.000092,-0.000097,-0.000037,0.000270,0.000397,0.000229,0.000281,0.000324,0.000193,0.000100,0.000095,0.000120,-0.000017,-0.000140,-0.000225,-0.000069,-0.000058,-0.000056,-0.000202,-0.000203,-0.000299,-0.000272,-0.000076,-0.000073,0.000037,0.000023,0.000115,0.000152,0.000096,-0.000002,-0.000151,-0.000157,0.000009,-0.000088,-0.000002,0.000153,0.000075,0.000066,-0.000022,-0.000138,-0.000136,-0.000170,-0.000226,-0.000310,-0.000238,-0.000322,-0.000282,-0.000170,-0.000012,-0.000023,0.000249,0.000204,0.000314,0.000271,0.000334,0.000352,0.000403,0.000398,0.000263,0.000257,0.000209,0.000052,0.000179,0.000108,0.000083,0.000080,0.000056,0.000049,0.000171,0.000163,0.000172,0.000123,0.000051,0.000236,0.000153,0.000148,0.000160,0.000232,0.000256,0.000197,0.000285,0.000282,0.000220,0.000165,0.000173,0.000141,0.000188,0.000216,0.000078,0.000053,0.000033,-0.000122,-0.000079,-0.000150,-0.000135,-0.000169,-0.000224,-0.000228,-0.000276,-0.000343,-0.000191,-0.000212,-0.000164,-0.000047,0.000012,0.000021,0.000087,0.000118,0.000129,0.000153,0.000126,0.000168,0.000176,0.000137,0.000092,0.000065,0.000011,0.000015,-0.000032,-0.000072,-0.000072,-0.000131,-0.000160,-0.000150,-0.000198,-0.000164,-0.000214,-0.000180,-0.000129,-0.000090,-0.000050,-0.000039,-0.000064,0.000007,0.000015,0.000052,0.000078,0.000089,0.000063,0.000159,0.000113,0.000165,0.000180,0.000022,0.000086,0.000070,0.000013,0.000072,0.000050,0.000047,0.000014,-0.000052,0.000005,-0.000023,0.000004,-0.000037,0.000006,-0.000015,-0.000044,0.000034,0.000053,0.000067,0.000067,0.000163,0.000158,0.000147,0.000149,0.000108,0.000108,0.000095,0.000042,0.000044,0.000041,0.000094,0.000085,0.000090,0.000041,0.000104,0.000031,0.000085,0.000023,0.000075,0.000014,0.000075,0.000035,0.000023,0.000076,0.000042,0.000043,0.000085,0.000061,0.000043,0.000064,0.000079,0.000079,0.000071,0.000057,0.000056,0.000030,0.000047,0.000041,0.000051,0.000102,0.000017,-0.000012,0.000053,0.000031,0.000007,0.000021,-0.000005,0.000000,-0.000017,0.000006,0.000003,-0.000023,0.000024,-0.000023,-0.000016,-0.000028,0.000001,0.000008,0.000019,0.000056,0.000025,0.000020,0.000052,0.000085,0.000072,0.000088,0.000065,0.000092,0.000030,0.000059,0.000056,0.000039,0.000015,0.000025,0.000015,0.000039,0.000004,-0.000009,-0.000008,-0.000020,-0.000007,-0.000014,-0.000022,0.000010,-0.000015,0.000001,-0.000009,-0.000017,0.000013,0.000009,0.000017,0.000028,0.000006,0.000025,0.000014,-0.000002,-0.000001,-0.000024,0.000036,-0.000006,0.000006,-0.000004,0.000025,-0.000039,0.000018,-0.000020,0.000023,-0.000006,-0.000001,-0.000011,-0.000010,-0.000041,-0.000002,-0.000021,0.000006,-0.000023,-0.000013,0.000013,0.000005,-0.000012,-0.000006,0.000012,0.000009,-0.000000,0.000021,0.000008,0.000013,-0.000009,-0.000005,0.000016,-0.000003},
//            {0.000022,0.000017,0.000066,0.000046,0.000031,0.000061,0.000026,0.000035,0.000036,0.000075,0.000079,0.000017,0.000023,0.000007,0.000008,0.000005,0.000024,0.000006,-0.000031,0.000031,-0.000041,0.000054,-0.000008,-0.000060,-0.000061,-0.000044,-0.000098,-0.000106,-0.000081,-0.000039,-0.000007,-0.000127,-0.000057,-0.000077,-0.000162,-0.000114,-0.000056,-0.000011,-0.000035,0.000008,0.000068,0.000050,0.000052,0.000047,0.000065,0.000066,0.000115,0.000193,0.000100,0.000083,-0.000014,0.000029,0.000012,0.000032,0.000005,0.000067,-0.000019,0.000012,0.000052,-0.000022,-0.000093,-0.000091,-0.000088,-0.000112,-0.000141,-0.000075,-0.000163,-0.000225,-0.000138,-0.000003,0.000002,-0.000002,-0.000055,-0.000021,-0.000106,0.000009,0.000070,0.000063,-0.000060,-0.000056,0.000023,0.000147,0.000147,0.000111,0.000041,0.000014,0.000039,-0.000045,-0.000166,-0.000019,0.000006,-0.000001,-0.000051,0.000027,0.000077,0.000080,0.000030,0.000116,0.000037,-0.000009,0.000018,-0.000011,-0.000059,-0.000072,-0.000088,-0.000058,0.000061,0.000117,0.000025,-0.000094,-0.000193,-0.000176,-0.000162,-0.000083,0.000035,0.000128,0.000187,0.000195,0.000117,-0.000099,-0.000259,-0.000339,-0.000291,-0.000165,-0.000048,0.000110,0.000231,0.000045,-0.000008,-0.000113,-0.000082,-0.000064,-0.000084,-0.000092,-0.000084,-0.000194,-0.000187,-0.000131,-0.000115,0.000127,0.000122,0.000089,0.000113,0.000136,0.000101,0.000081,0.000141,0.000226,0.000095,0.000035,-0.000030,-0.000049,-0.000001,-0.000046,0.000006,0.000067,0.000096,0.000148,0.000217,0.000286,0.000191,0.000150,0.000179,0.000086,0.000017,0.000095,0.000083,0.000150,0.000185,0.000235,0.000175,0.000116,0.000011,0.000015,-0.000038,-0.000049,0.000052,0.000096,0.000109,0.000054,0.000105,0.000045,-0.000023,0.000019,0.000054,-0.000048,-0.000038,-0.000164,-0.000224,-0.000256,-0.000269,-0.000133,-0.000058,0.000053,0.000189,0.000231,0.000106,0.000246,0.000176,0.000103,0.000023,0.000043,0.000044,0.000008,-0.000043,-0.000015,0.000006,0.000002,0.000004,0.000016,-0.000110,-0.000034,-0.000037,-0.000161,-0.000177,-0.000227,-0.000238,-0.000208,-0.000080,-0.000136,-0.000026,0.000016,0.000094,0.000061,0.000034,-0.000021,-0.000029,-0.000013,0.000083,0.000068,0.000204,0.000186,0.000161,0.000112,0.000016,-0.000044,-0.000013,-0.000026,-0.000045,0.000006,-0.000092,-0.000020,-0.000031,-0.000009,-0.000045,-0.000065,0.000048,-0.000057,-0.000063,-0.000058,-0.000077,0.000024,0.000048,0.000137,0.000208,0.000143,0.000098,0.000096,0.000032,0.000043,-0.000092,-0.000010,0.000003,0.000086,0.000081,0.000091,0.000081,0.000081,0.000069,0.000036,0.000010,0.000001,-0.000004,0.000014,0.000108,0.000040,0.000080,0.000088,0.000108,0.000129,0.000120,0.000109,0.000077,0.000093,0.000043,0.000062,0.000069,0.000074,0.000093,0.000074,0.000100,0.000056,0.000094,0.000059,-0.000016,-0.000029,-0.000018,-0.000023,0.000020,0.000075,0.000027,0.000047,-0.000018,0.000005,-0.000023,0.000037,-0.000038,0.000002,-0.000028,-0.000041,0.000005,0.000016,0.000040,0.000030,0.000046,0.000043,0.000046,0.000066,0.000048,0.000102,0.000039,0.000083,0.000079,0.000076,0.000001,0.000010,0.000013,0.000013,-0.000025,0.000044,-0.000009,-0.000023,-0.000006,-0.000036,-0.000080,-0.000021,-0.000048,-0.000058,-0.000077,-0.000011,-0.000010,-0.000050,-0.000027,-0.000017,-0.000066,0.000007,-0.000007,-0.000016,0.000037,-0.000037,-0.000044,-0.000048,-0.000008,0.000011,-0.000009,-0.000053,0.000013,-0.000026,-0.000010,0.000006,-0.000051,0.000002,0.000005,-0.000013,0.000033,0.000018,-0.000015,0.000002,-0.000012,-0.000032,-0.000024,0.000030,0.000003,-0.000030,-0.000020,0.000004,0.000018,-0.000007,0.000031,0.000013,0.000040,0.000024,-0.000002,-0.000004,0.000060,0.000096,0.000067,0.000078,0.000099,0.000098,0.000049,0.000063,0.000078,0.000097,0.000088,0.000072,0.000062,0.000091,0.000082,0.000070,0.000058,0.000076,0.000078,0.000055,0.000026,0.000050,0.000071,0.000036,0.000041,0.000038,0.000026,0.000028,0.000067,0.000048,0.000029,0.000066,0.000001,0.000028,0.000058,0.000019,0.000038,0.000028,0.000029,-0.000004,0.000017,0.000007,0.000026,0.000030,-0.000000,0.000049,0.000003,0.000004,-0.000008,0.000018,0.000023,0.000007,0.000036,0.000038,-0.000009,0.000006,-0.000001,0.000014,-0.000004,0.000013,-0.000012,-0.000013,0.000020,0.000015,0.000013,0.000020,0.000005,0.000042,0.000029,0.000004,0.000030,0.000035,0.000010,0.000028,0.000028,0.000052,0.000039,0.000025,0.000008,0.000042,0.000016,0.000010,0.000039,0.000031,0.000026,0.000040,0.000036,0.000039,0.000051,0.000041,0.000057,0.000038,0.000049,0.000057,0.000032,0.000030,0.000012,0.000042,0.000030,0.000038,0.000023,0.000047,0.000020,0.000027,0.000041,0.000027,0.000031,0.000039,0.000031,0.000030,0.000035,0.000017,0.000029,0.000005,0.000034,0.000020,0.000015,0.000027,-0.000002,0.000015,0.000020,0.000003,0.000020,0.000016,0.000022,0.000013,0.000011,0.000006},
        };
    }

    float[][][][] states0 = new float[1][2][256][2];
    float[][][][] states1 = new float[1][2][256][2];
    FileOutputStream outhandle0;
    FileOutputStream outhandle1;
    FileOutputStream stateHandle0;
    FileOutputStream stateHandle1;
    FileOutputStream fftHandle0;
    FileOutputStream fftHandle1;
    FileOutputStream ifftHandle0;
    FileOutputStream ifftHandle1;
    public void unitTest() throws IOException {
        outhandle0 = getHandle("/android_text_out.txt");
        outhandle1 = getHandle("/android_text_out1.txt");
        stateHandle0 = getHandle("/android_text_state0.txt");
        stateHandle1 = getHandle("/android_text_state1.txt");
        fftHandle0 = getHandle("/android_text_fft0.txt");
        fftHandle1 = getHandle("/android_text_fft1.txt");
        ifftHandle0 = getHandle("/android_text_ifft0.txt");
        ifftHandle1 = getHandle("/android_text_ifft1.txt");

        final Interpreter.Options tfLiteOptions = new Interpreter.Options();

        // load mode 1
//        MappedByteBuffer mode0 = loadModelFile(getAssets(), "dtln_aec_128_1.tflite");
        MappedByteBuffer mode0 = loadModelFile(mContext.getAssets(), "dtln_aec_512_1.tflite");
        Interpreter interpreter0 = new Interpreter(mode0, tfLiteOptions);
//        interpreter0.resizeInput();
//        tensor0.setTo(pcm0);

        // load mode 2
        MappedByteBuffer mode1 = loadModelFile(mContext.getAssets(), "dtln_aec_512_2.tflite");
        Interpreter interpreter1 = new Interpreter(mode1, tfLiteOptions);
        Tensor tensor0 = interpreter1.getInputTensor(0);
        Tensor tensor1 = interpreter1.getInputTensor(1);
        Tensor tensor2 = interpreter1.getInputTensor(2);
        Tensor out_tensor0 = interpreter1.getOutputTensor(0);
        Tensor out_tensor1 = interpreter1.getOutputTensor(1);

        for (int i = 0; i < Data.RecorderPcmIns.length; i++) {
            // process mode 1
            double[] recoderPcm = Data.RecorderPcmIns[i];
            double[] speakerPcm = Data.SpeakerPcmIns[i];
            float[] input = process0(interpreter0, recoderPcm, speakerPcm);

            // process mode 2
            float[] output = process1(interpreter1, input, speakerPcm);

            // check error
            for (int j = 0; j < Data.Out[i].length; j++) {
                float diff = (float) (Data.Out[i][j] - output[j]);
                if (diff > 1.0E-06) {
                    Log.e(TAG, "error: " + diff);
                }
            }
        }
    }

    private float[] process0(Interpreter interpreter, short[] recoderPcm, short[] speakerPcm) throws IOException {
        float[] RecorderIn = process00(recoderPcm);
        float[] SpeakerIn = process00(speakerPcm);

        float[] out = process01(interpreter, RecorderIn, SpeakerIn);
        return out;
    }

    private void readShape(Interpreter interpreter1) {
        Tensor tensor0 = interpreter1.getInputTensor(0); // [1, 1, 257]
        Tensor tensor1 = interpreter1.getInputTensor(1); // [1, 1, 257]
        Tensor tensor2 = interpreter1.getInputTensor(2); // states0 [1, 2, 256, 2]
        Tensor out_tensor0 = interpreter1.getOutputTensor(0); // [1, 1, 257]
        Tensor out_tensor1 = interpreter1.getOutputTensor(1); // states1 [1, 2, 256, 2]
    }

    float[] process0(Interpreter interpreter, double[] recoderPcm, double[] speakerPcm) throws IOException {
        readShape(interpreter);
        assert recoderPcm.length == 512: "recoderPcm 需要 512 的大小";
        assert speakerPcm.length == 512: "speakerPcm 需要 512 的大小";

        // 1: ads()
        FFTUtil.Complex[] rComplexs = new FFTUtil.Complex[512];
        for (int i = 0; i < recoderPcm.length; i++) {
            FFTUtil.Complex c = new FFTUtil.Complex();
            c.m_dlReal = (float) recoderPcm[i];
            rComplexs[i] = c;
        }
        float[] RecorderIn = process00(rComplexs);

        FFTUtil.Complex[] sComplexs = new FFTUtil.Complex[512];
        for (int i = 0; i < speakerPcm.length; i++) {
            FFTUtil.Complex c = new FFTUtil.Complex();
            c.m_dlReal = (float) speakerPcm[i];
            sComplexs[i] = c;
        }
        float[] SpeakerIn = process00(sComplexs);

        // 2: out_mask
        float[] out_mask = process01(interpreter, RecorderIn, SpeakerIn);

        // 3: rComplexs * out_mask
        rComplexs[0].m_dlReal = rComplexs[0].m_dlReal * out_mask[0];
        rComplexs[0].m_dlImag = rComplexs[0].m_dlImag * out_mask[0];
        for (int i = 1; i < out_mask.length - 1; i++) {
            rComplexs[i].m_dlReal = rComplexs[i].m_dlReal * out_mask[i];
            rComplexs[i].m_dlImag = rComplexs[i].m_dlImag * out_mask[i];

            rComplexs[512 - i].m_dlReal = rComplexs[i].m_dlReal;
            rComplexs[512 - i].m_dlImag = - rComplexs[i].m_dlImag;
        }
        rComplexs[256].m_dlReal = rComplexs[256].m_dlReal * out_mask[256];
        rComplexs[256].m_dlImag = rComplexs[256].m_dlImag * out_mask[256];

//        for (int i = 0; i < rComplexs.length - 1; i++) {
//            if (i > 256) {
//                rComplexs[i].m_dlReal = rComplexs[i].m_dlReal * out_mask[512 - i];
//                rComplexs[i].m_dlImag = rComplexs[i].m_dlImag * out_mask[512 - i];
//            } else {
//                rComplexs[i].m_dlReal = rComplexs[i].m_dlReal * out_mask[i];
//                rComplexs[i].m_dlImag = rComplexs[i].m_dlImag * out_mask[i];
//            }
//        }

        FFTUtil.ifft(rComplexs, 9);
        if (ifftHandle0 != null) {
            for (int j = 0; j < recoderPcm.length; j++) {
                ifftHandle0.write(String.valueOf(rComplexs[j].m_dlReal).getBytes());
                ifftHandle0.write(",".getBytes());
                ifftHandle0.write(String.valueOf(rComplexs[j].m_dlImag).getBytes());
                ifftHandle0.write(",".getBytes());
            }
            ifftHandle0.write("...\n".getBytes());
        }
        float[] out = new float[rComplexs.length];
        for (int i = 0; i < rComplexs.length; i++) {
            out[i] = rComplexs[i].m_dlReal;
        }
        return out;
    }

    float[] process1(Interpreter interpreter, float[] IfftRecorderIn, double[] speakerPcm) throws IOException {
        float[] SpeakerIn = new float[speakerPcm.length];
        for (int j = 0; j < SpeakerIn.length; j++) {
            SpeakerIn[j] = (float) speakerPcm[j];
        }
        return process10(interpreter, IfftRecorderIn, SpeakerIn);
    }

    private float[] process00(short[] recoderPcm) throws IOException {
        assert recoderPcm.length == 512: "recoderPcm 需要 512 的大小";
        FFTUtil.Complex[] complexs = new FFTUtil.Complex[512];
        for (int i = 0; i < recoderPcm.length; i++) {
            FFTUtil.Complex c = new FFTUtil.Complex();
            c.m_dlReal = recoderPcm[i];
            complexs[i] = c;
        }
        FFTUtil.fft(complexs, 9);
        // 记录 fft
        if (fftHandle0 != null) {
            for (int j = 0; j < recoderPcm.length; j++) {
                fftHandle0.write(String.valueOf(complexs[j].m_dlReal).getBytes());
                fftHandle0.write(",".getBytes());
                fftHandle0.write(String.valueOf(complexs[j].m_dlImag).getBytes());
                fftHandle0.write(",".getBytes());
            }
            fftHandle0.write("...\n".getBytes());
        }

        int size = 257;
        float[] RecorderIn = new float[size];
        for (int j = 0; j < size; j++) {
            RecorderIn[j] = (float) Math.sqrt(complexs[j].m_dlReal * complexs[j].m_dlReal + complexs[j].m_dlImag * complexs[j].m_dlImag);
        }
        // 记录 fft 后的 complex
        if (fftHandle0 != null) {
            fftHandle0.write("complex: \n".getBytes());
            for (int j = 0; j < RecorderIn.length; j++) {
                fftHandle0.write(String.valueOf(RecorderIn[j]).getBytes());
                fftHandle0.write(",".getBytes());
            }
            fftHandle0.write("...\n".getBytes());
        }
        return RecorderIn;
    }

    private float[] process00(FFTUtil.Complex[] complexs) throws IOException {
        // fft
        FFTUtil.fft(complexs, 9);
        // 记录 fft
        if (fftHandle0 != null) {
            for (int j = 0; j < complexs.length; j++) {
                fftHandle0.write(String.valueOf(complexs[j].m_dlReal).getBytes());
                fftHandle0.write(",".getBytes());
                fftHandle0.write(String.valueOf(complexs[j].m_dlImag).getBytes());
                fftHandle0.write(",".getBytes());
            }
            fftHandle0.write("...\n".getBytes());
        }

        int size = 257;
        float[] RecorderIn = new float[size];
        for (int j = 0; j < size; j++) {
            RecorderIn[j] = (float) Math.sqrt(complexs[j].m_dlReal * complexs[j].m_dlReal + complexs[j].m_dlImag * complexs[j].m_dlImag);
        }
        // 记录 fft 后的 complex
        if (fftHandle0 != null) {
            fftHandle0.write("complex: \n".getBytes());
            for (int j = 0; j < RecorderIn.length; j++) {
                fftHandle0.write(String.valueOf(RecorderIn[j]).getBytes());
                fftHandle0.write(",".getBytes());
            }
            fftHandle0.write("...\n".getBytes());
        }
        return RecorderIn;
    }

    private float[] process01(Interpreter interpreter, float[] RecorderIn, float[] SpeakerIn) throws IOException {
        int size = 257;
        float[][][] pcm0 = new float[1][1][size];
        float[][][] pcm1 = new float[1][1][size];
        float[][][] out_mask = new float[1][1][size];

        pcm0[0][0] = RecorderIn;
        pcm1[0][0] = SpeakerIn;

        Object[] inputs = new Object[]{pcm0, pcm1, states0};
        Map<Integer, Object> outputs = new HashMap<>();
        outputs.put(0, out_mask);
        outputs.put(1, states0);
        interpreter.runForMultipleInputsOutputs(inputs, outputs);

        // 记录 out
        if (outhandle0 != null) {
            for (int j = 0; j < size; j++) {
                outhandle0.write(String.valueOf(out_mask[0][0][j]).getBytes());
                outhandle0.write(",".getBytes());
            }
            outhandle0.write("...\n".getBytes());
        }

        // 记录 state
        if (stateHandle0 != null && stateHandle1 != null) {
            for (int j = 0; j < size; j++) {
                stateHandle0.write(String.valueOf(states0[0][0][j][0]).getBytes());
                stateHandle0.write(",".getBytes());
                stateHandle0.write(String.valueOf(states0[0][0][j][1]).getBytes());
                stateHandle0.write(",".getBytes());

                stateHandle1.write(String.valueOf(states0[0][1][j][0]).getBytes());
                stateHandle1.write(",".getBytes());
                stateHandle1.write(String.valueOf(states0[0][1][j][1]).getBytes());
                stateHandle1.write(",".getBytes());
            }
            stateHandle0.write("...\n".getBytes());
            stateHandle1.write("...\n".getBytes());
        }

        return out_mask[0][0];
    }

    private float[] process10(Interpreter interpreter, float[] IfftRecorderIn, float[] SpeakerIn) throws IOException {
        int size = 512;
        float[][][] param0 = new float[1][1][size];
        float[][][] param1 = new float[1][1][size];
        float[][][] out_mask = new float[1][1][size];

        param0[0][0] = IfftRecorderIn;
        param1[0][0] = SpeakerIn;

        Object[] inputs = new Object[]{param0, param1, states1};
        Map<Integer, Object> outputs = new HashMap<>();
        outputs.put(0, out_mask);
        outputs.put(1, states1);
        interpreter.runForMultipleInputsOutputs(inputs, outputs);

        // 记录 out
        if (outhandle1 != null) {
            for (int j = 0; j < size; j++) {
                outhandle1.write(String.valueOf(out_mask[0][0][j]).getBytes());
                outhandle1.write(",".getBytes());
            }
            outhandle1.write("...\n".getBytes());
        }

        return out_mask[0][0];
    }

    private FileOutputStream getHandle(String name) {
        File f = new File(mContext.getCacheDir().getAbsolutePath() + "/" + name);
        if (!f.exists()) {
            try {
                f.createNewFile();
            } catch (IOException e) {
                e.printStackTrace();
            }
        }
        try {
            return new FileOutputStream(f);
        } catch (FileNotFoundException e) {
            e.printStackTrace();
        }
        return null;
    }

    /** Memory-map the model file in Assets. */
    public static MappedByteBuffer loadModelFile(AssetManager assets, String modelFilename)
            throws IOException {
        AssetFileDescriptor fileDescriptor = assets.openFd(modelFilename);
        FileInputStream inputStream = new FileInputStream(fileDescriptor.getFileDescriptor());
        FileChannel fileChannel = inputStream.getChannel();
        long startOffset = fileDescriptor.getStartOffset();
        long declaredLength = fileDescriptor.getDeclaredLength();
        return fileChannel.map(FileChannel.MapMode.READ_ONLY, startOffset, declaredLength);
    }

    public static MappedByteBuffer loadModelFilePath(String modelFilePath)
            throws IOException {
        File file = new File(modelFilePath);
        FileInputStream inputStream = new FileInputStream(file);
        FileChannel fileChannel = inputStream.getChannel();
        long startOffset = 0;
        long declaredLength = file.length();
        return fileChannel.map(FileChannel.MapMode.READ_ONLY, startOffset, declaredLength);
    }
}