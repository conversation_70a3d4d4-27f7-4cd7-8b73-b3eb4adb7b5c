package co.timekettle.speech.ispeech.utils;

import java.util.Arrays;

public class ShortRingBuffer {
   private short[] array;
   private int readPos; // 读的起始位置
   private int writePos; // 写的起始位置
   private int remain; // 空余可写大小
   private int capacity;
   private boolean invalid = false;
   private int writed_count = 0;
   public ShortRingBuffer(int capacity) {
      this.capacity = capacity;
      this.remain = capacity;
      array = new short[capacity];
   }

   public synchronized void clear() {
      readPos = writePos = remain = 0;
      array = new short[capacity];
   }

   public synchronized void setInvalid(boolean invalid) {
      this.invalid = invalid;
   }

   public synchronized boolean invalid() {
      return invalid;
   }

   public synchronized int writedCount() {
      return writed_count;
   }

   public short[] toShortArray() {
      short[] copy = new short[readable()];
      this.read(copy);
      return copy;
   }
   public synchronized int remaining() {
      return remain;
   }

   public synchronized int readable() {
      return array.length - remaining();
   }

   private synchronized int reCapacity(int need) {
      if (need <= array.length) {
         return array.length;
      }
      int newCap = array.length;
      while (newCap < need) {
         newCap *= 2;
      }
      int addition = newCap - array.length; // 添加多少空间
      short[] temp = new short[newCap];
      if (readPos == writePos) {
         if (remain == 0) {
            int length = array.length - readPos;
            System.arraycopy(array, readPos, temp, 0, length);
            System.arraycopy(array, 0, temp, length, writePos);
            writePos = length + writePos;
            readPos = 0;
            array = temp;
         } else {
            writePos = 0;
            readPos = 0;
            array = temp;
//                throw new IllegalStateException(String.format("state error, don't reCapacity when buffer is empty, readPos = %s, writePos = %s, remain = %s", readPos, writePos, remain));
         }
      } else if (readPos < writePos) {
         int length = readable();
         System.arraycopy(array, readPos, temp, 0, length);
         readPos = 0;
         writePos = readPos + length;
         array = temp;
      } else {
         int length = array.length - readPos;
         System.arraycopy(array, readPos, temp, 0, length);
         System.arraycopy(array, 0, temp, length, writePos);
         writePos = length + writePos;
         readPos = 0;
         array = temp;
      }
      remain = remain + addition;
      return array.length;
   }

   public synchronized int write(short[] data) {
      return write(data, 0, data.length);
   }

   public synchronized int write(short[] data, int offset, int length) {
      writed_count = writed_count + length;
      int pre = this.readable();
      if (data == null || data.length == 0 || length == 0) return 0;
      if (offset < 0 || length < 0 || (offset + length > data.length)) {
         throw new IllegalArgumentException(String.format("params error , offset = %s,length = %s,data.length = %s", offset, length, data.length));
      }
      int remaining = remaining();
      if (length > remaining) {
         reCapacity(array.length + (length - remaining));
      }
      if (readPos <= writePos) {
         int wrote = Math.min(array.length - writePos, length);
         System.arraycopy(data, offset, array, writePos, wrote);
         writePos += wrote;
         if (length > wrote) {
            System.arraycopy(data, offset + wrote, array, 0, length - wrote);
            writePos = (length - wrote);
         }
      } else {
         System.arraycopy(data, offset, array, writePos, length);
         writePos += length;
      }
      remain -= length;

      return length;
   }

   public synchronized int peek(short[] buff) {
      return peek(buff, 0, buff.length);
   }

   public synchronized int peek(short[] buff, int offset, int length) {
      return read(buff, offset, length, true);
   }

   public synchronized int read(short[] buff) {
      return read(buff, 0, buff.length, false);
   }

   public synchronized int read(short[] buff, int offset, int length, boolean peek) {
      if (buff == null || buff.length == 0 || length == 0) return 0;
      if (offset < 0 || length < 0 || offset >= buff.length) {
         throw new IllegalArgumentException(String.format("params error , offset = %s,length = %s,buff.length = %s", offset, length, buff.length));
      }
      length = Math.min(readable(), length); // 当前缓存实际可读长度
      length = Math.min(buff.length - offset, length); // buff 在 offset 位置后实际可读长度
      if (length > 0) {
         if (readPos < writePos) {
            System.arraycopy(array, readPos, buff, offset, length);
            if (!peek) {
               readPos += length;
            }
         } else {
            int read = Math.min(array.length - readPos, length);
            System.arraycopy(array, readPos, buff, offset, read);
            if (!peek) {
               readPos += read;
            }
            if (read < length) {
               System.arraycopy(array, 0, buff, offset + read, length - read);
               if (!peek) {
                  readPos = (length - read);
               }
            }
         }
      }
      if (!peek) {
         remain += length;
      }
      return length;
   }

   public synchronized String dump() {
      String msg = "array.length = " + array.length + ",";
      msg += ("readPos = " + readPos + ",");
      msg += ("writePos = " + writePos + ",");
      msg += ("readable = " + readable() + ",");
      msg += ("remaining = " + remaining());
      return msg;
   }

   public static void main(String[] args) {
      if (true) {
         final ShortRingBuffer buffer = new ShortRingBuffer(8);
         final short[] data = new short[]{0, 1, 2, 3, 4, 5, 6, 7};
         buffer.write(data);
         buffer.read(data, 0, 2, false);
         System.out.println("dump : " + buffer.dump());
         buffer.write(data, 0, 2);
         System.out.println("dump : " + buffer.dump());
         buffer.write(data, 0, 2);
         System.out.println("dump : " + buffer.dump());
         return;
      }

      final ShortRingBuffer buffer = new ShortRingBuffer(8);
      final short[] data = new short[]{0, 1, 2, 3, 4, 5, 6, 7, 8, 9};
      buffer.write(data);
      System.out.println("write data : " + Arrays.toString(data));
      System.out.println("dump : " + buffer.dump());
      for (int i = 0; i < 10; i++) {
         int readable = buffer.readable();
         System.out.println("i = " + i + ",readable = " + buffer.readable());
         if (i % 3 == 0) {
            short[] readBuff = new short[readable];
            buffer.read(readBuff);
            System.out.println("i = " + i + ",readBuff = " + Arrays.toString(readBuff));
            System.out.println("dump : " + buffer.dump());
         } else {
            short[] temp = data.clone();
            if (i % 2 == 0) {
               temp = data.clone().clone();
            }
            System.out.println("i = " + i + ",write data : " + Arrays.toString(temp));
            buffer.write(temp);
            System.out.println("dump : " + buffer.dump());
         }
      }
   }
}
