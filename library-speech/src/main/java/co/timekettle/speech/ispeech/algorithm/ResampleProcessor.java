package co.timekettle.speech.ispeech.algorithm;

public class ResampleProcessor {
    private final static int BL = 64;
    private final double[] filterB = {
        -0.003153335084774,-0.003590152237582, 0.005704447325486,  0.01723833354592,
                0.01363995299636,-0.001609694569001,-0.004806437223483, 0.006115905028161,
                0.00635826431921, -0.00651006356064,-0.005637588362203, 0.008721218472434,
                0.005552611229619, -0.01103503425125,-0.005017464671399,  0.01394594834814,
                0.004063166812484, -0.01744868313164,-0.002432006634085,  0.02171642772436,
                -0.0001826939763624, -0.02705475902571,  0.00428516836776,  0.03407289039768,
                -0.010885227617, -0.04420867492187,  0.02238728264573,  0.06153798771986,
                -0.04666820676854,  -0.1033830397675,   0.1335943615354,   0.4651678008514,
                0.4651678008514,   0.1335943615354,  -0.1033830397675, -0.04666820676854,
                0.06153798771986,  0.02238728264573, -0.04420867492187,   -0.010885227617,
                0.03407289039768,  0.00428516836776, -0.02705475902571,-0.0001826939763624,
                0.02171642772436,-0.002432006634085, -0.01744868313164, 0.004063166812484,
                0.01394594834814,-0.005017464671399, -0.01103503425125, 0.005552611229619,
                0.008721218472434,-0.005637588362203, -0.00651006356064,  0.00635826431921,
                0.006115905028161,-0.004806437223483,-0.001609694569001,  0.01363995299636,
                0.01723833354592, 0.005704447325486,-0.003590152237582,-0.003153335084774
    };

    private final float[] h = {
            0.0004f, 0.0005f, -0.0000f, -0.0005f, -0.0006f, 0.0000f, 0.0007f, 0.0008f, -0.0000f, -0.0010f, -0.0012f, -0.0000f, 0.0015f, 0.0017f, -0.0000f, -0.0021f, -0.0024f, 0.0000f, 0.0030f, 0.0033f, -0.0000f, -0.0040f, -0.0045f, 0.0000f, 0.0054f, 0.0059f, -0.0000f, -0.0072f, -0.0078f, 0.0000f, 0.0094f, 0.0103f, -0.0000f, -0.0123f, -0.0135f, 0.0000f, 0.0164f, 0.0181f, -0.0000f, -0.0224f, -0.0251f, 0.0000f, 0.0325f, 0.0376f, -0.0000f, -0.0538f, -0.0679f, 0.0000f, 0.1372f, 0.2752f, 0.3330f, 0.2752f, 0.1372f, 0.0000f, -0.0679f, -0.0538f, -0.0000f, 0.0376f, 0.0325f, 0.0000f, -0.0251f, -0.0224f, -0.0000f, 0.0181f, 0.0164f, 0.0000f, -0.0135f, -0.0123f, -0.0000f, 0.0103f, 0.0094f, 0.0000f, -0.0078f, -0.0072f, -0.0000f, 0.0059f, 0.0054f, 0.0000f, -0.0045f, -0.0040f, -0.0000f, 0.0033f, 0.0030f, 0.0000f, -0.0024f, -0.0021f, -0.0000f, 0.0017f, 0.0015f, -0.0000f, -0.0012f, -0.0010f, -0.0000f, 0.0008f, 0.0007f, 0.0000f, -0.0006f, -0.0005f, -0.0000f, 0.0005f, 0.0004f,
    };

    private double[] z = new double[BL];

    public ResampleProcessor() {
        for (int iz = 0; iz < BL; iz++)
        {
            z[iz] = 0;
        }
    }

    /**
     * 降采样
     * @param outdata 输出数据
     * @param indata 输入数据
     * @param ifsSrc 源采样率
     * @param ifsTgt 目标采样率
     * @param iframeLen 输入数据采样点数
     */
    public void resampleDown(short []outdata, short []indata, int ifsSrc, int ifsTgt, int iframeLen)
    {
        float sum;
        float srcRate;
        float th = 0;
        int j = 0;
        srcRate = ifsTgt * 1.0f / ifsSrc;
        for (int i = 0; i < iframeLen; i++)
        {

            for (int iz = BL - 2; iz >=0; iz--)
            {
                z[iz + 1] = z[iz];
            }
            z[0] = (float)indata[i];
            th += srcRate;
            if (th >= 1.0f)
            {
                th = th - 1.0f;
                sum = 0;
                for (int iz = 0; iz < BL; iz++)
                {
                    sum += z[iz] * filterB[iz];
                }
                outdata[j] = (short)(sum);
                j++;
            }
        }
    }

    public void process(short []outdata, short []indata, int ifsSrc, int ifsTgt, int iframeLen)
    {
        resampleDown(outdata, indata, ifsSrc, ifsTgt, iframeLen);
    }

    /**
     * 升采样
     * @param input_signal 输入
     * @param input_len 输入长度
     * @param output_signal 输出
     * @param output_len 第一位为输出有效数据长度
     */
    public void resampleUp(short[] input_signal, int input_len, short[] output_signal, int[] output_len) {
        int upsampled_len = input_len * 3;

        float[] x_upsampled = new float[upsampled_len]; // 创建一个长度为 upsampled_len 的浮点数数组

        int y_len = upsampled_len + 101 - 1;

        float[] y = new float[y_len]; // 创建一个长度为 y_len 的浮点数数组

        // 上采样
        for (int i = 0; i < input_len; i++) {
            x_upsampled[i * 3] = input_signal[i];
        }

        // 卷积
        for (int i = 0; i < upsampled_len; i++) {
            for (int j = 0; j < 101; j++) {
                y[i + j] += x_upsampled[i] * h[j]; // h[j] 代表滤波器的系数
            }
        }

        // 降采样
        for (int i = 0; i < upsampled_len; i++) {
            output_signal[i] = (short) y[i + (101 - 1) / 2]; // 将浮点数转换为短整型
        }
        output_len[0] = upsampled_len; // 设置输出长度为 upsampled_len

        // 释放内存
        // Java 中不需要手动释放内存，垃圾回收机制会自动处理
    }
    /**
     * 这个降采样算法，比上面的效果要好，也不会有音频丢失的问题，实测没问题
     *
     * @param outdata 输出数据
     * @param indata  输入数据
     * @param ifsSrc  源采样率
     * @param ifsTgt  目标采样率
     */
    public void resampleDownGPT(short[] outdata, short[] indata, int ifsSrc, int ifsTgt) {
        // 计算采样率比率
        double ratio = (double) ifsSrc / ifsTgt;
        // 计算输出数据的长度
        int outLength = (int) (indata.length / ratio);
        if (outdata.length < outLength - 1) {  // outdata的长度太短了，避免越界！
            return;
        }
        // 遍历输出数据数组
        for (int i = 0; i < outLength; i++) {
            // 计算输入数据的索引
            double srcIndex = i * ratio;
            // 获取输入数据的整数部分和小数部分
            int index = (int) srcIndex;
            double frac = srcIndex - index;
            // 线性插值计算输出数据
            if (index + 1 < indata.length) {
                outdata[i] = (short) ((1 - frac) * indata[index] + frac * indata[index + 1]);
            } else {
                outdata[i] = indata[index];
            }
        }
    }

}
