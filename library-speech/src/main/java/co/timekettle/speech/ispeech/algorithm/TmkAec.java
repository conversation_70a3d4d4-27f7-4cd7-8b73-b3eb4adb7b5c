package co.timekettle.speech.ispeech.algorithm;

import java.util.Arrays;

public class TmkAec {
    AudioAecJni aecjni;
    short[] prefix_smooth;
    short[] suffix_smooth;
    short[] empty; // 数量为 0 的空数据
    short[] debug_data; // 调试数据
    static final boolean DEBUG_MODE = false;
    int offset;
    TfLiteProcessor tflitePro;
    public TmkAec() {
        aecjni = new AudioAecJni();
        prefix_smooth = null;
        suffix_smooth = null;
        empty = new short[0];
        if (DEBUG_MODE) {
            debug_data = new short[AudioAecJni.SamplesInMode];
            Arrays.fill(debug_data, Short.MAX_VALUE);
        }
        offset = 0;
//        tflitePro = TfLiteProcessor.Factory(context);
    }

    public static int getSizePerRoutine() {
//        return TfLiteProcessor.SamplesInMode;
        return AudioAecJni.SamplesInMode;
    }

    public short[] process(short[] recoderPcm, short[] speakerPcm) {
        short[] output = aecjni.process(recoderPcm, speakerPcm);
        short[] ret;
        if (prefix_smooth == null) prefix_smooth = new short[AudioAecJni.SamplesInMode * 6]; // 第 0 帧与后续 aec 处理的帧做三角滤波, 共删除 6 帧(48ms)
        if (offset < prefix_smooth.length) {
            System.arraycopy(recoderPcm, 0, prefix_smooth, offset, recoderPcm.length);
            ret = DEBUG_MODE ? debug_data : empty;
        } else if (offset == prefix_smooth.length) {
            // 三角滤波
            for (int i = 0; i < output.length; i++) {
                prefix_smooth[i] = (short) (((output.length - i - 1.0f) / output.length) * prefix_smooth[i]);
            }
            for (int i = 0; i < output.length; i++) {
                output[i] = (short) (((i + 1.0f) / output.length) * output[i]);
            }
            for (int i = 0; i < output.length; i++) {
                output[i] = (short) (output[i] + prefix_smooth[i]);
            }
            suffix_smooth = output; // 延迟到下一帧返回
            ret = empty;
        } else {
            ret = suffix_smooth;
            suffix_smooth = output;
        }
        offset = offset + output.length;
        return ret;
    }

    public short[] process_end(short[] recoderPcm, short[] speakerPcm) {
        short[] output = recoderPcm; // aecjni.process(recoderPcm, speakerPcm);
        offset = offset + output.length;

        // 三角滤波
        for (int i = 0; i < output.length; i++) {
            suffix_smooth[i] = (short) (((output.length - i - 1.0f) / output.length) * suffix_smooth[i]);
        }
        for (int i = 0; i < output.length; i++) {
            output[i] = (short) (((i + 1.0f) / output.length) * output[i]);
        }
        for (int i = 0; i < output.length; i++) {
            output[i] = (short) (output[i] + prefix_smooth[i]);
        }
        if (DEBUG_MODE) {
            short[] ret = new short[output.length * 2];
            System.arraycopy(output, 0, ret, 0, output.length);
            System.arraycopy(debug_data, 0, ret, output.length, output.length);
            return ret;
        }
        return output;
    }

    public void reset() {
        aecjni.reset();
        prefix_smooth = null;
        suffix_smooth = null;
        offset = 0;
    }
}