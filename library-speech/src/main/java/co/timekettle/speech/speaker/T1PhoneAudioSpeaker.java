package co.timekettle.speech.speaker;

import android.content.Context;
import android.media.AudioFormat;
import android.media.AudioManager;
import android.media.AudioTrack;
import android.util.Log;

import co.timekettle.speech.ISpeechConstant;
import co.timekettle.speech.utils.BytesTrans;
import co.timekettle.speech.recorder.HeadsetSco;
import co.timekettle.speech.utils.AiSpeechLogUtil;

import static android.media.AudioManager.STREAM_VOICE_CALL;

public class T1PhoneAudioSpeaker extends AudioSpeakerBase {

    private static final String TAG = "T1PhoneAudioSpeaker";

    private Task<Long> curTask = null;

    public T1PhoneAudioSpeaker(String type, Context context) {
        super(ISpeechConstant.SPEAKER.T1PHONE.toString(), type, context);
    }

    @Override
    public void doPlay(Task<Long> task) {
        curTask = task;

        if (listener != null) {
            task.moduleName = name;
            task.speakerType = type;
            listener.onSpeakStart(task);
        }

        byte[] sound = task.sound;
        Object extData = task.extData;

        AiSpeechLogUtil.e(TAG, "Music 播放数据长度: " + sound.length);
        final AudioManager audioManager = (AudioManager) context.getSystemService(context.AUDIO_SERVICE);
//        final int currentVolume = audioManager.getStreamVolume(AudioManager.STREAM_MUSIC);

        boolean scoState = false;
        int streamType = AudioManager.STREAM_MUSIC;
        if (!HeadsetSco.shareInstance().invaild) {
            streamType = STREAM_VOICE_CALL;  // sco 下免提播放
            scoState = true;

            HeadsetSco.shareInstance().pauseBluetoothSco(context);
        }

        // 练了蓝牙耳机，声音设置为STREAM_VOICE_CALL，避免连接了蓝牙耳机的情况下，从耳机播放出来了。
//        if(HeadsetSco.shareInstance().isBlueToothHeadsetConnected()) streamType = STREAM_VOICE_CALL;

        // 开启外放
        audioManager.setSpeakerphoneOn(true); // 会导致 sco 关闭, 并且状态 audioManager.isBluetoothScoOn() 置为 false(oppo reno 和 小米 10T)
        AiSpeechLogUtil.e(TAG, "doPlay: " + "mode: " + audioManager.getMode() + " isSpeakerphoneOn: " + audioManager.isSpeakerphoneOn() + " isBluetoothScoOn: " + audioManager.isBluetoothScoOn() + " isBluetoothA2dpOn:" + audioManager.isBluetoothA2dpOn());

        boolean downLeft = extData != "Self"; // 手机上方为左通道, 下方为右通道
        float aginL = downLeft ? 0.f : 1;
        float aginR = downLeft ? 1 : 0.f;
        Log.d(TAG, "doPlay: speaker " + (downLeft ? "2" : "1") + " 播放");
        boolean useStereo = true; // 设置 true 使用立体音
        int sampleRateInHz = 16000;
        int channelConfig = useStereo ? AudioFormat.CHANNEL_OUT_STEREO : AudioFormat.CHANNEL_OUT_MONO;
        int audioFormat = AudioFormat.ENCODING_PCM_16BIT;

        int bufferSize = AudioTrack.getMinBufferSize(sampleRateInHz, channelConfig, audioFormat);
        final AudioTrack audioTrack = new AudioTrack(streamType, sampleRateInHz, channelConfig, audioFormat,
                bufferSize, AudioTrack.MODE_STREAM);
        if (audioTrack.getState() != AudioTrack.STATE_INITIALIZED) {
            return;
        }

        int audioLen = sound.length / 2;
        if (bufferSize > sound.length) {
            audioTrack.play();
            audioTrack.write(sound, 0, sound.length);
        } else {
            audioTrack.setNotificationMarkerPosition(audioLen);
            audioTrack.setPlaybackPositionUpdateListener(new AudioTrack.OnPlaybackPositionUpdateListener() {
                @Override
                public void onMarkerReached(AudioTrack audioTrack) {
                    AiSpeechLogUtil.e(TAG, "onMarkerReached 播放完成");
//                audioManager.setStreamVolume(AudioManager.STREAM_MUSIC, currentVolume, 0);
                    synchronized (lock) {
                        lock.notify();
                    }
                }

                @Override
                public void onPeriodicNotification(AudioTrack audioTrack) {
                    AiSpeechLogUtil.e(TAG, "onMarkerReached 播放中" + audioTrack.getPositionNotificationPeriod());

                }
            });

            audioTrack.play(); // 开始播放后再写入数据
            new Thread(() -> {
                if (useStereo) {
                    short[] monoSound = BytesTrans.getInstance().Bytes2Shorts(sound);
                    short[] stereoSound = new short[monoSound.length * 2];

                    for (int i = 0; i < monoSound.length; i++) {
                        stereoSound[i * 2]     = (short) (monoSound[i] * aginL);
                        stereoSound[i * 2 + 1] = (short) (monoSound[i] * aginR);
                    }
                    audioTrack.write(stereoSound, 0, stereoSound.length);
                } else {
                    audioTrack.write(sound, 0, sound.length);
                }
            }).start();

            synchronized (lock) {
                try {
                    lock.wait();
                } catch (InterruptedException e) {
                    e.printStackTrace();
                }
            }
        }

        audioTrack.stop();
        audioTrack.release();
        AiSpeechLogUtil.e(TAG, "Music 播放完成");

        // 关闭外放
        audioManager.setSpeakerphoneOn(false);
        if (scoState) {
            HeadsetSco.shareInstance().restartBluetoothSco(context, null);
        }

        if (listener != null) {
            task.moduleName = name;
            task.speakerType = type;
            listener.onSpeakEnd(task);
        }
        curTask = null;
    }

    public void stopWorker(long workerId) {
        super.stopWorker(workerId);

        if (curTask != null && curTask.id == workerId) {
            synchronized (lock) {
                lock.notify();
            }
            AiSpeechLogUtil.d(TAG, "stopWorker: 停止播放任务(当前) " + workerId);
        }
    }
}
