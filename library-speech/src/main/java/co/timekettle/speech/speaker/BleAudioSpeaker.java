package co.timekettle.speech.speaker;

import android.content.Context;

import org.greenrobot.eventbus.EventBus;

import co.timekettle.speech.ISpeechConstant;
import co.timekettle.speech.utils.AiSpeechLogUtil;

import java.util.HashMap;

public class <PERSON>leAudioSpeaker extends AudioSpeakerBase {

    private static final String TAG = "BleAudioSpeaker";

    private static final String BLE_EVENT_NAME_KEY = "BLE_EVENT_NAME_KEY"; // 消息名的 key, 也作为 EventBus 的 key, 消息实体为 Map
    private static final String BLE_EVENT_NAME_PLAY = "BLE_EVENT_NAME_PLAY"; // 消息名的 value, {BLE_EVENT_NAME_KEY: BLE_EVENT_NAME_PLAY}
    private static final String BLE_EVENT_NAME_PLAY_STOP = "BLE_EVENT_NAME_PLAY_STOP"; // 消息名的 value, {BLE_EVENT_NAME_KEY: BLE_EVENT_NAME_PLAY_STOP}

    private static final String BLE_EVENT_PLAY_TASKID_KEY = "BLE_EVENT_PLAY_TASKID_KEY"; // {BLE_EVENT_PLAY_TASKID_KEY: taskId}, 消息播放 taskId 的 key, 值为 taskId
    private static final String BLE_EVENT_PLAY_SOUND_KEY = "BLE_EVENT_PLAY_SOUND_KEY"; // {BLE_EVENT_PLAY_SOUND_KEY: new byte[]},
    private static final String BLE_EVENT_PLAY_DEVICES_KEY = "BLE_EVENT_PLAY_DEVICES_KEY"; // {BLE_EVENT_PLAY_DEVICES_KEY: [deviceId]]},
    private static final String BLE_EVENT_PLAY_EXCEPT_KEY = "BLE_EVENT_PLAY_EXCEPT_KEY";  // {BLE_EVENT_PLAY_EXCEPT_KEY: exceptDeviceId}, 除此设备相同角色外都需要播放

    private static final String BLE_EVENT_PLAY_BEGINCALLBACK_KEY = "BLE_EVENT_PLAY_BEGINCALLBACK_KEY";
    private static final String BLE_EVENT_PLAY_ENDCALLBACK_KEY = "BLE_EVENT_PLAY_ENDCALLBACK_KEY";

    public BleAudioSpeaker(String type, Context context) {
        super(ISpeechConstant.SPEAKER.BLE.toString(), type, context);
    }

    @Override
    public void stop() {
        super.stop();

        EventBus.getDefault().post(new HashMap<String, Object>(){{
            put(BLE_EVENT_NAME_KEY, BLE_EVENT_NAME_PLAY_STOP);
        }});
    }

    @Override
    public void doPlay(Task<Long> task) {
        task.beginCb = (iden) -> {
            synchronized (task) {
                if (!task.isStart) {
                    task.isStart = true;
                    AiSpeechLogUtil.d(TAG, task.chkey + " onSpeakStart: " + task.id);

                    if (listener != null) {
                        task.moduleName = name;
                        task.speakerType = type;
                        listener.onSpeakStart(task);
                    }
                }
            }
            return null;
        };
        task.endCb = (iden) -> {
            synchronized (task) {
                if (!task.isEnd) {
                    task.isEnd = true;
                    AiSpeechLogUtil.d(TAG, task.chkey + " onSpeakEnd: " + task.id);

                    if (listener != null) {
                        task.moduleName = name;
                        task.speakerType = type;
                        listener.onSpeakEnd(task);
                    }
                }
            }
            return null;
        };

        EventBus.getDefault().post(new HashMap<String, Object>(){{
            put(BLE_EVENT_NAME_KEY, BLE_EVENT_NAME_PLAY);
            put(BLE_EVENT_PLAY_TASKID_KEY, task.id);
            put(BLE_EVENT_PLAY_SOUND_KEY, task.sound);
            put(BLE_EVENT_PLAY_EXCEPT_KEY, task.extData);

            put(BLE_EVENT_PLAY_BEGINCALLBACK_KEY, task.beginCb);
            put(BLE_EVENT_PLAY_ENDCALLBACK_KEY, task.endCb);
        }});
    }

    public void stopWorker(long workerId) {
        AiSpeechLogUtil.e("BleAudioSpeaker", "stopWorker: " + workerId);

        EventBus.getDefault().post(new HashMap<String, Object>(){{
            put(BLE_EVENT_NAME_KEY, BLE_EVENT_NAME_PLAY_STOP);
            put(BLE_EVENT_PLAY_TASKID_KEY, workerId);
        }});
    }
}

