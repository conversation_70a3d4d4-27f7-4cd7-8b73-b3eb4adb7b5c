package co.timekettle.speech.speaker;

import android.content.Context;

import java.util.concurrent.LinkedBlockingQueue;
import java.util.function.Function;

import co.timekettle.speech.utils.BytesTrans;
import co.timekettle.speech.utils.AiSpeechLogUtil;
import co.timekettle.speech.jni.SoundStretchJni;
public abstract class AudioSpeakerBase {

    private static final String TAG = "AudioSpeakerBase";

    protected String name;
    protected String type;
    protected Context context;
    protected boolean isPlaying = false;
    protected Listener listener = null;
    protected final Object lock = new Object(); // 用于同步控制, 除 ble speaker 外
    protected LinkedBlockingQueue<Task> taskQueue = new LinkedBlockingQueue<>();

    protected SoundStretchJni soundstretch = new SoundStretchJni();
    public float tempo = 1.0f; // 倍速播放, 支持 0.5 - 3.0

    public AudioSpeakerBase(String name, String type, Context context) {
        this.name = name;
        this.type = type;
        this.context = context;

        startConsumer();
    }

    public String getName() {
        return name;
    }

    public String getType() {
        return type;
    }

    public void setListener(Listener listener) {
        this.listener = listener;
    }

    protected abstract void doPlay(Task<Long> task);

//    public void play(final String chkey, final long session, final byte[] sound, final Object extData) {
//        try {
//            taskQueue.put(new Task(chkey, session, sound, extData));
//        } catch (InterruptedException e) {
//            e.printStackTrace();
//        }
//    }

    public void play(Task<Long> task) {
        try {
            taskQueue.put(task);
        } catch (InterruptedException e) {
            e.printStackTrace();
        }
    }

    public void stop() {
        AiSpeechLogUtil.d(TAG, name + " stop 停止播放");
        taskQueue.clear();

        synchronized (lock) {
            if (isPlaying) {
                isPlaying = false;
                AiSpeechLogUtil.d(TAG, "clear lock: " + lock);
                lock.notify();
            }
        }
    }

    public void stopWorker(long workerId) {
        for (Task<Long> task : taskQueue) {
            if (task.id == workerId) {
                task.invalid = true;
                AiSpeechLogUtil.d(TAG, "stopWorker: 停止播放任务(队列中) " + workerId);
            }
        }
    }

    public void destroy() {
        AiSpeechLogUtil.d(TAG, "destroy 停止播放");
        taskQueue.clear();

        synchronized (lock) {
            if (isPlaying) {
                isPlaying = false;
                AiSpeechLogUtil.d(TAG, "clear lock: " + lock);
                lock.notify();
                try {
                    taskQueue.put(new Task()); // FIXME: 2020/10/15 使得循环退出吗
                } catch (InterruptedException e) {
                    e.printStackTrace();
                }
            }
        }
    }

    private void startConsumer() {
         Thread playThread = new Thread(() -> {
            while (true) {
                try {
                    Task task = taskQueue.take();
                    if (task.invalid) continue;
                    if (task.sound.length == 0) {
                        break; // FIXME: 2020/10/15 若声音长度为 0, 不应该退出应该跳过循环?
                    }

                    isPlaying = true; // ble speaker 指正在发送数据
                    if (tempo > 0) {
                        short[] output = soundstretch.stretch(BytesTrans.getInstance().Bytes2Shorts(task.sound), tempo);
                        task.sound = BytesTrans.getInstance().Shorts2Bytes(output);
                    }
                    doPlay(task); // 除 ble speaker 外是同步执行, ble speaker 是发送数据到具体设备对象
                    isPlaying = false; // ble speaker 指结束发送数据

                } catch (InterruptedException e) {
                    e.printStackTrace();
                }
            }
            AiSpeechLogUtil.d(TAG, "销毁 speaker: " + name);
        });
        playThread.setName("Tmk-Play-" + name);
        playThread.start();
    }

    public static class Task<T> {
        public String moduleName; // 处理模块
        public String speakerType; // 处理模块
        public T id;
        public String chkey;
        public byte[] sound;
        public Object extData = null;
        public Object userData = null;

        public Function<T, Object> beginCb = null;
        public Function<T, Object> endCb = null;

        public boolean isStart = false;
        public boolean isEnd = false;

        public boolean invalid = false;

        public Task() {
            this.sound = new byte[0];
        }

        public Task(String chkey, T id, byte[] sound, Object extData) {
            this.chkey = chkey;
            this.id = id;
            this.sound = sound;
            this.extData = extData;
        }
    }

    public interface Listener {
//        void onSpeakStart(final String moduleName, final String chkey, long session, String speakerType, Object extData);
//
//        void onSpeakEnd(final String moduleName, final String chkey, long session, String speakerType, Object extData);

        void onSpeakStart(Task<Long> task);
        void onSpeakEnd(Task<Long> task);
    }
}