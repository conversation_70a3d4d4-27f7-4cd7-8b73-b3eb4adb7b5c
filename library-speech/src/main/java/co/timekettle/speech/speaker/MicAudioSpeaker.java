package co.timekettle.speech.speaker;

import static android.media.AudioManager.STREAM_VOICE_CALL;

import android.content.Context;
import android.media.AudioFormat;
import android.media.AudioManager;
import android.media.AudioTrack;
import android.os.Build;

import java.util.ArrayList;
import java.util.Arrays;

import co.timekettle.speech.ISpeechConstant;
import co.timekettle.speech.recorder.HeadsetSco;
import co.timekettle.speech.utils.AiSpeechLogUtil;

public class MicAudioSpeaker extends AudioSpeakerBase {

    private static final String TAG = "MicAudioSpeaker";

    // 解决某些机型外放模式丢字的问题（解决办法：加1-2秒的静音）
    private ArrayList<String> listDelayPlayDevice = new ArrayList<>(Arrays.asList("sm-f711n", "moto g stylus 5g (2022)",
            "nth-an00", "xq-bc72", "xq-at52", "xq-au52", "xq-bt52", "xq-as72", "sm-s908u", "sm-s908u1", "sm-s908e", "sm-f926n",
            "xq-bq72", "xq-ct72", "xq-cc72", "xq-at42", "xq-bt44", "2107113sg", "sm-a326u", "cph2173", "pixel 4a", "m2101k6g",
            "lm-v600", "sm-n986n", "v2185a", "220333qny", "m2102j20sg", "m2012k11c", "pixel 7", "oneplus a600", "sm-s901n",
            "lm-v500", "lm-v500n", "lm-v500em", "lm-v500xm", "lm-v450pm", "lm-v450", "sm-g9910", "xiaomi-2210132c"));

    private Task<Long> curTask = null;

    public MicAudioSpeaker(String type, Context context) {
        super(ISpeechConstant.SPEAKER.PHONE.toString(), type, context);
    }

    @Override
    public void doPlay(Task<Long> task) {
        curTask = task;

        if (listener != null) {
            task.moduleName = name;
            task.speakerType = type;
            listener.onSpeakStart(task);
        }
        byte[] sound;
//        if (listDelayPlayDevice.contains(Build.MODEL.toLowerCase())) {
//            //新数组 前面补18000个静音，后面补12000个静音
////            AiSpeechLogUtil.d(TAG, "在兼容性列表里，补静音后再播放");
//            byte[] newData = new byte[task.sound.length + 30000];
//            System.arraycopy(task.sound, 0, newData, 18000, task.sound.length);
//            sound = newData;
//        } else {
            sound = task.sound;
//        }
        Object extData = task.extData;

        AiSpeechLogUtil.e(TAG, "Music MicSpeaker播放数据长度: " + sound.length + " 机型：" + Build.MODEL.toLowerCase());
        final AudioManager audioManager = (AudioManager) context.getSystemService(Context.AUDIO_SERVICE);
//        final int currentVolume = audioManager.getStreamVolume(AudioManager.STREAM_MUSIC);

        boolean scoState = false;
        int streamType = AudioManager.STREAM_MUSIC;
        if (!HeadsetSco.shareInstance().invaild) {
            streamType = STREAM_VOICE_CALL;  // sco 下免提播放
            scoState = true;

            HeadsetSco.shareInstance().pauseBluetoothSco(context);
        }

        // 练了蓝牙耳机，声音设置为STREAM_VOICE_CALL，避免连接了蓝牙耳机的情况下，从耳机播放出来了。
//        if(HeadsetSco.shareInstance().isBlueToothHeadsetConnected()) streamType = STREAM_VOICE_CALL;

        // 开启外放
        audioManager.setSpeakerphoneOn(true); // 会导致 sco 关闭, 并且状态 audioManager.isBluetoothScoOn() 置为 false(oppo reno 和 小米 10T)
        AiSpeechLogUtil.e(TAG, "doPlay: " + "mode: " + audioManager.getMode() + " isSpeakerphoneOn: " + audioManager.isSpeakerphoneOn() + " isBluetoothScoOn: " + audioManager.isBluetoothScoOn() + " isBluetoothA2dpOn:" + audioManager.isBluetoothA2dpOn());

        int sampleRateInHz = 16000;
        int channelConfig = AudioFormat.CHANNEL_OUT_MONO;
        int audioFormat = AudioFormat.ENCODING_PCM_16BIT;

//        int maxVolume = audioManager.getStreamMaxVolume(streamType);
//        audioManager.setStreamVolume(streamType, 0, 0);
        int bufferSize = AudioTrack.getMinBufferSize(sampleRateInHz, channelConfig, audioFormat);

        final AudioTrack audioTrack = new AudioTrack(streamType, sampleRateInHz, channelConfig, audioFormat,
                bufferSize, AudioTrack.MODE_STREAM);
        if (audioTrack.getState() != AudioTrack.STATE_INITIALIZED) {
            return;
        }

        int audioLen = sound.length / 2;
        if (bufferSize > sound.length) {
            audioTrack.play();
            audioTrack.write(sound, 0, sound.length);
        } else {
            audioTrack.setNotificationMarkerPosition(audioLen);
            audioTrack.setPlaybackPositionUpdateListener(new AudioTrack.OnPlaybackPositionUpdateListener() {
                @Override
                public void onMarkerReached(AudioTrack audioTrack) {
                    AiSpeechLogUtil.e(TAG, "onMarkerReached 播放完成");
//                audioManager.setStreamVolume(AudioManager.STREAM_MUSIC, currentVolume, 0);
                    synchronized (lock) {
                        lock.notify();
                    }
                }

                @Override
                public void onPeriodicNotification(AudioTrack audioTrack) {
                    AiSpeechLogUtil.e(TAG, "onMarkerReached 播放中" + audioTrack.getPositionNotificationPeriod());

                }
            });

            audioTrack.play();

            new Thread(new Runnable() {
                @Override
                public void run() {
                    audioTrack.write(sound, 0, sound.length);
                }
            }).start();

            synchronized (lock) {
                try {
                    lock.wait();
                } catch (InterruptedException e) {
                    e.printStackTrace();
                }
            }
        }

        audioTrack.stop();
        audioTrack.release();
        AiSpeechLogUtil.e(TAG, "Music 播放完成");

        // 关闭外放
        audioManager.setSpeakerphoneOn(false);
        if (scoState) {
            HeadsetSco.shareInstance().restartBluetoothSco(context, null);
        }

        if (listener != null) {
            task.moduleName = name;
            task.speakerType = type;
            listener.onSpeakEnd(task);
        }
        curTask = null;
    }

    @Override
    public void stopWorker(long workerId) {
        super.stopWorker(workerId);

        if (curTask != null && curTask.id == workerId) {
            synchronized (lock) {
                lock.notify();
            }
            AiSpeechLogUtil.d(TAG, "stopWorker: 停止播放任务(当前) " + workerId);
        }
    }
}
