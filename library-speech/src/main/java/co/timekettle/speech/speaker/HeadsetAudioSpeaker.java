package co.timekettle.speech.speaker;

import android.content.Context;
import android.media.AudioFormat;
import android.media.AudioManager;
import android.media.AudioTrack;

import co.timekettle.speech.ISpeechConstant;
import co.timekettle.speech.utils.BytesTrans;
import co.timekettle.speech.utils.AiSpeechLogUtil;

public class HeadsetAudioSpeaker extends AudioSpeakerBase {
    private static final String TAG = "HeadsetAudioSpeaker";

    private Task<Long> curTask = null;

    public HeadsetAudioSpeaker(String type, Context context) {
        super(ISpeechConstant.SPEAKER.HEADSET.toString(), type, context);
    }

    @Override
    public void doPlay(Task<Long> task) {
        curTask = task;

        if (listener != null) {
            task.moduleName = name;
            task.speakerType = type;
            listener.onSpeakStart(task);
        }
        byte[] sound = task.sound;
        Object extData = task.extData;

        final AudioManager audioManager = (AudioManager) context.getSystemService(context.AUDIO_SERVICE);

        // 关闭扬声器
        audioManager.setSpeakerphoneOn(false);

        AiSpeechLogUtil.d(TAG, "doPlay: " + "mode: " + audioManager.getMode() + " isSpeakerphoneOn: " + audioManager.isSpeakerphoneOn() + " isBluetoothScoOn: " + audioManager.isBluetoothScoOn() + " isBluetoothA2dpOn:" + audioManager.isBluetoothA2dpOn());
        if (audioManager.isBluetoothScoOn()) {
            // 华为和荣耀的手机不设置
//            String DeviceBrand =  android.os.Build.BRAND.toLowerCase();
//            if (DeviceBrand.contains("huawei") || DeviceBrand.contains("honor")) {
//                //            audioManager.setMode(AudioManager.MODE_IN_COMMUNICATION);
//                //            audioManager.setBluetoothScoOn(true);
//            } else {
//                audioManager.setMode(AudioManager.MODE_IN_COMMUNICATION);
//                audioManager.setBluetoothScoOn(true);
//            }
            AiSpeechLogUtil.d(TAG, "doPlay: HFP 方式播放");
            play(AudioManager.STREAM_VOICE_CALL, sound, audioManager);
        }
        else {
            AiSpeechLogUtil.d(TAG, "doPlay: A2DP 方式播放");
//            audioManager.setMode(AudioManager.MODE_IN_COMMUNICATION);
//            audioManager.setMode(AudioManager.MODE_NORMAL);
            play(AudioManager.STREAM_MUSIC, sound, audioManager);
        }

        if (listener != null) {
            task.moduleName = name;
            task.speakerType = type;
            listener.onSpeakEnd(task);
        }
        curTask = null;
    }

    private void play(int speakerType, final byte[] sound, final AudioManager audioManager) {
        AiSpeechLogUtil.d(TAG, "M2 播放数据长度: " + sound.length);

        boolean downLeft = false;
        float aginL = downLeft ? 0.1f : 1;
        float aginR = downLeft ? 1 : 0.1f;
        boolean useStereo = false; // 设置 true 使用立体音
        int sampleRateInHz = 16000;
        int channelConfig = useStereo ? AudioFormat.CHANNEL_OUT_STEREO : AudioFormat.CHANNEL_OUT_MONO;
        int audioFormat = AudioFormat.ENCODING_PCM_16BIT;

        int bufferSize = AudioTrack.getMinBufferSize(sampleRateInHz, channelConfig, audioFormat);
        final AudioTrack audioTrack = new AudioTrack(speakerType, sampleRateInHz, channelConfig, audioFormat,
                bufferSize, AudioTrack.MODE_STREAM);

        // 设置播放触发完成回调的条件
        int nSample = sound.length / 2;
        audioTrack.setNotificationMarkerPosition(nSample);
        audioTrack.setPlaybackPositionUpdateListener(new AudioTrack.OnPlaybackPositionUpdateListener() {
            @Override
            public void onMarkerReached(AudioTrack audioTrack) {
                synchronized (lock) {
                    lock.notify();
                }
            }

            @Override
            public void onPeriodicNotification(AudioTrack audioTrack) {

            }
        });

        if (audioTrack.getState() != AudioTrack.STATE_INITIALIZED) {
            return;
        }
        audioTrack.play(); // 开始播放后再写入数据
        new Thread(new Runnable() {
            @Override
            public void run() {
                if (useStereo) {
                    short[] monoSound = BytesTrans.getInstance().Bytes2Shorts(sound);
                    short[] stereoSound = new short[monoSound.length * 2];

                    for (int i = 0; i < monoSound.length; i++) {
                        stereoSound[i * 2]     = (short) (monoSound[i] * aginL);
                        stereoSound[i * 2 + 1] = (short) (monoSound[i] * aginR);
                    }
                    audioTrack.write(stereoSound, 0, stereoSound.length);
                } else {
                    audioTrack.write(sound, 0, sound.length);
                }
            }
        }).start();

        synchronized (lock) {
            try {
                lock.wait();
            } catch (InterruptedException e) {
                e.printStackTrace();
            }
        }

        audioTrack.stop();
        audioTrack.release();
        AiSpeechLogUtil.d(TAG, "M2 播放完成");
    }

    public void stopWorker(long workerId) {
        super.stopWorker(workerId);

        if (curTask != null && curTask.id == workerId) {
            synchronized (lock) {
                lock.notify();
            }
            AiSpeechLogUtil.d(TAG, "stopWorker: 停止播放任务(当前) " + workerId);
        }
    }
}
