package co.timekettle.speech;

import android.content.Context;

import com.damly.ispeech.Mic4SepJni;
import com.damly.speech.UsbAudioRecord;
import co.timekettle.speech.recorder.AudioRecorderBase;
import co.timekettle.speech.utils.AiSpeechLogUtil;
import co.timekettle.speech.save.RecordClient;
import com.hian.HianAudioSep;
import com.hian.jni.AudioProJni;
import com.hian.main.TypeUtils;

import java.util.Map;


/**
 * Created by timekettle on 2018/7/9.
 */

public class UsbAudioRecorder extends AudioRecorderBase {
    public static final String TAG = "UsbVoiceRecorder";

    private UsbAudioRecord mAudioRecord;

    private int iFrontNum = 10;
    private int iEndNum = 22;
    private float fFrontThs = 5.0f;
    private float fEndThs = 2.0f;
    private float fThres = 3.5f;
    private int iAngleLoc = AudioProJni.getInstance().ABSCISSA;
    private int iBwParam = 14;
    private int iNrFlag = 1;
    private int iGain = 6;

    private int iOrdinate = Mic4SepJni.getInstance().ORDINATE_DN;
    private int iAnrFlag = 1;
    private float fAnrDepth = 20.0f;
    private float fPhiThold = 90.0f;     /*    p_fPhiThold  方位角阈值 (60.0~90.0 默认90.0)                      */
    private float fPhiAlpha = 0.8f;      /*    p_fPhiAlpha  方位角差值平滑因子 (0~1 默认0.8)                     */
    private int iPhiModel = 1;          /*    p_iPhiModel  方位角差值计算模式（0-线性模式/1-三角模式 默认0）    */

    private int nSamplePerFrame = 256; // 单通道帧数
    private int iReadCount = 4;
    private int iChannelNumber = 4;
    private int nFrameSize = nSamplePerFrame * iChannelNumber;
    private int bFrameSize = nFrameSize * 2;
    private int bReadSize = bFrameSize * iReadCount;
    private int nCacheSize = ((nFrameSize) / iChannelNumber) * iReadCount;

    private TestRecorder sourceRecorder;
    private TestRecorder targetRecorder;

    public UsbAudioRecorder(Context context) {
        super(context, ISpeechConstant.RECORDER.USB.toString());

        mAudioRecord = new UsbAudioRecord(context, bReadSize);
    }

    @Override
    public void setDropSampleCount() {
        dropSampleCount = 5;
    }

    @Override
    public void addChannel(final Map<String, Object> options) {
        options.put(AudioChannelOptions.BYTE_PER_PACKET, nSamplePerFrame * 2);
        super.addChannel(options);
    }

    public void setOptions(final Map<String, Object> options) {
        if (options == null) {
            return;
        }

        if (options.containsKey("iAngleLoc")) iAngleLoc = (int) options.get("iAngleLoc");

//        if (iAngleLoc == AudioProJni.getInstance().ORDINATE) {
//            if (options.containsKey("iOrdinate")) iOrdinate = (int) options.get("iOrdinate");
//            if (options.containsKey("fPhiThold")) fPhiThold = (float) options.get("fPhiThold");
//            if (options.containsKey("fPhiAlpha")) fPhiAlpha = (float) options.get("fPhiAlpha");
//            if (options.containsKey("iPhiModel")) iPhiModel = (int) options.get("iPhiModel");
//
//            int ret = Mic4SepJni.getInstance().SetLsPro(iOrdinate, iBwParam, iAnrFlag, fAnrDepth, iGain, fPhiThold, fPhiAlpha, iPhiModel);
//            AiSpeechLogUtil.e(TAG, "SetLsPro ret:" + ret + ", iOrdinate:" + iOrdinate + ", iBwParam:" + iBwParam + ", iAnrFlag:" + iAnrFlag + ", fAnrDepth:" + fAnrDepth + ", iGain:" + iGain + ", fPhiThold:" + fPhiThold + ", fPhiAlpha:" + fPhiAlpha + ", iPhiModel:" + iPhiModel);
//        } else {
            if (options.containsKey("iFrontNum")) iFrontNum = (int) options.get("iFrontNum");
            if (options.containsKey("iEndNum")) iEndNum = (int) options.get("iEndNum");
            if (options.containsKey("fFrontThs")) fFrontThs = (float) options.get("fFrontThs");
            if (options.containsKey("fEndThs")) fEndThs = (float) options.get("fEndThs");
            if (options.containsKey("fThres")) fThres = (float) options.get("fThres");
            if (options.containsKey("iBwParam")) iBwParam = (int) options.get("iBwParam");
            if (options.containsKey("iNrFlag")) iNrFlag = (int) options.get("iNrFlag");
            if (options.containsKey("iGain")) iGain = (int) options.get("iGain");

            boolean ret = HianAudioSep.getInstance().setParams(iFrontNum, fFrontThs, iEndNum, fEndThs, iAngleLoc, iBwParam, iNrFlag, iGain);
            AiSpeechLogUtil.e(TAG, "set: SetHianSep:" + (ret ? "true" : "false") + "\n" + options.toString());
//        }
    }

    @Override
    public void start(Map<String, Object> options) {
        super.start(options);

        this.setOptions(options);

        // if (iAngleLoc == AudioProJni.getInstance().ORDINATE) {
        //     short[] verifyData = new short[bFrameSize / 2];
        //     Arrays.fill(verifyData, (short) 10000);
        //     if (Mic4SepJni.getInstance().InitLsPro(verifyData, 4, nSamplePerFrame + 1) != 1) {
        //         AiSpeechLogUtil.e(TAG, "InitLsPro Error!");
        //         return;
        //     } else {
        //         AiSpeechLogUtil.e(TAG, "InitLsPro success");
        //     }

        //     iBwParam = 2;
        //     iAnrFlag = 1;
        //     fAnrDepth = 20;
        //     iGain = 1;
        //     int ret = Mic4SepJni.getInstance().SetLsPro(iOrdinate, iBwParam, iAnrFlag, fAnrDepth, iGain, fPhiThold, fPhiAlpha, iPhiModel);
        //     AiSpeechLogUtil.e(TAG, "SetLsPro ret:" + ret + ", iOrdinate:" + iOrdinate + ", iBwParam:" + iBwParam + ", iAnrFlag:" + iAnrFlag + ", fAnrDepth:" + fAnrDepth + ", iGain:" + iGain + ", fPhiThold:" + fPhiThold + ", fPhiAlpha:" + fPhiAlpha + ", iPhiModel:" + iPhiModel);

        // } else {
            if (!HianAudioSep.getInstance().init()) {
                AiSpeechLogUtil.e(TAG, "HianAudioSep init Error!");
                return;
            }
//        }

        if (isWorking) {
            AiSpeechLogUtil.e(TAG, "已经在工作, 不在开启");
            return ;
        }
        isWorking = true;

        AiSpeechLogUtil.e(TAG, "声道阈值: " + fThres);
        AiSpeechLogUtil.e(TAG, "音频角度: " + (iAngleLoc == AudioProJni.getInstance().ORDINATE ? "90/270(左右)" : (iAngleLoc == AudioProJni.getInstance().ABSCISSA ? "0/180(上下)" : "上下左右")));
        AiSpeechLogUtil.e(TAG, "波束宽度: " + iBwParam);
        AiSpeechLogUtil.e(TAG, "增益因子: " + iGain);


        AiSpeechLogUtil.e(TAG, "----> 开始录音");
        if (this.writeToFile) {
            sourceRecorder = new TestRecorder(context, "TK_Record", null, name + "-source", true); // 此处设置录音文件开关
            targetRecorder = new TestRecorder(context, "TK_Record", null, name + "-target", true); // 此处设置录音文件开关
        }

        if (options != null && options.containsKey(AudioRecordOptions.RECORD_AUDIO_FILENAME)) {
            int nChannel = 0;
            if (iAngleLoc == AudioProJni.getInstance().ABSCISSA || iAngleLoc == AudioProJni.getInstance().ORDINATE) {
                nChannel = 2;
            } else if (iAngleLoc == AudioProJni.getInstance().FULLAXIS) {
                nChannel = 4;
            }

            String fileName = (String) options.get(AudioRecordOptions.RECORD_AUDIO_FILENAME);
            RecordClient.getInstance().start(context, fileName, nChannel, true);
        }
        mAudioRecord.startRecording(new UsbAudioRecord.RecordListener() {
            @Override
            public void onRecord(byte[] stereoBuffer, byte[] leftBuffer, byte[] rightBuffer) {
                if (paused) {
//                    AiSpeechLogUtil.e(TAG, "onRecord: is pause");
                    return;
                }

                if (dropSampleCount > 0) {
                    dropSampleCount--;
                    AiSpeechLogUtil.e(TAG, "onRecord: mDropSampleCount: " + dropSampleCount);
                    return;
                }

                if (UsbAudioRecorder.this.writeToFile) sourceRecorder.write(stereoBuffer);

                byte[] bSrcData = new byte[bFrameSize];    // 每帧读取的数据
                short[] nSepData = new short[nFrameSize];
                short[] nVadFlag = new short[4];
                float[] fEngValue = new float[4];

                short[] sCh1Data = new short[nCacheSize];
                short[] sCh2Data = new short[nCacheSize];
                short[] sCh3Data = new short[nCacheSize];
                short[] sCh4Data = new short[nCacheSize];

                for (int i = 0; i < bReadSize / bFrameSize; i++) {

                    System.arraycopy(stereoBuffer, i * bFrameSize, bSrcData, 0, bFrameSize);

                    short[] nSrcData = TypeUtils.bytes2Shorts(bSrcData, nFrameSize);

                    // if (iAngleLoc == AudioProJni.getInstance().ORDINATE) {
                    //     int iLen = nSamplePerFrame;
                    //     short[] bChData = new short[iLen];
                    //     int ret = Mic4SepJni.getInstance().RunLsPro(bChData, nVadFlag, fEngValue, nSrcData);
                    //     if (ret != 1) {
                    //         AiSpeechLogUtil.e(TAG, "RunLsPro Error! " + ret);
                    //         return;
                    //     }

                    //     System.arraycopy(bChData, 0, sCh1Data, i * iLen, iLen);
                    //     System.arraycopy(bChData, 0, sCh2Data, i * iLen, iLen);
                    //     System.arraycopy(bChData, 0, sCh3Data, i * iLen, iLen);
                    //     System.arraycopy(bChData, 0, sCh4Data, i * iLen, iLen);

                    //     synchronized (audioChannel) {
                    //         for (String chkey : audioChannel) {
                    //             writeAudioToChannel(chkey, BytesTrans.getInstance().Shorts2Bytes(bChData));
                    //         }
                    //     }
                    //     continue;
                    // }

                    if (AudioProJni.getInstance().DealHianSep(nSepData, nVadFlag, fEngValue, nSrcData)
                            != AudioProJni.getInstance().RTMSG_OK) {
                        AiSpeechLogUtil.e(TAG, "DealHianVad Error!");
                        return;
                    }

                    int iLen = nFrameSize / iChannelNumber;
                    for (int iIndex = 0; iIndex < nFrameSize / iChannelNumber; iIndex++) {
                        sCh1Data[i * iLen + iIndex] = (short) (nSepData[iIndex * iChannelNumber + 0] * 2);
                        sCh2Data[i * iLen + iIndex] = (short) (nSepData[iIndex * iChannelNumber + 1] * 2);
                        sCh3Data[i * iLen + iIndex] = (short) (nSepData[iIndex * iChannelNumber + 2] * 2);
                        sCh4Data[i * iLen + iIndex] = (short) (nSepData[iIndex * iChannelNumber + 3] * 2);
                    }

                    byte[] bSepData = TypeUtils.shortsToBytes(nSepData, nFrameSize);
                    byte[] bStereoData = new byte[bSepData.length / 2];
                    byte[] bCh1Data = new byte[bSepData.length / 4];
                    byte[] bCh2Data = new byte[bSepData.length / 4];
                    byte[] bCh3Data = new byte[bSepData.length / 4];
                    byte[] bCh4Data = new byte[bSepData.length / 4];

                    for (int iIndex = 0; iIndex < bSepData.length / 8; iIndex++) {
                        bStereoData[iIndex * 4 + 0] = bSepData[iIndex * 8 + 0];
                        bStereoData[iIndex * 4 + 1] = bSepData[iIndex * 8 + 1];
                        bStereoData[iIndex * 4 + 2] = bSepData[iIndex * 8 + 2];
                        bStereoData[iIndex * 4 + 3] = bSepData[iIndex * 8 + 3];

                        bCh1Data[iIndex * 2 + 0] = bSepData[iIndex * 8 + 0];
                        bCh1Data[iIndex * 2 + 1] = bSepData[iIndex * 8 + 1];

                        bCh2Data[iIndex * 2 + 0] = bSepData[iIndex * 8 + 2];
                        bCh2Data[iIndex * 2 + 1] = bSepData[iIndex * 8 + 3];

                        bCh3Data[iIndex * 2 + 0] = bSepData[iIndex * 8 + 4];
                        bCh3Data[iIndex * 2 + 1] = bSepData[iIndex * 8 + 5];

                        bCh4Data[iIndex * 2 + 0] = bSepData[iIndex * 8 + 6];
                        bCh4Data[iIndex * 2 + 1] = bSepData[iIndex * 8 + 7];
                    }

                    if (UsbAudioRecorder.this.writeToFile) targetRecorder.write(bSepData);

//                    if (nVadFlag[0] != 0 || nVadFlag[1] != 0 || nVadFlag[2] != 0 || nVadFlag[3] != 0) {
//                      AiSpeechLogUtil.e(TAG, "nVadFlag: " + nVadFlag[0] + " " + nVadFlag[1] + " " + nVadFlag[2] + " " + nVadFlag[3]);
//                    }

                    if (iAngleLoc == AudioProJni.getInstance().ABSCISSA || iAngleLoc == AudioProJni.getInstance().ORDINATE) {

                        RecordClient.getInstance().write(bStereoData);

                        boolean isUpVoice = (nVadFlag[0] == AudioProJni.getInstance().VAD_STATE_ON);
                        boolean isDownVoice = (nVadFlag[1] == AudioProJni.getInstance().VAD_STATE_ON);

                        if (iAngleLoc == AudioProJni.getInstance().ABSCISSA) {
                            synchronized (audioChannel) {
                                if (audioChannel.size() > 0) {
                                    writeAudioToChannel(audioChannel.get(0), bCh1Data, isUpVoice);
                                    writeAudioToChannel(audioChannel.get(1), bCh2Data, isDownVoice);
                                }
                            }
                        } else {
                            synchronized (audioChannels) {
                                if (audioChannels.size() > 0) {
                                    for (Map.Entry<String, Integer> entry : audioChannels.entrySet()) {
                                        byte[] data = entry.getValue() == 0 ? bCh1Data : bCh2Data;
                                        boolean isVoice = entry.getValue() == 0 ? isUpVoice : isDownVoice;
                                        writeAudioToChannel(entry.getKey(), data, isVoice);
                                    }
                                }
                            }
                        }
                    } else if (iAngleLoc == AudioProJni.getInstance().FULLAXIS) {

                        RecordClient.getInstance().write(bSepData);

                        boolean isCh1Voice = (nVadFlag[0] == AudioProJni.getInstance().VAD_STATE_ON);
                        boolean isCh2Voice = (nVadFlag[1] == AudioProJni.getInstance().VAD_STATE_ON);
                        boolean isCh3Voice = (nVadFlag[2] == AudioProJni.getInstance().VAD_STATE_ON);
                        boolean isCh4Voice = (nVadFlag[3] == AudioProJni.getInstance().VAD_STATE_ON);

                        synchronized (audioChannel) {
                            if (audioChannel.size() > 0) {
                                writeAudioToChannel(audioChannel.get(0), bCh1Data, isCh1Voice);
                                writeAudioToChannel(audioChannel.get(1), bCh2Data, isCh2Voice);
                                writeAudioToChannel(audioChannel.get(2), bCh3Data, isCh3Voice);
                                writeAudioToChannel(audioChannel.get(3), bCh4Data, isCh4Voice);
                            }
                        }
                    }
                }

                if (iAngleLoc == AudioProJni.getInstance().ABSCISSA || iAngleLoc == AudioProJni.getInstance().ORDINATE) {
                    RecordClient.getInstance().sendData(0, sCh1Data, nCacheSize);
                    RecordClient.getInstance().sendData(1, sCh2Data, nCacheSize);
                } else if (iAngleLoc == AudioProJni.getInstance().FULLAXIS) {
                    RecordClient.getInstance().sendData(0, sCh1Data, nCacheSize);
                    RecordClient.getInstance().sendData(1, sCh4Data, nCacheSize);
                    RecordClient.getInstance().sendData(2, sCh2Data, nCacheSize);
                    RecordClient.getInstance().sendData(3, sCh3Data, nCacheSize);
                }

            }

            @Override
            public void onRelease() {
                AiSpeechLogUtil.e(TAG, "录音已停止");
            }

            @Override
            public void onError(int code, String message) {
                AiSpeechLogUtil.e(TAG, "<" + code + ">:" + message);
            }
        });
    }

    @Override
    public void stop() {
        super.stop();

        if(this.writeToFile && sourceRecorder != null) {
            sourceRecorder.close();
        }
        if(this.writeToFile && targetRecorder != null) {
            targetRecorder.close();
        }
        RecordClient.getInstance().stop();

        mAudioRecord.release();
        HianAudioSep.getInstance().release();
//        Mic4SepJni.getInstance().FreeLsPro();
        removeAllChannel();

        AiSpeechLogUtil.e(TAG, "UsbVoiceRecorder stop");
    }

    @Override
    public void stop2() {
        super.stop2();

        if(this.writeToFile && sourceRecorder != null) {
            sourceRecorder.close();
        }
        if(this.writeToFile && targetRecorder != null) {
            targetRecorder.close();
        }
        RecordClient.getInstance().stop();

        mAudioRecord.release();
        HianAudioSep.getInstance().release();
//        Mic4SepJni.getInstance().FreeLsPro();
//        removeAllChannel();

        AiSpeechLogUtil.e(TAG, "UsbVoiceRecorder stop");
    }
}
