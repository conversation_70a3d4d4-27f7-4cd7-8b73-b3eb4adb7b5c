package co.timekettle.speech;

class AgcCppImp {

    float g_fRate = 16000;            //信号采样率
    float g_fRatio = 0.05f;            //压缩率的倒数
    float g_fLookahead = 0.002f;       //时延 s 时延应该小于11ms
    float g_fAttack = 0.2532785535f;   //平滑滤波器系数 exp((-log(9))/(fs*TA)
    float g_fRelease = 0.9996567369f;  //平滑滤波器系数 exp((-log(9))/(fs*TR)
    float g_fGs = 0;                    //平滑增益 dB
    int g_iDataSize = 256;          //一帧数据长度
    float g_fGain = 3.0f;                //固定增益
    float g_fThreshold = 0.24f;        //压限电平
    float g_fClip = 0.3f;              //削波电平
    short[] g_nInDataOld = new short[256];

    /**
     * 使用 speakerType 播放 sound
     * @param sampleRate 采样率
     * @param nSamples 一次处理的样本数
     * @param gain gain 不能超过 10
     */
    public AgcCppImp(float sampleRate, int nSamples, float gain) {
        this.g_fRate = sampleRate;
        this.g_iDataSize = nSamples;
        this.g_nInDataOld = new short[nSamples];

        if (gain > 1.0) {
            this.g_fGain = gain;                                //固定增益
            this.g_fClip = (float) (0.95 / gain);               //削波电平, 即原始幅值
            float internal = 0.06f;
            this.g_fThreshold = (float) (this.g_fClip - internal);   //压限电平
        }
    }

    public int processAgc(short[] output, short[] input) {
        if (input.length == 0) {
            return -1;
        }
        int iDSampe = 0;
        int iVpu = 0;
        int iFrame;
        float fInput;  //压限器控制部分输入信号
        float fInData;//输入信号dB表示
        float fInDataSC; //输入信号静态特征
        float[] fGlin = new float[]{(float) 0.0};
        float fGlinTest = 0;
        float fGc = 0; //增益差值 dB
        iDSampe = (int) (this.g_fLookahead * this.g_fRate);
        for (iFrame = 0; iFrame < this.g_iDataSize; iFrame++) {
            fInput = input[iFrame] * 0.00003051757812f;
            fInData = Math.abs(fInput);
            if (fInData < this.g_fThreshold) {
                fInDataSC = fInData;
            } else {
                fInDataSC = this.g_fThreshold + (fInData - this.g_fThreshold) * this.g_fRatio;
                if (fInDataSC > this.g_fClip)
                    fInDataSC = this.g_fClip;
            }    //end of if (inData < this.tThreshold)
            if (fInData > 1e-15) {
                fGc = (float) (fInDataSC / (fInData + 1e-8));
            }
            if (fGc < this.g_fGs) {
                this.g_fGs = this.g_fAttack * this.g_fGs + (1 - this.g_fAttack) * fGc;
            } else {
                this.g_fGs = this.g_fRelease * this.g_fGs + (1 - this.g_fRelease) * fGc;
            }
            fGlinTest = this.g_fGs * this.g_fGain;
            //输出结果
            if (iFrame < iDSampe) {
                output[iFrame] = (short) Math.round(this.g_nInDataOld[this.g_iDataSize - iDSampe + iFrame] * fGlinTest);
            } else {
                output[iFrame] = (short) Math.round(input[iFrame - iDSampe] * fGlinTest);
            }
            this.g_nInDataOld[iFrame] = input[iFrame];
        }
        return 1;
    }//end of audio_compressor
}

public class AgcProcessor {
//    protected AgcJni agc;
    protected AgcCppImp agc;

    protected float gain = 1.0f;

    public AgcProcessor(float sampleRate, int nSamples, float gain) {
//        this.agc = new AgcJni();
//        if (gain < 4) { // 4倍以上的增益直接使用倍乘的增益方式
            this.agc = new AgcCppImp(sampleRate, nSamples, gain);
//        }
        this.gain = gain;
    }

    public AgcProcessor(float sampleRate, int nSamples) {
//        this.agc = new AgcJni();
        this.agc = new AgcCppImp(sampleRate, nSamples, 3.0f);
    }

//    public AgcProcessor(float gain) {
//        this.gain = gain;
//    }

    public void processAgc(short[] output, short[] input) {
        if (this.agc == null) {
            MultipleValueOfShortBuffer(output, input, input.length, this.gain);
        } else {
            this.agc.processAgc(output, input);
        }
    }

    private void MultipleValueOfShortBuffer(short[] outdata, short[] indata, int size, float multi) {
        int i = 0;
        while (size > 0) {
            int multiV = (int) (indata[i] * multi);
            if (multiV > 0x7fff) {
                outdata[i] = 0x7fff;
            } else if (multiV < -0x8000) {
                outdata[i] = -0x8000;
            } else {
                outdata[i] = (short) multiV;
            }
            i++;
            size--;
        }
    }

    public static void MultipleValueOfShortBuffer(short[] data, int size, float multi) {
        int i = 0;
        while (size > 0) {
            int multiV = (int) (data[i] * multi);
            if (multiV > 0x7fff) {
                data[i] = 0x7fff;
            } else if (multiV < -0x8000) {
                data[i] = -0x8000;
            } else {
                data[i] = (short) multiV;
            }
            i++;
            size--;
        }
    }
}
