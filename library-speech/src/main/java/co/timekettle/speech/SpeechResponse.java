package co.timekettle.speech;

import java.util.List;

public class SpeechResponse<T> {
    public T data;

    public T tdata; // 临时用于多输出的返回, 如翻译结果
    public String codeOfData; // data的code, 用于语种检测
//    public String tdataCode; // 多输出tdata的code, 用于语种检测

    public String engine; // 实际的引擎名称
    public boolean isLast; //  用于标识识别任务是否是 final_result(某一段识别完成的句子), 如: 识别中, 响应结果中isLast=true则表示可进行后续的翻译动作

    public boolean isFinished; // 任务是否已结束, 结果可以是失败/成功, 结果不做显示

    public SpeechError error;

    public static class CustomTranslationRegion {
        public String word; // 匹配词
        public int start; // 开始下标
        public int length; // 结束下标(不包括end本身)
    }

    public static class ResponseMessage {
        public long id; // 任务id
        public String srcCode; // 识别语言 code / 语种识别的识别 code
        public String dstCode; // 翻译语言 code / 语种识别的翻译 code

        public String asrText;  // 识别结果
        public String mtText; // 翻译结果
        public byte[] ttsData; // 合成结果

        public String engine; // 实际的引擎名称
        public boolean isLast; // 用于标识识别任务是否是 final_result(某一段识别完成的句子), 如: 识别中, 响应结果中isLast=true则表示可进行后续的翻译
        public boolean isFinished; // 任务是否已结束, 结果可以是失败/成功

        public int type; // 任务类型, 见 TaskType, 可能是类型集合, 如 TaskType_ASR | TaskType_MT

        public boolean isCustomTranslation; // 是否自定义翻译的响应, 识别和翻译结果都会替换成目标词
        public List<CustomTranslationRegion> asrRegionOfCustomTranslation; // 自定义翻译的 asrText中 词偏移
        public List<CustomTranslationRegion> mtRegionOfCustomTranslation; // 自定义翻译的 mtText中 词偏移
    }
}
