package co.timekettle.speech.translator;

import android.content.Context;
import android.text.TextUtils;

import com.iflytek.TransConstant;
import com.iflytek.TransError;
import com.iflytek.TransListener;
import com.iflytek.VoiceTranslator;

import java.io.File;
import java.nio.charset.Charset;
import java.util.Date;
import java.util.HashMap;
import java.util.Map;
import java.util.concurrent.LinkedBlockingQueue;

import co.timekettle.speech.utils.AiSpeechLogUtil;

public class IflytekOfflineUtil {
    private final String TAG = "OfflineIflytekTranslator";

    private String lastTranslateType = "";
    private String lastCfgPath = "";

    private final Map<String, Param> supports = new HashMap<>();
    private static boolean isLoad = false;

    private boolean outOfLength = false;

    private IflyOfflineListener listener;
    private Context mContext;

    protected LinkedBlockingQueue<CodeBlock> blocks = new LinkedBlockingQueue<>();
    private Thread taskloop;

    public static IflytekOfflineUtil shareInstance;

    public static IflytekOfflineUtil getInstance() {
        if (shareInstance == null) {
            shareInstance = new IflytekOfflineUtil();
        }
        return shareInstance;
    }

    public IflytekOfflineUtil() {
        if (taskloop == null) {
            taskloop = new Thread(new Runnable() {
                @Override
                public void run() {
                    while (true) {
                        try {
                            CodeBlock block = blocks.take();
                            block.invoke();
                        } catch (InterruptedException e) {
                            e.printStackTrace();
                        }
                    }
                }
            });
            taskloop.start();
        }
    }

    public boolean isSupport(String srcCode, String dstCode) {
        if (TextUtils.isEmpty(srcCode) || TextUtils.isEmpty(dstCode)) return false;

        String from = srcCode.split("-")[0];
        String to = dstCode.split("-")[0];
        return this.supports.containsKey(from + to);
    }

    public void initOffline(Context context, String resPath, Map<String, String> customResPaths) {
        addSupport("zhen", resPath, customResPaths);
        addSupport("enzh", resPath, customResPaths);
        addSupport("zhja", resPath, customResPaths);
        addSupport("jazh", resPath, customResPaths);
        addSupport("zhfr", resPath, customResPaths);
        addSupport("frzh", resPath, customResPaths);
        addSupport("zhes", resPath, customResPaths);
        addSupport("eszh", resPath, customResPaths);
        addSupport("zhru", resPath, customResPaths);
        addSupport("ruzh", resPath, customResPaths);
        addSupport("zhde", resPath, customResPaths);
        addSupport("dezh", resPath, customResPaths);
        this.mContext = context;
    }

    private void addSupport(String code, String workDir, Map<String, String> customResPaths) {
        // 有自定义路径则使用自定义路径
        if (customResPaths != null && customResPaths.containsKey(code)) {
            workDir = customResPaths.get(code);
        }
        this.supports.put(code, new Param(code, workDir));
    }

    private boolean initTranslateEngine(Param cParam) {
        if (!isLoad) {
            String cfgPath = cParam.getWorkDir() + "/mt/config/itrans.cfg"; // 默认加载中英 cfg 路径
            File cfg = new File(cfgPath);
            AiSpeechLogUtil.e(TAG, "是否存在 itrans.cfg : " + cfg.exists() + " " + cfgPath);

            if (!cfg.exists()) {
                AiSpeechLogUtil.e(TAG, "initVoiceTranslator: fail cfgPath 不存在");
                return false;
            }

            StringBuilder param = new StringBuilder();
            param.append(TransConstant.APPID + "=5a2f382e");
            param.append(",");
            param.append(TransConstant.CFG_PATH + "=" + cfgPath);
            AiSpeechLogUtil.d(TAG, "initEngine: " + param);

            // FIXME: 由于每次都是新的实例, 每次生成新的 TransListener, 并绑定了新实例的外部变量
            int ret = VoiceTranslator.initEngine(mContext, param.toString(), this.getTransListener());
            if (ret == 0) {
                lastCfgPath = cfgPath;
                isLoad = true;
            } else {
                AiSpeechLogUtil.e(TAG, "initVoiceTranslator: " + ret);
            }

        } else {
            AiSpeechLogUtil.d(TAG, "initVoiceTranslator: isLoad: true");
        }

        return isLoad;
    }

    public TransListener getTransListener() {
        return new TransListener() {
            @Override
            public void onResult(String text) {
                if (IflytekOfflineUtil.this.outOfLength) {
                    text = text + "...";
                }

                if (listener != null) {
                    listener.onResult(true, null, text);
                }
            }

            @Override
            public void onError(TransError transError) {
                // FIXME: 2020/11/30 void com.iflytek.TransListener.onError(com.iflytek.TransError)' on a null object reference
                AiSpeechLogUtil.e(TAG, "讯飞离线翻译失败, onError: " + transError.getErrorCode() + " " + transError.getPlainDescription(true));
                if (listener != null) {
                    listener.onResult(false, transError.getErrorCode() + " 讯飞离线翻译失败", null);
                }
            }
        };
    }

    public void translate(String srcCode, String dstCode, String text, IflyOfflineListener listener) {
//        AiSpeechLogUtil.d(TAG, "start: 使用讯飞离线翻译 " + srcCode + " " + dstCode);

        String from = srcCode.split("-")[0];
        String to = dstCode.split("-")[0];
        String key = from + to;
        String type = key.replace("zh", "cn");

        Param param = this.supports.get(from + to);

        if (!this.initTranslateEngine(param)) {
            if (listener != null) {
                listener.onResult(false, "讯飞离线翻译初始化失败", null);
            }
            return;
        }

        // FIXME: 2020/11/14 当前为单例只能串行处理形式, 只保存一个了监听者
        this.listener = listener;

        try {
            int ret = 0;
            if (lastTranslateType.isEmpty()) {
                VoiceTranslator.setParam(TransConstant.TYPE, type);
                VoiceTranslator.setParam(TransConstant.KEY_OUT_FMT, "txt");//txt结果
                VoiceTranslator.setParam(TransConstant.KEY_SPLIT, "1");//完整结果
                VoiceTranslator.setParam(TransConstant.KEY_RESULT_TYPE, "1");//不分句

                long now = new Date().getTime();
                ret = VoiceTranslator.loadRes();
                AiSpeechLogUtil.d(TAG, "start: end loadResource cost: " + (new Date().getTime() - now));

            } else if (!lastTranslateType.equals(type)) {
                String cfgPath = param.getWorkDir() + "/mt/config/itrans.cfg"; // 默认加载中英 cfg 路径

                long now = new Date().getTime();
                if (cfgPath.equals(lastCfgPath)) { // 相同环境, 则直接切换资源
                    VoiceTranslator.switchRes(type);
                } else { //
                    this.destory();
                    AiSpeechLogUtil.d(TAG, "start: end destory cost: " + (new Date().getTime() - now));
                    translate(srcCode, dstCode, text, listener);
                    return;
                }
                AiSpeechLogUtil.d(TAG, "start: end switchRes cost: " + (new Date().getTime() - now));
            }

            if (ret != 0) {
                AiSpeechLogUtil.e(TAG, "start: 失败: " + ret);
                if (listener != null) {
                    listener.onResult(false, "离线翻译失败", null);
                }
                return;
            }

            AiSpeechLogUtil.d(TAG, "start: from: " + from + " to: " + to);
            VoiceTranslator.startTrans(this.toLess256B(text));
            lastTranslateType = type;

        } catch (TransError e) {
            AiSpeechLogUtil.e(TAG, "VoiceTranslator translate: " + e.getMessage());
        }
    }

    public void destory() {
        // 需要释放并重置isLoad, 重新绑定监听者
        VoiceTranslator.destory();
        isLoad = false;
    }

    private String toLess256B(String text) {
        this.outOfLength = false;
        // 测试 超过 256 字节
//        text = "在中国应对疫情的艰难时刻，普京总统曾致电习主席表示慰问，俄方也及时向中国伸出援手。对抗击疫情中中俄相互支持、守望相助，习主席在通话中这样评价：“体现了新时代中俄关系高水平”。普京总统还讲了一句意味深长的话：“中国的行动是对个别国家挑衅和污名化中国的响亮回答。”";
        int len = 256;
        byte[] originBytes = text.getBytes();
        if (originBytes.length > len) {
            String temp = new String(originBytes, 0, len, Charset.defaultCharset());
//            AiSpeechLogUtil.d(TAG, "toLess256B: 前: " + temp + " length:" + temp.length());
            temp = text.substring(0, temp.length() - 1);
//            AiSpeechLogUtil.d(TAG, "toLess256B: 后: " + temp + " length:" + temp.length());
            this.outOfLength = true;
            return temp;
        }
        return text;
    }

    public interface IflyOfflineListener {
        void onResult(boolean success, String errDesc, String text);
    }

    public interface CodeBlock {
        void invoke();
    }

    static class Param {
        private final String code;
        private final String workDir;

        public Param(String code, String workDir) {
            this.code = code;
            this.workDir = workDir;
        }

        public String getCode() {
            return code;
        }

        public String getWorkDir() {
            return workDir;
        }

        @Override
        public String toString() {
            return "Param{" +
                    "code='" + code + '\'' +
                    ", workDir='" + workDir + '\'' +
                    '}';
        }
    }
}
