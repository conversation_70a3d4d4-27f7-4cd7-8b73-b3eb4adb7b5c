package co.timekettle.speech.translator;

import android.content.Context;
import android.text.TextUtils;
import android.util.Log;

import com.niutrans.translator.Translator;

import java.io.File;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.LinkedBlockingQueue;

import co.timekettle.speech.utils.AiSpeechLogUtil;

public  class NiuOfflineUtil {
    private final String TAG = "NiuOfflineUtil";

    private final Map<String, Param> supports = new HashMap<>();

    private Context mContext;
    private String niuAuthDirPath = null;

    protected LinkedBlockingQueue<CodeBlock> blocks = new LinkedBlockingQueue<>();
    private Thread taskloop;

    public static NiuOfflineUtil shareInstance;
    public static NiuOfflineUtil getInstance() {
        if (shareInstance == null) {
            shareInstance = new NiuOfflineUtil();
        }
        return shareInstance;
    }
    public NiuOfflineUtil() {
        if (taskloop == null) {
            taskloop = new Thread(new Runnable() {
                @Override
                public void run() {
                    while (true) {
                        try {
                            CodeBlock block = blocks.take();
                            block.invoke();
                        } catch (InterruptedException e) {
                            e.printStackTrace();
                        }
                    }
                }
            });
            taskloop.start();
        }
    }

    public boolean isSupport(String srcCode, String dstCode) {
        if (TextUtils.isEmpty(srcCode) || TextUtils.isEmpty(dstCode)) return false;

        String from = srcCode.split("-")[0];
        String to = dstCode.split("-")[0];
        return this.supports.containsKey(from + to);
    }

    public void initOffline(Context context, String resPath, Map<String, String> customResPaths) {
        clearSupports();
        addSupport("zhko", resPath, customResPaths);
        addSupport("kozh", resPath, customResPaths);
        addSupport("enja", resPath, customResPaths);
        addSupport("jaen", resPath, customResPaths);
        addSupport("enko", resPath, customResPaths);
        addSupport("koen", resPath, customResPaths);
        addSupport("enfr", resPath, customResPaths);
        addSupport("fren", resPath, customResPaths);
        addSupport("enes", resPath, customResPaths);
        addSupport("esen", resPath, customResPaths);
        addSupport("enru", resPath, customResPaths);
        addSupport("ruen", resPath, customResPaths);
        addSupport("ende", resPath, customResPaths);
        addSupport("deen", resPath, customResPaths);
        this.mContext = context;

        this.niuAuthDirPath = this.mContext.getFilesDir().getAbsolutePath() + "/niutrans_auth";
        File f = new File(this.niuAuthDirPath);
        if (!f.exists()) {
            f.mkdir();
        }
    }

    public void initOffline2(Context context, String resPath, Map<String, String> customResPaths) {
        clearSupports();
        addSupport("zhko", resPath, customResPaths);
        addSupport("kozh", resPath, customResPaths);
        addSupport("enja", resPath, customResPaths);
        addSupport("jaen", resPath, customResPaths);
        addSupport("enko", resPath, customResPaths);
        addSupport("koen", resPath, customResPaths);
        addSupport("enfr", resPath, customResPaths);
        addSupport("fren", resPath, customResPaths);
        addSupport("enes", resPath, customResPaths);
        addSupport("esen", resPath, customResPaths);
        addSupport("enru", resPath, customResPaths);
        addSupport("ruen", resPath, customResPaths);
        addSupport("ende", resPath, customResPaths);
        addSupport("deen", resPath, customResPaths);

        addSupport("zhen", resPath, customResPaths);
        addSupport("enzh", resPath, customResPaths);
        addSupport("zhja", resPath, customResPaths);
        addSupport("jazh", resPath, customResPaths);
        addSupport("zhfr", resPath, customResPaths);
        addSupport("frzh", resPath, customResPaths);
        addSupport("zhes", resPath, customResPaths);
        addSupport("eszh", resPath, customResPaths);
        addSupport("zhru", resPath, customResPaths);
        addSupport("ruzh", resPath, customResPaths);
        addSupport("zhde", resPath, customResPaths);
        addSupport("dezh", resPath, customResPaths);
        this.mContext = context;

        this.niuAuthDirPath = this.mContext.getFilesDir().getAbsolutePath() + "/niutrans_auth";
        File f = new File(this.niuAuthDirPath);
        if (!f.exists()) {
            f.mkdir();
        }
    }

    private void addSupport(String code, String workDir, Map<String, String> customResPaths) {
        // 有自定义路径则使用自定义路径
        if (customResPaths != null && customResPaths.containsKey(code)) {
            workDir = customResPaths.get(code);
        }
        assert workDir != null;
        assert workDir.contains("niutrans") : "资源路径可能错误, 检查是否是 niutrans 文件夹";
        if (workDir.contains("niutrans")) {
            workDir = workDir.replace("niutrans", "");
        }
        this.supports.put(code, new Param(code, workDir));
    }

    private void clearSupports() {
        this.supports.clear();
    }

    public boolean tryAuth(String srcCode, String dstCode) {
        String from = srcCode.split("-")[0].toLowerCase();
        String to = dstCode.split("-")[0].toLowerCase();
        Param param1 = this.supports.get(from + to);
        Param param2 = this.supports.get(to + from);
        if (param1 == null || param2 == null) {
            AiSpeechLogUtil.d(TAG, "小牛翻译不支持此语言对，from：" + from + "  to：" + to);
            return false;
        }

        String workDir = param1.getWorkDir();
        if (!new File(workDir + "niutrans").exists()) {
            AiSpeechLogUtil.e(TAG, "小牛翻译 初始化， from：" + from + "  to：" + to + " 不存在目录: " + workDir + "niutrans");
            return false;
        }

        try {
            // 若是资源需要在外部存储空间, android 10 需要在AndroidManifest.xml 中 <application> 添加 android:requestLegacyExternalStorage="true"
            //        workDir = null; // 会崩溃 SIGSEGV, native 层崩溃
            // #01 /base.apk!libNiuTrans.so (offset 0x63dc000) (LoadModel(char const*, char const*, char const*, char const*, char*)+996)
            // #02 /base.apk!libNiuTrans.so (offset 0x63dc000) (Java_com_niutrans_translator_Translator_LoadModelSepAuth+680)
            //        this.niuAuthDirPath  = null; // 不会崩溃, 打印 "初始化失败： 1.该设备未授权 2.没有..."
            // 老版本库需要 READ_PHONE_STATE 权限
            synchronized (shareInstance) {
                boolean ret_from = param1.loadResource(mContext, null);
                boolean ret_to = param2.loadResource(mContext, null);
                if (ret_to && ret_from) {
                    AiSpeechLogUtil.d(TAG, "小牛翻译[" + param1.code + "] 初始化(鉴权)成功");
                    return true;
                } else {
                    AiSpeechLogUtil.e(TAG, "小牛翻译[" + param1.code + "] 初始化失败： 1.该设备未授权 2.没有对应的语言模型 3.设备无网络 4.未在小牛控制后台创建对应的项目 5.已经有另一个相同语言的项目在控制台了 6.激活的设备数达到了后台最大");
                    return false;
                }
            }
        } catch (Exception e) {
            AiSpeechLogUtil.e(TAG, "小牛翻译[" + param1.code + "] 初始化失败：可能是权限问题, android 10 需要在AndroidManifest.xml 中 <application> 添加 android:requestLegacyExternalStorage=\"true\"");
            e.printStackTrace();
            return false;
        }
    }

    public synchronized void translate(String srcCode, String dstCode, String text, TranslateListener listener) {
        try {
            blocks.put(() -> {
                String from = srcCode.split("-")[0].toLowerCase();
                String to = dstCode.split("-")[0].toLowerCase();
                AiSpeechLogUtil.d(TAG, "小牛翻译原文：" + text + " from：" + from + "  to：" + to);
                if (text.isEmpty()) {
                    listener.onResult(false,"原始文本为空", null);
                    return;
                }
                synchronized (shareInstance) {
                    Param param = this.supports.get(from + to);
                    String workDir = param.getWorkDir();
                    if (!new File(workDir + "niutrans").exists()) {
                        String desc = "小牛离线翻译 初始化异常[" + param.code + "] 不存在目录: " + workDir + "niutrans";
                        AiSpeechLogUtil.e(TAG, desc);
                        listener.onResult(false, desc, null);
                        return;
                    }

                    boolean ret = param.loadResource(mContext, this.niuAuthDirPath);
                    if (!ret) {
                        String desc = "小牛离线翻译[" + param.code + "] 初始化异常： 1.该设备未授权 2.没有对应的语言模型 3.设备无网络 4.未在小牛控制后台创建对应的项目 5.已经有另一个相同语言的项目在控制台了 6.激活的设备数达到了后台最大";
                        listener.onResult(false,desc, null);
                        return;
                    }

                    AiSpeechLogUtil.d(TAG, "小牛离线翻译[" + param.code + "] 初始化(鉴权)成功");
                    String output = param.translator.translate(text);
                    AiSpeechLogUtil.d(TAG, "小牛离线翻译结果：" + output);
                    listener.onResult(true, null, output);
                }
            });
        } catch (InterruptedException e) {
            e.printStackTrace();
        }
    }


    public interface TranslateListener {
        void onResult(boolean success, String errDesc, String text);
    }

    public interface CodeBlock {
        void invoke();
    }

    static class Param {
        private final String code;
        private final String workDir;
        private boolean authed; // load 过一次说明授权成功
        private final Translator translator;
        private boolean isLoad;
        public static final List<Param> loadedList = new ArrayList<>();

        public Param(String code, String workDir) {
            this.code = code;
            this.workDir = workDir;
            this.translator = new Translator();
        }

        public String getCode() {
            return code;
        }

        public String getWorkDir() {
            return workDir;
        }

        public void setAuthed(boolean authed) {
            this.authed = authed;
        }
        public boolean isAuthed() {
            return authed;
        }

        public String getAuthInformation(Context context) {
            String authInformation = this.translator.getAuthInformation(context);
            return authInformation != null ? authInformation.replace("\n", " ") : "";
        }

        /**
         * Auth Failed! 联网鉴权—鉴权信息错误
         * Network Connection Error! 联网鉴权—网络错误
         * Auth Ret Error! 联网鉴权—其他错误
         * Error 1: AUTH_RET_FILE_SIZE_ERROR 鉴权文件大小错误
         * Error 2: AUTH_RET_MD5_ERROR 鉴权错误
         * Error 3: AUTH_OVERDUE_ERROR 鉴权过期
         * Error 4: AUTH_FILE_UPDATE_ERROR 鉴权文件更新失败
         * Error 5: AUTH_RET_FILE_EXISTS_ERROR 鉴权文件不存在
         * AUTH_RET_OK 鉴权成功
         * */
        public String getAuthExtraInformation(Context context, Translator translator, String from, String to, String workDir) {
            return translator != null ? translator.OnlyAuth(from, to, workDir, context) : "translator is null";
        }

        public void setLoad(boolean load) {
            isLoad = load;
            if (isLoad) authed = true;
        }
        public boolean isLoad() {
            return isLoad;
        }

        public synchronized boolean loadResource(Context context, String authDirPath) {
            if (this.isLoad) return true;
            if (loadedList.size() >= 2) {
                loadedList.get(0).unloadResource();
            }
            boolean ret = this.translator.load(code.substring(0, 2), code.substring(2, 4), workDir, context);
            if (ret) loadedList.add(this); // 保存 load 列表
            AiSpeechLogUtil.d("NiuOfflineUtil", "getAuthInformation: " + this.getAuthInformation(context) + " extra: " + this.getAuthExtraInformation(context, this.translator, code.substring(0, 2), code.substring(2, 4), workDir));
            this.setLoad(ret);
            return this.isLoad;
        }

        public synchronized void unloadResource() {
            this.translator.unload();
            this.setLoad(false);
            loadedList.remove(this);
        }

        @Override
        public String toString() {
            return "Param{" +
                    "code='" + code + '\'' +
                    ", workDir='" + workDir + '\'' +
                    ", authed=" + authed +
                    ", translator=" + translator +
                    ", isLoad=" + isLoad +
                    '}';
        }
    }
}