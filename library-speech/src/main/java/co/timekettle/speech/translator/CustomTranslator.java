package co.timekettle.speech.translator;

import android.text.TextUtils;

import co.timekettle.speech.SpeechError;
import co.timekettle.speech.SpeechRequest;
import co.timekettle.speech.utils.HttpsConnection;
import co.timekettle.speech.utils.Language;
import co.timekettle.speech.utils.AiSpeechLogUtil;

import org.json.JSONException;
import org.json.JSONObject;

import java.io.UnsupportedEncodingException;
import java.net.URLEncoder;
import java.util.HashMap;
import java.util.Map;

public class CustomTranslator extends TranslatorBase {
    private static final String TAG = "CustomTranslator";

    public CustomTranslator() {
        this.name = "custom";
        this.singleton = false;
        this.isCustomModule = true;
    }

    HashMap<String, String> supports = new HashMap<String, String>(){{
        put("zh<->en", "");
        put("zh<->es", "");
        put("en<->es", "");
    }};
    public boolean isSupport(final String srcCode, final String dstCode) {
//        String from = srcCode.split("-")[0];
//        String to = dstCode.split("-")[0];
//        if (!(TextUtils.isEmpty(from) && TextUtils.isEmpty(to))) {
//            return supports.containsKey(from + "<->" + to) || supports.containsKey(to + "<->" + from);
//        }
        return false;
    }

    public void setListener(Listener listener) {
        this.listener = listener;
    }

    public Listener getListener() {
        return this.listener;
    }

    @Override
    public void start(Language srcCode, Language dstCode, String text, Map<String, Object> opts) {
        super.start(srcCode, dstCode, text, opts);

//        AiSpeechLogUtil.d(TAG, "start: 自定义翻译参数" + opts.get(SpeechRequest.OptsKeyNeedCustomMt) + " " + opts.get(SpeechRequest.OptsKeyCustomMtTableId));
//        new Thread(new Runnable() {
//            @Override
//            public void run() {
//                int tableId = (int) opts.get(SpeechRequest.OptsKeyCustomMtTableId);
//                SpeechError err = null;
//                String transText = null;
//                try {
//                    String encodeText = URLEncoder.encode(text, "UTF-8");
//                    String params = "str=" + encodeText + "&src_lang=" + srcCode.standardCode + "&dst_lang=" + dstCode.standardCode + "&custom_flag=true&cus_voc_list_id=" + tableId + "&lianxiang=true";
////                    String url = "http://customtrans.wttwo.com/api/inditrans/sub4?" + params;
//                    String url = "http://120.24.239.218/api/inditrans/sub4?" + params;
//                    AiSpeechLogUtil.d(TAG, "自定义翻译 url: " + url);
//                    byte[] resBytes = HttpsConnection.get(url);
//                    if (resBytes != null && resBytes.length > 0) {
//                        String resString = new String(resBytes);
//                        AiSpeechLogUtil.d(TAG, "自定义翻译: " + resString);
//                        JSONObject result = new JSONObject(resString);
//                        transText = result.getString("cus_trans");
//                    } else {
//                        AiSpeechLogUtil.e(TAG, "自定义翻译: 翻译返回失败!");
//                        err = new SpeechError(-1, "自定义翻译响应为空");
//                    }
//                } catch (JSONException | UnsupportedEncodingException e) {
//                    err = new SpeechError(-1, e.getMessage());
//                    e.printStackTrace();
//                } finally {
//                    if (listener != null) {
//                        listener.onTranslateResult(CustomTranslator.this, transText, name, err);
//                    }
//                }
//            }
//        }).start();
//        if (listener != null) {
//            listener.onTranslateResult(CustomTranslator.this, text + "(custom)", name, (text.length() % 2 == 0 ? new SpeechError(-1, "异常测试(文本长度偶数为异常)") : null));
//        }
    }

    @Override
    public void stop() {

    }
}
