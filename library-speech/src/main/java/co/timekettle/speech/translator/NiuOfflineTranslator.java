package co.timekettle.speech.translator;

import android.content.Context;
import android.text.TextUtils;

import co.timekettle.speech.SpeechError;
import co.timekettle.speech.utils.FantiUtil;
import co.timekettle.speech.utils.Language;
import co.timekettle.speech.utils.AiSpeechLogUtil;

import java.util.HashMap;
import java.util.Map;

public class NiuOfflineTranslator extends TranslatorBase{

    private final String TAG = "NiuOfflineTranslator";

    public NiuOfflineTranslator() {
        this.name = "NiuOfflineTranslator";
        this.isOfflineModule = true;
    }

    /**
     * 程序运行的时候初始化
     * @param context 上下文
     * @param resPath 资源路径
     */
    public static void initOffline(Context context, String resPath) {
        NiuOfflineUtil.getInstance().initOffline(context, resPath, null);
    }

    /**
     * 程序运行的时候初始化
     * @param context 上下文
     * @param resPath 资源路径
     * @param customResPaths 某些语言对需要的自定义资源目录配置
     */
    public static void initOffline(Context context, String resPath, Map<String, String> customResPaths) {
        NiuOfflineUtil.getInstance().initOffline(context, resPath, customResPaths);
    }

    public static void initOffline2(Context context, String resPath, Map<String, String> customResPaths) {
        NiuOfflineUtil.getInstance().initOffline2(context, resPath, customResPaths);
    }

    public static boolean tryAuth(String srcCode, String dstCode) {
        return NiuOfflineUtil.getInstance().tryAuth(srcCode, dstCode);
    }
    
    @Override
    public boolean isSupport(String srcCode, String dstCode) {
        return NiuOfflineUtil.getInstance().isSupport(srcCode, dstCode);
    }

    @Override
    public void start(Language srcCode, Language dstCode, String text, Map<String, Object> opts) {
        super.start(srcCode, dstCode, text, opts);
        if (TextUtils.isEmpty(text)) {
            if (listener != null) {
                listener.onTranslateResult(NiuOfflineTranslator.this, null, name, new SpeechError(-1, "错误, 离线翻译文本为空"));
            }
            return;
        }
        NiuOfflineUtil.getInstance().translate(srcCode.standardCode, dstCode.standardCode, text, (success, errDesc, result) -> {
            AiSpeechLogUtil.e(TAG, "onResult: 离线翻译结束 " + session + " from:" + srcCode.standardCode + " to:" + dstCode.standardCode + " " + result);
            if (success) {
                if (dstCode.standardCode.toLowerCase().endsWith("zh-tw"))
                    result = FantiUtil.traditionalized(result);
                if (listener != null) {
                    listener.onTranslateResult(NiuOfflineTranslator.this, result, name, null);
                }
            } else {
                if (listener != null) {
                    listener.onTranslateResult(NiuOfflineTranslator.this, null, name, new SpeechError(-1, errDesc));
                }
            }
        });
    }
}


