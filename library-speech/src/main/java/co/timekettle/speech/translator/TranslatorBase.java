package co.timekettle.speech.translator;

import android.content.Context;

import co.timekettle.speech.SpeechError;
import co.timekettle.speech.utils.Language;

import java.util.Map;

public abstract class TranslatorBase {
    protected String name;
    protected String key;
    protected Listener listener;
    protected Context context;
    protected long session = 0;
    protected boolean singleton;

    protected Language srcCode;
    protected Language dstCode;
    public boolean isOfflineModule = false;
    public boolean isCustomModule = false;

    public void setContext(Context context) {
        this.context = context;
    }
    public boolean isSingleton() {
        return singleton;
    }

    public void setKey(String key) {
        this.key = key;
    }

    public String getKey() {
        return this.key;
    }

    public long getSession() {
        return session;
    }

    public void setSession(long s) {
        this.session = s;
    }

    public String getName() {
        return this.name;
    }

    public Listener getListener() {
        return this.listener;
    }

    public void setListener(Listener listener) {
        this.listener = listener;
    }

    public boolean isSupport(final String srcCode, final String dstCode) {
        return true;
    }

    public void start(final Language srcCode, final Language dstCode, final String text, final Map<String, Object> opts) {
        this.srcCode = srcCode;
        this.dstCode = dstCode;
    }

    public void stop() {}

    public interface Listener {
//        void onTranslateResult(String name, String key, long session, String text, String engine);
//        void onError(String name, String key, long session, int code, String message);

        void onTranslateResult(TranslatorBase translator, String text, String engine, SpeechError error);
    }
}
