package co.timekettle.speech.translator;

import android.util.Log;

import co.timekettle.speech.SpeechError;
import co.timekettle.speech.utils.Language;
import co.timekettle.speech.utils.ThreadPoolManager;
import co.timekettle.tmkengine.NetSessionContext;
import co.timekettle.tmkengine.TmkSpeechClient;

import java.util.Map;
import java.util.Objects;

public class ISpeechTranslator extends TranslatorBase {
//    Translator translator;
    NetSessionContext translator;

    public ISpeechTranslator() {
        this.name = "ispeech";
    }

    public boolean isSupport(final String srcCode, final String dstCode) {
        return true;
    }

    public void setListener(Listener listener) {
        this.listener = listener;
    }

    public Listener getListener() {
        return this.listener;
    }

    @Override
    public void start(Language srcCode, Language dstCode, String text, Map<String, Object> opts) {
        super.start(srcCode, dstCode, text, opts);

        String selfCode = srcCode.standardCode;
        String otherCode = dstCode.standardCode;
        boolean isSameCode = Objects.equals(selfCode, otherCode);
        boolean isSameLan = selfCode.split("-")[0].equalsIgnoreCase(otherCode.split("-")[0]);
        boolean isZh = selfCode.split("-")[0].equalsIgnoreCase("zh") || otherCode.split("-")[0].equalsIgnoreCase("zh");
        // 相同 code 或 非中文的相同语种
        if (isSameCode || (isSameLan && !isZh)) {
            if (listener != null) {
                listener.onTranslateResult(ISpeechTranslator.this, text, "self", null);
            }
            return;
        }

        NetSessionContext context = TmkSpeechClient.shareInstance().createTranslator(selfCode, otherCode, text, new NetSessionContext.ContextListener() {
            @Override
            public void onRecognizeResult(NetSessionContext context, long session, boolean isLast, String srcCode, String rtext, String ttext, String engine) {
            }

            @Override
            public void onTranslateResult(NetSessionContext context, long session, String result, String engine) {
                if (listener != null) {
                    listener.onTranslateResult(ISpeechTranslator.this, result, engine, null);
                }
            }

            @Override
            public void onSynthesizeBuffer(NetSessionContext context, long session, byte[] output, int outputSize) {

            }

            @Override
            public void onCompleted(NetSessionContext context, long session, String engine) {

            }

            @Override
            public void onError(NetSessionContext context, long session, String engine, int code, String message) {
                if (listener != null) {
                    listener.onTranslateResult(ISpeechTranslator.this, null, name, new SpeechError(code, message));
                }
            }
        });
        translator = context;
        context.setSession(getSession());
        context.start();
    }

    @Override
    public void stop() {
        if (translator != null) {
            translator.stop();
            translator = null;
        }
    }
}
