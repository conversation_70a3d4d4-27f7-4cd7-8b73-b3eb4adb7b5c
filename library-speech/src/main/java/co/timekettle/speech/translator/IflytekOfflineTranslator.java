package co.timekettle.speech.translator;

import android.content.Context;
import android.text.TextUtils;

import co.timekettle.speech.SpeechError;
import co.timekettle.speech.utils.FantiUtil;
import co.timekettle.speech.utils.Language;

import co.timekettle.speech.utils.AiSpeechLogUtil;

import java.util.HashMap;
import java.util.Map;

public class IflytekOfflineTranslator extends TranslatorBase {
    private final String TAG = "OfflineIflytekTranslator";

    public IflytekOfflineTranslator() {
        this.name = "iflytekOfflineMt";
//        singleton = true;
        this.isOfflineModule = true;
    }

    public static void initOffline(Context context, String resPath) {
        IflytekOfflineUtil.getInstance().initOffline(context, resPath, null);
    }
    
    public static void initOffline(Context context, String resPath, Map<String, String> customResPaths) {
        IflytekOfflineUtil.getInstance().initOffline(context, resPath, customResPaths);
    }

//    public void setSession(long s) {
//        session = s;
//    }

    @Override
    public boolean isSupport(String srcCode, String dstCode) {
        return IflytekOfflineUtil.getInstance().isSupport(srcCode, dstCode);
    }

    @Override
    public void start(Language srcCode, Language dstCode, String text, Map<String, Object> opts) {
        super.start(srcCode, dstCode, text, opts);

        synchronized (NiuOfflineUtil.getInstance()) {
            try {
                IflytekOfflineUtil.getInstance().blocks.put(() -> {
                    if (TextUtils.isEmpty(text)) {
                        if (listener != null) {
                            listener.onTranslateResult(IflytekOfflineTranslator.this, null, name, new SpeechError(-1, "错误, 离线翻译文本为空"));
                        }
                        return;
                    }

                    IflytekOfflineUtil.getInstance().translate(srcCode.standardCode, dstCode.standardCode, text, new IflytekOfflineUtil.IflyOfflineListener() {
                        @Override
                        public void onResult(boolean success, String errDesc, String text) {
                            AiSpeechLogUtil.e(TAG, "onResult: 翻译结束 " + session + " [" + srcCode.standardCode + " -> " + dstCode.standardCode + "] " + text);

                            if (success) {
                                if (dstCode.standardCode.toLowerCase().endsWith("zh-tw"))
                                    text = FantiUtil.traditionalized(text);

                                if (listener != null) {
                                    listener.onTranslateResult(IflytekOfflineTranslator.this, text, name, null);
                                }
                            } else {
                                if (listener != null) {
                                    listener.onTranslateResult(IflytekOfflineTranslator.this, text, name, new SpeechError(-1, errDesc));
                                }
                            }
                        }
                    });
                });
            } catch (InterruptedException e) {
                e.printStackTrace();
            }
        }
        return;
    }
}

