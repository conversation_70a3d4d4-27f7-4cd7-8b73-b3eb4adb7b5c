package co.timekettle.speech;

import android.content.Context;

import java.io.File;
import java.io.FileNotFoundException;
import java.io.FileOutputStream;
import java.io.IOException;
import java.util.UUID;

import co.timekettle.speech.utils.DateUtil;
import co.timekettle.speech.utils.AiSpeechLogUtil;

public class TestRecorder {

    private FileOutputStream os;
    private boolean mCanRecord = false;
    private String mFileName = "";
    private String sssid = "";
    private Context context = null;
    private String dir = "";
    final String TAG = "TestRecorder";

    private boolean isTxt = false;

    public TestRecorder(Context context, String dir) {
        this(context, dir, true);
    }

    public TestRecorder(Context context, String dir, boolean canRecord) {
        this(context, dir, null, canRecord);
    }

    public TestRecorder(Context context, String dir, String fileName, boolean canRecord) {
        this(context, dir, fileName, null, canRecord);
    }

    public TestRecorder(Context context, String dir, String fileName, String id, boolean canRecord) {
        this.sssid = id == null || id.isEmpty() ? getUUID() : id.replace(":", "_");
        this.mFileName = fileName == null || fileName.isEmpty() ? "" : fileName.replace(":", "_");
        this.mCanRecord = canRecord;
        this.context = context;
        this.dir = dir;
        this.open();
    }

    public TestRecorder(Context context, String dir, String fileName, String id, boolean canRecord, boolean isTxt) {
        this.sssid = id == null || id.isEmpty() ? getUUID() : id.replace(":", "_");
        this.mFileName = fileName == null || fileName.isEmpty() ? "" : fileName.replace(":", "_");
        this.mCanRecord = canRecord;
        this.context = context;
        this.dir = dir;
        this.isTxt = isTxt;
        this.open();
    }

    public static String getUUID() {
        UUID uuid = UUID.randomUUID();
        String str = uuid.toString();
        String uuidStr = str.replace("-", "");
        return uuidStr;
    }

    public void open() {
        if (!mCanRecord) {
            return;
        }
        String timeStr = DateUtil.getCurrentDateString();

        File pcmFolder = new File(this.context.getExternalCacheDir(), dir);

        if (!pcmFolder.exists()) {
            pcmFolder.mkdir();
        }

        if (!mFileName.isEmpty()) {
            timeStr = mFileName;
        }

        File pcmFile = new File(pcmFolder, timeStr + "-" + sssid + (this.isTxt ? ".txt" : ".pcm"));
        try {
            this.os = new FileOutputStream(pcmFile);
            AiSpeechLogUtil.d(TAG, "本地录音创建成功: " + pcmFile.getAbsolutePath());
        } catch (FileNotFoundException e) {
            e.printStackTrace();
        }
    }

    public void write(byte[] data) {
        if (!mCanRecord) {
            return;
        }

        if (os != null) {
            try {
//                AiSpeechLogUtil.d(TAG, "开始写数据到本地 " + Arrays.toString(data));
                os.write(data);
            } catch (IOException e) {
                AiSpeechLogUtil.e(TAG, "write: 写入数据失败 ========");
                e.printStackTrace();
            }
        }
    }

    public void close() {
        if (!mCanRecord) {
            return;
        }

        if (os != null) {
            try {
                os.close();
                os = null;
            } catch (IOException e) {
                e.printStackTrace();
            }
        }
    }

    public boolean isOpen() {
        return this.os != null;
    }
}
