package co.timekettle.speech.speech_translation;

import co.timekettle.speech.ISpeechConstant;
import co.timekettle.speech.SpeechError;
import co.timekettle.tmkengine.NetSessionContext;
import co.timekettle.tmkengine.TmkSpeechClient;

public class ISpeechTranslationTwoStep extends TmkWorkerBase {

    private NetSessionContext netContext;

    public ISpeechTranslationTwoStep() {
    }

    @Override
    public void setWorkerId(long workerId) {
        super.setWorkerId(workerId);
        if (netContext != null) netContext.setSession(workerId);
    }

    @Override
    public boolean isOfflineModule() {
        return false;
    }

    @Override
    public boolean isSingleton() {
        return false;
    }

    @Override
    public String getName() {
        return ISpeechConstant.SPEECHTRANSLATION.SPEECHTRANSLATION_TWOSTEP.getName();
    }

    @Override
    public boolean isSupport(String srcCode, String dstCode) {
        return true;
    }


    private NetSessionContext.ContextListener netListener = new NetSessionContext.ContextListener() {
        @Override
        public void onRecognizeResult(NetSessionContext context, long session, boolean isLast, String srcCode, String rtext, String ttext, String engine) {
            if (getListener() != null) {
                getListener().onTranslateResult(ISpeechTranslationTwoStep.this, isLast, srcCode, rtext, ttext, engine);
            }
        }

        @Override
        public void onTranslateResult(NetSessionContext context, long session, String result, String engine) {

        }

        @Override
        public void onSynthesizeBuffer(NetSessionContext context, long session, byte[] output, int outputSize) {

        }

        @Override
        public void onCompleted(NetSessionContext context, long session, String engine) {
            if (getListener() != null) {
                getListener().onFinished(ISpeechTranslationTwoStep.this, engine, null);
            }
            clear();
        }

        @Override
        public void onError(NetSessionContext context, long session, String engine, int code, String message) {
            if (getListener() != null) {
                getListener().onFinished(ISpeechTranslationTwoStep.this, engine, new SpeechError(code, message));
            }
            clear();
        }
    };

    @Override
    public void start(String srcCode, String dstCode) {
        netContext = TmkSpeechClient.shareInstance().createSpeechTranslation2(srcCode, dstCode, ISpeechConstant.useOpus, netListener);
        netContext.setSession(getWorkerId());
        netContext.start();
    }

    @Override
    public void stop() {
        if (netContext != null) {
            netContext.stop();
            netContext = null;
        }
    }

    @Override
    public void writeAudio(byte[] data) {
        if (netContext != null) {
            netContext.writeAudio(data);
        }
    }

    private void clear() {
        if (netContext != null) {
            netContext.setListener(null);
            netListener = null;
            netContext = null;
        }
    }
}
