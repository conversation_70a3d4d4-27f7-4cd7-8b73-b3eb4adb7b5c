package co.timekettle.speech.speech_translation;

import co.timekettle.speech.SpeechError;
import co.timekettle.tmkengine.NetSessionContext;

public abstract class TmkWorkerBase {
//   private boolean isOfflineModule = false;
//   private boolean singleton = false;
//   private String name;
   private long workerId; // 唯一 id
   private String userData; // 用户数据
   private Listener listener;
   protected boolean stopped = false; // 已停止任务, 但不代表已结束, 需要等待最终识别结果后才真正结束

   public Listener getListener() {
      return listener;
   }

   public void setListener(Listener listener) {
      this.listener = listener;
   }

   public long getWorkerId() {
      return workerId;
   }

   public void setWorkerId(long workerId) {
      this.workerId = workerId;
   }

   public String getUserData() {
      return userData;
   }

   public void setUserData(String userData) {
      this.userData = userData;
   }

   public boolean isStopped() {
      return stopped;
   }

   public void setStopped(boolean stopped) {
      this.stopped = stopped;
   }

   public abstract String getName(); // worker 的名字
   public abstract boolean isOfflineModule(); // 是否是离线模块
   public abstract boolean isSingleton(); // 是否是单实例(单实例不能并发)
   public abstract boolean isSupport(final String srcCode, final String dstCode);
   public abstract void start(final String srcCode, final String dstCode);
   public abstract void stop();
   public abstract void writeAudio(byte[] data);
   public interface Listener {
      void onTranslateResult(TmkWorkerBase worker, boolean isLast, String srcCode, String rtext, String ttext, String engine);
      void onSynthesizeResult(TmkWorkerBase worker, byte[] data, String engine);

      void onFinished(TmkWorkerBase worker, String engine, SpeechError error);
   }
}
