package co.timekettle.speech;

/*
* 单个任务, asr, mt, tts, 或 play 其中之一
* */
public class SpeechTask<Q, R> {
    public String chkey; // 通道名称
    public long session; // 任务 id

    public TaskType type; // 配置任务类型, 识别, 翻译或合成
    public boolean isLast; // 标记任务是否是最终任务, 存在多个子任务时, 是否是最终的任务, 收到响应后进行下一个阶段, 如 mt -> tts
    public boolean isFinished; // 标记任务是否全部完成
    public SpeechTask<Q, R> next;

    public SpeechRequest<Q> request; // 原始数据
    public SpeechResponse<R> response; // 结果数据

    public enum TaskType
    {
        //添加枚举的指定常量
        PICK(1 << 1),
        ASR(1 << 2),
        MT(1 << 3),
        TTS(1 << 4),
        PLAY(1 << 5);

        // 必须增加一个构造函数,变量,得到该变量的值
        private int mState = 0;
        private TaskType(int value)
        {
            mState = value;
        }
        public int value() {
            return mState;
        }

        public static int getValue(TaskType type) {
            switch (type) {
                case PICK: return 1 << 1;
                case ASR: return 1 << 2;
                case MT: return 1 << 3;
                case TTS: return 1 << 4;
                case PLAY: return 1 << 5;
            }
            return 1 << 1;
        }
    }

    public SpeechTask(TaskType type, String chkey, long session) {
        request = new SpeechRequest<Q>();
        response = new SpeechResponse<R>();

        this.type = type;
        this.chkey = chkey;
        this.session = session;
    }
}