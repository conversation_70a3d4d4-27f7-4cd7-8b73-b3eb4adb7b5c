package co.timekettle.speech;

import java.util.HashMap;
import java.util.Map;

import co.timekettle.speech.jni.TmkCustomTranslationJni;
import co.timekettle.speech.utils.AiSpeechLogUtil;

/**
 * 自定义翻译实现原理:
 * 目前主要是 <自定义识别+翻译> 的处理(自定义识别基于发音序列匹配), 类似
 * 词表: 时空壶 -> Timekettle, hello-> tmkhola
 * 识别结果: 时空湖的同事们真棒
 * 自定义识别结果: 时空壶的同事们真棒
 * 预翻译结果: Timekettle的同事们真棒
 * 翻译结果:  The colleagues at Timekettle are awesome
 *
 * 目前存在的问题:
 * · 原始词和目标词只允许当前语言文本, 不允许混合
 * · 不允许阿拉伯数字和特殊符号等, 原始词支持: 输入语言文本+".", 目标词支持: 输入语言文本+空格+"."
 * · 原始词包含空格, 导致处理异常的问题
 * · 生造词支持不好 如: hellokkk -> tmkhola        --> 目前无法处理
 * · 自定义词大小写敏感, 大小发音不一致           --> 标点左右加个空格处理
 * · 自定义词列表中不能包含发音相同/相似的词, 存在“太阳”->“Sun” 和 “太阳雨”->“Sun Rain” 会出现异常; "flower"->"花朵" 和 "our"->"我们"; "时空壶" -> "Timekettle", "时空虎" -> "Timekettle"
 * · 下标不够准确, 如下情况会导致此问题
 *     1.自定义翻译目标词带空格, 翻译引擎可能会去除, 导致下标异常 如: 设备司仪 -> Equipment emcee    --> 目前已规避, 但多个词会出现异常建议把词缩短
 *     2.自定义翻译目标词是生造词, 翻译引擎可能会去除  如:  hello -> 一你好                         --> 目前无法处理
 *     3.如果翻译内容出现与“自定义词中的目标词一致”, 也会全部标出来                                 --> 目前无法处理
 *        词表: 本子 -> bill
 *        识别结果: 这到底是账单还是本子
 *        自定义识别结果: 这到底是账单还是bill
 *        预翻译结果: Is this a bill or a bill?
 *        翻译结果:  Is this a bill or a bill?
 *     4.离线翻译会出问题, 类似"月亮" -> "MoonCT", MoonCt会翻译出错成 "Yo."等, 导致翻译文本下标出错   --> 目前无法处理, 离线翻译类似生造词会出现错误
 *     5.类似"hello" -> "你好", 处理识别文本"Hello.", 英文模型无法匹配到, 西语模型可以匹配到, "Hello,"是可以匹配  --> 标点左右加个空格处理
 *
 * 自定义识别SDK(发音匹配)BUG:
 * · 原始词包含空格, 导致处理异常的问题
 * · 自定义词列表中不能包含发音相同/相似的词, 存在“太阳”->“Sun” 和 “太阳雨”->“Sun Rain” 会出现异常, "flower"->"花朵" 和 "our"->"我们"; "时空壶" -> "Timekettle", "时空虎" -> "Timekettle"
 * · 英文自定义识别会删掉空格, 自定义词Hello -> 你好, weather -> 天气, 如: Hello, how's the weather like today? -> Hello, how's the weatherlike today?
 * · 类似"Hello.", 英文模型无法匹配到, 西语模型可以匹配到, "Hello,"是可以匹配
 * · 自定义词hello -> 你好, 输入"hello, ", 会匹配成"hello,"
 * · 输入"二二.": "Two.", 或 "二.": @"Two."会导致匹配很多结果
 */
public class CustomTranslationManager {
   final static String TAG = "CustomTranslationManager";
   private static CustomTranslationManager instance = null;
   private final HashMap<String, TmkCustomTranslationJni> supports = new HashMap<>();

   private CustomTranslationManager() {
      supports.put("zh", new TmkCustomTranslationJni("zh"));
      supports.put("en", new TmkCustomTranslationJni("en"));
      supports.put("es", new TmkCustomTranslationJni("es"));
   }

   public static CustomTranslationManager getInstance() {
      if (instance == null) {
         instance = new CustomTranslationManager();
      }
      return instance;
   }

   public TmkCustomTranslationJni findCustomTranslation(String code) {
      String lan = code.split("-")[0];
      for (String key : supports.keySet()) {
         if (key.startsWith(lan.toLowerCase())) {
            return supports.get(key);
         }
      }
      return null;
   }

   public void updateModelPath(String path, String code) {
      TmkCustomTranslationJni ct = findCustomTranslation(code);
      try {
         ct.load(path);
      } catch (Exception e) {
         AiSpeechLogUtil.e(TAG, "更新异常: " + e.getMessage());
      }
   }

   /**
    * 查看是否支持此语言对翻译, 目前支持: 中文—>英语, 中文—>西班牙语，英语—>西班牙语
    *
    * @param srcCode 原语言代码, 如 en 或 en-US
    * @param dstCode 目标语言代码, 如 zh 或 zh-CN
    */
   public boolean isSupport(String srcCode, String dstCode) {
      return supports.containsKey(getCodeKey(srcCode, dstCode));
   }

   /**
    * 查看此语言对翻译是否有效, 目前支持: 中文—>英语, 中文—>西班牙语，英语—>西班牙语
    *
    * @param srcCode 原语言代码, 如 en 或 en-US
    * @param dstCode 目标语言代码, 如 zh 或 zh-CN
    */
   public boolean isValid(String srcCode, String dstCode) {
      String key = getCodeKey(srcCode, dstCode);
      TmkCustomTranslationJni ct = supports.get(key);
      return ct != null && ct.isValid();
   }

   public void updateWords(Map<String, String> words, String code) throws Exception {
      TmkCustomTranslationJni ct = findCustomTranslation(code);
      ct.update(words);
   }

   /**
    * 更新模型和语言词表
    *
    * @param code 语言代码, 如 en 或 en-US
    * @param modePath 模型路径
    * @param words 改语言的词表
    * @throws Exception 异常信息
    */
   public void updateCustomTranslation(String code, String modePath, Map<String, String> words) throws Exception {
      if (words == null) {
         words = new HashMap<>();
         if (code.contains("zh")) words.put("时空壶", "Timekettle");
      }

      TmkCustomTranslationJni ct = findCustomTranslation(code);
      if (modePath != null && !modePath.isEmpty()) ct.load(modePath);
      if (words != null && !words.isEmpty()) ct.update(words);
   }

   /**
    * 自定义翻译(词不允许数字, 不允许多语言混合)
    * words: {"时空壶": "Timekettle", "你好": "tmkhao"} text: 你好,时空虎,我曾经有一次去时空胡公司参观过
    *    oriText: 你好,时空虎,我曾经有一次去时空胡公司参观过
    *    crText : 你好,时空壶,我曾经有一次去时空壶公司参观过
    *    ctText : tmkhao,Timekettle,我曾经有一次去Timekettle公司参观过
    *    mrs    :
    *       oriWord: 你好   range:0(2)
    *       oriWord: 时空虎 range:3(3)
    *       oriWord: 时空胡 range:13(3)
    * @param text 原文本
    * @param srcCode 原语言代码, 如 en 或 en-US
    * @param dstCode 目标语言代码, 如 zh 或 zh-CN
    * @return 翻译结果
    */
   public TmkCustomTranslationJni.TmkCustomTranslationResult process(String text, String srcCode, String dstCode) {
      TmkCustomTranslationJni ct = findCustomTranslation(srcCode);
      if (!ct.isValid()) return null;
      try {
         return ct.process(text);
      } catch (Exception e) {
         AiSpeechLogUtil.e(TAG, "处理异常: " + e.getMessage());
      }
      return null;
   }

   public String getCodeKey(String srcCode, String dstCode) {
      String from = srcCode.split("-")[0];
      String to = dstCode.split("-")[0];
      return from + to;
   }
}
