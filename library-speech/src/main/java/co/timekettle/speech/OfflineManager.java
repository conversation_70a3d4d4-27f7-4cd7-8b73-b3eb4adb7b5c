package co.timekettle.speech;

import android.content.Context;
import android.text.TextUtils;

import com.google.gson.Gson;

import java.io.File;
import java.io.IOException;
import java.io.InputStream;
import java.lang.reflect.Type;
import java.nio.charset.StandardCharsets;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

import co.timekettle.speech.recognizer.IflytekOfflineRecognizer;
import co.timekettle.speech.recognizer.aeesdk.AeeOfflineRecognizer;
import co.timekettle.speech.synthesizer.HoyaSynthesizer;
import co.timekettle.speech.synthesizer.XzySynthesizer;
import co.timekettle.speech.translator.IflytekOfflineTranslator;
import co.timekettle.speech.translator.NiuOfflineTranslator;
import co.timekettle.speech.utils.AiSpeechLogUtil;
/**
 * 离线管理, 一般使用步骤:
 * 在下载模块实际使用 init() -> isReadyOffline() 返回是否需下载的资源
 * 在翻译模块实际使用 init() -> openOffline() 尝试开启, 失败则返回需下载的资源
 *
 * 支持如下语言对:
 * zh<->en	asr: zhen(122M)
 *          mt: cnen(169M) encn(169M) + 讯飞翻译公共包(119M)
 * zh<->ko	asr: zhen(122M) + ko( 90M)
 *          mt: zh2ko(101M) ko2zh(101M)
 * en<->es	asr: zhen(122M) + es(101M)
 *          mt: en2es( 86M) es2en( 86M)
 * zh<->ja	asr: zhen(122M) + ja(100M)
 *          mt: cnja(135M) jacn(135M) + 讯飞翻译公共包(119M)
 * en<->de	asr: zhen(122M) + de( 97M)
 *          mt: en2de( 84M) de2en( 84M)
 * zh<->es	asr: zhen(122M) + es(101M)
 *          mt: cnes(121M) escn(124M) + 讯飞翻译公共包(119M)
 * en<->ru	asr: zhen(122M) + ru(107M)
 *          mt: en2ru( 80M) ru2en( 80M)
 * zh<->de	asr: zhen(122M) + de( 97M)
 *          mt: cnde(129M) decn(122M) + 讯飞翻译公共包(119M)
 * en<->ko	asr: zhen(122M) + ko( 90M)
 *          mt: en2ko( 94M) ko2en( 89M)
 * zh<->ru	asr: zhen(122M) + ru(107M)
 *          mt: cnru(125M) rucn(126M) + 讯飞翻译公共包(119M)
 * en<->ja	asr: zhen(122M) + ja(100M)
 *          mt: en2ja( 98M) ja2en( 89M)
 * zh<->fr	asr: zhen(122M) + fr( 97M)
 *          mt: cnfr(120M) frcn(123M) + 讯飞翻译公共包(119M)
 * en<->fr	asr: zhen(122M) + fr( 97M)
 *          mt: en2fr( 83M) fr2en( 83M)
 *
 * 其中以上语言对均由讯飞识别,讯飞翻译,小牛翻译组合而成, 例en<->es中,
 * 资源包括 asr: zhen(122M)(讯飞识别) + es(101M)(讯飞识别) mt: en2es( 86M)(小牛翻译) es2en( 86M)(小牛翻译),
 * 则识别使用的是讯飞引擎, 翻译使用的是小牛引擎
 *
 * 讯飞识别	        讯飞翻译
 * zhen(122M)	    公共包(119M)
 * ja(100M)	        cnen(169M) encn(169M)=338M
 * fr( 97M)	        cnja(135M) jacn(135M)=270M
 * es(101M)	        cnfr(120M) frcn(123M)=243M
 * ru(107M)	        cnes(121M) escn(124M)=245M
 * de( 97M)	        cnru(125M) rucn(126M)=251M
 * ko( 90M)	        cnde(129M) decn(122M)=251M
 *
 * 小牛翻译(android)	zh2ko(101M) ko2zh(101M)=202M
 * 	en2ko( 94M) ko2en( 89M)=183M
 * 	en2de( 84M) de2en( 84M)=168M
 * 	en2es( 86M) es2en( 86M)=172M
 * 	en2fr( 83M) fr2en( 83M)=166M
 * 	en2ru( 80M) ru2en( 80M)=160M
 * 	en2ja( 98M) ja2en( 89M)=187M
 */
public class OfflineManager {
    static String TAG = "OfflineManager";

    private static OfflineManager instance = null;
    private Context mContext;

    private final Map<String, Boolean> supports = new HashMap<>(); // key 为"支持离线的语言对", value 为"是否已打开"
    private String curLangCoupleCode; // 当前已经开启并使用的语言对, 临时用于识别器管理中判断当前语言对是否开启离线来选择离线识别器
    private Map<String, Object> offlineConfig = null;

    private final String CodeSeparateStr = "<->";
    private boolean enable = true;
    private String iflyResWorkDirPath; // 讯飞资源根目录, 注意: 讯飞资源放至 getExternalStorageDirectory() 会授权失败10008导致不工作
    private String niuResWorkDirPath; // 小牛资源根目录, 注意: 小牛放至 getExternalStorageDirectory() 和 getExternalCacheDir() 会导致崩溃, 推测放至 External 均如此
    private String xzyResWorkDirPath; // 学之友资源根目录, 放在哪里都可以
    private String xzyMtResWorkDirPath; // 学之友资源根目录, 放在哪里都可以
    private String xzyTtsResWorkDirPath; // 学之友资源根目录, 放在哪里都可以

    private Map<String, String> customResPaths;
    private Map<String, String> asrCustomResPaths;
    private Map<String, String> mtCustomResPaths;
    private Map<String, String> ttsCustomResPaths;
    private int useVersion = 1; // 使用离线版本号, 对应的版本配置等. 版本有: 1, 2, 3

    public static OfflineManager getInstance() {
        if (instance == null) {
            instance = new OfflineManager();
        }
        return instance;
    }

    private OfflineManager() {
    }

    public Map<String, Object> getOfflineConfig() {
        return offlineConfig;
    }

    /**
     * 讯飞离线工作文件夹的绝对路径, 默认是 getFilesDir() + "/IflyOfflineResource2"
     */
    public void setIflyResWorkDirPath(String iflyResWorkDirPath) {
        if (TextUtils.isEmpty(iflyResWorkDirPath)) return;
        this.iflyResWorkDirPath = iflyResWorkDirPath;
    }

    /**
     * 小牛离线工作文件夹的绝对路径, 默认是 getFilesDir(), 文件夹名字必须是 niutrans/
     */
    public void setNiuResWorkDirPath(String niuResWorkDirPath) {
        if (TextUtils.isEmpty(niuResWorkDirPath)) return;
        this.niuResWorkDirPath = niuResWorkDirPath;
    }

    /**
     * xzy离线工作文件夹的绝对路径, 默认是 getFilesDir() + "/xzy"
     */
    public void setAeeResWorkDirPath(String xzyResWorkDirPath) {
        if (TextUtils.isEmpty(xzyResWorkDirPath)) return;
        this.xzyResWorkDirPath = xzyResWorkDirPath;
    }

    public int getUseVersion() {
        return useVersion;
    }

    /**
     * 加载离线资源
     * @param context 上下文
     * @param config 离线配置
     * @param customResPaths 指定具体位置:
     *         Map<String, String> customResPaths = new HashMap<String, String>() {{
     *             // 讯飞离线识别
     *             put("zh", iflyResWorkDirPath);
     *             put("en", iflyResWorkDirPath);
     *             put("fr", ResWorkDirPath);
     *             put("ja", ResWorkDirPath);
     *             // 讯飞离线翻译
     *             put("zhen", iflyResWorkDirPath);
     *             put("enzh", iflyResWorkDirPath);
     *          }}
     *  根据 key 具体指定资源路径
     *  使用
     */
    @Deprecated
    public void loadOffline(Context context, Map<String, Object> config, Map<String, String> customResPaths) {
        this.init(context, config, customResPaths);
    }

    @Deprecated
    public void loadOffline(Context context, Map<String, Object> config) {
        this.loadOffline(context, config, null);
    }

    /**
     * 加载离线资源
     * @param context 上下文
     * @param config 离线配置
     * @param customResPaths 指定具体位置:
     *         Map<String, String> customResPaths = new HashMap<String, String>() {{
     *             // 讯飞离线识别
     *             put("zh", iflyResWorkDirPath);
     *             put("en", iflyResWorkDirPath);
     *             put("fr", ResWorkDirPath);
     *             put("ja", ResWorkDirPath);
     *             // 讯飞离线翻译
     *             put("zhen", iflyResWorkDirPath);
     *             put("enzh", iflyResWorkDirPath);
     *          }}
     *  根据 key 具体指定资源路径
     *  特殊地, 需要制定讯飞识别(离线1.0)鉴权目录, key为"offline1.0IflyWorkDir": iflyResWorkDirPath
     */
    public void init(Context context, Map<String, Object> config, Map<String, String> customResPaths) {
        if (customResPaths != null) {
            initCustomResPaths(customResPaths);
        }
        this.mContext = context.getApplicationContext();
        this.iflyResWorkDirPath = mContext.getFilesDir().getAbsolutePath() + "/IflyOfflineResource2"; // 讯飞防止 sd 卡空间内会授权失败10008导致不工作
        this.niuResWorkDirPath = mContext.getFilesDir().getAbsolutePath() + "/niutrans"; // Environment.getExternalStorageDirectory().getAbsolutePath(); 小牛放至外部会导致崩溃

//        this.iflyResWorkDirPath = mContext.getCacheDir().getAbsolutePath() + "/IflyOfflineResource2"; // 讯飞资源放至 getExternalStorageDirectory() 会授权失败10008导致不工作
//        this.niuResWorkDirPath = mContext.getCacheDir().getAbsolutePath() + "/niutrans"; // 小牛放至 getExternalStorageDirectory() 会导致崩溃


        // 初始化配置
        if (config == null) {
            try {
                String iflyWorkDir = customResPaths != null ? customResPaths.get("offline1.0IflyWorkDir") : null; // 讯飞识别路径
                int useVersion = getUseVersion(mContext, iflyWorkDir); // 必须是讯飞鉴权目录
                String configName = getConfigNameByVersion(useVersion);
                InputStream in = this.mContext.getAssets().open(configName);
                byte[] buffer = new byte[in.available()];
                while (-1 != in.read(buffer)) {
                    config = new Gson().fromJson(new String(buffer, StandardCharsets.UTF_8), (Type) Map.class);
                }
            } catch (IOException e) {
                e.printStackTrace();
            }
        }
        this.offlineConfig = config;
        // 根据配置文件获取版本号
        String version = (String) this.offlineConfig.get("version");
        if (Objects.equals(version, "1")) {
            this.useVersion = 1;
        } else if (Objects.equals(version, "2")) {
            this.useVersion = 2;
        } else if (Objects.equals(version, "3")) {
            this.useVersion = 3;
        } else {
            this.useVersion = 1;
        }

        // 根据版本设置路径
        if (this.getUseVersion() == 1) {
            this.iflyResWorkDirPath = mContext.getFilesDir().getAbsolutePath() + "/IflyOfflineResource2"; // 讯飞防止 sd 卡空间内会授权失败10008导致不工作
            this.niuResWorkDirPath = mContext.getFilesDir().getAbsolutePath() + "/niutrans"; // Environment.getExternalStorageDirectory().getAbsolutePath(); 小牛放至外部会导致崩溃
        } else if (this.getUseVersion() == 2) {
            this.xzyResWorkDirPath = mContext.getFilesDir().getAbsolutePath() + "/ClientOffline2/xzy/asr"; // 学之友放至外部会导致崩溃
            this.xzyMtResWorkDirPath = mContext.getFilesDir().getAbsolutePath() + "/ClientOffline2/xzy/mt"; // 学之友放至外部会导致崩溃
            this.xzyTtsResWorkDirPath = mContext.getFilesDir().getAbsolutePath() + "/ClientOffline2/xzy/tts"; // 学之友放至外部会导致崩溃
            this.niuResWorkDirPath = mContext.getFilesDir().getAbsolutePath() + "/ClientOffline2/mt/niutrans"; // Environment.getExternalStorageDirectory().getAbsolutePath(); 小牛放至外部会导致崩溃
        } else if (this.getUseVersion() == 3) {

        }

        List<String> coupleCodeConfigs = (List<String>) offlineConfig.get("languagePairUrl");
        assert coupleCodeConfigs != null;
//        ArrayList<String> list = new ArrayList<>();
        for (Object o : coupleCodeConfigs) {
            Map conf = (Map) o;
            String coupleCode = (String) conf.get("code");
            // 部分语言选择至外部
//            conf.put("localResPath", "");
//            list.add((String) conf.get("code"));
            supports.put(coupleCode, false);
        }
//        this.supports = list;
        AiSpeechLogUtil.d(TAG, "loadOffline 当前版本: " + this.useVersion + ", supports: " + this.supports.toString());

        if (this.getUseVersion() == 1) {
            // 初始化 讯飞 识别翻译 离线
            IflytekOfflineRecognizer.initOffline(this.mContext, this.iflyResWorkDirPath, this.asrCustomResPaths);
            IflytekOfflineTranslator.initOffline(this.mContext, this.iflyResWorkDirPath, this.mtCustomResPaths);
            // 初始化 小牛 翻译 离线
            NiuOfflineTranslator.initOffline(this.mContext, this.niuResWorkDirPath, this.mtCustomResPaths);
            // 初始化 hoya tts 离线
            HoyaSynthesizer.load(this.mContext);
        } else if (this.getUseVersion() == 2) {
            // 初始化 学之友 离线识别
            AeeOfflineRecognizer.initOffline2(this.mContext, this.xzyResWorkDirPath, asrCustomResPaths);
            // 初始化 小牛 翻译 离线
            NiuOfflineTranslator.initOffline2(this.mContext, this.niuResWorkDirPath, mtCustomResPaths);
            // 初始化 hoya tts 离线
            XzySynthesizer.load(this.mContext);
        }
    }

    private void initCustomResPaths(Map<String,String> customResPaths){
        this.customResPaths = customResPaths;
        // 分割为三个独立的 HashMap
        this.asrCustomResPaths = customResPaths.entrySet().stream()
                .filter(entry -> entry.getKey().startsWith("asr-"))
                .collect(Collectors.toMap(
                        entry -> entry.getKey().substring(4), // 去掉前缀 "asr-"
                        Map.Entry::getValue
                ));
        this.mtCustomResPaths = customResPaths.entrySet().stream()
                .filter(entry -> entry.getKey().startsWith("mt-"))
                .collect(Collectors.toMap(
                        entry -> entry.getKey().substring(3), // 去掉前缀 "mt-"
                        Map.Entry::getValue
                ));
        this.ttsCustomResPaths = customResPaths.entrySet().stream()
                .filter(entry -> entry.getKey().startsWith("tts-"))
                .collect(Collectors.toMap(
                        entry -> entry.getKey().substring(4), // 去掉前缀 "tts-"
                        Map.Entry::getValue
                ));
    }

    /**
     * 尝试联网鉴权, 一般在网络开启的时候调用; 必须要 init() 之后调用
     * @param context 上下文
     * @param fromCode 原语言
     * @param toCode 目标语言
     */
    public boolean tryFetchAuth(Context context, String fromCode, String toCode) {
        if (this.getUseVersion() == 1) {
            boolean retIfly = tryFetchIflyAuth(fromCode);
            boolean retNiu = tryFetchNiutransAuth(fromCode, toCode);
            if (context != null) tryFetchHoyaAuth(context);
            return retIfly && retNiu;
        } else if (this.getUseVersion() == 2) {
            boolean retXzy = tryFetchXzyAuth(context, fromCode);
            boolean retNiu = tryFetchNiutransAuth(fromCode, toCode);
            if (context != null) AeeOfflineRecognizer.tryAuth(context, fromCode);
            return retXzy && retNiu;
        }
        return false;
    }

    public String getConfigNameByVersion(int useVersion) {
        String configName = "IflyNiuOfflieConfigT1.json";
        // 根据版本号加载配置
        if (useVersion == 1) configName = "IflyNiuOfflieConfigT1.json";
        else if (useVersion == 2) configName = "OfflieConfig2.json";
        else if (useVersion == 3) configName = "OfflieConfig3.json";
        return configName;
    }

    public int getUseVersion(Context context, String iflyWorkDir) {
        assert context != null;
        if (context == null) return 1;
//        String workDir = customResPaths != null ? customResPaths.get("zh") : null; // 讯飞识别路径
        if (iflyWorkDir == null) iflyWorkDir = context.getFilesDir().getAbsolutePath() + "/IflyOfflineResource2";
        boolean isExistIflyAsrResource = new File(iflyWorkDir + "/asr/cnen").exists();
        int useVersion = 1;
        if (isExistIflyAsrResource) {
            if (tryFetchIflyZhAuth(context, iflyWorkDir)) {
                useVersion = 1;
            } else {
                useVersion = 2;
            }
        }
        else {
            useVersion = 2;
//                    boolean isExistXXAsrResource = new File("/zhen").exists();
//                    if (isExistXXAsrResource) useVersion = 2;
//                    else useVersion = 3;
        }

        return useVersion;
    }

    /**
     * 加载离线资源, 解压 hoya , 加载讯飞离线等
     * @param context 上下文
     * @param config 离线配置, 如果为空, 则默认使用内部的
     */
    public void init(Context context, Map<String, Object> config) {
        this.init(context, config, null);
    }

    /**
     * 加载离线资源, 解压 hoya , 加载讯飞离线等
     * @param context 上下文
     */
    public void init(Context context) {
        this.init(context, null, null);
    }

    /**
     * 讯飞尝试鉴权, 在网络开启的时候, 调用一次触发鉴权
     */
    public boolean tryFetchIflyAuth(String srcCode) {
        return IflytekOfflineRecognizer.tryAuth(srcCode);
    }

    /**
     * 讯飞尝试鉴权, 在网络开启的时候, 调用一次触发鉴权
     */
    public boolean tryFetchIflyZhAuth(Context context, String workDir) {
        return IflytekOfflineRecognizer.tryAuth2(context, workDir);
    }

    /**
     * 学之友尝试鉴权, 在网络开启的时候, 调用一次触发鉴权
     */
    public boolean tryFetchXzyAuth(Context context, String srcCode) {
        return AeeOfflineRecognizer.tryAuth(context, srcCode);
    }

    /**
     * 讯飞尝试鉴权, 在网络开启的时候, 调用一次触发鉴权
     */
    public boolean tryFetchNiutransAuth(String srcCode, String dstCode) {
        return NiuOfflineTranslator.tryAuth(srcCode, dstCode);
    }

    public void tryFetchHoyaAuth(Context context) {
        HoyaSynthesizer.synDownloadLicenseFile(context);
    }

    /**
     * 全局是否开启了离线
     */
    public boolean isEnableOffline() {
        return enable;
    }

    public void downloadOfflineResource() {
        HoyaSynthesizer.downloadOfflineResource();
    }

    /**
     * 设置全部离线是否可用
     */
    public void setEnableOffline(boolean isEnable) {
        enable = isEnable;
        AiSpeechLogUtil.d(TAG, "isEnableOffline: 是否全局打开离线能力" + enable);
    }

    /**
     * 查询支持哪些语言对的离线翻译
     * @param srcCode 源语言 code
     * @param dstCode 目标语言 code
     * @return 是否支持
     */
    public boolean isSupport(String srcCode, String dstCode) {
        if (this.supports == null || this.supports.size() == 0) return false;

        if (TextUtils.isEmpty(srcCode) || TextUtils.isEmpty(dstCode)) return false;

        String from = srcCode.split("-")[0].toLowerCase();
        String to = dstCode.split("-")[0].toLowerCase();
        return this.supports != null && (this.supports.containsKey(from + CodeSeparateStr + to) || this.supports.containsKey(to + CodeSeparateStr + from));
    }

    /**
     * 检查资源是否已下载
     * @param srcCode 源语言 code
     * @param dstCode 目标语言 code
     * @return 返回检查结果 CheckResult
     */
    public CheckResult isReadyOffline(String srcCode, String dstCode) {
        CheckResult result = isVaildResource(srcCode, dstCode);
        if (result == null) result = new CheckResult(null, false, null, null);
        return result;
    }

    /**
     * 检查资源是否已下载
     * @param coupleCode 语言对 code, 如 zh<->en, zhen, enzh
     * @return 返回检查结果 CheckResult
     */
    public CheckResult isReadyOffline(String coupleCode) {
        if (coupleCode.contains(CodeSeparateStr)) {
            coupleCode = coupleCode.replace(CodeSeparateStr, "");
        } else if (coupleCode.contains("-")) {
            coupleCode = coupleCode.replace("-", "");
        }
        return isVaildResource(coupleCode);
    }

    /**
     * 当前 srcCode <-> dstCode 是否已启用离线
     * @param srcCode 源语言 code
     * @param dstCode 目标语言 code
     * @return 是否已启用
     */
    public boolean isEnable(String srcCode, String dstCode) {
        if (this.supports == null || this.supports.size() == 0) return false;
        if (TextUtils.isEmpty(srcCode) && TextUtils.isEmpty(dstCode)) return false;
        if (TextUtils.isEmpty(srcCode)) {
            String to = dstCode.split("-")[0].toLowerCase();
            return this.curLangCoupleCode != null && this.curLangCoupleCode.contains(to);
        }
        if (TextUtils.isEmpty(dstCode)) {
            String from = srcCode.split("-")[0].toLowerCase();
            return this.curLangCoupleCode != null && this.curLangCoupleCode.contains(from);
        }

        return this.curLangCoupleCode != null && Boolean.TRUE.equals(this.supports.get(getLangCouple(srcCode, dstCode)));
    }

    public CheckResult openOffline(String srcCode, String dstCode, Boolean isCurrent) {
        // 检查资源是否存在
        CheckResult cr = isReadyOffline(srcCode, dstCode);
        if (cr == null) return null;
        if (cr.vaild) {
            if (isCurrent) {
                curLangCoupleCode = cr.coupleCode;
            }
            this.supports.put(cr.coupleCode, true);
        }
        return cr;
    }

    /**
     * 开启 srcCode <-> dstCode 离线, 先检查本地离线资源是否存在, 如已存在, 部分引擎进行预加载
     * 特别地, 小牛离线资源需要对语言对鉴权, 在下载完初始化是最佳的选择, 下载判断可以调用 openOffline() 判断
     * @param srcCode 源语言 code
     * @param dstCode 目标语言 code
     * @return 返回检查结果 CheckResult, vaild 则离线已成功开启
     */
    public CheckResult openOffline(String srcCode, String dstCode) {
        // 检查资源是否存在
        CheckResult cr = isReadyOffline(srcCode, dstCode);
        if (cr == null) return null;
        if (cr.vaild) {
            // 由于讯飞只支持单实例, 讯飞通过实时加载资源, 不在此加载

            //在打开离线的时候，去初始化资源（过程需要耗时，也避免没网的情况下鉴权失败的问题）
//            if (NiuOfflineUtil.getInstance().isSupport(srcCode, dstCode)) {
//                if (NiuOfflineUtil.getInstance().initVoiceTranslator(srcCode, dstCode)) {
//                    cr.setActive(true);
//                    AiSpeechLogUtil.d(TAG, "小牛翻译初始化----end：from " + srcCode + "  to：" + dstCode);
//                }
//            } else if (IflytekOfflineUtil.getInstance().isSupport(srcCode, dstCode)) {
//                IflytekOfflineRecognizer.tryFetchAuth(srcCode);
//                cr.setActive(true);
//                AiSpeechLogUtil.d(TAG, "讯飞翻译初始化----end：from " + srcCode + "  to：" + dstCode);
//            }

            curLangCoupleCode = cr.coupleCode;
            this.supports.put(cr.coupleCode, true);
        }
        return cr;
    }

    /**
     * 关闭语言对离线功能
     */
    public boolean closeOffline(String srcCode, String dstCode, Boolean isCurrent) {
        String coupleCode = getLangCouple(srcCode, dstCode);
        this.supports.put(coupleCode, false);
        if (isCurrent) {//如果是当前语言对，则置空，否则保留。此判断是为了解决开网之后，立刻关网，导致离线翻译不翻译的问题
            curLangCoupleCode = null;
        }
        return true;
    }

    /**
     * 关闭语言对离线功能
     */
    public boolean closeOffline(String srcCode, String dstCode) {
        String coupleCode = getLangCouple(srcCode, dstCode);
        if (coupleCode != null) this.supports.put(coupleCode, false);
        curLangCoupleCode = null;
        return true;
    }

    /**
     * 开启 srcCode <-> dstCode 离线, 先检查本地离线资源是否存在, 如已存在, 部分引擎进行预加载
     * 特别地, 小牛离线资源需要对语言对鉴权, 在下载完初始化是最佳的选择, 下载判断可以调用 openOffline() 判断
     * @param coupleCode 语言对 code, 如 zh<->en, zhen, enzh
     * @return 返回检查结果 CheckResult, vaild 则离线已成功开启
     */
    public CheckResult openOffline(String coupleCode) {
        // 检查资源是否存在
        CheckResult cr = isReadyOffline(coupleCode);
        if (cr == null) return null;
        if (cr.vaild) {
            // 由于讯飞只支持单实例, 讯飞通过实时加载资源, 不在此加载

//            String[] codes = coupleCode.split(CodeSeparateStr);
//            //在打开离线的时候，去初始化资源（过程需要耗时，也避免没网的情况下鉴权失败的问题）
//            if (NiuOfflineUtil.getInstance().isSupport(codes[0], codes[1])) {
//                NiuOfflineUtil.getInstance().initVoiceTranslator(codes[0], codes[1]);
//            }

            curLangCoupleCode = cr.coupleCode;
            this.supports.put(cr.coupleCode, true);
        }
        return cr;
    }

    /**
     * 关闭语言对离线功能
     */
    public boolean closeOffline(String coupleCode) {
        if (coupleCode == null) {
            curLangCoupleCode = null;
            return true;
        }
        if (coupleCode.contains(CodeSeparateStr)) {
            coupleCode = coupleCode.replace(CodeSeparateStr, "");
        } else if (coupleCode.contains("-")) {
            coupleCode = coupleCode.replace("-", "");
        }
        this.supports.put(coupleCode, false);
        curLangCoupleCode = null;
        return true;
    }

    /**
     * 根据语言对返回本地指定资源路径
     */
    public List<String> getSpecificResourcePaths(String srcCode, String dstCode) {
        String from = srcCode.split("-")[0];
        String to = dstCode.split("-")[0];
        String coupleCode = from + to;
        return getSpecificResourcePaths(coupleCode);
    }

    public List<String> getSpecificResourcePaths(String coupleCode) {
        if (getUseVersion() == 1) {
            return getSpecificResourcePaths1(coupleCode);
        } else if (getUseVersion() == 2) {
            return getSpecificResourcePaths2(coupleCode);
        } else {
            return new ArrayList<>();
        }
    }

    /**
     * 根据语言对(en<->ja, enja)返回本地指定资源路径
     */
    public List<String> getSpecificResourcePaths1(String coupleCode) {
        if (coupleCode.contains(CodeSeparateStr)) {
            coupleCode = coupleCode.replace(CodeSeparateStr, "");
        } else if (coupleCode.contains("-")) {
            coupleCode = coupleCode.replace("-", "");
        }

        assert offlineConfig != null : "未初始化离线管理器, 检查 loadOffline()";
        if (offlineConfig == null) return new ArrayList<>();

        Map<String, Object> coupleCodeConfig = null;
        // 根据 code 找到 urls
        List<String> coupleCodeConfigs = (List<String>) offlineConfig.get("languagePairUrl");
        assert coupleCodeConfigs != null;
        for (Object o : coupleCodeConfigs) {
            List<String> resCodes = (List<String>) ((Map) o).get("resCodes");
            if (resCodes.contains(coupleCode)) {
                coupleCodeConfig = ((Map) o);
                break;
            }
        }
        if (coupleCodeConfig == null) {
            AiSpeechLogUtil.e(TAG, "isVaildResource: 未找到语言对配置: " + coupleCode);
            return new ArrayList<>();
        }

        List<String> rUrls = (List<String>) coupleCodeConfig.get("urls"); // 下载链接
        String baseUrlIfly = (String) offlineConfig.get("baseUrlIfly");
        String baseUrlNiuAndroid = (String) offlineConfig.get("baseUrlNiuAndroid");
        List<String> commonMtUrls = (List<String>) offlineConfig.get("iflyCommonUrls");
        Map<String, Double> totalSizes = (Map<String, Double>) offlineConfig.get("totalSizes");

        List<String> paths = new ArrayList<>();
        String iflyMtResPath = null;
        // 检查专有资源
        for (int i = 0; i < rUrls.size(); i++) {
            String rUrl = (String) rUrls.get(i);

            String dirPath = null;
            if (rUrl.endsWith(".zip")) {
                dirPath = rUrl.replace(".zip", "");
            }
            assert dirPath != null;
            if (rUrl.startsWith("baseUrlIfly/asr")) {
                String resCode = dirPath.substring(dirPath.lastIndexOf("/") + 1);

                dirPath = dirPath.replace("baseUrlIfly", "");
                dirPath = getIflyAsrResPath(resCode) + dirPath;
            } else if (dirPath.startsWith("baseUrlIfly/mt")) {
                String resCode = dirPath.substring(dirPath.lastIndexOf("/") + 1);
                resCode = resCode.replace("cn", "zh"); // 讯飞资源 "cnen" 替换 "zhen"

                dirPath = dirPath.replace("baseUrlIfly", "");
                dirPath = getIflyMtResPath(resCode) + dirPath;

                iflyMtResPath = getIflyMtResPath(resCode);
            } else if (dirPath.startsWith("baseUrlNiu")) {
                String resCode = dirPath.substring(dirPath.lastIndexOf("/") + 1);
                resCode = resCode.replace("2", ""); // 小牛资源 "zh2ko" 替换 "zhko"

                dirPath = dirPath.replace("baseUrlNiu", "");
                dirPath = getNiuResPath(resCode) + dirPath;
            }
            File f = new File(dirPath);
            if (f.exists()) {
                paths.add(dirPath);
            }
        }
        AiSpeechLogUtil.d(TAG, "getSpecificResourcePaths: 资源路径: " + paths);
        return paths;
    }

    /**
     * 根据语言对(en<->ja, enja)返回本地指定资源路径
     */
    public List<String> getSpecificResourcePaths2(String coupleCode) {
        if (coupleCode.contains(CodeSeparateStr)) {
            coupleCode = coupleCode.replace(CodeSeparateStr, "");
        } else if (coupleCode.contains("-")) {
            coupleCode = coupleCode.replace("-", "");
        }

        assert offlineConfig != null : "getSpecificResourcePaths2 未初始化离线管理器, 检查 loadOffline()";
        if (offlineConfig == null) return new ArrayList<>();

        Map<String, Object> coupleCodeConfig = null;
        // 根据 code 找到 urls
        List<String> coupleCodeConfigs = (List<String>) offlineConfig.get("languagePairUrl");
        assert coupleCodeConfigs != null;
        for (Object o : coupleCodeConfigs) {
            List<String> resCodes = (List<String>) ((Map) o).get("resCodes");
            if (resCodes.contains(coupleCode)) {
                coupleCodeConfig = ((Map) o);
                break;
            }
        }
        if (coupleCodeConfig == null) {
            AiSpeechLogUtil.e(TAG, "isVaildResource: 未找到语言对配置: " + coupleCode);
            return new ArrayList<>();
        }

        List<String> rUrls = (List<String>) coupleCodeConfig.get("urls"); // 下载链接

        List<String> paths = new ArrayList<>();
        // 检查专有资源, "baseUrlXzy/cnen.zip" 必然内置不进行检测
        //  "urls": [
        //   "baseUrlXzy/fr.zip",
        //   "baseUrlNiu/zh2fr.zip",
        //   "baseUrlNiu/fr2zh.zip"
        // ]
        for (int i = 0; i < rUrls.size(); i++) {
            String rUrl = (String) rUrls.get(i); // 如 "baseUrlXzy/fr.zip"

            String dirPath = null;
            if (rUrl.endsWith(".zip")) {
                dirPath = rUrl.replace(".zip", "");
            }
            assert dirPath != null; // 如 "baseUrlXzy/fr"
            if (dirPath.startsWith("baseUrlXzy/")) {
                String resCode = dirPath.substring(dirPath.lastIndexOf("/") + 1); // 如 "fr"

                dirPath = dirPath.replace("baseUrlXzy/", "/");
                dirPath = getXzyResPath(resCode) + dirPath;
            } else if (dirPath.startsWith("baseUrlXzyTts/")) {
                String resCode = dirPath.substring(dirPath.lastIndexOf("/") + 1); // 如 "fr"

                dirPath = dirPath.replace("baseUrlXzyTts/", "/");
                dirPath = getXzyTtsResPath(resCode) + dirPath;
            } else if (dirPath.startsWith("baseUrlNiu/")) {
                String resCode = dirPath.substring(dirPath.lastIndexOf("/") + 1);
                resCode = resCode.replace("2", ""); // 小牛资源 "zh2fr" 替换 "zhfr"

                dirPath = dirPath.replace("baseUrlNiu/", "/");
                dirPath = getNiuResPath(resCode) + dirPath;
            }
            File f = new File(dirPath);
            if (f.exists()) {
                paths.add(dirPath);
            }
        }
        AiSpeechLogUtil.d(TAG, "getSpecificResourcePaths2: 资源路径: " + paths);
        return paths;
    }

    private CheckResult isVaildResource(String srcCode, String dstCode) {
        String from = srcCode.split("-")[0];
        String to = dstCode.split("-")[0];
        String coupleCode = from + to;
        return isVaildResource(coupleCode);
    }

    private CheckResult isVaildResource(String coupleCode) {
        if (getUseVersion() == 1) {
            return isVaildResource1(coupleCode);
        } else if (getUseVersion() == 2) {
            return isVaildResource2(coupleCode);
        } else {
            return new CheckResult(coupleCode, false, null, null);
        }
    }

    /**
     * key of supports, 根据语言对「en<->ja」检查本地资源包是否存在
     */
    private CheckResult isVaildResource1(String coupleCode) {
        if (getUseVersion() == 2) {
            return isVaildResource2(coupleCode);
        }
        assert offlineConfig != null : "未初始化离线管理器, 检查 loadOffline()";
        if (offlineConfig == null) return null;

        Map<String, Object> coupleCodeConfig = null;
        // 根据 code 找到 urls
        List<String> coupleCodeConfigs = (List<String>) offlineConfig.get("languagePairUrl");
        assert coupleCodeConfigs != null;
        for (Object o : coupleCodeConfigs) {
            List<String> resCodes = (List<String>) ((Map) o).get("resCodes");
            if (resCodes.contains(coupleCode)) {
                coupleCodeConfig = ((Map) o);
                break;
            }
        }
        if (coupleCodeConfig == null) {
            AiSpeechLogUtil.e(TAG, "isVaildResource: 未找到语言对配置: " + coupleCode);
            return new CheckResult(null, false, null, null);
        }

        List<String> rUrls = (List<String>) coupleCodeConfig.get("urls"); // 下载链接
        String baseUrlIfly = (String) offlineConfig.get("baseUrlIfly");
        String baseUrlNiuAndroid = (String) offlineConfig.get("baseUrlNiuAndroid");
        List<String> commonMtUrls = (List<String>) offlineConfig.get("iflyCommonUrls");
        Map<String, Double> totalSizes = (Map<String, Double>) offlineConfig.get("totalSizes");

        Map<String, String> missUrls = new HashMap<>();
        Map<String, Integer> missUrlsSizes = new HashMap<>();
        String iflyMtResPath = null;
        // 检查专有资源
        for (int i = 0; i < rUrls.size(); i++) {
            String rUrl = (String) rUrls.get(i);

            String dirPath = null;
            if (rUrl.endsWith(".zip")) {
                dirPath = rUrl.replace(".zip", "");
            }
            assert dirPath != null;
            if (rUrl.startsWith("baseUrlIfly/asr")) {
                String resCode = dirPath.substring(dirPath.lastIndexOf("/") + 1);

                dirPath = dirPath.replace("baseUrlIfly", "");
                dirPath = getIflyAsrResPath(resCode) + dirPath;
            } else if (dirPath.startsWith("baseUrlIfly/mt")) {
                String resCode = dirPath.substring(dirPath.lastIndexOf("/") + 1);
                resCode = resCode.replace("cn", "zh"); // 讯飞资源 "cnen" 替换 "zhen"

                dirPath = dirPath.replace("baseUrlIfly", "");
                dirPath = getIflyMtResPath(resCode) + dirPath;

                iflyMtResPath = getIflyMtResPath(resCode);
            } else if (dirPath.startsWith("baseUrlNiu")) {
                String resCode = dirPath.substring(dirPath.lastIndexOf("/") + 1);
                resCode = resCode.replace("2", ""); // 小牛资源 "zh2ko" 替换 "zhko"

                dirPath = dirPath.replace("baseUrlNiu", "");
                dirPath = getNiuResPath(resCode) + dirPath;
            }
            File f = new File(dirPath);
            if (!f.exists()) {
                String url = "";

                String urlSizeKey = rUrl;
                if (rUrl.startsWith("baseUrlIfly")) {
                    assert baseUrlIfly != null;
                    url = rUrl.replace("baseUrlIfly", baseUrlIfly);
                } else if (rUrl.startsWith("baseUrlNiu")) {
                    assert baseUrlNiuAndroid != null;
                    url = rUrl.replace("baseUrlNiu", baseUrlNiuAndroid);
                    urlSizeKey = rUrl.replace("baseUrlNiu", "baseUrlNiuAndroid");
                }
                AiSpeechLogUtil.d(TAG, "isVaildResource: 资源不存在: " + dirPath);
                missUrls.put(url, dirPath);

                missUrlsSizes.put(url, totalSizes.get(urlSizeKey).intValue());
            }
        }

        // 检查公共资源, 讯飞中英识别和公共翻译资源
        for (int i = 0; i < commonMtUrls.size(); i++) {
            String rUrl = (String) commonMtUrls.get(i);

            String dirPath = null;
            if (rUrl.endsWith(".zip")) {
                dirPath = rUrl.replace(".zip", "");
            }
            assert dirPath != null;
            if (dirPath.startsWith("baseUrlIfly/asr")) {
                dirPath = dirPath.replace("baseUrlIfly", "");
                if (dirPath.contains("cnen")) {
                    dirPath = getIflyAsrResPath("zh") + dirPath;
                } else continue;
            } else if (dirPath.startsWith("baseUrlIfly/mt")) {
                if (iflyMtResPath == null) continue; // 不需要使用那个讯飞离线翻译, 则不检查
                dirPath = dirPath.replace("baseUrlIfly", "");
                dirPath = iflyMtResPath + dirPath;
            }
            File f = new File(dirPath);
            if (!f.exists()) {
                String url = rUrl.replace("baseUrlIfly", baseUrlIfly);

                AiSpeechLogUtil.d(TAG, "isVaildResource: 资源不存在: " + dirPath);
                missUrls.put(url, dirPath);
                missUrlsSizes.put(url, totalSizes.get(rUrl).intValue());
            }
        }
//        missUrls.clear();
//        AiSpeechLogUtil.d(TAG, "isVaildResource: 缺少资源<下载链接, 本地路径>: " + missUrls);
//        AiSpeechLogUtil.d(TAG, "isVaildResource: 缺少资源<下载链接, zip包大小>: " + missUrlsSizes);
        return new CheckResult((String) coupleCodeConfig.get("code"), missUrls.size() == 0, missUrls, missUrlsSizes);
    }

    /**
     * key of supports, 根据语言对「en<->ja」检查本地资源包是否存在
     */
    private CheckResult isVaildResource2(String coupleCode) {
        assert offlineConfig != null : "未初始化离线管理器, 检查 loadOffline()";
        if (offlineConfig == null) return null;

        Map<String, Object> coupleCodeConfig = null;
        // 根据 code 找到 urls
        List<String> coupleCodeConfigs = (List<String>) offlineConfig.get("languagePairUrl");
        assert coupleCodeConfigs != null;
        for (Object o : coupleCodeConfigs) {
            List<String> resCodes = (List<String>) ((Map) o).get("resCodes");
            if (resCodes.contains(coupleCode)) {
                coupleCodeConfig = ((Map) o);
                break;
            }
        }
        if (coupleCodeConfig == null) {
            AiSpeechLogUtil.e(TAG, "isVaildResource2: 未找到语言对配置: " + coupleCode);
            return new CheckResult(null, false, null, null);
        }

        List<String> rUrls = (List<String>) coupleCodeConfig.get("urls"); // 下载链接
        String baseUrlXzy = (String) offlineConfig.get("baseUrlXzy");
        String baseUrlXzyTts = (String) offlineConfig.get("baseUrlXzyTts");
        String baseUrlNiu = (String) offlineConfig.get("baseUrlNiu");
        List<String> commonUrls = (List<String>) offlineConfig.get("commonUrls");
        Map<String, Double> totalSizes = (Map<String, Double>) offlineConfig.get("totalSizes");

        Map<String, String> missUrls = new HashMap<>();
        Map<String, Integer> missUrlsSizes = new HashMap<>();
        String iflyMtResPath = null;
        // 检查专有资源
        for (int i = 0; i < rUrls.size(); i++) {
            String rUrl = (String) rUrls.get(i);

            String dirPath = null;
            if (rUrl.endsWith(".zip")) {
                dirPath = rUrl.replace(".zip", "");
            }
            assert dirPath != null;
            if (rUrl.startsWith("baseUrlXzy/")) {
                String resCode = dirPath.substring(dirPath.lastIndexOf("/") + 1);

                dirPath = dirPath.replace("baseUrlXzy/", "/");
                dirPath = getXzyResPath(resCode) + dirPath;
            } else if (dirPath.startsWith("baseUrlXzyTts/")) {
                String resCode = dirPath.substring(dirPath.lastIndexOf("/") + 1);

                dirPath = dirPath.replace("baseUrlXzyTts/", "/");
                dirPath = getXzyTtsResPath(resCode) + dirPath;
            } else if (dirPath.startsWith("baseUrlNiu/")) {
                String resCode = dirPath.substring(dirPath.lastIndexOf("/") + 1);
                resCode = resCode.replace("2", ""); // 小牛资源 "zh2ko" 替换 "zhko"

                dirPath = dirPath.replace("baseUrlNiu/", "/");
                dirPath = getNiuResPath(resCode) + dirPath;
            }
            File f = new File(dirPath);
            if (!f.exists()) {
                String url = "";

                String urlSizeKey = rUrl;
                if (rUrl.startsWith("baseUrlXzy/")) {
                    assert baseUrlXzy != null;
                    url = rUrl.replace("baseUrlXzy/", baseUrlXzy+"/");
                } else if (rUrl.startsWith("baseUrlXzyTts/")) {
                    assert baseUrlXzyTts != null;
                    url = rUrl.replace("baseUrlXzyTts/", baseUrlXzyTts+"/");
                } else if (rUrl.startsWith("baseUrlNiu")) {
                    assert baseUrlNiu != null;
                    url = rUrl.replace("baseUrlNiu/", baseUrlNiu+"/");
                    urlSizeKey = rUrl.replace("baseUrlNiu/", "baseUrlNiu/");
                }
                AiSpeechLogUtil.d(TAG, "isVaildResource2: 资源不存在: " + dirPath);
                missUrls.put(url, dirPath);

                Double size = totalSizes.get(urlSizeKey);
                if (size != null) missUrlsSizes.put(url, size.intValue());
                else AiSpeechLogUtil.e(TAG, "isVaildResource2: totalSizes不存在: " + urlSizeKey);
            }
        }

        // 检查公共资源, 讯飞中英识别和公共翻译资源
        for (int i = 0; i < commonUrls.size(); i++) {
            String rUrl = (String) commonUrls.get(i);

            String dirPath = null;
            if (rUrl.endsWith(".zip")) {
                dirPath = rUrl.replace(".zip", "");
            }
            assert dirPath != null;
            if (dirPath.startsWith("baseUrlXzy/")) {
                dirPath = dirPath.replace("baseUrlXzy/", "/");
                if (dirPath.contains("cnen")) {
                    dirPath = getXzyResPath("zh") + dirPath;
                } else continue;
            }
            File f = new File(dirPath);
            if (!f.exists()) {
                String url = rUrl.replace("baseUrlXzy/", baseUrlXzy+"/");

                AiSpeechLogUtil.d(TAG, "isVaildResource2: 通用资源不存在: " + url + " -> " + dirPath);
                missUrls.put(url, dirPath);
                Double size = totalSizes.get(rUrl);
                if (size != null) missUrlsSizes.put(url, size.intValue());
                else AiSpeechLogUtil.e(TAG, "isVaildResource2: totalSizes不存在: " + rUrl);
            }
        }
//        missUrls.clear();
        AiSpeechLogUtil.d(TAG, "isVaildResource2: 缺少资源<下载链接, 本地路径>: " + missUrls);
        AiSpeechLogUtil.d(TAG, "isVaildResource2: 缺少资源<下载链接, zip包大小>: " + missUrlsSizes);
        return new CheckResult((String) coupleCodeConfig.get("code"), missUrls.size() == 0, missUrls, missUrlsSizes);
    }

    /**
     *  如果外部指定具体位置:
     *         Map<String, String> customResPaths = new HashMap<String, String>() {{
     *             // 讯飞离线识别
     *             put("zh", iflyResWorkDirPath);
     *             put("en", iflyResWorkDirPath);
     *             put("fr", ResWorkDirPath);
     *             put("ja", ResWorkDirPath);
     *             // 讯飞离线翻译
     *             put("zhen", iflyResWorkDirPath);
     *             put("enzh", iflyResWorkDirPath);
     *          }}
     *  根据 key 具体指定资源路径
     * @param resCode customResPaths 的 key
     * @return 返回具体路径
     */
    private String getIflyAsrResPath(String resCode) {
        String workDir = this.iflyResWorkDirPath;

        if (asrCustomResPaths != null && asrCustomResPaths.containsKey(resCode)) {
            workDir = asrCustomResPaths.get(resCode);
        }
        return workDir;
    }

    private String getIflyMtResPath(String resCode) {
        String workDir = this.iflyResWorkDirPath;

        if (mtCustomResPaths != null && mtCustomResPaths.containsKey(resCode)) {
            workDir = mtCustomResPaths.get(resCode);
        }
        return workDir;
    }

    private String getNiuResPath(String resCode) {
        String workDir = this.niuResWorkDirPath;

        if (mtCustomResPaths != null && mtCustomResPaths.containsKey(resCode)) {
            workDir = mtCustomResPaths.get(resCode);
        }
        return workDir;
    }

    /**
     *  如果外部指定具体位置:
     *         Map<String, String> customResPaths = new HashMap<String, String>() {{
     *             // 讯飞离线识别
     *             put("zh", iflyResWorkDirPath);
     *             put("en", iflyResWorkDirPath);
     *             put("fr", ResWorkDirPath);
     *             put("ja", ResWorkDirPath);
     *             // 讯飞离线翻译
     *             put("zhen", iflyResWorkDirPath);
     *             put("enzh", iflyResWorkDirPath);
     *          }}
     *  根据 key 具体指定资源路径
     * @param resCode customResPaths 的 key
     * @return 返回具体路径
     */
    private String getXzyResPath(String resCode) {
        String workDir = this.xzyResWorkDirPath;

        if (asrCustomResPaths != null && asrCustomResPaths.containsKey(resCode)) {
            workDir = asrCustomResPaths.get(resCode);
        }
        return workDir;
    }

    private String getXzyMtResPath(String resCode) {
        String workDir = this.xzyMtResWorkDirPath;

        if (mtCustomResPaths != null && mtCustomResPaths.containsKey(resCode)) {
            workDir = mtCustomResPaths.get(resCode);
        }
        return workDir;
    }

    private String getXzyTtsResPath(String resCode) {
        String workDir = this.xzyTtsResWorkDirPath;

        if (ttsCustomResPaths != null && ttsCustomResPaths.containsKey(resCode)) {
            workDir = ttsCustomResPaths.get(resCode);
        }
        return workDir;
    }

    private String getLangCouple(String srcCode, String dstCode, boolean isReverse) {
        String from = srcCode.split("-")[0].toLowerCase();
        String to = dstCode.split("-")[0].toLowerCase();

        return !isReverse ? from + CodeSeparateStr + to : to + CodeSeparateStr + from;
    }

    private String getLangCouple(String srcCode, String dstCode) {
        if (offlineConfig == null) return null;
//        HashMap = {"zhen", "enzh", "zhja", "jazh", "zhfr", "frzh", "zhes", "eszh", "zhru", "ruzh", "zhde", "dezh",
//                "zhko", "kozh", "enja", "jaen", "enko", "koen", "enfr", "fren", "enes", "esen", "enru", "ruen", "ende", "deen"};
        String from = srcCode.split("-")[0].toLowerCase();
        String to = dstCode.split("-")[0].toLowerCase();
        String coupleCode = from + to;

        List<String> coupleCodeConfigs = (List<String>) offlineConfig.get("languagePairUrl");
        assert coupleCodeConfigs != null;
        for (Object o : coupleCodeConfigs) {
            List<String> resCodes = (List<String>) ((Map) o).get("resCodes");
            if (resCodes.contains(coupleCode)) {
                return (String) ((Map) o).get("code");
            }
        }
        return null;
    }

    private String[] getKeysByLangCouple(String langCouple) {
        if (TextUtils.isEmpty(langCouple)) {
            return null;
        }
        return langCouple.split(CodeSeparateStr);
    }

    public String getKey(String fromCode, String toCode, boolean reverse) {
        String from = fromCode.split("-")[0].toLowerCase();
        String to = toCode.split("-")[0].toLowerCase();

        return !reverse ? from + to : to + from;
    }

    /**
     * 离线资源检测结果
     */
    public static class CheckResult {
        public boolean vaild; // true 则不需要下载, 此时urls.size() 为 0; false 则需要下载, 此时 urls 为 <下载链接, 本地路径> 的 Map
        public boolean isActive; //是否被激活了
        public String coupleCode; // 当前语言对
        public Map<String, String> urls; // 需要下载的资源, <url, local_path> 即 <下载链接, 本地路径>
        public Map<String, Integer> sizes; // 需要下载的资源, <url, size> 即 <下载链接, zip包大小>

        public boolean isActive() {
            return isActive;
        }

        public void setActive(boolean active) {
            isActive = active;
        }

        public CheckResult(String coupleCode, boolean vaild, Map<String, String> urls, Map<String, Integer> sizes) {
            this.vaild = vaild;
            this.urls = urls;
            this.sizes = sizes;
            this.coupleCode = coupleCode;
        }

        @Override
        public String toString() {
            return "CheckResult{" +
                    "vaild=" + vaild +
                    ", isActive=" + isActive +
                    ", coupleCode='" + coupleCode + '\'' +
                    ", urls=" + urls +
                    ", sizes=" + sizes +
                    '}';
        }
    }

    public static void main(String[] args) {
        String srcCode = "zh-CN";
        String dstCode = "en-US";
        String from = srcCode.split("-")[0].toLowerCase();
        String to = dstCode.split("-")[0].toLowerCase();
        String coupleCode = from + OfflineManager.getInstance().CodeSeparateStr + to;

//        OfflineManager.CheckResult cr = OfflineManager.getInstance().isReadyOffline(srcCode, dstCode); // 只检查资源是否存在
        CheckResult cr = OfflineManager.getInstance().openOffline(srcCode, dstCode); // 开启离线
        if (!cr.vaild) {
            // 离线资源未下载
            AiSpeechLogUtil.e(TAG, "开启离线失败: " + coupleCode + " 需要下载资源数: " + cr.urls.size());
        } else {
            // 离线资源已下载
            AiSpeechLogUtil.d(TAG, "开启离线: " + coupleCode);
        }
    }
}

