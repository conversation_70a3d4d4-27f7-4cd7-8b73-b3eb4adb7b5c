package co.timekettle.speech;

public class ServiceQualityResult {
   public String chkey;
   public String name;
   public String firmwareVersion;
   public String rssi;
   public String electric;
   public float velocity;
   public long loss;
   public long totalLoss;
   public long recv;
   public long total;
   public long createTime;
   public long destroyTime;
   public boolean isFinal;
   public boolean isDisconnect;

   ServiceQualityResult(Builder builder) {
      this.chkey = builder.chkey;
      this.name = builder.name;
      this.firmwareVersion = builder.firmwareVersion;
      this.rssi = builder.rssi;
      this.electric = builder.electric;
      this.velocity = builder.velocity;
      this.loss = builder.loss;
      this.totalLoss = builder.totalLoss;
      this.recv = builder.recv;
      this.total = builder.total;
      this.createTime = builder.createTime;
      this.destroyTime = builder.destroyTime;
      this.isFinal = builder.isFinal;
      this.isDisconnect = builder.isDisconnect;
   }

   @Override
   public String toString() {
      return "ServiceQualityResult{" +
              "chkey='" + chkey + '\'' +
              ", name='" + name + '\'' +
              ", firmwareVersion='" + firmwareVersion + '\'' +
              ", rssi='" + rssi + '\'' +
              ", electric='" + electric + '\'' +
              ", velocity=" + velocity +
              ", loss=" + loss +
              ", totalLoss=" + totalLoss +
              ", recv=" + recv +
              ", total=" + total +
              ", createTime=" + createTime +
              ", destroyTime=" + destroyTime +
              ", isFinal=" + isFinal +
              ", isDisconnect=" + isDisconnect +
              '}';
   }

   public static class Builder {
      public String chkey;
      public String name;
      public String firmwareVersion;
      public String rssi;
      public String electric;
      public float velocity;
      public long loss;
      public long totalLoss;
      public long recv;
      public long total;
      public long createTime;
      public long destroyTime;
      public boolean isFinal;
      public boolean isDisconnect;

      public ServiceQualityResult build() {
         return new ServiceQualityResult(this);
      }

      public Builder chkey(String chkey) {
         this.chkey = chkey;
         return this;
      }
      public Builder firmwareVersion(String firmwareVersion) {
         this.firmwareVersion = firmwareVersion;
         return this;
      }
      public Builder name(String name) {
         this.name = name;
         return this;
      }
      public Builder rssi(String rssi) {
         this.rssi = rssi;
         return this;
      }
      public Builder electric(String electric) {
         this.electric = electric;
         return this;
      }

      public Builder velocity(long velocity) {
         this.velocity = velocity;
         return this;
      }
      public Builder loss(long loss) {
         this.loss = loss;
         return this;
      }
      public Builder totalLoss(long totalLoss) {
         this.totalLoss = totalLoss;
         return this;
      }
      public Builder recv(long recv) {
         this.recv = recv;
         return this;
      }
      public Builder total(long total) {
         this.total = total;
         return this;
      }
      public Builder createTime(long createTime) {
         this.createTime = createTime;
         return this;
      }
      public Builder destroyTime(long destroyTime) {
         this.destroyTime = destroyTime;
         return this;
      }
      public Builder isFinal(boolean isFinal) {
         this.isFinal = isFinal;
         return this;
      }
      public Builder isDisconnect(boolean isDisconnect) {
         this.isDisconnect = isDisconnect;
         return this;
      }
   }
}