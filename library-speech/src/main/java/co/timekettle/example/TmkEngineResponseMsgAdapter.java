package co.timekettle.example;

import android.content.Context;
import android.graphics.Color;
import android.view.Gravity;
import android.view.View;
import android.view.ViewGroup;
import android.widget.BaseAdapter;
import android.widget.LinearLayout;
import android.widget.TextView;

import java.util.ArrayList;
import java.util.List;

import co.timekettle.speech.R;

public class TmkEngineResponseMsgAdapter extends BaseAdapter {

    private final Context context;
    private final List<TmkEngineResponseMsg> msgList = new ArrayList<>();

    public TmkEngineResponseMsgAdapter(Context context) {
        this.context = context;
    }

    public TmkEngineResponseMsg findMsg(long session) {
        for (TmkEngineResponseMsg msg : msgList) {
            if (msg.session == session) return msg;
        }
        return null;
    }

    public void addMsg(TmkEngineResponseMsg msg) {
        msgList.add(msg);
    }

    public void removeMsg(TmkEngineResponseMsg msg) {
    }

    public void clear() {
    }

    @Override
    public int getCount() {
        return msgList.size();
    }

    @Override
    public TmkEngineResponseMsg getItem(int position) {
        if (position > msgList.size())
            return null;
        return msgList.get(position);
    }

    @Override
    public long getItemId(int position) {
        return 0;
    }

    @Override
    public View getView(int position, View convertView, ViewGroup parent) {
        ViewHolder holder;
        if (convertView != null) {
            holder = (ViewHolder) convertView.getTag();
        } else {
            convertView = View.inflate(context, R.layout.tmkengine_adapter_msg, null);
            holder = new ViewHolder();
            convertView.setTag(holder);
            holder.txt_name = (TextView) convertView.findViewById(R.id.txt_name);
            holder.txt_log = (TextView) convertView.findViewById(R.id.txt_log);
        }

        final TmkEngineResponseMsg msg = getItem(position);
        if (msg != null) {
            holder.txt_name.setVisibility(View.GONE);
            holder.txt_log.setText(msg.asr.text + ((msg.mt.text != null ? "\n" + msg.mt.text : "") + (msg.tts.isPlaying ?"(play)":"")));
            holder.txt_log.setTextColor(!msg.isSelf ? Color.BLACK : Color.RED);
            ((LinearLayout)convertView).setGravity(msg.isSelf ? Gravity.END : Gravity.START);
            holder.txt_log.setTextAlignment(msg.isSelf ? View.TEXT_ALIGNMENT_TEXT_END : View.TEXT_ALIGNMENT_TEXT_START);
        }

        return convertView;
    }

    static class ViewHolder {
        TextView txt_name;
        TextView txt_log;
    }

    public interface OnMsgClickListener {
        void onMark(TmkEngineResponseMsg msg);
        void onDetail(TmkEngineResponseMsg msg);
    }

    private OnMsgClickListener mListener;
    public void setOnMsgClickListener(OnMsgClickListener listener) {
        this.mListener = listener;
    }

}
