package co.timekettle.example;

import java.io.File;
import java.io.FileOutputStream;
import java.io.IOException;

import co.timekettle.tmkengine.utils.TmkLogger;

public class AudioFileHandler {
    private final FileOutputStream fos;
    String filePath;

    public AudioFileHandler(String name, String dirPath) throws IOException {
        filePath = dirPath + "/" + name + "-" + System.currentTimeMillis() + ".pcm";

        File dirFile = new File(dirPath);
        if (!dirFile.exists()) {
            boolean ret = dirFile.mkdirs();
            if (!ret) TmkLogger.d("创建目录失败: " + dirPath);
        }
        File f = new File(filePath);
        if (!f.exists()) {
            boolean ret = f.createNewFile();
            if (!ret) TmkLogger.d("创建文件失败: " + filePath);
        }
        fos = new FileOutputStream(f);
    }

    public String getFilePath() {
        return filePath;
    }

    public void writeAndFlush(byte[] data) {
        if (fos != null) {
            try {
                fos.write(data);
                fos.flush();
            } catch (IOException e) {
                e.printStackTrace();
            }
        }
    }

    public void close() {
        try {
            if (fos != null) {
                fos.flush();
                fos.close();
            }
        } catch (IOException e) {
            e.printStackTrace();
        }
    }
}
