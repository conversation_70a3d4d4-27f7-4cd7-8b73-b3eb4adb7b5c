package co.timekettle.example;

public class TmkEngineResponseMsg {
   public String chkey;
   public long session;
   public String srcCode;
   public String dstCode;
   public boolean isSelf;

   public Asr asr = new Asr();
   public Mt mt = new Mt();
   public Tts tts = new Tts();

   public static class Asr {
      public boolean isLast;
      public String text;
      public String engine;
   }

   public static class Mt {
      public boolean isLast;
      public String text;
      public String engine;
   }

   public static class Tts {
      public boolean isPlaying;
      public byte[] data;
      public String engine;
   }
}
