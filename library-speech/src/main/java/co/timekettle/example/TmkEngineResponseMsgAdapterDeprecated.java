package co.timekettle.example;

import android.content.Context;
import android.view.View;
import android.view.ViewGroup;
import android.widget.BaseAdapter;
import android.widget.TextView;

import java.util.ArrayList;
import java.util.List;

import co.timekettle.speech.R;

public class TmkEngineResponseMsgAdapterDeprecated extends BaseAdapter {

    private final Context context;
    private final List<Object> msgList = new ArrayList<>();

    public TmkEngineResponseMsgAdapterDeprecated(Context context) {
        this.context = context;
    }

    public void addMsg(Object msg) {
        msgList.add(msg);
    }

    public void removeMsg(Object msg) {
//        for (int i = 0; i < msgList.size(); i++) {
//            Object msg = msgList.get(i);
//            if (msg.id.equals(msg.id)) {
//                msgList.remove(i);
//            }
//        }
    }

    public void clearConnectedMsg() {
//        for (int i = 0; i < msgList.size(); i++) {
//            Object msg = msgList.get(i);
//            if (msg.isConnected()) {
//                msgList.remove(i);
//            }
//        }
    }

    public void clearScanMsg() {
//        for (int i = 0; i < msgList.size(); i++) {
//            Object msg = msgList.get(i);
//            if (!msg.isConnected()) {
//                msgList.remove(i);
//            }
//        }
    }

    public void clear() {
        clearConnectedMsg();
        clearScanMsg();
    }

    @Override
    public int getCount() {
        return msgList.size();
    }

    @Override
    public Object getItem(int position) {
        if (position > msgList.size())
            return null;
        return msgList.get(position);
    }

    @Override
    public long getItemId(int position) {
        return 0;
    }

    @Override
    public View getView(int position, View convertView, ViewGroup parent) {
        ViewHolder holder;
        if (convertView != null) {
            holder = (ViewHolder) convertView.getTag();
        } else {
            convertView = View.inflate(context, R.layout.tmkengine_adapter_msg, null);
            holder = new ViewHolder();
            convertView.setTag(holder);
            holder.txt_name = (TextView) convertView.findViewById(R.id.txt_name);
            holder.txt_log = (TextView) convertView.findViewById(R.id.txt_log);
        }

        final Object msg = getItem(position);
        if (msg != null) {
//            holder.txt_name.setText("??");
            holder.txt_name.setVisibility(View.GONE);
            holder.txt_log.setText((String)msg);
        }

        return convertView;
    }

    static class ViewHolder {
        TextView txt_name;
        TextView txt_log;
    }

    public interface OnMsgClickListener {
        void onMark(Object msg);

        void onDetail(Object msg);
    }

    private OnMsgClickListener mListener;

    public void setOnMsgClickListener(OnMsgClickListener listener) {
        this.mListener = listener;
    }

}
