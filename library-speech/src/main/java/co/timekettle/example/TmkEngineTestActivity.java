package co.timekettle.example;

import android.media.AudioFormat;
import android.media.AudioManager;
import android.media.AudioTrack;
import android.os.Bundle;
import android.os.Handler;
import android.text.TextUtils;
import android.util.Log;
import android.view.MotionEvent;
import android.view.View;
import android.widget.AbsListView;
import android.widget.AdapterView;
import android.widget.ArrayAdapter;
import android.widget.Button;
import android.widget.EditText;
import android.widget.LinearLayout;
import android.widget.ListView;
import android.widget.Spinner;
import android.widget.Switch;
import android.widget.TextView;
import android.widget.Toast;

import androidx.appcompat.app.AppCompatActivity;

import java.io.ByteArrayOutputStream;
import java.io.File;
import java.io.FileInputStream;
import java.io.FileNotFoundException;
import java.io.IOException;
import java.io.InputStream;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Objects;
import java.util.concurrent.ConcurrentHashMap;

import co.timekettle.speech.AiSpeechManager;
import co.timekettle.speech.EngineHost;
import co.timekettle.speech.R;
import co.timekettle.tmkengine.JsonRequest;
import co.timekettle.tmkengine.NetSessionContext;
import co.timekettle.tmkengine.TmkSpeechClient;
import co.timekettle.tmkengine.utils.TimeUtil;
import co.timekettle.tmkengine.utils.TmkLogger;

public class TmkEngineTestActivity extends AppCompatActivity implements View.OnClickListener {
    private static final String TAG = "TmkEngineTestActivity";
    private Spinner spin_langcode;
    private LinearLayout layout_langcode_other;
    private Spinner spin_langcode_other;
    private Spinner spin_type;
    private Spinner spin_engine;
    private Spinner spin_host;
    private EditText et_langcode;
    private EditText et_langcode_other;
    private EditText et_type;
    private EditText et_engine;
    private EditText et_sample_text;
    private EditText et_host;
    private Switch sw_opus;

    private LinearLayout layout_setting;
    private TextView txt_tip;
    private TextView txt_setting;
    private Button btn_test;

    private String showSettingsDesc = "展开设置";
    private String hideSettingsDesc = "收起设置";
    private TmkEngineResponseMsgAdapterDeprecated msgAdapter;

    AudioTrack mAudioTrack;

    public enum MainLangCode {
        ZH("zh-CN"),
        EN("en-US"),
        JA("ja-JP"),
        KO("ko-KR"),
        FR("fr-FR"),
        ES("es-ES"),
        RU("ru-RU"),
        DE("de-DE"),
        TH("th-TH");
        public final String code;

        MainLangCode(String code) {
            this.code = code;
        }
    }

    static class LangCode {
        private final String code;
        private final String desc;
        private final String sampleText;
        LangCode(String code, String desc, String sampleText) {
            this.code = code;
            this.desc = desc;
            this.sampleText = sampleText;
        }
    }

    static LangCode[] LangCodes = {
            new LangCode("ar-AE", "阿拉伯语(阿联酋)", "مرحبًا ، من فضلك لا تستخدم الأرقام."),
            new LangCode("ar-BH", "阿拉伯语(巴林)", "مرحبًا ، من فضلك لا تستخدم الأرقام."),
            new LangCode("ar-DZ", "阿拉伯语(阿尔及利亚)", "مرحبًا ، من فضلك لا تستخدم الأرقام."),
            new LangCode("ar-EG", "阿拉伯语(埃及)", "مرحبًا ، من فضلك لا تستخدم الأرقام."),
            new LangCode("ar-IL", "阿拉伯语(以色列)", "مرحبًا ، من فضلك لا تستخدم الأرقام."),
            new LangCode("ar-IQ", "阿拉伯语(伊拉克)", "مرحبًا ، من فضلك لا تستخدم الأرقام."),
            new LangCode("ar-JO", "阿拉伯语(约旦)", "مرحبًا ، من فضلك لا تستخدم الأرقام."),
            new LangCode("ar-KW", "阿拉伯语(科威特)", "مرحبًا ، من فضلك لا تستخدم الأرقام."),
            new LangCode("ar-LB", "阿拉伯语(黎巴嫩)", "مرحبًا ، من فضلك لا تستخدم الأرقام."),
            new LangCode("ar-MA", "阿拉伯语(摩洛哥)", "مرحبًا ، من فضلك لا تستخدم الأرقام."),
            new LangCode("ar-OM", "阿拉伯语(阿曼)", "مرحبًا ، من فضلك لا تستخدم الأرقام."),
            new LangCode("ar-PS", "阿拉伯语(巴勒斯坦)", "مرحبًا ، من فضلك لا تستخدم الأرقام."),
            new LangCode("ar-QA", "阿拉伯语(卡塔尔)", "مرحبًا ، من فضلك لا تستخدم الأرقام."),
            new LangCode("ar-SA", "阿拉伯语(沙特阿拉伯)", "مرحبًا ، من فضلك لا تستخدم الأرقام."),
            new LangCode("ar-TN", "阿拉伯语(突尼斯)", "مرحبًا ، من فضلك لا تستخدم الأرقام."),
            new LangCode("bg-BG", "保加利亚语(保加利亚)", "Здравейте, моля, не използвайте числа."),
            new LangCode("zh-HK", "中文(粤语)", "你好，請不要使用數字。"),
            new LangCode("zh-TW", "中文(繁体)", "你好，請不要使用數字。"),
            new LangCode("zh-CN", "中文(简体)", "你好，请不要使用数字。"),
            new LangCode("ca-ES", "加泰罗尼亚语(西班牙)", "Hola, no feu servir números."),
            new LangCode("cs-CZ", "捷克语(捷克)", "Dobrý den, prosím nepoužívejte čísla."),
            new LangCode("da-DK", "丹麦语(丹麦)", "Hej, brug venligst ikke tal."),
            new LangCode("en-AU", "英语(澳大利亚)", "Hi, please don't use numbers."),
            new LangCode("en-CA", "英语(加拿大)", "Hi, please don't use numbers."),
            new LangCode("en-GB", "英语(英国)", "Hi, please don't use numbers."),
            new LangCode("en-GH", "英语(加纳)", "Hi, please don't use numbers."),
            new LangCode("en-IE", "英语(爱尔兰)", "Hi, please don't use numbers."),
            new LangCode("en-IN", "英语(印度)", "Hi, please don't use numbers."),
            new LangCode("en-US", "英语(美国)", "Hi, please don't use numbers."),
            new LangCode("en-KE", "英语(肯尼亚)", "Hi, please don't use numbers."),
            new LangCode("en-NG", "英语(尼日利亚)", "Hi, please don't use numbers."),
            new LangCode("en-PH", "英语(菲律宾)", "Hi, please don't use numbers."),
            new LangCode("en-TZ", "英语(坦桑尼亚)", "Hi, please don't use numbers."),
            new LangCode("en-ZA", "英语(南非)", "Hi, please don't use numbers."),
            new LangCode("en-NZ", "英语(新西兰)", "Hi, please don't use numbers."),
            new LangCode("en-SG", "英语(新加坡)", "Hi, please don't use numbers."),
            new LangCode("fi-FI", "芬兰语(芬兰)", "Hei, älä käytä numeroita."),
            new LangCode("fr-FR", "法语(法国)", "Bonjour, merci de ne pas utiliser de chiffres."),
            new LangCode("fr-CA", "法语(加拿大)", "Bonjour, merci de ne pas utiliser de chiffres."),
            new LangCode("el-GR", "希腊语(希腊)", "Γεια σας, μην χρησιμοποιείτε αριθμούς."),
            new LangCode("de-DE", "德语(德国)", "Hallo, bitte keine Nummern verwenden."),
            new LangCode("he-IL", "希伯来语(以色列)", "שלום, נא לא להשתמש במספרים."),
            new LangCode("hi-IN", "印地语(印度)", "नमस्ते, कृपया संख्याओं का उपयोग न करें।"),
            new LangCode("hr-HR", "克罗地亚语(克罗地亚)", "Bok, molim te ne koristi brojeve."),
            new LangCode("hu-HU", "匈牙利语(匈牙利)", "Szia, kérlek, ne használj számokat."),
            new LangCode("it-IT", "意大利语(意大利)", "Ciao, per favore non usare i numeri."),
            new LangCode("id-ID", "印尼语(印尼)", "Hai, tolong jangan gunakan angka."),
            new LangCode("ja-JP", "日语(日本)", "こんにちは、数字を使用しないでください。"),
            new LangCode("ko-KR", "韩语(韩国)", "안녕하세요, 숫자를 사용하지 마십시오."),
            new LangCode("ms-MY", "马来语(马来西亚)", "Hai, tolong jangan gunakan nombor."),
            new LangCode("nl-NL", "荷兰语(荷兰)", "Hallo, gebruik geen cijfers."),
            new LangCode("nb-NO", "挪威语(挪威)", "Hei, ikke bruk tall."),
            new LangCode("pl-PL", "波兰语(波兰)", "Cześć, proszę nie używać cyfr."),
            new LangCode("pt-PT", "葡萄牙语(葡萄牙)", "Olá, por favor, não use números."),
            new LangCode("pt-BR", "葡萄牙语(巴西)", "Olá, por favor, não use números."),
            new LangCode("ro-RO", "罗马尼亚语(罗马尼亚)", "Bună, vă rog să nu folosiți numere."),
            new LangCode("ru-RU", "俄语(俄罗斯)", "Здравствуйте, пожалуйста, не используйте цифры."),
            new LangCode("es-ES", "西班牙语(西班牙)", "Hola, por favor no use números."),
            new LangCode("es-US", "西班牙语(美国)", "Hola, por favor no use números."),
            new LangCode("es-AR", "西班牙语(阿根廷)", "Hola, por favor no use números."),
            new LangCode("es-BO", "西班牙语(玻利维亚)", "Hola, por favor no use números."),
            new LangCode("es-CL", "西班牙语(智利)", "Hola, por favor no use números."),
            new LangCode("es-CO", "西班牙语(哥伦比亚)", "Hola, por favor no use números."),
            new LangCode("es-CR", "西班牙语(哥斯达黎加)", "Hola, por favor no use números."),
            new LangCode("es-DO", "西班牙语(多米尼克)", "Hola, por favor no use números."),
            new LangCode("es-EC", "西班牙语(厄瓜多尔)", "Hola, por favor no use números."),
            new LangCode("es-GT", "西班牙语(危地马拉)", "Hola, por favor no use números."),
            new LangCode("es-HN", "西班牙语(洪都拉斯)", "Hola, por favor no use números."),
            new LangCode("es-MX", "西班牙语(墨西哥)", "Hola, por favor no use números."),
            new LangCode("es-NI", "西班牙语(尼加拉瓜)", "Hola, por favor no use números."),
            new LangCode("es-PA", "西班牙语(巴拿马)", "Hola, por favor no use números."),
            new LangCode("es-PE", "西班牙语(秘鲁)", "Hola, por favor no use números."),
            new LangCode("es-PR", "西班牙语(波多黎各)", "Hola, por favor no use números."),
            new LangCode("es-PY", "西班牙语(巴拉圭)", "Hola, por favor no use números."),
            new LangCode("es-SV", "西班牙语(萨尔瓦多)", "Hola, por favor no use números."),
            new LangCode("es-VE", "西班牙语(委内瑞拉)", "Hola, por favor no use números."),
            new LangCode("es-UY", "西班牙语(乌拉圭)", "Hola, por favor no use números."),
            new LangCode("sk-SK", "斯洛伐克语(斯洛伐克)", "Dobrý deň, prosím nepoužívajte čísla."),
            new LangCode("sl-SI", "斯洛文尼亚语(斯洛文尼亚)", "Pozdravljeni, prosim, ne uporabljajte številk."),
            new LangCode("sv-SE", "瑞典语(瑞典)", "Hej, använd inte siffror."),
            new LangCode("ta-IN", "泰米尔语(印度)", "வணக்கம், எண்களைப் பயன்படுத்த வேண்டாம்."),
            new LangCode("ta-SG", "泰米尔语(新加坡)", "வணக்கம், எண்களைப் பயன்படுத்த வேண்டாம்."),
            new LangCode("ta-LK", "泰米尔语(斯里兰卡)", "வணக்கம், எண்களைப் பயன்படுத்த வேண்டாம்."),
            new LangCode("ta-MY", "泰米尔语(马来西亚)", "வணக்கம், எண்களைப் பயன்படுத்த வேண்டாம்."),
            new LangCode("te-IN", "泰卢固语(印度)", "హాయ్, దయచేసి నంబర్‌లను ఉపయోగించవద్దు."),
            new LangCode("th-TH", "泰语(泰国)", "สวัสดี โปรดอย่าใช้ตัวเลข"),
            new LangCode("tr-TR", "土耳其语(土耳其)", "Merhaba, lütfen rakam kullanmayın."),
            new LangCode("vi-VN", "越南语(越南)", "Xin chào, vui lòng không sử dụng số."),
            new LangCode("ur-PK", "乌尔都语(巴基斯坦)", "ہیلو، براہ کرم نمبر استعمال نہ کریں۔"),
            new LangCode("ur-IN", "乌尔都语(印度)", "ہیلو، براہ کرم نمبر استعمال نہ کریں۔"),
            new LangCode("uk-UA", "乌克兰语(乌克兰)", "Привіт, будь ласка, не використовуйте цифри."),
            new LangCode("is-IS", "冰岛语(冰岛)", "Hæ, vinsamlegast ekki nota tölur."),
            new LangCode("fil-PH", "菲律宾语(菲律宾)", "Hello po, wag po sana gumamit ng numbers."),
    };
    private static String WorkDir;
    ConcurrentHashMap<Long, NetSessionContext> netSessionContexts = new ConcurrentHashMap<>();

    private Handler handler = new Handler();
    private boolean isScrolling = false;
    private Runnable autoScrollRunnable; // 定义自动滚动的延迟任务
    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setContentView(R.layout.tmkengine_sample_activity);

        spin_langcode = findViewById(R.id.spin_langcode);
        layout_langcode_other = findViewById(R.id.layout_langcode_other);
        spin_langcode_other = findViewById(R.id.spin_langcode_other);
        spin_type = findViewById(R.id.spin_type);
        spin_engine = findViewById(R.id.spin_engine);
        spin_host = findViewById(R.id.spin_host);

        layout_setting = (LinearLayout) findViewById(R.id.layout_setting);
        txt_setting = (TextView) findViewById(R.id.txt_setting);
        txt_setting.setOnClickListener(this);
        layout_setting.setVisibility(View.GONE);
        txt_setting.setText(showSettingsDesc);

        btn_test = (Button) findViewById(R.id.btn_test);
        btn_test.setOnClickListener(this);

        ListView msgListView = (ListView) findViewById(R.id.list_msg);
        msgListView.setAdapter(getAdapter());
//        msgListView.setTranscriptMode(ListView.TRANSCRIPT_MODE_ALWAYS_SCROLL);
        // 在触摸事件监听器中使用 Handler 进行延迟操作
        msgListView.setOnTouchListener((v, event) -> {
            int action = event.getAction();

            switch (action) {
                case MotionEvent.ACTION_DOWN:
                    // 按下触摸事件时，禁用自动滚动并取消之前的延迟任务
                    msgListView.setTranscriptMode(ListView.TRANSCRIPT_MODE_DISABLED);
                    handler.removeCallbacks(autoScrollRunnable);
                    isScrolling = true;
                    break;
                case MotionEvent.ACTION_UP:
                    // 抬起触摸事件时，延迟 5 秒后启用自动滚动
                    handler.postDelayed(autoScrollRunnable, 5000);
                    break;
            }

            return false;
        });
        // 在 ListView 的滚动监听器中判断是否处于手动滚动状态
        msgListView.setOnScrollListener(new AbsListView.OnScrollListener() {
            @Override
            public void onScrollStateChanged(AbsListView view, int scrollState) {
                // 如果滚动状态为静止且之前处于手动滚动状态，则重新开始延迟任务
                if (scrollState == SCROLL_STATE_IDLE && isScrolling) {
                    handler.postDelayed(autoScrollRunnable, 5000);
                }
            }

            @Override
            public void onScroll(AbsListView view, int firstVisibleItem, int visibleItemCount, int totalItemCount) {
                // 不需要实现该方法，可以为空
            }
        });
        autoScrollRunnable = () -> {
            // 启用自动滚动
            msgListView.setTranscriptMode(ListView.TRANSCRIPT_MODE_ALWAYS_SCROLL);
            isScrolling = false;
        };

        et_type = findViewById(R.id.et_type);
        et_engine = findViewById(R.id.et_engine);
        et_langcode = findViewById(R.id.et_langcode);
        et_langcode_other = findViewById(R.id.et_langcode_other);
        et_sample_text = findViewById(R.id.et_sample_text);
        txt_tip = findViewById(R.id.txt_tip);
        et_host = findViewById(R.id.et_host);
        sw_opus = findViewById(R.id.sw_opus);
//        sw_opus.setOnCheckedChangeListener((buttonView, isChecked) -> {
//            ISpeechConstant.useOpus = isChecked;
//            Log.d(TAG, "是否开启 opus: " + ISpeechConstant.useOpus);
//        });
        sw_opus.setChecked(true);

        String[] langcodes = new String[LangCodes.length];
        for (int i = 0; i < LangCodes.length; i++) {
            langcodes[i] = LangCodes[i].code + "(" + LangCodes[i].desc;
        }
        ArrayAdapter<String> langcodeAdapter = new ArrayAdapter<String>(this, android.R.layout.simple_spinner_item, langcodes);
        langcodeAdapter.setDropDownViewResource(android.R.layout.simple_spinner_item);
        spin_langcode.setAdapter(langcodeAdapter);
        spin_langcode.setOnItemSelectedListener(new AdapterView.OnItemSelectedListener() {
            @Override
            public void onItemSelected(AdapterView<?> adapterView, View view, int i, long l) {
                et_langcode.setText(LangCodes[i].code);

                et_sample_text.setText(LangCodes[i].sampleText);
            }

            @Override
            public void onNothingSelected(AdapterView<?> adapterView) {
                et_langcode.setText("");
            }
        });
        ArrayAdapter<String> langcodeotherAdapter = new ArrayAdapter<String>(this, android.R.layout.simple_spinner_item, langcodes);
        langcodeotherAdapter.setDropDownViewResource(android.R.layout.simple_spinner_item);
        spin_langcode_other.setAdapter(langcodeotherAdapter);
        spin_langcode_other.setOnItemSelectedListener(new AdapterView.OnItemSelectedListener() {
            @Override
            public void onItemSelected(AdapterView<?> adapterView, View view, int i, long l) {
                et_langcode_other.setText(LangCodes[i].code);
            }

            @Override
            public void onNothingSelected(AdapterView<?> adapterView) {
                et_langcode_other.setText("");
            }
        });


        String[] types = (String[]) Arrays.stream(JsonRequest.Type.values()).map((JsonRequest.Type::getCode)).toArray(String[]::new);
        ArrayAdapter<String> typeAdapter = new ArrayAdapter<String>(this, android.R.layout.simple_spinner_item, types);
        typeAdapter.setDropDownViewResource(android.R.layout.simple_spinner_item);
        spin_type.setAdapter(typeAdapter);
        spin_type.setOnItemSelectedListener(new AdapterView.OnItemSelectedListener() {
            @Override
            public void onItemSelected(AdapterView<?> adapterView, View view, int i, long l) {
                et_type.setText(types[i]);

                JsonRequest.Type type = JsonRequest.Type.get(et_type.getText().toString());
                boolean isTranslteOrSynthesize = type == JsonRequest.Type.Translate || type == JsonRequest.Type.Synthesize;
                et_sample_text.setVisibility(isTranslteOrSynthesize ? View.VISIBLE : View.GONE);
                boolean isDoubleLang = type != null && type.isDoubleLang();
                layout_langcode_other.setVisibility(isDoubleLang ? View.VISIBLE : View.GONE);

                boolean isAsr = type != null && type.isAsr();
                sw_opus.setVisibility(isAsr ? View.VISIBLE : View.GONE);
                if (isAsr && (SamplesFiles == null || SamplesFiles.length == 0)) {
                    txt_tip.setText("sdcard 目录 'cache/测试音频素材' 不存在音频测试文件, 请导入");
                } else {
                    txt_tip.setText("");
                }
            }

            @Override
            public void onNothingSelected(AdapterView<?> adapterView) {
                et_type.setText("");
            }
        });

        String[] engines = new String[]{JsonRequest.Engine.Default.name, JsonRequest.Engine.Microsoft.name, JsonRequest.Engine.Google.name, JsonRequest.Engine.Iflytek.name, JsonRequest.Engine.Newtranx.name};
        ArrayAdapter<String> engineAdapter = new ArrayAdapter<String>(this, android.R.layout.simple_spinner_item, engines);
        engineAdapter.setDropDownViewResource(android.R.layout.simple_spinner_item);
        spin_engine.setAdapter(engineAdapter);
        spin_engine.setOnItemSelectedListener(new AdapterView.OnItemSelectedListener() {
            @Override
            public void onItemSelected(AdapterView<?> adapterView, View view, int i, long l) {
                et_engine.setText(engines[i]);
            }

            @Override
            public void onNothingSelected(AdapterView<?> adapterView) {
                et_engine.setText("");
            }
        });

        spin_host.setOnItemSelectedListener(new AdapterView.OnItemSelectedListener() {
            @Override
            public void onItemSelected(AdapterView<?> adapterView, View view, int i, long l) {
                if (showHosts == null || showHosts.size() == 0) {
                    et_host.setText("");
                } else {
                    et_host.setText(showHosts.get(i) == null ? "" : showHosts.get(i).ip + ":" + showHosts.get(i).port);
                }
                selectedHost = showHosts.get(i);
            }

            @Override
            public void onNothingSelected(AdapterView<?> adapterView) {
                et_host.setText("");
                selectedHost = null;
            }
        });

        this.initSdk();

        // 初始化播放器
        int mSampleRate = 16000;
        int outputBufferSize = AudioTrack.getMinBufferSize(mSampleRate, AudioFormat.CHANNEL_OUT_MONO, AudioFormat.ENCODING_PCM_16BIT);
        try{
            mAudioTrack = new AudioTrack(AudioManager.STREAM_MUSIC, mSampleRate, AudioFormat.CHANNEL_OUT_MONO, AudioFormat.ENCODING_PCM_16BIT, outputBufferSize, AudioTrack.MODE_STREAM);
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    @Override
    protected void onDestroy() {
        super.onDestroy();

        TmkSpeechClient.shareInstance().destoryUtility();
        netSessionContexts.entrySet().forEach((entry) -> {
            NetSessionContext netSessionContext = entry.getValue();
            if (!netSessionContext.isInvalid()) {
                netSessionContext.cancel();
            }
        });
    }

    private TmkEngineResponseMsgAdapterDeprecated getAdapter() {
        if (this.msgAdapter == null) {
            TmkEngineResponseMsgAdapterDeprecated msgAdapter = new TmkEngineResponseMsgAdapterDeprecated(this);
            msgAdapter.setOnMsgClickListener(new TmkEngineResponseMsgAdapterDeprecated.OnMsgClickListener() {
                @Override
                public void onMark(Object msg) {

                }

                @Override
                public void onDetail(Object msg) {

                }
            });
            this.msgAdapter = msgAdapter;
        }
        return this.msgAdapter;
    }

    List<EngineHost> showHosts = new ArrayList<>();
    EngineHost selectedHost;
    void initSdk() {
        TmkSpeechClient.shareInstance().createUtility(this, "9A5C1E41066458D50F91636A111FED89");
//        TmkSpeechClient.shareInstance().setSpecificHost(new EngineHost("***********", 5050));
        String params = ""; // "?param1=0";
        String BaseURL = "http://*************:30013";
        String Api = "/terptrans/user/mediate/routes";
        String url = BaseURL + Api + params;
        AiSpeechManager.shareInstance().fetchHosts(url, (hosts, error) -> {
            TmkSpeechClient.shareInstance().setSpecificHosts(hosts);

            showHosts.add(null);
            showHosts.addAll(hosts);
            runOnUiThread(() -> {
                String[] _strHosts = new String[showHosts.size()];
                for (int i = 0; i < showHosts.size(); i++) {
                    if (showHosts.get(i) == null) {
                        _strHosts[i] = "指定通讯IP";
                        continue;
                    }
                    _strHosts[i] = showHosts.get(i).ip + ":" + showHosts.get(i).port;
                }
                ArrayAdapter<String> hostsAdapter = new ArrayAdapter<String>(this, android.R.layout.simple_spinner_item, _strHosts);
                hostsAdapter.setDropDownViewResource(android.R.layout.simple_spinner_item);
                spin_host.setAdapter(hostsAdapter);
            });
            return null;
        });
        TmkLogger.setLogCallback((int level, String tag, String msg) -> {
            Log.e(tag, msg);
            runOnUiThread(() -> {
                this.msgAdapter.addMsg(TimeUtil.getShortCurrentTime() + " " + msg);
                this.msgAdapter.notifyDataSetChanged();
            });
        });
        TmkLogger.enableFileLogger(this, true);

        WorkDir = this.getExternalCacheDir().getAbsolutePath();
        fetchSoundFiles(); // 获取所有音频示例文件
    }

    JsonRequest.Type getTaskType(String code) {
        JsonRequest.Type type = JsonRequest.Type.get(code);
        if (type != null) return type;
        else return JsonRequest.Type.Recognize;
    }

    NetSessionContext.ContextListener getListener(JsonRequest.Type taskType) {
        AudioFileHandler audioWriter = null;
        if (taskType == JsonRequest.Type.Synthesize) {
            try {
                audioWriter = new AudioFileHandler("synthesize", WorkDir + "/audio/");
            } catch (IOException e) {
                e.printStackTrace();
            }
        }
        AudioFileHandler finalAudioWriter = audioWriter;
        NetSessionContext.ContextListener contextListener = new NetSessionContext.ContextListener() {
            @Override
            public void onRecognizeResult(NetSessionContext context, long session, boolean isLast, String srcCode, String rtext, String ttext, String engine) {
//                Log.d(TAG, "onRecognizeResult " + session + " engine " + engine + "  " + rtext + " | " + ttext);
            }

            @Override
            public void onTranslateResult(NetSessionContext context, long session, String result, String engine) {
//                Log.d(TAG, "onTranslateResult " + session + " engine " + engine + "  " + result);
            }

            @Override
            public void onSynthesizeBuffer(NetSessionContext context, long session, byte[] output, int outputSize) {
                if (finalAudioWriter != null) finalAudioWriter.writeAndFlush(output);
            }

            @Override
            public void onCompleted(NetSessionContext context, long session, String engine) {
                if (finalAudioWriter != null) finalAudioWriter.close();
                if (finalAudioWriter != null) {
                    mAudioTrack.play();
                    byte[] buffer = InputStream2ByteArray(finalAudioWriter.getFilePath());
                    mAudioTrack.write(buffer, 0, buffer.length);
                }
            }

            @Override
            public void onError(NetSessionContext context, long session, String engine, int code, String message) {

            }
        };
        return contextListener;
    }

    public byte[] DefaultInputStream2ByteArray(String path) {

        try {
            InputStream in = this.getAssets().open(path);

            ByteArrayOutputStream out = new ByteArrayOutputStream();
            byte[] buffer = new byte[1024 * 4];
            int n = 0;
            while ((n = in.read(buffer)) != -1) {
                out.write(buffer, 0, n);
            }

            in.close();

            return out.toByteArray();
        } catch (FileNotFoundException e) {
            e.printStackTrace();
        } catch (IOException e) {
            e.printStackTrace();
        }

        return new byte[0];
    }

    public byte[] InputStream2ByteArray(String filePath) {

        try {
            InputStream in = new FileInputStream(filePath);

            ByteArrayOutputStream out = new ByteArrayOutputStream();
            byte[] buffer = new byte[1024 * 4];
            int n = 0;
            while ((n = in.read(buffer)) != -1) {
                out.write(buffer, 0, n);
            }

            in.close();

            return out.toByteArray();
        } catch (FileNotFoundException e) {
            e.printStackTrace();
        } catch (IOException e) {
            e.printStackTrace();
        }

        return new byte[0];
    }

    File[] SamplesFiles;
    void fetchSoundFiles() {
        boolean existFile = false;
        File dir = new File(WorkDir + "/测试音频素材");
        if (dir.isDirectory() && dir.exists()) {
            File[] subFiles = dir.listFiles();
            assert subFiles != null;
            SamplesFiles = Arrays.stream(subFiles).filter((file -> file.getAbsolutePath().endsWith(".pcm"))).toArray(File[]::new);
            existFile = SamplesFiles.length > 0;
        }

        if (!existFile) {
            Toast.makeText(this, "sdcard 目录 'cache/测试音频素材' 不存在音频测试文件, 请导入", Toast.LENGTH_SHORT).show();
            txt_tip.setText("sdcard 目录 'cache/测试音频素材' 不存在音频测试文件, 请导入");
        }
    }

    File findSoundFile(String code) {
        if (SamplesFiles == null || SamplesFiles.length == 0) {
            runOnUiThread(() -> {
                Toast.makeText(this, "sdcard 目录 'cache/测试音频素材' 不存在音频测试文件, 请导入", Toast.LENGTH_SHORT).show();
                txt_tip.setText("sdcard 目录 'cache/测试音频素材' 不存在音频测试文件, 请导入");
            });
            return null;
        }
        for (File samplesFile : SamplesFiles) {
            if (samplesFile.getAbsolutePath().contains(code)) return samplesFile;
        }
//        String path = SamplesFiles[0].getAbsolutePath();
//        String filename = path.substring(path.lastIndexOf("/") + 1);
        return null;
    }

    void doTask(String code, String dstCode, String text, String hostStr, boolean useOpus, String engine, String sType) {
        JsonRequest.Type taskType = getTaskType(sType);

        boolean enableOpus = useOpus;
        if (!Objects.equals(hostStr, "")) {
            boolean valid = hostStr.matches("\\d{1,3}\\.\\d{1,3}\\.\\d{1,3}\\.\\d{1,3}:\\d{1,5}");
            if (!valid) {
                runOnUiThread(() -> {
                    txt_tip.setText("输入指定引擎格式错误, 请检查格式, 如: ************:5050");
                    Toast.makeText(this, "输入指定引擎格式错误, 请检查格式, 如: ************:5050", Toast.LENGTH_SHORT).show();
                });
                return;
            }
            String[] ipAndPost = hostStr.split(":");
            EngineHost host = new EngineHost(ipAndPost[0], Integer.parseInt(ipAndPost[1]));
            TmkSpeechClient.shareInstance().setSpecificHost(host);
        } else {
            TmkSpeechClient.shareInstance().setSpecificHost(null);
        }
        TmkSpeechClient.shareInstance().setSpecificEngine(!TextUtils.isEmpty(engine) ? engine : null);
        new Thread(() -> {
            boolean isAsr = taskType != null && taskType.isAsr();
            if (isAsr) {
                NetSessionContext context;
                if (taskType == JsonRequest.Type.Recognize) {
                    context = TmkSpeechClient.shareInstance().createRecognizer(code, enableOpus, getListener(taskType));
                } else {
                    context = TmkSpeechClient.shareInstance().createSpeechTranslation(code, dstCode, taskType, enableOpus, getListener(taskType));
                }
                netSessionContexts.put(context.getSession(), context);

                // 查找文件
                File file = findSoundFile(code);
                String path = "audio_samples/16ktts(en-US).pcm";
                byte[] data;
                if (file == null) {
                    String finalPath = path;
                    runOnUiThread(() -> {
                        txt_tip.setText("不存在 " + code + " 相关音频测试文件, 使用内置默认音频: " + finalPath);
                    });
                    Log.d(TAG, "不存在 " + code + " 相关音频测试文件, 使用内置默认音频: " + path);
                    data = DefaultInputStream2ByteArray(path);
                } else {
                    path = file.getAbsolutePath();
                    data = InputStream2ByteArray(path);
                }
                context.start();
                {
                    int nLen = 640;
                    byte[] frame = new byte[nLen];
                    int sentCount = data.length / nLen;
                    double sentTime = 0;
                    Log.d(TAG, "将发送文件数据: " + path);
                    for (int i = 0; i < sentCount; i++) {
                        if (context.isInvalid()) {
                            Log.d(TAG, "网络请求已失效, 停止发送数据");
                            break;
                        }
                        System.arraycopy(data, (i * 640), frame, 0, 640);
                        context.writeAudio(frame);
                        try {
                            Thread.sleep(20);
                        } catch (InterruptedException e) {
                            e.printStackTrace();
                        }
                        sentTime = i * nLen / 2.0 / 16000.0;
                        if (sentTime % 1 == 0) Log.d(TAG, "已发送数据: " + sentTime + "s");
                        if (sentTime > 10) break;
                    }
                    Log.d(TAG, "发送结束, 已发送数据: " + sentTime + "s");
                }
                context.stop();
            } else if (taskType == JsonRequest.Type.Translate) {
                NetSessionContext context = TmkSpeechClient.shareInstance().createTranslator(code, dstCode, text, getListener(taskType));
                netSessionContexts.put(context.getSession(), context);
                context.start();
            } else if (taskType == JsonRequest.Type.Synthesize) {
                NetSessionContext context = TmkSpeechClient.shareInstance().createSynthesizer(code, text, getListener(taskType));
                netSessionContexts.put(context.getSession(), context);
                context.start();
            }  else {
                Log.d(TAG, "任务类型错误: " + sType);
            }

        }).start();
    }

    @Override
    public void onClick(View view) {
        int id = view.getId();

        if (id == R.id.btn_test) {
            String type = et_type.getText().toString();
            String code = et_langcode.getText().toString();
            String dstCode = et_langcode_other.getText().toString();
            String text = et_sample_text.getText().toString();
            String engine = et_engine.getText().toString();
            String host = et_host.getText().toString();
            boolean useOpus = sw_opus.isChecked();
            boolean isLive = et_type.getText().toString().equals(JsonRequest.Type.SpeechTranslation.name());
            if (TextUtils.isEmpty(code)) {
                Toast.makeText(this, "请先设置 code 等参数", Toast.LENGTH_SHORT).show();
                return;
            }

            doTask(code, dstCode, text, host, useOpus, engine, type);
        } else if (id == R.id.txt_setting) {
            if (layout_setting.getVisibility() == View.VISIBLE) {
                layout_setting.setVisibility(View.GONE);
                txt_setting.setText(showSettingsDesc);
            } else {
                layout_setting.setVisibility(View.VISIBLE);
                txt_setting.setText(hideSettingsDesc);
            }
        }

    }
}
