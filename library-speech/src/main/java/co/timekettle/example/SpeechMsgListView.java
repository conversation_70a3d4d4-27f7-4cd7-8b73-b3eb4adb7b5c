package co.timekettle.example;

import android.content.Context;
import android.os.Handler;
import android.util.AttributeSet;
import android.view.MotionEvent;
import android.widget.AbsListView;
import android.widget.ListView;

public class SpeechMsgListView extends ListView {

    private final Handler handler = new Handler();
    private boolean isScrolling = false;
    private Runnable autoScrollRunnable; // 定义自动滚动的延迟任务

    public SpeechMsgListView(Context context, AttributeSet attrs) {
        super(context, attrs);

        // 在触摸事件监听器中使用 Handler 进行延迟操作
        this.setOnTouchListener((v, event) -> {
            int action = event.getAction();

            switch (action) {
                case MotionEvent.ACTION_DOWN:
                    // 按下触摸事件时，禁用自动滚动并取消之前的延迟任务
                    this.setTranscriptMode(ListView.TRANSCRIPT_MODE_DISABLED);
                    handler.removeCallbacks(autoScrollRunnable);
                    isScrolling = true;
                    break;
                case MotionEvent.ACTION_UP:
                    // 抬起触摸事件时，延迟 5 秒后启用自动滚动
                    handler.postDelayed(autoScrollRunnable, 5000);
                    break;
            }

            return false;
        });
        // 在 ListView 的滚动监听器中判断是否处于手动滚动状态
        this.setOnScrollListener(new OnScrollListener() {
            @Override
            public void onScrollStateChanged(AbsListView view, int scrollState) {
                // 如果滚动状态为静止且之前处于手动滚动状态，则重新开始延迟任务
                if (scrollState == SCROLL_STATE_IDLE && isScrolling) {
                    handler.postDelayed(autoScrollRunnable, 5000);
                }
            }

            @Override
            public void onScroll(AbsListView view, int firstVisibleItem, int visibleItemCount, int totalItemCount) {
                // 不需要实现该方法，可以为空
            }
        });
        autoScrollRunnable = () -> {
            // 启用自动滚动
            this.setTranscriptMode(ListView.TRANSCRIPT_MODE_ALWAYS_SCROLL);
            isScrolling = false;
        };
    }
}
