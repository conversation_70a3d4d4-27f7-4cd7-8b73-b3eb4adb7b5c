package co.timekettle.example;

import android.Manifest;
import android.content.Intent;
import android.content.pm.PackageManager;
import android.os.Bundle;
import android.util.Log;
import android.view.View;
import android.widget.Button;
import android.widget.SeekBar;
import android.widget.Switch;
import android.widget.Toast;

import androidx.annotation.NonNull;
import androidx.appcompat.app.AppCompatActivity;
import androidx.core.app.ActivityCompat;
import androidx.core.content.ContextCompat;

import com.google.gson.Gson;

import java.io.ByteArrayOutputStream;
import java.io.FileNotFoundException;
import java.io.IOException;
import java.io.InputStream;
import java.lang.reflect.Type;
import java.nio.charset.StandardCharsets;
import java.util.ArrayDeque;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Locale;
import java.util.Map;
import java.util.Objects;
import java.util.concurrent.CountDownLatch;

import co.timekettle.handy_tool.PhoneAutoRecordMode;
import co.timekettle.speech.AiSpeechManager;
import co.timekettle.speech.AudioChannel;
import co.timekettle.speech.AudioChannelOptions;
import co.timekettle.speech.AudioRecordOptions;
import co.timekettle.speech.CustomTranslationManager;
import co.timekettle.speech.EngineHost;
import co.timekettle.speech.ISpeechConstant;
import co.timekettle.speech.OfflineManager;
import co.timekettle.speech.R;
import co.timekettle.speech.RecordManager;
import co.timekettle.speech.SpeechRequest;
import co.timekettle.speech.SpeechResponse;
import co.timekettle.speech.SpeechSessionContext;
import co.timekettle.speech.SynthesizeManager;
import co.timekettle.speech.TestRecorder;
import co.timekettle.speech.TranslateManager;
import co.timekettle.speech.ispeech.algorithm.AudioDoaJni;
import co.timekettle.speech.ispeech.algorithm.ResampleProcessor;
import co.timekettle.speech.jni.SoundStretchJni;
import co.timekettle.speech.jni.TmkCustomTranslationJni;
import co.timekettle.speech.synthesizer.HoyaSynthesizer;
import co.timekettle.speech.utils.AiSpeechLogUtil;
import co.timekettle.speech.utils.BleLossStatistics;
import co.timekettle.speech.utils.BytesTrans;
import co.timekettle.speech.utils.VolumeUtil;
import co.timekettle.tmkengine.JsonSynthesizeRequest;

public class TranslateExampleActivity extends AppCompatActivity implements View.OnClickListener {
    static final String TAG = "TranslateExampleActivity";
    private Button btn_asr;
    private Button btn_asr_file;
    private Button btn_asr_t1;
    private Button btn_asr_file_t1;
    private Button btn_tts;
    private Button btn_switch_voicename;
    private Button btn_play;
    private Button btn_mt;
    private Button btn_mt_offline;
    private Button btn_offline_tryauth;
    private Button btn_asr_mutil_channel;
    private Button btn_asr_pick1;
    private Button btn_asr_pick2;
    private Button btn_vad_file;
    private Button btn_toggle_offline;
    private Button btn_update_host;
    private Button btn_custom_mt;
    private Button btn_test_alltask;
    private Button btn_switch_offline_tts;
    private Button btn_test_punctuation_segmentation;
    private Switch sw_opus;
    private SeekBar sb_volume;

    String srcCode = "zh-CN";
    String dstCode = "en-US";
    String srcText = "正在测试讯飞离线翻译";

    //    String dstCode = "en-AU";
//        String srcCode = "en-US";
//        String dstCode = "zh-CN";
//        String srcText = "Testing iFLYTEK ofline translation";
//    String srcCode = "en-US";
//    String dstCode = "es-ES";
//    String srcText = "testing niu translation";

//    String srcCode = "cs-CZ";
//    String dstCode = "zh-CN";
//    String srcText = "Dobrý den, prosím nepoužívejte čísla.";
//    String srcText = "Zjistěte, zda máte zpětnou vazbu odtud, je připraven odpočítávat sám, stačí toto kolo pozastavit. 123456789 jedenáct 3456789 deset francouzských entai nedává bezpečnost, čínština může být vždy zobrazena, neexistuje žádný jiný výsledek překladu nebo jaký požadavek vypršel.";

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setContentView(R.layout.translate_sample_activity);
        btn_asr = (Button) findViewById(R.id.btn_asr);
        btn_asr.setOnClickListener(this);

        btn_asr_file = (Button) findViewById(R.id.btn_asr_file);
        btn_asr_file.setOnClickListener(this);

        btn_asr_t1 = (Button) findViewById(R.id.btn_asr_t1);
        btn_asr_t1.setOnClickListener(this);

        btn_asr_file_t1 = (Button) findViewById(R.id.btn_asr_file_t1);
        btn_asr_file_t1.setOnClickListener(this);

        btn_tts = (Button) findViewById(R.id.btn_tts);
        btn_tts.setOnClickListener(this);

        btn_switch_voicename = (Button) findViewById(R.id.btn_switch_voicename);
        btn_switch_voicename.setOnClickListener(this);

        btn_mt = (Button) findViewById(R.id.btn_mt);
        btn_mt.setOnClickListener(this);

        btn_mt_offline = (Button) findViewById(R.id.btn_mt_offline);
        btn_mt_offline.setOnClickListener(this);

        btn_offline_tryauth = (Button) findViewById(R.id.btn_offline_tryauth);
        btn_offline_tryauth.setOnClickListener(this);

        btn_play = (Button) findViewById(R.id.btn_play);
        btn_play.setOnClickListener(this);

        btn_asr_mutil_channel = (Button) findViewById(R.id.btn_asr_mutil_channel);
        btn_asr_mutil_channel.setOnClickListener(this);

        btn_asr_pick1 = (Button) findViewById(R.id.btn_asr_pick1);
        btn_asr_pick1.setOnClickListener(this);

        btn_asr_pick2 = (Button) findViewById(R.id.btn_asr_pick2);
        btn_asr_pick2.setOnClickListener(this);

        btn_vad_file = (Button) findViewById(R.id.btn_vad_file);
        btn_vad_file.setOnClickListener(this);

        btn_toggle_offline = (Button) findViewById(R.id.btn_toggle_offline);
        btn_toggle_offline.setOnClickListener(this);

        btn_custom_mt = (Button) findViewById(R.id.btn_custom_mt);
        btn_custom_mt.setOnClickListener(this);

        btn_test_alltask = (Button) findViewById(R.id.btn_test_alltask);
        btn_test_alltask.setOnClickListener(this);

        btn_update_host = (Button) findViewById(R.id.btn_update_host);
        btn_update_host.setOnClickListener(this);

        btn_switch_offline_tts = (Button) findViewById(R.id.btn_switch_offline_tts);
        btn_switch_offline_tts.setOnClickListener(this);

        sb_volume = findViewById(R.id.sb_volume);
        sb_volume.setMax(120);
        sb_volume.setMin(0);
        
        btn_test_punctuation_segmentation = (Button) findViewById(R.id.btn_test_punctuation_segmentation);
        btn_test_punctuation_segmentation.setOnClickListener(this);

        sw_opus = findViewById(R.id.sw_opus);
        sw_opus.setOnCheckedChangeListener((buttonView, isChecked) -> {
            AiSpeechManager.shareInstance().setUseOpus(isChecked);
            Log.d(TAG, "是否开启 opus: " + isChecked);
            AiSpeechManager.shareInstance().enableLID(isChecked);
        });
        sw_opus.setChecked(true);

        SpeechMsgListView msgListView = findViewById(R.id.list_msg);
        msgListView.setAdapter(getAdapter());

        // 进入模式页
//        AiSpeechManager.HttpApiBaseUrl = "http://*************:30013";
        AiSpeechManager.HttpApiBaseUrl = "https://internal-apis-and-pages2.timekettle.co:30013";
        Log.d(TAG, "ip 列表: " + Arrays.toString(AiSpeechManager.shareInstance().getAllRemoteHost()));
        AiSpeechLogUtil.setLogLevel(2);
        AiSpeechLogUtil.enableFileLogger(this, true);
        AiSpeechManager.shareInstance().create(this, "9A5C1E41066458D50F91636A111FED89");
//        AiSpeechManager.shareInstance().fetchHosts();
//        AiSpeechManager.shareInstance().create(this, "9A5C1E41066458D50F91636A111FED89", new EngineHost("***********", 5050));
//        AiSpeechManager.shareInstance().create(this, "9A5C1E41066458D50F91636A111FED89", new EngineHost("*************", 5050));
        AiSpeechManager.shareInstance().enablePunctuationSegmentation(true);
        checkPermissions();

        boolean isCustomOfflineResourcePath = false;
        Map<String, String> customResPaths = null;
        if (isCustomOfflineResourcePath) {
            // T1 自定义离线资源路径
//        String iflyResWorkDirPath = this.getCacheDir().getAbsolutePath() + "/IflyOfflineResource2"; // 讯飞防止 sd 卡空间内会授权失败10008导致不工作
//        String niuResWorkDirPath = this.getCacheDir().getAbsolutePath() + "/niutrans"; // Environment.getExternalStorageDirectory().getAbsolutePath(); 小牛放至外部会导致崩溃
//        String iflyResWorkDirPath = this.getFilesDir().getAbsolutePath() + "/IflyOfflineResource2"; // 讯飞防止 sd 卡空间内会授权失败10008导致不工作
//        String niuResWorkDirPath = this.getFilesDir().getAbsolutePath() + "/niutrans"; // Environment.getExternalStorageDirectory().getAbsolutePath(); 小牛放至外部会导致崩溃

            // 外部存储空间
//        String iflyResWorkDirPath = Environment.getExternalStorageDirectory().getAbsolutePath() + "/IflyOfflineResource2";
//        String niuResWorkDirPath = Environment.getExternalStorageDirectory().getAbsolutePath() + "/niutrans";
//        String iflyResWorkDirPath = Environment.getExternalStorageDirectory().getAbsolutePath() + "/offlineRes/IflyOfflineResource2";
//        String niuResWorkDirPath = Environment.getExternalStorageDirectory().getAbsolutePath() + "/offlineRes/niutrans";

            // 系统目录路径
//        String iflyResWorkDirPath = "/system/offlineRes" + "/IflyOfflineResource2";
//        String niuResWorkDirPath = "/system/offlineRes" + "/niutrans";

            // 外部沙箱位置
            String iflyResWorkDirPath = this.getExternalCacheDir().getAbsolutePath() + "/IflyOfflineResource2";
            String niuResWorkDirPath = this.getExternalCacheDir().getAbsolutePath() + "/niutrans";

            customResPaths = new HashMap<String, String>() {{
                // 讯飞离线识别
                put("zh", iflyResWorkDirPath);
                put("en", iflyResWorkDirPath);
                put("es", iflyResWorkDirPath);
                put("ru", iflyResWorkDirPath);
                put("de", iflyResWorkDirPath);
                // 讯飞离线翻译
//                put("zhen", iflyResWorkDirPath);
//                put("enzh", iflyResWorkDirPath);
                // 小牛离线翻译
                put("enes", niuResWorkDirPath);
                put("esen", niuResWorkDirPath);
                put("enru", niuResWorkDirPath);
                put("ruen", niuResWorkDirPath);
                put("ende", niuResWorkDirPath);
                put("deen", niuResWorkDirPath);

                // 新小牛离线翻译支持语种
                put("zhen", niuResWorkDirPath);
                put("enzh", niuResWorkDirPath);
                put("zhja", niuResWorkDirPath);
                put("jazh", niuResWorkDirPath);
                put("zhfr", niuResWorkDirPath);
                put("frzh", niuResWorkDirPath);
                put("zhes", niuResWorkDirPath);
                put("eszh", niuResWorkDirPath);
                put("zhru", niuResWorkDirPath);
                put("ruzh", niuResWorkDirPath);
                put("zhde", niuResWorkDirPath);
                put("dezh", niuResWorkDirPath);
            }};
        }
        try {
            InputStream in = this.getAssets().open("OfflieConfig2.json");
            byte[] buffer = new byte[in.available()];
            while (-1 != in.read(buffer)) {
                Map<String, Object> config = new Gson().fromJson(new String(buffer, StandardCharsets.UTF_8), (Type) HashMap.class);
                OfflineManager.getInstance().loadOffline(this, config, null);
            }
        } catch (IOException e) {
            e.printStackTrace();
        }

        isDestroy = false;
    }

    @Override
    protected void onDestroy() {
        super.onDestroy();
        isDestroy = true;
        // 退出模式页
        AiSpeechManager.shareInstance().destroy();
    }

    private TmkEngineResponseMsgAdapter msgAdapter;

    private TmkEngineResponseMsgAdapter getAdapter() {
        if (this.msgAdapter == null) {
            TmkEngineResponseMsgAdapter msgAdapter = new TmkEngineResponseMsgAdapter(this);
            msgAdapter.setOnMsgClickListener(new TmkEngineResponseMsgAdapter.OnMsgClickListener() {
                @Override
                public void onMark(TmkEngineResponseMsg msg) {

                }

                @Override
                public void onDetail(TmkEngineResponseMsg msg) {

                }
            });
            this.msgAdapter = msgAdapter;
        }
        return this.msgAdapter;
    }

    @Override
    public void onClick(View v) {
        int id = v.getId();
        if (id == R.id.btn_asr) {
            new Thread(this::startAsr).start();
        } else if (id == R.id.btn_asr_file) {
            new Thread(this::startAsrFile).start();
        } else if (id == R.id.btn_asr_t1) {
            new Thread(this::startAsrT1).start();
        } else if (id == R.id.btn_asr_file_t1) {
            new Thread(this::startAsrWithT1StereoSound).start();
        } else if (id == R.id.btn_vad_file) {
            new Thread(this::startVadFile).start();
        } else if (id == R.id.btn_switch_voicename) {
            this.switchVoiceName();
        } else if (id == R.id.btn_tts) {
//            this.startTts();
            this.startTtsAndPlay();
        } else if (id == R.id.btn_mt) {
            this.startMt();
        } else if (id == R.id.btn_mt_offline) {
            this.startOfflineTest();
        } else if (id == R.id.btn_offline_tryauth) {
            this.tryOfflineAuth();
        } else if (id == R.id.btn_play) {
            this.startPlay();
        } else if (id == R.id.btn_asr_mutil_channel) {
            this.startAsrMutilChannel();
        } else if (id == R.id.btn_asr_pick1) {
            this.startAsrPick1();
        } else if (id == R.id.btn_asr_pick2) {
            this.startAsrPick2();
        } else if (id == R.id.btn_toggle_offline) {
            this.toggleOffline(srcCode, dstCode);
        } else if (id == R.id.btn_update_host) {
            this.updateHost();
        } else if (id == R.id.btn_custom_mt) {
            this.testCustomMt();
        } else if (id == R.id.btn_test_alltask) {
            try {
                testAllTask();
            } catch (IOException e) {
                throw new RuntimeException(e);
            }
        } else if (id == R.id.btn_switch_offline_tts) {
            this.switchOfflineTts();
        } else if (id == R.id.btn_test_punctuation_segmentation) {
            this.testPunctuationSegmentation();
        }
    }

    ArrayList<Float> volums = new ArrayList<>();
    private AiSpeechManager.Listener aiSpeechListener = new AiSpeechManager.Listener() {
        @Override
        public void onVadBegin(@NonNull AudioChannel channel, long session) {
            Log.d(TAG, "onVadBegin: " + channel.getKey() + "[" + session + "] ");
//            AiSpeechManager.shareInstance().disableAllAudioPipe();;
//            AiSpeechManager.shareInstance().enableAudioPipe(pipe.getName());
            volums = new ArrayList<>();
        }

        @Override
        public void onVadEnd(@NonNull AudioChannel channel, long session) {
            boolean ret = VolumeUtil.judgeVolume(volums);
            Log.d(TAG, "onVadEnd: " + channel.getKey() + "[" + session + "] isNormal:" + ret);
        }

        @Override
        public void onActivity(@NonNull AudioChannel channel, long session, float volume) {
//            Log.d(TAG, "onActivity: " + channel.getKey() + "[" + session + "] volume:" + volume);
            volums.add(volume);
            if (volume > 0) {
                runOnUiThread(() -> {
                    sb_volume.setProgress((int) volume);
                });
            }
        }

        @Override
        public void onRecognizeResult(String chkey, long session, String srcCode, String dstCode, boolean isLast, String text, String engine, TmkCustomTranslationJni.TmkCustomTranslationResult ctr) {
            Log.d(TAG, (ctr != null ? "自定义" : "") + "识别: " + chkey + "[" + engine + "] " + session + " " + isLast + " " + text + (ctr != null ? " ctr:" + ctr: ""));
            TmkEngineResponseMsg msg = msgAdapter.findMsg(session);
            if (msg == null) {
                msg = new TmkEngineResponseMsg();
                msg.isSelf = Objects.equals(srcCode, TranslateExampleActivity.this.srcCode);
                msgAdapter.addMsg(msg);
            }
            msg.chkey = chkey;
            msg.session = session;
            msg.srcCode = srcCode;
            msg.dstCode = dstCode;
            msg.asr.isLast = isLast;
            msg.asr.text = text;
            msg.asr.engine = engine;
            getAdapter().notifyDataSetChanged();
        }

        @Override
        public void onTranslateResult(String chkey, long session, boolean isLast, String text, String engine, TmkCustomTranslationJni.TmkCustomTranslationResult ctr) {
            Log.d(TAG, (ctr != null ? "自定义" : "") + "翻译: " + chkey + "[" + engine + "] " + session + " " + isLast + " " + text + (ctr != null ? " ctr:" + ctr : ""));
            //            if (AiSpeechManager.shareInstance().isSynthesizeDisabled() && isLast) AiSpeechManager.shareInstance().enableAllAudioPipe();
            TmkEngineResponseMsg msg = getAdapter().findMsg(session);
            if (msg == null) {
                msg = new TmkEngineResponseMsg();
                getAdapter().addMsg(msg);
            }
            msg.chkey = chkey;
            msg.session = session;
            msg.mt.isLast = isLast;
            msg.mt.text = text;
            msg.mt.engine = engine;
            getAdapter().notifyDataSetChanged();
        }

        @Override
        public void onSynthesizeCompleted(String chkey, long session, byte[] data, String engine) {
            Log.d(TAG, "onSynthesizeCompleted: " + chkey + "[" + engine + "] " + session);
            TmkEngineResponseMsg msg = getAdapter().findMsg(session);
            if (msg == null) {
                msg = new TmkEngineResponseMsg();
                getAdapter().addMsg(msg);
            }
            msg.tts.data = data;
            msg.tts.engine = engine;
        }

        @Override
        public void onError(String chkey, long session, int code, String message) {
            Log.e(TAG, "onError: " + chkey + " " + session + " code:" + code + " message:" + message);
        }

        @Override
        public void onSpeakStart(String chkey, long session, String text, String speakerType, Object extData) {
            Log.d(TAG, "onSpeakStart: " + chkey + "[" + speakerType + "] " + session + " extData:" + extData);
            TmkEngineResponseMsg msg = getAdapter().findMsg(session);
            if (msg == null) {
                msg = new TmkEngineResponseMsg();
                getAdapter().addMsg(msg);
            }
            msg.chkey = chkey;
            msg.session = session;
            msg.tts.isPlaying = true;
            getAdapter().notifyDataSetChanged();
        }

        @Override
        public void onSpeakEnd(String chkey, long session, String text, String speakerType, Object extData) {
            Log.d(TAG, "onSpeakEnd: " + chkey + "[" + speakerType + "] " + session + " " + extData);
            //            AiSpeechManager.shareInstance().enableAllAudioPipe();
            TmkEngineResponseMsg msg = getAdapter().findMsg(session);
            if (msg == null) {
                msg = new TmkEngineResponseMsg();
                getAdapter().addMsg(msg);
            }
            msg.chkey = chkey;
            msg.session = session;
            msg.tts.isPlaying = false;
            getAdapter().notifyDataSetChanged();
        }

        @Override
        public void onFinished(String chkey, long session, int type, String engine) {

        }

        @Override
        public void onSpeechTranslationResult(AudioChannel channel, SpeechSessionContext context, SpeechResponse.ResponseMessage msg) {

        }
    };

    void enterMode() {
        // 开启日志, 并放置外部储存空间
        AiSpeechLogUtil.setLogLevel(2);
        AiSpeechLogUtil.enableFileLogger(this, true);

        // 初始化
        AiSpeechManager.shareInstance().create(this, "9A5C1E41066458D50F91636A111FED89", new EngineHost(""));
        AiSpeechManager.shareInstance().setListener(aiSpeechListener);

        // 更新 ip 列表
        boolean isTestEnv = false;
        String BaseURL = isTestEnv ? "http://*************:30013" : "https://internal-apis-and-pages2.timekettle.co:30013";
        {
            String params = ""; // "?param1=0";
            String Api = "/markov/user/mediate/routes";
            String url = BaseURL + Api + params;
            AiSpeechManager.shareInstance().fetchHosts(url);
        }

        // 更新 配置
        {
            String params = "?deviceType=app"; // deviceType 参数值为 app(时空壶APP), t1, t1mini
            String Api = "/markov/user/mediate/switch";
            String url = BaseURL + Api + params;
//            AiSpeechManager.shareInstance().fetchConfig(url);
        }
    }

    void exitMode() {
        AiSpeechManager.shareInstance().destroy();
    }

    List<SpeechResponse.ResponseMessage> testResults = new ArrayList<>();

    // 任务类型: 两段式语音翻译, 语音翻译, 识别播放合成
    // 独立任务测试: 识别, 翻译, 合成, 播放
    // 离线识别翻译, 自定义识别翻译
    // 断句时长: 0.4, 1.0, 3.0
    // 灵敏度: 0.1, 1, 10
    // 异常测试: 噪声数据, 网络异常, 超时异常等
    private void testAllTask() throws IOException {
        new Thread(() -> {
            try {
                String srcCode = "zh-CN";
                String dstCode = "en-US";
                InputStream in = this.getAssets().open("audio_samples/16ktts(zh-CN).pcm");

                this.testTwoStepSpeechTranslationTask(srcCode, dstCode, in);
                in.reset();

                this.testSpeechTranslationTask(srcCode, dstCode, in);
                in.reset();

//                this.testRecognizeTranslateTask(srcCode, dstCode, in);
//                in.reset();

            } catch (IOException e) {
                throw new RuntimeException(e);
            }
        }).start();
    }

    private void testTwoStepSpeechTranslationTask(String srcCode, String dstCode, InputStream in) {
        this.enterMode();

        AiSpeechManager.shareInstance().enableSpeechTranslation(false);

        String chkey = "test";
        AudioChannel channel = new AudioChannel(this, chkey, true);
        channel.setSrcCode(srcCode);
        channel.setDstCode(dstCode);
        channel.setMarkVadDataTag(true);
        channel.setSpeaker(ISpeechConstant.SPEAKER.PHONE.toString());
        AiSpeechManager.shareInstance().addChannel(channel);

        try {
            int size = ISpeechConstant.DefaultBytesPerPacketInMono;
            byte[] buffer = new byte[size];
            float offset_time = 0;
            while (-1 != in.read(buffer)) {
                AiSpeechManager.shareInstance().writeAudioToChannel(chkey, buffer);
                Thread.sleep(16);
                offset_time = (float) (offset_time + buffer.length / 2.0 / 16000.0);
            }
            Thread.sleep(3000);
        } catch (IOException | InterruptedException e) {
            e.printStackTrace();
        }

        this.exitMode();
    }

    private void testSpeechTranslationTask(String srcCode, String dstCode, InputStream in) {
        this.enterMode();

        AiSpeechManager.shareInstance().enableSpeechTranslation(true);

        String chkey = "test";
        AudioChannel channel = new AudioChannel(this, chkey, true);
        channel.setSrcCode(srcCode);
        channel.setDstCode(dstCode);
        channel.setMarkVadDataTag(true);
        channel.setSpeaker(ISpeechConstant.SPEAKER.PHONE.toString());
        AiSpeechManager.shareInstance().addChannel(channel);

        try {
            int size = ISpeechConstant.DefaultBytesPerPacketInMono;
            byte[] buffer = new byte[size];
            float offset_time = 0;
            while (-1 != in.read(buffer)) {
                AiSpeechManager.shareInstance().writeAudioToChannel(chkey, buffer);
                Thread.sleep(16);
                offset_time = (float) (offset_time + buffer.length / 2.0 / 16000.0);
            }
            Thread.sleep(3000);
        } catch (IOException | InterruptedException e) {
            e.printStackTrace();
        }

        this.exitMode();
    }

    private void testRecognizeTranslateTask(String srcCode, String dstCode, InputStream in) {
        this.enterMode();

        AiSpeechManager.shareInstance().enableSpeechTranslation(false);

        String chkey = "test";
        AudioChannel channel = new AudioChannel(this, chkey, true);
        channel.setSrcCode(srcCode);
        channel.setDstCode(dstCode);
        channel.setMarkVadDataTag(true);
        channel.setSpeaker(ISpeechConstant.SPEAKER.PHONE.toString());
        AiSpeechManager.shareInstance().addChannel(channel);

        try {
            int size = ISpeechConstant.DefaultBytesPerPacketInMono;
            byte[] buffer = new byte[size];
            float offset_time = 0;
            while (-1 != in.read(buffer)) {
                AiSpeechManager.shareInstance().writeAudioToChannel(chkey, buffer);
                Thread.sleep(16);
                offset_time = (float) (offset_time + buffer.length / 2.0 / 16000.0);
            }
            Thread.sleep(3000);
        } catch (IOException | InterruptedException e) {
            e.printStackTrace();
        }

        this.exitMode();
    }

    PhoneAutoRecordMode mode = new PhoneAutoRecordMode();

    private void startAsr() {
        BleLossStatistics statistics = new BleLossStatistics(this, false);
        RecordManager.shareInstance().setSqListener((e) -> {
            Log.d(TAG, "打印当前 ble 服务事件: " + e.toString());
            String desc = "";
            String baseDesc = String.format(Locale.getDefault(), " %s(%s) rssi:%s electric:%s recv:%d total:%d v:%.1f", e.name, e.firmwareVersion, e.rssi, e.electric, e.recv, e.total, e.velocity);
            boolean isBleRecording = !e.isDisconnect && !e.isFinal; // ble 录音中
            boolean isBleRecordEnd = !e.isDisconnect && e.isFinal; // ble 录音结束
            boolean isBleDisconnect = e.isDisconnect;
            if (isBleRecording) {
//                    if (e.loss > 0 && e.loss < 10) perip.extloss = perip.extloss + parseInt(e.loss); // 001 单工模式下, 播放时不录音导致
                desc = String.format(Locale.getDefault(), " %s loss:%d%s tloss:%d", baseDesc, e.loss, e.loss > 0 && e.loss < 10 ? "(play)" : "", e.totalLoss);
                statistics.writeLossLog(desc);
            } else {
                if (isBleRecordEnd) {
                    boolean didDisconnect = false;
                    if (didDisconnect) {
                        desc = String.format("--------------------------------- %s(%s)  早已断开------------------------------------------", e.name, e.firmwareVersion);
                        statistics.writeLossLog(desc);
                    }
                } else if (isBleDisconnect) {
                    // ble 录音断开
                }
                long interval = (e.destroyTime - e.createTime) / 1000;
                String intervalDesc = String.format(Locale.getDefault(), "%dm%ds", interval / 60, Math.round(interval % 60));
                int extloss = 0; // 001 单工模式下, 播放导致
                float lossRate = (float) (((e.totalLoss - extloss) * 1.0) / e.total * 100);
                desc = String.format(Locale.getDefault(), " %s tloss:%d(%d) interval:%s loss%%:%.1f", baseDesc, e.totalLoss, extloss, intervalDesc, lossRate);
                statistics.writeAllLossLog(desc);
            }
        });

        mode.start(srcCode, dstCode, this.aiSpeechListener);
    }

    String chkey1 = "mic1";
    String chkey2 = "mic2";

    private void startAsrMutilChannel() {
        AiSpeechManager.shareInstance().setListener(aiSpeechListener);

        AiSpeechManager.shareInstance().setRecognizeDisabled(true);
        AiSpeechManager.shareInstance().setTranslateDisabled(true);
        AiSpeechManager.shareInstance().setSynthesizeDisabled(true);

        String speaker = ISpeechConstant.SPEAKER.PHONE.toString();
        HashMap<String, Object> options = new HashMap<String, Object>() {{
            put(AudioChannelOptions.KEY, chkey1);
            put(AudioChannelOptions.SRC_CODE, srcCode);
            put(AudioChannelOptions.DST_CODE, dstCode);
            put(AudioChannelOptions.RECORDER, speaker);
            put(AudioChannelOptions.SPEAKER_TYPE, speaker);

            put(AudioChannelOptions.MIN_VAD_ENERGY, AudioChannel.defalutMinVadEnergy); // 一般小于

            put(AudioChannelOptions.WRITE_TO_FILE, true); // 是否写成录音文件
            put(AudioChannelOptions.SHOULD_GEN_VAD_TAG, true); // 是否打上 vad 标签用于调试
        }};

        HashMap<String, Object> recorder_options = new HashMap<String, Object>() {{
            put(AudioRecordOptions.CAN_RECORD_WHEN_SPEAKING, false);
            put(AudioRecordOptions.WRITE_TO_FILE, true); // 录音器原始录音是否写成录音文件
        }};
        String recorder = ISpeechConstant.RECORDER.PHONE.toString();
        options.put(AudioChannelOptions.KEY, chkey1);
        options.put(AudioChannelOptions.SRC_CODE, srcCode);
        options.put(AudioChannelOptions.DST_CODE, dstCode);
        AiSpeechManager.shareInstance().addChannel(options);

        options.put(AudioChannelOptions.KEY, chkey2);
        options.put(AudioChannelOptions.SRC_CODE, dstCode);
        options.put(AudioChannelOptions.DST_CODE, srcCode);
        AiSpeechManager.shareInstance().addChannel(options);

        AiSpeechManager.shareInstance().startRecorder(recorder, recorder_options);
    }

    private void startAsrPick1() {
        AiSpeechManager.shareInstance().disableAudioChannel(chkey2);
        AiSpeechManager.shareInstance().enableAudioChannel(chkey1);
    }

    private void startAsrPick2() {
        AiSpeechManager.shareInstance().disableAudioChannel(chkey1);
        AiSpeechManager.shareInstance().enableAudioChannel(chkey2);
    }

    private void startAsrT1() {
        AiSpeechManager.shareInstance().setListener(aiSpeechListener);

//        AiSpeechManager.shareInstance().setRecognizeDisabled(true);
//        AiSpeechManager.shareInstance().setTranslateDisabled(true);
//        AiSpeechManager.shareInstance().setSynthesizeDisabled(true);

        String chkey1 = AudioChannel.Role.Self.toString();
        String chkey2 = AudioChannel.Role.Other.toString();
        String speaker = ISpeechConstant.SPEAKER.T1PHONE.toString();
        HashMap<String, Object> options = new HashMap<String, Object>() {{
            put(AudioChannelOptions.KEY, chkey1);
            put(AudioChannelOptions.SRC_CODE, srcCode);
            put(AudioChannelOptions.DST_CODE, dstCode);
            put(AudioChannelOptions.ROLE, AudioChannel.Role.Self.toString());
            put(AudioChannelOptions.SPEAKER_TYPE, speaker);

            put(AudioChannelOptions.MIN_VAD_ENERGY, AudioChannel.defalutMinVadEnergy); // 一般小于
            put(AudioChannelOptions.CAN_HEAR_ECHO, false);

            put(AudioChannelOptions.WRITE_TO_FILE, true); // 是否写成录音文件
            put(AudioChannelOptions.SHOULD_GEN_VAD_TAG, true); // 是否打上 vad 标签用于调试
        }};

        HashMap<String, Object> recorder_options = new HashMap<String, Object>() {{
            put(AudioRecordOptions.CAN_RECORD_WHEN_SPEAKING, true); // << ------------------------ 注意此参数
            put(AudioRecordOptions.WRITE_TO_FILE, true); // 录音器原始录音是否写成录音文件
        }};
        String recorder = ISpeechConstant.RECORDER.T1PHONE.toString();
        options.put(AudioChannelOptions.KEY, chkey1);
        options.put(AudioChannelOptions.SRC_CODE, srcCode);
        options.put(AudioChannelOptions.DST_CODE, dstCode);
        options.put(AudioChannelOptions.ROLE, AudioChannel.Role.Self.toString());
        AiSpeechManager.shareInstance().addChannel(options);
        options.put(AudioChannelOptions.KEY, chkey2);
        options.put(AudioChannelOptions.SRC_CODE, dstCode);
        options.put(AudioChannelOptions.DST_CODE, srcCode);
        options.put(AudioChannelOptions.ROLE, AudioChannel.Role.Other.toString());
        AiSpeechManager.shareInstance().addChannel(options);

        AiSpeechManager.shareInstance().startRecorder(recorder, recorder_options);
    }

    private void startAsrFile() {
        AiSpeechManager.shareInstance().setListener(aiSpeechListener);

        AiSpeechManager.shareInstance().setRecognizeDisabled(true);
        AiSpeechManager.shareInstance().setTranslateDisabled(true);
        AiSpeechManager.shareInstance().setSynthesizeDisabled(true);

        String chkey = "test";
        AudioChannel channel = new AudioChannel(this, chkey, true);
        channel.setSrcCode(srcCode);
//        channel.setSrcCode(dstCode);
        channel.setDstCode(dstCode);
        channel.setMarkVadDataTag(true);
        channel.setSpeaker(ISpeechConstant.SPEAKER.PHONE.toString());
        AiSpeechManager.shareInstance().addChannel(channel);

        OfflineManager.getInstance().openOffline(srcCode, dstCode);
        try {
            int size = ISpeechConstant.DefaultBytesPerPacketInMono;
            byte[] buffer = new byte[size];
            // java.io.FileNotFoundException 注释选项 ignoreAssets "audio_samples"
//            InputStream in = this.getAssets().open("audio_samples/16ktts(en-US).pcm");
            InputStream in = this.getAssets().open("audio_samples/75db.pcm");
            float offset_time = 0;
            while (-1 != in.read(buffer)) {
                AiSpeechManager.shareInstance().writeAudioToChannel(chkey, buffer);
                Thread.sleep(16);
                offset_time = (float) (offset_time + buffer.length / 2.0 / 16000.0);
//                if (offset_time > 5) break;
            }
//            byte[] mute2s = new byte[16000 * 2 * 2];
//            AiSpeechManager.shareInstance().writeAudioToChannel(chkey, mute2s);
        } catch (IOException | InterruptedException e) {
            e.printStackTrace();
        }
    }

    private void toggleOffline(String srcCode, String dstCode) {
        String from = srcCode.split("-")[0].toLowerCase();
        String to = dstCode.split("-")[0].toLowerCase();
        String coupleCode = from + "<->" + to;
        if (btn_toggle_offline.getText().toString().startsWith("开启离线")) {

//            OfflineManager.CheckResult cr = OfflineManager.getInstance().isReadyOffline(from+to); // 只检查资源是否存在
//            OfflineManager.CheckResult cr = OfflineManager.getInstance().isReadyOffline(coupleCode); // 只检查资源是否存在
//            OfflineManager.CheckResult cr = OfflineManager.getInstance().isReadyOffline(srcCode, dstCode); // 只检查资源是否存在
            OfflineManager.CheckResult cr = OfflineManager.getInstance().openOffline(srcCode, dstCode); // 开启离线
            AiSpeechManager.shareInstance().setOnlyOffline(true);

            if (!cr.vaild) {
                // 离线资源未下载
                Log.d(TAG, "开启离线失败: " + coupleCode + " 需要下载资源数: " + cr.urls.size());
            } else {
                // 离线资源已下载
                btn_toggle_offline.setText("关闭离线" + coupleCode);
                Log.d(TAG, "开启了离线: " + coupleCode);
            }
        } else {
            AiSpeechManager.shareInstance().setOnlyOffline(true);
            btn_toggle_offline.setText("开启离线" + coupleCode);
            OfflineManager.getInstance().closeOffline(srcCode, dstCode); // 关闭离线

            Log.d(TAG, "关闭离线: " + coupleCode);
        }
    }

    private void startVadFile() {
        AiSpeechManager.shareInstance().setListener(aiSpeechListener);

        AiSpeechManager.shareInstance().setRecognizeDisabled(true);
        AiSpeechManager.shareInstance().setTranslateDisabled(true);
        AiSpeechManager.shareInstance().setSynthesizeDisabled(true);

        String chkey = "test";
        AudioChannel channel = new AudioChannel(this, chkey, (int) ISpeechConstant.DefaultSampleRate, true);
        channel.setSrcCode(srcCode);
//        channel.setSrcCode(dstCode);
        channel.setDstCode(dstCode);
        channel.setMarkVadDataTag(true);
        channel.setWriteToFile(true);
        AiSpeechManager.shareInstance().addChannel(channel);

        try {
            int size = ISpeechConstant.DefaultBytesPerPacketInMono;
            byte[] buffer = new byte[size];
            InputStream in = this.getAssets().open("audio_samples/20220809_102402-m3-low-energy.pcm");
            while (-1 != in.read(buffer)) {
                AiSpeechManager.shareInstance().writeAudioToChannel(chkey, buffer);
                Thread.sleep(10);
            }
        } catch (IOException | InterruptedException e) {
            e.printStackTrace();
        }
    }

    protected ResampleProcessor resampler = null;

    private void startResampleFile() {
        if (resampler == null) resampler = new ResampleProcessor();

        int srcSample = 16000;
        int dstSample = 8000;
        TestRecorder handle = new TestRecorder(this, "Resample", "resample-16k-8k", true);
        handle.open();
        try {
//            int size = ISpeechConstant.DefaultBytesPerPacketInMono;
            int size = 640;
            short[] inBuffer = new short[size / 2];
            byte[] inByteBuffer = new byte[size];
            short[] outBuffer = new short[size / 2 / 2];

            // 36s 后
            int destOffset = 36 * 1000 / 16; // ms
            int offset = 0;
            InputStream in = this.getAssets().open("audio_samples/en16ktts.pcm");
            while (-1 != in.read(inByteBuffer)) {
                offset++;
//                if (offset < destOffset) continue; // 测试部分音频

                inBuffer = BytesTrans.getInstance().Bytes2Shorts(inByteBuffer);
                resampler.process(outBuffer, inBuffer, srcSample, dstSample, size / 2);

                handle.write(BytesTrans.getInstance().Shorts2Bytes(outBuffer));
                Thread.sleep(10);
            }
        } catch (IOException | InterruptedException e) {
            e.printStackTrace();
        }
        handle.close();
    }

    int nSampleDoaInPerChannel = 512; // 每通道 512 个采样点, 32ms
    int nSampleDoaInPerPacket = nSampleDoaInPerChannel * 2; // 双通道采样点个数
    int nSizeInBuffer = nSampleDoaInPerPacket;
    int nSizeInChannel = nSampleDoaInPerChannel / 2;

    private void startAsrWithT1StereoSound() {
        AiSpeechManager.shareInstance().setListener(aiSpeechListener);

//        AiSpeechManager.shareInstance().setTranslateDisabled(true);
//        AiSpeechManager.shareInstance().setSynthesizeDisabled(true);

        String chkey1 = "mic1";
        {
            AudioChannel channel = new AudioChannel(this, chkey1, (int) ISpeechConstant.DefaultSampleRate, true);
            channel.setSrcCode(srcCode);
            channel.setDstCode(dstCode);
            channel.setSpeakerType(ISpeechConstant.SPEAKER.T1PHONE.toString());
            AiSpeechManager.shareInstance().addChannel(channel);
        }
        String chkey2 = "mic2";
        {
            AudioChannel channel = new AudioChannel(this, chkey2, (int) ISpeechConstant.DefaultSampleRate, true);
            channel.setSrcCode(dstCode);
            channel.setDstCode(srcCode);
            channel.setSpeakerType(ISpeechConstant.SPEAKER.T1PHONE.toString());
            AiSpeechManager.shareInstance().addChannel(channel);
        }

        TestRecorder fileHandle = new TestRecorder(this, "TK_Record", null, "t1" + "-raw-record", true); // 此处设置录音文件开关

        ArrayDeque<short[]> caches = new ArrayDeque<>();

        HoyaSynthesizer.load(this); // 使用 hoya
        AudioDoaJni.Channel dir = AudioDoaJni.Channel.None;
        AudioDoaJni doa = new AudioDoaJni();
        try {

            byte[] buffer = new byte[nSampleDoaInPerPacket * 2]; // 32ms, 512*2 个 short, 双通道数据, 送入算法
//            short[] stereoPacket = new short[nSampleDoaInPerPacket]; // 32ms, 512*2 个 short, 双通道数据, 送入算法
            short[] bufferInChannel = new short[nSizeInChannel]; // 16ms, 256 个 short, 送入通道的数据
            short[] bufferInChanne2 = new short[nSizeInChannel]; // 16ms, 256 个 short, 送入通道的数据

//            InputStream in = this.getAssets().open("audio_samples/T1-2022-07-21-11-56.pcm");
            InputStream in = this.getAssets().open("audio_samples/20220729_174503-t1-raw-record.pcm");
            while (-1 != in.read(buffer)) {
                short[] doaPacket = BytesTrans.getInstance().Bytes2Shorts(buffer);
                AudioDoaJni.Channel mou = doa.processDoa(doaPacket);

                // 读取到当前包, 发现方向转变
                if (dir == AudioDoaJni.Channel.None) {
                    dir = mou;
                } else {
                    // 方向转变, 需要写完缓存的 6 帧
                    if (dir != mou) {
                        dir = mou; // 更新新方向
                        while (caches.size() > 0) {
                            short[] oldDoaPacket = caches.pop();
                            send(chkey1, chkey2, fileHandle, oldDoaPacket, mou, bufferInChannel, bufferInChanne2);
                        }

                    }
                }

                caches.offer(doaPacket);
                if (caches.size() <= 6) continue;
                doaPacket = caches.pop();

                send(chkey1, chkey2, fileHandle, doaPacket, mou, bufferInChannel, bufferInChanne2);
            }
        } catch (IOException e) {
            e.printStackTrace();
        }
    }

    void testCustomMt() {
        {
            String code = "zh";
            String path = this.getExternalCacheDir().getAbsolutePath() + "/CustomTranslationModels/" + code + ".fst";
            AiSpeechManager.shareInstance().enableCustomTranslation(true);
            try {
                AiSpeechManager.shareInstance().updateCustomTranslation(code, path);
                AiSpeechManager.shareInstance().updateCustomTranslation(code, new HashMap<String, String>() {{
                    put(" .  时空壶  .  ", "Timekettle");
                    put("   时空虎  .  ", "TMK");
//                    put("你好", "tmk hao");
//                    put("测试", "tmktest lelectric motorhellohhj and electric and water heater are in our house so we are still waiting to");
//                    put("二.", "tmk hao");
//                    put("二二.", "tmk hao");
                    put(" . 月亮", "MoonCT");
                }});
            } catch (Exception e) {
                throw new RuntimeException(e);
            }
            // 以下是测试代码, 实际使用时只需开启(enableCustomTranslation)和更新(updateCustomTranslation)即可, 内部会做处理
            String text = "时空胡。你好 今天     月亮   哈哈哈哈哈   - 时空虎.";
//            String text = "   你 好,   时空虎,   我曾经在时空胡公司当过测试";
            TmkCustomTranslationJni.TmkCustomTranslationResult ct = CustomTranslationManager.getInstance().process(text, "zh", "en");
            AiSpeechLogUtil.d(TAG, code + "自定义翻译: " + ct);
        }

        {
            String code = "en";
            String path = this.getExternalCacheDir().getAbsolutePath() + "/CustomTranslationModels/" + code + ".fst";
            AiSpeechManager.shareInstance().enableCustomTranslation(true);
            try {
                AiSpeechManager.shareInstance().updateCustomTranslation(code, path);
                AiSpeechManager.shareInstance().updateCustomTranslation(code, new HashMap<String, String>() {{
//                    put("  Hello. ", "Tmk Hello");
                    put(" . Hello.one  .  ", "Tmk Hello");
                }});
            } catch (Exception e) {
                throw new RuntimeException(e);
            }
            // 以下是测试代码, 实际使用时只需开启(enableCustomTranslation)和更新(updateCustomTranslation)即可, 内部会做处理
            TmkCustomTranslationJni.TmkCustomTranslationResult ct = CustomTranslationManager.getInstance().process("  hello, hello one   can you hear me", code, "zh");
            AiSpeechLogUtil.d(TAG, code + "自定义翻译: " + ct);
        }

        {
            String code = "es";
            String path = this.getExternalCacheDir().getAbsolutePath() + "/CustomTranslationModels/" + code + ".fst";
            AiSpeechManager.shareInstance().enableCustomTranslation(true);
            try {
                AiSpeechManager.shareInstance().updateCustomTranslation(code, path);
                AiSpeechManager.shareInstance().updateCustomTranslation(code, new HashMap<String, String>() {{
                    put("hola", "Tmkhola");
                }});
            } catch (Exception e) {
                throw new RuntimeException(e);
            }
            // 以下是测试代码, 实际使用时只需开启(enableCustomTranslation)和更新(updateCustomTranslation)即可, 内部会做处理
            TmkCustomTranslationJni.TmkCustomTranslationResult ct = CustomTranslationManager.getInstance().process("Hola, por favor no use números.", code, "zh");
            AiSpeechLogUtil.d(TAG, code + "自定义翻译: " + ct);
        }
    }

    private List<EngineHost> hosts; // Tmk引擎主机列表
    private int selectedHostIndex = 0; // 当前选择的主机下标

    void updateHost() {
        String params = ""; // "?param1=0";
        String BaseURL = "http://*************:30013";
        String Api = "/terptrans/user/mediate/routes";
        String url = BaseURL + Api + params;
        AiSpeechManager.shareInstance().fetchHosts(url, (_hosts, error) -> {
            if (error == null) hosts = _hosts;
            return null;
        });
        if (this.hosts == null) {
            Log.e(TAG, "updateHost: 当前还未获取到 hosts");
            return;
        }
        if (!(selectedHostIndex > 0 && this.hosts.size() > selectedHostIndex)) {
            selectedHostIndex = 0;
        }
        mode.updateHost(this.hosts.get(selectedHostIndex).ip, this.hosts.get(selectedHostIndex).port);
        selectedHostIndex++;
    }

    void switchOfflineTts() {
        String name = SynthesizeManager.shareInstance().getFirstSynthesizerName();
        boolean enableTts = Objects.equals(ISpeechConstant.SYNTHESIZER.SYNTHESIZER_HOYA.name(), name);
        if (enableTts)
            SynthesizeManager.shareInstance().removeSynthesizer(ISpeechConstant.SYNTHESIZER.SYNTHESIZER_HOYA.name()); // 移除离线 tts
        else
            SynthesizeManager.shareInstance().addSynthesizer(0, ISpeechConstant.SYNTHESIZER.SYNTHESIZER_HOYA.name()); // 添加离线 tts
    }

    static class AsrBean {
        public AsrBean(boolean isLast, String rText) {
            this.isLast = isLast;
            this.rText = rText;
        }

        boolean isLast;
        String rText;
    }
    public List<AsrBean> parseToAsrBean(String input) {
        String[] lines = input.split("\n");
        List<AsrBean> asrBeans = new ArrayList<>();
        for (String line : lines) {
            if (!line.startsWith("\"")) continue;
            boolean isLast = line.contains("\"isLast\":true");
            String rText = line.substring(line.indexOf("\"rText\":\"") + 9, line.lastIndexOf("\""));
            if (isLast) {
                Log.d(TAG, "parseToAsrBean: ");
            }
            asrBeans.add(new AsrBean(isLast, rText));
        }
        return asrBeans;
    }

    void testPunctuationSegmentation() {
        SpeechSessionContext.SentenceParser sentenceParser = new SpeechSessionContext.SentenceParser();
        new Thread(() -> {
//            String path = "asr_samples/测试Case.txt";
            String path = "asr_samples/测试Case-short.txt";
            byte[] contentBytes = DefaultInputStream2ByteArray(path);
            List<AsrBean> asrBeans = parseToAsrBean(new String(contentBytes));
            for (int i = 0; i < asrBeans.size(); i++) {
                AsrBean bean = asrBeans.get(i);

                long startTime = System.currentTimeMillis(); // 实际花的时间

                sentenceParser.hasNewSentence3(bean.rText, bean.isLast, (String sentence) -> {
                    long costTime = System.currentTimeMillis() - startTime; // 实际花的时间
                    Log.d(TAG, "语义断句 播放: " + sentence + " 当前句子: (" + bean.isLast + "=" + costTime + ")" + bean.rText);
                });
                Log.d(TAG, "语义断句 将处理: " + bean.rText + " 当前句子: (" + bean.isLast + ")");
            }
        }).start();
    }

    public byte[] DefaultInputStream2ByteArray(String path) {

        try {
            InputStream in = this.getAssets().open(path);

            ByteArrayOutputStream out = new ByteArrayOutputStream();
            byte[] buffer = new byte[1024 * 4];
            int n = 0;
            while ((n = in.read(buffer)) != -1) {
                out.write(buffer, 0, n);
            }

            in.close();

            return out.toByteArray();
        } catch (FileNotFoundException e) {
            e.printStackTrace();
        } catch (IOException e) {
            e.printStackTrace();
        }

        return new byte[0];
    }

    void send(String chkey1, String chkey2, TestRecorder tagFileHandle, short[] doaPacket, AudioDoaJni.Channel mou, short[] bufferInChannel1, short[] bufferInChannel2) {

//        if (this.writeToFile) {
        if (tagFileHandle != null) {
            byte[] firstTag = new byte[]{(byte) 0xff, (byte) 0x7f, (byte) 0x0, (byte) 0x0};
            byte[] SecondTag = new byte[]{(byte) 0x0, (byte) 0x0, (byte) 0x00, (byte) 0x80};
            tagFileHandle.write(mou == AudioDoaJni.Channel.First ? firstTag : SecondTag);
            tagFileHandle.write(BytesTrans.getInstance().Shorts2Bytes(doaPacket));
        }
//        }

        for (int i = 0; i < nSampleDoaInPerChannel; i++) {
            bufferInChannel1[i % nSizeInChannel] = doaPacket[i * 2];
            bufferInChannel2[i % nSizeInChannel] = doaPacket[i * 2 + 1];

            if (mou != AudioDoaJni.Channel.First) {
                bufferInChannel1[i % nSizeInChannel] = 0;
            } else {
                bufferInChannel2[i % nSizeInChannel] = 0;
            }

            if (i % nSizeInChannel == nSizeInChannel - 1) {
//                synchronized (audioChannel) {
                // copy 出新数据送入通道
                AiSpeechManager.shareInstance().writeAudioToChannel(chkey1, Arrays.copyOf(bufferInChannel1, bufferInChannel1.length));
                AiSpeechManager.shareInstance().writeAudioToChannel(chkey2, Arrays.copyOf(bufferInChannel2, bufferInChannel1.length));
//                            Arrays.fill(buffer1, (short) 0);
//                            Arrays.fill(buffer2, (short) 0);
//                }
                try {
                    Thread.sleep(10);
                } catch (InterruptedException e) {
                    e.printStackTrace();
                }
            }
        }
    }

    private void startMt() {
//        String module = ISpeechConstant.TRANSLATOR.IFLY_OFFLINE_MT.getName();
        // String module = ISpeechConstant.TRANSLATOR.NIU_OFFLINE_MT.getName();
        String module = ISpeechConstant.TRANSLATOR.ISPEECH.getName();
        AiSpeechManager.shareInstance().translate(chkey, srcText, srcCode, dstCode, module, (ret) -> {
            Log.d(TAG, "startMt: 翻译结果: " + ret);
            return null;
        });
    }

    private void startOfflineTest() {
        Map<String, String> oriTexts = new HashMap<String, String>() {{
            put("zh-CN", "不允许使用数字");
            put("en-US", "Numbers are not allowed");
            put("ja-JP", "数字は許可されていません");
            put("ko-KR", "숫자는 허용되지 않습니다.");
            put("fr-FR", "Les chiffres ne sont pas autorisés");
            put("es-ES", "No se permiten números");
            put("ru-RU", "Цифры не допускаются");
            put("de-DE", "Zahlen sind nicht erlaubt");
        }};

        String zhCode = "zh-CN";
        String[] iflyOthers = {"en-US", "ja-JP", "fr-FR", "es-ES", "ru-RU", "de-DE"};
//        String[] iflyOthers = {"en-US", "ja-JP", "fr-FR", "es-ES"};
        for (String ot : iflyOthers) {
            startOfflineMt(zhCode, ot, oriTexts.get(zhCode));
            startOfflineMt(ot, zhCode, oriTexts.get(ot));
        }

        String enCode = "en-US";
        String[] niuOthers = {"ja-JP", "ko-KR", "fr-FR", "es-ES", "ru-RU", "de-DE"};
//        String[] niuOthers = {"ja-JP", "ko-KR", "fr-FR", "es-ES"};
//        String[] niuOthers = {"ja-JP","fr-FR"};
//        for (String ot : niuOthers) {
//            startOfflineMt(enCode, ot, oriTexts.get(enCode));
//            startOfflineMt(ot, enCode, oriTexts.get(ot));
//        }
    }

    private void startOfflineMt(String srcCode, String dstCode, String srcText) {
        co.timekettle.speech.SpeechTask<String, String> task = new co.timekettle.speech.SpeechTask<>(co.timekettle.speech.SpeechTask.TaskType.MT, chkey, System.currentTimeMillis());
        task.request.module = ISpeechConstant.TRANSLATOR.NIU_OFFLINE_MT.getName();
        task.request.code = srcCode;
        task.request.dstCode = dstCode;
        task.request.data = srcText;
        TranslateManager.shareInstance().start(task, (co.timekettle.speech.SpeechTask<String, String> t) -> {
            Log.d(TAG, "startOfflineMt: [" + srcCode + dstCode + "]翻译结果: " + t.response.data);
            toggleOffline(srcCode, dstCode);
            synchronized (this) {
                this.notify();
            }
            return null;
        });
        try {
            synchronized (this) {
                this.wait(10000);
            }
        } catch (InterruptedException e) {
            e.printStackTrace();
        }
    }

    private void tryOfflineAuth() {
        OfflineManager.getInstance().tryFetchIflyAuth(srcCode);
        OfflineManager.getInstance().tryFetchXzyAuth(this, srcCode);
    }

    class SpeechTask {
        String code;
        String text;

        SpeechTask(String code, String text) {
            this.code = code;
            this.text = text;
        }
    }

    private void startTts() {
        SpeechTask[] tasks = {
                new SpeechTask("fr-FR", "de l'eau，s'il vous plaît"),
                new SpeechTask("it-IT", "Il mio telefono è morto，posso caricarlo nella tua auto?"),
                new SpeechTask("en-US", "welcome to the hotel"),
                new SpeechTask("zh-CN", "我需要加一床被子"),
                new SpeechTask("it-IT", "Devo dare un consiglio qui ?"),
                new SpeechTask("zh-CN", "小心，我箱子里有电脑"),
                new SpeechTask("zh-CN", "顺便问一下，餐厅在哪里？"),
        };
        for (SpeechTask task : tasks) {
            try {
                Thread.sleep(200);
            } catch (InterruptedException e) {
                throw new RuntimeException(e);
            }
            AiSpeechManager.shareInstance().synthesizeText(this, task.code, task.text, null);
        }
    }

    private void switchVoiceName() {
        String voiceName = JsonSynthesizeRequest.SynthesizeVoiceGender.Female.name();
        AudioChannel[] channels = AiSpeechManager.shareInstance().getAudioChannels();
        if (channels == null || channels.length == 0) {
            Toast.makeText(this, "通道为空, 还未开始录音识别, 请先开启!", Toast.LENGTH_SHORT).show();
            return;
        }

        for (AudioChannel channel : channels) {
            if (channel.optsForEngine != null) {
                String _voiceName = (String) channel.optsForEngine.get(SpeechRequest.OptsKeyTtsVoiceName);
                if (_voiceName != null && _voiceName.equals(voiceName)) {
                    voiceName = JsonSynthesizeRequest.SynthesizeVoiceGender.Male.name();
                }
            }

            channel.optsForEngine = new HashMap<>();
            channel.optsForEngine.put(SpeechRequest.OptsKeyTtsVoiceName, voiceName);
        }
        Toast.makeText(this, "切换合成音色: " + voiceName, Toast.LENGTH_SHORT).show();
    }

    private void startTtsAndPlay() {
//        boolean needUpSpeaker = false; // true 为需要上方喇叭播放, false 为需要下方喇叭播放
//        String extPlayDirection = needUpSpeaker ? AudioChannel.Role.Self.toString() : AudioChannel.Role.Other.toString();
//        AiSpeechManager.shareInstance().playText(srcText, srcCode, ISpeechConstant.SPEAKER.T1PHONE.toString(), extPlayDirection, (ret) -> {
//            Log.d(TAG, "startTts: 合成结果: " + ret);
//            return null;
//        });

        AudioChannel[] channels = AiSpeechManager.shareInstance().getAudioChannels();
        if (channels.length == 0) {
            Toast.makeText(this, "先开启识别等通道后测试合成播放", Toast.LENGTH_SHORT).show();
            return;
        }
        AudioChannel channel = channels[0];
        // 测试播放和播放队列
        int count = 0;
        for (int i = 0; i < 3; i++) {
            String text = "";

            if (count % 3 == 0) {
                text = "第" + (count % 3) + "次测试合成并播放, 长语句合成; Facebook（现Meta）基于深度学习的音频压缩算法主要是通过他们的研究项目“EnCodec”实现的。EnCodec是一个利用深度学习的音频编解码器，能够实现高效的音频压缩。以下是对EnCodec及其相关技术的介绍。 EnCodec: 基于深度学习的音频压缩算法 EnCodec 是由 Meta AI（前身为 Facebook AI Research, FAIR）开发的一种新型音频压缩方法。它使用深度神经网络来对音频数据进行高效编码和解码，显著减少存储和带宽需求，同时保持高音质。";
            } else if (count % 3 == 1) {
                text = "第" + (count % 3) + "次合成";
            } else if (count % 3 == 2) {
                text = "第" + (count % 3) + "次合成";
            }

            long timestamp = System.currentTimeMillis();
            AiSpeechManager.shareInstance().playText(channel, timestamp, text, srcCode, (ret) -> {
                Log.d(TAG, "startTts: 合成结果: " + ret);
                return null;
            });

            try {
                Thread.sleep(10); // Sleep for 10 milliseconds
            } catch (InterruptedException e) {
                Thread.currentThread().interrupt();
                throw new RuntimeException(e);
            }

            count += 1;
        }
    }


    long play_workId;
    String chkey;

    private void startPlay() {
        if (btn_play.getText().equals("开始播放")) {
            btn_play.setText("停止播放");

            chkey = "test";
            String speaker = ISpeechConstant.SPEAKER.PHONE.toString();
//            String speaker = ISpeechConstant.SPEAKER.HEADSET.toString();

//            chkey = "E8:0D:49:35:28:A3";
//            String speaker = ISpeechConstant.SPEAKER.BLE.toString();
            try {
//                InputStream in = this.getAssets().open("audio_samples/en16ktts.pcm");
                InputStream in = this.getAssets().open("audio_samples/zh16ktts.pcm");
//                InputStream in = this.getAssets().open("audio_samples/zh16ktts-stereo.pcm");
//                InputStream in = this.getAssets().open("audio_samples/zh16ktts-stereo-diff.pcm");
                byte[] buffer = new byte[in.available()];
//                byte[] buffer = new byte[640];
                in.read(buffer);

                // 倍速播放
                short[] output = new SoundStretchJni().stretch(BytesTrans.getInstance().Bytes2Shorts(buffer), 3.0f);
                play_workId = AiSpeechManager.shareInstance().play(BytesTrans.getInstance().Shorts2Bytes(output), speaker, (ret) -> {
                    Log.d(TAG, "startTts: 播放完成: " + ret);

                    btn_play.setText("停止播放");
                    return null;
                });
            } catch (IOException e) {
                e.printStackTrace();
            }
        } else {
            btn_play.setText("开始播放");

            AiSpeechManager.shareInstance().stopWorker(play_workId, chkey);
            AiSpeechManager.shareInstance().stopAllWorker();
        }
    }

    static boolean isDestroy = false;
    static boolean DownSpeakerPlay = false;

    private void startStereoPlay() {

        if (btn_play.getText().equals("开始播放")) {
            btn_play.setText("停止播放");

            chkey = "test";
//            String speaker = ISpeechConstant.SPEAKER.HEADSET.toString();
            String speaker = ISpeechConstant.SPEAKER.T1PHONE.toString();

//            chkey = "E8:0D:49:35:28:A3";
//            String speaker = ISpeechConstant.SPEAKER.BLE.toString();
            new Thread(() -> {
                try {
//                    InputStream in = this.getAssets().open("audio_samples/en16ktts.pcm");
//                    InputStream in = this.getAssets().open("audio_samples/zh16ktts.pcm");
//                InputStream in = this.getAssets().open("audio_samples/zh16ktts-stereo.pcm");
//                InputStream in = this.getAssets().open("audio_samples/zh16ktts-stereo-diff.pcm");
                    InputStream in = this.getAssets().open("audio_samples/20221115_104142-speaker-source.pcm");
//                    InputStream in = this.getAssets().open("tts/zh16ktts.pcm");
                    byte[] buffer = new byte[in.available()];
//                byte[] buffer = new byte[640];
                    in.read(buffer);

                    CountDownLatch countDown = new CountDownLatch(1);
                    while (!isDestroy) {
//                        DownSpeakerPlay = !DownSpeakerPlay;
                        CountDownLatch finalCountDown = countDown;
                        play_workId = AiSpeechManager.shareInstance().play(Arrays.copyOf(buffer, buffer.length), speaker, DownSpeakerPlay ? AudioChannel.Role.Other.toString() : AudioChannel.Role.Self.toString(), (ret) -> {
                            Log.d(TAG, "startTts: " + (DownSpeakerPlay ? "下方 mic" : "上方 mic") + "播放完成: " + ret);

                            btn_play.setText("开始播放");
                            finalCountDown.countDown();
                            return null;
                        });
                        try {
                            countDown.await();
                        } catch (InterruptedException e) {
                            e.printStackTrace();
                        }
                        try {
//                            Thread.sleep((long) ((10 + Math.random() * 5) * 1000));
                            Thread.sleep((long) (2 * 1000));
                        } catch (InterruptedException e) {
                            e.printStackTrace();
                        }
                        countDown = new CountDownLatch(1);
                    }

                } catch (IOException e) {
                    e.printStackTrace();
                }
            }).start();

        } else {
            btn_play.setText("开始播放");

            AiSpeechManager.shareInstance().stopWorker(play_workId, chkey);
            AiSpeechManager.shareInstance().stopAllWorker();
            isDestroy = true;
        }
    }

    private static final int REQUEST_CODE_PERMISSION_RECORD_AUDIO = 2;

    private void checkPermissions() {
//        String[] permissions = {Manifest.permission.RECORD_AUDIO};
        String[] permissions = {Manifest.permission.RECORD_AUDIO, Manifest.permission.READ_EXTERNAL_STORAGE};
//        String[] permissions = {Manifest.permission.RECORD_AUDIO, Manifest.permission.READ_EXTERNAL_STORAGE, Manifest.permission.WRITE_EXTERNAL_STORAGE};
        List<String> permissionDeniedList = new ArrayList<>();
        for (String permission : permissions) {
            int permissionCheck = ContextCompat.checkSelfPermission(this, permission);
            if (permissionCheck == PackageManager.PERMISSION_GRANTED) {
                onPermissionGranted(permission);
            } else {
                permissionDeniedList.add(permission);
            }
        }
        if (!permissionDeniedList.isEmpty()) {
            Log.e(TAG, "checkPermissions: 未授权录音");
            String[] deniedPermissions = permissionDeniedList.toArray(new String[permissionDeniedList.size()]);
            ActivityCompat.requestPermissions(this, deniedPermissions, REQUEST_CODE_PERMISSION_RECORD_AUDIO);
        }
    }

    private void onPermissionGranted(String permission) {
        switch (permission) {
            case Manifest.permission.RECORD_AUDIO:
                break;
        }
    }

    @Override
    protected void onActivityResult(int requestCode, int resultCode, Intent data) {
        super.onActivityResult(requestCode, resultCode, data);
        if (requestCode == REQUEST_CODE_PERMISSION_RECORD_AUDIO) {
            Log.d(TAG, "checkPermissions: 授权了录音");
        }
    }
}