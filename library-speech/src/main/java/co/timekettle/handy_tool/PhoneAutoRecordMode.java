package co.timekettle.handy_tool;

import android.content.Context;
import android.util.Log;

import androidx.annotation.NonNull;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.Map;

import co.timekettle.speech.AiSpeechManager;
import co.timekettle.speech.AudioChannel;
import co.timekettle.speech.AudioChannelOptions;
import co.timekettle.speech.AudioRecordOptions;
import co.timekettle.speech.EngineHost;
import co.timekettle.speech.ISpeechConstant;
import co.timekettle.speech.SpeechResponse;
import co.timekettle.speech.SpeechSessionContext;
import co.timekettle.speech.jni.TmkCustomTranslationJni;

public class PhoneAutoRecordMode {
   final String modeDesc = "AutoRecordMode"; // 注意修改, 现绑定了判断, 如丢包统计, 后续修改成<模式的枚举>
   static final String TAG = "AutoRecordMode";

   Context mContext;
   public String selfCode = "zh-CN";
   public String otherCode = "en-US";
   long baseMinVadEnergy = AudioChannel.defalutMinVadEnergy;

   protected AiSpeechManager.Listener aiSpeechListener = new AiSpeechManager.Listener() {
      @Override
      public void onVadBegin(@NonNull AudioChannel channel, long session) {
//            Log.d(TAG, "onVadBegin: " + channel.getName() + "[" + session + "] ");
      }

      @Override
      public void onVadEnd(@NonNull AudioChannel channel, long session) {
//            Log.d(TAG, "onVadEnd: " + channel.getName() + "[" + session + "] ");
      }

      @Override
      public void onActivity(@NonNull AudioChannel channel, long session, float volume) {
//            Log.d(TAG, "onActivity: " + channel.getName() + "[" + session + "] ");
      }

      @Override
      public void onRecognizeResult(String chkey, long session, String srcCode, String dstCode, boolean isLast, String text, String engine, TmkCustomTranslationJni.TmkCustomTranslationResult ctr) {
         Log.d(TAG, (ctr != null ? "自定义" : "") + "识别: " + chkey + "[" + engine + "] " + session + " " + isLast + " " + text + (ctr != null ? " ctr:" + ctr: ""));
      }

      @Override
      public void onTranslateResult(String chkey, long session, boolean isLast, String text, String engine, TmkCustomTranslationJni.TmkCustomTranslationResult ctr) {
         Log.d(TAG, (ctr != null ? "自定义" : "")+"翻译: " + chkey + "[" + engine + "] " + session + " " + isLast + " " + text + (ctr != null ? " ctr:" + ctr.toCtOffsetString() : ""));
      }

      @Override
      public void onSynthesizeCompleted(String chkey, long session, byte[] data, String engine) {
         Log.d(TAG, "合成: " + chkey + "[" + engine + "] " + session);
      }

      @Override
      public void onError(String chkey, long session, int code, String message) {
         Log.e(TAG, "处理出错: " + chkey + " " + session + " code:" + code + " message:" + message);
      }

      @Override
      public void onSpeakStart(String chkey, long session, String text, String speakerType, Object extData) {
         Log.d(TAG, "播放开始: " + chkey + "[" + speakerType + "] " + session + " " + extData);
      }

      @Override
      public void onSpeakEnd(String chkey, long session, String text, String speakerType, Object extData) {
         Log.d(TAG, "播放结束: " + chkey + "[" + speakerType + "] " + session + " " + extData);
      }

      @Override
      public void onSpeechTranslationResult(AudioChannel channel, SpeechSessionContext context, SpeechResponse.ResponseMessage msg) {

      }

      @Override
      public void onFinished(String chkey, long session, int type, String engine) {

      }
   };

   public void enter(Context context) {
      Log.d(TAG, "不选择服务器, 由内部指定");
      this.mContext = context;
      TranslationModeUtil.enterMode(this.mContext);
   }

   public void enter(Context context, EngineHost host) {
      Log.d(TAG, "选择服务器: " + host.ip + ":" + host.port);
      this.mContext = context;
      TranslationModeUtil.enterMode(this.mContext, host, null);
   }

   public void exit() {
      this.mContext = null;
      TranslationModeUtil.exitMode();
   }

   public void updateHost(String ip, int port) {
      Log.d(TAG, "更新服务器: " + ip);
      TranslationModeUtil.setSpecificHost(ip, port);
   }

   public void start(String selfCode, AiSpeechManager.Listener listener) {
      this.start(selfCode, null, listener);
   }

   public void start(String selfCode, String otherCode, AiSpeechManager.Listener listener) {
      this.selfCode = selfCode;
      this.otherCode = otherCode;
      this.baseMinVadEnergy = TranslationModeUtil.getMinVadEnergy(TranslationModeUtil.RecoderDeviceType.Phone);

      String chkey = "test";
      String recorder = ISpeechConstant.RECORDER.PHONE.toString();
      String speaker = ISpeechConstant.SPEAKER.PHONE.toString();
      ArrayList<Map<String, Object>> channel_options = new ArrayList<>();
      HashMap<String, Object> option = new HashMap<String, Object>() {{
         put(AudioChannelOptions.KEY, chkey);
         put(AudioChannelOptions.SRC_CODE, selfCode);
         put(AudioChannelOptions.DST_CODE, otherCode);
         put(AudioChannelOptions.RECORDER, recorder);
         put(AudioChannelOptions.SPEAKER_TYPE, speaker);
          put(AudioChannelOptions.VAD_END, 200); // 200 * 16ms

         put(AudioChannelOptions.MIN_VAD_ENERGY, baseMinVadEnergy);
      }};
      channel_options.add(option);
      HashMap<String, Object> recorder_options = new HashMap<String, Object>() {{
         put(AudioRecordOptions.CAN_RECORD_WHEN_SPEAKING, false);
      }};
      TranslationModeUtil.startMode(channel_options, recorder_options, listener != null ? listener : aiSpeechListener);
//      TranslationModeUtil.disableTranslate();
//      TranslationModeUtil.disableTtsAndPlay();

      AiSpeechManager.shareInstance().setActivityVolumeDuration(6);
   }

   public void start1(String selfCode, String otherCode, AiSpeechManager.Listener listener) {
      assert this.mContext != null : "mContext为空, 请先初始化";
      this.selfCode = selfCode;
      this.otherCode = otherCode;
      this.baseMinVadEnergy = TranslationModeUtil.getMinVadEnergy(TranslationModeUtil.RecoderDeviceType.Phone);

      String chkey = "test";

      ArrayList<AudioChannel> channels = new ArrayList<>();
      AudioChannel channel = new AudioChannel(this.mContext, chkey, true);
      channel.setSrcCode(selfCode);
      channel.setDstCode(otherCode);
      channel.setRecorder(ISpeechConstant.RECORDER.PHONE.toString());
      channel.setSpeaker(ISpeechConstant.SPEAKER.PHONE.toString());
      // channel.setVadEnd(200); // 200 * 16ms
      channels.add(channel);

      HashMap<String, Object> recorder_options = new HashMap<String, Object>() {{
         put(AudioRecordOptions.CAN_RECORD_WHEN_SPEAKING, false);
      }};
      TranslationModeUtil.startModeWithChannels(channels, recorder_options, listener != null ? listener : aiSpeechListener);
   }

   public void stop() {
      TranslationModeUtil.stopMode();
   }
}
