package co.timekettle.handy_tool;

import android.util.Log;

import androidx.annotation.NonNull;;

import co.timekettle.speech.AiSpeechManager;
import co.timekettle.speech.AudioChannel;
import co.timekettle.speech.SpeechResponse;
import co.timekettle.speech.SpeechSessionContext;
import co.timekettle.speech.jni.TmkCustomTranslationJni;

/**
 * 模式, 一般一个翻译方向抽象为一个通道, 如当前 zh-CN <-> en-US 需要双向互译, 则通道数至少需要两个, 特殊地, 若一个翻译方向存在多路音频数据, 则每一路需要抽象成一个通道, 方便进行数据流控制
 */
public abstract class BaseTranslationMode {
   final String modeDesc = "wt2(001/004)同传模式"; // 注意修改, 现绑定了判断, 如丢包统计, 后续修改成<模式的枚举>
   static final String TAG = "SomeMode";

   String selfCode = "zh-CN";
   String otherCode = "en-US";
   /// 每个产品录音(单个包)有默认的 vad 最小能量的阈值, 此值根据产品和模式变化, 如 W3 同传 是 AudioChannel.defalutMinVadEnergy / 3, W3其他模式时 AudioChannel.defalutMinVadEnergy / 10;
   long baseMinVadEnergy = AudioChannel.defalutMinVadEnergy;

   protected AiSpeechManager.Listener aiSpeechListener = new AiSpeechManager.Listener() {
      @Override
      public void onVadBegin(@NonNull AudioChannel channel, long session) {
//            Log.d(TAG, "onVadBegin: " + channel.getName() + "[" + session + "] ");
      }

      @Override
      public void onVadEnd(@NonNull AudioChannel channel, long session) {
//            Log.d(TAG, "onVadEnd: " + channel.getName() + "[" + session + "] ");
      }

      @Override
      public void onActivity(@NonNull AudioChannel channel, long session, float volume) {
//            Log.d(TAG, "onActivity: " + channel.getName() + "[" + session + "] ");
      }

      @Override
      public void onRecognizeResult(String chkey, long session, String srcCode, String dstCode, boolean isLast, String text, String engine, TmkCustomTranslationJni.TmkCustomTranslationResult ctr) {
         Log.d(TAG, (ctr != null ? "自定义" : "") + "识别: " + chkey + "[" + engine + "] " + session + " " + isLast + " " + text + (ctr != null ? " ctr:" + ctr: ""));
      }

      @Override
      public void onTranslateResult(String chkey, long session, boolean isLast, String text, String engine, TmkCustomTranslationJni.TmkCustomTranslationResult ctr) {
         Log.d(TAG, "onTranslateResult: " + chkey + "[" + engine + "] " + session + " " + isLast + " " + text);
      }

      @Override
      public void onSynthesizeCompleted(String chkey, long session, byte[] data, String engine) {
         Log.d(TAG, "onSynthesizeCompleted: " + chkey + "[" + engine + "] " + session);
      }

      @Override
      public void onError(String chkey, long session, int code, String message) {
         Log.e(TAG, "onError: " + chkey + " " + session + " code:" + code + " message:" + message);
      }

      @Override
      public void onSpeakStart(String chkey, long session, String text, String speakerType, Object extData) {
         Log.d(TAG, "onSpeakStart: " + chkey + "[" + speakerType + "] " + session + " " + extData);
      }

      @Override
      public void onSpeakEnd(String chkey, long session, String text, String speakerType, Object extData) {
         Log.d(TAG, "onSpeakEnd: " + chkey + "[" + speakerType + "] " + session + " " + extData);
      }

      @Override
      public void onFinished(String chkey, long session, int type, String engine) {

      }

      @Override
      public void onSpeechTranslationResult(AudioChannel channel, SpeechSessionContext context, SpeechResponse.ResponseMessage msg) {

      }
   };

   public void start(String selfCode, AiSpeechManager.Listener listener) {
      this.start(selfCode, null, listener);
   }

   public void start(String selfCode, String otherCode, AiSpeechManager.Listener listener) {
      this.selfCode = selfCode;
      this.otherCode = otherCode;
   }

   public void stop() {
      TranslationModeUtil.stopMode();
   }
}