package co.timekettle.handy_tool;

import android.content.Context;

import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.function.Function;

import co.timekettle.speech.AiSpeechManager;
import co.timekettle.speech.AudioChannel;
import co.timekettle.speech.AudioChannelOptions;
import co.timekettle.speech.AudioRecordOptions;
import co.timekettle.speech.EngineHost;
import co.timekettle.speech.OfflineManager;
import co.timekettle.speech.utils.AiSpeechLogUtil;
import co.timekettle.tmkengine.TmkSpeechClient;

/**
 * 模式工具, 一般使用步骤:
 * enterMode()
 * startMode()
 * ...
 * ... 随时热更新通道信息
 * updateChannel()
 * ...
 * ... 暂停后更新通道信息(语言等)
 * pauseMode()
 * updateChannel()
 * resumeMode()
 * ... 切换说话通道
 * switchToChannel()
 * ...
 * pauseMode()
 * resumeMode()
 * ...
 * stopMode()
 * exitMode()
 */
public class TranslationModeUtil {
    enum RecoderDeviceType {
        WT2, ZERO, M2, M2P, W3, M3, Phone
    }

    private static final String SubscriptionKey = "9A5C1E41066458D50F91636A111FED89";
    private static final String TAG = "ModeUtil";
    public static boolean enableDebugAudioFile = true;

    /**
     * 设置日志回调, 根据
     * @param level 等级是控制回调内容
     * @param callback 回调内容
     */
    public static void setLogCallback(int level, AiSpeechLogUtil.SpeechLogCallback callback) {
        // 进入模式页
        AiSpeechLogUtil.setLogLevel(level);
        AiSpeechLogUtil.setLogCallback(callback);
    }

    /**
     * 进入模式, 初始化
     * @param context 上下文
     * @param host 服务器
     * @param offlineConfig 离线配置
     */
    public static void enterMode(Context context, EngineHost host, Map<String, Object> offlineConfig) {
        OfflineManager.getInstance().loadOffline(context, offlineConfig);
        AiSpeechManager.shareInstance().create(context, SubscriptionKey, host);
    }

    /**
     * 进入模式, 初始化
     * @param context 上下文
     */
    // 进入模式, 初始化
    public static void enterMode(Context context) {
        enterMode(context, null, null);
    }

    /**
     * 退出模式, 反初始化
     */
    public static void exitMode() {
        // 退出模式页
        AiSpeechManager.shareInstance().destroy();
        OfflineManager.getInstance().closeOffline(null);
    }

    /**
     * 指定引擎服务器
     * @param ip 指定服务器 ip
     * @param port 指定服务器 端口
     */
    public static void setSpecificHost(String ip, int port) {
        TmkSpeechClient.shareInstance().setSpecificHost(new EngineHost(ip, port));
    }

    /**
     * 开启录音, 录音数据会直接流向通道进行识别任务处理, 事件响应通过 Listener 回调
     * @param channel_options 通道选项, 查看 {@link co.timekettle.speech.AudioChannelOptions}
     * @param recorder_options 录音器选项, 查看 {@link co.timekettle.speech.AudioRecordOptions}
     * @param listener 唤醒事件, 识别翻译合成事件监听器
     */
    public static void startMode(List<Map<String, Object>> channel_options, Map<String, Object> recorder_options, AiSpeechManager.Listener listener) {
        AiSpeechManager.shareInstance().setListener(listener);

        Set<String> recorders = new HashSet<>();
        channel_options.forEach((channel_option) -> {
            String recoder = (String) channel_option.get(AudioChannelOptions.RECORDER);
            if (recoder != null) recorders.add(recoder);
            channel_option.put(AudioChannelOptions.WRITE_TO_FILE, enableDebugAudioFile);
            channel_option.put(AudioChannelOptions.SHOULD_GEN_VAD_TAG, enableDebugAudioFile);
            AiSpeechManager.shareInstance().addChannel(channel_option);
        });

        recorder_options.put(AudioRecordOptions.WRITE_TO_FILE, enableDebugAudioFile);
        recorders.forEach((recorder) -> {
            AiSpeechManager.shareInstance().startRecorder(recorder, recorder_options);
        });
    }

    /**
     * 开启录音, 录音数据会直接流向通道进行识别任务处理, 事件响应通过 Listener 回调
     * @param channels 通道选项, 查看 {@link co.timekettle.speech.AudioChannel}
     * @param recorder_options 录音器选项, 查看 {@link co.timekettle.speech.AudioRecordOptions}
     * @param listener 唤醒事件, 识别翻译合成事件监听器
     */
    public static void startModeWithChannels(List<AudioChannel> channels, Map<String, Object> recorder_options, AiSpeechManager.Listener listener) {
        AiSpeechManager.shareInstance().setListener(listener);
        Set<String> recorders = new HashSet<>();
        channels.forEach((AudioChannel channel) -> {
            String recoder = channel.recorder;
            if (recoder != null) recorders.add(recoder);
            channel.setWriteToFile(enableDebugAudioFile);
            channel.setMarkVadDataTag(enableDebugAudioFile);
            AiSpeechManager.shareInstance().addChannel(channel);
        });
        recorder_options.put(AudioRecordOptions.WRITE_TO_FILE, enableDebugAudioFile);
        recorders.forEach((recorder) -> {
            AiSpeechManager.shareInstance().startRecorder(recorder, recorder_options);
        });
    }

    public static void stopMode() {
        AiSpeechManager.shareInstance().stopAllRecorder();
        AiSpeechManager.shareInstance().stopAllWorker();
        AiSpeechManager.shareInstance().setListener(null);
    }

    /**
     * 暂停录音, 如用于进入设置页面等
     */
    public static void pauseMode() {
        AiSpeechManager.shareInstance().muteAllRecorder();
    }

    /**
     * 恢复录音, 如用于进入设置页面后回来模式页等
     */
    public static void resumeMode() {
        AiSpeechManager.shareInstance().unmuteAllRecorder();
    }

    /**
     * 录音暂停, 数据流不会流入通道
     * @param name 录音器名称
     */
    public static void pauseModeAndRecorder(String name) {
        AiSpeechManager.shareInstance().pauseRecorder(name);
    }

    /**
     * 录音恢复, 数据流会流入通道
     * @param name 录音器名称
     */
    public static void resumeModeAndRecorder(String name) {
        AiSpeechManager.shareInstance().resumeRecorder(name);
    }

    /**
     * 切换通道, 只启用指定通道, 通常用于触控模式
     * @param chkey 通道 key
     */
    public static void switchToChannel(String chkey) {
        AiSpeechManager.shareInstance().disableAllAudioChannel();
        AiSpeechManager.shareInstance().enableAudioChannel(chkey);
    }

    /**
     * 启用通道, 通常用于触控模式
     * @param chkey 通道 key
     */
    public static void enableChannel(String chkey) {
        AiSpeechManager.shareInstance().enableAudioChannel(chkey);
    }

    /**
     * 禁用通道, 通常用于触控模式
     * @param chkey 通道 key
     */
    public static void disableChannel(String chkey) {
        AiSpeechManager.shareInstance().disableAudioChannel(chkey);
    }

    /**
     * 通道是否已禁用, 通常用于触控模式
     * @param chkey 通道 key
     */
    public static boolean isEnableChannel(String chkey) {
        return AiSpeechManager.shareInstance().isEnableAudioChannel(chkey);
    }

    /**
     * 通过通道的role获取整个通道对象
     * @param role 通道角色 {@link AudioChannel.Role}
     * @return 通道数组
     */
    public static List<AudioChannel> getChannelsByRole(AudioChannel.Role role) {
        return AiSpeechManager.shareInstance().getAudioChannels(role.toString());
    }

    /**
     * 禁用所有通道，通常用于同传模式
     */
    public static void disableAllChannel() {
        AiSpeechManager.shareInstance().disableAllAudioChannel();
    }

    /**
     * 启用所有通道，通常用于同传模式
     */
    public static void enableAllChannel() {
        AiSpeechManager.shareInstance().enableAllAudioChannel();
    }

    /**
     * 是否有正在启用的通道
     */
    public static boolean hasActiveChannel() {
        for (AudioChannel audioChannel : AiSpeechManager.shareInstance().getAudioChannels()) {
            if (audioChannel.isEnabled()) return true;
        }
        return false;
    }

    /**
     * 通道参数更新
     * @param options 多个参数, 参数见 {@link co.timekettle.speech.AudioChannelOptions}
     */
    public static void updateChannel(Map<String, Object> options) {
        AiSpeechManager.shareInstance().updateChannel(options);
    }

    /**
     * 更新语言, 对应通道进行语言更新
     * @param selfCode 源语言 code
     * @param otherCode 目标语言 code
     */
    public static void updateLang(String selfCode, String otherCode) {
        for (AudioChannel channel : AiSpeechManager.shareInstance().getAudioChannels()) {
            if (Objects.equals(channel.role, AudioChannel.Role.Self.toString())) {
                channel.setSrcCode(selfCode);
                channel.setDstCode(otherCode);
            } else {
                channel.setSrcCode(otherCode);
                channel.setDstCode(selfCode);
            }
        }
    }

    /**
     * 设置灵敏度的值, 0.01f, 0.1f, 1.0f, 5.0f, 10.0f, 越嘈杂灵敏度要越低, 否则在嘈杂环境下容易误唤醒
     * @param sen 灵敏度倍数
     */
    public static void setSensitivity(float sen) {
        setSensitivity(sen , AudioChannel.defalutMinVadEnergy);
    }

    /**
     * 设置灵敏度的值, 0.01f, 0.1f, 1.0f, 5.0f, 10.0f, 越嘈杂灵敏度要越低, 否则在嘈杂环境下容易误唤醒
     * @param sen 灵敏度倍数
     * @param baseMinVadEnergy 基线最小 vad 能量
     */
    public static void setSensitivity(float sen, long baseMinVadEnergy) {
        AiSpeechManager.shareInstance().setMinVadEnergy((int) (sen * baseMinVadEnergy));
    }

    /**
     * 设置断句时长, 一般的等级为 0.4f, 0.6f, 1.0f, 2.0f, 3.0f , 单位 s
     * @param duration 断句时长, 单位 ms
     */
    public static void setBreakTime(int duration) {
        AiSpeechManager.shareInstance().setVadEnd(duration);
    }

    /**
     * 翻译接口, 一般用于模式页, 通过通道配置去翻译, 自定义或者其他翻译
     * @param channel 通道
     * @param text 需要翻译的文本
     */
    public static void translate(AudioChannel channel, String text, String code, String dstCode, Function callback) {
        AiSpeechManager.shareInstance().translate(channel, text, code, dstCode, callback);
    }

    /**
     * 合成播放接口, 一般用于模式页, 通过通道配置去合成并通过通道指定 speaker 播放
     * @param channel 通道
     * @param text 需要合成播放的文本
     */
    public static void playText(AudioChannel channel, long session, String text, String code, Function callback) {
        AiSpeechManager.shareInstance().playText(channel, session, text, code, callback);
    }

    /**
     * 获得不同产品的适合的不同的最小vad能量(经验值)
     * 每个产品录音(单个包)有默认的 vad 最小能量的阈值, 此值根据产品和模式变化, 如 W3 同传 是 AudioChannel.defalutMinVadEnergy / 5, W3其他模式时 AudioChannel.defalutMinVadEnergy / 10;
     * @param type 产品类型
     * @return 最小 vad 能量
     */
    static long getMinVadEnergy(RecoderDeviceType type) {
        switch (type) {
            case W3: return AudioChannel.defalutMinVadEnergy / 10; // 不同模式有区分, 特别地, 同传模式是 AudioChannel.defalutMinVadEnergy / 5
            case WT2:
            case ZERO:
            case M2:
            case M2P:
            case M3:
            case Phone:
            default: return AudioChannel.defalutMinVadEnergy;
        }
    }

    /**
     * W3 同传模式的最小vad能量值
     * @param type 产品类型
     * @return 最小 vad 能量
     */
    static long getMinVadEnergyOfW3Simual(RecoderDeviceType type) {
        return AudioChannel.defalutMinVadEnergy / 5;
    }

    static void enableTranslate() {
        AiSpeechManager.shareInstance().setTranslateDisabled(false);
    }

    static void disableTranslate() {
        AiSpeechManager.shareInstance().setTranslateDisabled(true);
    }

    static void enableTtsAndPlay() {
        AiSpeechManager.shareInstance().setSynthesizeDisabled(false);
    }

    static void disableTtsAndPlay() {
        AiSpeechManager.shareInstance().setSynthesizeDisabled(true);
    }

    /**
     * 翻译接口, 一般用于历史记录等非模式页等
     * @param text 源语言code对应的文本
     * @param code 需要翻译的文本的源语言code
     * @param dstCode 需要翻译的文本的目标语言code
     * @param callback 结果回调
     */
    static void translate(String text, String code, String dstCode, Function callback) {
        AiSpeechManager.shareInstance().translate(text, code, dstCode, callback);
    }

    /**
     * 合成播放接口, 一般用于历史记录等非模式页, 通过指定 speaker 播放
     * @param text 需要合成播放的文本
     * @param code 需要合成播放的文本的语言code
     * @param speaker 播放器类型
     * @param extRole 排查某类角色播放
     */
    static void playText(String text, String code, String speaker, String extRole, Function callback) {
        AiSpeechManager.shareInstance().playText(text, code, speaker, extRole, callback);
    }

    /**
     * 检查资源是否已下载
     * @param selfCode 源语言 code
     * @param otherCode 目标语言 code
     * @return 返回检查结果 CheckResult
     */
    static OfflineManager.CheckResult isReadyOffline(String selfCode, String otherCode) {
        return OfflineManager.getInstance().isReadyOffline(selfCode, otherCode);
    }

    /**
     * 查询支持哪些语言对的离线翻译
     * @param selfCode 源语言 code
     * @param otherCode 目标语言 code
     * @return 是否支持
     */
    static boolean isSupportOffline(String selfCode, String otherCode) {
        return OfflineManager.getInstance().isSupport(selfCode, otherCode);
    }

    /**
     * 当前 selfCode <-> otherCode 是否已启用离线
     * @param selfCode 源语言 code
     * @param otherCode 目标语言 code
     * @return 是否已启用
     */
    static boolean isEnableOffline(String selfCode, String otherCode) {
        return OfflineManager.getInstance().isEnable(selfCode, otherCode);
    }

    /**
     * 开启 srcCode <-> dstCode 离线, 先检查本地离线资源是否存在, 如已存在, 部分引擎进行预加载
     * 特别地, 小牛离线资源需要对语言对鉴权, 在下载完初始化是最佳的选择, 下载判断可以调用 openOffline() 判断
     * @param selfCode 源语言 code
     * @param otherCode 目标语言 code
     * @return 返回检查结果 CheckResult, vaild 则离线已成功开启
     */
    static OfflineManager.CheckResult openOffline(String selfCode, String otherCode) {
        return OfflineManager.getInstance().openOffline(selfCode, otherCode);
    }

    /**
     * 关闭语言对离线功能
     */
    static boolean closeOffline(String selfCode, String otherCode) {
        return OfflineManager.getInstance().closeOffline(selfCode, otherCode);
    }
}
