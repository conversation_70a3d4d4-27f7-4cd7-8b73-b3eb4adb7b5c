package co.timekettle.tmkengine.SocketClient;

import co.timekettle.tmkengine.NettyClient.TmkClient;

public final class SocketClientBuilder {
    private String host;
    private int port = 7889;
    private TmkClient.TmkClientListener listener;

    private SocketClientBuilder() {
    }

    public static SocketClientBuilder aTcpClient() {
        return new SocketClientBuilder();
    }

    public SocketClientBuilder withHost(String host) {
        this.host = host;
        return this;
    }

    public SocketClientBuilder withPort(int port) {
        this.port = port;
        return this;
    }

    public SocketClientBuilder withListener(TmkClient.TmkClientListener listener) {
        this.listener = listener;
        return this;
    }

    public SocketClient build() {
        SocketClient tcpClient = new SocketClient();
        tcpClient.setHost(host);
        tcpClient.setPort(port);
        tcpClient.setListener(listener);
        return tcpClient;
    }
}
