package co.timekettle.tmkengine.SocketClient;

import co.timekettle.tmkengine.JsonResponse;
import co.timekettle.tmkengine.NettyClient.TmkClient;
import co.timekettle.tmkengine.utils.TmkLogger;
import co.timekettle.tmkengine.TmkPacket;

import java.io.DataInputStream;
import java.io.IOException;
import java.io.OutputStream;
import java.net.InetAddress;
import java.net.Socket;
import java.util.concurrent.LinkedBlockingQueue;

public class SocketClient {

    private LinkedBlockingQueue<byte[]> writeQueue = new LinkedBlockingQueue<>();
    private String host;
    private int port = 7889;
    private Socket socket;
    private TmkClient.TmkClientListener listener;
    private OutputStream outputStream;
    private DataInputStream dataInputStream;

    public void setHost(String host) {
        this.host = host;
    }

    public String getHost() {
        return host;
    }

    public int getPort() {
        return port;
    }

    public void setPort(int port) {
        this.port = port;
    }

    public void setListener(TmkClient.TmkClientListener listener) {
        this.listener = listener;
    }

    public void disconnect() {
        try {
            if (dataInputStream != null) dataInputStream.close();
            if (outputStream != null) outputStream.close();
            if (socket != null) {
                TmkLogger.d("socket: " + socket.isConnected());
                socket.close();
            }
        } catch (IOException e) {
            e.printStackTrace();
        }
    }

    public boolean connect() {
        try {
            TmkLogger.d("connect: " + host + ":" + port);
            InetAddress address = InetAddress.getByName(host);
            socket = new Socket(address, port);
            outputStream = socket.getOutputStream();
            dataInputStream = new DataInputStream(socket.getInputStream());
            listener.onConnected();

            runReadThread();

            return true;
        } catch (IOException e) {
            e.printStackTrace();
        }

        return false;
    }

    public void write(long session, String text) {
        write(new TmkPacket(session, text));
    }

    public void write(long session, byte[] data) {
        write(new TmkPacket(session, data));
    }

    private void write(TmkPacket protocol) {
        try {
            byte[] value = protocol.toByteArray();
//            TmkLogger.d("发送数据: " + Utils.toHexStr(value));
//            TmkLogger.d("发送数据: " + Utils.toHexStr(value, 0, 17, "") + " " + Utils.toHexStr(value, 17, value.length - 17, ""));
            outputStream.write(value);
            outputStream.flush();
        } catch (IOException e) {
            e.printStackTrace();
        }
    }

    private void runReadThread() {
        new Thread(new Runnable() {
            @Override
            public void run() {
                while (true) {
                    TmkPacket protocol = new TmkPacket();
                    if (protocol.read(dataInputStream) == -1) {
                        break;
                    }
                    String text = (String) protocol.get();
                    JsonResponse response = JsonResponse.parse(text);
                    if (protocol.getType() == TmkPacket.BODY_JSON) {
                        listener.onSocketJsonFrame(protocol.getSession(), response);
                    } else if (protocol.getType() == TmkPacket.BODY_SOUND) {
                        listener.onSocketSoundFrame(protocol.getSession(), (byte[]) protocol.get());
                    }
                }

                listener.onFinishedOrDisconnected(null);
                TmkLogger.d("读线程退出");
            }
        }).start();
    }

}
