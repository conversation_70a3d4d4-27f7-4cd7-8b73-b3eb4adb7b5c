/*
 * Copyright 2012 The Netty Project
 *
 * The Netty Project licenses this file to you under the Apache License,
 * version 2.0 (the "License"); you may not use this file except in compliance
 * with the License. You may obtain a copy of the License at:
 *
 *   https://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS, WITHOUT
 * WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied. See the
 * License for the specific language governing permissions and limitations
 * under the License.
 */
package co.timekettle.tmkengine.NettyClient;

import co.timekettle.tmkengine.TmkPacket;
import io.netty.buffer.ByteBuf;
import io.netty.channel.ChannelHandlerContext;
import io.netty.handler.codec.ByteToMessageDecoder;
import io.netty.handler.codec.CorruptedFrameException;

import java.util.List;

public class TmkPacketDecoder extends ByteToMessageDecoder {

    @Override
    protected void decode(ChannelHandlerContext ctx, ByteBuf in, List<Object> out) {
        // 等待包头可用
        if (in.readableBytes() < TmkPacket.headerSize()) {
            return;
        }

        in.markReaderIndex();

        // 检查同步字节(magic number)
        int magicNumber = in.readInt();
        if (magicNumber != TmkPacket.SYN) {
            in.resetReaderIndex();
            throw new CorruptedFrameException("Invalid magic number(syn: " + TmkPacket.SYN + "): " + magicNumber);
        }

        // 消息类型
        byte type = in.readByte();
        // 会话ID
        long session = in.readLong();
        // 二进制pcm标识
        int protocolId = in.readInt();

        // 消息的长度, 检查直到整个数据可用
        int bodyLength = in.readInt();
        if (in.readableBytes() < bodyLength) {
            in.resetReaderIndex();
            return;
        }

        // body数据, 将接收到的数据转换为新的Packet
        byte[] body = new byte[bodyLength];
        in.readBytes(body);

        out.add(new TmkPacket(session, type, body));
    }
}
