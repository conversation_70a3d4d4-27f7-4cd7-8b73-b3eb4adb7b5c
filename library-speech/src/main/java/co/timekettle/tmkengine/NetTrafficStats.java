package co.timekettle.tmkengine;

import android.net.TrafficStats;

import co.timekettle.tmkengine.utils.TmkLogger;

class NetTrafficStats {
   long taskId;
   boolean isStart = false;
   long startTime = 0;
   long initialTxBytes = 0;

   NetTrafficStats(long taskId) {
      this.taskId = taskId;
   }

   void start() {
      this.startTime = System.currentTimeMillis();
      // 在TCP发送之前获取当前的发送字节数
      this.initialTxBytes = TrafficStats.getTotalTxBytes();
   }

   float stop() {
      // 在TCP发送之后获取当前的发送字节数
      long finalTxBytes = TrafficStats.getTotalTxBytes();
      long endTime = System.currentTimeMillis();
      double timeInSeconds = (endTime - this.startTime) / 1000.0;

      // 计算发送速率
      long bytesTransferred = finalTxBytes - initialTxBytes;
      double transferRateMbps = bytesTransferred / 1024.0 / timeInSeconds;

      // 打印发送速率
      float kbps = (float)(transferRateMbps*1000)/1000.f;
      TmkLogger.d("任务[" + this.taskId + "]网络发送速率: " + ((int)(transferRateMbps*1000)/1000.f) + " KBps");
      return kbps;
   }
}
