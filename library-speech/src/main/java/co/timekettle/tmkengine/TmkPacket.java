package co.timekettle.tmkengine;

import io.netty.buffer.ByteBuf;
import io.netty.buffer.Unpooled;

import java.io.*;
import java.nio.charset.StandardCharsets;

/**
 * 旧
 *     SYN | TYPE | SESSION | LENGTH | BODY |
 *      4  |  1   |    8    |   4    | ...  |
 */
public class TmkPacket {
    public static final int SYN = 0x0ffffffe;
    public static final byte BODY_JSON = 0;
    public static final byte BODY_SOUND = 1;

    private int syn = TmkPacket.SYN; // 同步字节, 数据包的起始标记
    private int length; // 4个字节, body 的长度
    private int protocolId; // 4个字节, 为二进制pcm标识
    private long session; // 8个字节, 任务id
    private byte type; // 1个字节, body 数据类型, 包括: BODY_TEXT, BODY_SOUND
    private byte[] body; // 数据

    public static int headerSize() {
        return 17 + 4;
    }

    public TmkPacket() {
    }

    public TmkPacket(long session, byte type, byte[] content) {
        this.session = session;
        this.type = type;
        this.body = content;
        this.length = content.length;
    }

    public TmkPacket(long session, String content) {
        this.session = session;
        this.type = TmkPacket.BODY_JSON;
        this.body = content.getBytes();
//        this.body = Utils.getBase64Encode(content).getBytes(StandardCharsets.UTF_8);
        this.length = body.length;
    }

    public TmkPacket(long session, byte[] content) {
        this.session = session;
        this.type = TmkPacket.BODY_SOUND;
        this.body = content;
//        this.body = Utils.getBase64Encode(content).getBytes();
        this.length = content.length;
    }

    public TmkPacket(long session, byte[] content, int pcmIndex) {
        this.session = session;
        this.type = TmkPacket.BODY_SOUND;
        this.body = content;
//        this.body = Utils.getBase64Encode(content).getBytes();
        this.length = content.length;
        this.protocolId = pcmIndex;
    }

    @Deprecated
    public int read(DataInputStream dins) {
        if (dins == null) {
            return -1;
        }

        try {
            syn = dins.readInt();
            type = dins.readByte();
            session = dins.readLong();
            protocolId = dins.readInt();
            length = dins.readInt();
            body = new byte[length];
            dins.readFully(body);
            return 0;
        } catch (EOFException e) {

        } catch (IOException e) {
//            e.printStackTrace();
        }
        return -1;
    }

    public int getSyn() {
        return syn;
    }

    public int getLength() {
        return this.length;
    }

    public byte getType() {
        return type;
    }

    public long getSession() {
        return session;
    }

    public byte[] getBody() {
        return body;
    }

    public Object get() {
        if (type == TmkPacket.BODY_SOUND) {
            return body;
        } else if (type == TmkPacket.BODY_JSON) {
            return new String(body, StandardCharsets.UTF_8);
        }
        return null;
    }

    public void flushToByteBuf(ByteBuf byteBuf) {
        byteBuf.writeInt(syn);
        byteBuf.writeByte(type);
        byteBuf.writeLong(session);
        byteBuf.writeInt(protocolId);
        byteBuf.writeInt(length);
        byteBuf.writeBytes(body, 0, length);
    }

    public byte[] toByteArray() {
        ByteBuf byteBuf = Unpooled.buffer(17 + length); // 17 = syn + type + session + length
        this.flushToByteBuf(byteBuf);
        byte[] bytes = new byte[byteBuf.readableBytes()];
        byteBuf.readBytes(bytes);
        byteBuf.release();
        return bytes;
    }

//    public byte[] toByteArray() {
//        try {
//            ByteArrayOutputStream baos = new ByteArrayOutputStream();
//            DataOutputStream out = new DataOutputStream(baos);
//            out.writeInt(syn);
//            out.writeByte(type);
//            out.writeLong(session);
//            out.writeInt(length);
//            out.write(body);
//            return baos.toByteArray();
//
//        } catch (IOException e) {
//            e.printStackTrace();
//        }
//
//        return null;
//    }
}

