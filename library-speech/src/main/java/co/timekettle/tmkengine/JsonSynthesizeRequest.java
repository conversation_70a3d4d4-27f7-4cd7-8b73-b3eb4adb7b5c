package co.timekettle.tmkengine;

public class JsonSynthesizeRequest extends JsonRequest {
    private String text;
    private String gender; // 合成声音的性别

    private boolean ttsOpus; // 识别音频是否通过 opus 压缩后传输

    public void setTTSOpus(boolean ttsOpus) {
        this.ttsOpus = ttsOpus;
    }

    public boolean isTTSOpus() {
        return ttsOpus;
    }

    public enum SynthesizeVoiceGender {
        Female,
        Male;
    }

    private Object dstCode; // 兼容旧引擎
    @Override
    public void setSrcCode(Object srcCode) { // 兼容旧引擎
        super.setSrcCode(srcCode);
        this.dstCode = srcCode;
    }

    public void setText(String text) {
        this.text = text;
    }

    public void setGender(String gender) {
        this.gender = gender;
    }
}
