package co.timekettle.tmkengine;

import co.timekettle.tmkengine.NettyClient.TmkClient;

public final class NetSessionContextBuilder {
    private NetSessionContext.ContextListener listener;
    private JsonRequest request;
    private TmkClient tcpClient;

    private NetSessionContextBuilder() {
    }

    public static NetSessionContextBuilder aContext() {
        return new NetSessionContextBuilder();
    }

    public NetSessionContextBuilder withListener(NetSessionContext.ContextListener listener) {
        this.listener = listener;
        return this;
    }

    public NetSessionContextBuilder withRequest(JsonRequest request) {
        this.request = request;
        return this;
    }

    public NetSessionContextBuilder withTcpClient(TmkClient tcpClient) {
        this.tcpClient = tcpClient;
        return this;
    }

    public NetSessionContext build() {
        NetSessionContext context = new NetSessionContext();
        context.setListener(listener);
        context.setRequest(request);
        context.setTcpClient(tcpClient);
        return context;
    }
}
