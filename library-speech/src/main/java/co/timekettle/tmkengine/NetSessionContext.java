package co.timekettle.tmkengine;

import org.concentus.OpusException;

import java.util.Arrays;
import java.util.Random;
import java.util.concurrent.LinkedBlockingQueue;

import co.timekettle.kafka.KafkaClient;
import co.timekettle.kafka.KafkaLogger;
import co.timekettle.kafka.KafkaMsg;
import co.timekettle.opus.OpusCodec;
import co.timekettle.speech.utils.ThreadPoolManager;
import co.timekettle.tmkengine.utils.OpusUtil;
import co.timekettle.speech.utils.RingBuffer;
import co.timekettle.tmkengine.NettyClient.TmkClient;
import co.timekettle.tmkengine.utils.TmkLogger;

public class NetSessionContext {
    private long session;
    private ContextListener listener;
    private JsonRequest request;
    private TmkClient tcpClient;
    private int nBytesWrote;    // 已发送数据字节数
    private int pcmPacketIndex = 0;   // 记录音频序列, 第若干次发送
    private boolean stopped; // 记录是否已被停止
    private boolean cancelled; // 记录是否已取消
    private boolean invalid; // 记录是否已无效
    private KafkaLogger kafkaLogger; // kafka 日志记录

    private LinkedBlockingQueue<byte[]> audioQueue = new LinkedBlockingQueue<>();

    public long getSession() {
        return session;
    }

    public void setSession(long session) {
        assert (session + "").length() <= 13 : "session的值异常, 需满足<=13位";
        int random = new Random().nextInt(9000) + 1000;
        this.session = Long.parseLong(TmkSpeechClient.Prefix + session + random);
        this.request.session = this.session;
        this.tcpClient.setSession(this.session);
    }

    public ContextListener getListener() {
        return listener;
    }

    public void setListener(ContextListener listener) {
        this.listener = listener;
    }

    public JsonRequest getRequest() {
        return request;
    }

    public void setRequest(JsonRequest request) {
        this.request = request;
    }

    public TmkClient getTcpClient() {
        return tcpClient;
    }

    public void setTcpClient(TmkClient tcpClient) {
        this.tcpClient = tcpClient;
    }

    public boolean isStopped() {
        return stopped;
    }

    public boolean isCancelled() {
        return cancelled;
    }

    public boolean isInvalid() {
        return invalid;
    }

    void logE(String desc) {
        TmkLogger.e("任务[" + session + "]" + desc);
    }

    void logD(String desc) {
        TmkLogger.d("任务[" + session + "]" + desc);
    }

    NetTrafficStats netTrafficStats;
    void doWrite(TmkPacket packet, boolean isVoice) {
        boolean isOpus = request instanceof JsonRecognizeRequest && ((JsonRecognizeRequest) request).isOpus();
        if (isVoice) {
            if (netTrafficStats == null) {
                netTrafficStats = new NetTrafficStats(session);
                netTrafficStats.start();
            }
            nBytesWrote = nBytesWrote + packet.getBody().length;
            float sentTime = (float) (isOpus ? nBytesWrote * OpusCompressionRatio / 2.0 / 16000.0 : nBytesWrote / 2.0 / 16000.0);
            if (sentTime % 5 == 0) logD("已发送数据: " + sentTime + "s");
            float kbps = 0.f;
            if (packet.getBody().length == 0) { // 空包退出
                kbps = netTrafficStats.stop();
                logD("发送空包(停止识别), 本次识别数据: " + sentTime + "s(" + nBytesWrote + "bytes) kbps: " + kbps);
            }
            boolean success = tcpClient.write(packet);
            if (success) {
                if (kafkaLogger != null) kafkaLogger.writeLog(new KafkaMsg(packet, request, true, isOpus, kbps, tcpClient.getHost(), tcpClient.getPort()).toJSONString());
            }
        } else {
            boolean success = tcpClient.write(packet);
            if (success) {
                if (kafkaLogger != null) kafkaLogger.writeLog(new KafkaMsg(packet, request, true, isOpus, 0.f, tcpClient.getHost(), tcpClient.getPort()).toJSONString());
            }
        }
    }

    private TmkClient.TmkClientListener tmkClientListener = new TmkClient.TmkClientListener() {
        @Override
        public void onConnected() {
            logD("已经连接服务器 " + tcpClient.getHost() + ":" + tcpClient.getPort());
            listener.onDidConnectServer(NetSessionContext.this, session, tcpClient.getHost() + ":" + tcpClient.getPort());

            // 写请求
            String req = request.toJSONString();
            logD("发送请求[json]: " + req);
            listener.onSendRequest(NetSessionContext.this, session, tcpClient.getHost() + ":" + tcpClient.getPort());
            doWrite(new TmkPacket(session, req), false);
        }

        @Override
        public void onFinishedOrDisconnected(TmkClient.TmkEngineException e) {
            invalid = true;
            logD("主动关闭了连接 " + tcpClient.getHost() + ":" + tcpClient.getPort());

            if (e != null) {
                int code = 0;
                String desc = null;
                if (e.cause != null) {
                    code = JsonResponse.Status.NetworkError.getCode();
                    desc = JsonResponse.Status.NetworkError.getDesc() + "(连接等): " + e.cause.getLocalizedMessage();
                    if (listener != null) {
                        listener.onError(NetSessionContext.this, session, null, code, desc);
                    }
                } else if (e.status != null) {
                    code = e.status.getCode();
                    desc = e.status.getDesc();
                    if (listener != null) {
                        listener.onError(NetSessionContext.this, session, null, code, desc);
                    }
                }
                logE("发生了异常 " + tcpClient.getHost() + ":" + tcpClient.getPort() + " e:" + desc);
                if (kafkaLogger != null) kafkaLogger.writeLog(new KafkaMsg(request, code, desc, tcpClient.getHost(), tcpClient.getPort()).toJSONString());
            }
            // 清理
            clearTmkClientListener();
        }

        public void onSocketReceive(TmkPacket packet) {
            boolean isOpus = request instanceof JsonRecognizeRequest && ((JsonRecognizeRequest) request).isOpus();
            if (kafkaLogger != null) kafkaLogger.writeLog(new KafkaMsg(packet, request, false, isOpus, 0.f, tcpClient.getHost(), tcpClient.getPort()).toJSONString());

            if (packet.getType() == TmkPacket.BODY_JSON) {
                String text = (String) packet.get();
                JsonResponse response = JsonResponse.parse(text);
                this.onSocketJsonFrame(packet.getSession(), response);
            } else if (packet.getType() == TmkPacket.BODY_SOUND) {
                this.onSocketSoundFrame(packet.getSession(), (byte[]) packet.get());
            }
        }

        @Override
        public void onSocketJsonFrame(long session, JsonResponse response) {
            logD("收到响应[json]: " + response.toJSONString());

            if (listener == null) {
                return;
            }
            String engine = null;
            if (request.getType() == JsonRequest.Type.Translate) {
                engine = response.getTranslate() != null ? response.getTranslate().getEngine() : null;
            } else if (request.getType() == JsonRequest.Type.Synthesize) {
                engine = response.getSynthesize() != null ? response.getSynthesize().getEngine() : null;
            } else {
                engine = response.getSpeech() != null ? response.getSpeech().getEngine() : null;
            }
            if (response.getCode() == JsonResponse.Status.Ready.getCode()) {
                listener.onRequestReceiveResp(NetSessionContext.this, session, null);
                if (request.getType() != JsonRequest.Type.Synthesize && request.getType() != JsonRequest.Type.Translate)
                    doReady();
            } else if (response.getCode() == JsonResponse.Status.Completed.getCode()) {
                listener.onCompleted(NetSessionContext.this, session, engine);
                tcpClient.disconnect(null); // 1001 后连接失效, 直接关闭
            } else if (response.getCode() == JsonResponse.Status.RecognizeResult.getCode()) {
                JsonResponse.Speech speech = response.getSpeech();
                listener.onRecognizeResult(NetSessionContext.this, session, speech.getIsLast(), speech.getSrcCode(), speech.getrText(), speech.gettText(), engine);
            } else if (response.getCode() == JsonResponse.Status.TranslateResult.getCode()) {
                JsonResponse.Translate translate = response.getTranslate();
                listener.onTranslateResult(NetSessionContext.this, session, translate.gettText(), engine);
                // FIXME: 2023/4/3 后续根据内部 1001 关闭任务
                tcpClient.disconnect(null);
            } else if (response.getCode() > JsonResponse.Status.Error.getCode()) {
                listener.onError(NetSessionContext.this, session, engine, response.getCode(), response.getMessage());
                if (response.getCode() == JsonResponse.Status.InvalidAuthorization.getCode()) {
                    logD("认证失败");
                }
                tcpClient.disconnect(new TmkClient.TmkEngineException(JsonResponse.Status.get(response.getCode())));
            }
        }

        @Override
        public void onSocketSoundFrame(long session, byte[] data) {
            logD("收到响应[sound]: " + data.length);
            byte[] soundData = data;
            //使用opus传输，需要单独解码
            if (((JsonSynthesizeRequest) request).isTTSOpus()) {
                try {
                    soundData = OpusUtil.decode(soundData, getOpusEncoder());
                } catch (Exception e) {
                    soundData = data;
                }
            }
            if (listener != null) {
                listener.onSynthesizeBuffer(NetSessionContext.this, session, soundData, soundData.length);
            }
        }
    };

    public void start() {
        ThreadPoolManager.getInstance().execute(() -> {
            if (session == 0) setSession(System.currentTimeMillis());
            // kafka 网络日志
            if (KafkaClient.ShouldUploadKafkaLog) {
                kafkaLogger = new KafkaLogger();
                kafkaLogger.start(session+"");
            }
            this.tcpClient.setListener(tmkClientListener);
            logD("将连接服务器 " + this.tcpClient.getHost() + ":" + this.tcpClient.getPort());
            listener.onWillConnectServer(this, session, this.tcpClient.getHost() + ":" + this.tcpClient.getPort());
            boolean ret = this.tcpClient.connect();
        });
    }

    // 在主动断开 / 异常断开，并且执行完回调的逻辑之后，清除监听器
    private void clearTmkClientListener() {
        this.tcpClient.setListener(null);
    }

    public void stop() {
        stopped = true;
        if (this.request.getType().isAsr()) {
            try {
                writeAudioCache(true, new byte[0]);
            } catch (InterruptedException e) {
                throw new RuntimeException(e);
            }
        } else {
            this.tcpClient.disconnect(null);
        }
    }

    public void cancel() {
        cancelled = true;
        this.tcpClient.disconnect(null);
    }

    public void writeAudio(byte[] data) {
        try {
            byte[] buffer = new byte[data.length];
            System.arraycopy(data, 0, buffer, 0, data.length);
//            writeQueue.put(buffer);

            writeAudioCache(false, buffer);
        } catch (InterruptedException e) {
            e.printStackTrace();
        }
    }

    private void doReady() {
        ThreadPoolManager.getInstance().execute(() -> {
            while (true) {
                try {
                    byte[] data = audioQueue.take();
                    pcmPacketIndex++;
                    doWrite(new TmkPacket(session, data, pcmPacketIndex), true);
                    if (data.length == 0) { // 空包退出
                        break;
                    }
                } catch (InterruptedException e) {
                    e.printStackTrace();
                }
            }
//            logD("退出发送数据线程");
        });
    }

    OpusCodec encoder;
    int OpusCompressionRatio = 16;
    OpusCodec getOpusEncoder() throws OpusException {
        if (this.encoder == null) {
            this.encoder = new OpusCodec(16000, 1);
        }
        return this.encoder;
    }
    int bytesIn20ms = 640;
    int encodeSize = 40;
    int nPacketPerSend = 1;
    byte[] decodePacket = new byte[bytesIn20ms];
    byte[] encodePacket = new byte[encodeSize];
    byte[] decodePackets = new byte[bytesIn20ms * nPacketPerSend];
    byte[] encodePackets = new byte[encodeSize * nPacketPerSend];

    RingBuffer cache = new RingBuffer(bytesIn20ms * 10);
    public synchronized void writeAudioCache(boolean end, byte[] data) throws InterruptedException {
        assert request.getType().isAsr(): "类型错误, 类型必须满足 isAsr()";
        if (((JsonRecognizeRequest)request).isOpus()) {
            cache.write(data);

            while (cache.readable() >= bytesIn20ms * nPacketPerSend) {
                cache.read(decodePackets);
                putSendQueue(decodePackets, encodePackets, nPacketPerSend, end);
            }
            if (end) {
                int _count = cache.readable() / bytesIn20ms;
                if (cache.readable() % bytesIn20ms != 0) _count = _count + 1;
                byte[] _decodePackets = new byte[bytesIn20ms * _count];
                cache.read(_decodePackets);
                byte[] _encodePackets = new byte[encodeSize * _count];
                putSendQueue(_decodePackets, _encodePackets, _count, end);
            }
        } else {
            cache.write(data);
            while (cache.readable() >= 3200) {
                byte[] decodePacketsTemp = new byte[3200];
                cache.read(decodePacketsTemp);
                audioQueue.put(decodePacketsTemp);
            }
            if (end) {
                byte[] decodePacketsTemp = new byte[3200];
                cache.read(decodePacketsTemp);
                audioQueue.put(decodePacketsTemp);
                audioQueue.put(new byte[0]); // 结束标记
            }
        }
    }

    public synchronized void putSendQueue(byte[] decodePackets, byte[] encodePackets, int count, boolean end) throws InterruptedException {
        for (int i = 0; i < count; i++) {
            System.arraycopy(decodePackets, i * bytesIn20ms, decodePacket, 0, bytesIn20ms);
            int size = 0;
            try {
                size = getOpusEncoder().encode(decodePacket, bytesIn20ms / 2, encodePacket);
            } catch (OpusException | AssertionError e) {
                logE("opus编码异常: " + e.getLocalizedMessage());
                e.printStackTrace();
            }
            assert size == encodePacket.length: "长度异常, 值必须是: " + encodeSize;
            System.arraycopy(encodePacket, 0, encodePackets, i * encodeSize, encodeSize);
        }
        audioQueue.put(Arrays.copyOf(encodePackets, encodePackets.length));
        // FIXME: 2023/4/7 后续通过停止指令(json请求的形式, 与开始指令对应)
        if (end) audioQueue.put(new byte[0]); // 结束标记
    }

    public interface ContextListener {
        void onRecognizeResult(NetSessionContext context, long session, boolean isLast, String srcCode, String rtext, String ttext, String engine);

        void onTranslateResult(NetSessionContext context, long session, String result, String engine);

        void onSynthesizeBuffer(NetSessionContext context, long session, byte[] output, int outputSize);

        void onCompleted(NetSessionContext context, long session, String engine);

        void onError(NetSessionContext context, long session, String engine, int code, String message);


        default void onWillConnectServer(NetSessionContext context, long session, String engine){} // 将连接服务器
        default void onDidConnectServer(NetSessionContext context, long session, String engine){} // 将连接服务器
        default void onSendRequest(NetSessionContext context, long session, String engine){} // 将发送识别/翻译/合成请求
        default void onRequestReceiveResp(NetSessionContext context, long session, String engine){} // 识别/翻译/合成请求收到了响应，可以发送数据了

//    void onRecognizeResult(Response response);
//
//    void onTranslateResult(Response response);
//
//    void onSynthesizeBuffer(Response response);
//
//    void onCompleted(Response response);
    }
}
