package co.timekettle.tmkengine;

import com.google.gson.Gson;

public class JsonResponse {
    private String $JsonString; // 对应的 json 字符串, 通过 '$' 前缀与网络协议定义字段区分
    private int code;
    private String message;
    private Speech speech; // 识别和语音翻译的响应
    private Translate translate;
    private Synthesize synthesize;

    public JsonResponse() {

    }

    private JsonResponse(Builder builder) {
        this.code = builder.code;
        this.message = builder.error;
        this.speech = builder.speech;
        this.translate = builder.translate;
        this.synthesize = builder.synthesize;
    }

    public void setMessage(String message) {
        this.message = message;
    }

    public String getMessage() {
        return message;
    }

    public void setCode(int code) {
        this.code = code;
    }

    public int getCode() {
        return code;
    }

    public void setSpeech(Speech speech) {
        this.speech = speech;
    }

    public Speech getSpeech() {
        return speech;
    }

    public void setTranslate(Translate translate) {
        this.translate = translate;
    }

    public Translate getTranslate() {
        return translate;
    }

    public Synthesize getSynthesize() {
        return synthesize;
    }

    public String getEngine() {
        String engine = null;
        if (this.getSpeech() != null) {
            engine = this.getSpeech().getEngine();
        } else if (this.getTranslate() != null) {
            engine = this.getTranslate().getEngine();
        } else if (this.getSynthesize() != null) {
            engine = this.getSynthesize().getEngine();
        }
        return engine;
    }

    public String toJSONString() {
        if (this.$JsonString != null) {
            return this.$JsonString;
        }
        return new Gson().toJson(this);
    }

    public static JsonResponse parse(String json) {
        JsonResponse res = new Gson().fromJson(json, JsonResponse.class);
        res.$JsonString = json;
        return res;
//        return new Gson().fromJson(json, JsonResponse.class);
    }

    public static class Item {
        private String engine;

        public Item() {

        }

        public Item(String engine) {
            this.engine = engine;
        }

        public void setEngine(String engine) {
            this.engine = engine;
        }

        public String getEngine() {
            return engine;
        }
    }

    public static class Speech extends Item {
        static class LidOtherText {
            private String dstCode;
            private String tText;
        }
        private boolean isLast;
        private String rText;
        private String tText;
        private String srcCode;
        private LidOtherText[] tTexts;

        public Speech() {

        }

        public Speech(String engine, String text, boolean isLast) {
            super(engine);
            this.rText = text;
            this.isLast = isLast;
        }

        public Speech(String engine, String rtext, String ttext, boolean isLast) {
            super(engine);
            this.rText = rtext;
            this.tText = ttext;
            this.isLast = isLast;
        }

        public void setIsLast(boolean isLast) {
            this.isLast = isLast;
        }

        public boolean getIsLast() {
            return isLast;
        }

        public String gettText() {
            if (tText != null) return tText;
            if (tTexts != null && tTexts.length > 0) return tTexts[0].tText;
            return null;
        }

        public void settText(String tText) {
            this.tText = tText;
        }

        public void setrText(String rText) {
            this.rText = rText;
        }

        public String getrText() {
            return rText;
        }

        public String getSrcCode() {
            return srcCode;
        }
    }

    public static class Translate extends Item {
        private String tText;

        public Translate() {

        }

        public Translate(String engine, String text) {
            super(engine);
            this.tText = text;
        }

        public String gettText() {
            return tText;
        }

        public void settText(String tText) {
            this.tText = tText;
        }
    }

    public static class Synthesize extends Item {
    }

    public static class Builder {
        private int code;
        private String error;
        private Speech speech;
        private Translate translate;
        private Synthesize synthesize;

        public Builder code(Status code) {
            this.code = code.getCode();

            if (code.getCode() > Status.Error.getCode()) {
                this.error = code.getDesc();
            }

            return this;
        }

        public Builder message(String message) {
            this.error = message;
            return this;
        }

        public Builder speech(String name, String text) {
            this.speech = new Speech(name, text, false);
            return this;
        }

        public Builder speech(String name, String text, boolean isLast) {
            this.speech = new Speech(name, text, isLast);
            return this;
        }

        public Builder translate(String name, String text) {
            this.translate = new Translate(name, text);
            return this;
        }

        public JsonResponse build() {
            return new JsonResponse(this);
        }
    }

    public enum Status {

        Ready(1000, "服务准备已就绪"),
        Completed(1001, "任务完成"),
        RecognizeResult(1002, "识别结果"),
        TranslateResult(1003, "翻译结果"),
        SynthesizeResult(1004, "合成结果"),
        Disconnect(1005, "连接断开连接"),

        /*************************
         * 错误码
         * ***********************/
        Error(2000, "错误起始码"),
        InvalidAuthorization(2001, "授权失败"),
        MissingParameters(2002, "缺少参数"),
        InvalidParameters(2003, "无效的参数"),
        UnsupportedTranslationLanguage(2004, "不支持的翻译语言"),
        UnsupportedRecognitionLanguage(2005, "不支持的识别语言"),
        UnsupportedSyntheticLanguage(2006, "不支持的合成语言"),
        Uninitialized(2007, "未初始化"),
        IllegalRequest(2008, "无效的请求"),
        ServiceError(2009, "服务器错误"),
        NoMatch(2010, "无法识别"),
        ThirdMicrosoftConnectError(2011, "Third Microsoft Connect Error."),
        ThirdGoogleConnectError(2012, "Third Google Connect Error."),
        ThirdAmiVoiceConnectError(2013, "Third Ami Voice Connect Error."),
        ThirdError(2014,"Third Return Fail"),
        UnKnowError(3000, "未知错误"),

        ConnectTimeout(4000, "连接服务器超时"),
        NetworkError(4004, "网络异常"),
        ServerResponeTimeout(5012, "服务器响应超时");

        private final int code;
        private final String desc;

        Status(int code, String value) {
            this.code = code;
            this.desc = value;
        }

        public String getDesc() {
            return this.desc;
        }

        public int getCode() {
            return code;
        }

        public static Status get(int code) {
            for (Status s : Status.values()) {
                if (s.code == code) {
                    return s;
                }
            }
            return UnKnowError;
        }
    }
}
