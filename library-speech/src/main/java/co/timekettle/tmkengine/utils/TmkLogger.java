package co.timekettle.tmkengine.utils;

import android.content.Context;
import android.util.Log;

import java.io.File;
import java.io.FileOutputStream;
import java.io.IOException;
import java.io.OutputStream;
import java.text.DateFormat;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Comparator;
import java.util.Date;
import java.util.Locale;

public class TmkLogger {
    private static final String TAG = "tmkengine";
    public static LogCallback mCallback = null; // 根据设置等级进行回调

    public static void setLogCallback(LogCallback mCallback) {
        TmkLogger.mCallback = mCallback;
    }

    public static void d(String msg) {
//        msg = TimeUtil.getCurrentTime() + " " + msg;
//        System.out.println(msg);

        if (filelogger != null) {
            filelogger.writeLog(msg);
        }
        if (mCallback != null) {
            mCallback.invoke(2, TAG, msg);
        }
    }

    public static void e(String msg) {
//        msg = TimeUtil.getCurrentTime() + " " + msg;
//        System.out.println(msg);

        if (filelogger != null) {
            filelogger.writeLog(msg);
        }
        if (mCallback != null) {
            mCallback.invoke(1, TAG, msg);
        }
    }

    static FileLogger filelogger;
    public synchronized static void enableFileLogger(Context context, boolean toExternal) {
        filelogger = new FileLogger(context, toExternal);
        try {
            filelogger.start(toExternal);
        } catch (IOException e) {
            throw new RuntimeException(e);
        }
    }

    public synchronized static void disableFileLogger(Context context) {
        filelogger = null;
    }

    public static class FileLogger {
        private static final String TAG = "tmkengine_log";
        String rootDirPath; // 指定所处根目录
        String dirNameTag = TAG; // 目录名字标识, 目录和文件包含此字符串
        String dirPath; // 日志目录路径
        File logFile = null;
        private final DateFormat dateFormat = new SimpleDateFormat("yyyyMMdd-HHmmss", Locale.getDefault());

        public FileLogger(Context context) {
            this(context, false);
        }

        public FileLogger(Context context, boolean toExternal) {
            // 指定所处根目录
            String logDir = "/Log";
            if (toExternal) logDir = "/userlog";
            String rootDirPath = (toExternal ? context.getExternalCacheDir() : context.getCacheDir()).getAbsolutePath() + logDir;
            this.dirPath = rootDirPath + "/" + this.dirNameTag + "/";
            this.logFile = null;
        }

        private void cleanOldFile(boolean cacheAll) {
            // 清理旧目录
            File f = new File(this.dirPath);
            assert f.isDirectory() : "this.dirPath 路径需要为目录路径";

            ArrayList<File> items = new ArrayList<>();
            File[] subFiles = f.listFiles();
            if (subFiles != null) {
                for (File item : subFiles) {
                    // 清理大小为 0 字节的文件
                    if ((item.isDirectory() && item.length() <= 4096) || (item.isFile() && item.length() == 0)) {
                        item.delete();
                        continue;
                    }
                    if (item.getName().contains(this.dirNameTag)) items.add(item);
                }
            }
            if (items.isEmpty()) return;

            Log.d(TAG, "当前 enginelog 文件夹数量: " + items.size());
            items.sort(Comparator.comparingLong(File::lastModified).reversed());

            Log.d(TAG, "缓存所有文件开关 " + cacheAll);
            if (cacheAll) return;

            int nLeave = 20;
            for (int index = 0; index < items.size(); index++) {
                if (index >= nLeave - 1) {
                    File element = items.get(index);
                    element.delete();
                }
            }
            Log.d(TAG, "清理后 enginelog 文件夹数量" + items.size());
        }

        public void start(boolean cacheAll) throws IOException {
            if (this.logFile != null) {
                Log.e(TAG, "当前存在日志文件");
                return;
            }
            // 创建 enginelog 根目录
            File dirPathFile = new File(this.dirPath);
            boolean isExsitsRootDirPath = dirPathFile.exists();
            if (isExsitsRootDirPath) {
                this.cleanOldFile(cacheAll);
            } else dirPathFile.mkdirs();

            // 创建当前目录
            String curDirPath = this.dirPath + "/";
            new File(curDirPath).mkdirs();

            // 根据每个文件是否达到一定大小去创建日志
            Date date = new Date();
            String dateName = dateFormat.format(date);
            this.logFile = new File(curDirPath + dateName + "-" + this.dirNameTag + ".txt");
            File enginelogFile = this.logFile;
            enginelogFile.createNewFile();
            enginelogFile.setLastModified(date.getTime());
        }

        public void appendFile(File f, String content) {
            try {
                OutputStream outputStream = new FileOutputStream(f, true);
                outputStream.write(content.getBytes());
                outputStream.close();
            } catch (Exception ex) {
                ex.printStackTrace();
            }
        }

//        public void stop() {
//            this.logFile = null;
//        }

        public void writeLog(String contents) {
            if (this.logFile == null) {
                Log.e(TAG, "logFile 为空");
                return;
            }
            appendFile(this.logFile, dateFormat.format(new Date()) + " " + contents + "\n");
        }
    }

    public interface LogCallback {
        void invoke(int level, String tag, String msg);
    }
}
