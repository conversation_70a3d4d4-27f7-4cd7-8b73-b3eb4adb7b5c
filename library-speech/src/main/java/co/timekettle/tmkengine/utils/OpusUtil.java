package co.timekettle.tmkengine.utils;

import android.util.Log;

import org.concentus.OpusException;

import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import co.timekettle.opus.OpusCodec;

public class OpusUtil {
    private static Map<String, OpusCodec> opusCodecMap = new HashMap<>();
    private static int mBytesPerDecodePacket = 40 * 16;
    private static final String TAG = "OpusUtil";
    private static final int mBytesDecodeFrames = 80;

    public static synchronized byte[] decode(byte[] byteArray, OpusCodec opusCodec) {
        byte[] output = new byte[mBytesPerDecodePacket];
        ByteArrayOutputStream outputStream = new ByteArrayOutputStream();
        List<byte[]> outSplitByteArray = splitByteArray(byteArray, mBytesDecodeFrames);
        for (int i = 0; i< outSplitByteArray.size(); i++) {
            byte[] data = outSplitByteArray.get(i);
            try {
                if (opusCodec != null) {
                    opusCodec.decode(data, output, 320);
                }
                outputStream.write(output);
            } catch (OpusException | IOException e) {
                Log.d(TAG,"opus decode 失败：" + e.toString());
            }
        }
        Log.d(TAG,"opus decode 完成");
        return outputStream.toByteArray();
    }

    public static synchronized byte[] decode(byte[] byteArray, String key) {
        byte[] output = new byte[mBytesPerDecodePacket];
        ByteArrayOutputStream outputStream = new ByteArrayOutputStream();
        OpusCodec opusCodec = getOpusCodec(key);
        List<byte[]> outSplitByteArray = splitByteArray(byteArray, mBytesDecodeFrames);
        for (int i = 0; i< outSplitByteArray.size(); i++) {
            byte[] data = outSplitByteArray.get(i);
            try {
                if (opusCodec != null) {
                    opusCodec.decode(data, output, 320);
                }
                outputStream.write(output);
            } catch (OpusException | IOException e) {
                Log.d(TAG,"opus decode 失败：" + e.toString());
            }
        }
        Log.d(TAG,"opus decode 完成");
        return outputStream.toByteArray();
    }


    //使用opus压缩，将音频数据压缩成opus格式
    public static byte[] encode(byte[] byteArray, String key) {
        byte[] output = new byte[mBytesDecodeFrames];
        ByteArrayOutputStream outputStream = new ByteArrayOutputStream();
        OpusCodec opusCodec = getOpusCodec(key);
        List<byte[]> outSplitByteArray = splitByteArray(byteArray, mBytesPerDecodePacket);
        for (int i = 0; i< outSplitByteArray.size(); i++) {
            byte[] data = outSplitByteArray.get(i);
            try {
                if (opusCodec != null) {
                    opusCodec.encode(data, 320, output);
                }
                outputStream.write(output);
            } catch (OpusException | IOException e) {
                Log.d(TAG,"opus encode 失败：" + e.toString());
            }
        }
        Log.d(TAG,"opus encode 完成");
        return outputStream.toByteArray();
    }

    public static List<byte[]> splitByteArray(byte[] byteArray, int length) {
        List<byte[]> result = new ArrayList<>();
        int index = 0;
        while (index < byteArray.length) {
            int endIndex = Math.min(index + length, byteArray.length);
            byte[] subArray = Arrays.copyOfRange(byteArray, index, endIndex);
            if (subArray.length < length) {
                byte[] temp = new byte[length];
                System.arraycopy(subArray, 0, temp, 0, subArray.length);
                subArray = temp;
            }
            result.add(subArray);
            index += length;
        }
        return result;
    }

    private static OpusCodec getOpusCodec(String key) {
        if (opusCodecMap.containsKey(key)) {
            return opusCodecMap.get(key);
        }
        OpusCodec opusCodec = null;
        try {
            opusCodec = new OpusCodec(16000, 1);
        } catch (OpusException e) {
            Log.d(TAG,"opus 初始化失败：" + e.toString());
        }
        opusCodecMap.put(key, opusCodec);
        return opusCodec;
    }
}
