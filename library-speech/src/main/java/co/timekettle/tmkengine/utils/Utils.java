package co.timekettle.tmkengine.utils;

import android.util.Base64;

import java.io.*;
import java.nio.charset.StandardCharsets;
import java.util.Random;

public class Utils {

    public static boolean isFileExists(String fileName) {
        File file = new File(fileName);
        return file.exists() && !file.isDirectory();
    }

    public static boolean deleteFile(String fileName) {
        File file = new File(fileName);
        if (file.exists() && file.isFile()) {
            if (file.delete()) {
                return true;
            } else {
                return false;
            }
        } else {
            return false;
        }
    }

    public static String getRandomString(int length) {
        String str = "abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789";
        Random random = new Random();
        StringBuffer sb = new StringBuffer();
        for (int i = 0; i < length; i++) {
            int number = random.nextInt(62);
            sb.append(str.charAt(number));
        }
        return sb.toString();
    }

    public static byte[] file2ByteArray(String filePath) {

        try {
            InputStream in = new FileInputStream(filePath);

            ByteArrayOutputStream out = new ByteArrayOutputStream();
            byte[] buffer = new byte[1024 * 4];
            int n = 0;
            while ((n = in.read(buffer)) != -1) {
                out.write(buffer, 0, n);
            }

            in.close();

            return out.toByteArray();
        } catch (FileNotFoundException e) {
            e.printStackTrace();
        } catch (IOException e) {
            e.printStackTrace();
        }

        return new byte[0];
    }

    public static String readFileToString(String filePath) {
        try {
            File file = new File(filePath);
            if (file.isFile() && file.exists()) {
                InputStreamReader read = new InputStreamReader(new FileInputStream(file), "UTF-8");
                BufferedReader bufferedReader = new BufferedReader(read);
                String all = bufferedReader.readLine();

                String lineTxt;
                while ((lineTxt = bufferedReader.readLine()) != null) {
                    all = all + lineTxt;
                }

                return all;
            }
        } catch (UnsupportedEncodingException | FileNotFoundException e) {
            TmkLogger.d("Cannot find the file specified!");
            e.printStackTrace();
        } catch (IOException e) {
            TmkLogger.d("Error reading file content!");
            e.printStackTrace();
        }
        return null;
    }

    public static String readFileToString(InputStream inputStream) {
        try {
            InputStreamReader read = new InputStreamReader(inputStream, "UTF-8");
            BufferedReader bufferedReader = new BufferedReader(read);
            String all = bufferedReader.readLine();

            String lineTxt;
            while ((lineTxt = bufferedReader.readLine()) != null) {
                all = all + lineTxt;
            }
            return all;
        } catch (UnsupportedEncodingException | FileNotFoundException e) {
            TmkLogger.d("Cannot find the file specified!");
            e.printStackTrace();
        } catch (IOException e) {
            TmkLogger.d("Error reading file content!");
            e.printStackTrace();
        }
        return null;
    }

    public static void copyFile(InputStream in, String targetPath) {
        File targetFile = new File(targetPath);
        FileOutputStream out = null;
        try {
            out = new FileOutputStream(targetFile);
            byte[] buf = new byte[8 * 1024];
            int len = 0;
            while ((len = in.read(buf)) != -1) {
                out.write(buf, 0, len);
                out.flush();
            }
        } catch (Exception e) {
            e.printStackTrace();
        } finally {
            try {
                if (out != null) {
                    out.close();
                }
            } catch (Exception e) {
                TmkLogger.d("关闭输出流错误！");
            }
        }
    }

    public static void saveFile(String filepath, byte[] data) throws Exception {
        if (data != null) {
            File file = new File(filepath);
            if (file.exists()) {
                file.delete();
            }
            FileOutputStream fos = new FileOutputStream(file);
            fos.write(data, 0, data.length);
            fos.flush();
            fos.close();
        }
    }

    public static void saveFile(String filepath, String text) {
        File file = new File(filepath);
        FileOutputStream outputStream;
        try {
            outputStream = new FileOutputStream(file);
            outputStream.write(text.getBytes());
            outputStream.close();
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    //////////////////////////////////////////////////////////////////////
    public static String toHexStr(byte[] data) {
        return toHexStr(data, null);
    }

    public static String toHexStr(byte[] data, String separator) {
        return toHexStr(data, 0, data.length, separator);
    }

    public static String toHexStr(byte[] data, int offset, int len, String separator) {
        len = Math.min(data.length - offset, len);
        StringBuilder stringBuilder = new StringBuilder();
        for (int i = offset; i < offset + len; i++) {
            byte singleByte = data[i];
            if (stringBuilder.length() > 0 && separator != null) stringBuilder.append(separator);
            stringBuilder.append(Integer.toString((singleByte & 0xff) + 0x100, 16).substring(1).toUpperCase());
        }

        return stringBuilder.toString();
    }

    public static String getBase64Encode(byte[] bt) {
        if (bt == null || bt.length == 0) {
            return "";
        }
        return Base64.encodeToString(bt, Base64.DEFAULT);
    }

    public static String getBase64Encode(String str) {
        if (str == null || "".equals(str)) {
            return "";
        }
        byte[] bt = str.getBytes(StandardCharsets.UTF_8);
        str = Base64.encodeToString(bt, Base64.DEFAULT);
        return str;
    }

    public static String getBase64Decode(String str) {
        if (str == null || "".equals(str)) {
            return "";
        }
        byte[] bt = Base64.decode(str, Base64.DEFAULT);
        str = new String(bt, StandardCharsets.UTF_8);
        return str;
    }

}
