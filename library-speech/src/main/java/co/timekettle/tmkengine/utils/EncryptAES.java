package co.timekettle.tmkengine.utils;

import javax.crypto.Cipher;
import javax.crypto.spec.SecretKeySpec;
import java.nio.charset.StandardCharsets;
import java.util.Objects;

public class EncryptAES {
    // 加密
    public static String Encrypt(String sSrc, String sKey) throws Exception {
        // 创建一个空的length位字节数组（默认值为0）
        byte[] raw = new byte[16];
        byte[] arrBTmp = sKey.getBytes(StandardCharsets.UTF_8);
        // 将原始字节数组转换为8位
        for (int i = 0; i < arrBTmp.length && i < raw.length; i++) {
            raw[i] = arrBTmp[i];
        }

        SecretKeySpec skeySpec = new SecretKeySpec(raw, "AES");
        Cipher cipher = Cipher.getInstance("AES"); //"算法/模式/补码方式"
        cipher.init(Cipher.ENCRYPT_MODE, skeySpec);
        byte[] encrypted = cipher.doFinal(sSrc.getBytes(StandardCharsets.UTF_8));
//        return Arrays.toString(Base64.encode(encrypted, Base64.DEFAULT)); // 此处使用BASE64做转码功能，同时能起到2次加密的作用。
        return parseByte2HexStr(encrypted); // 此处使用BASE64做转码功能，同时能起到2次加密的作用。
    }

    // 解密
    public static String Decrypt(String sSrc, String sKey) throws Exception {
        try {
            // 创建一个空的length位字节数组（默认值为0）
            byte[] raw = new byte[16];
            byte[] arrBTmp = sKey.getBytes(StandardCharsets.UTF_8);
            // 将原始字节数组转换为8位
            for (int i = 0; i < arrBTmp.length && i < raw.length; i++) {
                raw[i] = arrBTmp[i];
            }

            SecretKeySpec skeySpec = new SecretKeySpec(raw, "AES");
            Cipher cipher = Cipher.getInstance("AES");
            cipher.init(Cipher.DECRYPT_MODE, skeySpec);
//            byte[] encrypted1 = Base64.decode(sSrc, Base64Dialect.STANDARD); // 先用base64解密
            try {
                byte[] original = cipher.doFinal(Objects.requireNonNull(parseHexStr2Byte(sSrc)));
                return new String(original, StandardCharsets.UTF_8);
            } catch (Exception e) {
                System.out.println(e.toString());
                return null;
            }
        } catch (Exception ex) {
            System.out.println(ex.toString());
            return null;
        }
    }

    public static void main(String[] args) throws Exception {
        String msg = "9A5C1E41066458D50F91636A111FED89." + System.currentTimeMillis();
        String cKey = "woshichuanqi";
        // 需要加密的字串
        System.out.println("原始字串是：" + msg);
        // 加密
        String enString = EncryptAES.Encrypt(msg, cKey);
        System.out.println("加密后的字串是：" + enString);

        // 解密
        String DeString = EncryptAES.Decrypt(enString, cKey);
        System.out.println("解密后的字串是：" + DeString);
    }

    public static byte[] parseHexStr2Byte(String hexStr) {
        if (hexStr.length() < 1)
            return null;
        byte[] result = new byte[hexStr.length() / 2];
        for (int i = 0; i < hexStr.length() / 2; i++) {
            int high = Integer.parseInt(hexStr.substring(i * 2, i * 2 + 1), 16);
            int low = Integer.parseInt(hexStr.substring(i * 2 + 1, i * 2 + 2),
                    16);
            result[i] = (byte) (high * 16 + low);
        }
        return result;
    }

    public static String parseByte2HexStr(byte[] buf) {
        StringBuilder sb = new StringBuilder();
        for (byte b : buf) {
            String hex = Integer.toHexString(b & 0xFF);
            if (hex.length() == 1) {
                hex = '0' + hex;
            }
            sb.append(hex.toUpperCase());
        }
        return sb.toString();
    }
}