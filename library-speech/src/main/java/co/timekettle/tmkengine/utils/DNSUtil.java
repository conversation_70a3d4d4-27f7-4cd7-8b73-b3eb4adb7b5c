package co.timekettle.tmkengine.utils;

import co.timekettle.speech.EngineHost;
import io.netty.bootstrap.Bootstrap;
import io.netty.buffer.ByteBufUtil;
import io.netty.channel.*;
import io.netty.channel.nio.NioEventLoopGroup;
import io.netty.channel.socket.nio.NioDatagramChannel;
import io.netty.channel.socket.nio.NioSocketChannel;
import io.netty.util.NetUtil;
import io.netty.handler.codec.dns.*;

import java.net.InetSocketAddress;
import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.TimeUnit;

public class DNSUtil {
    static final String DOMAIN = "forward.wttwo.com"; // 需要解析的域名
    static final String ALI_DNS_SERVER = "*********";
    static final String GOOGLE_DNS_SERVER = "*******"; // 国内有时候会失效
    static final int DNS_PORT = 53; // dns 服务器端口

    final private ArrayList<String> ips = new ArrayList<>();

    public ArrayList<String> startDns(String dnsServer, int dnsPort, String queryDomain) {
        InetSocketAddress addr = new InetSocketAddress(dnsServer, dnsPort);
        EventLoopGroup group = new NioEventLoopGroup();
        try {
            Bootstrap b = new Bootstrap();
            b.group(group)
                    .channel(NioDatagramChannel.class)
                    .handler(new ChannelInitializer<Channel>() {
                        @Override
                        protected void initChannel(Channel channel) throws Exception {
                            ChannelPipeline p = channel.pipeline();
                            p.addLast(new DatagramDnsQueryEncoder())
                                    .addLast(new DatagramDnsResponseDecoder())
                                    .addLast(new UdpChannelInboundHandler());
                        }
                    });

            final Channel ch = b.bind(0).sync().channel();

            int randomID = (int) (System.currentTimeMillis() / 1000);
            DnsQuery query = new DatagramDnsQuery(null, addr, randomID).setRecord(DnsSection.QUESTION, new DefaultDnsQuestion(queryDomain, DnsRecordType.A));
            ch.writeAndFlush(query).sync();
            boolean result = ch.closeFuture().await(10, TimeUnit.SECONDS);
            if (!result) {
                System.out.println("DNS查询失败");
                ch.close().sync();
            }
        } catch (Exception e) {
            TmkLogger.d("连接等异常(ConnectTimeoutException | IOException): " + e.getLocalizedMessage());
            e.printStackTrace();
        } finally {
            group.shutdownGracefully();
        }

        return ips;
    }

    class UdpChannelInboundHandler extends SimpleChannelInboundHandler<DatagramDnsResponse> {
        @Override
        protected void channelRead0(ChannelHandlerContext ctx, DatagramDnsResponse msg) {
            try {
                readMsg(msg);
            } finally {
                ctx.close();
            }
        }

        private void readMsg(DatagramDnsResponse msg) {
            if (msg.count(DnsSection.QUESTION) > 0) {
                DnsQuestion question = msg.recordAt(DnsSection.QUESTION, 0);
                System.out.println("question is :" + question);
            }
            int i = 0, count = msg.count(DnsSection.ANSWER);
            System.out.println("解析到 ip 数量: " + count);
            while (i < count) {
                DnsRecord record = msg.recordAt(DnsSection.ANSWER, i);
                if (record.type() == DnsRecordType.A) {
                    // A记录用来指定主机名或者域名对应的IP地址
                    DnsRawRecord raw = (DnsRawRecord) record;
                    String ip = NetUtil.bytesToIpAddress(ByteBufUtil.getBytes(raw.content()));
                    ips.add(ip);
                    System.out.println("ip address is: " + ip);
                }
                i++;
            }
        }
    }

    public static List<String> fetch() {
        List<String> ips = new DNSUtil().startDns(ALI_DNS_SERVER, DNS_PORT, DNSUtil.DOMAIN);
        System.out.println("ip address is: " + ips);
        testDelay(ips);
        return ips;
    }

    public static void testConnectCost(List<EngineHost> hosts) {
        EventLoopGroup group = new NioEventLoopGroup();
        Bootstrap b = new Bootstrap();
        b.option(ChannelOption.CONNECT_TIMEOUT_MILLIS, 2000); // 设置超时时间
        b.group(group)
                .channel(NioSocketChannel.class)
                .handler(new ChannelInitializer<Channel>() {
                    @Override
                    protected void initChannel(Channel channel) throws Exception {
                    }
                });
        for (EngineHost host : hosts) {
            long startTime = System.currentTimeMillis();
            String dnsServer = host.ip;
            int dnsPort = host.port;
            try {
                ChannelFuture f = b.connect(dnsServer, dnsPort).sync();
                f.channel().close().sync();
            } catch (Exception e) {
                TmkLogger.d("连接等异常(ConnectTimeoutException | IOException): " + e.getLocalizedMessage());
                e.printStackTrace();
            } finally {
                long costTime = System.currentTimeMillis() - startTime;
                host.connect_cost = costTime;
                TmkLogger.d(dnsServer + " 连接消耗时间时间: " + costTime);
            }
        }
        group.shutdownGracefully();
    }

    public static void testDelay(List<String> ips) {
        EventLoopGroup group = new NioEventLoopGroup();
        Bootstrap b = new Bootstrap();
        b.option(ChannelOption.CONNECT_TIMEOUT_MILLIS, 2000); // 设置超时时间
        b.group(group)
                .channel(NioSocketChannel.class)
                .handler(new ChannelInitializer<Channel>() {
                    @Override
                    protected void initChannel(Channel channel) throws Exception {
                    }
                });
        for (String ip : ips) {
            long startTime = System.currentTimeMillis();
            String dnsServer = ip;
            int dnsPort = 7889;
            try {
                ChannelFuture f = b.connect(dnsServer, dnsPort).sync();
                f.channel().close().sync();
            } catch (Exception e) {
                TmkLogger.d("连接等异常(ConnectTimeoutException | IOException): " + e.getLocalizedMessage());
                e.printStackTrace();
            } finally {
                long endTime = System.currentTimeMillis();
                TmkLogger.d(dnsServer + " 连接消耗时间时间: " + (endTime - startTime));
            }
        }
        group.shutdownGracefully();
    }

    public static void main(String[] args) throws Exception {
        List<String> ips = new DNSUtil().startDns(ALI_DNS_SERVER, DNS_PORT, DNSUtil.DOMAIN);
        System.out.println("ip address is: " + ips);
        testDelay(ips);
    }

}