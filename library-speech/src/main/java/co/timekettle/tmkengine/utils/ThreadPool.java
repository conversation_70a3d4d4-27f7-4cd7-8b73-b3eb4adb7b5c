package co.timekettle.tmkengine.utils;

import java.util.concurrent.*;

public class ThreadPool {

    private static final int CORE_SIZE = 12;

    private static final int MAX_SIZE = 24;

    private static final long KEEP_ALIVE_TIME = 30;

    private static final int QUEUE_SIZE = 50000;

    private static ThreadPoolExecutor THREAD_POOL = new ThreadPoolExecutor(CORE_SIZE, MAX_SIZE, KEEP_ALIVE_TIME,
            TimeUnit.SECONDS, new LinkedBlockingQueue<Runnable>(QUEUE_SIZE), new ThreadPoolExecutor.AbortPolicy());


    private static ThreadPoolExecutor CACHE_POOL  = new ThreadPoolExecutor(CORE_SIZE, Integer.MAX_VALUE,
                                      120L,TimeUnit.SECONDS,
                                      new SynchronousQueue<Runnable>(), new ThreadPoolExecutor.AbortPolicy());

    public static Future<?> submit(Runnable task) {
        return THREAD_POOL.submit(task);
    }

    public static void main(String[] args) {

        for(int i=0; i<10; i++) {
            ThreadPool.submit(new Runnable() {
                @Override
                public void run() {
                    try {
                        System.out.println(Thread.currentThread()+" start");
                        Thread.sleep(10000);
                        System.out.println(Thread.currentThread()+" end");
                    } catch (InterruptedException e) {
                        e.printStackTrace();
                    }
                }
            });
        }
    }
}