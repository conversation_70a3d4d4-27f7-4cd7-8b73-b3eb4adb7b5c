package co.timekettle.tmkengine.utils;

import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.Locale;
import java.util.TimeZone;

public class TimeUtil {
    static SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss SSS", Locale.getDefault());
    static SimpleDateFormat shortSdf = new SimpleDateFormat("HH:mm:ss", Locale.getDefault());
    public static String getCurrentTime() {
        Date d = new Date();
        // sdf.setTimeZone(TimeZone.getTimeZone("Asia/Shanghai")); // 设置北京时区
        return sdf.format(d);
    }


    public static String getShortCurrentTime() {
        Date d = new Date();
        // shortSdf.setTimeZone(TimeZone.getTimeZone("Asia/Shanghai")); // 设置北京时区
        return shortSdf.format(d);
    }

}
