package co.timekettle.tmkengine;

import co.timekettle.tmkengine.utils.TmkLogger;

import com.google.gson.Gson;

import java.util.Random;


public class JsonRequest {
    public long session;
    private String subscriptionKey;
    private Type type;
    private String encoding = "utf-8";
    private final String clientType = TmkSpeechClient.ClientType;
    private Object srcCode;
    private String reqEngine;
    private String sn;

    public String getSubscriptionKey() {
        return subscriptionKey;
    }

    public void setSubscriptionKey(String subscriptionKey) {
        this.subscriptionKey = subscriptionKey;
    }

    public Type getType() {
        return type;
    }

    public void setType(Type type) {
        this.type = type;
    }

    public String getEncoding() {
        return encoding;
    }

    public void setEncoding(String encoding) {
        this.encoding = encoding;
    }

    public Object getSrcCode() {
        return srcCode;
    }

    public void setSrcCode(Object srcCode) {
        this.srcCode = srcCode;
    }

    public void setSn(String sn) {
        this.sn = sn;
    }

    public String getSn() {
        return sn;
    }

    public String getReqEngine() {
        return reqEngine;
    }

    public void setReqEngine(String reqEngine) {
        TmkLogger.d("使用指定引擎: " + reqEngine);
        this.reqEngine = reqEngine;
    }

    public JsonRequest() {
        int random = new Random().nextInt(10000);
        this.session = Long.parseLong(TmkSpeechClient.Prefix + System.currentTimeMillis() + random);
    }

    public String toJSONString() {
        return new Gson().toJson(this);
    }

    public enum Type {
        Recognize("Recognize"),
        Translate("Translate"),
        Synthesize("Synthesize"),
        SpeechTranslation("SpeechTranslation"),
        TwoStepSpeechTranslation("TwoStepSpeechTranslation"),
        MeetingSpeechTranslation("MeetingSpeechTranslation"),
        MultiSpeechTranslation("MultiSpeechTranslation");

        private String code;

        Type(String code) {
            this.code = code;
        }

        public String getCode() {
            return code;
        }

        public boolean isMutilSrcLang() {
            return this == MultiSpeechTranslation;
        }

        public boolean isMutilDstLang() {
            return this == MeetingSpeechTranslation || this == MultiSpeechTranslation;
        }

        public boolean isDoubleLang() {
            return this == Translate || this == SpeechTranslation || this == MeetingSpeechTranslation || this == MultiSpeechTranslation || this == TwoStepSpeechTranslation;
        }

        public boolean isAsr() {
            return this == Recognize || this == SpeechTranslation || this == MeetingSpeechTranslation || this == MultiSpeechTranslation || this == TwoStepSpeechTranslation;
        }

        public static Type get(String code) {
            for (Type s : Type.values()) {
                if (s.code.equals(code)) {
                    return s;
                }
            }
            return null;
        }
    }

    public enum Engine {
        Microsoft("microsoft"),
        Google("google"),
        Iflytek("iflytek"),
        Amivoice("amivoice"),
        Newtranx("newtranx"),

        Default("");

        public String name;
        Engine(String name) {
            this.name = name;
        }
    }
}
