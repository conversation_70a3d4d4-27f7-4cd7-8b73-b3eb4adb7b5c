package co.timekettle.kafka;

import android.util.Log;

import java.io.BufferedReader;
import java.io.File;
import java.io.FileOutputStream;
import java.io.FileReader;
import java.io.IOException;
import java.io.OutputStream;
import java.text.DateFormat;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Comparator;
import java.util.Date;
import java.util.Locale;
import java.util.Objects;
import java.util.function.Function;

import co.timekettle.speech.utils.AiSpeechLogUtil;

public class KafkaLogger {
    private static final String TAG = "KafkaLogger";
    private static final String dirNameTag = "KafkaLogFiles"; // 目录名字标识, 目录和文件包含此字符串
    private static String RootDirPath; // 指定所处根目录
    private static String LogDirPath; // 日志目录路径
    File logFile = null;
    private final DateFormat dateFormat = new SimpleDateFormat("yyyyMMdd-HHmmss", Locale.getDefault());

    public KafkaLogger() {
        assert RootDirPath != null : "需要初始化设定日志根路径 RootDirPath";
    }

    public static void setRootDirPath(String rootDirPath) {
        RootDirPath = rootDirPath;
        LogDirPath = RootDirPath + "/" + dirNameTag + "/";
    }

    public static class ReadTask {
        private boolean cancelled = false;
        public void start(Function<String, Boolean> callback) {
            new Thread(() -> {
                if (LogDirPath == null) return;
                File f = new File(LogDirPath);
                if (!f.exists()) return;

                File[] subFiles = f.listFiles();
                if (subFiles == null) return;
                for (File file : Objects.requireNonNull(subFiles)) {
                    readKafkaMsg(file, callback);
                    AiSpeechLogUtil.d(TAG, "start: 读取 kafka 日志文件 : " + file.getAbsolutePath());
                    file.delete();
                    if (cancelled) break;
                }
            }).start();
        }

        public void stop() {
            cancelled = true;
        }
    }

    public static synchronized void readKafkaMsg(Function<String, Boolean> callback) {
        File f = new File(LogDirPath);
        if (!f.exists()) return;

        File[] subFiles = f.listFiles();
        if (subFiles == null) return;
        for (File file : Objects.requireNonNull(subFiles)) {
            readKafkaMsg(file, callback);
            file.delete();
        }
    }

    public static void readKafkaMsg(File file, Function<String, Boolean> callback) {
        try {
            BufferedReader reader = new BufferedReader(new FileReader(file));
            String msg;
            while ((msg = reader.readLine()) != null) {
                if (callback != null) callback.apply(msg);
            }
            reader.close();
        } catch (IOException e) {
            e.printStackTrace();
        }
    }

    private void cleanOldFile() {
        // 清理旧目录
        File f = new File(LogDirPath);
        assert f.isDirectory() : "this.dirPath 路径需要为目录路径";

        ArrayList<File> items = new ArrayList<>();
        File[] subFiles = f.listFiles();
        if (subFiles != null) {
            for (File item : subFiles) {
                // 清理大小为 0 字节的文件
                if ((item.isDirectory() && item.length() <= 4096) || (item.isFile() && item.length() == 0)) {
                    item.delete();
                    continue;
                }
                if (item.getName().contains(this.dirNameTag)) items.add(item);
            }
        }
        if (items.size() == 0) return;

        Log.d(TAG, "当前 KafkaLogFiles 文件夹数量: " + items.size());
        items.sort(Comparator.comparingLong(File::lastModified).reversed());

        int nLeave = 20;
        for (int index = 0; index < items.size(); index++) {
            if (index >= nLeave - 1) {
                File element = items.get(index);
                element.delete();
            }
        }
        Log.d(TAG, "清理后 KafkaLogFiles 文件夹数量" + items.size());
    }

    // 一次请求一个文件
    public void start(String trackId) {
        if (this.logFile != null) {
            Log.e(TAG, "当前存在日志文件");
            return;
        }
        // 创建 KafkaLogFiles 根目录
        boolean ret;
        File dirPathFile = new File(LogDirPath);
        if (!dirPathFile.exists()) ret = dirPathFile.mkdirs();

        // 创建当前目录
        String curDirPath = LogDirPath + "/";
        Date date = new Date();
        String dateName = dateFormat.format(date);
        this.logFile = new File(curDirPath + dateName + "-" + trackId + ".txt");
        File KafkaLogFilesFile = this.logFile;
        try {
            ret = KafkaLogFilesFile.createNewFile();
        } catch (IOException e) {
            e.printStackTrace();
        }
        ret =KafkaLogFilesFile.setLastModified(date.getTime());
    }

    public void stop() {
        this.logFile = null;
    }

    public void writeLog(String contents) {
        if (this.logFile == null) {
            Log.e(TAG, "logFile 为空");
            return;
        }
        appendFile(this.logFile, contents + "\n");
    }

    private void appendFile(File f, String content) {
        try {
            OutputStream outputStream = new FileOutputStream(f, true);
            outputStream.write(content.getBytes());
            outputStream.close();
        } catch (Exception ex) {
            ex.printStackTrace();
        }
    }
}
