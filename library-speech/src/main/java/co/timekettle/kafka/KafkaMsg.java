package co.timekettle.kafka;

import com.google.gson.Gson;

import co.timekettle.tmkengine.JsonRequest;
import co.timekettle.tmkengine.JsonResponse;
import co.timekettle.tmkengine.TmkPacket;
import co.timekettle.tmkengine.TmkSpeechClient;
import co.timekettle.tmkengine.utils.MD5;

public class KafkaMsg {
    /* 通用字段  */
    String ip = "";  // IP
    String port = "";  // 端口

    long   spanId = 0;                                      // 此处为随机递增值, 分层的标记
    String parentSpanId = "";                               // 客户端不需要填
    String clientType = TmkSpeechClient.ClientType;         // 客户端类型
    String clientVersion = TmkSpeechClient.ClientVersion;   // 客户端版本
    String clientUnionKey = TmkSpeechClient.ClientUnionKey; // 客户端唯一key(mac地址/用户ID等)

    long   traceId = 0;                     // 保证1个月内唯一, timestamp+random -> 10(prefix)+timestamp+random (渠道标识)
    String context = "";                    // 消息体(body)的摘要; 如pcm的uuid值

    /* 请求字段  */
    String type = "";                       // 请求类型
    long reqTime = 0;                       // 发送请求的时间

    /* 响应字段  */
    int stateCode = 0;                      // 如果是请求，填0，如果是返回值，填返回的code码
    String stateMsg = "";                   // 如果是请求，不填，如果是返回值，填返回的message
    String engine = "";                     // 返回值可以带上引擎，请求无需填
    long respTime = 0;                      // 收到引擎响应的时间

    /* 语音指标 */
    float kbps = 0.f;                       // 识别数据发送速度
    boolean opus = false;                   // 是否开启 opus

    /* kafka 消息字段 */
    long sendTime = 0; // 当前此条 kafka 消息发送

    public void setReqTime(long reqTime) {
        this.reqTime = reqTime;
        this.spanId = reqTime;
    }

    public void setRespTime(long respTime) {
        this.respTime = respTime;
        this.spanId = respTime;
    }

    public KafkaMsg(TmkPacket packet, JsonRequest request, boolean isReq, boolean opus, float kbps, String ip, int port) {
        /* 通用字段  */
        this.ip = ip;
        this.port = port + "";

        this.traceId = request.session;
        this.context = MD5.calculateMD5(packet.getBody());
        this.type = request.getType().getCode();
        this.opus = opus;
        this.kbps = kbps;

        /* 请求字段  */
        if (isReq) {
            this.setReqTime(System.currentTimeMillis());
        } else {
            if (packet.getType() == TmkPacket.BODY_JSON) {
                String text = (String) packet.get();
                JsonResponse response = JsonResponse.parse(text);
                this.engine = response.getEngine();
                this.setRespTime(System.currentTimeMillis());
                this.stateCode = response.getCode();
                this.stateMsg = response.getMessage();
            }
        }
    }

    public KafkaMsg(JsonRequest request, int code, String desc, String ip, int port) {
        /* 通用字段  */
        this.ip = ip;
        this.port = port + "";

        this.traceId = request.session;
        this.context = "";
        this.type = request.getType().getCode();

        this.stateCode = code;
        this.stateMsg = desc;
    }

    public String toJSONString() {
        return new Gson().toJson(this);
    }
}
