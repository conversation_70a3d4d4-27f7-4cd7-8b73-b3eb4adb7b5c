package com.hian.jni;

public class AudioProJni {

    public final int RTMSG_OK       =  1;
    public final int RTMSG_KO       = -1;

    public final int VAD_STATE_SIL  =  0;
    public final int VAD_STATE_ON   =  1;
    public final int VAD_STATE_END  =  2;

    public final int ORDINATE       =  0;
    public final int ABSCISSA       =  1;
    public final int FULLAXIS       =  2;

    public int   m_iLVadFlag;
    public int   m_iRVadFlag;
    public float m_fLEngValue;
    public float m_fREngValue;

    private static AudioProJni sAudioProJni = null;

    private AudioProJni() {

    }

    public static AudioProJni getInstance() {
        if (sAudioProJni == null) {
            synchronized (AudioProJni.class) {
                if (sAudioProJni == null) {
                    sAudioProJni = new AudioProJni();
                }
            }
        }
        return sAudioProJni;
    }

    static {
        System.loadLibrary("audioSep");
    }

    /* ******************************************************************** */
    /* FUN                                                                  */
    /*    初始化                                                                                                                                                                                                                         */
    /* IN:                                                                  */
    /*    strAppId     AppId,由hian提供                                                                                                                                        */
    /* OUT:                                                                 */
    /*    NULL                                                              */
    /* RTN:                                                                 */
    /*    RTMSG_KO -- 不成功                                                                                                                                                                               */
    /*    RTMSG_OK -- 成功
    /* ******************************************************************* */
    public native int InitHianSep(String p_strAppId);
    /* ******************************************************************** */
    /* FUN                                                                  */
    /*    设置参数                                                                                                                                                                                                                     */
    /* IN:                                                                  */
    /*    p_iFrontNum  从sil到on状态需要的帧数     默认10                              */
    /*    p_iEndNum    从on到end状态需要的帧数     默认20                              */
    /*    p_fThres     声道阈值（灵敏度）  1-10  默认为5，值越小越灵敏                                                                             */
    /*    p_iAngleLoc  选择分离音频角度（ABSCISSA - 0/180 ORDINATE - 90/270）                    */
    /*                                   FULLAXIS - 0/90/180/270            */
    /*    p_iBwParam   波束宽度控制参数范围2-20    默认20                              */
    /*    p_iNrFlag    是否进行单通道降噪 1 - 是 0 否                                                                                                                  */
    /*    p_iGain      增益因子范围1-8             默认1                                   */
    /* OUT:                                                                 */
    /* 	  NULL                                                              */
    /* RTN:                                                                 */
    /*    RTMSG_KO -- 不成功                                                                                                                                                                               */
    /*    RTMSG_OK -- 成功                                                                                                                                                                                    */
    /* ******************************************************************** */
//    public native int SetHianSep(int p_iFrontNum, int p_iEndNum, float p_fThres, int p_iAngleLoc, int p_iBwParam, int p_iNrFlag, int p_iGain);
    public native int SetHianSep(int p_iFrontNum, float p_fFrontThs, int p_iEndNum, float p_fEndThs, int p_iAngleLoc, int p_iBwParam, int p_iNrFlag, int p_iGain);

    /************************************************************************/
    /* FUN                                                                  */
    /*    设置参数                                                          */
    /* IN:                                                                  */
    /*    p_iFrontNum  从sil到on状态需要的帧数     默认10                   */
    /*    p_fFrontThs  从sil到on状态灵敏度阈值（1~10）默认为5，越小越灵敏   */
    /*    p_iEndNum    从on到end状态需要的帧数     默认20                   */
    /*    p_fEndThs    从on到end状态灵敏度阈值（1~10）默认为5，越小越灵敏   */
    /*    p_iAngleLoc  选择分离音频角度（ABSCISSA - 0/180 ORDINATE - 90/270）*/
    /*                                   FULLAXIS - 0/90/180/270            */
    /*    p_iBwParam   波束宽度控制参数范围2-20    默认20                   */
    /*    p_iNrFlag    是否进行单通道降噪 1 - 是 0 否                       */
    /*    p_iGain      增益因子范围1-8             默认1                    */
    /* OUT:                                                                 */
    /*    NULL                                                              */
    /* RTN:                                                                 */
    /*    -1 -- 不成功                                                      */
    /*     1 -- 成功                                                        */
    /************************************************************************/
    /* ******************************************************************** */
    /* FUN                                                                  */
    /*    处理一帧数据的分离降噪、VAD状态                                                                         			                */
    /* IN:                                                                  */
    /*    p_nWavData   需要处理的音频数据(暂时固定长度为4*256short)   			        */
    /* OUT:                                                                 */
    /*    p_nTgtData   降噪之后的数据，固定长度（4*256），输出长度跟p_iAngleLoc参数有关                     */
    /*    p_nVadFlag   声道Vad标志位，固定长度（4），输出长度跟p_iAngleLoc参数有关                                 */
    /*    p_fEngValue  声道能量幅度（0-30）                                                                                            			    */
    /* RTN:                                                                 */
    /*    -1 -- 不成功                                                      											*/
    /*     1 -- 成功                                                        											*/
    /* ******************************************************************** */
    public native int DealHianSep(short[] p_nTgtData, short[] p_nVadFlag, float[] p_fEngValue, short[] p_nWavData);
    /* ******************************************************************** */
    /* FUN                                                                  */
    /*    资源释放                                                                                                                                                                                                                     */
    /* IN:                                                                  */
    /*    NULL                                                              */
    /* RTN:                                                                 */
    /*    RTMSG_KO -- 不成功                                                                                                                                                                               */
    /*    RTMSG_OK -- 成功                                                                                                                                                                                    */
    /* ******************************************************************** */
    public native int FreeHianSep();


//    /************************************************************************/
//    /* FUN                                                                  */
//    /*    初始化                                                            */
//    /* IN:                                                                  */
//    /*    p_cAppId     AppId,由hian提供                                     */
//    /* OUT:                                                                 */
//    /*    NULL                                                              */
//    /* RTN:                                                                 */
//    /*    -1 -- 不成功                                                      */
//    /*     1 -- 成功                                                        */
//    /************************************************************************/
//    public native int InitAudioPro(String p_cAppId);
//
//    /************************************************************************/
//    /* FUN                                                                  */
//    /*    设置参数                                                          */
//    /* IN:                                                                  */
//    /*    p_iFrontNum  从sil到on状态需要的帧数     默认10                   */
//    /*    p_fFrontThs  从sil到on状态灵敏度阈值（1~10）默认为5，越小越灵敏   */
//    /*    p_iEndNum    从on到end状态需要的帧数     默认20                   */
//    /*    p_fEndThs    从on到end状态灵敏度阈值（1~10）默认为5，越小越灵敏   */
//    /*    p_iAngleLoc  选择分离音频角度（ABSCISSA - 0/180 ORDINATE - 90/270）*/
//    /*                                   FULLAXIS - 0/90/180/270            */
//    /*    p_iBwParam   波束宽度控制参数范围2-20    默认20                   */
//    /*    p_iNrFlag    是否进行单通道降噪 1 - 是 0 否                       */
//    /*    p_iGain      增益因子范围1-8             默认1                    */
//    /* OUT:                                                                 */
//    /*    NULL                                                              */
//    /* RTN:                                                                 */
//    /*    -1 -- 不成功                                                      */
//    /*     1 -- 成功                                                        */
//    /************************************************************************/
//    public native int SetAudioPro(int p_iFrontNum, float p_fFrontThs, int p_iEndNum, float p_fEndThs, int p_iAngleLoc, int p_iBwParam, int p_iNrFlag, int p_iGain);
//
//    /************************************************************************/
//    /* FUN                                                                  */
//    /*    更新噪声参考值                                                    */
//    /* IN:                                                                  */
//    /*    p_nCtrlModes   噪声更新模式，1 -- 手动 0 - - 自动                 */
//    /*    p_nNoiseFlag   噪声标志位，1 -- 噪声 0 - - 有效音                 */
//    /************************************************************************/
//    public native int UpdAudioNoise(short p_nCtrlModes, short p_nNoiseFlag);
//
//    /************************************************************************/
//    /* FUN                                                                  */
//    /*    处理一帧数据的分离将在、VAD状态                                   */
//    /* IN:                                                                  */
//    /*    p_nWavData   需要处理的音频数据(暂时固定长度为4*256short)         */
//    /*OUT:                                                                  */
//    /*    p_nSepData  分离之后的数据，固定长度（2*256）                     */
//    /*    p_nVadFlag  左声道Vad标志位 0--静音 1--有效语音 2--语音结束       */
//    /*    p_fEngValue 左声道能量幅值（0-30）                                */
//    /*RTN:                                                                  */
//    /*    -1 -- 不成功                                                      */
//    /*     1 -- 成功                                                        */
//    /************************************************************************/
//    public native int RunAudioPro(short[] p_nTgtData, short[] p_nVadFlag, float[] p_fEngValue, short[] p_nWavData);
//
//    /************************************************************************/
//    /* FUN                                                                  */
//    /*    资源释放                                                          */
//    /* IN:                                                                  */
//    /*    p_iVadFlag  无                                                    */
//    /* OUT:                                                                 */
//    /*    NULL                                                              */
//    /* RTN:                                                                 */
//    /*    -1 -- 不成功                                                      */
//    /*     1 -- 成功                                                        */
//    /************************************************************************/
//    public native int FreeAudioPro();
}
