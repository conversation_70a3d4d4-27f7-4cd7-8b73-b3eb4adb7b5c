package com.hian;

import com.hian.jni.AudioProJni;

import co.timekettle.speech.utils.AiSpeechLogUtil;

public class HianAudioSep {

    private static final String TAG = "HianAudioSep";

    private static HianAudioSep instance = null;

    private String strAppId = "02167252"; // 期限到 2025 年中
    private boolean inited = false;

    //    1.默认参数(70-75db适用)
//    FrontNum:22
//    EndNum:30
//    FrontThs:2
//    EndThs:0.3
//    Gain:6
//
//    2.嘈杂参数(80-85db适用)
//    FrontNum:20
//    EndNum:30
//    FrontThs:6
//    EndThs:2
//    Gain:6
    private int iFrontNum = 10;
    private int iEndNum = 22;
    private float fFrontThs = 5.0f;
    private float fEndThs = 2.0f;
    //    private float fThres = (float) 3.5;
    private int iAngleLoc = AudioProJni.getInstance().ABSCISSA;
    private int iBwParam = 14;
    private int iNrFlag = 1;
    private int iGain = 6;

    public static HianAudioSep getInstance() {
        if (instance == null) {
            instance = new HianAudioSep();
        }
        return instance;
    }

    HianAudioSep() {
        if (AudioProJni.getInstance().InitHianSep(strAppId)
                != AudioProJni.getInstance().RTMSG_OK) {
            AiSpeechLogUtil.e(TAG, "InitHianVad Error!");
        }
        AiSpeechLogUtil.e(TAG, "init: InitHianSep");

        // 默认参数设置
        int ret = AudioProJni.getInstance().SetHianSep(iFrontNum, fFrontThs, iEndNum, fEndThs, iAngleLoc, iBwParam, iNrFlag, iGain);
        AiSpeechLogUtil.d(TAG, "init: SetHianSep:" + (ret == AudioProJni.getInstance().RTMSG_OK ? "true" : "false"));
    }

    public boolean init() {

//        if(this.inited) {
//            return true;
//        }
//
//        if (AudioProJni.getInstance().InitHianSep(strAppId)
//                != AudioProJni.getInstance().RTMSG_OK) {
//            AiSpeechLogUtil.e(TAG, "InitHianVad Error!");
//            return false;
//        }
//        AiSpeechLogUtil.e(TAG, "init: InitHianSep");
//        this.inited = true;
        return true;
    }

    public boolean setParams(int iFrontNum, float fFrontThs, int iEndNum, float fEndThs, int iAngleLoc, int iBwParam, int iNrFlag, int iGain) {
        if (
                this.iFrontNum != iFrontNum || this.fFrontThs != fFrontThs
                        || this.iEndNum != iEndNum || this.fEndThs != fEndThs
                        || this.iAngleLoc != iAngleLoc || this.iBwParam != iBwParam
                        || this.iNrFlag != iNrFlag || this.iGain != iGain
        ) {
            int ret = AudioProJni.getInstance().SetHianSep(iFrontNum, fFrontThs, iEndNum, fEndThs, iAngleLoc, iBwParam, iNrFlag, iGain);
            AiSpeechLogUtil.e(TAG, "setParams: SetHianSep:" + (ret == AudioProJni.getInstance().RTMSG_OK ? "true" : "false"));
            if (ret != AudioProJni.getInstance().RTMSG_OK) return false;
        }

        return true;
    }


    public void release() {
//        this.inited = false;
//        AudioProJni.getInstance().FreeHianSep();
    }
}
