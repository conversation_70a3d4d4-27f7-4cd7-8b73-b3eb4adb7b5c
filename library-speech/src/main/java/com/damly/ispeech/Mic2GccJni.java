package com.damly.ispeech;/* DO NOT EDIT THIS FILE - it is machine generated */

import co.timekettle.speech.utils.AiSpeechLogUtil;

public class Mic2GccJni {
    private boolean bGccInit = false;
    public float fVadLkTH = 0.08f;

    private Mic2GccJni() {

    }

    private static Mic2GccJni sAudioProMic2GccJni = null;

    public static Mic2GccJni getInstance() {
        if (sAudioProMic2GccJni == null) {
            synchronized (Mic2GccJni.class) {
                if (sAudioProMic2GccJni == null) {
                    sAudioProMic2GccJni = new Mic2GccJni();
                }
            }
        }
        return sAudioProMic2GccJni;
    }

    static {
        System.loadLibrary("tmk_gcc");
    }

    public synchronized boolean Init() {
        int ret = -1;
        if (!bGccInit) {
            ret = Init2MicGcc();
            AiSpeechLogUtil.e("Mic2GccJni", "Init2MicGcc: " + ret);
            bGccInit = ret == 1;
        }
        
        if (bGccInit) {
            float jfHCorrTH = 0.7f;
            float jfLCorrTH = 0.5f;
            float jfVadOnTH = 0.2f;
//            float jfVadLkTH = 0.08f;
            ret = Mic2GccJni.getInstance().Set2MicGcc(jfHCorrTH, jfLCorrTH, jfVadOnTH, Mic2GccJni.getInstance().fVadLkTH);
            AiSpeechLogUtil.e("Mic2GccJni", "Set2MicGcc: " + ret);
        }
        return bGccInit;
    }

    public synchronized boolean Set(float jfHCorrTH, float jfLCorrTH, float jfVadOnTH, float jfVadLkTH) {
        int ret = Mic2GccJni.getInstance().Set2MicGcc(jfHCorrTH, jfLCorrTH, jfVadOnTH, jfVadLkTH);
        return ret == 1;
    }

    public synchronized boolean Deal(short[] jnWaveOut, short[] jnVadFlag, float[] jfCorrNF, short[] jnWaveIn) {
        int ret = Mic2GccJni.getInstance().Deal2MicGcc(jnWaveOut, jnVadFlag, jfCorrNF, jnWaveIn);
        return ret == 1;
    }

    public synchronized boolean Free() {
        if (!bGccInit) return true;
        int ret = Mic2GccJni.getInstance().Free2MicGcc();
        if (ret == 1) bGccInit = false;
        return ret == 1;
    }

/*
 广义自相关算法
*/

    /*
     * Class:     com_damly_ispeech_Mic2GccJni
     * Method:    Gcc
     * Signature: ([SI)[F
     */
/************************************************************************/
    /* FUN                                                                  */
    /*    初始化                                                            */
    /* IN:                                                                  */
    /*    NULL                                                              */
    /* OUT:                                                                 */
    /*    NULL                                                              */
    /* RTN:                                                                 */
    /*    -1 -- 不成功                                                      */
    /*     1 -- 成功                                                        */
    /************************************************************************/
    private native int Init2MicGcc();

/************************************************************************/
    /* FUN                                                                  */
    /*    参数设置                                                          */
    /* IN:                                                                  */
    /*    jfHCorrTH    互相关均值阈值,高门限值                              */
    /*    jfLCorrTH    互相关均值阈值,低门限值                              */
    /*    jfVadOnTH    VAD激活状态的阈值                                    */
    /*    jfVadLkTH    VAD疑似状态的阈值                                    */
    /*OUT:                                                                  */
    /*    NULL                                                              */
    /*RTN:                                                                  */
    /*    -1 -- 不成功                                                      */
    /*     1 -- 成功                                                        */
    /************************************************************************/
    private native int Set2MicGcc(float jfHCorrTH, float jfLCorrTH, float jfVadOnTH, float jfVadLkTH);

/************************************************************************/
    /* FUN                                                                  */
    /*    处理一帧数据的音频泄漏                                            */
    /* IN:                                                                  */
    /*    jnWaveIn   需要处理的音频数据(暂时固定长度为2*256 short)          */
    /*OUT:                                                                  */
    /*    jnWaveOut   处理之后的数据，固定长度（2*256 short）               */
    /*    jnVadFlag    左右通道VAD标志位                                    */
    /*    jfCorrNF     左右通道数据互相关系数，仅下标0有值                  */
    /*RTN:                                                                  */
    /*    -1 -- 不成功                                                      */
    /*     1 -- 成功                                                        */
    /************************************************************************/
    private native int Deal2MicGcc(short[] jnWaveOut, short[] jnVadFlag, float[] jfCorrNF, short[] jnWaveIn);

/************************************************************************/
    /* FUN                                                                  */
    /*    资源释放                                                          */
    /* IN:                                                                  */
    /*    NULL                                                              */
    /* OUT:                                                                 */
    /*    NULL                                                              */
    /* RTN:                                                                 */
    /*    -1 -- 不成功                                                      */
    /*     1 -- 成功                                                        */
    /************************************************************************/
    private native int Free2MicGcc();

}
