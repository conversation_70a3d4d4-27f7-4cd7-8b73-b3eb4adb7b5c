package com.damly.ispeech;/* DO NOT EDIT THIS FILE - it is machine generated */

public class Mic4SepJni {

    public final int ORDINATE_UP = 0;
    public final int ORDINATE_DN = 1;

    private Mic4SepJni() {

    }

    private static Mic4SepJni sAudioPro4MicJni = null;
    public static Mic4SepJni getInstance() {
        if (sAudioPro4MicJni == null) {
            synchronized (Mic4SepJni.class) {
                if (sAudioPro4MicJni == null) {
                    sAudioPro4MicJni = new Mic4SepJni();
                }
            }
        }
        return sAudioPro4MicJni;
    }

    static {
        System.loadLibrary("Tmk4MicSep_Ls");
    }


    /*
     ZERO横屏模式 分离算法
    */

    /*
     * Class:     com_damly_ispeech_4MicSepJni
     * Method:    4MicSep
     * Signature: ([SI)[F
     */
    /************************************************************************/
    /* FUN                                                                  */
    /*    初始化参数                                                        */
    /* IN:                                                                  */
    /*    jnCalData  校准需要的参数（格式:C0......C1.....C2......C3......） */
    /*    jiRow      原始数据的行数                                         */
    /*    jiCol      原始数据的列数                                         */
    /* OUT:                                                                 */
    /*    NULL                                                              */
    /* RTN:                                                                 */
    /*    -1 -- 不成功                                                      */
    /*     1 -- 成功                                                        */
    /************************************************************************/
    public native int InitLsPro(short[] jnCalData, int jiRow, int jiCol);

    /************************************************************************/
    /* FUN                                                                  */
    /*    重新设置参数                                                      */
    /* IN:                                                                  */
    /*    jiAngleLoc  选择分离音频角度（ORDINATE_UP - 270）                 */
    /*                                   ORDINATE_DN - 90                   */
    /*    jiBwParam   波束宽度控制参数范围2-20    默认2.0                   */
    /*    jiAnrFlag   降噪标志位 0 -- 关闭 / 1 -- 开启                      */
    /*    jfAnrDepth  降噪深度，越大降噪越多      默认20                    */
    /*    jfGain      增益因子范围1-8             默认1                     */
    /*====just for test=====================================================*/
    /*    jfPhiThold  方位角阈值 (60.0~90.0 默认90.0)                       */
    /*    jfPhiAlpha  方位角差值平滑因子 (0~1 默认0.8)                      */
    /*    jiPhiModel  方位角差值计算模式（0-线性模式/1-三角模式 默认0）     */
    /* OUT:                                                                 */
    /*    NULL                                                              */
    /* RTN:                                                                 */
    /*    -1 -- 不成功                                                      */
    /*     1 -- 成功                                                        */
    /************************************************************************/
    public native int SetLsPro(int jiAngleLoc, int jiBwParam, int jiAnrFlag, float jfAnrDepth, float jfGain, float jfPhiThold, float jfPhiAlpha, int jiPhiModel);

    /************************************************************************/
    /* FUN                                                                  */
    /*    处理一帧数据的分离将在、VAD状态                                   */
    /* IN:                                                                  */
    /*    jnWaveIn   需要处理的音频数据(暂时固定长度为4*256 short)          */
    /*OUT:                                                                  */
    /*    jnWaveOut  分离之后的数据，固定长度（256）                        */
    /*    jnVadFlagt 左声道Vad标志位 0--静音 1--有效语音 2--语音结束        */
    /*    jfEngValue 左声道能量幅值（0-30）                                 */
    /*RTN:                                                                  */
    /*    -1 -- 不成功                                                      */
    /*     1 -- 成功                                                        */
    /************************************************************************/
    public native int RunLsPro(short[] jnWaveOut, short[] jnVadFlag, float[] jfEngValue, short[] jnWaveIn);

    /************************************************************************/
    /* FUN                                                                  */
    /*    资源释放                                                          */
    /* IN:                                                                  */
    /*    NULL                                                              */
    /* OUT:                                                                 */
    /*    NULL                                                              */
    /* RTN:                                                                 */
    /*    -1 -- 不成功                                                      */
    /*     1 -- 成功                                                        */
    /************************************************************************/
    public native int FreeLsPro();
}