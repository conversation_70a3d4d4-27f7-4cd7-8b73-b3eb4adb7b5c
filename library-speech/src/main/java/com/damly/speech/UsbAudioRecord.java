package com.damly.speech;

import static android.app.PendingIntent.FLAG_IMMUTABLE;
import static android.app.PendingIntent.FLAG_ONE_SHOT;

import android.app.PendingIntent;
import android.content.BroadcastReceiver;
import android.content.Context;
import android.content.Intent;
import android.content.IntentFilter;
import android.hardware.usb.UsbDevice;
import android.hardware.usb.UsbDeviceConnection;
import android.hardware.usb.UsbManager;

import java.util.HashMap;

import co.timekettle.speech.utils.AiSpeechLogUtil;

/**
 * Created by timekettle on 2018/7/7.
 */

public class UsbAudioRecord {

    private static final String TAG = "UsbAudioRecorder";
    private static final String ACTION_USB_DEVICE_PERMISSION = "com.damly.uac.action.USB_PERMISSION";

    private static final int DEFAULT_BUFFER_SIZE = 2048;

    private static boolean mLibraryLoaded = false;
    private static RecordListener mRecordListener = null;

    private UsbDeviceConnection mUsbDeviceConnection = null;
    private Context mContext;
    private UsbManager mUsbManager;
    private PendingIntent mPendingIntent;
    private boolean mWorking = false;
    private Thread mPorcessThread;

    private static int mBufferSizeInBytes = DEFAULT_BUFFER_SIZE;
    private static int mStereoBufferIndex = 0;
    private static byte[] mStereoBuffer = null;

    private static int mLeftBufferIndex = 0;
    private static byte[] mLeftBuffer = null;

    private static int mRightBufferIndex = 0;
    private static byte[] mRightBuffer = null;

    static {
        if (!mLibraryLoaded) {
            System.loadLibrary("usb100");
            System.loadLibrary("uac");
            mLibraryLoaded = true;
        }
    }

    private static short value = 0;
    private static short prevalue = 0;
    private static String print = "";
    private static long receive_count = 0;
    private static void checkPacketLoss(byte[] data) {
        for (int i = 0; i < data.length / 2; i++) {
            value = (short)((data[i * 2 + 1] << 8) + data[i * 2 + 0]);
            if ((value - prevalue == 1) || (value == 1 && prevalue == 64)) {
//                    AiSpeechLogUtil.e(TAG, "receive: " + value);
            } else {
                for (int j = Math.max(i * 2 - 10, 0); j < Math.min(data.length, i * 2 + 10); j++) {
                    if (j / 2 == i) {
                        if (j % 2 == 0) {
                            print = print + " [" + data[j];
                        } else {
                            print = print + " " + data[j] + "]";
                        }
                    } else {
                        print = print + " " + data[j];
                    }
                }
                AiSpeechLogUtil.e(TAG, "checkPacketLoss: receive_count: " + receive_count + " | " + prevalue + " -> " + value + " --> 此处丢失 " + print);
                print = "";
            }
            prevalue = value;

            if (receive_count == 64) {
                String str = "";
                for (int j = 0; j < Math.max(data.length, 64 * 2 + 10); j++) {
                    str = str + " " + data[j];
                }
                AiSpeechLogUtil.e(TAG, "checkPacketLoss: " + str);
            }
            receive_count++;
        }
    }

    private static void receive(byte[] data) {
        if (mRecordListener != null) {

//            checkPacketLoss(data);
            // 一般过来的数据是 1280, 但有时也有 1276, 1240 等, 是 4 的倍数, 除 4 遍历的原因?
            int count = data.length / 4;
            for (int i = 0; i < count; i++) {
                mLeftBuffer[mLeftBufferIndex++] = data[i * 4 + 0];
                mLeftBuffer[mLeftBufferIndex++] = data[i * 4 + 1];

                mRightBuffer[mRightBufferIndex++] = data[i * 4 + 2];
                mRightBuffer[mRightBufferIndex++] = data[i * 4 + 3];

                mStereoBuffer[mStereoBufferIndex++] = data[i * 4 + 0];
                mStereoBuffer[mStereoBufferIndex++] = data[i * 4 + 1];
                mStereoBuffer[mStereoBufferIndex++] = data[i * 4 + 2];
                mStereoBuffer[mStereoBufferIndex++] = data[i * 4 + 3];
                if (mStereoBufferIndex == mBufferSizeInBytes) {
                    mLeftBufferIndex = 0;
                    mRightBufferIndex = 0;
                    mStereoBufferIndex = 0;
                    mRecordListener.onRecord(mStereoBuffer, mLeftBuffer, mRightBuffer);
                }
            }
        }
    }

    private native int open(int vid, int pid, int busnum, int devaddr, int fd, String usbfs);

    private native int process();

    private native void exit();

    public UsbAudioRecord(Context context, int bufferSizeInBytes) {
        mContext = context;
        mUsbManager = (UsbManager) context.getSystemService(Context.USB_SERVICE);
//        mPendingIntent = PendingIntent.getBroadcast(context, 0, new Intent(ACTION_USB_DEVICE_PERMISSION), 0);
        if (android.os.Build.VERSION.SDK_INT >= android.os.Build.VERSION_CODES.S) {
            mPendingIntent = PendingIntent.getBroadcast(context, 0, new Intent(ACTION_USB_DEVICE_PERMISSION), FLAG_IMMUTABLE);
        } else {
            mPendingIntent = PendingIntent.getBroadcast(context, 0, new Intent(ACTION_USB_DEVICE_PERMISSION), FLAG_ONE_SHOT);
        }

        if (bufferSizeInBytes > DEFAULT_BUFFER_SIZE) {
            mBufferSizeInBytes = (bufferSizeInBytes / 64) * 64;
        }
    }

    public UsbAudioRecord(Context context) {
        mContext = context;
        mUsbManager = (UsbManager) context.getSystemService(Context.USB_SERVICE);
        mPendingIntent = PendingIntent.getBroadcast(context, 0, new Intent(ACTION_USB_DEVICE_PERMISSION), 0);
    }

    public void startRecording(RecordListener listener) {

        if (this.mWorking) {
            if (mRecordListener != null) {
                mRecordListener.onError(-1, "It's already recorded, please stop recording first!");
            }
            return;
        }

        this.register();
        mRecordListener = listener;

        UsbDevice device = null;

        HashMap<String, UsbDevice> map = mUsbManager.getDeviceList();

        for (String key : map.keySet()) {
            device = map.get(key);
            break;
        }

        if (device != null) {

            if (mUsbManager.hasPermission(device)) {
                AiSpeechLogUtil.e(TAG, "已有权限, 直接打开设备");
                this.openDevice(device);
            } else {
                AiSpeechLogUtil.e(TAG, "没有权限, 申请打开设备");
                mUsbManager.requestPermission(device, mPendingIntent);
            }
        } else {
            if (mRecordListener != null) {
                mRecordListener.onError(-3, "No usb audio device was found!");
            }
        }
    }

    public void release() {

        if (mWorking) {
            mPorcessThread.interrupt();

            mWorking = false;

            if (mRecordListener != null) {
                mRecordListener.onRelease();
            }
//            this.unregister();
////            this.exit();
//            if (mUsbDeviceConnection != null) {
//                mUsbDeviceConnection.close();
//                mUsbDeviceConnection = null;
//            }

            mRecordListener = null;
        }

        receive_count = 0;
    }

    private boolean openDevice(UsbDevice device) {

        mStereoBufferIndex = 0;
        mStereoBuffer = new byte[mBufferSizeInBytes];

        mLeftBufferIndex = 0;
        mLeftBuffer = new byte[mBufferSizeInBytes / 2];

        mRightBufferIndex = 0;
        mRightBuffer = new byte[mBufferSizeInBytes / 2];

        mUsbDeviceConnection = mUsbManager.openDevice(device);
        int fd = mUsbDeviceConnection.getFileDescriptor();

        String[] v = device.getDeviceName().split("/");

        int busnum = Integer.parseInt(v[v.length - 2]);
        int devnum = Integer.parseInt(v[v.length - 1]);

        AiSpeechLogUtil.e(TAG, "USB类型: " + device.toString());

        int rc = this.open(device.getVendorId(), device.getProductId(), busnum, devnum, fd, device.getDeviceName());
        if (rc == 0) {
            mWorking = true;

            mPorcessThread = new Thread(new Runnable() {
                @Override
                public void run() {
                    AiSpeechLogUtil.e(TAG, "usb start record: " + mWorking);

                    while (!Thread.interrupted()) {
                        int rc = process();
                        if (rc != 0) {
                            AiSpeechLogUtil.e(TAG, "uac handle event err " + rc);
                            break;
                        }
                    }

                    mWorking = false;
                    exit();
                    unregister();
                    if (mUsbDeviceConnection != null) {
                        mUsbDeviceConnection.close();
                        mUsbDeviceConnection = null;
                    }

                    AiSpeechLogUtil.e(TAG, "handle event thread exit.");
                }
            });

            mPorcessThread.start();

            if (mRecordListener != null) {
                mRecordListener.onError(0, "Open usb device success!");
            }
            return true;
        } else {
            if (mRecordListener != null) {
                mRecordListener.onError(-2, "Open usb device error!");
            }
        }

        return false;
    }

    private BroadcastReceiver mUsbBroadcastReceiver = new BroadcastReceiver() {
        @Override
        public void onReceive(Context context, Intent intent) {
            String action = intent.getAction();
            if (ACTION_USB_DEVICE_PERMISSION.equals(action)) {
                synchronized (this) {
                    UsbDevice device = intent.getParcelableExtra(UsbManager.EXTRA_DEVICE);
                    if (intent.getBooleanExtra(UsbManager.EXTRA_PERMISSION_GRANTED, false)) {
                        if (device != null) {
                            if (mRecordListener != null) {
                                mRecordListener.onError(0, "USB device request permission is successful!");
                            }
                            openDevice(device);
                        }
                    } else {
                        AiSpeechLogUtil.e(TAG, "USB 设备请求权限失败！");
                        if (mRecordListener != null) {
                            mRecordListener.onError(-4, "USB device request permission failed!");
                        }
                    }
                }
            }

            switch (action) {
                case UsbManager.ACTION_USB_DEVICE_DETACHED: {
//                    UsbDevice device = intent.getParcelableExtra(UsbManager.EXTRA_DEVICE);
                    if (mRecordListener != null) {
                        mRecordListener.onError(0, "USB device detached!");
                    }
                    release();
                    break;
                }
            }
        }
    };

    private void register() {
        IntentFilter intentFilter = new IntentFilter();
        intentFilter.addAction(ACTION_USB_DEVICE_PERMISSION);
        intentFilter.addAction(UsbManager.ACTION_USB_DEVICE_DETACHED);
        mContext.registerReceiver(mUsbBroadcastReceiver, intentFilter);
    }

    private void unregister() {
        mContext.unregisterReceiver(mUsbBroadcastReceiver);
    }

    public abstract interface RecordListener {
        public void onRecord(byte[] stereoBuffer, byte[] leftBuffer, byte[] rightBuffer);

        public void onRelease();

        public void onError(int code, String message);
    }
}
