# This file contains the fastlane.tools configuration
# You can find the documentation at https://docs.fastlane.tools
#
# For a list of all available actions, check out
#
#     https://docs.fastlane.tools/actions
#
# For a list of all available plugins, check out
#
#     https://docs.fastlane.tools/plugins/available-plugins
#

# Uncomment the line if you want fastlane to automatically update itself
# update_fastlane

default_platform(:android)

platform :android do
  # desc "Runs all the tests"
  # lane :test do
  #   gradle(task: "test")
  # end

  desc "Submit a new Beta Build to Crashlytics Beta"
  lane :beta do
    # 构建 APK 文件
    # gradle(task: "clean assembleRelease")
    gradle(
      task: "assemble", # "bundle", # or "assemble", if you want to build an APK
      build_type: "release",
      properties: {
        "android.injected.signing.store.file" => ENV["KEYSTORE_FILE"],
        "android.injected.signing.store.password" => ENV["KEYSTORE_PASSWORD"],
        "android.injected.signing.key.alias" => ENV["KEY_ALIAS"],
        "android.injected.signing.key.password" => ENV["KEY_PASSWORD"],
      }
    )

    # 上传到蒲公英
    # answer = pgyer(
    #   api_key: ENV["PGYER_API_KEY"], # 在环境变量中配置蒲公英 API Key
    #   apk: ENV["GRADLE_APK_OUTPUT_PATH"], # APK 文件路径
    #   update_description: "测试: apk已通过fastlane编译打包并上传pyger。" # 更新描述，可自定义
    # )
    apk_path = lane_context[SharedValues::GRADLE_APK_OUTPUT_PATH]
    sh("echo 'APK_PATH=#{apk_path}' >> $GITHUB_ENV")

    # crashlytics
  
    # sh "your_script.sh"
    # You can also use other beta testing services here
  end

  desc "Deploy a new version to the Google Play"
  lane :deploy do
    # fetch_and_increment_build_number

    gradle(
      task: "bundle", # or "assemble", if you want to build an APK
      build_type: "release",
      properties: {
        "android.injected.signing.store.file" => ENV["KEYSTORE_FILE"],
        "android.injected.signing.store.password" => ENV["KEYSTORE_PASSWORD"],
        "android.injected.signing.key.alias" => ENV["KEY_ALIAS"],
        "android.injected.signing.key.password" => ENV["KEY_PASSWORD"],
      }  
    )
    
    # upload_to_play_store(
    #   track: "internal",
    #   json_key: ENV["ANDROID_JSON_KEY_FILE"]
    # )
  end

  desc "Fetches the latest version code from the Play Console and increments it by 1"
  lane :fetch_and_increment_build_number do
    app_identifier = CredentialsManager::AppfileConfig.try_fetch_value(:app_identifier)

    version_codes = google_play_track_version_codes(
      package_name: app_identifier,
      track: "internal",
      json_key: ENV["ANDROID_JSON_KEY_FILE"]
    )
    
    updated_version_code = version_codes[0] + 1
    
    increment_version_code(
      version_code: updated_version_code
    )
  end
end
