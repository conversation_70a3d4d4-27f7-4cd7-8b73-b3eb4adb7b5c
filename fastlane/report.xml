<?xml version="1.0" encoding="UTF-8"?>
<testsuites>
  <testsuite name="fastlane.lanes">
    
    
    
      
      <testcase classname="fastlane.lanes" name="0: default_platform" time="0.000207">
        
      </testcase>
    
      
      <testcase classname="fastlane.lanes" name="1: test" time="7.860863">
        
          <failure message="/opt/homebrew/Cellar/fastlane/2.222.0/libexec/gems/fastlane-2.226.0/fastlane/lib/fastlane/actions/actions_helper.rb:67:in `execute_action&apos;&#10;/opt/homebrew/Cellar/fastlane/2.222.0/libexec/gems/fastlane-2.226.0/fastlane/lib/fastlane/runner.rb:255:in `block in execute_action&apos;&#10;/opt/homebrew/Cellar/fastlane/2.222.0/libexec/gems/fastlane-2.226.0/fastlane/lib/fastlane/runner.rb:229:in `chdir&apos;&#10;/opt/homebrew/Cellar/fastlane/2.222.0/libexec/gems/fastlane-2.226.0/fastlane/lib/fastlane/runner.rb:229:in `execute_action&apos;&#10;/opt/homebrew/Cellar/fastlane/2.222.0/libexec/gems/fastlane-2.226.0/fastlane/lib/fastlane/runner.rb:157:in `trigger_action_by_name&apos;&#10;/opt/homebrew/Cellar/fastlane/2.222.0/libexec/gems/fastlane-2.226.0/fastlane/lib/fastlane/fast_file.rb:159:in `method_missing&apos;&#10;Fastfile:21:in `block (2 levels) in parsing_binding&apos;&#10;/opt/homebrew/Cellar/fastlane/2.222.0/libexec/gems/fastlane-2.226.0/fastlane/lib/fastlane/lane.rb:41:in `call&apos;&#10;/opt/homebrew/Cellar/fastlane/2.222.0/libexec/gems/fastlane-2.226.0/fastlane/lib/fastlane/runner.rb:49:in `block in execute&apos;&#10;/opt/homebrew/Cellar/fastlane/2.222.0/libexec/gems/fastlane-2.226.0/fastlane/lib/fastlane/runner.rb:45:in `chdir&apos;&#10;/opt/homebrew/Cellar/fastlane/2.222.0/libexec/gems/fastlane-2.226.0/fastlane/lib/fastlane/runner.rb:45:in `execute&apos;&#10;/opt/homebrew/Cellar/fastlane/2.222.0/libexec/gems/fastlane-2.226.0/fastlane/lib/fastlane/lane_manager.rb:46:in `cruise_lane&apos;&#10;/opt/homebrew/Cellar/fastlane/2.222.0/libexec/gems/fastlane-2.226.0/fastlane/lib/fastlane/command_line_handler.rb:34:in `handle&apos;&#10;/opt/homebrew/Cellar/fastlane/2.222.0/libexec/gems/fastlane-2.226.0/fastlane/lib/fastlane/commands_generator.rb:110:in `block (2 levels) in run&apos;&#10;/opt/homebrew/Cellar/fastlane/2.222.0/libexec/gems/commander-4.6.0/lib/commander/command.rb:187:in `call&apos;&#10;/opt/homebrew/Cellar/fastlane/2.222.0/libexec/gems/commander-4.6.0/lib/commander/command.rb:157:in `run&apos;&#10;/opt/homebrew/Cellar/fastlane/2.222.0/libexec/gems/commander-4.6.0/lib/commander/runner.rb:444:in `run_active_command&apos;&#10;/opt/homebrew/Cellar/fastlane/2.222.0/libexec/gems/fastlane-2.226.0/fastlane_core/lib/fastlane_core/ui/fastlane_runner.rb:124:in `run!&apos;&#10;/opt/homebrew/Cellar/fastlane/2.222.0/libexec/gems/commander-4.6.0/lib/commander/delegates.rb:18:in `run!&apos;&#10;/opt/homebrew/Cellar/fastlane/2.222.0/libexec/gems/fastlane-2.226.0/fastlane/lib/fastlane/commands_generator.rb:363:in `run&apos;&#10;/opt/homebrew/Cellar/fastlane/2.222.0/libexec/gems/fastlane-2.226.0/fastlane/lib/fastlane/commands_generator.rb:43:in `start&apos;&#10;/opt/homebrew/Cellar/fastlane/2.222.0/libexec/gems/fastlane-2.226.0/fastlane/lib/fastlane/cli_tools_distributor.rb:123:in `take_off&apos;&#10;/opt/homebrew/Cellar/fastlane/2.222.0/libexec/gems/fastlane-2.226.0/bin/fastlane:23:in `&lt;top (required)&gt;&apos;&#10;/opt/homebrew/Cellar/fastlane/2.222.0/libexec/bin/fastlane:25:in `load&apos;&#10;/opt/homebrew/Cellar/fastlane/2.222.0/libexec/bin/fastlane:25:in `&lt;main&gt;&apos;&#10;&#10;Exit status of command &apos;/Users/<USER>/Documents/TmkProjects/X1/w3pro/gradlew test -p .&apos; was 1 instead of 0.&#10;Configuration on demand is an incubating feature.&#10;&gt; Task :buildSrc:pluginDescriptors UP-TO-DATE&#10;&gt; Task :buildSrc:processResources NO-SOURCE&#10;&gt; Task :buildSrc:processTestResources NO-SOURCE&#10;&#10;&gt; Task :buildSrc:compileKotlin&#10;&apos;compileJava&apos; task (current target is 11) and &apos;compileKotlin&apos; task (current target is 1.8) jvm target compatibility should be set to the same Java version.&#10;&#10;&gt; Task :buildSrc:compileJava NO-SOURCE&#10;&gt; Task :buildSrc:compileGroovy NO-SOURCE&#10;&gt; Task :buildSrc:classes UP-TO-DATE&#10;&gt; Task :buildSrc:inspectClassesForKotlinIC UP-TO-DATE&#10;&gt; Task :buildSrc:jar UP-TO-DATE&#10;&gt; Task :buildSrc:assemble UP-TO-DATE&#10;&gt; Task :buildSrc:compileTestKotlin NO-SOURCE&#10;&gt; Task :buildSrc:pluginUnderTestMetadata UP-TO-DATE&#10;&gt; Task :buildSrc:compileTestJava NO-SOURCE&#10;&gt; Task :buildSrc:compileTestGroovy NO-SOURCE&#10;&gt; Task :buildSrc:testClasses UP-TO-DATE&#10;&gt; Task :buildSrc:test NO-SOURCE&#10;&gt; Task :buildSrc:validatePlugins UP-TO-DATE&#10;&gt; Task :buildSrc:check UP-TO-DATE&#10;&gt; Task :buildSrc:build UP-TO-DATE&#10;&#10;&gt; Configure project :library-speech&#10;gradle task = test&#10;release = false&#10;&#10;&gt; Configure project :shell&#10;WARNING:API &apos;android.registerTransform&apos; is obsolete.&#10;It will be removed in version 8.0 of the Android Gradle plugin.&#10;The Transform API is removed to improve build performance. Projects that use the&#10;Transform API force the Android Gradle plugin to use a less optimized flow for the&#10;build that can result in large regressions in build times. It’s also difficult to&#10;use the Transform API and combine it with other Gradle features; the replacement&#10;APIs aim to make it easier to extend the build without introducing performance or&#10;correctness issues.&#10;&#10;There is no single replacement for the Transform API—there are new, targeted&#10;APIs for each use case. All the replacement APIs are in the&#10;`androidComponents {}` block.&#10;For more information, see https://developer.android.com/studio/releases/gradle-plugin-api-updates#transform-api.&#10;To determine what is calling android.registerTransform, use -Pandroid.debug.obsoleteApi=true on the command line to display more information.&#10;====&gt; 当前时间戳（buildNumber）: 12270453&#10;WARNING:We recommend using a newer Android Gradle plugin to use compileSdk = 34&#10;&#10;This Android Gradle plugin (7.2.2) was tested up to compileSdk = 33&#10;&#10;This warning can be suppressed by adding&#10;    android.suppressUnsupportedCompileSdk=34&#10;to this project&apos;s gradle.properties&#10;&#10;The build will continue, but you are strongly encouraged to update your project to&#10;use a newer Android Gradle Plugin that has been tested with compileSdk = 34&#10;&#10;FAILURE: Build failed with an exception.&#10;&#10;* What went wrong:&#10;Could not determine the dependencies of task &apos;:library-comm:testBetaUnitTest&apos;.&#10;&gt; Could not resolve all task dependencies for configuration &apos;:library-comm:betaUnitTestRuntimeClasspath&apos;.&#10;   &gt; Could not resolve project :library-bluetooth.&#10;     Required by:&#10;         project :library-comm&#10;      &gt; No matching variant of project :library-bluetooth was found. The consumer was configured to find a runtime of a component, preferably optimized for Android, as well as attribute &apos;com.android.build.api.attributes.BuildTypeAttr&apos; with value &apos;beta&apos;, attribute &apos;com.android.build.api.attributes.AgpVersionAttr&apos; with value &apos;7.2.2&apos;, attribute &apos;org.jetbrains.kotlin.platform.type&apos; with value &apos;androidJvm&apos; but:&#10;          - Variant &apos;debugApiElements&apos; capability X1:library-bluetooth:unspecified declares a component, preferably optimized for Android, as well as attribute &apos;com.android.build.api.attributes.AgpVersionAttr&apos; with value &apos;7.2.2&apos;, attribute &apos;org.jetbrains.kotlin.platform.type&apos; with value &apos;androidJvm&apos;:&#10;              - Incompatible because this component declares an API of a component, as well as attribute &apos;com.android.build.api.attributes.BuildTypeAttr&apos; with value &apos;debug&apos; and the consumer needed a runtime of a component, as well as attribute &apos;com.android.build.api.attributes.BuildTypeAttr&apos; with value &apos;beta&apos;&#10;          - Variant &apos;debugRuntimeElements&apos; capability X1:library-bluetooth:unspecified declares a runtime of a component, preferably optimized for Android, as well as attribute &apos;com.android.build.api.attributes.AgpVersionAttr&apos; with value &apos;7.2.2&apos;, attribute &apos;org.jetbrains.kotlin.platform.type&apos; with value &apos;androidJvm&apos;:&#10;              - Incompatible because this component declares a component, as well as attribute &apos;com.android.build.api.attributes.BuildTypeAttr&apos; with value &apos;debug&apos; and the consumer needed a component, as well as attribute &apos;com.android.build.api.attributes.BuildTypeAttr&apos; with value &apos;beta&apos;&#10;          - Variant &apos;releaseApiElements&apos; capability X1:library-bluetooth:unspecified declares a component, preferably optimized for Android, as well as attribute &apos;com.android.build.api.attributes.AgpVersionAttr&apos; with value &apos;7.2.2&apos;, attribute &apos;org.jetbrains.kotlin.platform.type&apos; with value &apos;androidJvm&apos;:&#10;              - Incompatible because this component declares an API of a component, as well as attribute &apos;com.android.build.api.attributes.BuildTypeAttr&apos; with value &apos;release&apos; and the consumer needed a runtime of a component, as well as attribute &apos;com.android.build.api.attributes.BuildTypeAttr&apos; with value &apos;beta&apos;&#10;          - Variant &apos;releaseRuntimeElements&apos; capability X1:library-bluetooth:unspecified declares a runtime of a component, preferably optimized for Android, as well as attribute &apos;com.android.build.api.attributes.AgpVersionAttr&apos; with value &apos;7.2.2&apos;, attribute &apos;org.jetbrains.kotlin.platform.type&apos; with value &apos;androidJvm&apos;:&#10;              - Incompatible because this component declares a component, as well as attribute &apos;com.android.build.api.attributes.BuildTypeAttr&apos; with value &apos;release&apos; and the consumer needed a component, as well as attribute &apos;com.android.build.api.attributes.BuildTypeAttr&apos; with value &apos;beta&apos;&#10;   &gt; Could not resolve project :library-serialhelper.&#10;     Required by:&#10;         project :library-comm&#10;      &gt; No matching variant of project :library-serialhelper was found. The consumer was configured to find a runtime of a component, preferably optimized for Android, as well as attribute &apos;com.android.build.api.attributes.BuildTypeAttr&apos; with value &apos;beta&apos;, attribute &apos;com.android.build.api.attributes.AgpVersionAttr&apos; with value &apos;7.2.2&apos;, attribute &apos;org.jetbrains.kotlin.platform.type&apos; with value &apos;androidJvm&apos; but:&#10;          - Variant &apos;debugApiElements&apos; capability X1:library-serialhelper:unspecified declares a component, preferably optimized for Android, as well as attribute &apos;com.android.build.api.attributes.AgpVersionAttr&apos; with value &apos;7.2.2&apos;, attribute &apos;org.jetbrains.kotlin.platform.type&apos; with value &apos;androidJvm&apos;:&#10;              - Incompatible because this component declares an API of a component, as well as attribute &apos;com.android.build.api.attributes.BuildTypeAttr&apos; with value &apos;debug&apos; and the consumer needed a runtime of a component, as well as attribute &apos;com.android.build.api.attributes.BuildTypeAttr&apos; with value &apos;beta&apos;&#10;          - Variant &apos;debugRuntimeElements&apos; capability X1:library-serialhelper:unspecified declares a runtime of a component, preferably optimized for Android, as well as attribute &apos;com.android.build.api.attributes.AgpVersionAttr&apos; with value &apos;7.2.2&apos;, attribute &apos;org.jetbrains.kotlin.platform.type&apos; with value &apos;androidJvm&apos;:&#10;              - Incompatible because this component declares a component, as well as attribute &apos;com.android.build.api.attributes.BuildTypeAttr&apos; with value &apos;debug&apos; and the consumer needed a component, as well as attribute &apos;com.android.build.api.attributes.BuildTypeAttr&apos; with value &apos;beta&apos;&#10;          - Variant &apos;releaseApiElements&apos; capability X1:library-serialhelper:unspecified declares a component, preferably optimized for Android, as well as attribute &apos;com.android.build.api.attributes.AgpVersionAttr&apos; with value &apos;7.2.2&apos;, attribute &apos;org.jetbrains.kotlin.platform.type&apos; with value &apos;androidJvm&apos;:&#10;              - Incompatible because this component declares an API of a component, as well as attribute &apos;com.android.build.api.attributes.BuildTypeAttr&apos; with value &apos;release&apos; and the consumer needed a runtime of a component, as well as attribute &apos;com.android.build.api.attributes.BuildTypeAttr&apos; with value &apos;beta&apos;&#10;          - Variant &apos;releaseRuntimeElements&apos; capability X1:library-serialhelper:unspecified declares a runtime of a component, preferably optimized for Android, as well as attribute &apos;com.android.build.api.attributes.AgpVersionAttr&apos; with value &apos;7.2.2&apos;, attribute &apos;org.jetbrains.kotlin.platform.type&apos; with value &apos;androidJvm&apos;:&#10;              - Incompatible because this component declares a component, as well as attribute &apos;com.android.build.api.attributes.BuildTypeAttr&apos; with value &apos;release&apos; and the consumer needed a component, as well as attribute &apos;com.android.build.api.attributes.BuildTypeAttr&apos; with value &apos;beta&apos;&#10;   &gt; Could not resolve project :library-sip.&#10;     Required by:&#10;         project :library-comm&#10;      &gt; No matching variant of project :library-sip was found. The consumer was configured to find a runtime of a component, preferably optimized for Android, as well as attribute &apos;com.android.build.api.attributes.BuildTypeAttr&apos; with value &apos;beta&apos;, attribute &apos;com.android.build.api.attributes.AgpVersionAttr&apos; with value &apos;7.2.2&apos;, attribute &apos;org.jetbrains.kotlin.platform.type&apos; with value &apos;androidJvm&apos; but:&#10;          - Variant &apos;debugApiElements&apos; capability X1:library-sip:unspecified declares a component, preferably optimized for Android, as well as attribute &apos;com.android.build.api.attributes.AgpVersionAttr&apos; with value &apos;7.2.2&apos;, attribute &apos;org.jetbrains.kotlin.platform.type&apos; with value &apos;androidJvm&apos;:&#10;              - Incompatible because this component declares an API of a component, as well as attribute &apos;com.android.build.api.attributes.BuildTypeAttr&apos; with value &apos;debug&apos; and the consumer needed a runtime of a component, as well as attribute &apos;com.android.build.api.attributes.BuildTypeAttr&apos; with value &apos;beta&apos;&#10;          - Variant &apos;debugRuntimeElements&apos; capability X1:library-sip:unspecified declares a runtime of a component, preferably optimized for Android, as well as attribute &apos;com.android.build.api.attributes.AgpVersionAttr&apos; with value &apos;7.2.2&apos;, attribute &apos;org.jetbrains.kotlin.platform.type&apos; with value &apos;androidJvm&apos;:&#10;              - Incompatible because this component declares a component, as well as attribute &apos;com.android.build.api.attributes.BuildTypeAttr&apos; with value &apos;debug&apos; and the consumer needed a component, as well as attribute &apos;com.android.build.api.attributes.BuildTypeAttr&apos; with value &apos;beta&apos;&#10;          - Variant &apos;releaseApiElements&apos; capability X1:library-sip:unspecified declares a component, preferably optimized for Android, as well as attribute &apos;com.android.build.api.attributes.AgpVersionAttr&apos; with value &apos;7.2.2&apos;, attribute &apos;org.jetbrains.kotlin.platform.type&apos; with value &apos;androidJvm&apos;:&#10;              - Incompatible because this component declares an API of a component, as well as attribute &apos;com.android.build.api.attributes.BuildTypeAttr&apos; with value &apos;release&apos; and the consumer needed a runtime of a component, as well as attribute &apos;com.android.build.api.attributes.BuildTypeAttr&apos; with value &apos;beta&apos;&#10;          - Variant &apos;releaseRuntimeElements&apos; capability X1:library-sip:unspecified declares a runtime of a component, preferably optimized for Android, as well as attribute &apos;com.android.build.api.attributes.AgpVersionAttr&apos; with value &apos;7.2.2&apos;, attribute &apos;org.jetbrains.kotlin.platform.type&apos; with value &apos;androidJvm&apos;:&#10;              - Incompatible because this component declares a component, as well as attribute &apos;com.android.build.api.attributes.BuildTypeAttr&apos; with value &apos;release&apos; and the consumer needed a component, as well as attribute &apos;com.android.build.api.attributes.BuildTypeAttr&apos; with value &apos;beta&apos;&#10;   &gt; Could not resolve project :library-enginedetect.&#10;     Required by:&#10;         project :library-comm&#10;      &gt; No matching variant of project :library-enginedetect was found. The consumer was configured to find a runtime of a component, preferably optimized for Android, as well as attribute &apos;com.android.build.api.attributes.BuildTypeAttr&apos; with value &apos;beta&apos;, attribute &apos;com.android.build.api.attributes.AgpVersionAttr&apos; with value &apos;7.2.2&apos;, attribute &apos;org.jetbrains.kotlin.platform.type&apos; with value &apos;androidJvm&apos; but:&#10;          - Variant &apos;debugApiElements&apos; capability X1:library-enginedetect:unspecified declares a component, preferably optimized for Android, as well as attribute &apos;com.android.build.api.attributes.AgpVersionAttr&apos; with value &apos;7.2.2&apos;, attribute &apos;org.jetbrains.kotlin.platform.type&apos; with value &apos;androidJvm&apos;:&#10;              - Incompatible because this component declares an API of a component, as well as attribute &apos;com.android.build.api.attributes.BuildTypeAttr&apos; with value &apos;debug&apos; and the consumer needed a runtime of a component, as well as attribute &apos;com.android.build.api.attributes.BuildTypeAttr&apos; with value &apos;beta&apos;&#10;          - Variant &apos;debugRuntimeElements&apos; capability X1:library-enginedetect:unspecified declares a runtime of a component, preferably optimized for Android, as well as attribute &apos;com.android.build.api.attributes.AgpVersionAttr&apos; with value &apos;7.2.2&apos;, attribute &apos;org.jetbrains.kotlin.platform.type&apos; with value &apos;androidJvm&apos;:&#10;              - Incompatible because this component declares a component, as well as attribute &apos;com.android.build.api.attributes.BuildTypeAttr&apos; with value &apos;debug&apos; and the consumer needed a component, as well as attribute &apos;com.android.build.api.attributes.BuildTypeAttr&apos; with value &apos;beta&apos;&#10;          - Variant &apos;releaseApiElements&apos; capability X1:library-enginedetect:unspecified declares a component, preferably optimized for Android, as well as attribute &apos;com.android.build.api.attributes.AgpVersionAttr&apos; with value &apos;7.2.2&apos;, attribute &apos;org.jetbrains.kotlin.platform.type&apos; with value &apos;androidJvm&apos;:&#10;              - Incompatible because this component declares an API of a component, as well as attribute &apos;com.android.build.api.attributes.BuildTypeAttr&apos; with value &apos;release&apos; and the consumer needed a runtime of a component, as well as attribute &apos;com.android.build.api.attributes.BuildTypeAttr&apos; with value &apos;beta&apos;&#10;          - Variant &apos;releaseRuntimeElements&apos; capability X1:library-enginedetect:unspecified declares a runtime of a component, preferably optimized for Android, as well as attribute &apos;com.android.build.api.attributes.AgpVersionAttr&apos; with value &apos;7.2.2&apos;, attribute &apos;org.jetbrains.kotlin.platform.type&apos; with value &apos;androidJvm&apos;:&#10;              - Incompatible because this component declares a component, as well as attribute &apos;com.android.build.api.attributes.BuildTypeAttr&apos; with value &apos;release&apos; and the consumer needed a component, as well as attribute &apos;com.android.build.api.attributes.BuildTypeAttr&apos; with value &apos;beta&apos;&#10;   &gt; Could not resolve project :lingcast_for_android.&#10;     Required by:&#10;         project :library-comm&#10;      &gt; No matching variant of project :lingcast_for_android was found. The consumer was configured to find a runtime of a component, preferably optimized for Android, as well as attribute &apos;com.android.build.api.attributes.BuildTypeAttr&apos; with value &apos;beta&apos;, attribute &apos;com.android.build.api.attributes.AgpVersionAttr&apos; with value &apos;7.2.2&apos;, attribute &apos;org.jetbrains.kotlin.platform.type&apos; with value &apos;androidJvm&apos; but:&#10;          - Variant &apos;debugApiElements&apos; capability X1:lingcast_for_android:unspecified declares a component, preferably optimized for Android, as well as attribute &apos;com.android.build.api.attributes.AgpVersionAttr&apos; with value &apos;7.2.2&apos;, attribute &apos;org.jetbrains.kotlin.platform.type&apos; with value &apos;androidJvm&apos;:&#10;              - Incompatible because this component declares an API of a component, as well as attribute &apos;com.android.build.api.attributes.BuildTypeAttr&apos; with value &apos;debug&apos; and the consumer needed a runtime of a component, as well as attribute &apos;com.android.build.api.attributes.BuildTypeAttr&apos; with value &apos;beta&apos;&#10;          - Variant &apos;debugRuntimeElements&apos; capability X1:lingcast_for_android:unspecified declares a runtime of a component, preferably optimized for Android, as well as attribute &apos;com.android.build.api.attributes.AgpVersionAttr&apos; with value &apos;7.2.2&apos;, attribute &apos;org.jetbrains.kotlin.platform.type&apos; with value &apos;androidJvm&apos;:&#10;              - Incompatible because this component declares a component, as well as attribute &apos;com.android.build.api.attributes.BuildTypeAttr&apos; with value &apos;debug&apos; and the consumer needed a component, as well as attribute &apos;com.android.build.api.attributes.BuildTypeAttr&apos; with value &apos;beta&apos;&#10;          - Variant &apos;releaseApiElements&apos; capability X1:lingcast_for_android:unspecified declares a component, preferably optimized for Android, as well as attribute &apos;com.android.build.api.attributes.AgpVersionAttr&apos; with value &apos;7.2.2&apos;, attribute &apos;org.jetbrains.kotlin.platform.type&apos; with value &apos;androidJvm&apos;:&#10;              - Incompatible because this component declares an API of a component, as well as attribute &apos;com.android.build.api.attributes.BuildTypeAttr&apos; with value &apos;release&apos; and the consumer needed a runtime of a component, as well as attribute &apos;com.android.build.api.attributes.BuildTypeAttr&apos; with value &apos;beta&apos;&#10;          - Variant &apos;releaseRuntimeElements&apos; capability X1:lingcast_for_android:unspecified declares a runtime of a component, preferably optimized for Android, as well as attribute &apos;com.android.build.api.attributes.AgpVersionAttr&apos; with value &apos;7.2.2&apos;, attribute &apos;org.jetbrains.kotlin.platform.type&apos; with value &apos;androidJvm&apos;:&#10;              - Incompatible because this component declares a component, as well as attribute &apos;com.android.build.api.attributes.BuildTypeAttr&apos; with value &apos;release&apos; and the consumer needed a component, as well as attribute &apos;com.android.build.api.attributes.BuildTypeAttr&apos; with value &apos;beta&apos;&#10;&#10;* Try:&#10;&gt; Run with --stacktrace option to get the stack trace.&#10;&gt; Run with --info or --debug option to get more log output.&#10;&gt; Run with --scan to get full insights.&#10;&#10;* Get more help at https://help.gradle.org&#10;&#10;BUILD FAILED in 7s&#10;6 actionable tasks: 1 executed, 5 up-to-date&#10;" />
        
      </testcase>
    
  </testsuite>
</testsuites>
