<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@drawable/base_toast_gradient_bg"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools">

    <TextView
        android:id="@+id/vMsgTv"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:paddingHorizontal="12dp"
        android:paddingVertical="6.5dp"
        android:gravity="center"
        tools:textColor="#FFFFFFFF"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        tools:text="xxx已加入会议" />
</androidx.constraintlayout.widget.ConstraintLayout>