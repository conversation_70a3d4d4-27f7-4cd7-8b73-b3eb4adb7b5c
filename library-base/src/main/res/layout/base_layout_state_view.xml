<?xml version="1.0" encoding="utf-8"?>
<FrameLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:id="@+id/fl_empty_layout"
    android:layout_width="match_parent"
    android:layout_height="match_parent">

    <LinearLayout
        android:id="@+id/rl_empty_container"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:gravity="center"
        android:orientation="vertical"
        android:visibility="visible">

        <ImageView
            android:id="@+id/vDefIv"
            android:layout_width="151dp"
            android:layout_marginTop="-40dp"
            android:layout_height="151dp"
            android:src="@drawable/network_img_unpty" />

        <TextView
            android:id="@+id/tv_no_data"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_gravity="center"
            android:layout_marginTop="6dp"
            android:gravity="center"
            tools:text="网络异常，请检查网络"
            android:textColor="#595959"
            android:textSize="15sp" />
        
        <androidx.constraintlayout.widget.ConstraintLayout
            android:layout_width="wrap_content"
            android:id="@+id/ll_retry"
            android:minHeight="36dp"
            android:minWidth="128dp"
            android:layout_marginTop="12dp"
            android:background="@drawable/base_rect_white_18_grey_corner"
            android:layout_height="wrap_content">

            <TextView
                android:id="@+id/tv_retry"
                android:textSize="13sp"
                android:textColor="#000000"
                app:layout_constraintLeft_toLeftOf="parent"
                app:layout_constraintRight_toRightOf="parent"
                app:layout_constraintTop_toTopOf="parent"
                app:layout_constraintBottom_toBottomOf="parent"
                android:text="@string/common_retry"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content">

            </TextView>
        </androidx.constraintlayout.widget.ConstraintLayout>


    </LinearLayout>

    <ImageView
        android:id="@+id/vLoadingIv"
        android:layout_width="110dp"
        android:layout_height="110dp"
        android:layout_gravity="center"
        android:visibility="gone"
        android:src="@drawable/base_ic_loading" />

</FrameLayout>
