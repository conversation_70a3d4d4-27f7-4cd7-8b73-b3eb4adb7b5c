<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="wrap_content"
    android:layout_height="wrap_content"
    android:background="@drawable/base_rect_000000_aa_12">


    <ImageView
        android:id="@+id/vWarnImage"
        app:layout_constraintTop_toTopOf="parent"
        android:layout_marginTop="16dp"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintLeft_toLeftOf="parent"
        android:layout_marginLeft="60dp"
        android:layout_marginRight="60dp"
        android:src="@mipmap/pop_scran_icon_notice"
        android:layout_width="40dp"
        android:layout_height="40dp">

    </ImageView>

    <TextView
        android:id="@+id/vMsgTv"
        android:layout_width="wrap_content"
        android:minWidth="40dp"
        android:maxWidth="235dp"
        android:layout_marginLeft="12dp"
        android:layout_marginRight="12dp"
        android:layout_height="wrap_content"
        android:paddingVertical="10dp"
        android:gravity="center"
        android:textColor="#FFFFFFFF"
        android:layout_marginTop="6dp"
        android:textSize="13sp"
        android:layout_marginBottom="12dp"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/vWarnImage"
        tools:text="无效二维码" />

</androidx.constraintlayout.widget.ConstraintLayout>