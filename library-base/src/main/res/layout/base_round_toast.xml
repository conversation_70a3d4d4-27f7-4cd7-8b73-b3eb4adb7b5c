<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="wrap_content"
    android:layout_height="wrap_content"
    android:minHeight="32dp"
    android:paddingVertical="8dp"
    android:background="@drawable/base_rect_cc000000_16_corner">

    <TextView
        android:id="@+id/vMsgTv"
        android:layout_width="wrap_content"
        android:maxWidth="235dp"
        android:layout_height="wrap_content"
        android:paddingHorizontal="12dp"
        android:gravity="center"
        android:textColor="#FFFFFFFF"
        android:textSize="12sp"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        tools:text="账号与密码不符，确认后再次输入！" />

</androidx.constraintlayout.widget.ConstraintLayout>