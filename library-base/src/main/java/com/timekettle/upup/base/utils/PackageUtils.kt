package com.timekettle.upup.base.utils

import android.content.Context
import com.timekettle.upup.base.BaseApp

/**
 * App包工具类
 *
 */
object PackageUtils {

    /**
     * 判断指定app是否已经安装
     * 废弃，不允许使用这个方法
     * @param packageName String 指定app的包名
     * @return Boolean 是否安装
     */


    /**
     * 启动一个指定包名[packageName]的app
     *
     * @param context Context 上下文
     * @param packageName String 需要启动的app包名
     */
    fun startApp(context: Context, packageName: String) {
        // 获取包管理器
        val pm = BaseApp.context.packageManager
        val intent = pm.getLaunchIntentForPackage(packageName)
        context.startActivity(intent)
    }


}