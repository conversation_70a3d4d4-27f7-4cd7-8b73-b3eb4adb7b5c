package com.timekettle.upup.base

import android.annotation.SuppressLint
import android.app.Activity
import android.content.Context
import android.util.Log
import androidx.lifecycle.ViewModelProvider
import androidx.lifecycle.ViewModelStore
import androidx.lifecycle.ViewModelStoreOwner
import androidx.multidex.MultiDexApplication
import com.timekettle.upup.base.app.ActivityLifecycleCallbacksImpl
import com.timekettle.upup.base.app.ActivityStackManager
import com.timekettle.upup.base.app.LoadModuleProxy
import com.timekettle.upup.base.constant.LogTAG
import com.timekettle.upup.base.utils.logD
import kotlinx.coroutines.*
import kotlin.system.exitProcess
import kotlin.system.measureTimeMillis

/**
 * 自定义 Application 基类
 *
 * <AUTHOR>
 * @since 2022/04/15
 */
open class BaseApp : MultiDexApplication(), ViewModelStoreOwner {

    private val mLoadModuleProxy by lazy(mode = LazyThreadSafetyMode.NONE) { LoadModuleProxy() }
    private lateinit var mAppViewModelStore: ViewModelStore
    private var mFactory: ViewModelProvider.Factory? = null
    private val activityList = mutableListOf<Activity>()

    companion object {
        @SuppressLint("StaticFieldLeak")
        @JvmStatic
        @get:JvmName("context")
        lateinit var context: Context
            private set

        @SuppressLint("StaticFieldLeak")
        @JvmStatic
        @get:JvmName("application")
        lateinit var application: BaseApp
            private set

        // 全局CoroutineScope
        val mCoroutineScope by lazy(mode = LazyThreadSafetyMode.SYNCHRONIZED) {
            CoroutineScope(
                SupervisorJob()
                        + Dispatchers.Default
                        + CoroutineName("BaseApplicationJob")
                        + CoroutineExceptionHandler { _, throwable ->
                    Log.d(LogTAG.APP_CREATE_TAG, throwable.message ?: "error")
                    throwable.printStackTrace()
                })
        }
    }

    override fun attachBaseContext(base: Context) {
        super.attachBaseContext(base)
        context = base
        application = this
        mLoadModuleProxy.onAttachBaseContext(base)
    }


    override fun onCreate() {
        super.onCreate()

        // 全局监听 Activity 生命周期
        registerActivityLifecycleCallbacks(ActivityLifecycleCallbacksImpl())

        // 策略初始化第三方依赖
        initDepends()
        //创建ViewModelStore
        mAppViewModelStore = ViewModelStore()
        mLoadModuleProxy.onCreate(this)

        // 初始化崩溃处理
//        initCrashHandler()
    }

    /**
     * 初始化第三方依赖
     */
    private fun initDepends() {
        // 开启一个 Default Coroutine 进行初始化不会立即使用的第三方
        mCoroutineScope.launch(Dispatchers.Default) {
            delay(600)
            mLoadModuleProxy.initByBackstage()
        }
//        mLoadModuleProxy.initByFrontDesk()
        // 前台初始化
        val allTimeMillis = measureTimeMillis {
            val depends = mLoadModuleProxy.initByFrontDesk()
            var dependInfo: String
            depends.forEach {
                val dependTimeMillis = measureTimeMillis { dependInfo = it() }
                Log.d(LogTAG.APP_CREATE_TAG, "initDepends: $dependInfo : $dependTimeMillis ms")
            }
        }
        logD("初始化完成 initApplication $allTimeMillis ms")

    }


    private fun initCrashHandler() {
        Thread.setDefaultUncaughtExceptionHandler { _, _ ->
            run {
                ActivityStackManager.finishAllActivity()
                android.os.Process.killProcess(android.os.Process.myPid());
                exitProcess(1);
            }
        }
    }



    /**
     * 获取一个全局的ViewModel
     */
    fun getAppViewModelProvider(): ViewModelProvider {
        return ViewModelProvider(this, this.getAppFactory())
    }

    private fun getAppFactory(): ViewModelProvider.Factory {
        if (mFactory == null) {
            mFactory = ViewModelProvider.AndroidViewModelFactory.getInstance(this)
        }
        return mFactory as ViewModelProvider.Factory
    }
    override fun onTerminate() {
        super.onTerminate()
        mLoadModuleProxy.onTerminate(this)
        mCoroutineScope.cancel()
    }

    override fun getViewModelStore(): ViewModelStore {
        return mAppViewModelStore
    }

    fun addActivity(activity: Activity) {
        if (!activityList.contains(activity)) {
            activityList.add(activity)
        }
    }

    fun removeActivity(activity: Activity) {
        if (activityList.contains(activity)) {
            activityList.remove(activity)
        }
    }

    fun removeAllActivity() {
        activityList.forEach {
            it.finish()
        }
    }

}