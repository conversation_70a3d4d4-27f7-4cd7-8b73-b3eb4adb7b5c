package com.timekettle.upup.base.utils
import android.os.Handler
import android.os.Looper
import android.view.View
import com.timekettle.upup.base.ktx.ViewClickDelay
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.Job
import kotlinx.coroutines.delay
import kotlinx.coroutines.launch

/**
 * 防抖器
 * 用法：可以参照项目内的代码
 */
class Debouncer(private val delayMillis: Long) {

    private var lastTime:Long = 0L

    fun debounce(action: () -> Unit) {
        val currentTime = System.currentTimeMillis()
        if (currentTime - lastTime >=  delayMillis) {
            lastTime = currentTime
            action()
        }else{
            logD("触发了$delayMillis ms之内的防抖！")
        }
    }
}