package com.timekettle.upup.base.mvvm.v

import android.os.Bundle
import android.util.Log
import android.view.View
import android.view.WindowManager
import android.widget.ImageView
import androidx.annotation.ColorInt
import androidx.appcompat.app.AppCompatActivity
import androidx.core.splashscreen.SplashScreen
import androidx.viewbinding.ViewBinding
import com.alibaba.android.arouter.launcher.ARouter
import com.bumptech.glide.Glide
import com.gyf.immersionbar.BarHide
import com.gyf.immersionbar.ImmersionBar
import com.timekettle.upup.base.BaseApp
import com.timekettle.upup.base.R
import com.timekettle.upup.base.ktx.observeStateLayout
import com.timekettle.upup.base.listener.network.AutoRegisterNetListener
import com.timekettle.upup.base.listener.network.INetworkStateChangeListener
import com.timekettle.upup.base.mvvm.vm.BaseViewModel
import com.timekettle.upup.base.utils.BindingReflex
import com.timekettle.upup.base.utils.EventBusRegister
import com.timekettle.upup.base.utils.EventBusUtils
import com.timekettle.upup.base.widget.StateLayout
import java.io.BufferedReader
import java.io.InputStreamReader

/**
 * Activity基类 与业务无关
 *
 * <AUTHOR>
 * @since 2022/04/15
 */
abstract class BaseFrameActivity<VB : ViewBinding, VM : BaseViewModel> : AppCompatActivity(),
    FrameView<VB>, INetworkStateChangeListener {
    var splashScreen: SplashScreen? = null
    protected val mBinding: VB by lazy(mode = LazyThreadSafetyMode.NONE) {
        BindingReflex.reflexViewBinding(javaClass, layoutInflater)
    }

    protected abstract val mViewModel: VM
    open var statusBarColorString = "#ffffff" // 状态栏的颜色，子类可以修改

    override fun onCreate(savedInstanceState: Bundle?) {
        // 处理闪屏过渡
//        splashScreen = installSplashScreen()
        //        getSupportActionBar().hide();
        //所有activity全屏显示
//        supportRequestWindowFeature(Window.FEATURE_NO_TITLE)
        this.window.setFlags(
            WindowManager.LayoutParams.FLAG_FULLSCREEN, WindowManager.LayoutParams.FLAG_FULLSCREEN
        )
//        window.decorView.systemUiVisibility = View.SYSTEM_UI_FLAG_FULLSCREEN
//        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.R) {
//            val controller: WindowInsetsController? = window.insetsController
//            controller?.hide(WindowInsets.Type.statusBars())
//        }
        super.onCreate(savedInstanceState)

        BaseApp.application.addActivity(this)

        setContentView(mBinding.root)
        // ARouter 依赖注入
        ARouter.getInstance().inject(this)
        // 注册EventBus
        if (javaClass.isAnnotationPresent(EventBusRegister::class.java)) EventBusUtils.register(this)
        // 如果是手机，就禁用横屏
//        if (isPhoneOrTablet() == "Phone")
//            requestedOrientation = ActivityInfo.SCREEN_ORIENTATION_PORTRAIT;
//        setStatusBar()
        initToolBar()
        obtainLocationPermission()
        mBinding.initView()
//        initStateLayout()
        initNetworkListener()
        initObserve()
        initListener()
        initRequestData()
    }


    override fun onWindowFocusChanged(hasFocus: Boolean) {
        super.onWindowFocusChanged(hasFocus)
//        if (hasFocus) hideSystemUI()
    }

    private fun hideSystemUI() {
        // Enables regular immersive mode.
        // For "lean back" mode, remove SYSTEM_UI_FLAG_IMMERSIVE.
        // Or for "sticky immersive," replace it with SYSTEM_UI_FLAG_IMMERSIVE_STICKY
        window.decorView.systemUiVisibility = (View.SYSTEM_UI_FLAG_IMMERSIVE
                // Set the content to appear under the system bars so that the
                // content doesn't resize when the system bars hide and show.
                or View.SYSTEM_UI_FLAG_LAYOUT_STABLE
                or View.SYSTEM_UI_FLAG_LAYOUT_HIDE_NAVIGATION
                or View.SYSTEM_UI_FLAG_LAYOUT_FULLSCREEN
                // Hide the nav bar and status bar
                or View.SYSTEM_UI_FLAG_HIDE_NAVIGATION
                or View.SYSTEM_UI_FLAG_FULLSCREEN)
    }


    /**
     * 设置状态栏
     * 子类需要自定义时重写该方法即可
     * @return Unit
     */
    open fun setStatusBar() {
        ImmersionBar.with(this)
            .transparentStatusBar()
            .autoNavigationBarDarkModeEnable(true, 0.001f) //自动导航栏图标变色，必须指定导航栏颜色才可以自动变色哦
            .navigationBarColor(statusBarColorString) //导航栏颜色，不写默认黑色
            .fitsSystemWindows(true) // 预留出状态栏的高度，解决状态栏和布局重叠问题
            .hideBar(BarHide.FLAG_HIDE_NAVIGATION_BAR)
            .statusBarColor(statusBarColorString) // 如果预留了状态栏的高度， 那么就需要设置状态栏的颜色
            .statusBarDarkFont(true)   //状态栏字体是深色，不写默认为亮色
            .init()
    }

    /**
     * 初始化ToolBar
     */
    abstract fun initToolBar()

    /**
     * 初始化状态布局
     */
    open fun initStateLayout() {
        findViewById<StateLayout?>(R.id.vStateLayout)?.run {
            findViewById<ImageView>(R.id.vLoadingIv)?.let {
                Glide.with(context).load(R.drawable.base_ic_loading).into(it)
            }
            observeStateLayout(mViewModel.stateViewLD, this)
        }
    }

    /**
     * 初始化view的监听，例如：onClick、回调监听等
     */
    open fun initListener() {

    }


    /**
     * 强制 Android 系统将位置模式设置为“高精度”模（前提是当前应用作为系统及应用）
     * */
    private fun obtainLocationPermission() {
        try {
            // 设置命令，强制将系统的位置设置成高精度模式
            val command = "settings put secure location_mode 3"
            // 在 Runtime 中执行命令
            val process = Runtime.getRuntime().exec(command)
            // 获取命令输出结果
            val reader = BufferedReader(InputStreamReader(process.inputStream))
            var line: String? = ""
            while (reader.readLine().also { line = it } != null) {
                Log.d("Command Output", line!!)
            }
            reader.close()
            // 等待命令执行完成
            process.waitFor()
        } catch (e: Exception) {
            e.printStackTrace()
        }
    }

    /**
     * 设置状态布局背景颜色
     * @param color Int
     */
    open fun setStateLayoutBg(@ColorInt color: Int) {
        findViewById<StateLayout?>(R.id.vStateLayout)?.setBgColor(color)
    }

    /**
     * 初始化网络状态监听
     * @return Unit
     */
    private fun initNetworkListener() {
        lifecycle.addObserver(AutoRegisterNetListener(this))
    }

    /**
     * 网络类型更改回调
     * @param type Int 网络类型
     * @return Unit
     */
    override fun networkTypeChange(type: Int) {}

    /**
     * 网络连接状态更改回调
     * @param isConnected Boolean 是否已连接
     * @return Unit
     */
    override fun networkConnectChange(isConnected: Boolean) {
//        showToast(if (isConnected) "网络已连接" else "网络已断开")
    }

    override fun onDestroy() {
        if (javaClass.isAnnotationPresent(EventBusRegister::class.java)) EventBusUtils.unRegister(
            this
        )
        super.onDestroy()
        BaseApp.application.removeActivity(this)
    }
}