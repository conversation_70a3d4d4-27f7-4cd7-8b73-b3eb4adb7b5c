package com.timekettle.upup.base.ktx

import android.animation.Animator
import android.animation.ValueAnimator
import android.view.View

/**
 * 将View变得完全透明（不可见）
 */
inline fun View.animateAlpha0(
    duration: Long = 400,
    listener: Animator.AnimatorListener? = null,
    crossinline updateCall: (v: Float) -> Unit = {}
): ValueAnimator? {
    var animator: ValueAnimator? = null
    if (isGone) return null
    post {
        animator = ValueAnimator.ofFloat(1f, 0f).apply {
            addUpdateListener {
                alpha = it.animatedValue as Float
                updateCall.invoke(it.animatedFraction)
                if (alpha == 0f) invisible()
            }
            if (listener != null) addListener(listener)
            setDuration(duration)
            start()
        }
    }
    return animator
}

/**
 * 将View变得可见
 */
inline fun View.animateAlpha1(
    duration: Long = 100,
    listener: Animator.AnimatorListener? = null,
    crossinline updateCall: (v: Float) -> Unit = {}
): ValueAnimator? {
    if (isGone) return null
    var animator: ValueAnimator? = null
    post {
        visible()
        alpha = 0f
        animator = ValueAnimator.ofFloat(0f, 1f).apply {
            addUpdateListener {
                alpha = it.animatedValue as Float
                updateCall.invoke(it.animatedFraction)
            }
            if (listener != null) addListener(listener)
            setDuration(duration)
            start()
        }
    }
    return animator
}