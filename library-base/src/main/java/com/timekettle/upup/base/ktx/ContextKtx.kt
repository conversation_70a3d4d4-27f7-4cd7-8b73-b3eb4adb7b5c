package com.timekettle.upup.base.ktx

import android.app.Activity
import android.content.Context
import android.content.Intent
import android.os.Build.VERSION.SDK_INT
import android.os.Bundle
import android.os.Parcelable
import androidx.core.os.bundleOf
import androidx.fragment.app.Fragment

/**
 * Context相关扩展方法
 * <AUTHOR>
 * @since 2021/10/21
 * reified关键字是用于修饰内联函数的泛型，泛型被修饰后，能从泛型拿到泛型的Class对象，类型参数不会被擦除，
 * 这与Java有所不同，Java需要泛型和泛型的Class类型时，是要把Class传过来的。
 * 由此我们可以简化启动一个Activity代码
 */


inline fun <reified T : Activity> Context.startKtxActivity(vararg params: Pair<String, String>) {
    val intent = Intent(this, T::class.java)
    params.forEach { intent.putExtra(it.first, it.second) }
    startActivity(intent)
}



inline fun <reified T : Activity> Context.openActivity1(vararg params: Pair<String, Any?>) {
    val intent = Intent(this, T::class.java)
    intent.putExtras(bundleOf(*params))
    this.startActivity(intent)
}


// getParcelableExtra 在API 33 已经被弃用了
inline fun <reified T : Parcelable> Intent.parcelable(key: String): T? = when {
    SDK_INT >= 33 -> getParcelableExtra(key, T::class.java)
    else -> @Suppress("DEPRECATION") getParcelableExtra(key) as? T
}

inline fun <reified T : Parcelable> Bundle.parcelable(key: String): T? = when {
    SDK_INT >= 33 -> getParcelable(key, T::class.java)
    else -> @Suppress("DEPRECATION") getParcelable(key) as? T
}
