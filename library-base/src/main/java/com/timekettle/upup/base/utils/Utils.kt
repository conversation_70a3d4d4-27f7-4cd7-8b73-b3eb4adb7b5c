package com.timekettle.upup.base.utils

import android.R.id.text2
import android.content.Context
import android.content.res.Configuration
import android.graphics.Color
import android.provider.Settings
import android.text.Spannable
import android.text.SpannableString
import android.text.SpannableStringBuilder
import android.text.style.ForegroundColorSpan
import android.util.DisplayMetrics
import android.view.Display
import android.view.Gravity
import android.view.LayoutInflater
import android.view.WindowManager
import android.widget.TextView
import android.widget.Toast
import com.alibaba.android.arouter.launcher.ARouter
import com.blankj.utilcode.util.ConvertUtils
import com.blankj.utilcode.util.ToastUtils
import com.timekettle.upup.base.BaseApp
import com.timekettle.upup.base.BuildConfig
import com.timekettle.upup.base.R
import kotlinx.coroutines.*
import kotlinx.coroutines.flow.*
import java.util.*
import kotlin.math.pow
import kotlin.math.sqrt


/**
 * 以顶层函数存在的常用工具方法
 * startPolling() -> 开启一个轮询
 * toast() -> Toast
 * sendEvent() -> 发送普通EventBus事件
 * startActivityByRoute() -> 阿里路由不带参数跳转
 *
 */
/**************************************************************************************************/
/**
 * 使用 Flow 做的简单的轮询
 * 请使用单独的协程来进行管理该 Flow
 * Flow 仍有一些操作符是实验性的 使用时需添加 @InternalCoroutinesApi 注解
 * @param intervals 轮询间隔时间/毫秒
 * @param block 需要执行的代码块
 */
suspend inline fun startPolling(intervals: Long, crossinline block: () -> Unit) {
    flow {
        while (true) {
            delay(intervals)
            emit(0)
        }
    }
        .catch { logE("startPolling: $it") }
        .flowOn(Dispatchers.Default)  // 指定 flow{}、catch{}的线程模型，collect{}的线程取决于父协程的线程模型
        .collect { block.invoke() }
}

/**
 * 开始立刻循环
 * @param intervals Long
 * @param block Function0<Unit>
 */
suspend inline fun startImmediatelyPolling(intervals: Long, crossinline block: () -> Unit) {
    flow {
        while (true) {
            emit(0)
            delay(intervals)
        }
    }
        .catch { logE("startPolling: $it") }
        .flowOn(Dispatchers.Default)  // 指定 flow{}、catch{}的线程模型，collect{}的线程取决于父协程的线程模型
        .collect { block.invoke() }
}
/**************************************************************************************************/

/**
 * 发送普通EventBus事件
 */
fun sendEvent(event: Any) = EventBusUtils.postEvent(event)

/**************************************************************************************************/
/**
 * 阿里路由不带参数跳转
 * @param routerUrl String 路由地址
 */
fun startActivityByRoute(routerUrl: String) {
    ARouter.getInstance().build(routerUrl).navigation()
}


/**************************************************************************************************/

private var mToast: Toast? = null

/**
 * 显示toast
 * @param text String
 */
fun showToast(
    text: String, durations: Int = Toast.LENGTH_SHORT, gravity: Int = Gravity.BOTTOM,
    color: Int = Color.WHITE
) {
    BaseApp.mCoroutineScope.launch(Dispatchers.Main) {
        ToastUtils.make()
            .setBgResource(R.drawable.base_toast_gradient_bg)
            .setGravity(gravity, 0, ConvertUtils.dp2px(20f))
            .setTextColor(color)
            .show(text)
    }
}

/**
 * 显示 Spannable的toast
 * 场景：例如某段文本种的某个文字高亮
 */
fun showSpannableToast(
    spannableText: SpannableString,
    gravity: Int = Gravity.BOTTOM,
) {
    BaseApp.mCoroutineScope.launch(Dispatchers.Main) {
        val layout = LayoutInflater.from(BaseApp.context).inflate(R.layout.base_meeting_toast, null)
        var text = layout.findViewById<TextView>(R.id.vMsgTv)
        text.text = spannableText
        ToastUtils.make()
            .setGravity(gravity, 0, ConvertUtils.dp2px(20f))
            .show(layout)
    }
}

/**
 * 显示两个text不同颜色toast
 * */
fun showMultiColorToast(
    text1: String,
    text2: String,
    durations: Int = Toast.LENGTH_SHORT,
    gravity: Int = Gravity.BOTTOM
) {
    BaseApp.mCoroutineScope.launch(Dispatchers.Main) {
        val layout = LayoutInflater.from(BaseApp.context).inflate(R.layout.base_meeting_toast, null)
        var text = layout.findViewById<TextView>(R.id.vMsgTv)

        val spannableText = SpannableStringBuilder()

        spannableText.append(text1)
        spannableText.setSpan(
            ForegroundColorSpan(Color.parseColor("#FF43A5FF")),
            0,
            text1.length,
            Spannable.SPAN_EXCLUSIVE_EXCLUSIVE
        )

        // 添加第二个字符串，并设置颜色
        spannableText.append(" $text2")
        spannableText.setSpan(
            ForegroundColorSpan(Color.parseColor("#FFFFFFFF")),
            text1.length,
            spannableText.length,
            Spannable.SPAN_EXCLUSIVE_EXCLUSIVE
        )

        text.text = spannableText

        ToastUtils.make()
//            .setBgResource(R.drawable.base_toast_gradient_bg)
            .setGravity(gravity, 0, ConvertUtils.dp2px(20f))
            .show(layout)
    }

}


/**
 * 显示调试的Toast
 * @param text String
 * @param durations Int
 * @param gravity Int
 */
fun showDebugToast(
    text: String,
    gravity: Int = Gravity.BOTTOM
) {
    if (!BuildConfig.DEBUG) return
    ToastUtils.make()
        .setBgResource(R.drawable.base_toast_gradient_background)
        .setGravity(gravity, 0, -ConvertUtils.dp2px(20f))
        .setTextColor(Color.WHITE)
//        .show("\uD83D\uDE05\n" + text)
        .show(text)
}

// 显示顶部滑入的Toast提示，例如离线的开关
fun showTopTipToast(
    text: String,
    durations: Int = Toast.LENGTH_SHORT,
    gravity: Int = Gravity.TOP
) {
    if (!BuildConfig.DEBUG) return
    BaseApp.mCoroutineScope.launch(Dispatchers.Main) {
        mToast?.cancel()
        mToast = Toast(BaseApp.context).apply {
            setGravity(gravity, 0, ConvertUtils.dp2px(50f))
            duration = durations
            val layout =
                LayoutInflater.from(BaseApp.context).inflate(R.layout.base_round_toast, null)
            layout.findViewById<TextView>(R.id.vMsgTv)?.text = text
            view = layout
            show()
        }
    }
}


/**
 * 倒计时
 * @param total Int
 * @param onTick Function1<Int, Unit>
 * @param onFinish Function0<Unit>
 * @param scope LifecycleCoroutineScope
 * @return Job
 */
fun countDownCoroutines(
    total: Int,
    scope: CoroutineScope,
    onTick: ((Int) -> Unit)? = null,
    onStart: (() -> Unit)? = null,
    onFinish: (() -> Unit)? = null,
): Job {
    return flow {
        for (i in total downTo 0) {
            emit(i)
            delay(1000)
        }
    }.flowOn(Dispatchers.IO)
        .onStart { onStart?.invoke() }
        .onCompletion {
            onFinish?.invoke()
        }
        .onEach { onTick?.invoke(it) }
        .launchIn(scope)

}

fun countDownCoroutines(
    scope: CoroutineScope,
    onTick: (Int) -> Unit,
): Job {
    return flow {
        for (i in 11 downTo 0) {
            emit(i)
            delay(500)
        }
    }.flowOn(Dispatchers.IO)
        .onEach { onTick.invoke(it) }
        .launchIn(scope)

}

