package com.timekettle.upup.base.mvvm.vm

/**
 * https://stackoverflow.com/questions/44208618/how-to-handle-error-states-with-livedata
 * 对有状态的数据的包装
 */
data class StateData<out T>(
    val status: DataStatus,
    val errCode: Int? = -1,
    val data: T?,
    val message: String = ""
) {
    companion object { // 当包含它的类被加载时就初始化了的
        fun <T> success(data: T?): StateData<T> {
            return StateData(DataStatus.SUCCESS, data = data)
        }

        fun <T> error(code: Int = -1, msg: String, data: T? = null): StateData<T> {
            return StateData(DataStatus.ERROR,errCode = code, data = data, message = msg)
        }

        fun <T> loading(data: T?): StateData<T> {
            return StateData(DataStatus.LOADING, data = data)
        }
    }

    enum class DataStatus {
        CREATED, SUCCESS, ERROR, LOADING, COMPLETE
    }
}

