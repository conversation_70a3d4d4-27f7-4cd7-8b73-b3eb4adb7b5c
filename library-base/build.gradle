apply from: '../gradle-script/base-library.gradle'

android {
    resourcePrefix "base_"

    buildFeatures {
        viewBinding = true
    }
}

dependencies {
    implementation fileTree(dir: "libs", include: ["*.jar"])

    api AndroidLibs.AppCompat
    api AndroidLibs.CoreKtx
    api AndroidLibs.ActivityKtx
    api AndroidLibs.FragmentKtx
    api AndroidLibs.MultiDex
    api AndroidLibs.MaterialDesign
    api AndroidLibs.ConstraintLayout
    api AndroidLibs.SplashScreen


    api JetPackLibs.ViewModel
    api JetPackLibs.ViewModelSavedState
    api JetPackLibs.LiveData
    api JetPackLibs.Runtime
    api JetPackLibs.LifecycleService
    api JetPackLibs.HiltCore
    api JetPackLibs.Palette
    api JetPackLibs.Room
    api JetPackLibs.RoomCoroutineKtx

    api KotlinLibs.Kotlin
    api KotlinLibs.CoroutinesCore
    api KotlinLibs.CoroutinesAndroid

    api NetworkLibs.OkHttp
    api NetworkLibs.OkHttpInterceptorLogging
    api NetworkLibs.Retrofit
    api NetworkLibs.RetrofitConverterGson
    api NetworkLibs.Gson

//    api UILibs.AutoSize
    api UILibs.RecyclerViewAdapter
    api UILibs.Glide
    api UILibs.Lottie
    api UILibs.PictureSelector
    api UILibs.PictureCrop
    api UILibs.PictureCompress
    api UILibs.ShadowLayout
    api UILibs.ImmersionBar
    api UILibs.ImmersionBarKtx

    api UILibs.RefreshKernel
    api UILibs.RefreshHeader
    api UILibs.RefreshFooter
    api UILibs.RoundImageView
    api UILibs.AutoLinkTextView
    api UILibs.ToolTip
    api UILibs.CZXing
    api UILibs.Alert
    api UILibs.Pag
    api UILibs.DialogX
    api UILibs.BasePop
    api UILibs.Markwon

    api OtherLibs.MMKV
    api OtherLibs.ARoute
    api OtherLibs.EventBus
    api OtherLibs.XXPermission
    api OtherLibs.AutoService
    api OtherLibs.Logger
    api OtherLibs.UtilCode
    api OtherLibs.GoogleBilling
    api OtherLibs.GooglePlayServicesAuth

    debugApi OtherLibs.Leakcanary
    debugApi OtherLibs.debugChucker
    releaseApi OtherLibs.releaseChucker

    api SDKLibs.TencentBugly
    api SDKLibs.TencentTBSX5
    api SDKLibs.Fastjson
//    api SDKLibs.WechatSDK
//    api SDKLibs.FaceBookSDK
    api(SDKLibs.SensorsAnalyticsSDK) { // 仅依赖全埋点模块
        exclude(group:'com.sensorsdata.analytics.android', module:'advert') // 广告模块
        exclude(group:'com.sensorsdata.analytics.android', module:'encrypt') // 加密模块
        exclude(group:'com.sensorsdata.analytics.android', module:'exposure') // 曝光模块
        exclude(group:'com.sensorsdata.analytics.android', module:'visual') // 可视化模块
    }

    kapt JetPackLibs.LifecycleCompilerAPT
    kapt JetPackLibs.HiltApt
    kapt JetPackLibs.HiltAndroidx
    kapt JetPackLibs.RoomCompiler

    kapt OtherLibs.ARouteCompiler
    kapt OtherLibs.EventBusAPT
    kapt OtherLibs.AutoServiceAnnotations
}