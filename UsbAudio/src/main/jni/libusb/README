libusb
======

libusb is a library for USB device access from Linux, Mac OS X,
Windows and OpenBSD/NetBSD userspace.
It is written in C and licensed under the GNU Lesser General Public
License version 2.1 or, at your option, any later version (see COPYING).

libusb is abstracted internally in such a way that it can hopefully
be ported to other operating systems. Please see the PORTING file
for more information.

libusb homepage:
http://libusb.info/

Developers will wish to consult the API documentation:
http://api.libusb.info

Use the mailing list for questions, comments, etc:
http://mailing-list.libusb.info

- <PERSON> <<EMAIL>>
- <PERSON> <<EMAIL>>
- <PERSON><PERSON> <<EMAIL>>
- <PERSON><PERSON><PERSON> <<EMAIL>>
- <PERSON> <<EMAIL>>
(Please use the mailing list rather than mailing developers directly)
