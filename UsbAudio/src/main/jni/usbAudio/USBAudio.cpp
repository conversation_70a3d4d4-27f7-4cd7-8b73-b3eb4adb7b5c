//
// Created by <PERSON><PERSON> on 2021/11/2.
//
#include <jni.h>

#include <unistd.h>
#include <cerrno>
#include <cstdlib>
#include <cstdio>
#include <csignal>
#include <stdbool.h>
#include <android/log.h>
#include <cstring>
#include "../libusb/libusb/libusb.h"
#include "uac.h"
#include "USBAudio.h"
#include "../utilbase.h"


#define LOGD(...) \
    __android_log_print(ANDROID_LOG_DEBUG, "USBAudio", __VA_ARGS__)

#define UNUSED __attribute__((unused))


int USB_REQ_CS_ENDPOINT_SET = // 0x22
        LIBUSB_ENDPOINT_OUT |
        LIBUSB_REQUEST_TYPE_CLASS |
        LIBUSB_RECIPIENT_ENDPOINT;
int USB_REQ_CS_ENDPOINT_GET = // 0xa2
        LIBUSB_ENDPOINT_IN |
        LIBUSB_REQUEST_TYPE_CLASS |
        LIBUSB_RECIPIENT_ENDPOINT;


//private
int USBAudio::claim_if(libusb_device_handle *devh, int interface_number,int  bAlternateSetting) {
    int r = 0;

    if UNLIKELY(!uac_handler) return -1;
    //detach_kernel_driver
    r = libusb_kernel_driver_active(devh, interface_number);
    if (r == 1) { //find out if kernel driver is attached
        LOGD("Kernel Driver Active\n");
        if (libusb_detach_kernel_driver(devh, interface_number) == 0) //detach it
            LOGD("Kernel Driver Detached!\n");
    }
    LOGD("kernel detach interface_number:%d\n", interface_number);

    //claim_interface
    r = libusb_claim_interface(devh, interface_number);
    LOGD("claim_interface r:%s\n", libusb_error_name(r));
    if (r != 0) {
        LOGD("Error claiming interface: %s\n", libusb_error_name(r));
        return r;
    }
    LOGD("claim_interface r:%d\n", r);
    if (r < 0) {
        return r;
    }
    return 0;
}
int USBAudio::release_if(libusb_device_handle *devh, int interface_number,int  bAlternateSetting) {
    int r = 0;

    if UNLIKELY(!uac_handler) return -1;
    r = libusb_release_interface(devh, interface_number);
	if (0 == r) {
		/* Reattach any kernel drivers that were disabled when we claimed this interface */
		r = libusb_attach_kernel_driver(devh, interface_number);

		if LIKELY(!r) {
			LOGD("reattached kernel driver to interface %d", interface_number);
		} else if (r == LIBUSB_ERROR_NOT_FOUND || r == LIBUSB_ERROR_NOT_SUPPORTED) {
			r = 0;  /* NOT_FOUND and NOT_SUPPORTED are OK: nothing to do */
		} else {
			LOGD("error reattaching kernel driver to interface %d: %s",interface_number, libusb_error_name(r));
		}
	}
    if (r != 0) {
        return -1;
    }
    return 0;
}
int USBAudio::scan_audio_interface(libusb_device *usbDev) {
    int r = 0;
    const struct libusb_endpoint_descriptor *endpoint;

    if UNLIKELY(!uac_handler) return -1;
//    r = libusb_get_config_descriptor(usbDev, 0, &uac_handler->uac_config);
    LOGD("scan_audio_interface,bNumInterfaces=%d",uac_handler->uac_config->bNumInterfaces);
    for (int interface_idx = 0; interface_idx < uac_handler->uac_config->bNumInterfaces; interface_idx++) {
        i_face = &uac_handler->uac_config->interface[interface_idx];
        LOGD("scan_audio_interface :interface_idx=%d,num_altsetting=%d",interface_idx,i_face->num_altsetting);
        for (int i = 0; i < i_face->num_altsetting; ++i) {
            if_desc = &i_face->altsetting[i];
            LOGD("scan_audio_interface :interface altsetting idx=%d,bInterfaceClass=%d,bInterfaceSubClass=%d,bNumEndpoints=%d,bInterfaceNumber=%d",i,if_desc->bInterfaceClass,if_desc->bInterfaceSubClass,if_desc->bNumEndpoints,if_desc->bInterfaceNumber);
        	if (if_desc->bInterfaceClass == LIBUSB_CLASS_AUDIO/*1*/) {// Audio, Control
                switch (if_desc->bInterfaceSubClass) {
                    case 1://USB_SUBCLASS_AUDIOCONTROL
                        uac_handler->captureControlInterface = if_desc->bInterfaceNumber;
                        LOGD("scan_audio_interface:captureControlInterface=%d\n", uac_handler->captureControlInterface);
                        break;
                    case 2://USB_SUBCLASS_AUDIOSTREAMING
                        if (if_desc->bNumEndpoints) {
                            for (int ep_idx = 0; ep_idx < if_desc->bNumEndpoints; ep_idx++) {
                                endpoint = if_desc->endpoint + ep_idx;
                                LOGD("scan_audio_interface:ep_idx=%d,bEndpointAddress=0x%x,bmAttributes=0x%x\n", ep_idx,endpoint->bEndpointAddress,endpoint->bmAttributes);
                                if ((endpoint->bEndpointAddress & LIBUSB_ENDPOINT_IN) ==
                                    LIBUSB_ENDPOINT_IN) {
                                    LOGD("LIBUSB_ENDPOINT_IN set captureInterface");
                                    //获取 FORMAT_TYPE_I 相关数据
                                    set_audio_stream_desc(if_desc);
                                    //赋值
                                    uac_handler->captureInterface = if_desc->bInterfaceNumber;
                                    uac_handler->captureAltSetting = if_desc->bAlternateSetting;
                                    uac_handler->captureEndpoint = endpoint->bEndpointAddress;
                                    uac_handler->captureMaxPacketSize = endpoint->wMaxPacketSize;
                                    LOGD(" captureInterface %d captureControlInterface %d captureMaxPacketSize %d captureAltSetting %d,captureEndpoint %d\n",
                                         uac_handler->captureInterface,
                                         uac_handler->captureControlInterface,
                                         uac_handler->captureMaxPacketSize,
                                         uac_handler->captureAltSetting,uac_handler->captureEndpoint);
                                }else{
                                    LOGD("LIBUSB_ENDPOINT_OUT set playbackInterface");
                                    //鑾峰彇 FORMAT_TYPE_I 鐩稿叧鏁版嵁
                                    set_audio_stream_desc(if_desc);
                                    //璧嬪€?
                                    uac_handler->playbackInterface = if_desc->bInterfaceNumber;
                                    uac_handler->playbackAltSetting = if_desc->bAlternateSetting;
                                    uac_handler->playbackEndpoint = endpoint->bEndpointAddress;
                                    uac_handler->playbackMaxPacketSize = endpoint->wMaxPacketSize;
                                    LOGD(" playbackInterface %d playbackControlInterface %d playbackMaxPacketSize %d playbackAltSetting %d,playbackEndpoint %d\n",
                                         uac_handler->playbackInterface,
                                         uac_handler->playbackControlInterface ,
                                         uac_handler->playbackMaxPacketSize,
                                         uac_handler->playbackAltSetting,
                                         uac_handler->playbackEndpoint);
                                }

                            }
                        }
                        break;
                }
                //break;
        	}
        }
    }
//    libusb_free_config_descriptor(uac_handler->uac_config);
    return r;
}

void USBAudio::set_audio_stream_desc(const libusb_interface_descriptor *desc) {
    const unsigned char *if_audio_stream_desc = desc->extra;
    auto if_audio_stream_desc_size = desc->extra_length;

    //Remove AudioStreaming Interface Descriptor AS_GENERAL
    int extra_offset = if_audio_stream_desc[0];
    char *buffer = (char *) (if_audio_stream_desc + extra_offset);

    LOGD("set_audio_stream_desc: extra_length=%d,extra_offset=%d\n", if_audio_stream_desc_size,extra_offset);
    //AudioStreaming Interface Descriptor FORMAT_TYPE
    //Get AudioStreaming FORMAT_TYPE/FORMAT_TYPE_I About Audio Information
    char *desc_buffer = (char *) malloc(8);
   if(!desc_buffer)	return;
    memcpy(desc_buffer, buffer, 8);

    //Force desc buffer into audio streaming descriptor
    audio_stream_desc = reinterpret_cast<audio_streaming_descriptor *>(desc_buffer);
    LOGD("audio_stream_desc: bDescriptorSubtype=%d,bFormatType=%d,bNrChannels=%d,bSubframeSize=%d,bBitResolution=%d,bSamFreqType=%d\n", 
		audio_stream_desc->bDescriptorSubtype,audio_stream_desc->bFormatType,audio_stream_desc->bNrChannels,audio_stream_desc->bSubframeSize,audio_stream_desc->bBitResolution,audio_stream_desc->bSamFreqType);

    //Get Audio Sample Rates
    char *sam_freq_buffer = (char *) (buffer + 8);

    for (int j = 0; j < audio_stream_desc->bSamFreqType; ++j) {
        char data[3];
        int index = j * 3;
        data[0] = sam_freq_buffer[index];
        data[1] = sam_freq_buffer[index + 1];
        data[2] = sam_freq_buffer[index + 2];
        int rate = data[0] | (data[1] << 8) | (data[2] << 16);
        LOGD("Get desc rate %d\n", rate);
        if (rate <= 48000) {
            sample_rate = rate;
        }
    }
    free(desc_buffer);
}
int USBAudio::start_capture_stream_transfer() {
    int r = 0;
    int endpoint_bytes_per_packet;
    int packets_per_transfer = MAXNUM_PACKETS;
    int total_transfer_size;
    int transfer_id = 0;
    struct libusb_transfer *transfer;

    if UNLIKELY(!uac_handler) return -1;
    endpoint_bytes_per_packet = uac_handler->captureMaxPacketSize;
    total_transfer_size = uac_handler->captureMaxPacketSize * MAXNUM_PACKETS;
    LOGD("Setup capture the transfers\n");
    LOGD("before fill EndpointAddress:%d, per_packet:%d, packets:%d, total_transfer_size:%d\n",
         uac_handler->captureEndpoint, endpoint_bytes_per_packet, packets_per_transfer, total_transfer_size);
    r = libusb_set_interface_alt_setting(uac_handler->uac_devh, uac_handler->captureInterface,uac_handler->captureAltSetting);//bAlternateSetting for open mic/playback;0 for close mic/playback
    if (r < 0) {
	 LOGD("restrore the altsetting failed r:%s\n", libusb_error_name(r));
        return r;
    }
    uac_handler->captureTotalTransfers = MAXNUM_TRANSFERS;
    //libusb_clear_halt(uac_handler->uac_devh, uac_handler->captureEndpoint);
    for (transfer_id = 0; transfer_id < uac_handler->captureTotalTransfers; ++transfer_id) {
        transfer = (struct libusb_transfer *) libusb_alloc_transfer(packets_per_transfer);
        uac_handler->capture_transfers[transfer_id] = transfer;
        uac_handler->capture_transfer_bufs[transfer_id] = (unsigned char *) malloc(total_transfer_size);
        memset(uac_handler->capture_transfer_bufs[transfer_id], 0, total_transfer_size);
        libusb_fill_iso_transfer(transfer, uac_handler->uac_devh,
                                 uac_handler->captureEndpoint,
                                 uac_handler->capture_transfer_bufs[transfer_id], total_transfer_size,
                                 packets_per_transfer, uac_capture_stream_callback,
                                 (void *) uac_handler, 500);

        libusb_set_iso_packet_lengths(transfer, endpoint_bytes_per_packet);

    }
    for (transfer_id = 0; transfer_id < uac_handler->captureTotalTransfers; transfer_id++) {
        r = libusb_submit_transfer(uac_handler->capture_transfers[transfer_id]);
        if (r != 0) {
            LOGD("capture libusb_submit_transfer failed: %s, errno:%s,transfer_id=%d\n",
                 libusb_error_name(r),
                 strerror(errno),transfer_id);
            break;
        }
	else{
            LOGD("capture libusb_submit_transfer success: transfer_id=%d, transfer=%p\n",transfer_id,uac_handler->capture_transfers[transfer_id]);
	}
    }

    if (r != 0) {
        return r;
    }
    return r;
}

int USBAudio::stop_capture_stream_transfer() {
    int i,r;
    if UNLIKELY(!uac_handler) return -1;

    for (i = 0; i < uac_handler->captureTotalTransfers; i++) {
        if (uac_handler->capture_transfers[i]) {
            int res = libusb_cancel_transfer(uac_handler->capture_transfers[i]);
            if ((res < 0) && (res != LIBUSB_ERROR_NOT_FOUND)) {
                LOGD("capture libusb_cancel_transfer failed,transfer_id=%d",i);
            }
            uac_handler->capture_transfers[i]= nullptr;
        }
    }
    return 0;
}
int USBAudio::start_playback_stream_transfer() {
    int r = 0;
    int endpoint_bytes_per_packet;
    int packets_per_transfer = MAXOUTNUM_PACKETS;
    int total_transfer_size;
    int transfer_id = 0;
    struct libusb_transfer *transfer;
    int isochronous  = 0;
    const struct libusb_interface *interface;

    if UNLIKELY(!uac_handler) return -1;
    uac_handler->outbuf = (uint8_t *)malloc(uac_handler->size_buf);
    if(uac_handler->outbuf == NULL) return -1;
    uac_handler->holdbuf = (uint8_t *)malloc(uac_handler->size_buf);
    if(uac_handler->holdbuf == NULL) return -1;

    endpoint_bytes_per_packet = MICROFRAME_SIZE;
    if(uac_handler->playbackMaxPacketSize < endpoint_bytes_per_packet)
        endpoint_bytes_per_packet = uac_handler->playbackMaxPacketSize;
    total_transfer_size = endpoint_bytes_per_packet * MAXOUTNUM_PACKETS;
    LOGD("Setup playback transfers\n");
    LOGD("before fill EndpointAddress:%d, per_packet:%d, packets:%d, total_transfer_size:%d\n",
         uac_handler->playbackEndpoint, endpoint_bytes_per_packet, packets_per_transfer,
         total_transfer_size);

    r = libusb_set_interface_alt_setting(uac_handler->uac_devh, uac_handler->playbackInterface,uac_handler->playbackAltSetting);//bAlternateSetting for open mic/playback;0 for close mic/playback
    if (r < 0) {
	 LOGD("restrore the altsetting failed r:%s\n", libusb_error_name(r));
        return r;
    }

    interface = &uac_handler->uac_config->interface[uac_handler->playbackInterface];
    isochronous = interface->num_altsetting > 1;
    uac_handler->playbackTotalTransfers = MAXNUM_TRANSFERS;
    for (transfer_id = 0; transfer_id < uac_handler->playbackTotalTransfers; ++transfer_id) {
        transfer = (struct libusb_transfer *) libusb_alloc_transfer(packets_per_transfer);
        uac_handler->playback_transfers[transfer_id] = transfer;
        uac_handler->playback_transfer_bufs[transfer_id] = (unsigned char *) malloc(
                total_transfer_size);
        memset(uac_handler->playback_transfer_bufs[transfer_id], 0, total_transfer_size);
        libusb_fill_iso_transfer(transfer, uac_handler->uac_devh,
                                 uac_handler->playbackEndpoint,
                                 uac_handler->playback_transfer_bufs[transfer_id],
                                 total_transfer_size,
                                 packets_per_transfer, uac_playback_stream_callback,
                                 (void *) uac_handler, 100);
        transfer->type = LIBUSB_TRANSFER_TYPE_ISOCHRONOUS;
        libusb_set_iso_packet_lengths(transfer, endpoint_bytes_per_packet);
    }
	//Preprocessing data
	uac_playback_payload_buffer(uac_handler);
	uac_swap_buffers(uac_handler);
	for (transfer_id = 0; transfer_id < uac_handler->playbackTotalTransfers; transfer_id++) {
	 uac_playback_payload_iso(uac_handler->playback_transfers[transfer_id],uac_handler);
	    r = libusb_submit_transfer(uac_handler->playback_transfers[transfer_id]);
	    if (r != 0) {
	        LOGD("playback libusb_submit_transfer failed: %s, errno:%s,transfer_id=%d\n",
	             libusb_error_name(r),
	             strerror(errno), transfer_id);
	        return -1;
	    } else {
	        LOGD("playback libusb_submit_transfer success: transfer_id=%d, transfer=%p\n", transfer_id,
	             uac_handler->playback_transfers[transfer_id]);
	    }
	}
    return r;
}

int USBAudio::stop_playback_stream_transfer() {
    int i,r;

    if UNLIKELY(!uac_handler) return -1;
	if (uac_handler->outbuf) {
		free(uac_handler->outbuf);
		uac_handler->outbuf = NULL;
	}
	if (uac_handler->holdbuf) {
		free(uac_handler->holdbuf);
		uac_handler->holdbuf = NULL;
	}
    for (i = 0; i < uac_handler->playbackTotalTransfers; i++) {
        if (uac_handler->playback_transfers[i]) {
            int res = libusb_cancel_transfer(uac_handler->playback_transfers[i]);
            if ((res < 0) && (res != LIBUSB_ERROR_NOT_FOUND)) {
		        LOGD("playback libusb_cancel_transfer failed,transfer_id=%d",i);
            }
            uac_handler->playback_transfers[i]= nullptr;
        }
    }
    return 0;
}

int USBAudio::set_capture_sample_rate_v1(int rate) {
    unsigned char data[3];
    int ret, crate;

    data[0] = (rate & 0xff);
    data[1] = (rate >> 8);
    data[2] = (rate >> 16);


    ret = libusb_control_transfer(uac_handler->uac_devh,
                                  USB_REQ_CS_ENDPOINT_SET,
                                  UAC_SET_CUR,
                                  0x0100,
                                  uac_handler->captureEndpoint,
                                  data, sizeof(data), 500);
    if (ret < 0) {
        LOGD("%d:%d: cannot set freq %d to ep %#x\n",
             uac_handler->captureInterface, uac_handler->captureAltSetting, rate, uac_handler->captureEndpoint);
        return ret;
    }

    ret = libusb_control_transfer(uac_handler->uac_devh,
                                  USB_REQ_CS_ENDPOINT_GET,
                                  UAC_GET_CUR,
                                  0x0100,
                                  uac_handler->captureEndpoint,
                                  data, sizeof(data), 500);

    if (ret < 0) {
        LOGD("%d:%d: cannot get freq at ep %#x\n",
             uac_handler->captureInterface, uac_handler->captureAltSetting, uac_handler->captureEndpoint);
        /* some devices don't support reading */
    }


    crate = data[0] | (data[1] << 8) | (data[2] << 16);
    LOGD("host rate is %d ,device rate is %d\n", rate, crate);
    if (!crate) {
        LOGD("failed to read current rate; disabling the check\n");
        return 0;
    }

    if (crate != rate) {
        LOGD("current rate %d is different from the runtime rate %d\n", crate, rate);
        // runtime->rate = crate;
    }

    return 0;
}

int USBAudio::set_playback_sample_rate_v1(int rate) {
    unsigned char data[3];
    int ret, crate;

    data[0] = (rate & 0xff);
    data[1] = (rate >> 8);
    data[2] = (rate >> 16);


    ret = libusb_control_transfer(uac_handler->uac_devh,
                                  USB_REQ_CS_ENDPOINT_SET,
                                  UAC_SET_CUR,
                                  0x0100,
                                  uac_handler->playbackEndpoint,
                                  data, sizeof(data), 500);
    if (ret < 0) {
        LOGD("%d:%d: cannot set freq %d to ep %#x\n",
             uac_handler->playbackInterface, uac_handler->playbackAltSetting, rate, uac_handler->playbackEndpoint);
        return ret;
    }

    ret = libusb_control_transfer(uac_handler->uac_devh,
                                  USB_REQ_CS_ENDPOINT_GET,
                                  UAC_GET_CUR,
                                  0x0100,
                                  uac_handler->playbackEndpoint,
                                  data, sizeof(data), 500);

    if (ret < 0) {
        LOGD("%d:%d: cannot get freq at ep %#x\n",
             uac_handler->playbackInterface, uac_handler->playbackAltSetting, uac_handler->playbackEndpoint);
        /* some devices don't support reading */
    }


    crate = data[0] | (data[1] << 8) | (data[2] << 16);
    LOGD("host rate is %d ,device rate is %d\n", rate, crate);
    if (!crate) {
        LOGD("failed to read current rate; disabling the check\n");
        return 0;
    }

    if (crate != rate) {
        LOGD("current rate %d is different from the runtime rate %d\n", crate, rate);
        // runtime->rate = crate;
    }

    return 0;
}

//public
USBAudio::USBAudio() :
       sample_rate(DEFAULT_SIMPLING_RATE)
    	{
}

int USBAudio::initAudio(int vid, int pid, int busnum, int devaddr,
                        int fd, const char *usbfs) {
    int ret = 0;
    struct libusb_device_descriptor desc;

    //for return values
    if UNLIKELY(!uac_handler) return -1;
    LOGD("before 11111 vid:%d pid:%d\n", vid, pid);
    strdup(usbfs);
    fd = dup(fd);
    LOGD("before 11111 ret:%s\n", libusb_error_name(ret));
    ret = libusb_init2(&uac_handler->uac_ctx, usbfs);         //initialize a library session
    LOGD("before 11111 ret:%s\n", libusb_error_name(ret));
    if (ret < 0) {
        LOGD("Init Error \n"); //there was an error
        return ret;
    }
    LOGD("before 11111 vid:%d pid:%d\n", vid, pid);
    uac_handler->playback_data_ep_fmt = (EP_FMT *)malloc(sizeof(EP_FMT));
    if(!uac_handler->playback_data_ep_fmt)	return -1;
    uac_handler->playback_sync_ep_fmt = (EP_FMT *)malloc(sizeof(EP_FMT));
    if(!uac_handler->playback_sync_ep_fmt)	return -1;
    uac_handler->capture_data_ep_fmt = (EP_FMT *)malloc(sizeof(EP_FMT));
    if(!uac_handler->capture_data_ep_fmt)	return -1;
    uac_handler->capture_sync_ep_fmt = (EP_FMT *)malloc(sizeof(EP_FMT));
    if(!uac_handler->capture_sync_ep_fmt)	return -1;
    
		
    uac_handler->uac_dev = libusb_get_device_with_fd(uac_handler->uac_ctx, vid, pid, NULL, fd, busnum, devaddr);
    if (uac_handler->uac_dev) {
        ret = libusb_set_device_fd(uac_handler->uac_dev,
                                   fd);  // assign fd to libusb_device for non-rooted Android devices
        libusb_ref_device(uac_handler->uac_dev);
    } else {
        return -1;
    }
    ret = libusb_get_device_descriptor(uac_handler->uac_dev, &desc);
    if (ret == LIBUSB_SUCCESS) {
    	LOGD("libusb_device_descriptor:bcdUSB=0x%x,bDeviceClass=0x%x,bDeviceSubClass=0x%x,bDeviceProtocol=0x%x,bMaxPacketSize0=0x%x,idVendor=0x%x,idProduct=0x%x,bNumConfigurations=0x%x,\n"
			,desc.bcdUSB,desc.bDeviceClass,desc.bDeviceSubClass,desc.bDeviceProtocol,desc.bMaxPacketSize0,desc.idVendor,desc.idProduct,desc.bNumConfigurations);
    }
    LOGD("device_speed=%d\n", libusb_get_device_speed(uac_handler->uac_dev));
 
    ret = libusb_open(uac_handler->uac_dev, &uac_handler->uac_devh);
    if (ret != LIBUSB_SUCCESS) {
    	LOGD("open device err %s\n", libusb_error_name(ret));
        return ret;
    }

    //libusb_reset_device(dev_handle);
    LOGD("before scan_audio_interface ret:%s\n", libusb_error_name(ret));
    ret = libusb_get_config_descriptor(uac_handler->uac_dev, 0, &uac_handler->uac_config);
    if (ret < 0) {
        LOGD("get_config_descriptor err: ret:%s\n", libusb_error_name(ret));
        goto fail2;
    }
    ret = uac_config_analyse(uac_handler->uac_dev);
    if (ret < 0) {
        LOGD("get_config_descriptor err: ret:%s\n", libusb_error_name(ret));
        goto fail2;
    }

    //scan interface
    ret = scan_audio_interface(uac_handler->uac_dev);
    if (ret < 0) {
        LOGD("scan_capture_audio_interface err: ret:%s\n", libusb_error_name(ret));
        goto fail2;
    }

    //claim_interface and set_interface_alt_setting
    ret = claim_if(uac_handler->uac_devh,uac_handler->playbackInterface,uac_handler->playbackAltSetting);
    if (ret < 0) {
	goto fail1;
    }
    ret = claim_if(uac_handler->uac_devh,uac_handler->captureInterface,uac_handler->captureAltSetting);
    if (ret < 0) {
	goto fail0;
    }
    LOGD("set mic config fail %d libusb:%s,errno:%s\n", ret, libusb_error_name(ret),
         strerror(errno));

    //Set sample rate
    LOGD("before sample_rate:%d\n", sample_rate);
    ret = set_playback_sample_rate_v1(sample_rate);
    if (ret < 0) {
        LOGD("%d:%d: cannot get freq at ep %#x\n",
             uac_handler->playbackInterface, uac_handler->playbackAltSetting, uac_handler->playbackEndpoint);
        /* some devices don't support reading */
	goto fail0;
    }
    ret = set_capture_sample_rate_v1(sample_rate);
    if (ret < 0) {
        LOGD("%d:%d: cannot get freq at ep %#x\n",
             uac_handler->captureInterface, uac_handler->captureAltSetting, uac_handler->captureEndpoint);
        /* some devices don't support reading */
	goto fail0;
    }
    LOGD("before uac_set_data_ep_params ret:%s\n", libusb_error_name(ret));
    uac_set_data_ep_params(uac_handler->playback_data_ep_fmt,NULL,48000,16,2,3072, 768, 2);
    uac_handler->inited = 1;
    pthread_mutex_init(&uac_handler->cb_mutex, NULL);

    uac_start_handler_thread(uac_handler);
    return ret;
fail0:
    release_if(uac_handler->uac_devh,uac_handler->playbackInterface,uac_handler->playbackAltSetting);
fail1:
    release_if(uac_handler->uac_devh,uac_handler->captureInterface,uac_handler->captureAltSetting);
fail2:
    if (uac_handler->uac_config)
        libusb_free_config_descriptor(uac_handler->uac_config);
    if (uac_handler->uac_devh)
        libusb_close(uac_handler->uac_devh);
    return ret;
}


void USBAudio::setCallback(JavaVM *vm, JNIEnv *env, jobject callback_obj) {
    uac_handler = new uac_stream_handler_t;
    LOGD("USBAudio setCallback,playbackRunning=%d,captureRunning=%d",uac_handler->playbackRunning,uac_handler->captureRunning);
    uac_handler->playbackRunning = 0;
    uac_handler->captureRunning = 0;
    uac_handler->inited = 0;
    uac_handler->size_buf = XFER_BUF_SIZE;
    uac_handler->out_leave_bytes = uac_handler->out_bytes =  uac_handler->hold_bytes = 0;
    uac_handler->outbuf = uac_handler->holdbuf = NULL;
    uac_handler->kill_handler_thread = 0;
    uac_handler->audio_object = new audio_byte_object;
    if (vm) {
        uac_handler->audio_object->vm = vm;
    }
    if (env) {
        uac_handler->audio_object->env = env;
    }
    uac_handler->audio_object->audioObject = callback_obj;
    if (callback_obj) {
        // get method IDs of Java object for callback
        uac_handler->audio_object->audio_class = env->GetObjectClass(callback_obj);

        if (uac_handler->audio_object->audio_class) {
            uac_handler->audio_object->writeCapturePcmData = env->GetMethodID(uac_handler->audio_object->audio_class, "writeCapturePcmData",
                                                      "([B)V");
        } else {
            LOGD("failed to get object class");
        }
        if (uac_handler->audio_object->audio_class) {
            uac_handler->audio_object->readPlaybackPcmData = env->GetMethodID(uac_handler->audio_object->audio_class, "readPlaybackPcmData",
                                                      "(I)[B");
        } else {
            LOGD("failed to get object class");
        }

        env->ExceptionClear();
        if (!uac_handler->audio_object->writeCapturePcmData) {
            LOGD("Can't find IFrameCallback#onFrame");
            env->DeleteGlobalRef(callback_obj);
            uac_handler->audio_object->audioObject = callback_obj = NULL;
        }
    }
}
int USBAudio::getSampleRate() {
    return sample_rate;
}

int USBAudio::getChannelCount() {
    return audio_stream_desc->bNrChannels;
}

int USBAudio::getBitResolution() {
    return audio_stream_desc->bBitResolution;
}

bool USBAudio::isRunning() {
    LOGD("Audio loop: isRunning %d", (uac_handler->captureRunning == 1)||(uac_handler->playbackRunning == 1));
    if UNLIKELY(!uac_handler) return false;
    return (uac_handler->captureRunning == 1)||(uac_handler->playbackRunning == 1);
}

//Java 调用
int USBAudio::startCapture() {
    int r = 0;

    if UNLIKELY(!uac_handler) return -1;
    if UNLIKELY(!uac_handler->inited) return -1;
    LOGD("Audio loop: startCapture %d", uac_handler->captureRunning);
    if(uac_handler->captureRunning == 1) {
        LOGD("capture is running");
        return 0;
    }
    if (uac_handler->audio_object->audioObject) {
        uac_handler->captureRunning = 1;
    }
    r = start_capture_stream_transfer();
    if (r < 0) {
    	LOGD("start_capture_stream_transfer error %s\n", libusb_error_name(r));
        return r;
    }
    LOGD("Audio loop: audioObject %p", uac_handler->audio_object->audioObject);
//    r = startPlayback();
    return r;
}

int USBAudio::stopCapture() {
    LOGD("Audio loop: destroyAudio stopCapture=%d", uac_handler->captureRunning);

    if UNLIKELY(!uac_handler) return -1;
    if(uac_handler->captureRunning){
        uac_handler->captureRunning = 0;
        stop_capture_stream_transfer();
        //uac_handler->captureRunning = 0;
    }
//    stopPlayback();
    return 0;
}
//Java 调用
int USBAudio::startPlayback() {
    int r = 0;

    if UNLIKELY(!uac_handler) return -1;
    if UNLIKELY(!uac_handler->inited) return -1;
    LOGD("Audio loop: startPlayback %d", uac_handler->playbackRunning);
    if(uac_handler->playbackRunning == 1) {
        LOGD("playback is running");
        return 0;
    }
    if (uac_handler->audio_object->audioObject) {
        uac_handler->playbackRunning = 1;
    }
    r = start_playback_stream_transfer();
    if (r < 0) {
    	LOGD("start_playback_stream_transfer error %s\n", libusb_error_name(r));
        return r;
    }
    LOGD("Audio loop: audioObject %p", uac_handler->audio_object->audioObject);
    return r;
}


int USBAudio::stopPlayback() {
    LOGD("Audio loop: destroyAudio stopPlayback=%d", uac_handler->playbackRunning);

    if UNLIKELY(!uac_handler) return -1;
    if(uac_handler->playbackRunning){
        uac_handler->playbackRunning = 0;
        stop_playback_stream_transfer();
    }
    return 0;
}

void USBAudio::closeAudio() {
    int ret = 0;
    LOGD("Audio loop: destroyAudio %d", ((uac_handler->captureRunning == 1)||(uac_handler->playbackRunning == 1)));

    if UNLIKELY(!uac_handler) return;
    stopCapture();
    stopPlayback();
    if (uac_handler->audio_object->audioObject) {
        uac_handler->audio_object->env->DeleteGlobalRef(uac_handler->audio_object->audioObject);
    }
    ret = release_if(uac_handler->uac_devh,uac_handler->playbackInterface,uac_handler->playbackAltSetting);
    ret = release_if(uac_handler->uac_devh,uac_handler->captureInterface,uac_handler->captureAltSetting);
    if (uac_handler->uac_config)
        libusb_free_config_descriptor(uac_handler->uac_config);
    pthread_mutex_destroy(&uac_handler->cb_mutex);
    if (uac_handler->uac_devh){
		uac_handler->kill_handler_thread = 1;
        	libusb_close(uac_handler->uac_devh);
		pthread_join(uac_handler->handler_thread, NULL);
    	}
    libusb_exit(uac_handler->uac_ctx);
    uac_handler->inited = 0;
    if(uac_handler->playback_data_ep_fmt)
		free(uac_handler->playback_data_ep_fmt);
    if(uac_handler->playback_sync_ep_fmt)
		free(uac_handler->playback_sync_ep_fmt);
    if(uac_handler->capture_data_ep_fmt)
		free(uac_handler->capture_data_ep_fmt);
    if(uac_handler->capture_sync_ep_fmt)
		free(uac_handler->capture_sync_ep_fmt);
}



