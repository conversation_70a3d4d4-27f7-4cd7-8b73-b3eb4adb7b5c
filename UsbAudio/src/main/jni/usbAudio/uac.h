/*
 * Internal header for libusb
 * Copyright © 2007-2009 <PERSON> <<EMAIL>>
 * Copyright © 2001 <PERSON> <<EMAIL>>
 *
 * This library is free software; you can redistribute it and/or
 * modify it under the terms of the GNU Lesser General Public
 * License as published by the Free Software Foundation; either
 * version 2.1 of the License, or (at your option) any later version.
 *
 * This library is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the GNU
 * Lesser General Public License for more details.
 *
 * You should have received a copy of the GNU Lesser General Public
 * License along with this library; if not, write to the Free Software
 * Foundation, Inc., 51 Franklin Street, Fifth Floor, Boston, MA 02110-1301 USA
 */

#ifndef UAC_H
#define UAC_H

#ifdef __cplusplus
extern "C" {
#endif

#include <jni.h>
#include <stdint.h>
#include <pthread.h>
#include "../libusb/libusb/libusb.h"

#define MAX_NR_RATES	1024
#define MAX_PACKS	10		/* per URB */
#define MAX_PACKS_HS	(MAX_PACKS * 8)	/* in high speed mode */
#define MAX_URBS	8
#define SYNC_URBS	4	/* always four urbs for sync */
#define MAX_QUEUE	32	/* try not to exceed this queue length, in ms */
#define MAX_QUEUE_HS	30	/* try not to exceed this queue length, in ms */
#define LOW_LATENCY_MAX_QUEUE   6 /* for low latency case queue length */

#define DIV_ROUND_UP(x,y) (((x) + ((y) - 1)) / (y))
#ifndef MIN
#define MIN(a, b)	((a) < (b) ? (a) : (b))
#endif
#ifndef MAX
#define MAX(a, b)	((a) > (b) ? (a) : (b))
#endif

//#define USB_AUDIO_CLASS_VERSION		1
#define USB_AUDIO_CLASS_VERSION		2

/*
? Input Terminal (IT)
? Output Terminal (OT)
? Mixer Unit (MU)
? Selector Unit (SU)
? Feature Unit (FU)
? Sampling Rate Converter Unit
? Effect Unit (EU)
? Processing Unit (PU)
? Extension Unit (XU)

? Clock Source (CS)
? Clock Selector (CX)
? Clock Multiplier (CM)
*/

/*	4.1  Audio Channel Cluster Descriptor		******* Audio Channel Cluster Format     */
typedef struct 
{
	uint8_t	bNrChannels;		// Number   Number of logical output channels in the Terminal's output audio channel cluster.
	uint32_t	bmChannelConfig;	// Bitmap   Describes the spatial location of the logical channels.
	uint8_t	iChannelNames;	// Index  Index of a string descriptor, describing the name of the first logical channel.
}Audio_Channel_Cluster_Descriptor;

/*
4.2  Device Descriptor(uac 2.0)			4.1 Device Descriptor(uac 1.0)
uac2.0  ��uac1.0��Ҫͨ��bDeviceClass/bDeviceSubClass/bDeviceProtocol������
��ͨ��interface�е�bFunctionClass          1 Audio(Interface Association)��
bInterfaceClass         1 Audio(Interface Descriptor)��
ȷ����uac�豸��audio�豸
{
	  bLength;
	  bDescriptorType;
	  bcdUSB;
	  bDeviceClass;		(0xEF)		(0)
	  bDeviceSubClass;	(0x02)		(0)
	  bDeviceProtocol;	(0x01)		(0)
	  bMaxPacketSize0;
	  idVendor;
	  idProduct;
	  bcdDevice;
	  iManufacturer;
	  iProduct;
	  iSerialNumber;
	  bNumConfigurations;
}
	
audio functionality is always considered to reside at the interface level
*/

/*
4.3  Device_Qualifier Descriptor(uac2.0)
the bDeviceClass, bDeviceSubClass and bDeviceProtocol fields of the Device_Qualifier descriptor must also
USB Device Class Definition for Audio Devices
Release 2.0  May 31, 2006  46
be set to 0xEF, 0x02, and 0x01 respectively
*/
#define LIBUSB_DT_QUALIFIER		0x06
typedef struct 
{
	uint8_t		bLength;
	uint8_t		bDescriptorType;
	uint16_t		bcdUSB;
	uint8_t		bDeviceClass;
	uint8_t		bDeviceSubClass;
	uint8_t		bDeviceProtocol;
	uint8_t		bMaxPacketSize0;
	uint8_t		bNumConfigurations;
	uint8_t		bReserved;
	
}Device_Qualifier_Descriptor;

/*
4.4  Configuration Descriptor	4.2 Configuration Descriptor(uac1.0)	
*/

/*
4.5  Other_Speed_Configuration Descriptor(uac2.0)
*/
#define LIBUSB_DT_OTHER_SPEED_CONFIGURATION		0x07
typedef struct 
{
	uint8_t		bLength;		// Number Size of descriptor
	uint8_t		bDescriptorType;	// 	Constant Other_speed_Configuration Type
	uint16_t		wTotalLength;		// Total length of data returned
	uint8_t		bNumInterfaces;	// Number Number of interfaces supported by this speed configuration
	uint8_t		bConfigurationValue;	// Number Value to use to select configuration
	uint8_t		iConfiguration;	// Index Index of string descriptor
	uint8_t		bmAttributes;		// Bitmap Same as Configuration descriptor
	uint8_t		bMaxPower;	// mA Same as Configuration descriptor
	
}Other_Speed_Config_Descriptor;

#if 0
enum USB_DescriptorTypes_t
{
    USB_DESCTYPE_DEVICE                 = 0x01, /* Device descriptor */
    USB_DESCTYPE_CONFIGURATION          = 0x02, /* Configuration descriptor */
    USB_DESCTYPE_STRING                 = 0x03, /* String descriptor */
    USB_DESCTYPE_INTERFACE              = 0x04, /* Interface descriptor */
    USB_DESCTYPE_ENDPOINT               = 0x05, /* Endpoint descriptor */
    USB_DESCTYPE_DEVICE_QUALIFIER       = 0x06, /* Device qualifier descriptor */
    USB_DESCTYPE_OTHER_SPEED            = 0x07,
    USB_DESCTYPE_INTERFACE_POWER        = 0x08, /* Interface power descriptor */
    USB_DESCTYPE_OTG                    = 0x09,
    USB_DESCTYPE_DEBUG                  = 0x0A,
    USB_DESCTYPE_INTERFACE_ASSOCIATION  = 0x0B, /* Interface association descriptor */
};
#endif
#define LIBUSB_DT_INTERFACE_ASSOCIATION		0x0B
#define LIBUSB_DT_INT_ASSOCIATION_SIZE			8

/*
4.6  Interface Association Descriptor(uac2.0)
bFunctionClass  = AUDIO (LIBUSB_CLASS_AUDIO)	uac2.0 ����ͨ�����ֵ��ȷ����
bFunctionSubClass = FUNCTION_SUBCLASS_UNDEFINED
bFunctionProtocol = AF_VERSION_02_00
*/
/* A.1 Audio Function Class Code */
#define AUDIO_FUNCTION                                   AUDIO
/* A.2 Audio Function Subclass Codes */
#define FUNCTION_SUBCLASS_UNDEFINED                      0x00
/* A.3 Audio Function Protocol Codes */
enum USB_Audio_FuncProtocolCodes_t
{
	UAC_FUNC_PROTOCOL_UNDEFINED                          = 0x00,
	UAC_FUNC_PROTOCOL_AF_VERSION_02_00                   = 0x20
};
/* A.4 Audio Interface Class Code */
#define AUDIO                                            0x01

/*A.5  Audio Interface Subclass Codes*/
enum UAC_IntSubclassCodes_t
{
	UAC_INT_SUBCLASS_UNDEFINED			= 0x00,
	UAC_INT_SUBCLASS_AUDIOCONTROL                        = 0x01,
	UAC_INT_SUBCLASS_AUDIOSTREAMING                      = 0x02,
	UAC_INT_SUBCLASS_MIDISTREAMING                       = 0x03
};
/*A.6  Audio Interface Protocol Codes*/
enum UAC_IntProtocolCodes_t
{
    UAC_INT_PROTOCOL_UNDEFINED                           = 0x00,
    UAC_INT_PROTOCOL_IP_VERSION_02_00                    = 0x20
};
#define FUNCTION_PROTOCOL_UNDEFINED		UAC_INT_PROTOCOL_UNDEFINED
#define AF_VERSION_02_00						UAC_INT_PROTOCOL_IP_VERSION_02_00
/*A.7  Audio Function Category Codes*/
enum UAC_AudioFunctionCategory_t
{
    UAC_FUNCTION_SUBCLASS_UNDEFINED                      = 0x00,
    UAC_FUNCTION_DESKTOP_SPEAKER                         = 0x01,
    UAC_FUNCITON_HOME_THEATER                            = 0x02,
    UAC_FUNCTION_MICROPHONE                              = 0x03,
    UAC_FUNCITON_HEADSET                                 = 0x04,
    UAC_FUNCTION_TELEPHONE                               = 0x05,
    UAC_FUNCTION_CONVERTER                               = 0x06,
    UAC_FUNCTION_VOICE_SOUND_RECORDER                    = 0x07,
    UAC_FUNCTION_IO_BOX                                  = 0x08,
    UAC_FUNCTION_MUSICAL_INTRUMENT                       = 0x09,
    UAC_FUNCTION_PRO_AUDIO                               = 0x0A,
    UAC_FUNCTION_AUDIO_VIDEO                             = 0x0B,
    UAC_FUNCTION_CONTROL_PANEL                           = 0x0C,
    UAC_FUNCITON_OTHER                                   = 0xFF
};
/*A.8  Audio Class-Specific Descriptor Types(uac2.0)*/
enum UAC_CSDescriptorTypes_t
{
    UAC_CS_DESCTYPE_UNDEFINED                            = 0x20,
    UAC_CS_DESCTYPE_DEVICE                               = 0x21,
    UAC_CS_DESCTYPE_CONFIGURATION                        = 0x22,
    UAC_CS_DESCTYPE_STRING                               = 0x23,
    UAC_CS_DESCTYPE_INTERFACE                            = 0x24,
    UAC_CS_DESCTYPE_ENDPOINT                             = 0x25,
};
/*A.9  Audio Class-Specific AC Interface Descriptor Subtypes(uac2.0)  ACS:audio class specific AC:audio_control*/
enum UAC_CS_AC_InterfaceDescriptorSubtype_t
{
    UAC_CS_AC_INTERFACE_SUBTYPE_AC_DESCRIPTOR_UNDEFINED  = 0x00,
    UAC_CS_AC_INTERFACE_SUBTYPE_HEADER                   = 0x01,
    UAC_CS_AC_INTERFACE_SUBTYPE_INPUT_TERMINAL           = 0x02,
    UAC_CS_AC_INTERFACE_SUBTYPE_OUTPUT_TERMINAL          = 0x03,
    UAC_CS_AC_INTERFACE_SUBTYPE_MIXER_UNIT               = 0x04,
    UAC_CS_AC_INTERFACE_SUBTYPE_SELECTOR_UNIT            = 0x05,
    UAC_CS_AC_INTERFACE_SUBTYPE_FEATURE_UNIT             = 0x06,
    UAC_CS_AC_INTERFACE_SUBTYPE_EFFECT_UNIT              = 0x07,
    UAC_CS_AC_INTERFACE_SUBTYPE_PROCESSING_UNIT          = 0x08,
    UAC_CS_AC_INTERFACE_SUBTYPE_EXTENSION_UNIT           = 0x09,
    UAC_CS_AC_INTERFACE_SUBTYPE_CLOCK_SOURCE             = 0x0A,
    UAC_CS_AC_INTERFACE_SUBTYPE_CLOCK_SELECTOR           = 0x0B,
    UAC_CS_AC_INTERFACE_SUBSYPE_CLOCK_MULTIPLIER         = 0x0C,
    UAC_CS_AC_INTERFACE_SUBTYPE_SAMPLE_RATE_CONVERTER    = 0x0D
};
/*
A.10 Audio Class-Specific AS Interface Descriptor Subtypes(uac2.0)
AudioStreaming (AS)
*/
enum UAC_CS_AS_InterfaceDescriptorSubtype_t
{
    UAC_CS_AS_INTERFACE_SUBTYPE_UNDEFINED                = 0x00,
    UAC_CS_AS_INTERFACE_SUBTYPE_AS_GENERAL               = 0x01,
    UAC_CS_AS_INTERFACE_SUBTYPE_FORMAT_TYPE              = 0x02,
    UAC_CS_AS_INTERFACE_SUBTYPE_ENCODER                  = 0x03,
    UAC_CS_AS_INTERFACE_SUBTYPE_DECODER                  = 0x04
};

/*
A.11 Effect Unit Effect Types(uac2.0)
*/
#define EUET_EFFECT_UNDEFINED		0x00
#define EUET_PARAM_EQ_SECTION_EFFECT		0x01
#define EUET_REVERBERATION_EFFECT		0x02
#define EUET_MOD_DELAY_EFFECT		0x03
#define EUET_DYN_RANGE_COMP_EFFECT		0x04

/*
A.12 Processing Unit Process Types(uac2.0)
*/
#define PUPT_PROCESS_UNDEFINED		0x00
#define PUPT_UP_DOWNMIX_PROCESS		0x01
#define PUPT_DOLBY_PROLOGIC_PROCESS		0x02
#define PUPT_STEREO_EXTENDER_PROCESS		0x03
/*
A.13 Audio Class-Specific Endpoint Descriptor Subtypes(uac2.0)
*/
enum UAC_CS_EndpointDescriptorSubtype_t
{
    UAC_CS_ENDPOINT_SUBTYPE_UNDEFINED                   = 0x00,
    UAC_CS_ENDPOINT_SUBTYPE_EP_GENERAL                  = 0x01
};

/*
A.14 Audio Class-Specific Request Codes(uac2.0)
*/
enum UAC_ACS_RequestCode_t
{
	UAC_ACS_REQUEST_CODE_UNDEFINED	= 0x00,
	UAC_ACS_REQUEST_CUR	= 0x01,
	UAC_ACS_REQUEST_RANGE	= 0x02,
	UAC_ACS_REQUEST_MEM	= 0x03
};
/*
A.15 Encoder Type Codes(uac2.0)
*/
#define ENC_TYPE_CODE_ENCODER_UNDEFINED		0x00
#define ENC_TYPE_CODE_OTHER_ENCODER		0x01
#define ENC_TYPE_CODE_AC3_ENCODER 		0x03
#define ENC_TYPE_CODE_WMA_ENCODER 		0x04
#define ENC_TYPE_CODE_DTS_ENCODER 		0x05
/*
A.16 Decoder Type Codes(uac2.0)
*/
#define DEC_TYPE_CODE_ENCODER_UNDEFINED		0x00
#define DEC_TYPE_CODE_OTHER_ENCODER		0x01
#define DEC_TYPE_CODE_AC3_ENCODER 		0x03
#define DEC_TYPE_CODE_WMA_ENCODER 		0x04
#define DEC_TYPE_CODE_DTS_ENCODER 		0x05

/*
A.17.1  Clock Source Control Selectors(uac2.0)
*/
#define CS_CONTROL_UNDEFINED_UNDEFINED		0x00
#define CS_SAM_FREQ_CONTROL		0x01
#define CS_CLOCK_VALID_CONTROL		0x02
/*
A.17.2  Clock Selector Control Selectors(uac2.0)
*/
#define CX_CONTROL_UNDEFINED		0x00
#define CX_CLOCK_SELECTOR_CONTROL		0x01
/*
A.17.3  Clock Multiplier Control Selectors(uac2.0)
*/
#define CM_CONTROL_UNDEFINED		0x00
#define CM_NUMERATOR_CONTROL		0x01
#define CM_DENOMINATOR_CONTROL		0x02
/*
A.17.4  Terminal Control Selectors(uac2.0)
*/
#define TE_CONTROL_UNDEFINED		0x00
#define TE_COPY_PROTECT_CONTROL		0x01
#define TE_CONNECTOR_CONTROL		0x02
#define TE_OVERLOAD_CONTROL		0x03
#define TE_CLUSTER_CONTROL		0x04
#define TE_UNDERFLOW_CONTROL		0x05
#define TE_OVERFLOW_CONTROL		0x06
#define TE_LATENCY_CONTROL		0x07
/*
A.17.5  Mixer Control Selectors(uac2.0)
*/
#define MU_CONTROL_UNDEFINED		0x00
#define MU_MIXER_CONTROL		0x01
#define MU_CLUSTER_CONTROL		0x02
#define MU_UNDERFLOW_CONTROL		0x03
#define MU_OVERFLOW_CONTROL		0x04
#define MU_LATENCY_CONTROL		0x05
/*
A.17.6  Selector Control Selectors(uac2.0)
*/
#define SU_CONTROL_UNDEFINED		0x00
#define SU_SELECTOR_CONTROL		0x01
#define SU_LATENCY_CONTROL		0x02
/*
A.17.7  Feature Unit Control Selectors(uac2.0)
*/
#define FU_CONTROL_UNDEFINED		0x00
#define FU_MUTE_CONTROL		0x01
#define FU_VOLUME_CONTROL		0x02
#define FU_BASS_CONTROL		0x03
#define FU_MID_CONTROL		0x04
#define FU_TREBLE_CONTROL		0x05
#define FU_GRAPHIC_EQUALIZER_CONTROL		0x06
#define FU_AUTOMATIC_GAIN_CONTROL		0x07
#define FU_DELAY_CONTROL		0x08
#define FU_BASS_BOOST_CONTROL		0x09
#define FU_LOUDNESS_CONTROL		0x0A
#define FU_INPUT_GAIN_CONTROL		0x0B
#define FU_INPUT_GAIN_PAD_CONTROL		0x0C
#define FU_PHASE_INVERTER_CONTROL		0x0D
#define FU_UNDERFLOW_CONTROL		0x0E
#define FU_OVERFLOW_CONTROL		0x0F
#define FU_LATENCY_CONTROL		0x10

/*
A.17.8  Effect Unit Control Selectors
A.17.8.1  Parametric Equalizer Section Effect Unit Control Selectors(uac2.0)
*/
#define PE_CONTROL_UNDEFINED		0x00
#define PE_ENABLE_CONTROL		0x01
#define PE_CENTERFREQ_CONTROL		0x02
#define PE_QFACTOR_CONTROL		0x03
#define PE_GAIN_CONTROL		0x04
#define PE_UNDERFLOW_CONTROL		0x05
#define PE_OVERFLOW_CONTROL		0x06
#define PE_LATENCY_CONTROL		0x07
/*
A.17.8.2  Reverberation Effect Unit Control Selectors(uac2.0)
*/
#define RV_CONTROL_UNDEFINED		0x00
#define RV_ENABLE_CONTROL		0x01
#define RV_TYPE_CONTROL		0x02
#define RV_LEVEL_CONTROL		0x03
#define RV_TIME_CONTROL		0x04
#define RV_FEEDBACK_CONTROL		0x05
#define RV_PREDELAY_CONTROL		0x06
#define RV_DENSITY_CONTROL		0x07
#define RV_HIFREQ_ROLLOFF_CONTROL		0x08
#define RV_UNDERFLOW_CONTROL		0x09
#define RV_OVERFLOW_CONTROL		0x0A
#define RV_LATENCY_CONTROL		0x0B
/*
A.17.8.3  Modulation Delay Effect Unit Control Selectors(uac2.0)
*/
#define MD_CONTROL_UNDEFINED		0x00
#define MD_ENABLE_CONTROL		0x01
#define MD_BALANCE_CONTROL		0x02
#define MD_RATE_CONTROL		0x03
#define MD_DEPTH_CONTROL		0x04
#define MD_TIME_CONTROL		0x05
#define MD_FEEDBACK_CONTROL		0x06
#define MD_UNDERFLOW_CONTROL		0x07
#define MD_OVERFLOW_CONTROL		0x08
#define MD_LATENCY_CONTROL		0x09
/*
A.17.8.4  Dynamic Range Compressor Effect Unit Control Selectors
*/
/*
A.17.9  Processing Unit Control Selectors
A.17.9.1  Up/Down-mix Processing Unit Control Selectors
*/
/*
A.17.9.2  Dolby Prologic ?Processing Unit Control Selectors
*/
/*
A.17.9.3  Stereo Extender Processing Unit Control Selectors
*/
/*
A.17.10 Extension Unit Control Selectors
*/
/*
A.17.11 AudioStreaming Interface Control Selectors
*/
#define AS_CONTROL_UNDEFINED		0x00
#define AS_ACT_ALT_SETTING_CONTROL		0x01
#define AS_VAL_ALT_SETTINGS_CONTROL		0x02
#define AS_AUDIO_DATA_FORMAT_CONTROL		0x03
/*
A.17.12 Encoder Control Selectors
*/
/*
A.17.13 Decoder Control Selectors
A.17.13.1  MPEG Decoder Control Selectors
*/
/*
A.17.13.2  AC-3 Decoder Control Selectors
*/
/*
A.17.13.3  WMA Decoder Control Selectors
*/
/*
A.17.13.4  DTS Decoder Control Selectors
*/
/*
A.17.14 Endpoint Control Selectors
*/

typedef struct 
{
	uint8_t	bLength;	// Number  Size of this descriptor in bytes: 8
	uint8_t	bDescriptorType;	// Constant  INTERFACE ASSOCIATION Descriptor.	
	uint8_t	bFirstInterface;	// Number  Interface number of the first interface that is associated with this function.
	uint8_t	bInterfaceCount;	// Number  Number of contiguous interfaces that are associated with this function.
	uint8_t	bFunctionClass;	// Class  AUDIO_FUNCTION Function Class code(assigned by this specification). SeeAppendix A.1, audio Function Class Code?
	uint8_t	bFunctionSubClass; // SubClass  FUNCTION_SUBCLASS_UNDEFINED Function Subclass code. Currently not used. See Appendix A.2, audio Function Subclass Codes?
	uint8_t	bFunctionProtocol;	 // Protocol  AF_VERSION_02_00 Function Protocol code. Indicates the current version of the specification. See Appendix A.3, audio Function Protocol Codes?
	uint8_t	iFunction; // Index  Index of a string descriptor that describes this interface.
}Interface_Association_Descriptor;

/*
4.7  AudioControl Interface Descriptors(uac2.0)
*/
/*
4.7  AudioControl Interface Descriptors
4.7.1  Standard AC Interface Descriptor(uac2.0) 
	standard ac interface is standart usb interface
*/
typedef struct
{
	uint8_t 	bLength;		// Number  Size of this descriptor, in bytes: 9
	uint8_t 	bDescriptorType;	// Constant  INTERFACE descriptor type	4
	uint8_t 	bInterfaceNumber;	// Number  Number of interface. A zero-based value identifying the index in the array of concurrent interfaces supported by this configuration.
	uint8_t 	bAlternateSetting;	// Number  Value used to select an Alternate Setting for the interface identified in the prior field. Must be set to 0.
	uint8_t 	bNumEndpoints;	// Number  Number of endpoints used by this interface (excluding endpoint 0). This number is either 0 or 1 if the optional interrupt endpoint is present.
	uint8_t 	bInterfaceClass;	// Class  AUDIO. Audio Interface Class code uac2.0 ����ͨ�����ֵ��ȷ����
	uint8_t 	bInterfaceSubClass;	// Subclass  AUDIOCONTROL. Audio Interface Subclass code. Assigned by this specification. See Appendix A.5,audio Interface Subclass Codes.?
	uint8_t 	bInterfaceProtocol;
	uint8_t	iInterface;
}Standard_AC_Interface_Descriptor;

/*4.7.2  Class-Specific AC Interface Descriptor(uac2.0)*/
typedef struct
{
	uint8_t 	bLength;	//9	// Number  Size of this descriptor, in bytes: 9
	uint8_t 	bDescriptorType;	// Constant  CS_INTERFACE descriptor type.	0x24 36
	uint8_t 	bDescriptorSubtype;	// 1 // Constant  HEADER descriptor subtype.   1 ACS_AC_DT_HEADER
	uint16_t 	bcdADC;	//  BCD  Audio Device Class Specification Release Number in Binary-Coded Decimal.
	uint8_t 	bCategory;	// Constant  Constant, indicating the primary use of this audio function, as intended by the manufacturer. See Appendix A.7, aAudio Function Category Codes.?
	uint16_t 	wTotalLength;	// Number  Total number of bytes returned for the class-specific AudioControl interface descriptor. Includes the combined length of this descriptor header and all Clock Source, Unit and Terminal descriptors.
	uint8_t 	bmControls;	//  Bitmap  D1..0:  Latency Control D7..2:  Reserved. Must be set to 0.
}CS_AC_Interface_Header_Descriptor;
/********  Clock Source Descriptor(uac2.0)*/
/*
	uint8_t 	bLength;	//8	// Number  Size of this descriptor, in bytes: 8
	uint8_t 	bDescriptorType;	// Constant  CS_INTERFACE descriptor type.	0x24 36
	uint8_t 	bDescriptorSubtype; // 10	// Constant  CLOCK_SOURCE descriptor subtype.   1 ACS_AC_DT_HEADER
	uint8_t 	bClockID;	Constant uniquely identifying the Clock
						Source Entity within the audio function.
						This value is used in all requests to
						address this Entity.
	uint8_t 	bmAttributes;	Bitmap  D1..0:  Clock Type:
									00: External Clock
									01: Internal fixed Clock
									10: Internal variable Clock
									11: Internal programmable Clock
									D2:  Clock synchronized to SOF
									D7..3:  Reserved. Must be set to 0.
	uint8_t	bmControls;	Bitmap  D1..0:  Clock Frequency Control
								D3..2:  Clock Validity Control
								D7..4:  Reserved. Must be set to 0.
	uint8_t 	bAssocTerminal;	Constant  Terminal ID of the Terminal that is
								associated with this Clock Source.
	uint8_t 	iClockSource;	  Number  Index of a string descriptor, describing the Clock Source Entity.

*/
typedef struct
{
	uint8_t 	bLength;	//8	// Number  Size of this descriptor, in bytes: 8
	uint8_t 	bDescriptorType;	// Constant  CS_INTERFACE descriptor type.	0x24 36
	uint8_t 	bDescriptorSubtype; // 10	// Constant  CLOCK_SOURCE descriptor subtype.   1 ACS_AC_DT_HEADER
	uint8_t 	bClockID;	
	uint8_t 	bmAttributes;
	uint8_t	bmControls;	
	uint8_t 	bAssocTerminal;	
	uint8_t 	iClockSource;	//  Number  Index of a string descriptor, describing the Clock Source Entity.
}CS_AC_Interface_Clock_Source_Descriptor;
/********  Clock Selector Descriptor(uac2.0)*/
/*
	uint8_t 	bmAttributes;	Bitmap  D1..0:  Clock Type:
									00: External Clock
									01: Internal fixed Clock
									10: Internal variable Clock
									11: Internal programmable Clock
									D2:  Clock synchronized to SOF
									D7..3:  Reserved. Must be set to 0.

*/
#define NUMBER_INPUT_CLOCK	0x01	// 0x02 //0x03
typedef struct
{
	uint8_t 	bLength;	//7+p	// Number  Size of this descriptor, in bytes: 8
	uint8_t 	bDescriptorType;	// Constant  CS_INTERFACE descriptor type.	0x24 36
	uint8_t 	bDescriptorSubtype; // 11	// Constant  CLOCK_SELECTOR descriptor subtype.   1 ACS_AC_DT_HEADER
	uint8_t 	bClockID;	/*Constant uniquely identifying the Clock
						Source Entity within the audio function.
						This value is used in all requests to
						address this Entity.*/
	uint8_t 	bNrInPins;	// Number  Number of Input Pins of this Unit: p
	uint8_t	baCSourceID[1];	/*ID of the Clock Entity to which the first
							Clock Input Pin of this Clock Selector
							Entity is connected.*/
	uint8_t 	bmControls;	/*BitmapD1..0:  Clock Selector Control
								D7..2:  Reserved. Must be set to 0.*/
	uint8_t 	iClockSelector;	//  Index  Index of a string descriptor, describing the Clock Selector Entity..
}CS_AC_Interface_Clock_Selector_1_Descriptor;
typedef struct
{
	uint8_t 	bLength;	//7+p	// Number  Size of this descriptor, in bytes: 8
	uint8_t 	bDescriptorType;	// Constant  CS_INTERFACE descriptor type.	0x24 36
	uint8_t 	bDescriptorSubtype; // 11	// Constant  CLOCK_SELECTOR descriptor subtype.   1 ACS_AC_DT_HEADER
	uint8_t 	bClockID;	/*Constant uniquely identifying the Clock
						Source Entity within the audio function.
						This value is used in all requests to
						address this Entity.*/
	uint8_t 	bNrInPins;	// Number  Number of Input Pins of this Unit: p
	uint8_t	baCSourceID[2];	/*ID of the Clock Entity to which the first
							Clock Input Pin of this Clock Selector
							Entity is connected.*/
	uint8_t 	bmControls;	/*BitmapD1..0:  Clock Selector Control
								D7..2:  Reserved. Must be set to 0.*/
	uint8_t 	iClockSelector;	//  Index  Index of a string descriptor, describing the Clock Selector Entity..
}CS_AC_Interface_Clock_Selector_2_Descriptor;
typedef struct
{
	uint8_t 	bLength;	//7+p	// Number  Size of this descriptor, in bytes: 8
	uint8_t 	bDescriptorType;	// Constant  CS_INTERFACE descriptor type.	0x24 36
	uint8_t 	bDescriptorSubtype; // 11	// Constant  CLOCK_SELECTOR descriptor subtype.   1 ACS_AC_DT_HEADER
	uint8_t 	bClockID;	/*Constant uniquely identifying the Clock
						Source Entity within the audio function.
						This value is used in all requests to
						address this Entity.*/
	uint8_t 	bNrInPins;	// Number  Number of Input Pins of this Unit: p
	uint8_t	baCSourceID[3];	/*ID of the Clock Entity to which the first
							Clock Input Pin of this Clock Selector
							Entity is connected.*/
	uint8_t 	bmControls;	/*BitmapD1..0:  Clock Selector Control
								D7..2:  Reserved. Must be set to 0.*/
	uint8_t 	iClockSelector;	//  Index  Index of a string descriptor, describing the Clock Selector Entity..
}CS_AC_Interface_Clock_Selector_3_Descriptor;
/*
*******  Input Terminal Descriptor(uac2.0)
0  bLength  			1  Number  Size of this descriptor, in bytes: 17
1  bDescriptorType  		1  Constant  CS_INTERFACE descriptor type.
2  bDescriptorSubtype  	1  Constant  INPUT_TERMINAL descriptor subtype.
3  bTerminalID  			1  Constant  Constant uniquely identifying the Terminal
								within the audio function. This value is
								used in all requests to address this
								Terminal.
4  wTerminalType  		2  Constant  Constant characterizing the type of Terminal. See USB Audio Terminal
								Types.
6  bAssocTerminal  		1  Constant  ID of the Output Terminal to which this Input Terminal is associated.
7  bCSourceID  			1  Constant  ID of the Clock Entity to which this Input Terminal is connected.
8  bNrChannels  		1  Number  Number of logical output channels in the Terminals output audio channel cluster.
9  bmChannelConfig  	4  Bitmap  Describes the spatial location of the logical channels.
13  iChannelNames  		1  Index  Index of a string descriptor, describing the
							name of the first logical channel.
14  bmControls  		2  Bitmap  D1..0:  Copy Protect Control
							D3..2:  Connector Control
							D5..4:  Overload Control
							D7..6:  Cluster Control
							D9..8:  Underflow Control
							D11..10: Overflow Control
							D15..12: Reserved. Must be set to 0.
16  iTerminal  			1  Index  Index of a string descriptor, describing the Input Terminal.
*/
#define USB_TERMINAL_TYPES_USB_UNDEFINED	0X100
#define USB_TERMINAL_TYPES_USB_STREAMING	0X101
#define USB_TERMINAL_TYPES_USB_VENDOR_SPECIFIC	0X1FF

typedef struct
{
	uint8_t 	bLength;	// 17
	uint8_t 	bDescriptorType;	// CS_INTERFACE 36
	uint8_t 	bDescriptorSubtype;	// INPUT_TERMINAL 2
	uint8_t 	bTerminalID;	// Constant
	uint16_t 	wTerminalType;	// Constant
	uint8_t 	bAssocTerminal;	// Constant
	uint8_t 	bCSourceID;	// Constant
	uint8_t 	bNrChannels;	// 
	uint32_t 	bmChannelConfig;	// 
	uint8_t 	iChannelNames;	// 
	uint16_t 	bmControls;	// 
	uint8_t 	iTerminal;	// 
	
}CS_AC_Interface_Input_Terminal_Descriptor;
/*
*******  Output Terminal Descriptor(uac2.0)
*/
typedef struct
{
	uint8_t 	bLength;	// 12
	uint8_t 	bDescriptorType;	// CS_INTERFACE 36
	uint8_t 	bDescriptorSubtype;	// OUTPUT_TERMINAL 3
	uint8_t 	bTerminalID;	// Constant
	uint16_t 	wTerminalType;	// Constant
	uint8_t 	bAssocTerminal;	// Constant
	uint8_t 	bSourceID;	// Constant
	uint8_t 	bCSourceID;	// Constant
	uint16_t 	bmControls;	// 
	uint8_t 	iTerminal;	// 
	
}CS_AC_Interface_Output_Terminal_Descriptor;
/*
*******  Feature Unit Descriptor(uac2.0)
The Feature Unit is uniquely identified by the value in the bUnitID field of the Feature Unit descriptor
(FUD). No other Unit or Terminal within the AudioControl interface may have the same ID. This value
must be passed in the Entity ID field (part of the wIndex field) of each request that is directed to the
Feature Unit.
*/
#define NUM_USB_CHAN_OUT 0x02 //0x01 // 0x03 .........
typedef struct
{
	uint8_t 	bLength;	// 6+(ch+1)*4
	uint8_t 	bDescriptorType;	// CS_INTERFACE 36
	uint8_t 	bDescriptorSubtype;	// OUTPUT_TERMINAL 3
	uint8_t 	bUnitID;	// Constant
	uint8_t 	bSourceID;	// Constant
	uint16_t 	bmaControls[NUM_USB_CHAN_OUT+1];	// bmaControls[0]:master  [1]:ch 1.................
	uint8_t 	iFeature;	// 
}CS_AC_Interface_Feature_Unit_Descriptor;

/*
4.8.1  AC Control Endpoint Descriptors
*******  Standard AC Control Endpoint Descriptor
Because endpoint 0 is used as the AudioControl control endpoint, there is no dedicated standard control
endpoint descriptor.
*******  Class-Specific AC Control Endpoint Descriptor
There is no dedicated class-specific control endpoint descriptor.
4.8.2  AC Interrupt Endpoint Descriptors
*******  Standard AC Interrupt Endpoint Descriptor(uac2.0)
*/
/*
4.9  AudioStreaming Interface Descriptors
The AudioStreaming (AS) interface descriptors contain all relevant information to characterize the
AudioStreaming interface in full.
4.9.1  Standard AS Interface Descriptor(uac2.0)
*/
typedef struct
{
	uint8_t 	bLength;	// 9
	uint8_t 	bDescriptorType;	// INTERFACE 4
	uint8_t 	bInterfaceNumber;	// Number of interface. A zero-based value identifying the index in the array of concurrent interfaces supported by this configuration.
	uint8_t 	bAlternateSetting;	// Number  Value used to select an Alternate Setting for the interface identified in the prior field.
	uint8_t 	bNumEndpoints;	// Number  Number of endpoints used by this interface (excluding endpoint 0). Must be either 0 (no data endpoint), 1 (data endpoint) or 2 (data and explicit feedback endpoint).
	uint8_t 	bInterfaceClass;	// Class  AUDIO Audio Interface Class code (assigned by the USB). See Appendix A.4, Audio Interface Class Code?
	uint8_t 	bInterfaceSubClass;	// Subclass  AUDIO_STREAMING Audio Interface Subclass code. Assigned by this specification. See Appendix A.5, Audio Interface Subclass Codes.?
	uint8_t	bInterfaceProtocol;	// Protocol  IP_VERSION_02_00 Interface Protocol  code. Indicates the current version of the specification. See Appendix A.6, Audio Interface Protocol Codes?
	uint8_t	iInterface;	// Index  Index of a string descriptor that describes this interface.
}standard_AS_Interface_Descriptor;
/*
4.9.2  Class-Specific AS Interface Descriptor(uac2.0)
*/
typedef struct
{
	uint8_t 	bLength;	// 16
	uint8_t 	bDescriptorType;	// CS_INTERFACE 36
	uint8_t 	bDescriptorSubtype;	// AS_GENERAL 0x01
	uint8_t 	bTerminalLink;	// Constant  The Terminal ID of the Terminal to which this interface is connected.
	uint8_t 	bmControls;	// Bitmap  D1..0:  Active Alternate Setting Control
							//     D3..2:  Valid Alternate Settings Control
							//     D7..4:  Reserved. Must be set to 0.
	uint8_t 	bFormatType;	//  Constant  Constant identifying the Format Type the AudioStreaming interface is using.
	uint32_t 	bmFormats;	// Bitmap  The Audio Data Format(s) that can be
						//used to communicate with this interface.
						//See the USB Audio Data Formats
						//document for further details.
	uint8_t	bNrChannels;	// Number  Number of physical channels in the AS Interface audio channel cluster.
	uint32_t	bmChannelConfig;	// Bitmap  Describes the spatial location of the physical channels.
	uint8_t	iChannelNames;	// Index  Index of a string descriptor, describing the name of the first physical channel.
}CS_AS_Interface_Descriptor;
/*
4.9.3  Class-Specific AS Format Type Descriptor(uac2.0)
*******  Type I Format Type Descriptor
*/
typedef struct
{
	uint8_t 	bLength;	// 6
	uint8_t 	bDescriptorType;	// CS_INTERFACE 36
	uint8_t 	bDescriptorSubtype;	// FORMAT_TYPE 0x02
	uint8_t 	bFormatType;	// Constant  FORMAT_TYPE_I 0x01
	uint8_t 	bSubslotSize;	// Number  The number of bytes occupied by one audio subslot. Can be 1, 2, 3 or 4.
	uint8_t 	bBitResolution;	//  umber  The number of effectively used bits from the available bits in an audio subslot.
}Type_I_Format_Type_Interface_Descriptor;

/*
4.10 AudioStreaming Endpoint Descriptors
The following sections describe all possible endpoint-related descriptors for the AudioStreaming interface.
4.10.1 AS Isochronous Audio Data Endpoint Descriptors
The standard and class-specific audio data endpoint descriptors provide pertinent information on how
audio data streams are communicated to the audio function. In addition, specific endpoint capabilities and
properties are reported.
******** Standard AS Isochronous Audio Data Endpoint Descriptor(uac2.0)
*/
typedef struct
{
	uint8_t 	bLength;	// 7
	uint8_t 	bDescriptorType;	// ENDPOINT 5
	uint8_t 	bEndpointAddress;	// Endpoint  The address of the endpoint on the USB device described by this descriptor. The
							//address is encoded as follows:
							//D3..0:  The endpoint number,
							//determined by the designer.
							//D6..4:  Reserved, reset to zero
							//D7:  Direction.
							//0 = OUT endpoint
							// 1 = IN endpoint
	uint8_t 	bmAttributes;	// Bit Map  D1..0:  Transfer type		01 = Isochronous
							// 	 D3..2:  Synchronization Type	01 = Asynchronous	10 = Adaptive	11 = Synchronous
							//	 D5..4:  Usage Type	00 = Data endpoint	or	10 = Implicit feedback Data	endpoint
							//All other bits are reserved.
	uint16_t 	wMaxPacketSize;	// Number  Maximum packet size this endpoint is capable of sending or receiving when this configuration is selected. This is determined by the audio bandwidth constraints of the endpoint.
	uint8_t 	bInterval;	// Number  Interval for polling endpoint for data transfers.
}Standard_AS_Iso_Audio_Data_Endpoint_Descriptor;

/********* Class-Specific AS Isochronous Audio Data Endpoint Descriptor(uac2.0)*/
typedef struct
{
	uint8_t 	bLength;	// 8
	uint8_t 	bDescriptorType;	// CS_ENDPOINT 5
	uint8_t 	bDescriptorSubtype;	// Constant  EP_GENERAL descriptor subtype. 0x01
	uint8_t 	bmAttributes;	// Bit D7 indicates a requirement for
						//wMaxPacketSize packets.
						//D7:  MaxPacketsOnly
	uint8_t 	bmControls;	// Bitmap  D1..0:  Pitch Control
								//D3..2:  Data Overrun Control
								//D5..4:  Data Underrun Control
								//D7..6:  Reserved. Must be set to 0.
	uint8_t 	bLockDelayUnits;	// Number  Indicates the units used for the wLockDelay field:
							// 0:  Undefined
							// 1:  Milliseconds
							// 2:  Decoded PCM samples
							// 3..255: Reserved
	uint8_t 	wLockDelay;	// Number  Indicates the time it takes this endpoint to reliably lock its internal clock recovery circuitry. Units used depend on the value of the bLockDelayUnits field.
}CS_AS_Iso_Audio_Data_Endpoint_Descriptor;
/*
4.10.2 AS Isochronous Feedback Endpoint Descriptor
This descriptor is present only when one or more isochronous audio data endpoints of the adaptive source
type or the asynchronous sink type are implemented.
******** Standard AS Isochronous Feedback Endpoint Descriptor(uac2.0)
*/
typedef struct
{
	uint8_t 	bLength;	// 7
	uint8_t 	bDescriptorType;	// ENDPOINT 5
	uint8_t 	bEndpointAddress;	// Endpoint  The address of the endpoint on the USB device described by this descriptor. The
							//address is encoded as follows:
							//D3..0:  The endpoint number,
							//determined by the designer.
							//D6..4:  Reserved, reset to zero
							//D7:  Direction.
							//0 = OUT endpoint
							// 1 = IN endpoint
	uint8_t 	bmAttributes;	// Bit Map  D1..0:  Transfer type		01 = Isochronous
							// 	 D3..2:  Synchronization Type	00 = No Synchronization
							//	 D5..4:  01 = Feedback endpoint
							//All other bits are reserved.
	uint16_t 	wMaxPacketSize;	// Number  Maximum packet size this endpoint is capable of sending or receiving when this configuration is selected. This is determined by the audio bandwidth constraints of the endpoint.
	uint8_t 	bInterval;	// Number  Interval for polling endpoint for data transfers.
}Standard_AS_Iso_Audio_Feedback_Endpoint_Descriptor;

/***********************************************************************/
/** USB Device Class Definition for Audio Data Formats **/

/* A.1 Format Type Codes */
enum USB_audio_Fmt_FormatType_t
{
    UAC_FORMAT_TYPE_UNDEFINED       = 0x00,
    UAC_FORMAT_TYPE_I               = 0x01,
    UAC_FORMAT_TYPE_II              = 0x02,
    UAC_FORMAT_TYPE_III             = 0x03,
    UAC_FORMAT_TYPE_IV              = 0x04,
    UAC_EXT_FORMAT_TYPE_I           = 0x81,
    UAC_EXT_FORMAT_TYPE_II          = 0x82,
    UAC_EXT_FORMAT_TYPE_III         = 0x83
};

/* A.2 AudioData Format Bit Allocation in the bmFormats field */
/* A.2.1 Audio Data Format Type I Bit Allocations */
enum USB_Audio_Fmt_DataFormat_TypeI_t
{
	UAC_FORMAT_TYPEI_PCM               = 0x00000001,
	UAC_FORMAT_TYPEI_PCM8              = 0x00000002,
	UAC_FORMAT_TYPEI_IEEE_FLOAT        = 0x00000004,
	UAC_FORMAT_TYPEI_ALAW        = 0x00000008,
	UAC_FORMAT_TYPEI_MULAW        = 0x00000010,
	UAC_FORMAT_TYPEI_RAW_DATA          = 0x80000000
};

/* A.2.2 Audio Data Format Type II Bit Allocations */
enum USB_Audio_Fmt_DataFormat_TypeII_t
{
    UAC_FORMAT_TYPEII_MPEG             = 0x00000001,
    UAC_FORMAT_TYPEII_AC3              = 0x00000002,
    UAC_FORMAT_TYPEII_WMA              = 0x00000004,
    UAC_FORMAT_TYPEII_DTS              = 0x00000008,
    UAC_FORMAT_TYPEII_RAW_DATA         = 0x80000000
};

/* A.3 Side Band Protocol Codes */
#define PROTOCOL_UNDEFINED             0x00
#define PRESS_TIMESTAMP_PROTOCOL       0x01

/***********************************************************************/
/* Univeral Serial Bus  Device Class Definition for Terminal Types */

/* 2.1 USB Terminal Types */
/* Terminal Types that describe Terminals that handle signals carried over USB */
#define USB_TERMTYPE_UNDEFINED         0x0100
#define USB_TERMTYPE_USB_STREAMING     0x0101
#define USB_TERMTYPE_VENDOR_SPECIFIC   0x01FF

/* 2.2 Input Terminal Types */
/* Terminal Types that describe Terminals that are designed to record sounds */
enum USB_Audio_TT_InputTermType_t
{
    UAC_TT_INPUT_TERMTYPE_INPUT_UNDEFINED               = 0x0200,
    UAC_TT_INPUT_TERMTYPE_MICROPHONE                    = 0x0201,
    UAC_TT_INPUT_TERMTYPE_DESKTOP_MICROPHONE            = 0x0202,
    UAC_TT_INPUT_TERMTYPE_PERSONAL_MICROPHONE           = 0x0203,
    UAC_TT_INPUT_TERMTYPE_OMNIDIRECTIONAL_MICROPHONE    = 0x0204,
    UAC_TT_INPUT_TERMTYPE_MICROPHONE_ARRAY              = 0x0205,
    UAC_TT_INPUT_TERMTYPE_PROCESSING_MICROPHONE_ARRAY   = 0x0206
};

/* 2.3 Output Terminal Types */
/* These Terminal Types describe Terminals that produce audible signals that are intended to
 * be heard by the user of the audio function */
enum USB_Audio_TT_OutputTermType_t
{
    UAC_TT_OUTPUT_TERMTYPE_SPEAKER                       = 0x0301,
    UAC_TT_OUTPUT_TERMTYPE_HEADPHONES                    = 0x0302,
    UAC_TT_OUTPUT_TERMTYPE_HEAD_MOUNTED_DISPLAY          = 0x0303,
    UAC_TT_OUTPUT_TERMTYPE_DESKTOP_SPEAKER               = 0x0304,
    UAC_TT_OUTPUT_TERMTYPE_ROOM_SPEAKER                  = 0x0305,
    UAC_TT_OUTPUT_TERMTYPE_COMMUNICATION_SPEAKER         = 0x0306,
    UAC_TT_OUTPUT_TERMTYPE_LOW_FREQ_EFFECTS_SPEAKER      = 0x0307
};


enum UAC_Set_Device_Type_t
{
    UAC_SET_DEVICE_VOLUME                       = 0x00,
    UAC_SET_DEVICE_CHANNEL                    = 0x01,
    UAC_SET_DEVICE_SAMPLERATE          = 0x02,
    UAC_SET_DEVICE_RESOLUTION               = 0x03,
    UAC_SET_DEVICE_MUTE               = 0x04
};

typedef struct
{
	unsigned int nurbs;		/* # urbs */
	
	unsigned int freqn;		/* nominal sampling rate in fs/fps in Q16.16 format */
	unsigned int freqm;		/* momentary sampling rate in fs/fps in Q16.16 format */
	int	   freqshift;		/* how much to shift the feedback value to get Q16.16 */
	unsigned int freqmax;		/* maximum sampling rate, used for buffer management */
	unsigned int phase;		/* phase accumulator */
	unsigned int maxpacksize;	/* max packet size in bytes */
	unsigned int maxframesize;      /* max packet size in frames */
	unsigned int max_urb_frames;	/* max URB size in frames */
	unsigned int curpacksize;	/* current packet size in bytes (for capture) */
	unsigned int curframesize;      /* current packet size in frames (for capture) */
	unsigned int syncmaxsize;	/* sync endpoint packet size */
	unsigned int fill_max:1;	/* fill max packet size always */
	unsigned int udh01_fb_quirk:1;	/* corrupted feedback data */
	unsigned int datainterval;      /* log_2 of data packet interval */
	unsigned int syncinterval;	/* P for adaptive mode, 0 otherwise */
	unsigned char silence_value;
	unsigned int stride;
//	unsigned int urbs;	/* number of urb  in kernel maybe more than one   in app 1? */
	unsigned int urb_packets;	/* packtes in per urb */
}EP_FMT;

   const char * uac_string_TerminalType(uint32_t code);
   const char * uac_string_FormatType(uint32_t code);
   const char * uac_string_Audio_Data_Format_Type_I_Bit_Allocations(uint32_t code);
   const char * uac_string_Audio_Data_Format_Type_II_Bit_Allocations(uint32_t code);
   const char * uac_string_Audio_Data_Format_Type_III_Bit_Allocations(uint32_t code);

    int uac_get_interface_associate_desc(struct libusb_config_descriptor *conf_desc, Interface_Association_Descriptor *associate);
    int uac_get_AC_interface_header_desc(uint8_t *buffer, CS_AC_Interface_Header_Descriptor *header);
    int uac_get_AC_interface_desc(struct libusb_interface_descriptor *interface_desc,
			enum UAC_CS_AC_InterfaceDescriptorSubtype_t type, char *desc);
    int uac_parse_AC_interface_desc(const struct libusb_interface_descriptor *interface_desc);
    int uac_parse_AS_interface_desc(const struct libusb_interface_descriptor *interface_desc);
    int uac_parse_AS_Endpoint_desc(const struct libusb_endpoint_descriptor *endpoint_desc);
    int uac_request_set_device(libusb_device_handle *handle, enum UAC_Set_Device_Type_t type, int value);
    unsigned get_usb_full_speed_rate(unsigned int rate);
    unsigned get_usb_high_speed_rate(unsigned int rate);
    int uac_usb_endpoint_next_packet_size(EP_FMT *ep);
    int uac_set_data_ep_params(EP_FMT *ep_fmt, EP_FMT *sync_ep, int rate ,int format, int channels,unsigned int period_bytes,unsigned int frames_per_period,unsigned int periods_per_buffer);
    void uac_printf_tt(void);
    int uac_config_analyse(libusb_device *uac_dev);
#ifdef __cplusplus
}
#endif


#endif//UAC_H
