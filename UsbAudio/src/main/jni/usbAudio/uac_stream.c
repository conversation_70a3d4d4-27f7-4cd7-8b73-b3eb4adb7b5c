//
// Created by <PERSON><PERSON> on 2021/11/11.
//
#include <errno.h>
#include <malloc.h>
#include <string.h>
#include <stdbool.h>
#include <stdlib.h>
#include "uac.h"
#include "uac_stream.h"
#include "../utilbase.h"
#if defined(__ANDROID__)
#include <unistd.h>
#include <sys/time.h>
#include <sys/resource.h>
#endif	// defined(__ANDROID__)

#define LOGD(...) \
    __android_log_print(ANDROID_LOG_DEBUG, "audio_stream", __VA_ARGS__)

void *uac_handle_events(void *arg) {
	uac_stream_handler_t *uac_handler = (uac_stream_handler_t *) arg;

#if defined(__ANDROID__)
	// try to increase thread priority
	int prio = getpriority(PRIO_PROCESS, 0);
	nice(-18);
	if (UNLIKELY(getpriority(PRIO_PROCESS, 0) >= prio)) {
		LOGW("could not change thread priority");
	}
#endif
	for (; !uac_handler->kill_handler_thread ;)
		libusb_handle_events(uac_handler->uac_ctx);
	return NULL;
}
void uac_start_handler_thread(uac_stream_handler_t *uac_handler) {
	if (uac_handler->uac_ctx) {
		pthread_create(&uac_handler->handler_thread, NULL, uac_handle_events, (void*) uac_handler);
	}
}

static void uac_capture_delete_transfer(struct libusb_transfer *transfer) {
    int i;

    if UNLIKELY(!transfer) return;
    uac_stream_handler_t *uac_handler = transfer->user_data;
    if UNLIKELY(!uac_handler) return;

    // Mark transfer as deleted.
    for (i = 0; i < uac_handler->captureTotalTransfers; i++) {
        if (uac_handler->capture_transfers[i] == transfer) {
            libusb_cancel_transfer(uac_handler->capture_transfers[i]);	// XXX 20141112追加
            LOGD("Freeing transfer %d (%p)", i, transfer);
            free(transfer->buffer);
            libusb_free_transfer(transfer);
            uac_handler->capture_transfers[i] = NULL;
            break;
        }
    }
}
static void uac_playback_delete_transfer(struct libusb_transfer *transfer) {
    int i;
	
    if UNLIKELY(!transfer) return;
    uac_stream_handler_t *uac_handler = transfer->user_data;
    if UNLIKELY(!uac_handler) return;

    // Mark transfer as deleted.
    for (i = 0; i < uac_handler->playbackTotalTransfers; i++) {
        if (uac_handler->playback_transfers[i] == transfer) {
            libusb_cancel_transfer(uac_handler->playback_transfers[i]);	// XXX 20141112追加
            LOGD("Freeing transfer %d (%p)", i, transfer);
            free(transfer->buffer);
            libusb_free_transfer(transfer);
            uac_handler->playback_transfers[i] = NULL;
            break;
        }
    }
}
void uac_capture_stream_callback(struct libusb_transfer *transfer) {
    int resubmit = 1;

    if UNLIKELY(!transfer) return;
    uac_stream_handler_t *uac_handler = transfer->user_data;
    if UNLIKELY(!uac_handler) return;

//    LOGD("uac_capture_stream_callback: uac_handler->captureRunning=%d ,status=%d,length=%d", uac_handler->captureRunning,transfer->status,transfer->length);
    switch (transfer->status) {
        case LIBUSB_TRANSFER_COMPLETED:
            if (transfer->num_iso_packets) {
                /* This is an isochronous mode transfer, so each packet has a payload transfer */
                uac_capture_payload_iso(transfer, uac_handler);
            }
            break;
        case LIBUSB_TRANSFER_NO_DEVICE:
            uac_handler->captureRunning = 0;
            LOGD("uac_capture_stream_callback NO_DEVICE:%d\n", uac_handler->captureRunning);
            // this needs for unexpected disconnect of cable otherwise hangup
            // pass through to following lines
        case LIBUSB_TRANSFER_CANCELLED:
            libusb_free_transfer(transfer);
            return;
        case LIBUSB_TRANSFER_ERROR:
            resubmit = 0;
            break;
        case LIBUSB_TRANSFER_TIMED_OUT:
        case LIBUSB_TRANSFER_STALL:
        case LIBUSB_TRANSFER_OVERFLOW:
            break;
    }
    if (LIKELY(uac_handler->captureRunning && resubmit)) {
        libusb_submit_transfer(transfer);
    } else {
        // XXX delete non-reusing transfer
        // real implementation of deleting transfer moves to _uvc_delete_transfer
        uac_capture_delete_transfer(transfer);
    }
}


void uac_playback_stream_callback(struct libusb_transfer *transfer) {
    int resubmit = 1;
    int ret = 0;

    if UNLIKELY(!transfer) return;
    uac_stream_handler_t *uac_handler = transfer->user_data;
    if UNLIKELY(!uac_handler) return;

//    LOGD("uac_playback_stream_callback: transfer=%p,uac_handler->playbackRunning=%d ,status=%d,length=%d", (int)transfer,uac_handler->playbackRunning,transfer->status,transfer->length);
    switch (transfer->status) {
        case LIBUSB_TRANSFER_COMPLETED:
            if (transfer->num_iso_packets) {
                /* This is an isochronous mode transfer, so each packet has a payload transfer */
                ret = uac_playback_payload_iso(transfer, uac_handler);
                if(ret < 0)
                    resubmit = 0;
            }else {
		  /* This is a bulk mode transfer, so it just has one payload transfer */
                uac_playback_payload(transfer, uac_handler);
                if(ret < 0)
                    resubmit = 0;
            }
            break;
        case LIBUSB_TRANSFER_NO_DEVICE:
            LOGD("uac_playback_stream_callback NO_DEVICE:\n");
            // this needs for unexpected disconnect of cable otherwise hangup
            // pass through to following lines
        case LIBUSB_TRANSFER_CANCELLED:
            libusb_free_transfer(transfer);
            return;
        case LIBUSB_TRANSFER_ERROR:
            resubmit = 0;
            break;
        case LIBUSB_TRANSFER_TIMED_OUT:
        case LIBUSB_TRANSFER_STALL:
        case LIBUSB_TRANSFER_OVERFLOW:
            break;
    }
    if (LIKELY(uac_handler->playbackRunning && resubmit)) {
        libusb_submit_transfer(transfer);
    } else {
        // XXX delete non-reusing transfer
        // real implementation of deleting transfer moves to _uvc_delete_transfer
        uac_playback_delete_transfer(transfer);
    }
}

void uac_capture_payload_iso(struct libusb_transfer *transfer, uac_stream_handler_t *uac_handler) {
    unsigned int i;
    int len = 0;
    int maxLen = 0;

    // Get an env handle
    JNIEnv *env;
    void *void_env;
    struct audio_byte_object *object = uac_handler->audio_object;
    JavaVM *java_vm = object->vm;
    bool had_to_attach = false;
    jint status = (*java_vm)->GetEnv(java_vm, &void_env, JNI_VERSION_1_6);

    if (status == JNI_EDETACHED) {
        (*java_vm)->AttachCurrentThread(java_vm, &env, NULL);
        had_to_attach = true;
    } else {
        env = void_env;
    }
    // Create a jbyteArray.

    //杂音问题找到
    for (int j = 0; j < transfer->num_iso_packets; ++j) {
        struct libusb_iso_packet_descriptor *pack = &transfer->iso_packet_desc[j];
        maxLen += pack->actual_length;
//        LOGD("libusb_iso_packet_descriptor actual_length:%d length:%d",pack->actual_length,pack->length);
    }
    if (maxLen <= 0) {
        LOGD("Error :maxLen is %d ", maxLen);
        goto out;
    }
//     LOGD("uac_capture_payload_iso :maxLen is %d ", maxLen);
    jbyteArray audioByteArray = (*env)->NewByteArray(env, maxLen);


    for (i = 0; i < transfer->num_iso_packets; i++) {

        struct libusb_iso_packet_descriptor *pack = &transfer->iso_packet_desc[i];

        if (pack->status != LIBUSB_TRANSFER_COMPLETED) {
            LOGD("libusb_iso_packet_descriptor actual_length:%d length:%d",
                 pack->actual_length,
                 pack->length);
            LOGD("Error (status %d: %s) errno: %s:", pack->status,
                 libusb_error_name(pack->status), strerror(errno));
            /* This doesn't happen, so bail out if it does. */
            goto out;
        }
        const uint8_t *data = libusb_get_iso_packet_buffer_simple(transfer, i);
        (*env)->SetByteArrayRegion(env, audioByteArray, len, pack->actual_length, (jbyte *) data);
        len += pack->actual_length;
    }
    // Call write()
//     LOGD("capture transfer :total len  is %d ", maxLen);
    if(uac_handler->captureRunning == 1) (*env)->CallVoidMethod(env, object->audioObject,
                           object->writeCapturePcmData, audioByteArray, maxLen);
    (*env)->DeleteLocalRef(env, audioByteArray);
    if ((*env)->ExceptionCheck(env)) {
        LOGD("Exception while trying to pass sound data to java");
        return;
    }

    out:
    if (had_to_attach) {
        (*java_vm)->DetachCurrentThread(java_vm);
    }
}
int uac_playback_payload_buffer(uac_stream_handler_t *uac_handler) {
    // Get an env handle
    JNIEnv *env;
    void *void_env;
    struct audio_byte_object *object = uac_handler->audio_object;
    JavaVM *java_vm = object->vm;
    bool had_to_attach = false;
    jint status = (*java_vm)->GetEnv(java_vm, &void_env, JNI_VERSION_1_6);

    if (status == JNI_EDETACHED) {
        (*java_vm)->AttachCurrentThread(java_vm, &env, NULL);
        had_to_attach = true;
    } else {
        env = void_env;
    }
    // Call readPlaybackPcmData()
   jobject data = (*env)->CallObjectMethod(env, object->audioObject,
                           object->readPlaybackPcmData, uac_handler->size_buf);
   if(data == NULL) 
   	return -1;//if file eof,return;
    jbyte* bytekey = (*env)->GetByteArrayElements(env, data, 0);
    int bytekeylen = (*env)->GetArrayLength(env, data);
    memcpy(uac_handler->holdbuf,bytekey,bytekeylen);
    uac_handler->hold_bytes = bytekeylen;
    if ((*env)->ExceptionCheck(env)) {
        LOGD("Exception while trying to pass sound data to java");
        return -1;
    }
    out:
    if (had_to_attach) {
        (*java_vm)->DetachCurrentThread(java_vm);
    }
    return 0;
}
void uac_swap_buffers(uac_stream_handler_t *uac_handler) {
	uint8_t *tmp_buf;

	/* swap the buffers */
	tmp_buf = uac_handler->outbuf;
	uac_handler->out_bytes = uac_handler->out_leave_bytes = uac_handler->hold_bytes;
	uac_handler->outbuf = uac_handler->holdbuf;
	uac_handler->holdbuf = tmp_buf;
	uac_handler->hold_bytes = 0;
//        memset(uac_handler->holdbuf, 0, uac_handler->size_buf);
//        memset(uac_handler->outbuf, 0, uac_handler->size_buf);
}

int uac_playback_payload_iso(struct libusb_transfer *transfer, uac_stream_handler_t *uac_handler) {
    unsigned int i;
    int offset = 0;
    int reload_flag;
    struct libusb_iso_packet_descriptor *pack;
    uint8_t *packet_data;
    int ret = 0;

    for (i = 0; i < transfer->num_iso_packets && (uac_handler->playbackRunning == 1); i++) {
        pack = &transfer->iso_packet_desc[i];
//	LOGD("playback iso transfer :pack id=%d,length=%d,actual_length=%d,status=%d", i, pack->length,pack->actual_length,pack->status);
        if (UNLIKELY(pack->status != 0)) {
            libusb_clear_halt(uac_handler->uac_devh, uac_handler->playbackEndpoint);
            continue;
        }
//	if (UNLIKELY(!pack->actual_length)) {	// why transfered byte is zero...
//		continue;
//	}
        reload_flag = 0;
        packet_data = libusb_get_iso_packet_buffer(transfer, i);
        if(uac_handler->out_leave_bytes >= pack->length) {
            offset = uac_handler->out_bytes -uac_handler->out_leave_bytes;
            memcpy(packet_data,uac_handler->outbuf+offset,pack->length);
            uac_handler->out_leave_bytes -= pack->length;
            if(uac_handler->out_leave_bytes == 0)
                reload_flag =1;
        }else{
            offset = uac_handler->out_bytes -uac_handler->out_leave_bytes;
#if 1
            memcpy(packet_data,uac_handler->outbuf+offset,uac_handler->out_leave_bytes);
#else
            memset(packet_data, packet_num, pack->length);
#endif
            uac_handler->out_leave_bytes = 0;
//            return -1;// return -1 for testing,stop this file transfer ???
            reload_flag =1;
        }
        if(reload_flag){
            uac_handler->out_leave_bytes = 0;
            ret = uac_playback_payload_buffer(uac_handler);
            if(ret < 0)
                return -1;
            uac_swap_buffers(uac_handler);
        }
    }
    return 0;
}

int uac_playback_payload(struct libusb_transfer *transfer, uac_stream_handler_t *uac_handler) {
    unsigned int i;
    int pos = 0;
    // Get an env handle
    JNIEnv *env;
    void *void_env;
    struct audio_byte_object *object = uac_handler->audio_object;
    JavaVM *java_vm = object->vm;
    bool had_to_attach = false;
    jint status = (*java_vm)->GetEnv(java_vm, &void_env, JNI_VERSION_1_6);

    if (status == JNI_EDETACHED) {
        (*java_vm)->AttachCurrentThread(java_vm, &env, NULL);
        had_to_attach = true;
    } else {
        env = void_env;
    }
    // Call readPlaybackPcmData()
   jobject data = (*env)->CallObjectMethod(env, object->audioObject,
                           object->readPlaybackPcmData, transfer->length);
   if(data == NULL) 
   	return -1;//if file eof,return;
	
    jbyte* bytekey = (*env)->GetByteArrayElements(env, data, 0);
    int bytekeylen = (*env)->GetArrayLength(env, data);

//    LOGD("aaaaa:%d, ddd:%d", bytekey[0], bytekeylen);
    //subpacket ??
    memset(transfer->buffer, 0, transfer->length);
    memcpy(transfer->buffer,bytekey,bytekeylen);
    transfer->actual_length = bytekeylen;
//    LOGD("playback bulk  transfer :total len  is %d ", bytekeylen);
    if ((*env)->ExceptionCheck(env)) {
        LOGD("Exception while trying to pass sound data to java");
        return -1;
    }
    out:
    if (had_to_attach) {
        (*java_vm)->DetachCurrentThread(java_vm);
    }
    return 0;
}
