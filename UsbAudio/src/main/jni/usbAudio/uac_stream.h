//
// Created by <PERSON><PERSON> on 2021/11/11.
//

#include <jni.h>
#include <stdint.h>
#include <pthread.h>
#include "../libusb/libusb/libusb.h"
#include "uac.h"


#ifndef UAC_STREAM_H
#define UAC_STREAM_H
#ifdef __cplusplus
extern "C" {
#endif

#define MAXNUM_TRANSFERS 4
#define MAXNUM_PACKETS 8
#define MAXOUTNUM_PACKETS 10
#define PACKET_SIZE 192
/*playback:2 ch 16bit 48000,one microframe(1 microsecond)=2*2*48000/1000=192bytes*/
#define XFER_BUF_SIZE	 (38400)//7680
#define MICROFRAME_SIZE	 (192)	
/*capture:1 ch 16bit 48000,one microframe(1 microsecond)=2*2*48000/1000=96bytes*/

struct audio_byte_object {
    JavaVM *vm;
    JNIEnv *env;
    jclass audio_class;
    jobject audioObject;
    jmethodID writeCapturePcmData;
    jmethodID readPlaybackPcmData;
};

struct uac_stream_handler {
    int inited;
    int captureRunning;
    int playbackRunning;
//    int running;

    int captureMaxPacketSize;
    int captureControlInterface;
    int captureInterface;
    int captureAltSetting;
    uint32_t captureEndpoint; /*1;*/

    int playbackMaxPacketSize;
    int playbackControlInterface;
    int playbackInterface;
    int playbackAltSetting ;
    uint32_t playbackEndpoint;

    int captureTotalTransfers;
    struct libusb_transfer *capture_transfers[MAXNUM_TRANSFERS];
    uint8_t *capture_transfer_bufs[MAXNUM_TRANSFERS];

    int playbackTotalTransfers;
    struct libusb_transfer *playback_transfers[MAXNUM_TRANSFERS];
    uint8_t *playback_transfer_bufs[MAXNUM_TRANSFERS];
    struct audio_byte_object *audio_object;


    struct libusb_context *uac_ctx;
    struct libusb_device_handle *uac_devh;
    struct libusb_device *uac_dev;
    struct libusb_config_descriptor *uac_config;
    //buffer for playback
    uint32_t size_buf;	
    uint32_t hold_bytes;
    uint8_t *holdbuf; 
    uint32_t out_bytes;
    uint32_t out_leave_bytes;
    uint8_t *outbuf;

    pthread_mutex_t cb_mutex;
    pthread_t handler_thread;
    uint8_t kill_handler_thread;
     
    EP_FMT *playback_data_ep_fmt;
    EP_FMT *playback_sync_ep_fmt;

    EP_FMT *capture_data_ep_fmt;
    EP_FMT *capture_sync_ep_fmt;
};

//audio_streaming_descriptor
struct audio_streaming_descriptor {
    //该结构体的字节度长，
    uint8_t bLength;
    //描述符类型CS_INTERFACE，值为0x24
    uint8_t bDescriptorType;
    //描述符子类型 FORMAT_TYPE
    uint8_t bDescriptorSubtype;
    //音频数据格式，这里为FORMAT_TYPE_I
    uint8_t bFormatType;
    //音频数据的通道数
    uint8_t bNrChannels;
    //每通道数据的字节数，可以1，2，3，4
    uint8_t bSubframeSize;
    //bSubframeSize中的有效位数。
    uint8_t bBitResolution;
    //在当前interface下有多少种采样率
    uint8_t bSamFreqType;
};




typedef struct uac_stream_handler uac_stream_handler_t;

void uac_start_handler_thread(uac_stream_handler_t *uac_handler);
void uac_capture_stream_callback(struct libusb_transfer *transfer);
void uac_playback_stream_callback(struct libusb_transfer *transfer);

void uac_capture_payload_iso(struct libusb_transfer *transfer, uac_stream_handler_t *uac_handler);
int uac_playback_payload_iso(struct libusb_transfer *transfer, uac_stream_handler_t *uac_handler);
int uac_playback_payload_buffer(uac_stream_handler_t *uac_handler);
void uac_swap_buffers(uac_stream_handler_t *uac_handler) ;
int uac_playback_payload(struct libusb_transfer *transfer, uac_stream_handler_t *uac_handler) ;

#ifdef __cplusplus
}
#endif

#endif //UAC_STREAM_H
