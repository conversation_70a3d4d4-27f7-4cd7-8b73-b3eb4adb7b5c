#include <errno.h>
#include <malloc.h>
#include <string.h>
#include <stdbool.h>
#include <stdlib.h>

#include "../utilbase.h"
#if defined(__ANDROID__)
#include <unistd.h>
#include <sys/time.h>
#include <sys/resource.h>
#endif	// defined(__ANDROID__)
#include "uac.h"
#include "uac_stream.h"

//#define container_of(ptr, type, member)   ((type *)((char *)(ptr) - (char *) &((type *)0)->member))
#define OFFSETOF_(TYPE, MEMBER) ((size_t) &((TYPE *)0)->MEMBER)

#define container_of_(ptr, type, member) ({			\
	const typeof( ((type *)0)->member ) *__mptr = (ptr);	\
	(type *)( (char *)__mptr - OFFSETOF_(type,member) );})



static int FU_USBIN = -1;	/* Feature Unit: USB Audio device -> host */
static int FU_USBOUT = -1;	/* Feature Unit: USB Audio host -> device*/
static int ID_IT_USB = -1;	/* Input terminal: USB streaming */
static int ID_IT_AUD = -1;	/* Input terminal: Analogue input */
static int ID_OT_USB = -1;	/* Output terminal: USB streaming */
static int ID_OT_AUD = -1;	/* Output terminal: Analogue output */

static int ID_CLKSEL = -1;	/* Clock selector ID */
static int ID_CLKSRC_INT = -1;	/* Clock source ID (internal) */
static int ID_CLKSRC_SPDIF = -1;		/* Clock source ID (external) */
static int ID_CLKSRC_ADAT = -1;		/* Clock source ID (external) */

static int ID_XU_MIXSEL = -1;
static int ID_XU_OUT = -1;
static int ID_XU_IN = -1;

static int ID_MIXER_1 = -1;

static int iso_endpoint_in = -1;
static int iso_endpoint_out = -1;

EP_FMT *ep_fmt;
EP_FMT *sync_ep;
static int s_volume = 0x8100;	// min 0x8100 max 0xff00
#if 1
/*
define some string
*/
/*class code string*/
static char *string_class_code[]=
{
	" ",
	"AUDIO",		// 1
	"Communications",
	"HID",
	" ",
	"Physical",		// 5
	" ",
	" ",
	" ",
	" ",
	" ",		// 0x0A
	" ",		// 0x0B
	""
};
/*A.5  Audio Interface Subclass Codes
Table A-5: Audio Interface Subclass Codes*/
static char *string_Audio_Interface_Subclass_Codes[]=
{
	"undefined",
	"AUDIO CONTROL",		// 1
	"AUDIO STREAMING",
	"MIDISTREAMING",
	""
};

static char *string_uac_type[]=
{
	" ",
	"uac 1.0",
	"uac 2.0",
	""
};
/*A.9  Audio Class-Specific AC Interface Descriptor Subtypes
Table A-9: Audio Class-Specific AC Interface Descriptor Subtypes*/
static char *string_uac_AC_Int_Desc_Subtypes[]=
{
	"undefined",
	"HEADER",		// 0x01
	"INPUT_TERMINAL",
	"OUTPUT_TERMINAL",
	"MIXER_UNIT",
	"SELECTOR_UNIT",	//0x05
	"FEATURE_UNIT",	//0x06
	"EFFECT_UNIT",	//0x07
	"PROCESSING_UNIT",	//0x08
	"EXTENSION_UNIT",	//0x09
	"CLOCK_SOURCE",	//0x0A
	"CLOCK_SELECTOR",	//0x0B
	"CLOCK_MULTIPLIER",	//0x0C
	"SAMPLE_RATE_CONVERTER",	//0x0D
	""
};
/*
A.10 Audio Class-Specific AS Interface Descriptor Subtypes
Table A-10: Audio Class-Specific AS Interface Descriptor Subtypes
*/
static char *string_uac_AS_Int_Desc_Subtypes[]=
{
	"undefined",
	"AS_GENERAL",		// 0x01
	"FORMAT_TYPE",
	"ENCODER",
	"DECODER",
	""
};

static char *string_usb_Standard_Endpoint_Descriptor_Transfer_Type[]=
{
	"Control",
	"Isochronous",
	"Bulk",
	"Interrupt",
	""
	
};
static char *string_usb_Standard_Endpoint_Descriptor_Synchronization_Type[]=
{
	"No Synchronization",
	"Asynchronous",
	"Adaptive",
	"Synchronous",
	""
};
static char *string_usb_Standard_Endpoint_Descriptor_Usage_Type[]=
{
	"Data endpoint",
	"Feedback endpoint",
	"Implicit feedback Data endpoint",
	"Reserved",
	""
	
};
/*A.13 Audio Class-Specific Endpoint Descriptor Subtypes
Table A-13: Audio Class-Specific Endpoint Descriptor Subtypes*/
static char *string_usb_ACS_Endpoint_Descriptor_Subtypes[]=
{
	"DESCRIPTOR_UNDEFINED",
	"EP_GENERAL",
	""
	
};
#endif
const char * uac_string_TerminalType(uint32_t code)
{
	switch (code) {
	case USB_TERMTYPE_UNDEFINED:
		return "USB Undefined";
	case USB_TERMTYPE_USB_STREAMING:
		return "USB streaming";
	case USB_TERMTYPE_VENDOR_SPECIFIC:
		return "USB vendor specific ";
	case UAC_TT_INPUT_TERMTYPE_INPUT_UNDEFINED:
		return "Input Undefined";
	case UAC_TT_INPUT_TERMTYPE_MICROPHONE:
		return "Microphone  ";
	case UAC_TT_INPUT_TERMTYPE_DESKTOP_MICROPHONE:
		return "Desktop microphone";
	case UAC_TT_INPUT_TERMTYPE_PERSONAL_MICROPHONE:
		return "Personal microphone";
	case UAC_TT_INPUT_TERMTYPE_OMNIDIRECTIONAL_MICROPHONE:
		return "Omni-directional microphone";
	case UAC_TT_INPUT_TERMTYPE_MICROPHONE_ARRAY:
		return "Microphone array";
	case UAC_TT_INPUT_TERMTYPE_PROCESSING_MICROPHONE_ARRAY:
		return "Processing microphone array";
	case UAC_TT_OUTPUT_TERMTYPE_SPEAKER:
		return "Speaker";
	case UAC_TT_OUTPUT_TERMTYPE_HEADPHONES:
		return "Headphones";
	case UAC_TT_OUTPUT_TERMTYPE_HEAD_MOUNTED_DISPLAY:
		return "Head Mounted Display Audio ";

	case UAC_TT_OUTPUT_TERMTYPE_DESKTOP_SPEAKER:
		return "Desktop speaker  ";
	case UAC_TT_OUTPUT_TERMTYPE_ROOM_SPEAKER:
		return "Room speaker ";
	case UAC_TT_OUTPUT_TERMTYPE_COMMUNICATION_SPEAKER:
		return "Communication speaker";
	case UAC_TT_OUTPUT_TERMTYPE_LOW_FREQ_EFFECTS_SPEAKER:
		return "Low frequency effects speaker";
	default:
		return "**UNKNOWN**";
	}
}

/*
Appendix A.  Additional Audio Device Class Codes
A.1  Format Type Codes
Table A-1: Format Type Codes
*/
const char * uac_string_FormatType(uint32_t code)
{
	switch (code) {
	case UAC_FORMAT_TYPE_UNDEFINED:
		return "FORMAT_TYPE_UNDEFINED";
	case UAC_FORMAT_TYPE_I:
		return "FORMAT_TYPE_I";
	case UAC_FORMAT_TYPE_II:
		return "FORMAT_TYPE_II";
	case UAC_FORMAT_TYPE_III:
		return "FORMAT_TYPE_III";
	case UAC_FORMAT_TYPE_IV:
		return "FORMAT_TYPE_IV";
	case UAC_EXT_FORMAT_TYPE_I:
		return "EXT_FORMAT_TYPE_I";
	case UAC_EXT_FORMAT_TYPE_II:
		return "EXT_FORMAT_TYPE_II";
	case UAC_EXT_FORMAT_TYPE_III:
		return "EXT_FORMAT_TYPE_III";
	default:
		return "**UNKNOWN**";
	}
}
/*A.2  Audio Data Format Bit Allocation in the bmFormats field
A.2.1  Audio Data Format Type I Bit Allocations
Table A-2: Audio Data Format Type I Bit Allocations*/
const char * uac_string_Audio_Data_Format_Type_I_Bit_Allocations(uint32_t code)
{
	switch (code ) {
	case UAC_FORMAT_TYPEI_PCM:
		return "PCM";
	case UAC_FORMAT_TYPEI_PCM8:
		return "PCM8";
	case UAC_FORMAT_TYPEI_IEEE_FLOAT:
		return "IEEE_FLOAT";
	case UAC_FORMAT_TYPEI_ALAW:
		return "ALAW";
	case UAC_FORMAT_TYPEI_MULAW:
		return "MULAW";
	case     UAC_FORMAT_TYPEI_RAW_DATA:
		return "TYPE_I_RAW_DATA";
	default:
		return "**UNKNOWN**";
	}
}
/*A.2.2  Audio Data Format Type II Bit Allocations
Table A-3: Audio Data Format Type II Bit Allocations*/
const char * uac_string_Audio_Data_Format_Type_II_Bit_Allocations(uint32_t code)
{
	switch (code ) {
	case UAC_FORMAT_TYPEII_MPEG:
		return "MPEG";
	case UAC_FORMAT_TYPEII_AC3:
		return "AC-3";
	case UAC_FORMAT_TYPEII_WMA:
		return "WMA";
	case UAC_FORMAT_TYPEII_DTS:
		return "DTS";
	case UAC_FORMAT_TYPEII_RAW_DATA:
		return "TYPE_II_RAW_DATA";
	default:
		return "**UNKNOWN**";
	}
}
/*A.2.3  Audio Data Format Type III Bit Allocations
Table A-4: Audio Data Format Type III Bit Allocations*/
const char * uac_string_Audio_Data_Format_Type_III_Bit_Allocations(uint32_t code)
{
	switch (code ) {
	default:
		return "**UNKNOWN**";
	}
}

int uac_get_interface_associate_desc(struct libusb_config_descriptor *conf_desc, Interface_Association_Descriptor *associate)
{
	Interface_Association_Descriptor _associate;
	unsigned char associate_data[LIBUSB_DT_INT_ASSOCIATION_SIZE] = {0};
	int desc_bDescriptorType = 0;
	int desc_bLength = 0;
	int r;
	int i;
	
	#if 0
	r = libusb_get_descriptor(handle, LIBUSB_DT_INTERFACE_ASSOCIATION, 0, associate_data, LIBUSB_DT_INT_ASSOCIATION_SIZE);
	if (r < 0) 
	{
		//if (r != LIBUSB_ERROR_PIPE)
		{
			//usbi_err(handle->dev->ctx, "failed to read  INTERFACE_ASSOCIATION(%d)", r);
			LOGD("failed to read  INTERFACE_ASSOCIATION(%s)\n", libusb_strerror((enum libusb_error)r));
		}
		return r;
	}
	
	if (r < LIBUSB_DT_INT_ASSOCIATION_SIZE) 
	{
		//usbi_err(handle->dev->ctx, "short INTERFACE_ASSOCIATION read %d/%d", r, LIBUSB_DT_INT_ASSOCIATION_SIZE);
		LOGD("short INTERFACE_ASSOCIATION read %d/%d", r,LIBUSB_DT_INT_ASSOCIATION_SIZE);
		return LIBUSB_ERROR_IO;
	}

	memcpy(&associate,associate_data,LIBUSB_DT_INT_ASSOCIATION_SIZE);
	for(i = 0;i<LIBUSB_DT_INT_ASSOCIATION_SIZE;i++)
		LOGD("%d  ",associate_data[i]);
	LOGD("\n");
	#else
	if(conf_desc)
	{
		if(conf_desc->extra_length && conf_desc->extra)
		{
			desc_bLength = conf_desc->extra[0];
			desc_bDescriptorType = conf_desc->extra[1];
			//LOGD("		type length[%d %d]\n",desc_bDescriptorType,desc_bLength);
			if(desc_bDescriptorType == LIBUSB_DT_INTERFACE_ASSOCIATION && desc_bLength ==LIBUSB_DT_INT_ASSOCIATION_SIZE)
			{
				memcpy(associate, conf_desc->extra,LIBUSB_DT_INT_ASSOCIATION_SIZE);
				//LOGD("		associate.bLength[%d]\n",associate.bLength);
			}
			
		}
	}
	#endif
	

	return r;
}

//AC: audio control
// header 
int uac_get_AC_interface_header_desc(uint8_t *buffer, CS_AC_Interface_Header_Descriptor *header)
{
	int r = -1;

	if(buffer && header)
	{
		if(buffer[0] == 9 && buffer[1]  == UAC_CS_DESCTYPE_INTERFACE && buffer[2] == UAC_CS_AC_INTERFACE_SUBTYPE_HEADER)
		{
			memcpy(header,buffer,9);
			r = 0;
		}
	}
	
	return r;
}

int uac_get_AC_interface_desc(struct libusb_interface_descriptor *interface_desc,
			enum UAC_CS_AC_InterfaceDescriptorSubtype_t type, char *desc)
{
	int r = 0;
	enum UAC_CS_AC_InterfaceDescriptorSubtype_t ac_int_desc_subtype;
	uint8_t *extra = NULL;
	int extra_length = 0;
	int desc_bDescriptorType = 0;
	int desc_bLength = 0;

	if(interface_desc && interface_desc->extra_length && interface_desc->extra)
	{
		extra = (uint8_t *)interface_desc->extra;
		extra_length = interface_desc->extra_length;
		while(extra_length >= 3)
		{
			
			if(extra[1] == UAC_CS_DESCTYPE_INTERFACE)
			{
				switch(extra[2])
				{
					case UAC_CS_AC_INTERFACE_SUBTYPE_HEADER:
					{
						if(type == UAC_CS_AC_INTERFACE_SUBTYPE_HEADER)
						{
							r = uac_get_AC_interface_header_desc(extra ,(CS_AC_Interface_Header_Descriptor *)desc);
							return r;
						}
						extra_length -= extra[0];
						extra += extra[0];
					}
					break;

					default:
					{
						extra_length -= extra[0];
						extra += extra[0];
					}
					break;
				}
			}
			else
			{
				extra_length -= extra[0];
				extra += extra[0];
			}
		}
	}
	return r;
}

int uac_parse_AC_interface_desc(const struct libusb_interface_descriptor *interface_desc)
{
	int r = 0;
	uint8_t *extra = NULL;
	int extra_length = 0;
	int desc_bDescriptorType = 0;
	int desc_bLength = 0;
	int i = 0;
	int ch = 0;
	int wTerminalType = 0;

	if(interface_desc && interface_desc->bInterfaceSubClass == UAC_INT_SUBCLASS_AUDIOCONTROL)
	{
		if( interface_desc->extra_length && interface_desc->extra)
		{
			extra = (uint8_t *)interface_desc->extra;
			extra_length = interface_desc->extra_length;
			while(extra_length > 3)
			{
				if(extra[1] == UAC_CS_DESCTYPE_INTERFACE)
				{
					switch(extra[2])
					{
						case UAC_CS_AC_INTERFACE_SUBTYPE_HEADER:
						{
							LOGD("      Class-Specific AudioControl Interface Header Descriptor:\n");
							LOGD("        bLength  \t\t    %d\n",extra[0]);
							LOGD("        bDescriptorType  \t    %d\n",extra[1]);
							LOGD("        bDescriptorSubtype  \t    %d  (%s)\n",extra[2],string_uac_AC_Int_Desc_Subtypes[extra[2]]);
							LOGD("        bcdADC  \t\t    0x%04x\n",(extra[3] |extra[4]<<8));
							LOGD("        bCategory  \t\t    %d\n",extra[5]);
							LOGD("        wTotalLength  \t\t    0x%04x[%d]\n",(extra[6]|extra[7]<<8),(extra[6]|extra[7]<<8));
							LOGD("        bmControls  \t\t    %d\n",extra[8]);
						}
						break;
						case UAC_CS_AC_INTERFACE_SUBTYPE_INPUT_TERMINAL:
						{
							LOGD("      Class-Specific AudioControl Interface Input Terminal Descriptor:\n");
							LOGD("        bLength  \t\t    %d\n",extra[0]);
							LOGD("        bDescriptorType  \t    %d\n",extra[1]);
							LOGD("        bDescriptorSubtype  \t    %d  (%s)\n",extra[2],string_uac_AC_Int_Desc_Subtypes[extra[2]]);
							LOGD("        bTerminalID  \t\t    %d\n",extra[3] );
							LOGD("        wTerminalType  \t\t    0x%04x  %s\n",extra[4]|extra[5]<<8,uac_string_TerminalType(extra[4]|extra[5]<<8));
							wTerminalType = extra[4]|extra[5]<<8;
							if(wTerminalType == USB_TERMTYPE_USB_STREAMING)
							{
								if(ID_IT_USB == -1)
									ID_IT_USB = extra[3];
							}
							else if(wTerminalType == UAC_TT_INPUT_TERMTYPE_MICROPHONE)
							{
								if(ID_IT_AUD == -1)
									ID_IT_AUD = extra[3];
							}
							LOGD("        bAssocTerminal  \t    %d\n", extra[6]);
							LOGD("        bCSourceID  \t\t    %d\n",extra[7]);
							LOGD("        bNrChannels  \t\t    %d\n",extra[8]);
							LOGD("        bmChannelConfig  \t    %d\n",extra[9] | extra[10]<<8|extra[11]<<16|extra[12]<<24);
							LOGD("        iChannelNames  \t\t    %d\n",extra[13]);
							LOGD("        bmControls  \t\t    %d\n",extra[14]|extra[15]<<8);
							LOGD("        iTerminal  \t\t    %d\n",extra[16]);
						}
						break;
						case UAC_CS_AC_INTERFACE_SUBTYPE_OUTPUT_TERMINAL:
						{
							LOGD("      Class-Specific AudioControl Interface Output Terminal Descriptor:\n");
							LOGD("        bLength  \t\t    %d\n",extra[0]);
							LOGD("        bDescriptorType  \t    %d\n",extra[1]);
							LOGD("        bDescriptorSubtype  \t    %d  (%s)\n",extra[2],string_uac_AC_Int_Desc_Subtypes[extra[2]]);
							LOGD("        bTerminalID  \t\t    %d\n",extra[3] );
							LOGD("        wTerminalType  \t\t    0x%04x  %s\n",extra[4]|extra[5]<<8,uac_string_TerminalType(extra[4]|extra[5]<<8));
							wTerminalType = extra[4]|extra[5]<<8;
							if(wTerminalType == USB_TERMTYPE_USB_STREAMING)
							{
								if(ID_OT_USB == -1)
									ID_OT_USB = extra[3];
							}
							else if(wTerminalType == UAC_TT_OUTPUT_TERMTYPE_SPEAKER)
							{
								if(ID_OT_AUD == -1)
									ID_OT_AUD = extra[3];
							}
							LOGD("        bAssocTerminal  \t    %d\n", extra[6]);
							LOGD("        bSourceID  \t\t    %d\n",extra[7]);
							LOGD("        bCSourceID  \t\t    %d\n",extra[8]);
							LOGD("        bmControls  \t\t    %d\n",extra[9]|extra[10]<<8);
							LOGD("        iTerminal	  \t    %d\n",extra[16]);
						}
						break;
						case UAC_CS_AC_INTERFACE_SUBTYPE_MIXER_UNIT:
						{
							LOGD("      Class-Specific AudioControl Interface Clock Selector Descriptor:\n");
							LOGD("        bLength  \t\t    %d\n",extra[0]);
							LOGD("        bDescriptorType  \t    %d\n",extra[1]);
							LOGD("        bDescriptorSubtype  \t    %d  (%s)\n",extra[2],string_uac_AC_Int_Desc_Subtypes[extra[2]]);
							LOGD("        bUnitID  \t\t    %d\n",extra[3]);
							LOGD("        bNrInPins  \t\t    %d\n",extra[4]);
							for(i = 0; i<extra[4]; i++)
							{
							LOGD("        baSourceID[%d]  \t    %d\n",i, extra[5+i]);
							
							}
							LOGD("        bNrChannels  \t\t    %d\n",extra[5+extra[4]]);
							LOGD("        bmChannelConfig  \t    %d\n",extra[6+extra[4]] | extra[7+extra[4]]<<8 | extra[8+extra[4]]<<16 |extra[9+extra[4]]<<24);
							LOGD("        iChannelNames  \t    %d\n",extra[10+extra[4]]);
							LOGD("        bmMixerControls  \t    %d\n",extra[11+extra[4]]);
							LOGD("        bmControls  \t\t    0x%02x\n",extra[extra[0]-2]);
							LOGD("        iMixer  \t\t    %d\n",extra[extra[0]-1]);
						}
						break;

						case UAC_CS_AC_INTERFACE_SUBTYPE_SELECTOR_UNIT:
						{
							LOGD("      Class-Specific AudioControl Interface Selector Unit Descriptor:\n");
							LOGD("        bLength  \t\t    %d\n",extra[0]);
							LOGD("        bDescriptorType  \t    %d\n",extra[1]);
							LOGD("        bDescriptorSubtype  \t    %d  (%s)\n",extra[2],string_uac_AC_Int_Desc_Subtypes[extra[2]]);
							LOGD("        bUnitID  \t\t    %d\n",extra[3]);
							LOGD("        bNrInPins  \t\t    %d\n",extra[4]);
							for(i = 0; i<extra[4]; i++)
							{
							LOGD("        baSourceID[%d]  \t    %d\n",i, extra[5+i]);
							
							}
							LOGD("        bmControls  \t\t    0x%02x\n",extra[extra[0]-2]);
							LOGD("        iSelector  \t\t    %d\n",extra[extra[0]-1]);
						}
						break;
						case UAC_CS_AC_INTERFACE_SUBTYPE_FEATURE_UNIT:
						{
							LOGD("      Class-Specific AudioControl Interface Feature Unit Descriptor:\n");
							LOGD("        bLength  \t\t    %d\n",extra[0]);
							LOGD("        bDescriptorType  \t    %d\n",extra[1]);
							LOGD("        bDescriptorSubtype  \t    %d  (%s)\n",extra[2],string_uac_AC_Int_Desc_Subtypes[extra[2]]);
							LOGD("        bUnitID  \t\t    %d\n",extra[3]);
							LOGD("        bSourceID  \t\t    %d\n",extra[4]);
							if(extra[4] == ID_IT_USB)
							{
								if(FU_USBOUT == -1)
									FU_USBOUT = extra[3];
							}
							else if(extra[4] == ID_OT_USB)
							{
								if(FU_USBIN == -1)
									FU_USBIN = extra[3];
							}
							ch = (extra[0] -6)/4;
							for(i = 0; i<ch; i++)
							{
							LOGD("        bmaControls[%d]  \t    0x%02x\n",i, extra[5+i*4]|extra[5+i*4+1]<<8|extra[5+i*4+2]<<16|extra[5+i*4+3]<<24 );
							
							}
							LOGD("        iSelector  \t\t    %d\n",extra[extra[0]-1]);
						}
						break;
						case UAC_CS_AC_INTERFACE_SUBTYPE_EFFECT_UNIT:
						{
							LOGD("      Class-Specific AudioControl Interface Common Part of the Effect Unit Descriptor:\n");
							LOGD("        bLength  \t\t    %d\n",extra[0]);
							LOGD("        bDescriptorType  \t    %d\n",extra[1]);
							LOGD("        bDescriptorSubtype  \t    %d  (%s)\n",extra[2],string_uac_AC_Int_Desc_Subtypes[extra[2]]);
							LOGD("        bUnitID  \t\t    %d\n",extra[3]);
							LOGD("        wEffectType  \t\t    0x%04x\n",extra[4]|extra[5]<<8);
							LOGD("        bSourceID  \t\t    %d\n",extra[6]);
							ch = (extra[0] -16)/4;
							for(i = 0; i<ch; i++)
							{
							LOGD("        bmaControls[%d]  \t    0x%02x\n",i, extra[7+i*4]|extra[7+i*4+1]<<8|extra[7+i*4+2]<<16|extra[7+i*4+3]<<24 );
							
							}
							LOGD("        iSelector  \t\t    %d\n",extra[extra[0]-1]);
						}
						break;
						case UAC_CS_AC_INTERFACE_SUBTYPE_PROCESSING_UNIT:
						{
							LOGD("      Class-Specific AudioControl Interface Common Part of the Processing Unit Descriptor:\n");
							LOGD("        bLength  \t\t    %d\n",extra[0]);
							LOGD("        bDescriptorType  \t    %d\n",extra[1]);
							LOGD("        bDescriptorSubtype  \t    %d  (%s)\n",extra[2],string_uac_AC_Int_Desc_Subtypes[extra[2]]);
							LOGD("        bUnitID  \t\t    %d\n",extra[3]);
							LOGD("        wProcessType  \t\t    %d\n",extra[4]|extra[5]<<8);
							LOGD("        bNrInPins  \t\t    %d\n",extra[6]);
							for(i = 0; i<extra[6]; i++)
							{
							LOGD("        baSourceID[%d]  \t    %d\n",i, extra[7+i]);
							
							}
							LOGD("        bmChannelConfig  \t    %d\n",extra[8+extra[6]] | extra[9+extra[6]]<<8 | extra[10+extra[6]]<<16 |extra[11+extra[6]]<<24);
							LOGD("        iChannelNames  \t    %d\n",extra[12+extra[6]]);
							LOGD("        bmControls  \t\t    0x%04x\n",extra[13+extra[6]]|extra[14+extra[6]]<<8);
							LOGD("        iProcessing  \t\t    %d\n",extra[extra[0]-2]);
							LOGD("        Process-specific  \t    %d\n",extra[extra[0]-1]);
						}
						break;
						case UAC_CS_AC_INTERFACE_SUBTYPE_EXTENSION_UNIT:
						{
							LOGD("      Class-Specific AudioControl Interface Common Part of the Processing Unit Descriptor:\n");
							LOGD("        bLength  \t\t    %d\n",extra[0]);
							LOGD("        bDescriptorType  \t    %d\n",extra[1]);
							LOGD("        bDescriptorSubtype  \t    %d  (%s)\n",extra[2],string_uac_AC_Int_Desc_Subtypes[extra[2]]);
							LOGD("        bUnitID  \t\t    %d\n",extra[3]);
							LOGD("        wExtensionCode  \t    %d\n",extra[4]|extra[5]<<8);
							LOGD("        bNrInPins  \t\t    %d\n",extra[6]);
							for(i = 0; i<extra[6]; i++)
							{
							LOGD("        baSourceID[%d]  \t    %d\n",i, extra[7+i]);
							
							}
							LOGD("        bNrChannels  \t\t    %d\n",extra[8+extra[6]]);
							LOGD("        bmChannelConfig  \t    %d\n",extra[9+extra[6]] | extra[10+extra[6]]<<8 | extra[11+extra[6]]<<16 |extra[12+extra[6]]<<24);
							LOGD("        iChannelNames  \t    %d\n",extra[13+extra[6]]);
							LOGD("        bmControls  \t\t    %d\n",extra[14+extra[6]]);
							LOGD("        iExtension  \t\t    %d\n",extra[15+extra[6]]);
						}
						break;
						case UAC_CS_AC_INTERFACE_SUBTYPE_CLOCK_SOURCE:
						{
							LOGD("      Class-Specific AudioControl Interface Clock Source Descriptor:\n");
							LOGD("        bLength  \t\t    %d\n",extra[0]);
							LOGD("        bDescriptorType  \t    %d\n",extra[1]);
							LOGD("        bDescriptorSubtype  \t    %d  (%s)\n",extra[2],string_uac_AC_Int_Desc_Subtypes[extra[2]]);
							LOGD("        bClockID  \t\t    %d\n",extra[3]);
							if(ID_CLKSRC_INT == -1)
								ID_CLKSRC_INT = extra[3];
							LOGD("        bmAttributes  \t\t    0x%02x\n",extra[4]);
							LOGD("        bmControls  \t\t    0x%02x\n",extra[5]);
							LOGD("        bAssocTerminal  \t    %d\n",extra[6]);
							LOGD("        iClockSource  \t\t    %d\n",extra[7]);
						}
						break;
						case UAC_CS_AC_INTERFACE_SUBTYPE_CLOCK_SELECTOR:
						{
							LOGD("      Class-Specific AudioControl Interface Clock Selector Descriptor:\n");
							LOGD("        bLength  \t\t    %d\n",extra[0]);
							LOGD("        bDescriptorType  \t    %d\n",extra[1]);
							LOGD("        bDescriptorSubtype  \t    %d  (%s)\n",extra[2],string_uac_AC_Int_Desc_Subtypes[extra[2]]);
							LOGD("        bClockID  \t\t    %d\n",extra[3]);
							if(ID_CLKSEL == -1)
								ID_CLKSEL = extra[3];
							LOGD("        bNrInPins  \t\t    %d\n",extra[4]);
							for(i = 0; i<extra[4]; i++)
							{
							LOGD("        baCSourceID[%d]  \t    %d\n",i, extra[5+i]);
							
							}
							LOGD("        bmControls  \t\t    0x%02x\n",extra[5+extra[4]]);
							LOGD("        iClockSelector  \t    %d\n",extra[6+extra[4]]);
						}
						break;
						case UAC_CS_AC_INTERFACE_SUBSYPE_CLOCK_MULTIPLIER:
						{
							LOGD("      Class-Specific AudioControl Interface Clock Multiplier Descriptor:\n");
							LOGD("        bLength  \t\t    %d\n",extra[0]);
							LOGD("        bDescriptorType  \t    %d\n",extra[1]);
							LOGD("        bDescriptorSubtype  \t    %d  (%s)\n",extra[2],string_uac_AC_Int_Desc_Subtypes[extra[2]]);
							LOGD("        bClockID  \t\t    %d\n",extra[3]);
							LOGD("        bCSourceID  \t\t    %d\n",extra[4]);
							LOGD("        bmControls  \t\t    0x%02x\n",extra[5]);
							LOGD("        iClockMultiplier  \t    %d\n",extra[7]);
						}
						break;
						case UAC_CS_AC_INTERFACE_SUBTYPE_SAMPLE_RATE_CONVERTER:
						{
							LOGD("      Class-Specific AudioControl Interface  Sampling Rate Converter Unit Descriptor:\n");
							LOGD("        bLength  \t\t    %d\n",extra[0]);
							LOGD("        bDescriptorType  \t    %d\n",extra[1]);
							LOGD("        bDescriptorSubtype  \t    %d  (%s)\n",extra[2],string_uac_AC_Int_Desc_Subtypes[extra[2]]);
							LOGD("        bUnitID  \t\t    %d\n",extra[3]);
							LOGD("        bSourceID  \t\t    %d\n",extra[4]);
							LOGD("        bCSourceInID  \t\t    %d\n",extra[5]);
							LOGD("        bCSourceOutID  \t\t    %d\n",extra[6]);
							LOGD("        iSRC  \t\t    %d\n",extra[7]);
						}
						break;
						default:
						{
						}
						break;
					}
					extra_length -= extra[0];
					extra += extra[0];
				}
				else
				{
					extra_length -= extra[0];
					extra += extra[0];
				}
	
			}
		}
	}

	return r;
}

int uac_parse_AS_interface_desc(const struct libusb_interface_descriptor *interface_desc)
{
	int r = 0;
	uint8_t *extra = NULL;
	int extra_length = 0;
	int desc_bDescriptorType = 0;
	int desc_bLength = 0;
	int i = 0;
	int ch = 0;
	
	if(interface_desc && interface_desc->bInterfaceSubClass == UAC_INT_SUBCLASS_AUDIOSTREAMING)
	{
		if( interface_desc->extra_length && interface_desc->extra)
		{
			extra = (uint8_t *)interface_desc->extra;
			extra_length = interface_desc->extra_length;
			while(extra_length > 3)
			{
				if(extra[1] == UAC_CS_DESCTYPE_INTERFACE)
				{
					switch(extra[2])
					{
						case UAC_CS_AS_INTERFACE_SUBTYPE_AS_GENERAL:
						{
							LOGD("      Class-Specific AudioStreaming Interface Descriptor:\n");
							LOGD("        bLength  \t\t    %d\n",extra[0]);
							LOGD("        bDescriptorType  \t    %d\n",extra[1]);
							LOGD("        bDescriptorSubtype  \t    %d  (%s)\n",extra[2],string_uac_AS_Int_Desc_Subtypes[extra[2]]);
							LOGD("        bTerminalLink  \t\t    %d\n",extra[3]);
							LOGD("        bmControls  \t\t    %d\n",extra[4]);
							LOGD("        bFormatType  \t\t    %d  (%s)\n",extra[5],string_uac_AS_Int_Desc_Subtypes[extra[5]]);
							LOGD("        bmFormats  \t\t    0x%08x   ",extra[6]|extra[7]<<8|extra[8]<<16|extra[9]<<24);
							switch(extra[5])
							{
								case UAC_FORMAT_TYPE_I:
									LOGD("(%s)\n",uac_string_Audio_Data_Format_Type_I_Bit_Allocations(extra[6]|extra[7]<<8|extra[8]<<16|extra[9]<<24));
								break;
								case UAC_FORMAT_TYPE_II:
									LOGD("(%s)\n",uac_string_Audio_Data_Format_Type_II_Bit_Allocations(extra[6]|extra[7]<<8|extra[8]<<16|extra[9]<<24));
								break;
								default:
								break;
							}
							LOGD("        bNrChannels  \t\t    %d\n",extra[10]);
							LOGD("        bmChannelConfig  \t    0x%08x\n",extra[11]|extra[12]<<8|extra[13]<<16|extra[14]<<24);
							LOGD("        iChannelNames  \t\t    %d\n",extra[15]);
						}
						break;
						case UAC_CS_AS_INTERFACE_SUBTYPE_FORMAT_TYPE:
						{
							LOGD("      Class-Specific AudioStreaming Interface Format Type Descriptor:\n");
							LOGD("        bLength  \t\t    %d\n",extra[0]);
							LOGD("        bDescriptorType  \t    %d\n",extra[1]);
							LOGD("        bDescriptorSubtype  \t    %d  (%s)\n",extra[2],string_uac_AS_Int_Desc_Subtypes[extra[2]]);
							LOGD("        bFormatType  \t\t    %d  (%s)\n",extra[3], uac_string_FormatType(extra[3]));
							switch(extra[3])
							{
								case UAC_FORMAT_TYPE_I:
									LOGD("        bSubslotSize  \t\t    %d\n",extra[4]);
									LOGD("        bBitResolution  \t    %d\n",extra[5]);
								break;
								case UAC_FORMAT_TYPE_II:
								break;
								default:
								break;
							}
						}
						break;
						default:
						{
						}
						break;
					}
					extra_length -= extra[0];
					extra += extra[0];
				}
				else
				{
					extra_length -= extra[0];
					extra += extra[0];
				}
	
			}
		}	
	}

	return r;
}

int uac_parse_AS_Endpoint_desc(const struct libusb_endpoint_descriptor *endpoint_desc)
{
	int r = 0;
	uint8_t *extra = NULL;
	int extra_length = 0;
	int desc_bDescriptorType = 0;
	int desc_bLength = 0;
	int i = 0;
	int ch = 0;

		if( endpoint_desc->extra_length && endpoint_desc->extra)
		{
			extra = (uint8_t *)endpoint_desc->extra;
			extra_length = endpoint_desc->extra_length;
			while(extra_length > 3)
			{
				if(extra[1] == UAC_CS_DESCTYPE_ENDPOINT && extra[2] == UAC_CS_ENDPOINT_SUBTYPE_EP_GENERAL )
				{
					LOGD("        AudioStreaming Endpoint Descriptor :\n");
					LOGD("          bLength  \t\t    %d\n",extra[0]);
					LOGD("          bDescriptorType  \t    %d\n",extra[1]);
					LOGD("          bDescriptorSubtype  \t    %d  (%s)\n",extra[2],string_usb_ACS_Endpoint_Descriptor_Subtypes[extra[2]]);
					LOGD("          bmAttributes  \t    0x%02x\n",extra[3]);
					LOGD("          bmControls  \t\t    0x%02x\n",extra[4]);
					LOGD("          bLockDelayUnits  \t    %d\n",extra[5]);
					LOGD("          wLockDelay  \t\t    %d\n",extra[6]|extra[7]);
					extra_length -= extra[0];
					extra += extra[0];
				}
				else
				{
					extra_length -= extra[0];
					extra += extra[0];
				}
	
			}
		}	
	

	return r;
}

/*
bmRequest Type  		| bRequest	| wValue		| wIndex		| wLength | Data
00100001B				CUR			CS			Entity ID
10100001B				RANGE		and			and
									CN or MCN	Interface

00100010B										Endpoint
10100010B

Set request (D7 = 0b0)	CUR=0x01	Control Selector (CS) HB
Get request (D7 = 0b1)	RANGE=0x02  Channel Number (CN) LB
class-specific request 
(D6..5 = 0b01)
 an interface 
 (AudioControl or
AudioStreaming) 
of the audio function
(D4..0 = 0b00001)
 the isochronous endpoint 
 of an
AudioStreaming 
interface 
(D4..0 = 0b00010)
*/
/*
how to enlope the control data;
1:bRequest-->CUR/RANGE
		Inspect request, NOTE: these are class specific requests
2:wIndex >> 8 --->ID_CLKSRC_INT/ID_CLKSRC_SPDIF/ID_CLKSRC_ADAT/ID_CLKSEL/FU_USBOUT/FU_USBIN/
		Extract unitID from wIndex
3:wValue >> 8 --->CS_SAM_FREQ_CONTROL/CS_CLOCK_VALID_CONTROL/.../FU_VOLUME_CONTROL/FU_MUTE_CONTROL
		
*/
int uac_request_set_device(libusb_device_handle *handle, enum UAC_Set_Device_Type_t type, int value)
{
	int r = 0;
	unsigned char  data[0x10] = {0};

    if UNLIKELY(!handle) return -1;
	if(FU_USBOUT < 0)
	{
		LOGD("FU_USBOUT  is error\n");
		return -1;
	}

	LOGD("uac_request_set_device\n");
	switch(type)
	{
		case UAC_SET_DEVICE_VOLUME:
		{
			//handle, bmRequest Type, bRequest, wValue , wIndex, *data, wLength, timeout
			//libusb_control_transfer(handle , 0x21, UAC_ACS_REQUEST_CUR, 0, );
			data[0] = value &0xFF;
			data[1] = (value>>8) & 0xFF;
			libusb_control_transfer(handle , (uint8_t)0x21, UAC_ACS_REQUEST_CUR,  1 | (FU_VOLUME_CONTROL<<8),
								0|(FU_USBOUT<<8), data, 0x02,1000);
			libusb_control_transfer(handle , (uint8_t)0x21, UAC_ACS_REQUEST_CUR,  2 | (FU_VOLUME_CONTROL<<8),
								0|(FU_USBOUT<<8), data, 0x02,1000);
		}
		break;

		case UAC_SET_DEVICE_CHANNEL:
		{
			//handle, bmRequest Type, bRequest, wValue , wIndex, *data, wLength, timeout
			//libusb_control_transfer(handle , 0x21, UAC_ACS_REQUEST_CUR, 0, );
			data[0] = value &0xFF;
			data[1] = (value>>8) & 0xFF;
			libusb_control_transfer(handle , (uint8_t)0x21, UAC_ACS_REQUEST_CUR,  1 | (FU_VOLUME_CONTROL<<8),
								0|(FU_USBOUT<<8), data, 0x02,1000);
			libusb_control_transfer(handle , (uint8_t)0x21, UAC_ACS_REQUEST_CUR,  2 | (FU_VOLUME_CONTROL<<8),
								0|(FU_USBOUT<<8), data, 0x02,1000);
		}
		break;
		
		case UAC_SET_DEVICE_SAMPLERATE:
		{
			//handle, bmRequest Type, bRequest, wValue , wIndex, *data, wLength, timeout
			//libusb_control_transfer(handle , 0x21, UAC_ACS_REQUEST_CUR, 0, );
			data[0] = value &0xFF;
			data[1] = (value>>8) & 0xFF;
			data[2] = (value>>16) & 0xFF;
			data[3] = (value>>24) & 0xFF;
			libusb_control_transfer(handle , (uint8_t)0x21, UAC_ACS_REQUEST_CUR,  0 | (CS_SAM_FREQ_CONTROL<<8),
								0|(ID_CLKSRC_INT<<8), data, 0x04,1000);
		}
		break;
		
		case UAC_SET_DEVICE_RESOLUTION:
		{
		}
		break;
		
		case UAC_SET_DEVICE_MUTE:
		{
			/*
			  29.0  CTL    21 01 01 01  00 0a 01 00  SET CUR                 	25.1.0        10:03:26.273  
			  29.0  OUT    01                        .                       			25.2.0        10:03:26.273  
			  29.0  CTL    21 01 02 01  00 0a 01 00  SET CUR                 	26.1.0        10:03:26.273  
			  29.0  OUT    01                        .                       			26.2.0        10:03:26.274  
			*/
			data[0] = value &0xFF;
			libusb_control_transfer(handle , (uint8_t)0x21, UAC_ACS_REQUEST_CUR,  1 | (FU_MUTE_CONTROL<<8),
								0|(FU_USBOUT<<8), data, 0x01,1000);
			libusb_control_transfer(handle , (uint8_t)0x21, UAC_ACS_REQUEST_CUR,  2 | (FU_MUTE_CONTROL<<8),
								0|(FU_USBOUT<<8), data, 0x01,1000);
		}
		break;
		
		default:
		{
		}
		break;
	}
	return r;
}
/*
 * snd_usb_endpoint is a model that abstracts everything related to an
 * USB endpoint and its streaming.
 *
 * There are functions to activate and deactivate the streaming URBs and
 * optional callbacks to let the pcm logic handle the actual content of the
 * packets for playback and record. Thus, the bus streaming and the audio
 * handlers are fully decoupled.
 *
 * There are two different types of endpoints in audio applications.
 *
 * SND_USB_ENDPOINT_TYPE_DATA handles full audio data payload for both
 * inbound and outbound traffic.
 *
 * SND_USB_ENDPOINT_TYPE_SYNC endpoints are for inbound traffic only and
 * expect the payload to carry Q10.14 / Q16.16 formatted sync information
 * (3 or 4 bytes).
 *
 * Each endpoint has to be configured prior to being used by calling
 * snd_usb_endpoint_set_params().
 *
 * The model incorporates a reference counting, so that multiple users
 * can call snd_usb_endpoint_start() and snd_usb_endpoint_stop(), and
 * only the first user will effectively start the URBs, and only the last
 * one to stop it will tear the URBs down again.
 */

/*
 * convert a sampling rate into our full speed format (fs/1000 in Q16.16)
 * this will overflow at approx 524 kHz
 */
unsigned get_usb_full_speed_rate(unsigned int rate)
{
	return ((rate << 13) + 62) / 125;
}

/*
 * convert a sampling rate into USB high speed format (fs/8000 in Q16.16)
 * this will overflow at approx 4 MHz
 */
unsigned get_usb_high_speed_rate(unsigned int rate)
{
	return ((rate << 10) + 62) / 125;
}

/*
 * For streaming based on information derived from sync endpoints,
 * prepare_outbound_urb_sizes() will call next_packet_size() to
 * determine the number of samples to be sent in the next packet.
 *
 * For implicit feedback, next_packet_size() is unused.
 */
int uac_usb_endpoint_next_packet_size(EP_FMT *ep)
{
	unsigned long flags;
	int ret;

	if (ep->fill_max)
		return ep->maxframesize;

//	pthread_mutex_lock(&ep->lock);
	ep->phase = (ep->phase & 0xffff)
		+ (ep->freqm << ep->datainterval);
	ret = MIN(ep->phase >> 16, ep->maxframesize);
//	pthread_mutex_unlock(&ep->lock);

	return ret;
}

int uac_set_data_ep_params(EP_FMT *ep_fmt, EP_FMT *sync_ep, int rate ,int format, int channels,unsigned int period_bytes,unsigned int frames_per_period,unsigned int periods_per_buffer)
{
	unsigned int maxsize, minsize, packs_per_ms, max_packs_per_urb;
	unsigned int max_packs_per_period, urbs_per_period, urb_packs;
	unsigned int max_urbs, i;
	int frame_bits ;
	unsigned int max_queue;
	int speed = 2/*libusb_get_device_speed(dev)*/;

	ep_fmt->maxpacksize = 192;//playbackMaxPacketSize
	frame_bits = format * channels;
	ep_fmt->maxframesize = ep_fmt->maxpacksize * 8 / frame_bits;

	ep_fmt->freqshift = 0;
	ep_fmt->datainterval = 0;
	LOGD("ep_fmt->datainterval=%d,frame_bits=%d,maxframesize=%d\n",ep_fmt->datainterval,frame_bits,ep_fmt->maxframesize);
	
	LOGD("speed = %s\n",speed == 3?"high":"full");
	if (speed == 2)
		ep_fmt->freqn = get_usb_full_speed_rate(rate);
	else
		ep_fmt->freqn = get_usb_high_speed_rate(rate);
	/* calculate the frequency in 16.16 format */
	ep_fmt->freqm = ep_fmt->freqn;
	//ep_fmt->freqshift = INT_MIN;
	ep_fmt->freqshift = 0;
	LOGD("ep_fmt->freqn : %d[0x%08x]\n",ep_fmt->freqn,ep_fmt->freqn);
	LOGD("ep_fmt->freqm : %d[0x%08x]\n",ep_fmt->freqm,ep_fmt->freqm);
	LOGD("ep_fmt->freqshift : %d[0x%08x]\n",ep_fmt->freqshift,ep_fmt->freqshift);

	ep_fmt->stride = frame_bits >> 3; 
	ep_fmt->silence_value = 0;
	LOGD("ep_fmt->stride : %d\n",ep_fmt->stride);
	LOGD("ep_fmt->silence_value : %d\n",ep_fmt->silence_value);
	
	/* assume max. frequency is 25% higher than nominal */
	ep_fmt->freqmax = ep_fmt->freqn + (ep_fmt->freqn >> 2);
	LOGD("ep_fmt->freqmax : %d,[0x%08x]\n",ep_fmt->freqmax,ep_fmt->freqmax);

	/* Round up freqmax to nearest integer in order to calculate maximum
	 * packet size, which must represent a whole number of frames.
	 * This is accomplished by adding 0x0.ffff before converting the
	 * Q16.16 format into integer.
	 * In order to accurately calculate the maximum packet size when
	 * the data interval is more than 1 (i.e. ep->datainterval > 0),
	 * multiply by the data interval prior to rounding. For instance,
	 * a freqmax of 41 kHz will result in a max packet size of 6 (5.125)
	 * frames with a data interval of 1, but 11 (10.25) frames with a
	 * data interval of 2.
	 * (ep->freqmax << ep->datainterval overflows at 8.192 MHz for the
	 * maximum datainterval value of 3, at USB full speed, higher for
	 * USB high speed, noting that ep->freqmax is in units of
	 * frames per packet in Q16.16 format.)
	 */
	maxsize = (((ep_fmt->freqmax << ep_fmt->datainterval) + 0xffff) >> 16) *
			 (frame_bits >> 3);
	/* but wMaxPacketSize might reduce this */
	if (ep_fmt->maxpacksize && ep_fmt->maxpacksize < maxsize) {
		/* whatever fits into a max. size packet */
		unsigned int data_maxsize = maxsize = ep_fmt->maxpacksize;

		ep_fmt->freqmax = (data_maxsize / (frame_bits >> 3))
				<< (16 - ep_fmt->datainterval);
	}
	ep_fmt->curpacksize = maxsize;
	if (speed != 2) {
		packs_per_ms = 8 >> ep_fmt->datainterval;
		max_packs_per_urb = MAX_PACKS_HS;
		max_queue = MAX_QUEUE_HS;
	} else {
		packs_per_ms = 1;
		max_packs_per_urb = MAX_PACKS;
		max_queue = MAX_QUEUE;
	}
	LOGD("maxsize : [%d],curpacksize=%d\n",maxsize,ep_fmt->curpacksize);
	LOGD("sync_ep : %p\n",sync_ep);
	LOGD("snd_usb_endpoint_implicit_feedback_sink : %d\n",0);
	if (sync_ep /*&& !snd_usb_endpoint_implicit_feedback_sink(ep)*/)
	{
		sync_ep->syncinterval = 3;
		max_packs_per_urb = MIN(max_packs_per_urb,
					1U << sync_ep->syncinterval);
		LOGD("sync_ep->syncinterval : %d\n",sync_ep->syncinterval);
	}
	max_packs_per_urb = MAX(1u, max_packs_per_urb >> ep_fmt->datainterval);
	LOGD("curpacksize=%d, packs_per_ms=%d, max_packs_per_urb=%d,max_queue=%d,ep->maxpacksize=%d,maxsize=%d\n",
		ep_fmt->curpacksize, packs_per_ms, max_packs_per_urb,max_queue,ep_fmt->maxpacksize,maxsize);

	//just support playback
	/* determine how small a packet can be */
	minsize = (ep_fmt->freqn >> (16 - ep_fmt->datainterval)) *
			(frame_bits >> 3);
	LOGD("minsize : %d\n",minsize);
	/* with sync from device, assume it can be 12% lower */
	if (sync_ep)
		minsize -= minsize >> 3;
	LOGD("minsize : %d\n",minsize);
	minsize = MAX(minsize, 1u);
	LOGD("minsize : %d\n",minsize);

	LOGD("period_bytes,minsize : [%d ,  %d]\n",period_bytes,minsize);
	/* how many packets will contain an entire ALSA period? */
	max_packs_per_period = DIV_ROUND_UP(period_bytes, minsize);
	LOGD("period_bytes,minsize,max_packs_per_period : [%d ,  %d] [%d]\n",period_bytes,minsize,max_packs_per_period);

	/* how many URBs will contain a period? */
	urbs_per_period = DIV_ROUND_UP(max_packs_per_period,
			max_packs_per_urb);
	LOGD("max_packs_per_period,max_packs_per_urb,urbs_per_period : [%d ,  %d] [%d]\n",max_packs_per_period,max_packs_per_urb,urbs_per_period);
	/* how many packets are needed in each URB? */
	urb_packs = DIV_ROUND_UP(max_packs_per_period, urbs_per_period);
	LOGD("max_packs_per_period, urbs_per_period,urb_packs : [%d ,  %d] [%d]\n",max_packs_per_period, urbs_per_period,urb_packs);

	/* limit the number of frames in a single URB */
	ep_fmt->max_urb_frames = DIV_ROUND_UP(frames_per_period,
				urbs_per_period);
	LOGD("frames_per_period, urbs_per_period,ep->max_urb_frames : [%d ,  %d] [%d]\n",frames_per_period, urbs_per_period,ep_fmt->max_urb_frames);

	/* try to use enough URBs to contain an entire ALSA buffer */
	max_urbs = MIN((unsigned) MAX_URBS,
			MAX_QUEUE * packs_per_ms / urb_packs);
	ep_fmt->nurbs = MIN(max_urbs, urbs_per_period * periods_per_buffer);
	if (ep_fmt->nurbs < 2)
		ep_fmt->nurbs++;
	LOGD("max_urbs,ep->nurbs : [%d , %d]\n",max_urbs,ep_fmt->nurbs);
	ep_fmt->urb_packets = urb_packs;
	LOGD("ep_fmt->urb_packets : [%d]\n",ep_fmt->urb_packets);


	return 0;
}
void uac_printf_tt(void)
{
	LOGD("FU_USBIN\t\t%d\n",FU_USBIN);
	LOGD("FU_USBOUT\t\t%d\n",FU_USBOUT);
	LOGD("ID_IT_USB\t\t%d\n",ID_IT_USB);
	LOGD("ID_IT_AUD\t\t%d\n",ID_IT_AUD);
	LOGD("ID_OT_USB\t\t%d\n",ID_OT_USB);
	LOGD("ID_OT_AUD\t\t%d\n",ID_OT_AUD);

	LOGD("\n");
	LOGD("ID_CLKSEL\t\t%d\n",ID_CLKSEL);
	LOGD("ID_CLKSRC_INT\t\t%d\n",ID_CLKSRC_INT);
	LOGD("ID_CLKSRC_SPDIF\t\t%d\n",ID_CLKSRC_SPDIF);
	LOGD("ID_CLKSRC_ADAT\t\t%d\n",ID_CLKSRC_ADAT);
	
	LOGD("\n");
	LOGD("ID_XU_MIXSEL\t\t%d\n",ID_XU_MIXSEL);
	LOGD("ID_XU_OUT\t\t%d\n",ID_XU_OUT);
	LOGD("ID_XU_IN\t\t%d\n",ID_XU_IN);
}

int uac_config_analyse(libusb_device *uac_dev)
{
    int rc = 0;
    struct libusb_device_descriptor dev_desc;
    struct libusb_config_descriptor *conf_desc;
    const struct libusb_endpoint_descriptor *endpoint;
    Interface_Association_Descriptor associate;
    int iface, nb_ifaces, first_iface = -1;
    int m,i = 0, j ,k,r;
    uint8_t endpoint_in = 0, endpoint_out = 0;
    char string[128];
    uint8_t string_index[10]={0};	// indexes of the string descriptors

    if UNLIKELY(!uac_dev) return -1;
	
    libusb_get_device_descriptor(uac_dev, &dev_desc);
    LOGD("Device Descriptor:\n");
    LOGD("  bLength  \t\t%d\n", dev_desc.bLength);
    LOGD("  bDescriptorType  \t%d\n", dev_desc.bDescriptorType);
    LOGD("  bcdUSB  \t\t0x%04x\n", dev_desc.bcdUSB);
    LOGD("  bDeviceClass  \t%d\n", dev_desc.bDeviceClass);
    LOGD("  bDeviceSubClass  \t%d\n", dev_desc.bDeviceSubClass);
    LOGD("  bDeviceProtocol  \t%d\n", dev_desc.bDeviceProtocol);
    LOGD("  bMaxPacketSize0  \t%d\n", dev_desc.bMaxPacketSize0);
    LOGD("  idVendor  \t\t0x%04x\n", dev_desc.idVendor);
    LOGD("  idProduct  \t\t0x%04x\n", dev_desc.idProduct);
    LOGD("  bcdDevice  \t\t0x%04x\n", dev_desc.bcdDevice);
    LOGD("  iManufacturer  \t%d\n", dev_desc.iManufacturer);
    LOGD("  iProduct  \t\t%d\n", dev_desc.iProduct);
    LOGD("  iSerialNumber  \t%d\n", dev_desc.iSerialNumber);
    LOGD("  bNumConfigurations  \t%d\n", dev_desc.bNumConfigurations);

    for(m = 0;m<dev_desc.bNumConfigurations;m++)
    {
        libusb_get_config_descriptor(uac_dev, m, &conf_desc);
        LOGD("  Config Descriptor(%d):\n",m);
        LOGD("    bLength  \t\t  %d\n", conf_desc->bLength);
        LOGD("    bDescriptorType  \t  %d\n", conf_desc->bDescriptorType);
        LOGD("    wTotalLength  \t  %d\n", conf_desc->wTotalLength);
        LOGD("    bNumInterfaces  \t  %d\n", conf_desc->bNumInterfaces);
        LOGD("    bConfigurationValue    %d\n", conf_desc->bConfigurationValue);
        LOGD("    iConfiguration  \t  %d\n", conf_desc->iConfiguration);
        LOGD("    bmAttributes  \t  0x%02x\n", conf_desc->bmAttributes);
        LOGD("    MaxPower  \t\t  %dmA\n", conf_desc->MaxPower);
        LOGD("    extra  \t\t  %p\n", conf_desc->extra);
        LOGD("    extra_length \t  %d\n", conf_desc->extra_length);

        //libusb_get_descriptor(uac_handler->uac_dev, LIBUSB_DT_INTERFACE_ASSOCIATION, 0, unsigned char * data, int length);
        memset(&associate,0,LIBUSB_DT_INT_ASSOCIATION_SIZE);
        uac_get_interface_associate_desc(conf_desc, &associate);
        LOGD("    Interface Association Descriptor:\n");
        LOGD("      bLength  \t\t    %d\n",associate.bLength);
        LOGD("      bDescriptorType  \t    %d\n",associate.bDescriptorType);
        LOGD("      bFirstInterface  \t    %d\n",associate.bFirstInterface);
        LOGD("      bInterfaceCount  \t    %d\n",associate.bInterfaceCount);
        LOGD("      bFunctionClass  \t    %d  %s\n",associate.bFunctionClass, associate.bFunctionClass==0x01?"AUDIO":"");
        LOGD("      bFunctionSubClass      %d\n",associate.bFunctionSubClass);
        LOGD("      bFunctionProtocol      %d\n",associate.bFunctionProtocol);
        LOGD("      iFunction  \t    %d\n",associate.iFunction);

        nb_ifaces = conf_desc->bNumInterfaces;
        if (nb_ifaces > 0)
            first_iface = conf_desc->interface[0].altsetting[0].bInterfaceNumber;

        for (i=0; i<nb_ifaces; i++)
        {
               LOGD("   interface[%d]:num_altsetting=%d\n", i, conf_desc->interface[i].num_altsetting);
            for (j=0; j<conf_desc->interface[i].num_altsetting; j++)
            {
                LOGD("    alternate settings index[%d]:\n",j);
                LOGD("     Interface Descriptor[%d]:\n",conf_desc->interface[i].altsetting[j].bInterfaceNumber);
                LOGD("      bLength  \t\t    %d\n",conf_desc->interface[i].altsetting[j].bLength);
                LOGD("      bDescriptorType  \t    %d\n",conf_desc->interface[i].altsetting[j].bDescriptorType);
                LOGD("      bInterfaceNumber      %d\n",conf_desc->interface[i].altsetting[j].bInterfaceNumber);
                LOGD("      bAlternateSetting      %d\n",conf_desc->interface[i].altsetting[j].bAlternateSetting);
                LOGD("      bNumEndpoints  \t    %d\n",conf_desc->interface[i].altsetting[j].bNumEndpoints);
                LOGD("      bInterfaceClass  \t    %d  %s\n",conf_desc->interface[i].altsetting[j].bInterfaceClass, conf_desc->interface[i].altsetting[j].bInterfaceClass==0x01?"AUDIO":"");
                if(conf_desc->interface[i].altsetting[j].bInterfaceClass==0x01)
                    LOGD("      bInterfaceSubClass      %d  %s\n",conf_desc->interface[i].altsetting[j].bInterfaceSubClass,string_Audio_Interface_Subclass_Codes[conf_desc->interface[i].altsetting[j].bInterfaceSubClass]);
                else
                    LOGD("      bInterfaceSubClass      %d\n",conf_desc->interface[i].altsetting[j].bInterfaceSubClass);
                LOGD("      bInterfaceProtocol      %d\n",conf_desc->interface[i].altsetting[j].bInterfaceProtocol);
                LOGD("      iInterface  \t    %d\n",conf_desc->interface[i].altsetting[j].iInterface);
                LOGD("      extra  \t\t    %p\n",conf_desc->interface[i].altsetting[j].extra);
                LOGD("      extra_length  \t    %d\n",conf_desc->interface[i].altsetting[j].extra_length);
                //uac_parse_AC_interface_desc(uac_handler->uac_devh,conf_desc->interface[i].altsetting[j]);
                if(conf_desc->interface[i].altsetting[j].bInterfaceSubClass == UAC_INT_SUBCLASS_AUDIOCONTROL)
                    uac_parse_AC_interface_desc(&conf_desc->interface[i].altsetting[j]);
                else if(conf_desc->interface[i].altsetting[j].bInterfaceSubClass == UAC_INT_SUBCLASS_AUDIOSTREAMING)
                    uac_parse_AS_interface_desc(&conf_desc->interface[i].altsetting[j]);


                if ( conf_desc->interface[i].altsetting[j].bInterfaceClass == LIBUSB_CLASS_AUDIO )
                {
                    // Mass storage devices that can use basic SCSI commands
                    //test_mode = USE_SCSI;
                    //LOGD("			This is Usb Audio device\n");
                }
                for (k=0; k<conf_desc->interface[i].altsetting[j].bNumEndpoints; k++)
                {
                    endpoint = &conf_desc->interface[i].altsetting[j].endpoint[k];
                    LOGD("      Endpoint Descriptor:\n");
                    LOGD("        bLength  \t\t    %d\n",endpoint->bLength);
                    LOGD("        bDescriptorType  \t    %d\n",endpoint->bDescriptorType);
                    LOGD("        bEndpointAddress  \t    0x%02x  EP  %d  %s\n",endpoint->bEndpointAddress, endpoint->bEndpointAddress&0x0F , (endpoint->bEndpointAddress&LIBUSB_ENDPOINT_IN)?"IN":"OUT"  );
                    LOGD("        bmAttributes  \t\t    %d\n",endpoint->bmAttributes);
                    LOGD("          Transfer Type  \t    %s\n",string_usb_Standard_Endpoint_Descriptor_Transfer_Type[endpoint->bmAttributes&0x03]);
                    LOGD("          Synch Type  \t\t    %s\n",string_usb_Standard_Endpoint_Descriptor_Synchronization_Type[(endpoint->bmAttributes>>2)&0x03]);
                    LOGD("          Usage Type  \t\t    %s\n",string_usb_Standard_Endpoint_Descriptor_Usage_Type[(endpoint->bmAttributes>>4)&0x03]);
                    LOGD("        wMaxPacketSize  \t    %d\n",endpoint->wMaxPacketSize);
                    LOGD("        bInterval  \t\t    %d\n",endpoint->bInterval);
                    LOGD("        bRefresh  \t\t    %d\n",endpoint->bRefresh);
                    LOGD("        bSynchAddress  \t\t    %d\n",endpoint->bSynchAddress);
                    LOGD("        extra  \t\t\t    %p\n",endpoint->extra);
                    LOGD("        extra_length  \t\t    %d\n",endpoint->extra_length);

                    if(endpoint->extra)
                    {
                        uac_parse_AS_Endpoint_desc(endpoint);
                    }

                    //LOGD("       		endpoint[%d].address: %02X\n", k, endpoint->bEndpointAddress);
                    // Use the first interrupt or bulk IN/OUT endpoints as default for testing
#if 0
                    if ((endpoint->bmAttributes & LIBUSB_TRANSFER_TYPE_MASK) & (LIBUSB_TRANSFER_TYPE_ISOCHRONOUS)) {
                        if (endpoint->bEndpointAddress & LIBUSB_ENDPOINT_IN) {
                            if (!endpoint_in){
                                endpoint_in = endpoint->bEndpointAddress;
                                uac_handler->capture_data_ep_fmt->datainterval = endpoint->bInterval - 1;
                                uac_handler->capture_data_ep_fmt->maxpacksize = endpoint->wMaxPacketSize;
                                uac_handler->capture_data_ep_fmt->fill_max = !!(endpoint->bmAttributes & 0x80);
                                if((endpoint->bmAttributes>>4)&0x03 == 0x01)
                                {
                                    uac_handler->capture_sync_ep_fmt->syncinterval = endpoint->bInterval - 1;
                                }
                            }
                        } else {
                            if (!endpoint_out){
                                endpoint_out = endpoint->bEndpointAddress;
                                uac_handler->playback_data_ep_fmt->datainterval = endpoint->bInterval - 1;
                                uac_handler->playback_data_ep_fmt->maxpacksize = endpoint->wMaxPacketSize;
                                uac_handler->playback_data_ep_fmt->fill_max = !!(endpoint->bmAttributes & 0x80);
                                if((endpoint->bmAttributes>>4)&0x03 == 0x01)
                                {
                                    uac_handler->playback_sync_ep_fmt->syncinterval = endpoint->bInterval - 1;
                                }
                            }
                        }
                    }
#endif
                }
            }
        }
        libusb_free_config_descriptor(conf_desc);
    }
#if 0
    LOGD("\nReading string descriptors:\n");
    for (i=0; i<20; i++) {
        if (libusb_get_string_descriptor_ascii(uac_devh, i /*string_index[i]*/, (unsigned char*)string, 128) >= 0) {
            LOGD("   String (0x%02X): \"%s\"\n", i /*string_index[i]*/, string);
        }
    }
#endif	
    return rc;
}



