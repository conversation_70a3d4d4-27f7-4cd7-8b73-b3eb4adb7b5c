//
// Created by <PERSON><PERSON> on 2021/11/10.
//

#ifndef UVCCAMERA_USBAUDIO_H
#define UVCCAMERA_USBAUDIO_H

#include <jni.h>
#include <pthread.h>
#include "../libusb/libusb/libusb.h"
#include "uac.h"
#include "uac_stream.h"

#define DEFAULT_SIMPLING_RATE 48000

static unsigned long num_bytes = 0, num_xfer = 0;
static struct timeval tv_start;

/** UAC request code (A.8) */
enum uac_req_code {
    UAC_RC_UNDEFINED = 0x00,
    UAC_SET_CUR = 0x01,
    UAC_GET_CUR = 0x81
};

class USBAudio {
private:
    const struct libusb_interface *i_face;
    const struct libusb_interface_descriptor *if_desc;
//    struct libusb_config_descriptor *uac_config;
//    struct libusb_context *uac_ctx;
//    struct libusb_device_handle *uac_devh;

    struct audio_streaming_descriptor *audio_stream_desc;

//    struct libusb_device *uac_dev;
    char *mUsbFs;
    int sample_rate;
    uac_stream_handler_t *uac_handler;

    void set_audio_stream_desc(const libusb_interface_descriptor *desc);
    int claim_if(libusb_device_handle *devh, int interface_number,int  bAlternateSetting);
    int release_if(libusb_device_handle *devh, int interface_number,int  bAlternateSetting);
    int scan_audio_interface(libusb_device *usbDev);
    int start_capture_stream_transfer();
    int stop_capture_stream_transfer();
    int start_playback_stream_transfer();
    int stop_playback_stream_transfer();
    int set_capture_sample_rate_v1(int rate);
    int set_playback_sample_rate_v1(int rate);


public:
    USBAudio();

    void closeAudio();

    int initAudio(int vid, int pid, int busnum, int devaddr, int fd, const char *usbfs);

    void setCallback(JavaVM *vm, JNIEnv *env, jobject callback_obj);

    int startCapture();

    int stopCapture();

    int startPlayback();

    int stopPlayback();
    int getSampleRate();

    int getChannelCount();

    int getBitResolution();

    bool isRunning();

};


#endif //UVCCAMERA_USBAUDIO_H
