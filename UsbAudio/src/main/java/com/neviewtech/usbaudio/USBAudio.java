package com.neviewtech.usbaudio;

import android.content.Context;
import android.hardware.usb.UsbConstants;
import android.hardware.usb.UsbDevice;
import android.hardware.usb.UsbDeviceConnection;
import android.hardware.usb.UsbEndpoint;
import android.hardware.usb.UsbInterface;
import android.hardware.usb.UsbManager;
import android.text.TextUtils;
import android.util.Log;

import com.neviewtech.usbaudio.usb.DeviceFilter;
import com.neviewtech.usbaudio.usb.USBMonitor;

import java.util.HashMap;
import java.util.Locale;

public class USBAudio {
    private String TAG = "USBAudio";

    static {
        System.loadLibrary("NeviewUSBAudio");
    }

    private final UsbManager mUsbManager;
    //private USBMonitor mUSBMonitor;
    private UsbDevice mAudioDevice;
    //private USBMonitor.UsbControlBlock mCtrlBlock;
    private UsbInfo mUsbInfo;
    private static final String DEFAULT_USBFS = "/dev/bus/usb";

    protected long mNativePtr;

    private native long nativeCreate();

    private native void nativeClose(long id_camera);

    private native int nativeInit(long id_camera, int vid, int pid, int busnum, int devaddr, int fd, String usbfs);

    private native int nativeGetSampleRate(long id_camera);

    private native int nativeGetChannelCount(long id_camera);

    private native int nativeGetBitResolution(long id_camera);

    private native boolean nativeIsRunning(long id_camera);

    private native int nativeStartCapture(long id_camera);

    private native int nativeStopCapture(long id_camera);

    private native int nativeStartPlayback(long id_camera);

    private native int nativeStopPlayback(long id_camera);

    private native int nativeWriteAudioData(long id_camera, byte[] date,int size);

    private final OnUsbCallback mCallback;

    public USBAudio(Context context, OnUsbCallback callback) {

        mUsbManager = (UsbManager) context.getSystemService(Context.USB_SERVICE);
        mCallback = callback;

        /*mUSBMonitor = new USBMonitor(context, mOnDeviceConnectListener);
        mUSBMonitor.setDeviceFilter(DeviceFilter.getDeviceFilters(context, R.xml.device_filter));
        mUSBMonitor.register();*/

        Log.i(TAG, "USBAudio: nativeCreate "+"mNativePtr="+mNativePtr);
    }


    public int initAudio() {
        if(mNativePtr != 0) nativeClose(mNativePtr);
        mNativePtr = nativeCreate();
        openDevice();
        int result;
        try {
            result = nativeInit(mNativePtr,
                    mUsbInfo.getVenderId(), mUsbInfo.getProductId(),
                    mUsbInfo.getBusNum(),
                    mUsbInfo.getDevNum(),
                    mUsbInfo.getFileDescriptor(),
                    mUsbInfo.getUSBFSName());
        } catch (final Exception e) {
            Log.w(TAG, e);
            result = -1;
        }

        Log.i(TAG, "initAudio: " + result+" mNativePtr="+mNativePtr);
        if (result < 0) {
            return result;
        }

        setAudioConfig();
        return result;
    }

    public void setAudioConfig() {
        this.channel = nativeGetChannelCount(mNativePtr);
        SAMPLE_RATE_HZ = nativeGetSampleRate(mNativePtr);
        int bit = nativeGetBitResolution(mNativePtr);
        Log.i(TAG, "setAudioConfig: Channel " + channel);
        Log.i(TAG, "setAudioConfig: SampleRate " + SAMPLE_RATE_HZ);
        Log.i(TAG, "setAudioConfig: BitResolution " + bit);

        initPlay();
    }

    public int startCapture() {
        Log.i(TAG, "startCapture"+"mNativePtr="+mNativePtr);
        return nativeStartCapture(mNativePtr);
    }

    public int stopCapture() {
        Log.i(TAG, "stopCapture"+" mNativePtr="+mNativePtr);
        return nativeStopCapture(mNativePtr);
    }

    public int startPlayback() {
        Log.i(TAG, "startPlayback"+"mNativePtr="+mNativePtr);
        return nativeStartPlayback(mNativePtr);
    }

    public int stopPlayback() {
        Log.i(TAG, "stopCapture"+" mNativePtr="+mNativePtr);
        return nativeStopPlayback(mNativePtr);
    }

    public void closeAudio() {
        nativeClose(mNativePtr);

        /*if (mUSBMonitor != null) {
            mUSBMonitor.destroy();
            mUSBMonitor = null;
        }*/
        mUsbInfo = null;
        mAudioDevice = null;
        mNativePtr = 0;
    }

    private int SAMPLE_RATE_HZ = 48000;
    private int channel = 1;

    private void initPlay() {
    }

    public void writeCapturePcmData(byte[] data) {
        mCallback.writeCapturePcmData(data);
    }

    public byte[]  readPlaybackPcmData(int length ) {
        return mCallback.readPlaybackPcmData(length);
    }

    public void writeAudioData(byte[] data) {
        Log.i(TAG, "writeAudioData: " + data.length);
        nativeWriteAudioData(mNativePtr, data, data.length);
    }
    /*private USBMonitor.OnDeviceConnectListener mOnDeviceConnectListener = new USBMonitor.OnDeviceConnectListener() {
        @Override
        public void onAttach(UsbDevice device) {
            Log.i(TAG, "onAttach: " + device);
            mUSBMonitor.requestPermission(device);
        }

        @Override
        public void onDettach(UsbDevice device) {
            Log.i(TAG, "onDettach: " + device);
        }

        @Override
        public void onConnect(UsbDevice device, USBMonitor.UsbControlBlock ctrlBlock, boolean createNew) {
            Log.i(TAG, "onConnect: " + device);
            int interfaceCount = device.getInterfaceCount();
            for (int i = 0; i < interfaceCount; i++) {
                if (device.getInterface(i).getInterfaceClass() == UsbConstants.USB_CLASS_AUDIO) {
                    mCtrlBlock = ctrlBlock;
                    break;
                }
            }

            mAudioDevice = device;
        }

        @Override
        public void onDisconnect(UsbDevice device, USBMonitor.UsbControlBlock ctrlBlock) {

        }

        @Override
        public void onCancel(UsbDevice device) {

        }
    };*/

    private UsbEndpoint mUsbEndpointIn;
    private UsbEndpoint mUsbEndpointOut;
    private UsbInterface mUsbInterfaceInt;
    private UsbInterface mUsbInterfaceOut;

    private void openDevice() {
        boolean founded = false;
        final HashMap<String, UsbDevice> deviceList = mUsbManager.getDeviceList();
        for (UsbDevice device : deviceList.values()) {
            int interfaceCount = device.getInterfaceCount();
            for (int i = 0; i < interfaceCount; i++) {
                if (device.getInterface(i).getInterfaceClass() == UsbConstants.USB_CLASS_AUDIO) {
                    mAudioDevice = device;
                    founded = true;
                    Log.d(TAG, "audio device : " + mAudioDevice);
                    break;
                }
            }
            if(founded) break;
        }

        if (mAudioDevice == null) {
            Log.d(TAG, "mAudioDevice 空");
            return;
        } else {
            mUsbInfo = new UsbInfo(mAudioDevice);
        }

        int interfaceCount = mAudioDevice.getInterfaceCount(); // 接口数量
        Log.d(TAG, "interfaceCount:" + interfaceCount);
        for (int i = 0; i < interfaceCount; i++) {
            // 拿到USB设备
            UsbInterface usbInterface = mAudioDevice.getInterface(i);
            if (UsbConstants.USB_CLASS_AUDIO != usbInterface.getInterfaceClass() && UsbConstants.USB_CLASS_HID != usbInterface.getInterfaceClass()) {
                Log.d(TAG, "不是音频或隐藏界面，跳过它");
                continue;
            }
            int endpointCount = usbInterface.getEndpointCount();
            for (int j = 0; j < endpointCount; j++) {
                UsbEndpoint ep = usbInterface.getEndpoint(j);
                Log.d(TAG, ep.toString() + "/type:" + ep.getType());
                if (ep.getType() == UsbConstants.USB_ENDPOINT_XFER_ISOC) {
                    if (ep.getDirection() == UsbConstants.USB_DIR_IN) {
                        Log.d(TAG, "找到输入端点");
                        mUsbEndpointIn = ep;
                        mUsbInterfaceInt = usbInterface;
                    } else {
                        Log.d(TAG, "找到输出端点");
                        mUsbEndpointOut = ep;
                        mUsbInterfaceOut = usbInterface;
                    }
                }
                if (ep.getType() == UsbConstants.USB_ENDPOINT_XFER_INT) {
                    mUsbEndpointOut = ep;
                    mUsbInterfaceOut = usbInterface;
                }
            }
        }
    }

    private String getUSBFSName(final USBMonitor.UsbControlBlock ctrlBlock) {
        String result = null;
        final String name = ctrlBlock.getDeviceName();
        final String[] v = !TextUtils.isEmpty(name) ? name.split("/") : null;
        if ((v != null) && (v.length > 2)) {
            final StringBuilder sb = new StringBuilder(v[0]);
            for (int i = 1; i < v.length - 2; i++)
                sb.append("/").append(v[i]);
            result = sb.toString();
        }
        if (TextUtils.isEmpty(result)) {
            Log.w(TAG, "failed to get USBFS path, try to use default path:" + name);
            result = DEFAULT_USBFS;
        }
        return result;
    }

    private class UsbInfo {
        private final UsbDevice mUsbDevice;
        private final UsbDeviceConnection mConnection;
        private final int mBusNum;
        private final int mDevNum;

        private UsbInfo(final UsbDevice device) {
            mUsbDevice = device;
            mConnection = mUsbManager.openDevice(device);
            final String name = device.getDeviceName();
            final String[] v = !TextUtils.isEmpty(name) ? name.split("/") : null;
            int busnum = 0;
            int devnum = 0;
            if (v != null) {
                busnum = Integer.parseInt(v[v.length - 2]);
                devnum = Integer.parseInt(v[v.length - 1]);
            }
            mBusNum = busnum;
            mDevNum = devnum;

            if (mConnection != null) {
                final int desc = mConnection.getFileDescriptor();
                final byte[] rawDesc = mConnection.getRawDescriptors();
                Log.i(TAG, String.format(Locale.US, "name=%s,desc=%d,busnum=%d,devnum=%d,rawDesc=", name, desc, busnum, devnum) + rawDesc);
            } else {
                Log.e(TAG, "could not connect to device " + name);
            }
        }

        public String getDeviceName() {
            final UsbDevice device = mUsbDevice;
            return device != null ? device.getDeviceName() : "";
        }

        public int getVenderId() {
            final UsbDevice device = mUsbDevice;
            return device != null ? device.getVendorId() : 0;
        }

        public int getProductId() {
            final UsbDevice device = mUsbDevice;
            return device != null ? device.getProductId() : 0;
        }

        public int getBusNum() {
            return mBusNum;
        }

        public int getDevNum() {
            return mDevNum;
        }

        public synchronized int getFileDescriptor() {
            if (mConnection == null) {
                Log.e(TAG, "usb connection is closed ");
                return -1;
            }
            return mConnection.getFileDescriptor();
        }

        private String getUSBFSName() {
            String result = null;
            final String name = getDeviceName();
            final String[] v = !TextUtils.isEmpty(name) ? name.split("/") : null;
            if ((v != null) && (v.length > 2)) {
                final StringBuilder sb = new StringBuilder(v[0]);
                for (int i = 1; i < v.length - 2; i++)
                    sb.append("/").append(v[i]);
                result = sb.toString();
            }
            if (TextUtils.isEmpty(result)) {
                Log.w(TAG, "failed to get USBFS path, try to use default path:" + name);
                result = DEFAULT_USBFS;
            }
            return result;
        }
    }

    public interface OnUsbCallback {
        void writeCapturePcmData(byte[] data);

        byte[]  readPlaybackPcmData(int length);
    }
}
