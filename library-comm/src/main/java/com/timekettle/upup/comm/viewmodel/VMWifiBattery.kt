package com.timekettle.upup.comm.viewmodel

import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.viewModelScope
import co.timekettle.btkit.BleUtil
import co.timekettle.btkit.bean.RawBlePeripheral
import co.timekettle.btkit.bean.WT2BlePeripheral
import com.timekettle.upup.base.mvvm.vm.BaseViewModel
import com.timekettle.upup.base.utils.showDebugToast
import com.timekettle.upup.comm.ktx.findLeft
import com.timekettle.upup.comm.ktx.findRight
import com.timekettle.upup.comm.model.X1Device
import com.timekettle.upup.comm.model.X1Role
import com.timekettle.upup.comm.model.X1Status
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.Job
import kotlinx.coroutines.delay
import kotlinx.coroutines.launch


/**
 * 全局的ViewModel，在App的生命周期内都可见
 */
class VMWifiBattery : BaseViewModel() {
    val data = MutableLiveData<String>()

}