package com.timekettle.upup.comm.viewmodel

import android.annotation.SuppressLint
import androidx.lifecycle.viewModelScope
import com.blankj.utilcode.util.NetworkUtils
import com.timekettle.upup.comm.bean.CertState
import com.timekettle.upup.comm.utils.PingUtil
import com.timekettle.upup.base.mvvm.vm.BaseViewModel
import com.timekettle.upup.base.utils.logD
import com.timekettle.upup.base.utils.logE
import com.timekettle.upup.comm.constant.NetUrl
import com.timekettle.upup.comm.model.CertificateBean
import com.timekettle.upup.comm.net.ApiResult
import com.timekettle.upup.comm.net.helper.BusinessManager
import com.timekettle.upup.comm.tools.AKSManager
import com.timekettle.upup.comm.tools.PKCS12Manager
import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.filter
import kotlinx.coroutines.flow.first
import kotlinx.coroutines.launch
import kotlinx.coroutines.withTimeoutOrNull
import javax.inject.Inject

@HiltViewModel
class CertViewModel @Inject constructor() : BaseViewModel() {
    private val TAG = "CertViewModel"
    val certState = MutableStateFlow<CertState>(CertState.None)
    // ca接口配置项目，用于决定使用工厂还是公司的地址
    private val caAuthingFactory = true
    private val hasCert
        get() = AKSManager.isCertificateExists() && PKCS12Manager.isCertificateExists()
    var pingFactorySuccess = false

    @SuppressLint("MissingPermission")
    fun startCert() {
        if (certState.value != CertState.None && certState.value !is CertState.Fail) {
            logE("当前状态：${certState.value}，不允许再次鉴权", TAG)
            return
        }

        logD("开始鉴权", TAG)
        viewModelScope.launch(Dispatchers.IO) {
            certState.emit(CertState.Loading)
            if (!NetworkUtils.isConnected()) {
                certState.emit(CertState.Fail(-1, "网络不可用"))
                return@launch
            }
            if (!hasCert) {
                PKCS12Manager.delete()
                AKSManager.deleteKey()
            }
            // ping 本地认证服务器，如果通过则代表处于工厂，使用本地接口，否则使用公网接口
            val pingUrl = if (caAuthingFactory) NetUrl.LOCAL_FACTORY_AUTH_DOMAIN else NetUrl.LOCAL_COMPANY_AUTH_DOMAIN
            val pingResult = PingUtil.pingIpAddress(pingUrl)
            logD("ping结果：${pingResult.printInfo()}", TAG)
            pingFactorySuccess = pingResult.delay != null
            val authAddress = if (pingFactorySuccess) {
                if (caAuthingFactory) NetUrl.LOCAL_FACTORY_AUTH_ADDRESS else NetUrl.LOCAL_COMPANY_AUTH_ADDRESS
            } else {
                NetUrl.EXTERNAL_AUTH_ADDRESS
            }
            logD("证书地址：$authAddress", TAG)

            // 第一步：获取 AKS 证书
            val aksCsr = AKSManager.createCsr()
            val aksResult = BusinessManager.getCertificate(authAddress, aksCsr)
                .first { it !is ApiResult.Loading }  // 忽略 Loading 状态，直接等待最终结果
            when (aksResult) {
                is ApiResult.Success -> {
                    logD("获取AKS证书成功", TAG)
                    (aksResult.data as? CertificateBean)?.let { cert ->
                        AKSManager.savePemCertificateToKeyStore(cert.certificate)
                    }
                }
                is ApiResult.Error -> {
                    logE("获取AKS证书网络请求失败：${aksResult.message}", TAG)
                    certState.emit(CertState.Fail(-2, "网络异常，获取AKS证书失败：${aksResult.message}"))
                    return@launch
                }
                is ApiResult.AuthError -> {
                    logE("获取AKS证书请求成功，状态码校验失败：${aksResult.data} ${aksResult.message}", TAG)
                    certState.emit(CertState.Fail(aksResult.data?.toInt() ?: -3, aksResult.message ?: ""))
                    return@launch
                }
                else -> Unit // 不可能触发（因为已过滤 Loading）
            }

            // 第二步：只有 AKS 成功后才获取 PKCS12 证书
            val keyPair = PKCS12Manager.createKeyPair()
            val pkcs12Csr = PKCS12Manager.createCsr(keyPair)
            val pkcs12Result = BusinessManager.getCertificate(authAddress, pkcs12Csr)
                .first { it !is ApiResult.Loading }
            when (pkcs12Result) {
                is ApiResult.Success -> {
                    logD("获取PKCS12证书成功", TAG)
                    (pkcs12Result.data as? CertificateBean)?.let { cert ->
                        PKCS12Manager.saveCertificateChain(keyPair, cert.certificate)
                    }
                    BusinessManager.submitTokenWorker(0L)
                    val validToken = withTimeoutOrNull(10_000) {
                        BusinessManager.tokenFlow.filter { it.isNotEmpty() }.first()
                    }
                    if (validToken == null) {
                        certState.emit(CertState.Fail(-6, "业务token获取失败"))
                    } else {
                        certState.emit(CertState.Success) // 全部成功
                    }
                }
                is ApiResult.Error -> {
                    logE("获取PKCS12证书网络请求失败：${pkcs12Result.message}", TAG)
                    certState.emit(CertState.Fail(-4, "网络异常，获取AKS证书失败：${pkcs12Result.message}"))
                    return@launch
                }
                is ApiResult.AuthError -> {
                    logE("获取PKCS12证书请求成功，状态码校验失败：${pkcs12Result.data} ${pkcs12Result.message}", TAG)
                    certState.emit(CertState.Fail(pkcs12Result.data?.toInt() ?: -5, pkcs12Result.message ?: ""))
                    return@launch
                }
                else -> Unit
            }
        }
    }
}