package com.timekettle.upup.comm.widget

import android.graphics.Canvas
import android.graphics.Color
import android.graphics.Paint
import androidx.recyclerview.widget.RecyclerView
import com.blankj.utilcode.util.SizeUtils

object VerticalScrollBarDecoration: RecyclerView.ItemDecoration() {

    override fun onDrawOver(c: Canvas, parent: RecyclerView, state: RecyclerView.State) {
        super.onDrawOver(c, parent, state)

        val barWidth = SizeUtils.dp2px(2f)
        val scrollHeight = SizeUtils.dp2px(162f)
        val indicatorHeight = SizeUtils.dp2px(40f)
        val paddingRight = SizeUtils.dp2px(3f)
        val barX = (parent.width - paddingRight - barWidth).toFloat()
        val barY = (parent.height / 2 - scrollHeight / 2).toFloat()

        val paint = Paint()
        paint.isAntiAlias = true
        paint.color = Color.parseColor("#26FFFFFF")
        paint.strokeCap = Paint.Cap.ROUND
        paint.strokeWidth = barWidth.toFloat()
        c.drawLine(barX, barY, barX, barY + scrollHeight.toFloat(), paint)

        val extent = parent.computeVerticalScrollExtent()
        val range = parent.computeVerticalScrollRange()
        val offset = parent.computeVerticalScrollOffset()
        val maxEndY = (range - extent).toFloat()
        //可滑动
        if (maxEndY > 0) {
            val proportion = offset / maxEndY

            val scrollableDistance = scrollHeight - indicatorHeight

            val offsetY = scrollableDistance * proportion
            paint.color = Color.parseColor("#73FFFFFF")
            c.drawLine(barX, barY + offsetY, barX, barY + indicatorHeight.toFloat() + offsetY, paint)
        } else {
            paint.color = Color.parseColor("#73FFFFFF")
            c.drawLine(barX, barY, barX, barY + scrollHeight.toFloat(), paint)
        }
    }
}
