package com.timekettle.upup.comm.dialog

import android.annotation.SuppressLint
import android.app.Dialog
import android.content.Context
import android.content.DialogInterface
import android.os.CountDownTimer
import com.timekettle.upup.comm.R
import com.timekettle.upup.comm.databinding.CommDialogConfirmCancelBinding
import com.timekettle.upup.comm.databinding.CommDialogCountDownBinding

/**
 * 带有倒计时功能的弹窗
 * 并且在倒计时结束之后，自动执行动作
 */
class CountdownDialog(context: Context, private val countdownTime: Long = 10 * 1000L) :
    Dialog(context, R.style.comm_transparent_dialog) {

    private lateinit var countDownTimer: CountDownTimer
    private var timeLeft: Long = 0
    private var mBinding = CommDialogCountDownBinding.inflate(layoutInflater)
    var confirmTodo: (() -> Unit)? = null
    var cancelTodo: (() -> Unit)? = null
    var content: String = ""
        set(value) {
            field = value
            mBinding.vContentTv.text = value
        }
    var cancelText: String = ""
        set(value) {
            mBinding.vCancelTv.text = value
            field = value
        }

    var confirmText: String = ""
        set(value) {
            mBinding.vConfirmTv.text = value
            field = value
            updateFont()
        }

    init {

        setContentView(mBinding.root)
        setCancelable(false)
        setCanceledOnTouchOutside(false)
        mBinding.vConfirmTv.setOnClickListener { countDownTimer.cancel(); dismiss(); confirmTodo?.invoke() }
        mBinding.vCancelTv.setOnClickListener { countDownTimer.cancel(); dismiss(); cancelTodo?.invoke() }

    }

    fun setTime(time: Long) {
        timeLeft = time
        countDownTimer = object : CountDownTimer(timeLeft, 1000) {
            override fun onTick(millisUntilFinished: Long) {
                timeLeft = millisUntilFinished
                updateText()
            }

            override fun onFinish() {
                confirmTodo?.invoke()
                dismiss()
            }
        }
        updateText()
        countDownTimer.start()
    }

    override fun show() {
        super.show()
        setTime(countdownTime)
    }


    @SuppressLint("SetTextI18n")
    private fun updateText() {
        val seconds = (timeLeft / 1000).toInt() % 60
        val rawText = confirmText
        mBinding.vConfirmTv.text = "$rawText(${(seconds + 1)})"
        updateFont()
    }

    // 更新字体，防止国际化问题
    private fun updateFont() {
        if (mBinding.vConfirmTv.text.length >= 12)
            mBinding.vConfirmTv.textSize = 11f
    }


    override fun dismiss() {
        super.dismiss()
        countDownTimer.cancel()
    }

}