package com.timekettle.upup.comm.utils

import android.app.Application
import android.content.BroadcastReceiver
import android.content.Context
import android.content.Intent
import android.content.IntentFilter
import android.hardware.usb.UsbConstants
import android.hardware.usb.UsbDevice
import android.hardware.usb.UsbDeviceConnection
import android.hardware.usb.UsbEndpoint
import android.hardware.usb.UsbInterface
import android.hardware.usb.UsbManager
import android.util.Log
import com.bluetrum.fota.usb.scsi.SCSICommunicator
import com.bluetrum.fota.usb.scsi.SCSIInterface
import com.bluetrum.fota.usb.scsi.SCSIRawFotaResponse
import com.bluetrum.fota.usb.scsi.SCSIResponse
import com.timekettle.upup.base.BaseApp
import com.timekettle.upup.base.utils.logD
import com.timekettle.upup.base.utils.logE
import com.tmk.libserialhelper.serialport.DataConversion.decodeHexString
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch
import java.io.IOException
import java.lang.ref.WeakReference
import java.util.Locale
import java.util.concurrent.Executors
import java.util.concurrent.ScheduledFuture
import java.util.concurrent.TimeUnit

object USBHIDController {
    const val TAG = "USBHIDReader"
    private lateinit var usbManager: UsbManager
    private const val VID: Int = 32903
    private const val PID: Int = 4132
    private var usbDevice: UsbDevice? = null
    private var usbHidInterface: UsbInterface? = null
    private var usbHidRead: UsbEndpoint? = null
    private var usbHidWrite: UsbEndpoint? = null
    private var communicator: SCSICommunicator? = null
    private var usbDeviceConnection: UsbDeviceConnection? = null
    private var enableDebug = true
    private var packetSize = 0
    private val listeners = mutableListOf<WeakReference<IReceiveDataListener>>()
    private var attachListener: (() -> Unit)? = null
    private val scheduler = Executors.newSingleThreadScheduledExecutor()
    private var readTask: ScheduledFuture<*>? = null

    private var otaData: ByteArray? = null
    private var offset = 0
    private const val BLOCK_SIZE = 512
    private val totalSize: Int
        get() = otaData!!.size
    private val isDeviceReady: Boolean
        get() = communicator != null
    private val isUpdateReady: Boolean
        get() = isDeviceReady && otaData != null
    private val isFinished: Boolean
        get() = otaData?.size == offset

    fun open(): Boolean {
        usbDevice = usbManager.deviceList.values.find {
            it.vendorId == VID && it.productId == PID
        }
        if (usbDevice == null) {
            loge( "没有找到VID为$VID PID为$PID 的设备！")
            return false
        }
        if (usbManager.hasPermission(usbDevice)) {
            if (findInterface()) {
                if (assignEndpoint()) {
                    return openDevice()
                } else {
                    loge( "没有找到端点")
                }
            } else {
                loge( "没有找到Hid接口")
            }
        } else {
            loge("没权限打开")
        }
        return false
    }


    private fun findInterface(): Boolean {
        if (usbDevice == null) {
            return false
        }
        logd( "USB接口数量 : " + usbDevice!!.interfaceCount)
        for (i in 0 until usbDevice!!.interfaceCount) {
            val intf = usbDevice!!.getInterface(i)
            logd(  "USB接口 : $intf")
            if (intf.interfaceClass == UsbConstants.USB_CLASS_HID) {
                logd(  "找到了HID接口(class=3)")
                usbHidInterface = intf
                return true
            }
        }
        return false
    }


    // 指定端点
    private fun assignEndpoint(): Boolean {
        logd(  "指定分配端点")
        if (usbHidInterface == null) {
            return false
        }
        val endpointType = UsbConstants.USB_ENDPOINT_XFER_INT
        for (i in 0 until usbHidInterface!!.endpointCount) {
            val ep = usbHidInterface!!.getEndpoint(i)
            if (ep.type == endpointType) {
                if (ep.direction == UsbConstants.USB_DIR_OUT) {
                    logd( "找到了Out端点")
                    usbHidWrite = ep
                } else {
                    usbHidRead = ep
                    packetSize = usbHidRead!!.maxPacketSize
                    logd(  "找到了In端点,packetSize: $packetSize")
                }
            }
        }
        return true
    }


    private fun openDevice(): Boolean {
        if (usbHidInterface == null) {
            loge( "Usb接口为空，打开设备失败")
            return false
        }
        var conn: UsbDeviceConnection? = null
        if (usbManager.hasPermission(usbDevice)) {
            conn = usbManager.openDevice(usbDevice)
        }
        if (conn == null) {
            loge(  "连接为空，打开设备失败")
            return false
        }
        // 声明访问权限，参数force在这里表示如果该接口已经被其他应用程序声明，那么是否强制夺取该接口的控制权
        if (conn.claimInterface(usbHidInterface, true)) {
            usbDeviceConnection = conn
            logd( "打开设备成功")
        } else {
            conn.close()
        }
        return true
    }

    fun setUsbThreadDataReceiver() {
        if (readTask == null) {
            readTask = scheduler.scheduleWithFixedDelay(readRunnable, 0,
                1, TimeUnit.SECONDS)
        }
    }

    private fun stopRead() {
        readTask?.cancel(true)
        readTask = null
    }

    fun close() {
        stopCommunicate()
        stopRead()
        usbHidInterface?.let {
            usbDeviceConnection?.releaseInterface(usbHidInterface)
        }
        usbDeviceConnection?.close()
        usbDeviceConnection = null
        usbHidInterface = null
        usbHidRead = null
        usbHidWrite = null
        usbDevice = null
        logd( "USB connection closed")
    }

    fun sendBytes(data: ByteArray): Int {
        if (usbDevice != null && usbHidWrite != null && usbManager.hasPermission(usbDevice)) {
            //            int status = usbHidConnection.bulkTransfer(usbHidWrite, out, out.length, 250);
            logd(  "发送[${data.size}]-> " + bytes2HexString(data))
            val ret = usbDeviceConnection!!.bulkTransfer(usbHidWrite, data, data.size, 500)
            logd( "发送结果: $ret")
            return ret
        } else {
            loge(  "无法发送，检查权限和端口")
            return -1
        }
    }

    private fun sendHexStr(str: String): Int {
        val data = decodeHexString(str)
        return if (usbDevice != null && usbHidWrite != null && usbManager.hasPermission(usbDevice)) {
            logd( "发送[${str.length}]-> " + bytes2HexString(data))
            val status = usbDeviceConnection!!.bulkTransfer(usbHidWrite, data, data.size, 500)
            logd( "发送结果: $status")
            status
        } else {
            loge( "为空，无法发送")
            -1
        }
    }

    fun toUpgradeState() {
        sendHexStr("5501")
    }

    fun checkVersion() {
        sendHexStr("5502")
    }

    fun clearConnection() {
        sendHexStr("5510")
    }

    fun checkConnect() {
        sendHexStr("5511")
    }

    fun checkPlayState() {
        sendHexStr("5512")
    }

    fun disconnectDongle() {
        sendHexStr("5506")
    }

    private val readRunnable = Runnable {
        try {
            val buffer = ByteArray(packetSize)
            if (usbDeviceConnection != null && usbHidRead != null) {
                val status =
                    usbDeviceConnection!!.bulkTransfer(
                        usbHidRead,
                        buffer,
                        packetSize,
                        100
                    )
                if (status > 0) {
                    val bufferWithoutZero = buffer.trimTrailingZeros()
                    val valueString = String(bufferWithoutZero, Charsets.UTF_8)
                    logd( "收到数据: $valueString")
                    val activeListeners = listeners.mapNotNull { it.get() }
                    if (valueString.startsWith("version_")) {
                        activeListeners.forEach {
                            it.onReceiveVersion(valueString)
                        }
                    } else if (valueString.startsWith("connected")) {
                        val deviceName = valueString.substringAfter("connected_")
                            .substringAfter("_")
                        activeListeners.forEach {
                            it.onReceiveConnectChange(true, deviceName)
                        }
                    } else if (valueString.startsWith("disconnected")){
                        activeListeners.forEach {
                            it.onReceiveConnectChange(false, "")
                        }
                    } else if (valueString.startsWith("a2dp_")) {
                        val isPlay = valueString.contains("start")
                        activeListeners.forEach {
                            it.onReceivePlayStateChange(isPlay, HIDPlayEvent.A2DP)
                        }
                    } else if (valueString.startsWith("sco_")) {
                        val isPlay = valueString.contains("setup")
                        activeListeners.forEach {
                            it.onReceivePlayStateChange(isPlay, HIDPlayEvent.SCO)
                        }
                    } else if ("no_sco_a2dp" == valueString) {
                        activeListeners.forEach {
                            it.onReceivePlayStateChange(false, HIDPlayEvent.NONE)
                        }
                    }
                }
            }
        } catch (e: Exception) {
            loge( "Error in receive thread"+e.message)
        }
    }

    interface IReceiveDataListener {
        fun onReceiveVersion(version: String) {}

        fun onReceiveConnectChange(isConnect: Boolean, deviceName: String) {}

        fun onReceivePlayStateChange(isPlay: Boolean, event: HIDPlayEvent) {}

        fun onUpgradeError() {}

        fun onUpgradeProgress(progress: Int) {}

        fun onUpgradeFinish() {}
    }

    fun setAttachListener(listener: () -> Unit) {
        attachListener = listener
    }

    fun addListener(listener: IReceiveDataListener): Boolean {
        return listeners.add(WeakReference(listener))
    }

    fun removeListener(listener: IReceiveDataListener) {
        listeners.removeAll { it.get() == listener || it.get() == null}
    }

    fun init(application: Application) {
        logD("init USBHIDController")
        val context = application.baseContext
        usbManager = context.getSystemService(Context.USB_SERVICE) as UsbManager

        val usbReceiver = UsbBroadcastReceiver()
        val filter = IntentFilter()
        filter.addAction(UsbManager.ACTION_USB_DEVICE_ATTACHED)
        filter.addAction(UsbManager.ACTION_USB_DEVICE_DETACHED)
        context.registerReceiver(usbReceiver, filter)
    }

    fun listUsbDevices(): String {
        val deviceList = usbManager.deviceList
        if (deviceList.isEmpty()) {
            return "no usb devices found"
        }
        val deviceIterator: Iterator<UsbDevice> = deviceList.values.iterator()
        var returnValue = ""
        var usbInterface: UsbInterface
        while (deviceIterator.hasNext()) {
            val device = deviceIterator.next()
            returnValue += "Name: " + device.deviceName
            returnValue += """
                
                ID: ${device.deviceId}
                """.trimIndent()
            returnValue += """
                
                Protocol: ${device.deviceProtocol}
                """.trimIndent()
            returnValue += """
                
                Class: ${device.deviceClass}
                """.trimIndent()
            returnValue += """
                
                Subclass: ${device.deviceSubclass}
                """.trimIndent()
            returnValue += """
                
                Product ID: ${device.productId}
                """.trimIndent()
            returnValue += """
                
                Vendor ID: ${device.vendorId}
                """.trimIndent()
            returnValue += """
                
                Interface count: ${device.interfaceCount}
                """.trimIndent()
            returnValue += "\n——————————————————————"
            for (i in 0 until device.interfaceCount) {
                usbInterface = device.getInterface(i)
                returnValue += "\n  接口 ${i + 1}"
                returnValue += "\n\tInterface ID: " + usbInterface.id
                returnValue += "\n\tClass: " + usbInterface.interfaceClass
                returnValue += "\n\tProtocol: " + usbInterface.interfaceProtocol
                returnValue += "\n\tSubclass: " + usbInterface.interfaceSubclass
                returnValue += "\n\tEndpoint count: " + usbInterface.endpointCount
                for (j in 0 until usbInterface.endpointCount) {
                    returnValue += "\n\t  Endpoint $j"
                    returnValue += "\n\t\t\tAddress: " + usbInterface.getEndpoint(j).address
                    returnValue += "\n\t\t\tAttributes: " + usbInterface.getEndpoint(j).attributes
                    returnValue += "\n\t\t\tDirection: " + usbInterface.getEndpoint(j).direction
                    returnValue += "\n\t\t\tNumber: " + usbInterface.getEndpoint(j).endpointNumber
                    returnValue += "\n\t\t\tInterval: " + usbInterface.getEndpoint(j).interval
                    returnValue += "\n\t\t\tType: " + usbInterface.getEndpoint(j).type
                    returnValue += "\n\t\t\tMax packet size: " + usbInterface.getEndpoint(j).maxPacketSize
                }
                returnValue += "\n——————————————————————"

            }
        }
        return returnValue
    }

    fun usbDevicesIsEmpty() = usbManager.deviceList.isEmpty()

    private fun toInt(b: Byte): Int {
        return b.toInt() and 0xFF
    }

    private fun toByte(c: Int): Byte {
        return (if (c <= 0x7f) c else c % 0x80 - 0x80).toByte()
    }

    private fun bytes2HexString(b: ByteArray): String {
        var stmp = ""
        val sb = StringBuilder("")
        for (n in b.indices) {
            stmp = Integer.toHexString(b[n].toInt() and 0xFF)
            sb.append(if (stmp.length == 1) "0$stmp" else stmp)
            sb.append(" ")
        }
        return sb.toString().uppercase(Locale.getDefault()).trim { it <= ' ' }
    }

    private fun logd(msg: String) {
        if (enableDebug) Log.d(TAG, msg)
    }

    private fun loge(msg: String) {
        if (enableDebug) Log.e(TAG, msg)
    }

    private fun startUSBUpgrade(device: UsbDevice) {
        logd("处于升级状态，开始升级")
        usbDevice = device
        usbDeviceConnection = usbManager.openDevice(device)
        if (usbDeviceConnection == null) {
            loge("打开USB设备失败")
            sendUpgradeError()
            return
        }
        communicator = SCSICommunicator(
            usbDevice,
            usbDeviceConnection
        )
        val openResult = communicator?.openSCSICommunicator(scsiInterface) == true

        if (openResult) {
            communicator?.setTimeout(1000) // 设置超时
            logd("打开USB设备成功")
            startUpdate()
        } else {
            loge("USB设备建连失败")
            stopCommunicate()
            sendUpgradeError()
        }
    }

    private fun startUpdate() {
        offset = 0
        if (isUpdateReady) {
            doUpdate()
        } else {
            sendUpgradeError()
        }
    }

    fun setFilePath(context: Context, filePath: String): Boolean {
        // 读取文件在库外部进行处理
        try {
            this.otaData = getFileFromAssets(context, filePath)
            if (otaData == null) {
                Log.e(DongleOtaUtil.TAG, "升级文件读取为空")
                sendUpgradeError()
                return false
            }
            logD("ota大小 = ${otaData!!.size}")
            return true
        } catch (e: IOException) {
            Log.e(TAG, "升级文件读取出错", e)
            sendUpgradeError()
            return false
        }
    }

    private fun getFileFromAssets(context: Context, filePath: String): ByteArray? {
        return try {
            context.assets.open(filePath).use {
                // 如果能打开文件，表示文件存在
                it.readBytes()
            }
        } catch (e: IOException) {
            // 如果抛出 IOException，表示文件不存在
            logE("文件不存在")
            null
        }
    }

    private fun updateFinished() {
        stopCommunicate()
        val activeListeners = listeners.mapNotNull { it.get() }
        activeListeners.forEach {
            it.onUpgradeFinish()
        }
    }

    private fun doUpdate() {
        val otaData = otaData ?: return
        val communicator = communicator ?: return

        BaseApp.mCoroutineScope.launch(Dispatchers.IO) {
            if (offset < totalSize) { // 每次只发一包
                val dataSize =
                    if (totalSize - offset < BLOCK_SIZE)
                        totalSize - offset
                    else
                        BLOCK_SIZE
                val fotaData = otaData.copyOfRange(offset, offset + dataSize)
                offset += dataSize
                communicator.writeRawFota(fotaData)
                // 更新进度，包括正在发送
                showProgress(offset, totalSize)
            }
        }
    }

    private fun showProgress(offset: Int, total: Int) {
        val progress = offset * 100 / total
        val activeListeners = listeners.mapNotNull { it.get() }
        activeListeners.forEach {
            it.onUpgradeProgress(progress)
        }
    }

    private fun sendUpgradeError() {
        val activeListeners = listeners.mapNotNull { it.get() }
        activeListeners.forEach {
            it.onUpgradeError()
        }
    }

    private fun stopCommunicate() {
        communicator?.closeSCSICommunicator()
        communicator = null
    }

    /**
     * 去除 ByteArray 尾部的 0x00 字节
     *
     * @return 去除尾部 0x00 字节后的新数组
     */
    fun ByteArray.trimTrailingZeros(): ByteArray {
        var i = this.size - 1

        // 从数组末尾开始，找到第一个非 0x00 字节的位置
        while (i >= 0 && this[i] == 0x00.toByte()) {
            i--
        }

        // 创建一个新的数组，包含有效数据
        return this.copyOfRange(0, i + 1)
    }

    private class UsbBroadcastReceiver : BroadcastReceiver() {
        override fun onReceive(context: Context, intent: Intent) {
            val action = intent.action
            val usbDevice = intent.getParcelableExtra<UsbDevice>(UsbManager.EXTRA_DEVICE)

            when (action) {
                UsbManager.ACTION_USB_DEVICE_ATTACHED -> {
                    if (usbDevice?.vendorId == VID && usbDevice.productId == PID) {
                        logd("检测到 usb 设备加入，触发打开")
                        if (usbDevice.interfaceCount > 1) {
                            // 通信状态
                            if (open()) {
                                setUsbThreadDataReceiver()
                                attachListener?.invoke()
                                attachListener = null
                            }
                        } else if (usbDevice.interfaceCount == 1) {
                            // 升级状态，开始升级
                            startUSBUpgrade(usbDevice)
                        }
                    }
                }
                UsbManager.ACTION_USB_DEVICE_DETACHED -> {
                    if (usbDevice?.vendorId == VID && usbDevice.productId == PID) {
                        logd("检测到 usb 设备移除，触发关闭")
                        close()
                    }
                }
            }
        }
    }

    private val scsiInterface: SCSIInterface = object : SCSIInterface {
        override fun onSCSIOperationCompleted(response: SCSIResponse) {
            if (response.status == 0) {
                if (response is SCSIRawFotaResponse) {
                    if (isFinished) {
                        updateFinished()
                    } else {
                        doUpdate()
                    }
                }
            } else {
                sendUpgradeError()
            }
        }

        override fun onSCSIOperationStarted(status: Boolean) {
            // SCSI operation started
//            Log.d(TAG, "onSCSIOperationStarted status: $status")
        }

        override fun onSCSIErrorOrTimeout() {
            Log.d(TAG, "onSCSIErrorOrTimeout")
            sendUpgradeError()
        }
    }

    enum class HIDPlayEvent {
        A2DP,
        SCO,
        NONE
    }
}
