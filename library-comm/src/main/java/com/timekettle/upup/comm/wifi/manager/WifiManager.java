package com.timekettle.upup.comm.wifi.manager;

import android.content.Context;

import com.timekettle.upup.base.utils.SpUtils;
import com.timekettle.upup.comm.constant.SpKey;
import com.timekettle.upup.comm.wifi.entity.WifiEntity;
import com.timekettle.upup.comm.wifi.interf.IWifiManager;

import java.util.List;

public class WifiManager extends BaseWifiManager {

    private WifiManager(Context context) {
        super(context);
    }

    public static IWifiManager create(Context context) {
        return new WifiManager(context);
    }

    @Override
    public boolean isOpened() {
        return manager.isWifiEnabled();
    }

    @Override
    public void openWifi() {
        if (!manager.isWifiEnabled())
            manager.setWifiEnabled(true);
    }

    @Override
    public void closeWifi() {
        if (manager.isWifiEnabled())
            manager.setWifiEnabled(false);
    }

    @Override
    public void scanWifi() {
        manager.startScan();
    }

    @Override
    public boolean disConnectWifi() {
        return manager.disconnect();
    }

    public void reConnectWifi() {
        manager.reconnect();
    }

    @Override
    public boolean connectEncryptWifi(WifiEntity wifi, String password) {
        SpUtils.INSTANCE.put(SpKey.SSID, wifi.getSSID());
        if (manager.getConnectionInfo() != null && wifi.SSID.equals(manager.getConnectionInfo().getSSID())) {
            return true;
        }

        modifyWifi(context.getString(com.timekettle.upup.comm.R.string.main_start_connect));

//        if (android.os.Build.VERSION.SDK_INT < android.os.Build.VERSION_CODES.Q) {
            int networkId = WifiHelper.configOrCreateWifi(manager, wifi, password);
            boolean ret = manager.enableNetwork(networkId, true);
            return ret;
//        } else {
//            NetworkSpecifier specifier = new WifiNetworkSpecifier.Builder()
//                            .setSsidPattern(new PatternMatcher(wifi.getName(), PatternMatcher.PATTERN_PREFIX))
//                            .setWpa2Passphrase(password)
//                            .build();
//
//            NetworkRequest request =
//                    new NetworkRequest.Builder()
//                            .addTransportType(NetworkCapabilities.TRANSPORT_WIFI)
//                            .removeCapability(NetworkCapabilities.NET_CAPABILITY_INTERNET)
//                            .setNetworkSpecifier(specifier)
//                            .build();
//
//            ConnectivityManager connectivityManager = (ConnectivityManager)
//                    context.getSystemService(Context.CONNECTIVITY_SERVICE);
//
//            ConnectivityManager.NetworkCallback networkCallback = new ConnectivityManager.NetworkCallback() {
//                @Override
//                public void onAvailable(Network network) {
//                }
//
//                @Override
//                public void onUnavailable() {
//                }
//            };
//            connectivityManager.requestNetwork(request, networkCallback);
//
//        }
//        return false;
    }

    @Override
    public boolean connectSavedWifi(WifiEntity wifi) {
        SpUtils.INSTANCE.put(SpKey.SSID, wifi.getSSID());
        modifyWifi(context.getString(com.timekettle.upup.comm.R.string.main_start_connect));

        int networkId = WifiHelper.configOrCreateWifi(manager, wifi, null);
//        disConnectWifi();
        boolean ret = manager.enableNetwork(networkId, true);
//        modifyWifi(wifi.SSID, "开始连接...");
//        reConnectWifi();
        return ret;
    }

    @Override
    public boolean connectOpenWifi(WifiEntity wifi) {
        SpUtils.INSTANCE.put(SpKey.SSID, wifi.getSSID());
        boolean ret = connectEncryptWifi(wifi, null);
//        modifyWifi(wifi.SSID, "开始连接...");
        return ret;
    }

    @Override
    public boolean removeWifi(WifiEntity wifi) {
        boolean ret = WifiHelper.deleteWifiConfiguration(manager, wifi);
        modifyWifi();
        return ret;
    }

    @Override
    public List<WifiEntity> getWifi() {
        return wifiEntities;
    }

    @Override
    public int getWifiState() {
        return manager.getWifiState();
    }

}
