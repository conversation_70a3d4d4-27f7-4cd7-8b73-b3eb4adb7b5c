package com.timekettle.upup.comm.receiver

import android.database.ContentObserver
import android.net.Uri
import android.os.Handler
import android.provider.Settings
import android.util.Log
import co.timekettle.btkit.BleUtil
import co.timekettle.btkit.bean.WT2BlePeripheral
import com.blankj.utilcode.util.ActivityUtils
import com.timekettle.upup.base.utils.logD
import com.timekettle.upup.comm.constant.GlobalConstant
import com.timekettle.upup.comm.service.home.HomeServiceImplWrap
import com.timekettle.upup.comm.service.setting.SettingServiceImplWrap

class BleVolumeObserver(handler: Handler) : ContentObserver(handler) {
    override fun onChange(selfChange: Boolean, uri: Uri?) {
        uri?.let {
            val value =
                Settings.System.getInt(ActivityUtils.getTopActivity().contentResolver, "BLE_VOLUME")
            logD("新的BLE音量值：$value")
            GlobalConstant.bleVolume = value
            SettingServiceImplWrap.updateBleVolume(value)
        }
        super.onChange(selfChange, uri)
    }
}
