package com.timekettle.upup.comm.base

import android.app.Dialog
import android.content.Context
import android.content.res.Configuration
import android.content.res.Resources
import android.os.IBinder
import android.util.DisplayMetrics
import android.view.Display
import android.view.KeyEvent
import android.view.MotionEvent
import android.view.View
import android.view.ViewGroup
import android.view.WindowManager
import android.view.inputmethod.InputMethodManager
import android.widget.EditText
import android.widget.ImageView
import android.widget.TextView
import androidx.constraintlayout.widget.ConstraintLayout
import androidx.constraintlayout.widget.ConstraintSet
import androidx.viewbinding.ViewBinding
import com.alibaba.android.arouter.launcher.ARouter
import com.blankj.utilcode.util.ConvertUtils
import com.timekettle.upup.base.ktx.observeLiveData
import com.timekettle.upup.base.mvvm.v.BaseFrameActivity
import com.timekettle.upup.base.mvvm.vm.BaseViewModel
import com.timekettle.upup.base.utils.AndroidBugFixUtils
import com.timekettle.upup.base.utils.StateLayoutEnum
import com.timekettle.upup.comm.R
import com.timekettle.upup.comm.constant.RouteUrl
import com.timekettle.upup.comm.dialog.DialogFactory
import com.timekettle.upup.comm.widget.TopDeviceView
import com.timekettle.upup.comm.widget.WifiBatteryView
import kotlin.math.pow

/**
 * Activity 基类
 *
 * <AUTHOR>
 * @since 2022/04/15
 */
abstract class BaseActivity<VB : ViewBinding, VM : BaseViewModel> : BaseFrameActivity<VB, VM>() {

    var mTitleTv: TextView? = null
    var mTitleRightTv: TextView? = null
    var mBackIv: ImageView? = null
    var vTitleQuestionIcon: ImageView? = null
    var vTitleSettingIcon: ImageView? = null

    /**
     * 初始化顶部toolBar
     */
    override fun initToolBar() {

        findViewById<ImageView>(R.id.vBackIv)?.setOnClickListener { onBackPressed() }
        mTitleTv = findViewById(R.id.vTitleTv)
        mTitleRightTv = findViewById(R.id.vTitleRightTv)
        mBackIv = findViewById(R.id.vBackIv)
        vTitleQuestionIcon = findViewById(R.id.vTitleQuestionIcon)
        vTitleSettingIcon = findViewById(R.id.vTitleSettingIcon)
        mTitleTv?.setOnClickListener { onBackPressed() }
        initStateObserve()

    }

    override fun getResources(): Resources {
        // 主要是为了解决 AndroidAutoSize 在横屏切换时导致适配失效的问题
        // 但是 AutoSizeCompat.autoConvertDensity() 对线程做了判断 导致Coil等图片加载框架在子线程访问的时候会异常
        // 所以在这里加了线程的判断 如果是非主线程 就取消单独的适配
        val res = super.getResources()
        return res
    }

    override fun onDestroy() {
        // 解决某些特定机型会触发的Android本身的Bug
        AndroidBugFixUtils().fixSoftInputLeaks(this)
        super.onDestroy()
    }


    // Activity默认会监测stateViewLD的状态，并显示loading的弹窗
    // 如果需要自定义逻辑，重写这个方法直接return，然后自己观察stateViewLD的
    private fun initStateObserve() {
        observeLiveData(mViewModel.stateViewLD, ::processStateView)
    }

    open fun processStateView(state: StateLayoutEnum) {
        when (state) {
            StateLayoutEnum.LOADING -> showProgressDialog()
            else -> dismissProgressDialog()
        }
    }

    private var progressDialog: Dialog? = null


    open fun showProgressDialog() {
        dismissProgressDialog()
        progressDialog = DialogFactory.createProcessDialog(
            this,
        )
        progressDialog?.show()
    }

    open fun dismissProgressDialog() {
        progressDialog?.dismiss()
        progressDialog = null
    }


    /**
     * 根据EditText所在坐标和用户点击的坐标相对比，
     * 来判断是否隐藏键盘，因为当用户点击EditText时则不能隐藏
     */
    private fun isShouldHideKeyboard(v: View?, event: MotionEvent): Boolean {
        if (v != null && v is EditText) {
            val l = intArrayOf(0, 0)
            v.getLocationInWindow(l)
            val left = l[0]
            val top = l[1]
            val bottom = top + v.getHeight()
            val right = left + v.getWidth()
            return !(event.x > left && event.x < right && event.y > top && event.y < bottom)
        }
        // 如果焦点不是EditText则忽略，这个发生在视图刚绘制完，第一个焦点不在EditText上，和用户用轨迹球选择其他的焦点
        return false
    }


    /**
     * 获取InputMethodManager，隐藏软键盘
     */
    private fun hideKeyboard(token: IBinder?) {
        if (token != null) {
            val im = getSystemService(Context.INPUT_METHOD_SERVICE) as InputMethodManager
            im.hideSoftInputFromWindow(token, InputMethodManager.HIDE_NOT_ALWAYS)
        }
    }


    /**
     * 是否是平板
     *
     * @param context 上下文
     * @return 是平板则返回true，反之返回false
     */
    open fun isPad(context: Context): Boolean {
        val isPad: Boolean = (context.resources.configuration.screenLayout
                and Configuration.SCREENLAYOUT_SIZE_MASK) >= Configuration.SCREENLAYOUT_SIZE_LARGE
        val wm: WindowManager = context.getSystemService(Context.WINDOW_SERVICE) as WindowManager
        val display: Display = wm.defaultDisplay
        val dm = DisplayMetrics()
        display.getMetrics(dm)
        val x = (dm.widthPixels / dm.xdpi).toDouble().pow(2.0)
        val y = (dm.heightPixels / dm.ydpi).toDouble().pow(2.0)
        val screenInches = kotlin.math.sqrt(x + y) // 屏幕尺寸
        return isPad || screenInches >= 7.0
    }


    // 手动添加电池的View,需要传进来一个容器
    fun addDeviceView(layout: ViewGroup) {
        val view = TopDeviceView(this).apply {
            id = View.generateViewId()
            layoutParams = ConstraintLayout.LayoutParams(
                ConstraintLayout.LayoutParams.WRAP_CONTENT,
                ConstraintLayout.LayoutParams.WRAP_CONTENT,
            ).apply {
                startToStart = ConstraintSet.PARENT_ID
                endToEnd = ConstraintSet.PARENT_ID
            }
        }
        // 将 View 添加到 ConstraintLayout 中
        layout.addView(view)
        // 设置 View 在 ConstraintLayout 中水平和垂直居中
        val constraintSet = ConstraintSet()
        constraintSet.clone(view)
//        constraintSet.centerHorizontally(view.id, ConstraintSet.PARENT_ID)
//        constraintSet.centerVertically(view.id, ConstraintSet.PARENT_ID)
        constraintSet.connect(
            view.id,
            ConstraintSet.TOP,
            ConstraintSet.PARENT_ID,
            ConstraintSet.TOP
        )
        constraintSet.applyTo(view)
    }


    /**
     * 手动添加电池+Wifi的View,需要传进来一个容器
     * 通过 showWifi 控制是否显示WiFi的图标
     */

    fun addWifiBatteryView(layout: ViewGroup, showWifi: Boolean = true) {
        val battery = WifiBatteryView(this@BaseActivity).apply {
            id = View.generateViewId()
            layoutParams = ConstraintLayout.LayoutParams(
                ConstraintLayout.LayoutParams.WRAP_CONTENT,
                ConstraintLayout.LayoutParams.WRAP_CONTENT,
            ).apply {
                endToEnd = ConstraintSet.PARENT_ID
                bottomToBottom = ConstraintSet.PARENT_ID
                topToTop = ConstraintSet.PARENT_ID
                marginEnd = ConvertUtils.dp2px(4f)
            }
            setWifiVisible(showWifi)
        }
        layout.addView(battery)
    }

    var isKeyCode = true

    override fun onKeyDown(keyCode: Int, event: KeyEvent?): Boolean {
        if (keyCode == KeyEvent.KEYCODE_BACK && event!!.eventTime - event.downTime > 0) {
//            startActivityByRoute(RouteUrl.Translate.KeypadActivityMain)
            ARouter.getInstance().build(RouteUrl.Translate.KeypadActivityMain)
                .withInt("KeypadType", 1).navigation()
        }

        return super.onKeyDown(keyCode, event)
    }


}