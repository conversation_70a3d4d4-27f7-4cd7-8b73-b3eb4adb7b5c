package com.timekettle.upup.comm.widget

import android.graphics.Canvas
import android.graphics.Color
import android.graphics.Paint
import androidx.recyclerview.widget.RecyclerView
import com.blankj.utilcode.util.SizeUtils

class HorizontalScrollBarDecoration: RecyclerView.ItemDecoration() {

    override fun onDrawOver(c: Canvas, parent: RecyclerView, state: RecyclerView.State) {
        super.onDrawOver(c, parent, state)

        val barHeight = SizeUtils.dp2px(2f)
        val scrollWidth = SizeUtils.dp2px(80f)
        val indicatorWidth = SizeUtils.dp2px(50f)
        val paddingBottom = SizeUtils.dp2px(4f)
        val barX = (parent.width / 2 - scrollWidth / 2).toFloat()
        val barY = (parent.height - paddingBottom - barHeight).toFloat()
//        val barY = (parent.height  - barHeight).toFloat()

        val paint = Paint()
        paint.isAntiAlias = true
        paint.color = Color.parseColor("#26FFFFFF")
        paint.strokeCap = Paint.Cap.ROUND
        paint.strokeWidth = barHeight.toFloat()
        c.drawLine(barX, barY, barX + scrollWidth.toFloat(), barY, paint)

        val extent = parent.computeHorizontalScrollExtent()
        val range = parent.computeHorizontalScrollRange()
        val offset = parent.computeHorizontalScrollOffset()
        val maxEndX = (range - extent).toFloat()
        //可滑动
        if (maxEndX > 0) {
            val proportion = offset / maxEndX

            val scrollableDistance = scrollWidth - indicatorWidth

            val offsetX = scrollableDistance * proportion
            paint.color = Color.parseColor("#73FFFFFF")
            c.drawLine(barX + offsetX, barY, barX + indicatorWidth.toFloat() + offsetX, barY, paint)
        } else {
            paint.color = Color.parseColor("#73FFFFFF")
            c.drawLine(barX, barY, barX + scrollWidth.toFloat(), barY, paint)
        }
    }
}
