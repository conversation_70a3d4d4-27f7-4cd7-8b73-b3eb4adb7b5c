package com.timekettle.upup.comm.dao

import androidx.room.Dao
import androidx.room.Query
import com.timekettle.upup.comm.base.BaseDao
import com.timekettle.upup.comm.bean.CallRecordEntity
import java.util.Date

@Dao
abstract class CallRecordDao: BaseDao<CallRecordEntity>() {

    @Query("UPDATE CallRecordEntity SET date = :date WHERE phoneNumber = :phoneNumber")
    abstract suspend fun update(date: Date, phoneNumber: String): Int

    @Query("SELECT * FROM CallRecordEntity ORDER BY date DESC")
    abstract suspend fun getAllCallRecord(): List<CallRecordEntity>

    @Query("SELECT * FROM CallRecordEntity WHERE phoneNumber = :phoneNumber ORDER BY date DESC")
    abstract suspend fun getCallRecordByPhoneNumber(phoneNumber: String): List<CallRecordEntity>

    @Query("SELECT * FROM CallRecordEntity ORDER BY date DESC LIMIT 1")
    abstract suspend fun getLastCallRecord(): CallRecordEntity

}