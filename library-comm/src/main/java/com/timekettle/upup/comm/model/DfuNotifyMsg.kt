package com.timekettle.upup.comm.model

import android.os.Parcelable
import com.blankj.utilcode.util.PathUtils
import com.google.gson.annotations.SerializedName
import kotlinx.parcelize.Parcelize

/**
 * <AUTHOR>
 * @date 2022/11/30 18:34
 * @email <EMAIL>
 * @desc Dfu升级推送消息的实体bean
 */
@Parcelize
data class DfuNotifyMsg(
//    val versionA: String = "v1.0.2",  // 串口升级包的版本
    @SerializedName("audio_chip_version")
    val versionA: String? = "",  // 串口升级包的版本
//    val uriA: String = "http://*************/licoba/myfile/-/raw/main/fw5000_006.upd",
    @SerializedName("audio_chip_url")
    var uriA: String? = "",
    @SerializedName("iot_chip_version")
    val versionB: String = "v1.0.2",  // BLE升级包的版本
    @SerializedName("iot_chip_url")
    var uriB: String? = "",
    val appVersion: String? = "v1.0.0",
    @SerializedName("local_time_zone")
    val localTimeZone: Boolean = true,   // 是否根据时区来升级
    @SerializedName("publish_time")
    val publishTime: String? = "2023-06-29 03:00:00",   // 时区升级的时间
    val saveDir: String = "${PathUtils.getInternalAppDataPath()}/file/Download/",   // 常量，保存在本地的目录
    val product: String = "W3Pro",   //产品型号
    val id: Long = 1L
) : Parcelable