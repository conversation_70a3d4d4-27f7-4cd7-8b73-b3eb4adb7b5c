package com.timekettle.upup.comm.utils;

import java.util.HashMap;

/**
 * @author: licoba
 * @date: 2022/9/22
 * 用来在activity之间传递对象的类，一次性获取，在获取到对象之后会把hashtable清空，使用方式：
 * Activity1：IntentHelper.addObjectForKey(obj, "key");
 * Activity2：Object obj = (Object) IntentHelper.getObjectForKey("key");
 */
public class IntentHelper {
    private static IntentHelper _instance;
    private HashMap<String, Object> _hash;

    private IntentHelper() {
        _hash = new HashMap<>();
    }

    private static IntentHelper getInstance() {
        if(_instance==null) {
            _instance = new IntentHelper();
        }
        return _instance;
    }

    public static void addObjectForKey(Object object, String key) {
        getInstance()._hash.put(key, object);
    }

    public static Object getObjectForKey(String key) {
        IntentHelper helper = getInstance();
        Object data = helper._hash.get(key);
        helper._hash.remove(key);
        helper = null;
        return data;
    }
}
