package com.timekettle.upup.comm.repo

import com.blankj.utilcode.util.AppUtils
import com.timekettle.upup.base.mvvm.m.BaseRepository
import com.timekettle.upup.comm.net.CommApiService
import com.timekettle.upup.comm.net.helper.BusinessManager
import com.timekettle.upup.comm.net.helper.TokenManager
import com.timekettle.upup.comm.utils.NetApiUtil
import javax.inject.Inject

/**
 * 公共数据仓库
 * 写此类主要是为了复用代码
 *
 */
class CommRepository @Inject constructor(private val mApi: CommApiService) : BaseRepository() {

    suspend fun refreshToken(userId: Long) = request {
        mApi.refreshToken(TokenManager.refreshToken, userId).run {
            emit(this)
        }
    }

    suspend fun saveUserUsageDuration(
        userId: Long,
        sType: String,
        startTime: Long,
        endTime: Long,
        differTime: Long,
    ) = request {
        mApi.saveUserUsageDuration(
            NetApiUtil.createJsonRequestBody(
                "userId" to userId,
                "sType" to sType,
                "startTime" to startTime,
                "endTime" to endTime,
                "differTime" to differTime // 使用时长 单位-毫秒
            )
        ).run {
            emit(this)
        }
    }

    suspend fun getDfuNotify(appVersion: String) = request {
        mApi.getW3ProDfuNotify(
            AppUtils.getAppVersionName()
        ).run {
            if (this.data != null) emit(this.data)
        }

    }

    fun getDfuNotifyV2(appVersion: String) = BusinessManager.getDfuNotify(AppUtils.getAppVersionName())

    // 从三方的API获取时区信息
    suspend fun getTimeZone() = request {
        mApi.getTimeZone().run {
            emit(this.body())
        }
    }

}