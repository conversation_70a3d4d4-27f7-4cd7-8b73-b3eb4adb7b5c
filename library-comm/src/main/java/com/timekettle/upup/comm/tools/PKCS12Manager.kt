package com.timekettle.upup.comm.tools

import android.security.keystore.KeyProperties
import android.util.Base64
import com.timekettle.upup.base.BaseApp
import com.timekettle.upup.base.utils.logD
import com.timekettle.upup.base.utils.logE
import com.timekettle.upup.comm.utils.CertificateUtils
import com.timekettle.upup.comm.utils.DeviceUtil
import org.bouncycastle.asn1.x500.X500Name
import org.bouncycastle.operator.jcajce.JcaContentSignerBuilder
import org.bouncycastle.pkcs.jcajce.JcaPKCS10CertificationRequestBuilder
import java.io.File
import java.io.FileInputStream
import java.io.FileOutputStream
import java.security.KeyPair
import java.security.KeyPairGenerator
import java.security.KeyStore
import java.security.cert.CertificateFactory
import java.security.cert.X509Certificate
import javax.net.ssl.KeyManagerFactory
import javax.net.ssl.TrustManagerFactory
import javax.net.ssl.X509KeyManager
import javax.net.ssl.X509TrustManager

/**
 * 密钥管理类（PKCS12 版本）
 * author: weiconglee
 **/
object PKCS12Manager {
    private const val TAG = "PKCS12Manager"
    private var CERT_DIR_PATH = BaseApp.application.filesDir.parentFile?.absolutePath + "/certs"
    private var PKCS12_KEYSTORE_PATH = "$CERT_DIR_PATH/cert.bin"
    private var keyPassword = ""
    const val PKCS_KEY_ALIAS = "Timekettle_PKCS12_Key"
    private val keyStore: KeyStore = KeyStore.getInstance("PKCS12")

    @Synchronized
    private fun loadKeyStore(): Boolean {
        keyPassword = AKSManager.getPKCS12Key()
        if (keyPassword.isEmpty()) return false
        val dir = File(CERT_DIR_PATH)
        if (!dir.exists()) dir.mkdirs()
        if (!keyStore.isLoaded()) {
            logE("keystore not loaded")
            // 加载或创建 KeyStore
            if (File(PKCS12_KEYSTORE_PATH).exists()) {
                try {
                    FileInputStream(PKCS12_KEYSTORE_PATH).use { fis ->
                        keyStore.load(fis, keyPassword.toCharArray())
                    }
                } catch (e: Exception) {
                    e.printStackTrace()
                    logE("loadKeyStore: ${e.message}", TAG)
                    keyStore.load(null, keyPassword.toCharArray())
                }
            } else {
                keyStore.load(null, keyPassword.toCharArray())
            }
        }
        return true
    }

    /**
     * 判断 keyStore 是否已经加载
     * @receiver KeyStore
     * @return Boolean
     */
    private fun KeyStore.isLoaded(): Boolean {
        return try {
            // 尝试获取别名，如果成功则说明已经加载
            this.aliases().hasMoreElements()
            true
        } catch (e: Exception) {
            false
        }
    }

    /**
     * 生成设备基础密钥对（RSA 2048）并存储到 PKCS12 文件中
     * @return KeyPair
     */
    fun createKeyPair(): KeyPair {
        val keyPairGenerator = KeyPairGenerator.getInstance(KeyProperties.KEY_ALGORITHM_RSA)
        keyPairGenerator.initialize(2048)
        val keyPair = keyPairGenerator.generateKeyPair()
        return keyPair
    }

    /**
     * 创建CSR（PEM格式）
     * @param keyPair KeyPair
     * @return String
     */
    fun createCsr(keyPair: KeyPair): String {
        val csrBuilder = JcaPKCS10CertificationRequestBuilder(
            X500Name("CN=${DeviceUtil.getSerialNumber()}, OU=x1, O=Shenzhen Timekettle Technologies Co.\\,Ltd., L=Shenzhen, ST=Guangdong, C=China"),  // 可扩展其他字段（如O/OU）
            keyPair.public
        )

        val signer = JcaContentSignerBuilder("SHA256withRSA").build(keyPair.private)

        val csr = csrBuilder.build(signer)

        val base64 = Base64.encodeToString(csr.encoded, Base64.NO_WRAP)
        val formattedBase64 = base64.chunked(64).joinToString("\n")
        val csrString =
            "-----BEGIN CERTIFICATE REQUEST-----\n$formattedBase64\n-----END CERTIFICATE REQUEST-----"
        CertificateUtils.saveCertificate(csrString, "PKCS12.csr")
        return csrString
    }

    /**
     * 保存证书链到 PKCS12 文件中
     * @param keyPair KeyPair
     * @param pemCertificate String
     */
    fun saveCertificateChain(keyPair: KeyPair, pemCertificate: String) {
        CertificateUtils.saveCertificate(pemCertificate, "PKCS12.pem")
        val regex =
            Regex("-----BEGIN CERTIFICATE-----(.*?)-----END CERTIFICATE-----", RegexOption.DOT_MATCHES_ALL)
        val matches = regex.findAll(pemCertificate)
        val list = matches.map { it.groupValues[1] }.toList().toTypedArray()

        val certificateChain: MutableList<X509Certificate> = ArrayList()

        list.forEach {
            // 2. Base64解码
            val certificateBytes = Base64.decode(it, Base64.DEFAULT)
            // 3. 解析为X509Certificate
            val certificateFactory = CertificateFactory.getInstance("X.509")
            val certificate =
                certificateFactory.generateCertificate(certificateBytes.inputStream()) as X509Certificate
            certificateChain.add(certificate)
        }

        if (!loadKeyStore()) return

        // 保存证书链
        keyStore.setKeyEntry(PKCS_KEY_ALIAS, keyPair.private, keyPassword.toCharArray(), certificateChain.toTypedArray())

        // 保存 KeyStore 到文件
        FileOutputStream(PKCS12_KEYSTORE_PATH).use { fos ->
            keyStore.store(fos, keyPassword.toCharArray())
        }
    }

    /**
     * 判断证书是否存在
     * @return Boolean
     */
    fun isCertificateExists(): Boolean {
        return try {
            getCertificateFromKeyStore()?.let {
                CertificateUtils.getIssuerCommonName(it).equals("Timkettle Root CA")
                        && CertificateUtils.getSubjectCommonName(it).equals(DeviceUtil.getSerialNumber())
            } ?: false
        } catch (e: Exception) {
            e.printStackTrace()
            false
        }
    }

    /**
     * 从 KeyStore 中获取证书并读取信息
     */
    fun getCertificateFromKeyStore(): X509Certificate? {
        if (!loadKeyStore()) return null
        return if (keyStore.containsAlias(PKCS_KEY_ALIAS))
            keyStore.getCertificate(PKCS_KEY_ALIAS) as? X509Certificate
        else null
    }

    fun getKeyManager(): X509KeyManager? {
        try {
            if (!loadKeyStore()) {
                logE("KeyManager load PKCS12 keystore 失败")
                return null
            }
            // 初始化 KeyManagerFactory
            val keyManagerFactory =
                KeyManagerFactory.getInstance(KeyManagerFactory.getDefaultAlgorithm())
            keyManagerFactory.init(keyStore, keyPassword.toCharArray())

            return keyManagerFactory.keyManagers.first() as X509KeyManager
        } catch (e: Exception) {
            e.printStackTrace()
        }
        return null
    }

    fun getTrustManager(): X509TrustManager? {
        try {
            if (!loadKeyStore()) {
                logE("TrustManager load PKCS12 keystore 失败")
                return null
            }
            // 初始化 TrustManagerFactory
            val trustManagerFactory =
                TrustManagerFactory.getInstance(TrustManagerFactory.getDefaultAlgorithm())
            trustManagerFactory.init(keyStore)
            return trustManagerFactory.trustManagers.first() as X509TrustManager
        } catch (e: Exception) {
            e.printStackTrace()
        }
        return null
    }

    fun delete() {
        try {
            if (!loadKeyStore()) return
            keyStore.deleteEntry(PKCS_KEY_ALIAS)
            File(PKCS12_KEYSTORE_PATH).delete()
        } catch (e: Exception) {
            e.printStackTrace()
        }
    }
}