package com.timekettle.upup.comm.conference

import android.util.Log

/**
 * 添加拨号状态监听
 */
@Synchronized
fun ConController.addCallStateBackListener(listener: ((Int, Int, String, String, String, String) -> Unit)?) {
    listener?.let {
        this.callStateBackList.add(it)
    }
}

/**
 * 移除拨号状态监听
 */
@Synchronized
fun ConController.removeCallStateBackListener(listener: ((Int, Int, String, String, String, String) -> Unit)?) {
    listener?.let {
        this.callStateBackList.remove(it)
    }
}

/**
 * 添加消息（包括翻译文本，广播消息）回调监听
 */
@Synchronized
fun ConController.addCallMessageBackListener(listener: ((String) -> Unit)?) {
    listener?.let {
        this.callMessageBackList.add(it)
    }
}

/**
 * 移除消息（包括翻译文本，广播消息）回调监听
 */
@Synchronized
fun ConController.removeCallMessageBackListener(listener: ((String) -> Unit)?) {
    listener?.let {
        this.callMessageBackList.remove(it)
    }
}

/**
 * 添加sip注册回调监听
 */
@Synchronized
fun ConController.addCallRegBackListener(listener: ((Int, String) -> Unit)?) {
    listener?.let {
        this.callRegBackList.add(it)
        Log.d("sdfsdfsdfsdf","addCallRegBackListener")
    }
}

/**
 * 移除sip注册回调监听
 */
@Synchronized
fun ConController.removeCallRegBackListener(listener: ((Int, String) -> Unit)?) {
    listener?.let {
        this.callRegBackList.remove(it)
    }
}

/**
 * 添加日志回调监听
 */
@Synchronized
fun ConController.addLogBackListener(listener: ((Int, String) -> Unit)?) {
    listener?.let {
        this.logBackList.add(it)
    }
}

/**
 * 移除日志回调监听
 */
@Synchronized
fun ConController.removeLogBackListener(listener: ((Int, String) -> Unit)?) {
    listener?.let {
        this.logBackList.remove(it)
    }
}

enum class Code {
    SUCCESS,
    FAIL,
    NET_ERROR,
    PWD_ERROR,
    ERROR
}

enum class Order {
    KICK_OUT,
    SNATCHING_WHEAT,
    SET_LANGUAGE,
    TRANSFER_ADMIN,
    SET_MODE,
    GET_CONFERENCE,
    END_CONFERENCE,
    NONE,//7表示视频，暂时占位，没用到
    EDIT_NAME,//修改昵称
    LEAVE_CONFERENCE//成员主动退会
}

enum class PhoneState(val state: String) {
    DECLINE("Decline"),
    DECLINED("Declined"),
    FORBIDDEN("Forbidden"),
    REQUEST_TERMINATED("Request Terminated"),
    NORMAL_CALL_CLEARING("Normal call clearing"),
    TEMPORARILY_UNAVAILABLE("Temporarily unavailable")
}

//data class AccountBean(
//    @SerializedName("sipName")
//    val sipName: String,
//    @SerializedName("sipPwd")
//    val sipPwd: String,
//    @SerializedName("accId")
//    val accId: String,
//    @SerializedName("registrar")
//    val registrar: String,
//)