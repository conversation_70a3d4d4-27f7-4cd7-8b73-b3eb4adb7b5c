package com.timekettle.upup.comm.dao

import androidx.room.Dao
import androidx.room.Query
import com.timekettle.upup.comm.base.BaseDao
import com.timekettle.upup.comm.bean.MsgBean
import java.util.Date

@Dao
abstract class MsgBeanDao: BaseDao<MsgBean>() {
    @Query("SELECT COUNT(*) FROM MsgBean WHERE hDate = :date")
    abstract suspend fun countByHistoryDate(date: Date): Int

    @Query("SELECT * FROM MsgBean WHERE hDate = :date")
    abstract suspend fun queryMsgDetailByHistoryDate(date: Date): List<MsgBean>

    @Query("SELECT * FROM MsgBean WHERE pDate = :date")
    abstract suspend fun queryMsgDetailByPhoneDate(date: Date): List<MsgBean>

    @Query("DELETE FROM MsgBean WHERE hDate IN (:dates)")
    abstract suspend fun deleteMsgDetailByHistoryDateList(dates: List<Date>)

    @Query("DELETE FROM MsgBean WHERE pDate IN (:dates)")
    abstract suspend fun deleteMsgDetailByPhoneDateList(dates: List<Date>)
}