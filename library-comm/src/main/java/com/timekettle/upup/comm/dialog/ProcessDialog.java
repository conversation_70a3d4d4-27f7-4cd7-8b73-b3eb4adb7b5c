package com.timekettle.upup.comm.dialog;

import static android.view.View.SYSTEM_UI_FLAG_LIGHT_STATUS_BAR;

import android.app.Dialog;
import android.content.Context;
import android.graphics.Color;
import android.graphics.drawable.ColorDrawable;
import android.os.Build;
import android.os.Bundle;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.view.Window;
import android.view.WindowManager;
import android.widget.RelativeLayout;

import androidx.annotation.NonNull;

import com.lihang.ShadowLayout;
import com.timekettle.upup.base.BaseApp;
import com.timekettle.upup.comm.R;

import org.libpag.PAGFile;
import org.libpag.PAGView;

/**
 * <AUTHOR>
 * @date 2022/11/15 10:59
 * @email <EMAIL>
 * @desc 全屏幕的dialog process处理弹窗
 * https://blog.csdn.net/qq_31709249/article/details/100177614
 */
public class ProcessDialog extends Dialog {
    private View view;

    public ProcessDialog(@NonNull Context context) {
        super(context);
        // 设置状态栏字体是深色的
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.LOLLIPOP) {
            getWindow().addFlags(WindowManager.LayoutParams.FLAG_DRAWS_SYSTEM_BAR_BACKGROUNDS);
            getWindow().setStatusBarColor(Color.TRANSPARENT);
            getWindow().getDecorView().setSystemUiVisibility(SYSTEM_UI_FLAG_LIGHT_STATUS_BAR);
        }

    }

    public ProcessDialog(@NonNull Context context, View view) {
        super(context);
        this.view = view;
    }

    public ProcessDialog(@NonNull Context context, int themeResId, View view) {
        super(context, themeResId);
        this.view = view;

    }

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        getWindow().requestFeature(Window.FEATURE_NO_TITLE);
        // 设置View
        PAGView pagView = new PAGView(getContext());
        pagView.setLayoutParams(new RelativeLayout.LayoutParams(
                ViewGroup.LayoutParams.MATCH_PARENT,
                ViewGroup.LayoutParams.MATCH_PARENT
        ));
        pagView.setComposition(PAGFile.Load(BaseApp.context().getAssets(), "ani_loading_bmp.pag"));
        pagView.setRepeatCount(0);
        pagView.play();
        View view = LayoutInflater.from(getContext()).inflate(R.layout.comm_dialog_progress, null);
        ShadowLayout shadowLayout = view.findViewById(R.id.v_shadow_layout);
        shadowLayout.addView(pagView);
        setContentView(view);
        // 设置属性
        setCanceledOnTouchOutside(false);
        setCancelable(false);
        getWindow().setBackgroundDrawable(new ColorDrawable(0x00000000));
        getWindow().setLayout(WindowManager.LayoutParams.MATCH_PARENT, WindowManager.LayoutParams.MATCH_PARENT);
        getWindow().addFlags(WindowManager.LayoutParams.FLAG_DRAWS_SYSTEM_BAR_BACKGROUNDS);
        getWindow().setDimAmount(0f);

    }
}