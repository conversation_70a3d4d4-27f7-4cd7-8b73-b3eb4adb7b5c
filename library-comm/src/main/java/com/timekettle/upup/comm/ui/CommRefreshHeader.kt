package com.timekettle.upup.comm.ui

import android.annotation.SuppressLint
import android.content.Context
import android.util.AttributeSet
import android.view.LayoutInflater
import android.view.View
import androidx.constraintlayout.widget.ConstraintLayout
import com.airbnb.lottie.LottieAnimationView
import com.scwang.smart.refresh.layout.api.RefreshHeader
import com.scwang.smart.refresh.layout.api.RefreshKernel
import com.scwang.smart.refresh.layout.api.RefreshLayout
import com.scwang.smart.refresh.layout.constant.RefreshState
import com.scwang.smart.refresh.layout.constant.SpinnerStyle
import com.timekettle.upup.comm.R


/**
 * 公共的 下拉刷新头 自定义Header 结合Lottie
 * https://github.com/airbnb/lottie-android
 * https://github.com/wapchief/SmartRefreshLottie  结合Lottie https://www.jianshu.com/p/e4e8a41a75b6
 * https://github.com/scwang90/SmartRefreshLayout/blob/master/art/md_custom.md  自定义header
 */
class CommRefreshHeader @JvmOverloads constructor(
    context: Context, attrs: AttributeSet? = null
) : ConstraintLayout(context, attrs), RefreshHeader {
    var mAnimationView: LottieAnimationView

    init {
        val view = LayoutInflater.from(context)
            .inflate(R.layout.comm_refresh_header, this)
        mAnimationView =
            view.findViewById(R.id.vLoadingLottie)
    }

    override fun onStateChanged(
        refreshLayout: RefreshLayout,
        oldState: RefreshState,
        newState: RefreshState
    ) {
    }


    override fun getView(): View {
        return this;
    }

    override fun getSpinnerStyle(): SpinnerStyle {
        return SpinnerStyle.Translate
    }

    override fun setPrimaryColors(vararg colors: Int) {
    }

    override fun onInitialized(kernel: RefreshKernel, height: Int, maxDragHeight: Int) {
    }

    override fun onMoving(
        isDragging: Boolean,
        percent: Float,
        offset: Int,
        height: Int,
        maxDragHeight: Int
    ) {
    }

    override fun onReleased(refreshLayout: RefreshLayout, height: Int, maxDragHeight: Int) {
    }


    override fun onStartAnimator(refreshLayout: RefreshLayout, height: Int, maxDragHeight: Int) {
        mAnimationView.playAnimation();
    }

    override fun onFinish(refreshLayout: RefreshLayout, success: Boolean): Int {
        mAnimationView.cancelAnimation();
        return 0;
    }

    override fun onHorizontalDrag(percentX: Float, offsetX: Int, offsetMax: Int) {
    }

    override fun isSupportHorizontalDrag(): Boolean {
        return false
    }

}