package com.timekettle.upup.comm.button

import android.content.Context
import android.content.res.ColorStateList
import android.graphics.Canvas
import android.graphics.Color
import android.graphics.drawable.Drawable
import android.graphics.drawable.StateListDrawable
import android.util.AttributeSet
import android.util.Log
import android.view.Gravity
import androidx.appcompat.widget.AppCompatButton
import androidx.core.content.ContextCompat
import com.blankj.utilcode.util.ConvertUtils
import com.timekettle.upup.comm.R



/**
 *  @author: licoba
 *  @date: 2023/05/19
 *
 * Button(圆角Button带点击效果,正常Button带点击效果，可以设置文字和图标)
 * https://github.com/jiaowenzheng/CustomButton
 * normalSolidColor                               正常状态背景填充颜色
 * pressedSolidColor                              按下状态背景填充颜色
 * normalDrawable                                 正常状态背景图片
 * pressedDrawable                                按下状态背景图片
 * isSelected                                     是否支持button选中状态 与setSelected()配合使用
 * normalTextColor                                正常状态文字的颜色
 * selectedTextColor                              选中状态下文字的颜色
 *
 */
class CommonButton @JvmOverloads constructor(
    context: Context,
    attrs: AttributeSet? = null,
    defStyleAttr: Int = android.R.attr.buttonStyle   // 使用Button的默认样式，如果是0，代表无样式。
) : AppCompatButton(context, attrs, defStyleAttr) {


    private var selector: StateListDrawable? = null
    private var radius = 0
    private var theme = Theme.THEME_NORMAL  // 主题 （主色、蓝色、自定义）
    private var normalDrawable: Drawable? = null
    private var pressedDrawable: Drawable? = null
    private var lrPaddingDp: Float = 16f

    init {
        setAttributeSet(context, attrs)
        initProperty()
        resetPadding()
        setTheme(theme)
    }

    private fun resetPadding(){
        // 设置
        val paddingDp = 16 // 设置左右内边距为8dp
        val paddingPx = (paddingDp * resources.displayMetrics.density).toInt()
        setPadding(paddingPx, 0, paddingPx, 0)
    }

    private fun initProperty() {
        includeFontPadding = false  // 防止文字显示不完全
        gravity = Gravity.CENTER_VERTICAL
        isAllCaps = false
    }
    override fun onDraw(canvas: Canvas) {
        val drawable = compoundDrawables[0]
        val drawableWidth = drawable?.intrinsicWidth ?: 0
        // 计算图标和文本的总宽度
        val totalWidth =
            drawableWidth + paint.measureText(text.toString()) + ConvertUtils.dp2px(
                lrPaddingDp * 2f
            )
        // 计算居中的位置
        val x = (measuredWidth - totalWidth) / 2
        // 设置图标的位置
        canvas.save()
        canvas.translate(x, 0f)
        super.onDraw(canvas)
        canvas.restore()
    }

    private fun setAttributeSet(context: Context, attrs: AttributeSet?) {
        val a = context.obtainStyledAttributes(attrs, R.styleable.commonButton)
        val normalSolid =
            a.getColor(R.styleable.commonButton_normalSolidColor, Color.parseColor("#26FFFFFF"))
        val pressedSolid =
            a.getColor(R.styleable.commonButton_pressedSolidColor, Color.TRANSPARENT)
        val normalTextColor = a.getColor(R.styleable.commonButton_normalTextColor, Color.WHITE)
        val selectedTextColor = a.getColor(R.styleable.commonButton_selectedTextColor, 0)
        val isSelected = a.getBoolean(R.styleable.commonButton_isSelected, false)
        normalDrawable = a.getDrawable(R.styleable.commonButton_normalDrawable)
        pressedDrawable = a.getDrawable(R.styleable.commonButton_pressedDrawable)
        val themeName = a.getString(R.styleable.commonButton_themeName)
        theme = when (themeName) {
            "blue" -> Theme.THEME_BLUE
            "normal" -> Theme.THEME_NORMAL
            "custom" -> Theme.THEME_CUSTOM
            else -> Theme.THEME_NORMAL
        }

        a.recycle()
    }



    /**
     * 设置Button背景
     *
     * @param drawableNormal       正常状态背景填充颜色
     * @param drawablePressed      按下状态背景填充颜色
     * @param roundButtonRadius 圆角弧度
     */
    fun setBackGround(
        drawableNormal: Drawable,
        drawablePressed: Drawable,
    ) {
        selector = StateListDrawable().apply {
            addState(intArrayOf(android.R.attr.state_pressed), drawablePressed)
            addState(intArrayOf(), drawableNormal)
        }
        background = selector
    }

    /**
     * 设置Button文字颜色
     *
     * @param normalTextColor   正常状态颜色
     * @param selectedTextColor 选中状态颜色
     */
    fun setTextColor(normalTextColor: Int, selectedTextColor: Int) {
        var normalTextColor = normalTextColor
        var selectedTextColor = selectedTextColor
        normalTextColor = resources.getColor(normalTextColor)
        selectedTextColor = resources.getColor(selectedTextColor)
        val states = Array(3) { IntArray(1) }
        states[0] = intArrayOf(android.R.attr.state_selected)
        states[1] = intArrayOf(android.R.attr.state_pressed)
        states[2] = intArrayOf()
        val textColorSelect = ColorStateList(
            states,
            intArrayOf(selectedTextColor, selectedTextColor, normalTextColor)
        )
        setTextColor(textColorSelect)
    }

    override fun setEnabled(enabled: Boolean) {
        background = if (enabled) {
            selector
        } else { // 不可用状态
            ContextCompat.getDrawable(context, R.drawable.bg_btn_disabled)!!
        }
        super.setEnabled(enabled)
    }

    /**
     * 设置正常、按下状态 按钮的Drawable
     */
    fun setDrawables(normalDrawable: Drawable, pressedDrawable: Drawable) {
        this.normalDrawable = normalDrawable
        this.pressedDrawable = pressedDrawable
        if (theme != Theme.THEME_CUSTOM) {  // 如果不是自定义主题
            return
        }
        setTheme(theme)
    }


    /**
     * 设置主题
     */
    fun setTheme(theme: Theme) {
        this.theme = theme
        if (!isEnabled) return
        var normalBackgroundDrawable =
            ContextCompat.getDrawable(context, R.drawable.bg_btn_normal)!!
        var pressedBackgroundDrawable =
            ContextCompat.getDrawable(context, R.drawable.bg_btn_pressed)!!
        when (theme) {
            Theme.THEME_NORMAL -> {
                // 初始化背景 drawable
                normalBackgroundDrawable =
                    ContextCompat.getDrawable(context, R.drawable.bg_btn_normal)!!
                pressedBackgroundDrawable =
                    ContextCompat.getDrawable(context, R.drawable.bg_btn_pressed)!!
            }

            Theme.THEME_BLUE -> {
                normalBackgroundDrawable =
                    ContextCompat.getDrawable(context, R.drawable.bg_btn_normal_blue)!!
                pressedBackgroundDrawable =
                    ContextCompat.getDrawable(context, R.drawable.bg_btn_pressed_blue)!!
            }

            Theme.THEME_CUSTOM -> {
                normalBackgroundDrawable = normalDrawable ?: ContextCompat.getDrawable(
                    context,
                    R.drawable.bg_btn_normal
                )!!
                pressedBackgroundDrawable = pressedDrawable ?: ContextCompat.getDrawable(
                    context,
                    R.drawable.bg_btn_pressed
                )!!
            }
        }
        setBackGround(normalBackgroundDrawable, pressedBackgroundDrawable)
    }


    // 按钮状态
    companion object {
        const val STATE_NORMAL = 0
        const val STATE_PRESSED = 1
        const val STATE_DISABLED = 2
    }

    // 设置主题
    enum class Theme(theme: Int, name: String) {
        THEME_NORMAL(1, "normal"),  // 正常主题
        THEME_BLUE(2, "blue"), // 蓝色主题
        THEME_CUSTOM(3, "custom") // 自定义主题，此模式下需要自己通过代码设置backgroundDrawable
    }


}