package com.timekettle.upup.comm.base

import android.os.Bundle
import android.view.KeyEvent
import android.view.View
import android.view.ViewGroup
import android.widget.ImageView
import android.widget.TextView
import androidx.viewbinding.ViewBinding
import com.timekettle.upup.base.mvvm.v.BaseFrameActivity
import com.timekettle.upup.base.mvvm.vm.BaseViewModel
import com.timekettle.upup.base.utils.AndroidBugFixUtils
import com.timekettle.upup.base.utils.logI
import com.timekettle.upup.comm.R
import com.timekettle.upup.comm.webview.X5WebView

/**
 * WebViewActivity基类
 *
 * <AUTHOR>
 * @since 2022/04/15
 */
abstract class BaseWebActivity<VB : ViewBinding, VM : BaseViewModel> : BaseFrameActivity<VB, VM>() {

    protected var mWebView: X5WebView? = null
    var mTitleTv: TextView? = null
    var mTitleRightTv: TextView? = null

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
    }

    /**
     * 初始化顶部toolBar
     */
    override fun initToolBar() {
        findViewById<ImageView>(R.id.vBackIv)?.setOnClickListener { onBack() }
        mTitleTv = findViewById(R.id.vTitleTv)
        mTitleRightTv = findViewById(R.id.vTitleRightTv)
    }

    override fun onDestroy() {
        // 解决某些特定机型会触发的Android本身的Bug
        AndroidBugFixUtils().fixSoftInputLeaks(this)
        super.onDestroy()
        mWebView?.run {
            //加载null内容
            loadDataWithBaseURL(null, "", "text/html", "utf-8", null)
            //清除历史记录
            clearHistory()
            clearCache(true)
            clearFormData()
            //移除WebView
            (parent as ViewGroup).removeView(mWebView)
            destroy()
            mWebView = null
        }
    }

    override fun onKeyDown(keyCode: Int, event: KeyEvent?): Boolean {
        return if (keyCode == KeyEvent.KEYCODE_BACK && mWebView?.canGoBack() == true) {
            //返回键监听 回滚H5页面
            mWebView?.goBack()
            false
        } else {
            super.onKeyDown(keyCode, event)
        }
    }

    /**
     * 返回或关闭当前页，如果当前Web页面能够返回上一级就返回上一级，否则就关闭页面
     */
    private fun backOrFinish() {
        if (mWebView?.canGoBack() == true) {
            logI("canGoBack------------")
            mWebView?.goBack()
        } else {
            logI("onBack------------")
            onBack()
        }
    }

    protected open fun onBack(){
        finish()
    }
}