package com.timekettle.upup.comm.utils;

import android.util.Log;

import androidx.annotation.NonNull;

import com.blankj.utilcode.util.FileUtils;
import com.timekettle.upup.base.utils.SpUtils;

import org.jetbrains.annotations.NotNull;

import java.io.File;
import java.io.FileOutputStream;
import java.io.IOException;
import java.io.InputStream;

import okhttp3.Call;
import okhttp3.Callback;
import okhttp3.OkHttpClient;
import okhttp3.Request;
import okhttp3.Response;

/**
 * <AUTHOR>
 * @date 2022/10/11
 * @desc https://www.jianshu.com/p/3b269082cbbb
 */
public class DownloadUtil {

    private static DownloadUtil downloadUtil;
    private final OkHttpClient okHttpClient;
    private float curProgress = 0.0f;

    public static DownloadUtil get() {
        if (downloadUtil == null) {
            downloadUtil = new DownloadUtil();
        }
        return downloadUtil;
    }

    private DownloadUtil() {
        okHttpClient = new OkHttpClient();
    }

    /**
     * @param url      下载连接
     * @param saveDir  储存下载文件的SDCard目录
     * @param listener 下载监听
     */
    public void download(final String url, final String saveDir, final OnDownloadListener listener) {
        Request request = null;
        try {
            request = new Request.Builder().url(url).build();
        } catch (Exception e) {
            Log.e("DownloadUtil", "request初始化失败：" + e);
        }
        if (request == null) {
            listener.onDownloadFailed(new Exception("request初始化失败!!"));
            return;
        }
        okHttpClient.newCall(request).enqueue(new Callback() {
            @Override
            public void onFailure(Call call, IOException e) {
                listener.onDownloadFailed(e);
                curProgress = 0.0f;
            }

            @Override
            public void onResponse(Call call, Response response) throws IOException {
                InputStream is = null;
                byte[] buf = new byte[2048];
                int len = 0;
                FileOutputStream fos = null;
                // 储存下载文件的目录
                FileUtils.createOrExistsDir(saveDir);
                try {
                    is = response.body().byteStream();
                    long total = response.body().contentLength();
                    File file = new File(saveDir, getNameFromUrl(url));
                    fos = new FileOutputStream(file);
                    long sum = 0;
                    while ((len = is.read(buf)) != -1) {
                        fos.write(buf, 0, len);
                        sum += len;
                        float progress = (sum * 1.0f / total * 100);
                        // 下载中
                        if (progress - curProgress >= 0.2) { // 进度>%0.2的时候回调一次
                            curProgress = progress;
                            listener.onDownloading(url, (int) progress, sum);
                        }
                    }
                    fos.flush();
                    // 下载完成
                    curProgress = 0.0f;
                    listener.onDownloadSuccess(url, file.getAbsolutePath());
                } catch (Exception e) {
                    listener.onDownloadFailed(e);
                    curProgress = 0.0f;
                } finally {
                    try {
                        if (is != null) {
                            is.close();
                        }
                    } catch (IOException e) {
                    }
                    try {
                        if (fos != null) {
                            fos.close();
                        }
                    } catch (IOException e) {
                    }
                }
            }
        });
    }

    /**
     * 取消下载，调用此方法会回调一次onFail
     */
    public void cancel() {
        try {
            for (Call call : okHttpClient.dispatcher().runningCalls()) {
                call.cancel();
            }
            curProgress = 0;
        } catch (Exception e) {

        }

    }


    /**
     * @param url
     * @return 从下载连接中解析出文件名
     */
    @NonNull
    private String getNameFromUrl(String url) {
        return url.substring(url.lastIndexOf("/") + 1);
    }

    public interface OnDownloadListener {
        void onDownloadSuccess(String url, String filePath);

        /**
         * @param progress 下载进度,1-100
         */
        void onDownloading(String url, int progress, Long size);

        void onDownloadFailed(@NotNull Exception e);
    }

    // 如果某个链接下载完成了，就把它在本地保存起来
    public static void setUpgradeFileDownloadFinish(String link) {
        if (link == null || link.isEmpty()) return;
        SpUtils.INSTANCE.put(link, true);
    }

    // 看看这个链接是否有下载完成
    public static boolean isUpgradeFileDownloadFinish(String link) {
        return SpUtils.INSTANCE.getBoolean(link, false);
    }


}