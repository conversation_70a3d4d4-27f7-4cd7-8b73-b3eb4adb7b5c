package com.timekettle.upup.comm.tools

import android.content.Context
import com.google.gson.Gson
import com.google.gson.reflect.TypeToken
import com.timekettle.upup.base.BaseApp
import com.timekettle.upup.base.utils.logD
import com.timekettle.upup.base.utils.logV
import com.timekettle.upup.comm.R
import com.timekettle.upup.comm.bean.LanguageJsonBeanParent
import com.timekettle.upup.comm.utils.LanguageUtil
import java.util.Locale

/**
 * <AUTHOR>
 * @date 2022/11/7 10:38
 * @email <EMAIL>
 * @desc 和翻译模块逻辑相关的语言工具类
 */
object TransLanguageTool {

    private val gson = Gson()
    private val TAG = TransLanguageTool::class.java.simpleName
    private var languageBeanList: ArrayList<LanguageJsonBeanParent> = arrayListOf()


    private val lanListFromAssets by lazy {
        readLanguageFromAssets(BaseApp.context)
    }

    fun getLanguageBeanList(json: String?): ArrayList<LanguageJsonBeanParent> {
        val type = object : TypeToken<ArrayList<LanguageJsonBeanParent?>?>() {}.type
        languageBeanList = gson.fromJson(json, type)
        return languageBeanList
    }


    fun readLanguageFromAssets(context: Context): ArrayList<LanguageJsonBeanParent> {
        // String languageJson = getFromAssets(context, "language_zh_cn.json");
        // 原理：先在翻译模块的国际化文件中，根据“language_file” 这个key，拿到国际化文件名称，例如中国的就是“language_zh_cn.json”
        // 然后根据这个文件名称，去assets文件夹下面读取对应的json文件，并且读取JSON文件内容
        var languageBeanList = ArrayList<LanguageJsonBeanParent>()
        logV("读取本地配置文件 开始", TAG)
        val jsonAssetsFileName = "language/" + getJsonFileByLan()
        val languageJson = getFromAssets(context, jsonAssetsFileName)
        if (languageJson.isEmpty()) return languageBeanList
        languageBeanList = getLanguageBeanList(languageJson)
        logV("读取本地配置文件结束，实体列表: $languageBeanList", TAG)
        return languageBeanList
    }

    fun getJsonFileByLan(): String {
        return when (Locale.getDefault().language) {
            "zh" -> if (LanguageUtil.isSimpleZh()) "language_zh_cn.json" else "language_zh_tw.json"
            "cs" -> "language_cs.json"
            "de" -> "language_de.json"
            "en" -> "language_en.json"
            "es" -> "language_es.json"
            "fr" -> "language_fr.json"
            "it" -> "language_it.json"
            "ja" -> "language_ja.json"
            "ko" -> "language_ko.json"
            "pt" -> "language_pt.json"
            "ru" -> "language_ru.json"
            "sk" -> "language_sk.json"
            "sl" -> "language_sl.json"
            "th" -> "language_th.json"
            "uk" -> "language_uk.json"
            "tr" -> "language_tr.json"
            else -> "language.json"
        }
    }


    // 根据code获取语种的的显示名称（大语种，example：中文）
    fun getLanguageNameByCode(code: String?): String {
        for (parent in readLanguageFromAssets(BaseApp.context)) {
            if (parent.code == code) {
                return parent.title
            }
            for (child in parent.data) {
                if (child.code == code) {
                    return parent.title
                }
            }
        }
        return ""
    }

    // 根据code获取大语种的的index
    fun getLanguageIndexByCode(code: String?): Int? {
        for (parent in lanListFromAssets) {
            if (parent.code == code) {
                return lanListFromAssets.indexOf(parent)
            }
            for (child in parent.data) {
                if (child.code == code) {
                    return lanListFromAssets.indexOf(parent)
                }
            }
        }
        return null
    }

    // 根据parent的position和code获取口音的的index
    fun getAccentIndexByCode(langPosition: Int, code: String?): Int? {
        for (index in lanListFromAssets.indices) {
            if (langPosition == index) {
                var parent = lanListFromAssets[langPosition]
                for (child in parent.data) {
                    if (child.code == code) {
                        return parent.data.indexOf(child)
                    }
                }
            }
        }
        return null
    }

    // 根据code获取语种的完整显示名称（example：中文(简体)）
    fun getFullLanguageName(code: String?): String {
        for (parent in lanListFromAssets) {
            if (parent.code == code) {
                return parent.title
            }
            for (child in parent.data) {
                if (child.code == code) {
                    return "${parent.title}(${child.language})"
                }
            }
        }
        return ""
    }


    private fun getFromAssets(context: Context, fileName: String): String {
        try {
            return context.assets.open(fileName).bufferedReader().use { it.readText() }
        } catch (e: Exception) {
            e.printStackTrace()
        }
        return ""
    }

    fun getDefaultOtherLan(): String {
        val locale: Locale = Locale.getDefault();
        // locale： “zh_CN”
        // locale.language : "zh"
        // locale.languageTag : "zh-CN"
        val defaultLang = locale.toLanguageTag()
        logD("本地语言码Tag $defaultLang")
        // 如果不支持的语种，默认是英->中，所以self默认是英文
        return if (defaultLang.indexOf("en") != -1) { // 手机系统语言是英语，那么就到中文
            "zh-CN";
        } else { // 否则一律是翻译到英语
            "en-US";
        }
    }

    fun getDefaultSelfLan(): String {
        val locale: Locale = Locale.getDefault();
        // locale： “zh_CN”
        // locale.language : "zh"
        // locale.languageTag : "zh-CN"
        val defaultLang = locale.toLanguageTag()
        logD("getDefaultSelfLan: 本地语言码T=$defaultLang", "TransLanguageTool")
        var fromLang = "en-US"; // 如果不支持的语种，默认是英->中，所以self默认是英文
        if (defaultLang.indexOf("zh") != -1 && defaultLang.indexOf("GB") != -1) {
            fromLang = "zh-CN";
        } else if (defaultLang.indexOf("zh") != -1 && defaultLang.indexOf("CN") != -1) {
            fromLang = "zh-CN";
        } else if (defaultLang.indexOf("zh") != -1 && defaultLang.indexOf("TW") != -1) {
            fromLang = "zh-TW";
        } else if (defaultLang.indexOf("zh") != -1 && defaultLang.indexOf("HK") != -1) {
            fromLang = "zh-HK";
        } else if (defaultLang.indexOf("zh") != -1 && defaultLang.indexOf("MO") != -1) {
            fromLang = "zh-TW";
        } else if (defaultLang.indexOf("zh") != -1 && defaultLang.indexOf("Hans") != -1) {
            //荣耀V10手机是zh-Hans-ES, iPhone8是zh-Hans-GB
            fromLang = "zh-CN";
        } else if (defaultLang.indexOf("ja") != -1) {
            fromLang = "ja-JP";
        } else if (defaultLang.indexOf("en") != -1) {
            fromLang = "en-US";
        } else if (defaultLang.indexOf("cs") != -1) {
            fromLang = "cs-CZ";
        } else if (defaultLang.indexOf("de") != -1) {
            fromLang = "de-DE";
        } else if (defaultLang.indexOf("es") != -1) {
            fromLang = "es-MX";
        } else if (defaultLang.indexOf("fr") != -1) {
            fromLang = "fr-FR";
        } else if (defaultLang.indexOf("it") != -1) {
            fromLang = "it-IT";
        } else if (defaultLang.indexOf("pt") != -1) {
            fromLang = "pt-PT";
        } else if (defaultLang.indexOf("ru") != -1) {
            fromLang = "ru-RU";
        } else if (defaultLang.indexOf("sk") != -1) {
            fromLang = "sk-SK";
        } else if (defaultLang.indexOf("sl") != -1) {
            fromLang = "sl-SI";
        } else if (defaultLang.indexOf("ko") != -1) {
            fromLang = "ko-KR";
        } else if (defaultLang.indexOf("th") != -1) {
            fromLang = "th-TH";
        } else if (defaultLang.indexOf("vi") != -1) {
            fromLang = "vi-VN";
        } else if (defaultLang.indexOf("ur") != -1) {
            fromLang = "ur-PK"; //选用第一个
        } else if (defaultLang.indexOf("uk") != -1) {
            fromLang = "uk-UA";
        } else if (defaultLang.indexOf("tr") != -1) {
            fromLang = "tr-TR";
        } else if (defaultLang.indexOf("te") != -1) {
            fromLang = "te-IN";
        } else if (defaultLang.indexOf("ta") != -1) {
            fromLang = "ta-IN";
        } else if (defaultLang.indexOf("sv") != -1) {
            fromLang = "sv-SE";
        } else if (defaultLang.indexOf("ro") != -1) {
            fromLang = "ro-RO";
        } else if (defaultLang.indexOf("pl") != -1) {
            fromLang = "pl-PL";
        } else if (defaultLang.indexOf("nl") != -1) {
            fromLang = "nl-NL";
        } else if (defaultLang.indexOf("nb") != -1) {
            fromLang = "nb-NO";
        } else if (defaultLang.indexOf("ms") != -1) {
            fromLang = "ms-MY";
        } else if (defaultLang.indexOf("is") != -1) {
            fromLang = "is-IS";
        } else if (defaultLang.indexOf("id") != -1) {
            fromLang = "id-ID";
        } else if (defaultLang.indexOf("hu") != -1) {
            fromLang = "hu-HU";
        } else if (defaultLang.indexOf("hr") != -1) {
            fromLang = "hr-HR";
        } else if (defaultLang.indexOf("hi") != -1) {
            fromLang = "hi-IN";
        } else if (defaultLang.indexOf("he") != -1) {
            fromLang = "he-IL";
        } else if (defaultLang.indexOf("fil") != -1) {
            fromLang = "fil-PH";
        } else if (defaultLang.indexOf("fi") != -1) {
            fromLang = "fi-FI";
        } else if (defaultLang.indexOf("fa") != -1) {
            fromLang = "fa-IR";
        } else if (defaultLang.indexOf("el") != -1) {
            fromLang = "el-GR";
        } else if (defaultLang.indexOf("da") != -1) {
            fromLang = "da-DK";
        } else if (defaultLang.indexOf("ca") != -1) {
            fromLang = "ca-ES";
        } else if (defaultLang.indexOf("bg") != -1) {
            fromLang = "bg-BG";
        } else if (defaultLang.indexOf("ar") != -1) {
            fromLang = "ar-EG";
        }
        return fromLang
    }


}