package com.timekettle.upup.comm.utils

import android.annotation.SuppressLint
import android.bluetooth.BluetoothAdapter
import android.content.Context
import android.os.Handler
import android.os.Looper
import co.timekettle.btkit.BleUtil
import com.timekettle.upup.base.utils.logD
import com.timekettle.upup.base.utils.logE
import java.lang.ref.WeakReference

// 蓝牙工具类
object BtUtil {

    private var isResetting = false
    val handler = Handler(Looper.getMainLooper())
    val TAG = "蓝牙工具类"

    // 重置蓝牙（关闭蓝牙之后又开启蓝牙）
    @SuppressLint("MissingPermission")
    fun resetBluetooth() {
        val bluetoothAdapter: BluetoothAdapter = BluetoothAdapter.getDefaultAdapter()
        if (isResetting) {
            return
        }
        isResetting = true
        // Disable Bluetooth
        bluetoothAdapter.disable()
        handler.postDelayed({
            // Enable Bluetooth
            bluetoothAdapter.enable()
            isResetting = false
        }, 3000) // Adjust the delay as necessary
    }


    // 静默打开系统蓝牙（无弹窗）
    @SuppressLint("MissingPermission")
    fun openBluetoothBySilence() {
        val bluetoothAdapter: BluetoothAdapter = BluetoothAdapter.getDefaultAdapter()
        if (!bluetoothAdapter.isEnabled){
            logD("静默打开系统蓝牙...", TAG)
            bluetoothAdapter.enable()
        }
    }

    // 判断蓝牙是否开启
    fun isBluetoothEnabled():Boolean{
        val bluetoothAdapter: BluetoothAdapter = BluetoothAdapter.getDefaultAdapter()
        return bluetoothAdapter.isEnabled
    }

    @Deprecated("这个方法用了没作用")
    fun cleanBtCache(headsetKey: String) {
        logD("尝试调用clean方法...key：$headsetKey")
        try {
            val mBluetoothGatt = BleUtil.shared.getDeviceGatt(headsetKey)
            if (mBluetoothGatt == null) {
                logE("尝试调用clean方法，mBluetoothGatt为空！！！返回")
                return
            }
            val localMethod = mBluetoothGatt.javaClass.getMethod("refresh")
            localMethod.invoke(mBluetoothGatt)
            logE("调用clean方法成功！！！")
        } catch (e: Exception) {
            logE("调用clean方法失败！！！")
            e.printStackTrace()
        }
    }
}