package com.timekettle.upup.comm.utils

/**
 * DongleVersionUtil Dongle版本工具类
 *
 * @author: <PERSON><PERSON><PERSON>
 * @date: 2024/7/29
 */
object DongleVersionUtil {
//    const val DONGLE_FILE_PATH = "dongle/dongle1m_hid1_tws10_30.fot"
    const val DONGLE_FILE_PATH = "dongle/X1_dongle_31.fot"

    fun versionEquals(input1: String, input2: String): <PERSON><PERSON><PERSON> {
        // 提取 input1 中最后的数字
        val regex1 = "_(\\d+)\\.fot$".toRegex()
        val matchResult1 = regex1.find(input1)
        val number1 = matchResult1?.groups?.get(1)?.value?.toIntOrNull()

        // 提取 input2 中的版本号并转换为整数
        val regex2 = "version_(\\d+)\\.(\\d+)".toRegex()
        val matchResult2 = regex2.find(input2)
        val number2 = matchResult2?.let {
            val major = it.groups[1]?.value?.toIntOrNull() ?: 0
            val minor = it.groups[2]?.value?.toIntOrNull() ?: 0
            major * 10 + minor
        }
        return number1 != null && number2 != null && number1 == number2
    }
}