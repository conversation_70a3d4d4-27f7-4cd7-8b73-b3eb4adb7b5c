package com.timekettle.upup.comm.net.helper;

import com.alibaba.fastjson.JSON;
import com.blankj.utilcode.util.EncodeUtils;
import com.blankj.utilcode.util.StringUtils;


import java.security.*;
import java.security.spec.PKCS8EncodedKeySpec;
import java.security.spec.X509EncodedKeySpec;
import java.util.*;


/**
 * 签名验签工具类
 *
 * <AUTHOR>
 */
public class SignUtils {

    private static final String AND_CHAR = "&";
    private static final String EQUAL_CHAR = "=";

    private SignUtils() {
        throw new IllegalStateException("Utility class");
    }


    /**
     * 签名
     *
     * @param content    文本
     * @param privateKey 私钥
     * @return the base64 String of the signing operation's result.
     * @throws GeneralSecurityException
     */
    public static String sign(String content, String privateKey) throws GeneralSecurityException {
        Signature signature = Signature.getInstance("MD5withRSA");

        PKCS8EncodedKeySpec pkcs8EncodedKeySpec = null;
        pkcs8EncodedKeySpec = new PKCS8EncodedKeySpec(EncodeUtils.base64Decode(privateKey));
        KeyFactory keyFactory = KeyFactory.getInstance("RSA");
        PrivateKey key = keyFactory.generatePrivate(pkcs8EncodedKeySpec);

        signature.initSign(key);
        signature.update(content.getBytes());
        //签名
        byte[] sign = signature.sign();
        return EncodeUtils.base64Encode2String(sign);
    }

    /**
     * 验签
     *
     * @param content      签名文本
     * @param terminalSign 签名
     * @param publicKey    公钥
     * @return true if the signature was verified, false if not.
     * @throws GeneralSecurityException exception
     */
    public static boolean verify(String content, String terminalSign, String publicKey) throws GeneralSecurityException {
        Signature signature = Signature.getInstance("MD5withRSA");

        X509EncodedKeySpec x509EncodedKeySpec = new X509EncodedKeySpec(EncodeUtils.base64Decode(publicKey));
        KeyFactory keyFactory = KeyFactory.getInstance("RSA");
        PublicKey key = keyFactory.generatePublic(x509EncodedKeySpec);

        signature.initVerify(key);
        signature.update(content.getBytes());
        return signature.verify(EncodeUtils.base64Decode(terminalSign));
    }

    public static String toSignedContent(Map<String, Object> signParam) {
        // 参数按key排序
        String[] keys = signParam.keySet().toArray(new String[0]);
        Arrays.sort(keys);

        // 参数按 key1=value1&key2=value2 格式拼接
        StringBuilder stringBuilder = new StringBuilder();
        for (String key : keys) {
            Object o = signParam.get(key);
            // value为空、null等值不参与生成签名
            if (Objects.isNull(o) || StringUtils.isEmpty(String.valueOf(o))) {
                continue;
            }

            stringBuilder.append(key).append(EQUAL_CHAR).append(String.valueOf(o).trim()).append(AND_CHAR);
        }
        String joinParam = stringBuilder.toString();
        if (joinParam.endsWith(AND_CHAR)) {
            joinParam = joinParam.substring(0, joinParam.length() - 1);
        }
        return joinParam;
    }

    /**
     * 获取待签名的字符串
     *
     * @param request 请求
     * @return 待签名字符
     */
//    private static String toSignedContent(HttpServletRequest request) {
//        // 获取查询参数
//        Map<String, Object> queryParam = getQueryParam(request);
//        Map<String, Object> commonParam = new HashMap<>(3);
//        commonParam.put("snCode", request.getHeader(DEVICE_CODE_KEY));
//        commonParam.put("apiPath", request.getRequestURI());
//        commonParam.put("timestamp", request.getHeader(TIMESTAMP_KEY));
//
//        Map<String, Object> signParam = new HashMap<>(12);
//        signParam.putAll(commonParam);
//
//        if (!queryParam.isEmpty()) {
//            signParam.putAll(queryParam);
//        }
//        return toSignedContent(signParam);
//    }

}
