package com.timekettle.upup.comm.model

import android.os.Parcel
import android.os.Parcelable

/**
 * 我的模块的缓存数据
 * @author: licoba
 * @date: 2022/6/24
 */
data class MineBean (
    var balance: Double = 0.00,
    val customTransCount:Int = 0
): Parcelable {
    constructor(parcel: Parcel) : this(
        parcel.readDouble(),
        parcel.readInt()
    ) {
    }

    override fun writeToParcel(parcel: Parcel, flags: Int) {
        parcel.writeDouble(balance)
        parcel.writeInt(customTransCount)
    }

    override fun describeContents(): Int {
        return 0
    }

    companion object CREATOR : Parcelable.Creator<MineBean> {
        override fun createFromParcel(parcel: Parcel): MineBean {
            return MineBean(parcel)
        }

        override fun newArray(size: Int): Array<MineBean?> {
            return arrayOfNulls(size)
        }
    }


}