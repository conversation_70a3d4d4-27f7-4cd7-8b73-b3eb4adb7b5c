package com.timekettle.upup.comm.ui

import androidx.activity.viewModels
import androidx.lifecycle.lifecycleScope
import com.timekettle.upup.base.utils.showToast
import com.timekettle.upup.comm.base.BaseActivity
import com.timekettle.upup.comm.databinding.CommActivityNetTestBinding
import com.timekettle.upup.comm.viewmodel.VMNetTest
import dagger.hilt.android.AndroidEntryPoint
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.flow.launchIn
import kotlinx.coroutines.flow.onEach

/**
 * 延迟检测页
 *
 * @author: Pengwei Wang
 * @date: 2024/8/14
 */
@AndroidEntryPoint
class CommNetTestActivity : BaseActivity<CommActivityNetTestBinding, VMNetTest>() {
    override val mViewModel: VMNetTest by viewModels()
    override fun initObserve() {
        mViewModel.checkStatus
            .asStateFlow()
            .onEach {
                changeCheckState(it)
            }
            .launchIn(lifecycleScope)

        mViewModel.logs.onEach {
            mBinding.tvCheckLog.text = it
            mBinding.tvCheckLog.post {
                mBinding.svLog.smoothScrollTo(0, mBinding.tvCheckLog.bottom)
            }
        }.launchIn(lifecycleScope)
    }

    override fun initRequestData() {

    }

    override fun CommActivityNetTestBinding.initView() {
        mViewModel.doInit()
        vTitleBar.run {
            vTitleTv.text = "延迟检测"
        }
    }

    override fun initListener() {
        mBinding.btnCheckStart.setOnClickListener {
            mViewModel.startCheck()
        }
        mBinding.btnCheckStop.setOnClickListener {
            mViewModel.stopCheck()
        }
    }

    private fun changeCheckState(checkStatus: VMNetTest.CheckStatus) {
        when (checkStatus) {
            VMNetTest.CheckStatus.None -> {
                mBinding.btnCheckStart.isEnabled = true
                mBinding.btnCheckStop.isEnabled = false
            }
            VMNetTest.CheckStatus.Checking -> {
                mBinding.btnCheckStart.isEnabled = false
                mBinding.btnCheckStop.isEnabled = true
                showToast("检测中")
            }
            VMNetTest.CheckStatus.Error -> {
                mBinding.btnCheckStart.isEnabled = true
                mBinding.btnCheckStop.isEnabled = false
            }
            VMNetTest.CheckStatus.Finished -> {
                mBinding.btnCheckStart.isEnabled = true
                mBinding.btnCheckStop.isEnabled = false
            }
        }
    }
}