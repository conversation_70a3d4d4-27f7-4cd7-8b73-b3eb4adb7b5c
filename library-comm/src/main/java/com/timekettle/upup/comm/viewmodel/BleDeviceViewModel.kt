package com.timekettle.upup.comm.viewmodel

import androidx.lifecycle.MutableLiveData
import co.timekettle.btkit.BleUtil
import co.timekettle.btkit.bean.RawBlePeripheral
import co.timekettle.btkit.bean.WT2BlePeripheral
import com.timekettle.upup.base.mvvm.vm.BaseViewModel
import com.timekettle.upup.base.utils.logD
import com.timekettle.upup.base.utils.logE
import com.timekettle.upup.comm.bean.BleDeviceBean
import com.timekettle.upup.comm.service.home.HomeServiceImplWrap
import com.timekettle.upup.comm.tools.DeviceManager
import com.timekettle.upup.comm.tools.DeviceTool
import com.timekettle.upup.comm.utils.HallUtil
import com.tmk.libserialhelper.serialport.TmkSerialHelper

/**
 * BaseBleViewModel 用来在HeaderView和BottomView之间共享，
 * <只和BLE相关>
 * @author: licoba
 * @date: 2022/7/26
 * 这个会被用来作为全局的Viewmodel
 */
abstract class BleDeviceViewModel : BaseViewModel() {



    abstract fun refreshDeviceList()



    override fun onCleared() {
        super.onCleared()
    }

}