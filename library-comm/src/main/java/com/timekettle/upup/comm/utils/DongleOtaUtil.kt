package com.timekettle.upup.comm.utils

import android.bluetooth.BluetoothDevice
import android.content.Context
import android.util.Log
import co.timekettle.btkit.sample.FileUtils
import com.bluetrum.fota.bluetooth.OtaManager
import com.bluetrum.fota.bluetooth.SppOtaManager
import com.timekettle.upup.base.utils.logE
import kotlinx.coroutines.delay
import java.io.BufferedWriter
import java.io.FileWriter
import java.io.IOException


/**
 * 封装的Dongle OTA升级相关的工具类
 */
object DongleOtaUtil {
    const val TAG: String = "DongleOtaUtil升级"
    private var otaManager: OtaManager? = null

    /**
     * 根据Mac地址寻找设备，并且建立连接
     *
     * @param context    Context
     * @param device dongle设备
     */
    fun init(context: Context, device: BluetoothDevice, eventListener: OtaManager.EventListener) {
        otaManager = SppOtaManager(context, device, eventListener)
        otaManager?.init()
        // init方法初始化蓝牙连接，如果出现错误，将会在 onOtaError(OtaError error)回调中报错，如果顺利，则不会出现任何错误。
    }


    /**
     * 设置OTA文件的升级路径
     * note:蓝讯原厂FOT文件不包含固件信息，可自行进行封装，读取时自行解析，并设置固件版本用于版本判断
     *
     * @param filePath
     */
    fun setFilePath(context: Context, filePath: String) {
        // 读取文件在库外部进行处理
        try {
            val otaData: ByteArray? = getFileFromAssets(context, filePath)
            if (otaData == null) {
                Log.e(TAG, "升级文件读取为空")
                return
            }
            otaManager?.setOtaData(otaData)
        } catch (e: IOException) {
            Log.e(TAG, "升级文件读取出错", e)
        }
        // 0xFFFF表示固件端一定会升级，可根据要求自行修改
        otaManager?.setOtaFirmwareVersion(0xFFFF)
    }

    private fun getFileFromAssets(context: Context, filePath: String): ByteArray? {
        return try {
            context.assets.open(filePath).use {
                // 如果能打开文件，表示文件存在
                it.readBytes()
            }
        } catch (e: IOException) {
            // 如果抛出 IOException，表示文件不存在
            logE("文件不存在")
            null
        }
    }

    fun startDongleOta() {
        // 根据文档注释 （开始进行OTA升级，升级之前必须先判断OtaManager.isReadyToUpdate()。）
        if (otaManager?.isReadyToUpdate == true) {
            otaManager?.startOTA()
        } else {
            Log.e(TAG, "还没有准备好，请检查一下条件是否满足")
        }
    }

    fun releaseDongleOta() {
        if (otaManager != null) {
            otaManager?.release()
            otaManager = null
        }
    }

    /**
     * 写文件节点 打开 dongle 功能
     */
    fun setDongleOn(): Boolean {
        val dataStr = "1"
        val point1 = "/sys/devices/platform/bt_hp_ctrl/bt_dongle_on"
        val point2 = "/sys/devices/platform/otg_iddig/otg_sw_on"
        try {
            val bufferWriter = BufferedWriter(FileWriter(point1))
            bufferWriter.write(dataStr)
            bufferWriter.close()
            val bufferWriter2 = BufferedWriter(FileWriter(point2))
            bufferWriter2.write(dataStr)
            bufferWriter2.close()
        } catch (e: Exception) {
            Log.e(TAG,e.toString())
            return false
        }
        return true
    }

    /**
     * 写文件节点 关闭 dongle 功能
     */
    fun setDongleOff(): Boolean {
        val dataStr = "0"
        val point1 = "/sys/devices/platform/bt_hp_ctrl/bt_dongle_on"
        val point2 = "/sys/devices/platform/otg_iddig/otg_sw_on"
        try {
            val bufferWriter = BufferedWriter(FileWriter(point1))
            bufferWriter.write(dataStr)
            bufferWriter.close()
            val bufferWriter2 = BufferedWriter(FileWriter(point2))
            bufferWriter2.write(dataStr)
            bufferWriter2.close()
        } catch (e: Exception) {
            Log.e(TAG,e.toString())
            return false
        }
        return true
    }

    suspend fun reopenDongle() {
        delay(500)
        setDongleOff()
        delay(500)
        setDongleOn()
    }
}
