package com.timekettle.upup.comm.utils

import android.app.Activity
import com.blankj.utilcode.util.ActivityUtils

/**
 * App内的Activity工具类
 */
object MyActivityUtil {

    private const val HOME_ACTIVITY_NAME = "HomeActivityMain"
    private var isUpgrade = false   // 需要在进入/退出升级的时候，手动设置这个状态

    fun getTopActivityName(): String {
        return try {
            ActivityUtils.getTopActivity().javaClass.simpleName
        } catch (e: Exception) {
            ""
        }
    }

    // 当前的Activity是否需要显示升级弹窗
    fun canShowUpgradeDialog(): Boolean {
        if (isUpgradingAty()) return false
        return isHomeActivity()
    }


    // 是否正在升级界面
    fun isUpgradingAty(): Boolean {
        return isUpgrade
    }

    fun setIsUpgradeState(b: Boolean) {
        isUpgrade = b
    }


    fun isHomeActivity(): Boolean {
        return getTopActivityName() == HOME_ACTIVITY_NAME
    }


    // 判断是否是正在翻译页面使用
    fun isInTransUsing(): Boolean {
        val activityNames = listOf(
            "PhoneActivityComing",// 正在来电，不允许升级
            "MeetingActivityMain",// 正在会议页面，不允许升级
            "InterviewActivityMain",// 正在会议页面，不允许升级
            "KeypadActivityMain",// 正在会议页面，不允许升级
            "PhoneActivityMain",// 正在会议页面，不允许升级
            "TranslateActivityMain",// 正在会议页面，不允许升级
            "TranslateActivitySetting",// 正在会议页面，不允许升级
            "MeetingChooseLangActivity",// 正在会议页面，不允许升级
            "MeetingChooseAccentActivity",// 正在会议页面，不允许升级
            "ChooseAccentActivity",// 正在会议页面，不允许升级
            "ChooseLangActivity",// 正在会议页面，不允许升级
        )
        return activityNames.contains(getTopActivityName())
    }

    fun isTmkActivity(aty: Activity?): Boolean {
        if (aty == null) return false
        return aty.localClassName.contains("timekettle")
    }

}