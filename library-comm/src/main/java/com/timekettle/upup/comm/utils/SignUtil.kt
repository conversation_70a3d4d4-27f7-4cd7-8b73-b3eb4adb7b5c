package com.timekettle.upup.comm.utils

import android.util.Base64
import java.security.KeyFactory
import java.security.Signature
import java.security.spec.PKCS8EncodedKeySpec
import java.util.Arrays
import java.util.Objects

object SignUtil {

    fun sign(content: String, privateKey: String): String {
        val signature = Signature.getInstance("SHA256withRSA")
        val pkcs8EncodedKeySpec: PKCS8EncodedKeySpec?
        pkcs8EncodedKeySpec = PKCS8EncodedKeySpec(base64Decode(privateKey))
        val keyFactory = KeyFactory.getInstance("RSA")
        val key = keyFactory.generatePrivate(pkcs8EncodedKeySpec)
        signature.initSign(key)
        signature.update(content.toByteArray())
        //签名
        val sign = signature.sign()
        return base64Encode2String(sign)
    }

    private fun base64Decode(input: String): ByteArray {
        return if (input.isEmpty()) ByteArray(0) else Base64.decode(input, Base64.NO_WRAP)
    }

    private fun base64Encode2String(input: ByteArray): String {
        return if (input.isEmpty()) "" else Base64.encodeToString(input, Base64.NO_WRAP)
    }

    fun toSignedContent(signParam: Map<String, Any?>): String {
        // 参数按key排序
        val keys = signParam.keys.toTypedArray()
        Arrays.sort(keys)

        // 参数按 key1=value1&key2=value2 格式拼接
        val stringBuilder = StringBuilder()
        for (key in keys) {
            val o = signParam[key]
            // value为空、null等值不参与生成签名
            if (Objects.isNull(o) || o.toString().isEmpty()) {
                continue
            }
            stringBuilder.append(key).append(EQUAL_CHAR).append(o.toString().trim { it <= ' ' })
                .append(
                    AND_CHAR
                )
        }
        var joinParam = stringBuilder.toString()
        if (joinParam.endsWith(AND_CHAR)) {
            joinParam = joinParam.substring(0, joinParam.length - 1)
        }
        return joinParam
    }

    private const val AND_CHAR = "&"
    private const val EQUAL_CHAR = "="

    val PRIVATE_KEY_RELEASE = """
 MIIEowIBAAKCAQEAt9hPWSWIavxVsIsEP2z/jH4PpOKDmoi701ngKuVwF/MEQA2l
 GLIi6MhLvbjMytyk1QvUDtL+/yvbXQ/k7nyCy058R2QU7Nt2xaZaoy4oBEZEjy/M
 uJNkgKEHBlo0rg6LICx40nDYO07JJqpOTQlEBuKaQ8Jxridv6nlKPJLQZ61eHTo4
 pGt7iA+38HWzo/3S1pwXHLShN6P9SnLsy0K8ksOXe0SByTxhNntmO6Qb7bZl4CTZ
 jlnFRxlpXLHqAXRBX4Sh1oqK0Zzu+8tgOhSPp2B0D14oE36kBQ4h9yLNjTgxhuM6
 /7qLQQFKX+d+YNiqDLDkkqtu9hZU7pszrvvDXwIDAQABAoIBAEQofNp3EMlhPb+S
 +oMtPNzwS4p8s//R25iyxGoC3/jxDs1UgbXTLSASSgpPfhVjkfSuSvMoBIPD1bL9
 1okYPkFzmp25HAzoHHOQhh/jV9Cl4HsZZJO1iVgq2vje7pLtL2VBN5YFYhUy015z
 W04yNjc7QagHabAATPyn9P7aC0u0GMiBfeWh+ilYEzk820I9T/yOJ5vYDL9GKrKl
 R7Wiii9lXGMsZAsYKw2uCNamUDKU08eiY/eiNK6MHvO8D4vK0nYy1h7/DipWL+nI
 C0/aQba5QpxH5c+wDWpr6Lav3K5A3EAQK0OGdXrqpLTnkhaLS8yMlWuft41b7Vp2
 J0qSweECgYEA2hkN+t5zq1vPi/mFAe1OWVvnxJ4Qc7Ku863dnB2IJZcfrZgiC9H+
 4LO/NHhg04xp+DS9Uho8yXaICGytwenClzcZkZZeYX/wcJFgojfHdmJS6+SB6gCy
 mHD7Kfw/X5eTZHUr8WFrWZlcJsKyVGlVLC9ADPj4CnE2HGA26BZdQBECgYEA18th
 Hl+2+Z6Dnng4Mt3HkZEPlfVg1Y8gJv0gn0JUdqiI7re/oNJ/fTmXYr0bqEr+NAf+
 1ZRvy6U792BHuQqthXBQjQM1LcLfM8mc2g7CdNSJRDaXpntW5tq/f7O9ykGSscU/
 8I71/mcGVxwSdR71GcsE83TFB03EHPfYFMH5PG8CgYEAlngYtA8LoJrsGzdgM48k
 sfP9eCJRWXFa0NYQgefCCaU0/tBqFNuhzCdW8H1o2CsU5WCFiYJ+k8QKYD2St6lu
 TmN+aXpjzUK2gQNbty9iluOJMCRsGSS4EwJH1pSoqGvsDa/lTFiY9q0e7cL/w8sU
 LzK+LvUKNDIdUgBzgTYEyfECgYAZZLVo/ZpqhVy3qS3zzmVNm+RZoSZj+xOtgtvz
 4bpOvEZg0P6bzilycvR9igNXKnv/Y3F5cJ43OJp14b6O59LDgRP5hAcd+3CIh5EN
 l+8KEHiWrlXqIVl4kQU34c7TniPFB5EenbQU28rDNWp2nc352woaHAOTeHM7dW7Y
 AntxwQKBgHmpom/GcH5RqkitXoWvWsV/ytXvbzu/UAjiI5p46WcKHiPBd2XNv7JZ
 B5fXP9FcfkQB0Kl3SXOxMjm//yFEgHirqif0oPbQXXqPs8nnVnqYDlhdZsjZ923L
 Oe0BwgKAYqSe1FoMuiFTOR4xJwFVPfFMZusFTC/xa8CWYyXkkemo
 """.trimIndent()

    val PRIVATE_KEY_TEST = """
 MIIEpAIBAAKCAQEAv1/aO7CwBtlVvNYJeOyTcFl+S29LSKXNi3I8dooSBbORx2KB
 nEoU2KoR2t5Kwa69IDmge4a7f4WVPf/FAe0TPDKyv13bQBlnnkrfeZs6ZbhYbKwl
 UezwtbTcOt4SdZ/tirbiJJXiaN6DMHNlf+Latlmfh33Y1fhuUMMl5/Sahddb4HrP
 NUocT3v6g5mizHUXvou6mZb78Mrq0t5UqFBuwhvBYncgX7hnxMdn7GWR6IHngFKl
 xq/uv/tLcT4cNsC0+hMLRFDd3yM+E4kg12sOtqfjdFhxlAQmrGHgqY9SQOi4dNXV
 QcC6l00lyf6+VCr1aDHruvAQNRV4ID1tXrV0KwIDAQABAoIBAQC3w8AYPzLYcMu+
 l7zizDFL2fPHVehxqxyzwY9DuJNTvDnIvtN8kzpvTuf0Ri58y2E6V3u+AgN7kXdc
 4IR5tAAbdwdjcSm+5GY2fukWqbMqyBjIyYjQoXgG09QCBwqsvlRoNfLzdKXDU5+f
 379gTSL9pRPmFDcqR/MOREXa9WuIBjfkiAo+hZo63rvWYJd6ahPLe1YkTR3HUuw4
 bPQ1OaUpNPaJJ3pQXpIqNJmmUgJX3hsH5isymt4/3TxvYLXk4gBCGef5EeKO20gD
 HEAkjQ708bFbJkI/OMA+DOnMCh2wTKLYGpcIIFSTQq6LIyNBIAPV1FWaQCbcC2ol
 JskFspGRAoGBAMbo5B8xLg/kYMgvDmhKGjZ7QxUwXBx7d8G3odY/Q7ZbTv0yz9Ot
 Jxc9kVNZ/oYvFQmZewM90yDNZenP0LJLd+DWhtLGOu1spLVtT3rwmxMIG6KY7lGH
 977g30kN459w8PFFGKLFGlIwm0XvQiXY0NYVDLUa9N+dYvgeF0QO5/djAoGBAPZN
 S+Y2Bh6E0eOp8Mb93in4lmw5rsjfCzukgivMOQfpAqKZxy+zqpxniPxX8HqMQ+Dq
 gsPRydDelWFRpGQVTosmvYtS/IMT1Gif9aB61xRi7y0tT+GZGbjWYQlNp+yuagSF
 HYimuSyqPfaP/U2Jd3scSt9n332fVv6uleg9Jx6ZAoGAMzqiHkJ9WYx5PLWZ4VRM
 ggGnGlr1LkTCaDsSTGrI1ruXkXC3xA3RhpPNzQjdm9OeeHV2EDSpHsan29sxJep3
 +oll+f0iCio0FaIMmV5EolcxPuRiZM8/CINEHr2cBWGCPLGOEcXv7IEmTJG9mJ/J
 0YlkV3SU4s7XQY4Gcfng1n8CgYEArCUYMD2AB2KThAhptdsHpNQO5nNmexSROE1h
 gZUKI0IzSkwqkGeFji067AvWkwBoDNI7NPvyjx6Sz2MNf7n4nH8mKTOGM2LvqXRr
 a4+Ptbr0KeIqm6Wy+aI0ThlNjbnVdXVIUBMfB12xUxwOUVwL+yqqFfz2kkmEtxp0
 wc3EArkCgYA4UIbORl+y1mB6z/5wV4QlurVLxaKIPDZHAQDzdC+SZAVs3aSJyZlf
 LHFQ/osr42sX0J8TBj0zEbncR7OIGIBc2vy4TD9os863TSMeYBmxAVVCxldhQK6I
 G1hLqwMz2aqqEVNKxNoZ7NcS/4wAABq9z5I3wl35usSzL3cXOsu4yw==
 """.trimIndent()

}