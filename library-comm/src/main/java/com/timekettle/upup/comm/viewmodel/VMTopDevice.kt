package com.timekettle.upup.comm.viewmodel

import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.viewModelScope
import co.timekettle.btkit.BleCmdContant
import co.timekettle.btkit.BleUtil
import co.timekettle.btkit.BleUtil.toHexStr
import co.timekettle.btkit.bean.RawBlePeripheral
import co.timekettle.btkit.bean.WT2BlePeripheral
import com.timekettle.upup.base.mvvm.vm.BaseViewModel
import com.timekettle.upup.base.utils.SpUtils
import com.timekettle.upup.base.utils.logD
import com.timekettle.upup.base.utils.logE
import com.timekettle.upup.base.utils.logV
import com.timekettle.upup.comm.bean.BleDeviceBean
import com.timekettle.upup.comm.constant.SpKey
import com.timekettle.upup.comm.ktx.findLeft
import com.timekettle.upup.comm.ktx.findRight
import com.timekettle.upup.comm.model.BrokenReason
import com.timekettle.upup.comm.model.X1Device
import com.timekettle.upup.comm.model.X1Role
import com.timekettle.upup.comm.model.X1Status
import com.timekettle.upup.comm.service.home.HomeServiceImplWrap
import com.timekettle.upup.comm.service.setting.SettingServiceImplWrap.getBleVolume
import com.timekettle.upup.comm.tools.DeviceManager
import com.timekettle.upup.comm.tools.DeviceTool
import com.timekettle.upup.comm.utils.HallUtil
import com.timekettle.upup.comm.utils.MyActivityUtil
import com.timekettle.upup.comm.utils.OtaCheckUtil
import com.tmk.libserialhelper.tmk.W3ProHelper
import com.tmk.libserialhelper.tmk.ktx.findLeft
import com.tmk.libserialhelper.tmk.ktx.findRight
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.Job
import kotlinx.coroutines.delay
import kotlinx.coroutines.launch
import java.util.concurrent.ConcurrentHashMap

/**
 * 全局的ViewModel，在App的生命周期内都可见
 */
class VMTopDevice : BaseViewModel() {
    private var x1Devices = mutableListOf(
        X1Device(state = X1Status.ChargingInBox, x1Role = X1Role.Left),
        X1Device(state = X1Status.ChargingInBox, x1Role = X1Role.Right)
    )
    val liveX1Devices = MutableLiveData<MutableList<X1Device>>()
    val liveNeedUpgrade = MutableLiveData(false)  // 耳机是否需要升级
    private val disconnectDeviceMap = ConcurrentHashMap<String, Long>()
    private val RECONNECT_INTERVAL = 1500L
    private var jobRefresh: Job = Job()
    private var jobConnTimeOutL: Job? = null
    private var jobConnTimeOutR: Job? = null
    private var whileFlag: Boolean = true
    private var twoHeadsetInfo = W3ProHelper.getTwoHeadsetInfo()  //串口读取到的设备信息
    private var mBleListener: BleUtil.Listener? = null
//    private var jobNoReConnectL: Job? = null
//    private var jobNoReConnectR: Job? = null

    init {
        loadData()
        initBleListener()
    }

    fun loadData() {
        // 1秒钟刷新一次
        viewModelScope.launch(Dispatchers.IO + jobRefresh) {
            doTaskOnInterval(1000L) { getNewData() }
        }
    }

    /**
     * 更新设备状态
     */
    fun updateDeviceState(isLeft: Boolean, state: X1Status) {
        if (isLeft) {
            x1Devices.findLeft()?.state = state
        } else {
            x1Devices.findRight()?.state = state
        }
        liveX1Devices.postValue(x1Devices)
        // 如果是broken 状态，需要升级，得在弹窗里面提示
        if (state == X1Status.Broken && state.desc == BrokenReason.UpgradeFailToBrick.reasonDesc) {
            liveNeedUpgrade.postValue(true)
        }

        if (state == X1Status.ChargingInBox) {
            checkUpgradeAndUpdateState()
        }

    }

    // 判断是否需要升级，并更新到状态上
    private fun checkUpgradeAndUpdateState() {
        val notifyMsg = HomeServiceImplWrap.getDfuNotifyMsg()
        if (notifyMsg == null) {
            liveNeedUpgrade.postValue(false)
        } else {
            if (OtaCheckUtil.isMeetCondition(notifyMsg)) {
                liveNeedUpgrade.postValue(true)
            } else {
                liveNeedUpgrade.postValue(false)
            }
        }
    }

    fun disConnectDeviceBle(isLeft: Boolean, noReConnectSecond: Int = 5) {
        logD("app主动断开BLE连接 isLeft: $isLeft", TAG)
        if (isLeft) {
            BleUtil.shared.connectedPeripherals.forEach {
                DeviceTool.asWSeries(it)?.let { wt2 ->
                    if (wt2.role == RawBlePeripheral.Role.Left) {
                        disconnectDeviceMap[wt2.id] = System.currentTimeMillis()
                        BleUtil.shared.disconnect(wt2)
                    }
                }
            }
//            jobNoReConnectL?.cancel()
//            jobNoReConnectL = countDownCoroutines(3, viewModelScope, onFinish = {
//                jobNoReConnectL = null
//            })
        } else {
            BleUtil.shared.connectedPeripherals.forEach {
                DeviceTool.asWSeries(it)?.let { wt2 ->
                    logD("app主动断开BLE连接 wt2: $wt2", TAG)
                    if (wt2.role == RawBlePeripheral.Role.Right) {
                        disconnectDeviceMap[wt2.id] = System.currentTimeMillis()
                        BleUtil.shared.disconnect(wt2)
                    }
                }
            }
//            jobNoReConnectR?.cancel()
//            jobNoReConnectR = countDownCoroutines(3, viewModelScope, onFinish = {
//                jobNoReConnectR = null
//            })
        }
        updateDeviceState(isLeft, X1Status.ChargingInBox)
    }

    // 获取最新的数据，可以是从BLE里面读取，也可以是从串口里面读取
    fun getNewData() {
        twoHeadsetInfo = W3ProHelper.getTwoHeadsetInfo()
        val leftX1Device = x1Devices.findLeft()?.apply {
            bleDevice = null
            serialDevice = twoHeadsetInfo.findLeft()
        }
        val rightX1Device = x1Devices.findRight()?.apply {
            bleDevice = null
            serialDevice = twoHeadsetInfo.findRight()
        }
        // 只找到和左边串口Mac地址一样的BLE设备
        val leftBlePeripheral = BleUtil.shared.searchedPeripherals.find {
            if (it is WT2BlePeripheral)
                it.role == RawBlePeripheral.Role.Left && it.mac == twoHeadsetInfo.findLeft()?.bleMac
            else
                false
        }
        val rightBlePeripheral = BleUtil.shared.searchedPeripherals.find {
            if (it is WT2BlePeripheral)
                it.role == RawBlePeripheral.Role.Right && it.mac == twoHeadsetInfo.findRight()?.bleMac
            else
                false
        }
        if (!HallUtil.isLeftIn() && leftBlePeripheral != null) leftX1Device?.bleDevice =
            leftBlePeripheral as WT2BlePeripheral
        if (!HallUtil.isRightIn() && rightBlePeripheral != null) rightX1Device?.bleDevice =
            rightBlePeripheral as WT2BlePeripheral
        mapBleDevice2X1Device(leftX1Device)
        mapBleDevice2X1Device(rightX1Device)
        checkReadInfoSuccess(x1Devices)
        liveX1Devices.postValue(x1Devices)
        checkUpgradeAndUpdateState()
    }

    private fun checkReadInfoSuccess(x1Devices: MutableList<X1Device>) {
        val x1Left = x1Devices.findLeft()
        val x1Right = x1Devices.findRight()
        if (x1Left?.serialDevice?.isReadSuccess == true && x1Left.state == X1Status.Broken && HallUtil.isLeftIn()) {
            x1Left.state = X1Status.ChargingInBox
        }
        if (x1Left?.serialDevice?.isHalfHang == true) {
            x1Left.state = X1Status.Broken
        }
        if (x1Right?.serialDevice?.isReadSuccess == true && x1Right.state == X1Status.Broken && HallUtil.isRightIn()) {
            x1Right.state = X1Status.ChargingInBox
        }
        if (x1Right?.serialDevice?.isHalfHang == true) {
            x1Right.state = X1Status.Broken
        }
    }

    // 将BLE设备的状态映射为X1Device的状态
    private fun mapBleDevice2X1Device(x1Device: X1Device?) {
        val wt2 = x1Device?.bleDevice ?: return
        if (wt2.isConnected) {  // 提前置为连接状态
            x1Device.state = X1Status.Connected
            stopConnTimeoutJob(wt2.role == RawBlePeripheral.Role.Left)
        } else if (
            wt2.isConnecting
            || wt2.state == RawBlePeripheral.PeripheralState.NONE
            || wt2.state == RawBlePeripheral.PeripheralState.DISCONNECT
        ) {
            x1Device.state = X1Status.SearchConnecting
        }
    }

    // 在协程中周期性地执行任务
    private suspend fun doTaskOnInterval(intervalMillis: Long, task: suspend () -> Unit) {
        while (whileFlag) { // 无限循环
            task()
            delay(intervalMillis)
        }
    }

    override fun onCleared() {
        logD("VMTopDevice被销毁了", TAG)
        whileFlag = false
        BleUtil.shared.removeListener(mBleListener)
        super.onCleared()
    }

    //当被实例化时就开始监听ble，此处只作为连接用,没有连接的耳机就去连接
    private fun refreshDeviceList() {
        logV("获取W系列BLE设备列表(${BleUtil.shared.sortedSearchedPeripherals.size}): " + BleUtil.shared.sortedSearchedPeripherals, TAG)
        val searchedPeripherals = BleUtil.shared.sortedSearchedPeripherals //获取到所有扫描到的设备，之前指定过类型
        val bleTestMacList =
            SpUtils.getString(SpKey.NEED_CONN_TEST_MAC, "").uppercase().split(" ").toMutableList()
        W3ProHelper.getTwoHeadsetInfo().forEach {  // 添加串口读取到的设备Mac地址信息
            bleTestMacList.add(it.bleMac)
        }
        for (peripheral in searchedPeripherals) {
            if (peripheral.state == RawBlePeripheral.PeripheralState.NONE || peripheral.state == RawBlePeripheral.PeripheralState.DISCONNECT) {
                DeviceTool.asWSeries(peripheral)?.let {
                    if (!it.macSuffix4.isNullOrEmpty()) {
                        if (it.macSuffix4.uppercase() in bleTestMacList) connectBle(it)
                        if (it.mac.uppercase() in bleTestMacList) connectBle(it)
                    }
                }
            }
        }
        getNewData()
    }


    // 超时 10秒钟没连上，设为感叹号
    fun startConnTimeoutJob(isLeft: Boolean, timeOutSecond: Int = 15) {
        stopConnTimeoutJob(isLeft)
        val tempJob = viewModelScope.launch {
            delay(timeOutSecond * 1000L)
            logD("连接超时！！isLeft: $isLeft", TAG)
            updateDeviceState(isLeft, X1Status.Broken.apply {
                desc = BrokenReason.ConnTimeOut.reasonDesc
            })
        }
        if (isLeft) jobConnTimeOutL = tempJob else jobConnTimeOutR = tempJob
    }

    fun stopConnTimeoutJob(isLeft: Boolean) {
        if (isLeft) jobConnTimeOutL?.cancel().also { jobConnTimeOutL = null }
        else jobConnTimeOutR?.cancel().also { jobConnTimeOutR = null }
    }

    private var lastScanTime: Long = 0

    private fun initBleListener() {
        mBleListener = object : BleUtil.Listener {
            override fun dispatchEvent(type: BleUtil.BleEventName, perip: RawBlePeripheral?) {
                onBleEventUpdated(type, perip)
            }

            override fun onBluetoothStateUpdate(state: Int) {
            }

            override fun dispatchCmdEvent(
                perip: RawBlePeripheral?,
                cmdId: String?,
                values: ByteArray?
            ) {
                super.dispatchCmdEvent(perip, cmdId, values)
                if (cmdId == BleCmdContant.AppCmdId.TestSettingsNRR && !MyActivityUtil.isUpgradingAty()) {
                    if (values != null && perip != null) {
                        logD("从BLE获取到了算法参数: ${(perip as WT2BlePeripheral).mac} ${toHexStr(values)}", TAG)
                        val key = perip.mac ?: ""
                        var paramStr = toHexStr(values)
                        if (paramStr.length != 13 * 2) return
                        paramStr = paramStr.takeLast(8 * 2)
                        DeviceManager.saveAlgorithmParameter(mapOf(key to paramStr))
                    }
                }
            }
        }

        BleUtil.shared.setLogLevel(2)
        BleUtil.shared.setLogCallback { level, tag, msg, _ ->
            when (level) {
                1 -> {
                    logE(msg, tag)
                }

                2 -> {
                    if (msg.contains("onScanResult:")) {
                        val currentTime = System.currentTimeMillis()
                        if (currentTime - lastScanTime >= 500) {
                            lastScanTime = currentTime
                            logD(msg, tag)
                        }
                    } else {
                        logD(msg, tag)
                    }
                }
            }
        }

        BleUtil.shared.addListener(mBleListener)
    }

    fun onBleEventUpdated(type: BleUtil.BleEventName, perip: RawBlePeripheral?) {
        when (type) {
            BleUtil.BleEventName.BleDidStatusUpdate -> {
                refreshDeviceList()
            }

            BleUtil.BleEventName.BleConnectStandby -> {
                logD("模式外连接耳机", TAG)
                refreshDeviceList()
                if (!HomeServiceImplWrap.isPhoneMode()) {
                    DeviceTool.connectDevice(this::class.java.name, perip)
                }
                viewModelScope.launch {
                    delay(1000)

                    if (DeviceManager.getAlgoParameter((perip as WT2BlePeripheral).mac.toString()) == null) {
                        logD("${perip.mac} 本地没有算法参数，尝试发送指令获取算法参数", TAG)
                        BleUtil.shared.sendCmdToQueue(perip, BleCmdContant.AppCmdId.TestSettingsNRR)
                    }

                    if (!MyActivityUtil.isUpgradingAty() && perip != null) {  // 不在升级模式才更新音量
                        val mValue = getBleVolume()
                        val floatValue = mValue / 100f  // 0-100映射到 0f-1.0f
                        perip.writeVolume(floatValue)
                    }
                }
                judgeStopScan()
            }

            BleUtil.BleEventName.BleDisconnectedPeripheral -> {
                perip?.let {
                    disconnectDeviceMap.put(perip.id, System.currentTimeMillis())
                }
                refreshDeviceList()
                if (!HomeServiceImplWrap.isPhoneMode()) { // 没在通话状态
                    DeviceTool.disconnectDevice(this::class.java.name)
                }
                logD("BLE设备断开了 $perip", TAG)
                if (DeviceTool.isLeft(perip) && HallUtil.isLeftOut()) {
                    updateDeviceState(true, X1Status.SearchConnecting)
                    startConnTimeoutJob(true)
                }
                if (DeviceTool.isRight(perip) && HallUtil.isRightOut()) {
                    updateDeviceState(false, X1Status.SearchConnecting)
                    startConnTimeoutJob(false)
                }
                judgeStartScan()
            }

            BleUtil.BleEventName.BleProtocolChanged -> {}

            BleUtil.BleEventName.BleStError -> {}

            BleUtil.BleEventName.BleConnFailedTooMuch -> {
//                showDebugToast("短时间内连接失败次数过多133错误，将重启蓝牙!!")
//                logE("短时间内连接失败次数过多133错误，将重启蓝牙!!")
//                BtUtil.resetBluetooth()
            }

            else -> {}
        }
    }

    private fun connectBle(bleItem: WT2BlePeripheral) {
//        if (bleItem.isBleLeft() && jobNoReConnectL != null) {
//            logD("不连接设备L，等一会儿")
//            return
//        } else if (bleItem.isBleRight() && jobNoReConnectR != null) {
//            logD("不连接设备R，等一会儿")
//            return
//        }

        if (disconnectDeviceMap.containsKey(bleItem.id) &&
            System.currentTimeMillis() - disconnectDeviceMap[bleItem.id]!! < RECONNECT_INTERVAL) {
            logD("处于断连设备 ${bleItem.id} 等待期，等一会儿", TAG)
            return
        }
        logD("准备连接BLE设备: $bleItem", TAG)
        BleUtil.shared.connect(bleItem)
    }

    fun disconnectBle(bleItem: BleDeviceBean) {
        DeviceTool.asWSeries(bleItem.peripheral)?.let {
            DeviceManager.removeBleMac(it.mac)
        }
        BleUtil.shared.disconnect(bleItem.peripheral)
    }

    private fun judgeStopScan() {
        if (twoHeadsetConnected()) {
            logD("两只耳机都连上了，停止扫描", TAG)
            BleUtil.shared.stopScan()
        } else {
            logD("有耳机没连上，不停止扫描", TAG)
        }
    }

    fun judgeStartScan() {
        if (!twoHeadsetConnected()) {
            // 不处于 "两只耳机入盒，抽屉关闭" 的情况才开扫描
            if (!(HallUtil.isBoxDrawerIn() && HallUtil.isLeftRightIn())) {
                logD("有耳机没连上，开始扫描", TAG)
                BleUtil.shared.startScan(BleCmdContant.ProductType.W3_Pro)
            }
        } else {
            logD("两只耳机都连上了，不扫描", TAG)
        }
    }

    private fun twoHeadsetConnected(): Boolean {
        val connectedDeviceList = BleUtil.shared.connectedPeripherals.map { it.id }
        val twoHeadset = W3ProHelper.getTwoHeadsetInfo()
        return twoHeadset.findLeft()?.bleMac in connectedDeviceList &&
                twoHeadset.findRight()?.bleMac in connectedDeviceList
    }

    companion object {
        const val TAG ="VMTopDevice"
    }

}