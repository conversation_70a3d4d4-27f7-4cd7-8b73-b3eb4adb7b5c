package com.timekettle.upup.comm.net

enum class ApiStatus {
    SUCCESS,
    ERROR,
    LOADING
}  // for your case might be simplify to use only sealed class

sealed class ApiResult<out T>(val status: ApiStatus, val data: T?, val message: String?) {

    data class Success<out R>(private val _data: R?) : ApiResult<R>(
        status = ApiStatus.SUCCESS,
        data = _data,
        message = null
    )

    data class Error(private val exception: String) : ApiResult<Nothing>(
        status = ApiStatus.ERROR,
        data = null,
        message = exception
    )

    data class AuthError(private val exception: String, private val code: String?) : ApiResult<String>(
        status = ApiStatus.ERROR,
        data = code,
        message = exception
    )

    data class Loading(private val isLoading: Boolean) : ApiResult<Boolean>(
        status = ApiStatus.LOADING,
        data = isLoading,
        message = null
    )
}