package com.timekettle.upup.comm.bean

import android.os.Parcelable
import androidx.room.ColumnInfo
import androidx.room.Entity
import androidx.room.Ignore
import androidx.room.PrimaryKey
import com.chad.library.adapter.base.entity.MultiItemEntity
import com.google.gson.Gson
import com.google.gson.annotations.Expose
import kotlinx.android.parcel.Parcelize
import java.util.Date

/**
 *
 * @author: licoba
 * @date: 2022/8/11
 */

// 消息类的包裹类，加上消息是增加还是删除的操作，方便adapter做notifyItemXXX操作，避免每次都刷新一整个列表（一是性能问题，二是不够丝滑）
data class MsgListWrapper(
    var data: MutableList<MsgBean> = mutableListOf(),
    var index: Int = -1,
    val session: Long = -1L, // 消息的ID
    var operation: MsgOperation = MsgOperation.None,
    var direction: MsgDirection = MsgDirection.UnKnown
)

data class MsgWrapper(
    var data: MsgBean?,
    var direction: MsgDirection? = MsgDirection.Left
)

data class MsgInterview(
    var direction: MsgDirection? = MsgDirection.UnKnown,
    val code: String?,
    val isLast: Boolean,
    val text: String?
)

data class MsgSpeech(
    val sessionId: String,
    val state: String,
    val locale: String,
    var text: String
)

data class MsgNetState(
    val txQuality: Int,
    var rxQuality: Int
)

enum class MsgOperation {
    Add,  //代表是添加消息
    Delete, // 代表删除消息
    Update, // 代表更新消息
    None
}

@Parcelize
@Entity
data class MsgBean(
    @PrimaryKey var session: Long = 0,  // 创建时间，用这个做ID
    var pDate: Date? = null, // 关联的phone父表日期
    var hDate: Date? = null, // 关联的history父表日期
    // 需要加上recognize_的前缀，否则会提示 Multiple fields have the same columnName:session 错误，因为嵌套类和父类都有一个相同的session字段
    @ColumnInfo(name = "recognize_result") var recognizeResult: RecognizeResultBean = RecognizeResultBean(),
    @ColumnInfo(name = "translate_result") var translateResult: TranslateResultBean = TranslateResultBean(),
    @Ignore var isTts: Boolean = false,  // 是否开启Tts
    var srcCode: String? = "", // 原语言
    var dstCode: String? = "", // 目标语言
    var direction: MsgDirection = MsgDirection.UnKnown,
    @Ignore var isPlaying: Boolean = false, // 是否正在播放Tts
    @Ignore var nickName: String = "", // 显示的昵称，仅在多人触控模式下可用
    @Ignore var tips: String = "",//作为XXX加入群聊的item使用
    @Ignore var bubbleId: String = "", // 1v1确定气泡用，一个气泡的bubbleId相同

) : MultiItemEntity, Parcelable {
    override val itemType: Int
        get() =  direction.value

    fun deepCopy(): MsgBean {
        return Gson().fromJson(Gson().toJson(this), this.javaClass)
    }

}

@Parcelize
data class RecognizeResultBean(
    @Expose var chkey: String = "",
    @Expose var session: Long = -1,
    var srcCode: String? = "",
    var dstCode: String? = "",
    @Expose var isLast: Boolean = false,
    @Expose var text: String? = "",
    var engine: String? = "",
    var texts: MutableList<String> = mutableListOf(), // 临时存储多条消息使用

) : Parcelable

@Parcelize
data class TranslateResultBean(
    @Expose var chkey: String? = "",
    @Expose var session: Long = -1,
    @Expose var isLast: Boolean = false,
    @Expose var text: String? = "",
    var engine: String? = "",
    var texts: MutableList<String> = mutableListOf(), // 临时存储多条消息使用
    var tempText: String? = "",
) : Parcelable

data class TextBean(
    var direction: MsgDirection? = MsgDirection.UnKnown,
    var text: String? = "",
    var speakText: String = "",
    var showSpeak: Boolean = false
)


enum class MsgDirection(val value: Int, val str: String) {
    UnKnown(0,"UnKnown"),
    Left(1, "Left"),
    Right(2, "Right"),
    Up(3, "Up"),
    Down(4, "Down"),
    Middle(5, "Middle")//作为XXX加入群聊的item使用
}