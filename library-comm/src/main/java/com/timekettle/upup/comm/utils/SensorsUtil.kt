package com.timekettle.upup.comm.utils

import android.os.Parcelable
import com.sensorsdata.analytics.android.sdk.SensorsDataAPI
import com.timekettle.upup.base.utils.SpUtils
import com.timekettle.upup.base.utils.logD
import com.timekettle.upup.comm.model.SensorsCustomEvent
import org.json.JSONException
import org.json.JSONObject


/**
 * <AUTHOR>
 * @date 2023/3/13 12:23
 * @email <EMAIL>
 * @desc 神策的工具类
 */
object SensorsUtil {

    //  fun trackEvent(event: String, valueMap: HashMap<String, out Any?>? = null) {  // * 与 out Any? 等价
    fun trackEvent(event: String, valueMap: HashMap<String, *>? = null) {
        try {
            val properties = JSONObject()
//            logD("上传神策事件 [event]${event},[valueMap]$valueMap", "SensorsUtil")
            valueMap?.map {
                properties.put(it.key, it.value)
            }
            SensorsDataAPI.sharedInstance().track(event, properties)
        } catch (e: JSONException) {
            e.printStackTrace()
        }
    }


    private fun putEventString(event: SensorsCustomEvent, value: Any?) {
        try {
            val properties = JSONObject()
            properties.put("ProductID", value) // 设置商品 ID
            properties.put("ProductCatalog", "Laptop Computer") // 设置商品类别
            SensorsDataAPI.sharedInstance().track("BuyProduct", properties)
        } catch (e: JSONException) {
            e.printStackTrace()
        }
    }
}