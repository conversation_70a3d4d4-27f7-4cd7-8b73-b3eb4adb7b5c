package com.timekettle.upup.comm.edittext

import android.content.Context
import android.text.Editable
import android.text.InputFilter.LengthFilter
import android.util.AttributeSet
import android.view.LayoutInflater
import android.widget.FrameLayout
import androidx.core.widget.doAfterTextChanged
import com.timekettle.upup.comm.R
import com.timekettle.upup.comm.databinding.CommLengthEditTextBinding

/**
 * <AUTHOR>
 * @date 2022/9/27
 * @desc
 */
class CommonLengthEditText @JvmOverloads constructor(
    context: Context,
    attrs: AttributeSet? = null,
    defStyleAttr: Int = 0
) : FrameLayout(context, attrs, defStyleAttr) {

    private lateinit var mBinding: CommLengthEditTextBinding
    private var mAfterTextChangedListener: ((text: Editable?) -> Unit)? = null
    private var mMaxLength = 30


    init {
        init()
    }

    private fun init() {
        mBinding = CommLengthEditTextBinding.inflate(LayoutInflater.from(context), this, true)
        initData()
    }

    private fun initData() {
        with(mBinding) {
            tvCounter.text = "0/$mMaxLength"
            etContent.filters = arrayOf(LengthFilter(mMaxLength))
            etContent.doAfterTextChanged {
                val length = it?.length ?: 0
                tvCounter.text = "$length/$mMaxLength"
                if (length == mMaxLength) {
                    root.setBackgroundResource(R.drawable.shape_common_length_edit_text_error)
                } else {
                    root.setBackgroundResource(R.drawable.shape_common_length_edit_text)
                }
                mAfterTextChangedListener?.invoke(it)
            }
        }
    }

    fun getEditText() = mBinding.etContent

    fun getText() = mBinding.etContent.text.toString()

    fun setMaxLength(maxLength: Int) {
        mMaxLength = maxLength
        initData()
    }

    fun setHintText(text:String){
        with(mBinding) {
            etContent.hint = text
        }
    }

}