package com.timekettle.upup.comm.service.login

import android.content.Context
import com.alibaba.android.arouter.facade.template.IProvider
import com.timekettle.upup.comm.model.LoginMethod
import com.timekettle.upup.comm.model.UserBean

/**
 * https://github.com/alibaba/ARouter/blob/master/README_CN.md
 * 声明登录服务的接口,其他组件通过接口来调用服务
 * Create by licoba on 2022/4/27
 */
interface LoginService : IProvider {

    fun isLogin(): Boolean

    fun getUserInfo(): UserBean?

    fun saveUser(userBean: UserBean)

    fun removeUserInfo()

    fun start(context: Context)

    fun getLoginMethod(): LoginMethod

    fun startThirdLogin(loginMethod: LoginMethod) // 调用三方登录的服务

    fun logout()

    fun addOnLogoutListener(onLogoutListener: () -> Unit)

    fun isFishRechargeAvailable(): Boolean

    fun setFishRechargeAvailable(isAvailable: Boolean)

    fun isPolicyAgreed(): Boolean  // 是否同意了隐私协议
}