package com.timekettle.upup.comm.tools

import android.annotation.SuppressLint
import java.security.cert.X509Certificate
import javax.net.ssl.X509TrustManager

/**
 * author: weiconglee
 **/
@SuppressLint("CustomX509TrustManager")
class CustomPKCS12TrustManager : X509TrustManager {
    private var delegate: X509TrustManager? = null

    private fun getDelegate(): X509TrustManager? {
        if (delegate == null) {
            delegate = PKCS12Manager.getTrustManager()
        }
        return delegate
    }

    override fun checkClientTrusted(chain: Array<out X509Certificate>?, authType: String?) {
        getDelegate()?.checkClientTrusted(chain, authType)
    }

    override fun checkServerTrusted(chain: Array<out X509Certificate>?, authType: String?) {
        getDelegate()?.checkServerTrusted(chain, authType)
    }

    override fun getAcceptedIssuers(): Array<X509Certificate> {
        return getDelegate()?.acceptedIssuers ?: arrayOf()
    }


}