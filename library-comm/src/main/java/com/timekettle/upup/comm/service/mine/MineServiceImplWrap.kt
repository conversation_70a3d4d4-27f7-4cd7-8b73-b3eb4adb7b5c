package com.timekettle.upup.comm.service.mine

import com.alibaba.android.arouter.facade.annotation.Autowired
import com.alibaba.android.arouter.launcher.ARouter
import com.timekettle.upup.base.utils.SpUtils
import com.timekettle.upup.comm.constant.RouteUrl
import com.timekettle.upup.comm.constant.TmkProductType
import com.timekettle.upup.comm.model.OfflineEligibility
import com.timekettle.upup.comm.service.home.HomeService

/**
 **  我的（Mine）模块对其它模块的接口暴露
 **
 */
object MineServiceImplWrap {

    @Autowired(name = RouteUrl.Mine.MineService)
    lateinit var service: MineService

    fun saveOfflineBuyList(data: OfflineEligibility) {
        service.saveOfflineBuyList(data)
    }

    fun getOfflineEligibility(): OfflineEligibility? {
        return service.getOfflineEligibility()
    }

    fun clearMineBean() {
        return service.clearMineBean()
    }

    init {
        ARouter.getInstance().inject(this)
    }

}