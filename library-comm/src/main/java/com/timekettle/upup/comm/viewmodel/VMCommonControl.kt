package com.timekettle.upup.comm.viewmodel

import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.viewModelScope
import co.timekettle.sip.call.CallManager
import co.timekettle.sip.entity.IpEntity
import co.timekettle.sip.entity.ResEntity
import com.blankj.utilcode.util.DeviceUtils
import com.blankj.utilcode.util.GsonUtils
import com.google.gson.reflect.TypeToken
import com.timekettle.upup.base.mvvm.vm.BaseViewModel
import com.timekettle.upup.base.utils.logD
import com.timekettle.upup.base.utils.logE
import com.timekettle.upup.base.utils.showToast
import com.timekettle.upup.comm.bean.MeetEngineHost
import com.timekettle.upup.comm.model.TmkEngineBean
import com.timekettle.upup.comm.service.trans.TransServiceImplWrap
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch
import okhttp3.OkHttpClient
import okhttp3.Request
import org.json.JSONObject


/**
 * 控制页面的ViewMode
 */
class VMCommonControl : BaseViewModel() {

    // 根据BaseUrl，获取引擎的IP列表
    private val client: OkHttpClient by lazy { OkHttpClient() }
    val liveEngineIpList = MutableLiveData<MutableList<TmkEngineBean>>()
    val liveSipIpList = MutableLiveData<MutableList<MeetEngineHost>>()

    fun fetchHosts(ipAddress: String) {
        logD("根据baseUrl获取引擎的IP列表：$ipAddress")
        viewModelScope.launch(Dispatchers.IO) {
            val request = Request.Builder()
                .url(ipAddress)
                .build()
            try {
                client.newCall(request).execute().use { response ->
                    if (!response.isSuccessful) {
                        logE("获取引擎IP列表失败：$response")
                        showToast("❗❗️获取引擎IP列表失败：$response")
                    } else {
                        val json = JSONObject(response.body!!.string())
                        logD("获取到了引擎IP列表 $json")
                        // 获取data对象
                        val data = json.getJSONObject("data")
                        // 获取routes数组
                        val routes = data.getJSONArray("routes")
                        val list = GsonUtils.fromJson(routes.toString(),
                            object : TypeToken<MutableList<TmkEngineBean>>() {}.type)
                            ?: mutableListOf<TmkEngineBean>()
                        liveEngineIpList.postValue(list)
                    }
                }
            } catch (e: Exception) {
                logE("网络请求失败：$e")
                showToast("❗❗️网络请求失败：$e")
            }
        }
    }

    fun fetchSipHosts() {
        viewModelScope.launch(Dispatchers.IO) {
            logD("获取ip列表 开始")
            TransServiceImplWrap.fetchMeetHosts { code, hosts ->
                if (code == 0) {
                    liveSipIpList.postValue(hosts)
                } else {
                    logD("获取ip列表 失败 code=$code")
                }
            }
        }
    }

}