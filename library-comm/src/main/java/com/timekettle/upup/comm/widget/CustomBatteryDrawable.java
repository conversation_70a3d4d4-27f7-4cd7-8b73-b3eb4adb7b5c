package com.timekettle.upup.comm.widget;

import android.content.Context;
import android.content.res.ColorStateList;
import android.graphics.drawable.ClipDrawable;
import android.util.AttributeSet;
import android.view.LayoutInflater;
import android.widget.ImageView;
import android.widget.TextView;

import androidx.constraintlayout.widget.ConstraintLayout;

import com.timekettle.upup.comm.R;

/**
 * 自定义电量控件
 */
public class CustomBatteryDrawable extends ConstraintLayout {

    private static final int LOW_PERCENTAGE = 20;
    private static final float LEVEL_PERCENTAGE = 10000 / 100f;//ClipDrawable.MAX_LEVEL = 10000;

    private ImageView mBatteryOuter, mBatteryPercentage, mBatteryCharging;
    TextView mPercentText;

    private ClipDrawable mPercentageDrawable;

    private boolean mCharging;
    private int mLevel;
    private ColorStateList mColorLow, mColorNormal, mColorPercentageNormal, mColorCharging;

    public static CustomBatteryDrawable fromContext(Context context) {
        LayoutInflater inflater = LayoutInflater.from(context);
        CustomBatteryDrawable v = (CustomBatteryDrawable)
                inflater.inflate(R.layout.tk_status_bar_battery_group, null);
        v.init();

        return v;
    }

    public CustomBatteryDrawable(Context context) {
        this(context,null);
    }

    public CustomBatteryDrawable(Context context, AttributeSet attrs) {
        this(context, attrs, 0);
    }

    public CustomBatteryDrawable(Context context, AttributeSet attrs, int defStyleAttr) {
        this(context, attrs, defStyleAttr, 0);
    }

    public CustomBatteryDrawable(Context context, AttributeSet attrs, int defStyleAttr,
                                 int defStyleRes) {
        super(context, attrs, defStyleAttr, defStyleRes);

        mColorLow = ColorStateList.valueOf(context.getColor(R.color.tk_color_battery_low));
        mColorNormal = ColorStateList.valueOf(context.getColor(R.color.tk_color_battery_noraml));
        mColorPercentageNormal = ColorStateList.valueOf(context.getColor(R.color.tk_color_battery_percent_nomal));
        mColorCharging = ColorStateList.valueOf(context.getColor(R.color.tk_color_battery_charging));
    }

    private void init() {
        mBatteryOuter = findViewById(R.id.battery_outer_frame);
        mBatteryPercentage = findViewById(R.id.battery_percentage_frame);
        mBatteryCharging = findViewById(R.id.battery_charging);
        mPercentText = findViewById(R.id.battery_percentage);
        mPercentageDrawable = (ClipDrawable) mBatteryPercentage.getDrawable();
        mPercentText.setVisibility(VISIBLE);
    }

    public void setCharging(boolean charging) {
        if(mCharging != charging) {
            mCharging = charging;
            mBatteryCharging.setVisibility(mCharging ? VISIBLE : GONE);
            updateColors();
            invalidate();
        }
    }

    public void setBatteryLevel(int level) {
        if(mLevel != level) {
            mLevel = level;
            mPercentText.setText(mLevel +"%");
            mPercentageDrawable.setLevel((int) (mLevel * LEVEL_PERCENTAGE));
            updateColors();
            invalidate();
        }
    }

    public void setColors(int foregroundColor, int backgroundColor, int singleToneColor) {
        mColorNormal = ColorStateList.valueOf(foregroundColor);
        mPercentText.setTextColor(singleToneColor);
        updateColors();
    }

    private void updateColors() {
        mBatteryOuter.setImageTintList(mLevel <= LOW_PERCENTAGE ? mColorLow : mColorNormal);

        ColorStateList percentageColor = mLevel <= LOW_PERCENTAGE ? mColorLow :
                mCharging ? mColorCharging : mColorPercentageNormal;
        mBatteryPercentage.setImageTintList(percentageColor);

    }



}
