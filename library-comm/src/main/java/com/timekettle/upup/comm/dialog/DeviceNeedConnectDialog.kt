package com.timekettle.upup.comm.dialog

import android.app.Activity
import android.view.Gravity
import android.view.View
import android.view.ViewGroup
import android.view.animation.Animation
import androidx.constraintlayout.widget.ConstraintLayout
import androidx.core.view.isVisible
import androidx.lifecycle.LifecycleOwner
import com.timekettle.upup.base.BaseApp
import com.timekettle.upup.base.ktx.animateAlpha1
import com.timekettle.upup.base.ktx.getAppViewModel
import com.timekettle.upup.base.ktx.invisible
import com.timekettle.upup.base.ktx.visible
import com.timekettle.upup.base.utils.logD
import com.timekettle.upup.comm.R
import com.timekettle.upup.comm.databinding.DialogDeviceNeedConnectBinding
import com.timekettle.upup.comm.ktx.findLeft
import com.timekettle.upup.comm.ktx.findRight
import com.timekettle.upup.comm.model.X1Device
import com.timekettle.upup.comm.model.X1Status
import com.timekettle.upup.comm.viewmodel.VMTopDevice
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.cancel
import kotlinx.coroutines.delay
import kotlinx.coroutines.launch
import org.libpag.PAGFile
import org.libpag.PAGScaleMode
import org.libpag.PAGView
import razerdp.basepopup.BasePopupWindow
import razerdp.util.animation.AlphaConfig
import razerdp.util.animation.AnimationHelper
import razerdp.util.animation.ScaleConfig

/**
 *
 * <AUTHOR>
 * @desc 设备需要连接的大弹窗
 *
 */

open class DeviceNeedConnectDialog(activity: Activity, private var needCount: Int = 1) :
    BasePopupWindow(activity) {

    private val mainCoroutineScope = CoroutineScope(Dispatchers.Main) // 需要自己管理生命周期
    private val viewModel: VMTopDevice by lazy { getAppViewModel() }
    private lateinit var pagTakeOut: PAGView
    private lateinit var pagViewSuccess: PAGView
    private lateinit var binding: DialogDeviceNeedConnectBinding
    var connectDoneToDo: (() -> Unit)? = null  // 连接完毕需要做的事情
    var connectOneToDo: (() -> Unit)? = null  // 连接完毕需要做的事情

    init {
        popupGravity = Gravity.CENTER
        isOutSideTouchable = true
        setContentView(R.layout.dialog_device_need_connect)
        setOutSideDismiss(true)
        setBackPressEnable(true)
        setOutSideTouchable(false)
        initData()
    }

    private fun initData() {
        viewModel.liveX1Devices.observe(context as LifecycleOwner, ::processDevices)
    }


    override fun onViewCreated(contentView: View) {
        binding = DialogDeviceNeedConnectBinding.bind(contentView)
        binding.initListener()
        binding.tvBottomTip.text =
            if (needCount == 2) BaseApp.context.getString(R.string.device_take_out_tip)
            else BaseApp.context.getString(R.string.device_take_out_tip)

        // Pag的动效
        val pagView = PAGView(context).apply {
            layoutParams = ConstraintLayout.LayoutParams(
                ViewGroup.LayoutParams.MATCH_PARENT,
                ViewGroup.LayoutParams.MATCH_PARENT
            )
            composition = PAGFile.Load(context.assets, "ani_pop_earbus_light.pag")
            setRepeatCount(1)
            setScaleMode(PAGScaleMode.Stretch) // 拉伸填充
            play()
        }
        binding.root.addView(pagView)

        pagTakeOut = PAGView(context).apply {
            layoutParams = ConstraintLayout.LayoutParams(
                ViewGroup.LayoutParams.MATCH_PARENT,
                ViewGroup.LayoutParams.MATCH_PARENT
            )
            composition = PAGFile.Load(context.assets, "ani_pop_takeout_new.pag")
            setRepeatCount(0)
            setScaleMode(PAGScaleMode.Stretch) // 拉伸填充
            play()
        }
        binding.llPagTakeout.addView(pagTakeOut)

        pagViewSuccess = PAGView(context).apply {
            layoutParams = ConstraintLayout.LayoutParams(
                ViewGroup.LayoutParams.MATCH_PARENT,
                ViewGroup.LayoutParams.MATCH_PARENT
            )
            composition = PAGFile.Load(context.assets, "ani_newuser_earbus_success.pag")
            setRepeatCount(1)
            setScaleMode(PAGScaleMode.Stretch) // 拉伸填充
        }
        showCloseIcon()
    }

    private fun showCloseIcon() {
        mainCoroutineScope.launch {
            delay(200)
            binding.ivClose.animateAlpha1(duration = 300)
        }
    }

    private fun DialogDeviceNeedConnectBinding.initListener() {
        ivClose.setOnClickListener { dismiss() }
    }


    private fun processDevices(devices: MutableList<X1Device>) {
        if (needCount == 2) dealNeed2Case(devices)
        else dealNeed1Case(devices)
    }


    private fun dealNeed1Case(devices: MutableList<X1Device>) {
        val leftConnected = devices.findLeft()?.bleDevice?.isConnected == true
        val rightConnected = devices.findRight()?.bleDevice?.isConnected == true
        if (leftConnected || rightConnected) {  // 两只都连接上了
            if (binding.llConnDone.isVisible) return
            mainCoroutineScope.launch {
                setBackDisabled()
                pagTakeOut.setRepeatCount(1)
                binding.tvBottomTip.invisible()
                binding.llConnDone.visible()
                binding.llConnDone.removeAllViews()
                binding.llConnDone.addView(pagViewSuccess)
                pagViewSuccess.play()
                delay(1000)
                connectOneToDo?.invoke()
                dismiss()
            }
        } else {  // 等待扫描到耳机
            binding.llConnDone.invisible()
            binding.tvBottomTip.visible()
            pagTakeOut.setRepeatCount(0)
        }
    }


    private fun dealNeed2Case(devices: MutableList<X1Device>) {
        val leftConnected = devices.findLeft()?.bleDevice?.isConnected == true
        val rightConnected = devices.findRight()?.bleDevice?.isConnected == true

        val leftConnecting = devices.findLeft()?.state == X1Status.SearchConnecting
        val rightConnecting = devices.findRight()?.state == X1Status.SearchConnecting

        val showAsConnecting =
            (leftConnected && rightConnecting) || (rightConnected && leftConnecting) || (leftConnecting && rightConnecting)
        if (leftConnected && rightConnected) {  // 两只都连接上了
            if (binding.llConnDone.isVisible) return
            mainCoroutineScope.launch {
                setBackDisabled()
                binding.tvBottomTip.invisible()
                binding.llConnIng.invisible()
                binding.llConnDone.visible()
                binding.llConnDone.removeAllViews()
                binding.llConnDone.addView(pagViewSuccess)
                pagViewSuccess.play()
                delay(1200)
                logD("两只耳机都连接上了，执行闭包回调")
                connectDoneToDo?.invoke()
                delay(600)
                dismiss()
            }
        } else if (showAsConnecting) {  // 两只耳机已经扫描到了，至少有一只在连接
            binding.llConnIng.visible()
            binding.tvBottomTip.invisible()
            pagTakeOut.setRepeatCount(1)
        } else {  // 等待扫描到两只耳机
            binding.llConnIng.invisible()
            binding.tvBottomTip.visible()
            pagTakeOut.setRepeatCount(0)
        }
    }


    override fun onCreateShowAnimation(): Animation {
        return AnimationHelper.asAnimation()
            .withScale(ScaleConfig().scale(0.25f, 1.0f))
            .withAlpha(AlphaConfig.IN)
            .toShow().apply {
                duration = 300
            }
    }

    override fun onCreateDismissAnimation(): Animation {
        return AnimationHelper.asAnimation()
//            .withScale(ScaleConfig())
            .withAlpha(AlphaConfig.OUT)
            .toDismiss().apply {
                duration = 200
            }
    }

    override fun onDestroy() {
        mainCoroutineScope.cancel()
        super.onDestroy()
    }


    // 禁止返回（也就是禁止所有操作）
    private fun setBackDisabled() {
        setOutSideDismiss(false)
        setBackPressEnable(false)
        setOutSideTouchable(false)
    }


    override fun onDismiss() {
        connectDoneToDo = null
        connectOneToDo = null
        super.onDismiss()
    }
}