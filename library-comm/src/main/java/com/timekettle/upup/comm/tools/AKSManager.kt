package com.timekettle.upup.comm.tools

import android.security.keystore.KeyGenParameterSpec
import android.security.keystore.KeyProperties
import android.util.Base64
import com.timekettle.upup.comm.utils.CertificateUtils
import com.timekettle.upup.comm.utils.DeviceUtil
import org.bouncycastle.asn1.ASN1OctetString
import org.bouncycastle.asn1.x500.X500Name
import org.bouncycastle.operator.jcajce.JcaContentSignerBuilder
import org.bouncycastle.pkcs.jcajce.JcaPKCS10CertificationRequestBuilder
import java.security.KeyPair
import java.security.KeyPairGenerator
import java.security.KeyStore
import java.security.PrivateKey
import java.security.cert.CertificateFactory
import java.security.cert.X509Certificate
import java.security.spec.RSAKeyGenParameterSpec
import javax.net.ssl.KeyManagerFactory
import javax.net.ssl.TrustManagerFactory
import javax.net.ssl.X509KeyManager
import javax.net.ssl.X509TrustManager


/**
 * 密钥管理类（RSA非对称加密版本）
 * author: weiconglee
 **/
object AKSManager {
    private const val TAG = "AKSManager"
    const val AKS_KEY_ALIAS = "Timekettle_AKS_KEY"
    private val keyStore: KeyStore = KeyStore.getInstance("AndroidKeyStore").apply { load(null) }


    fun getKeyStore(): KeyStore {
        return keyStore
    }

    // 生成设备基础密钥对（RSA 2048）
    private fun getKeyPair(): KeyPair {
        if (!keyStore.containsAlias(AKS_KEY_ALIAS)) {
            val keyPairGenerator = KeyPairGenerator.getInstance(
                KeyProperties.KEY_ALGORITHM_RSA
            )
            val keySpec = KeyGenParameterSpec.Builder(
                AKS_KEY_ALIAS,
                KeyProperties.PURPOSE_SIGN or KeyProperties.PURPOSE_VERIFY
            ).apply {
                setAlgorithmParameterSpec(RSAKeyGenParameterSpec(2048, RSAKeyGenParameterSpec.F4))
                setDigests(KeyProperties.DIGEST_SHA256, KeyProperties.DIGEST_SHA512)
                setSignaturePaddings(KeyProperties.SIGNATURE_PADDING_RSA_PKCS1)
                setUserAuthenticationRequired(false) // 不能要求用户身份验证，否则 SSL 握手时无法访问密钥
            }.build()

            keyPairGenerator.initialize(keySpec)
            keyPairGenerator.generateKeyPair()
        }
        val privateKey = keyStore.getKey(AKS_KEY_ALIAS, null) as PrivateKey
        val publicKey = keyStore.getCertificate(AKS_KEY_ALIAS).publicKey
        return KeyPair(publicKey, privateKey)
    }

    // 创建CSR（PEM格式）
    fun createCsr(): String {
        val keyPair = getKeyPair()
        val csrBuilder = JcaPKCS10CertificationRequestBuilder(
            X500Name("CN=${DeviceUtil.getSerialNumber()}, OU=x1, O=Shenzhen Timekettle Technologies Co.\\,Ltd., L=Shenzhen, ST=Guangdong, C=China"),  // 可扩展其他字段（如O/OU）
            keyPair.public
        )

        val signer = JcaContentSignerBuilder("SHA256withRSA").build(keyPair.private)

        val csr = csrBuilder.build(signer)

        val base64 = Base64.encodeToString(csr.encoded, Base64.NO_WRAP)
        val formattedBase64 = base64.chunked(64).joinToString("\n")
        val csrString =
            "-----BEGIN CERTIFICATE REQUEST-----\n$formattedBase64\n-----END CERTIFICATE REQUEST-----"
        CertificateUtils.saveCertificate(csrString, "AndroidKeyStore.csr")
        return csrString
    }

    /**
     * 保存csr到对应的key
     * @param pemCertificate String
     */
    fun savePemCertificateToKeyStore(pemCertificate: String) {
        CertificateUtils.saveCertificate(pemCertificate, "AndroidKeyStore.pem")

        val regex =
            Regex("-----BEGIN CERTIFICATE-----(.*?)-----END CERTIFICATE-----", RegexOption.DOT_MATCHES_ALL)
        val matches = regex.findAll(pemCertificate)
        val list = matches.map { it.groupValues[1] }.toList().toTypedArray()

        val certificateChain: MutableList<X509Certificate> = ArrayList()

        list.forEach {
            // 2. Base64解码
            val certificateBytes = Base64.decode(it, Base64.DEFAULT)
            // 3. 解析为X509Certificate
            val certificateFactory = CertificateFactory.getInstance("X.509")
            val certificate =
                certificateFactory.generateCertificate(certificateBytes.inputStream()) as X509Certificate
            certificateChain.add(certificate)
        }

        val privateKeyEntry =
            keyStore.getEntry(AKS_KEY_ALIAS, null) as? KeyStore.PrivateKeyEntry
        // 存储证书链
        keyStore.setKeyEntry(AKS_KEY_ALIAS, privateKeyEntry?.privateKey, null, certificateChain.toTypedArray())
    }

    /**
     * 从 KeyStore 中获取证书并读取信息
     */
    private fun getCertificateFromKeyStore(): X509Certificate? =
        if (keyStore.containsAlias(AKS_KEY_ALIAS)) {
            keyStore.getCertificate(AKS_KEY_ALIAS) as? X509Certificate
        } else {
            null
        }

    private fun readExtendValue(certificate: X509Certificate, oid: String): String? {
        val extensionValue = certificate.getExtensionValue(oid)
        val offlineKey = extensionValue?.let {
            val octetString = ASN1OctetString.getInstance(extensionValue)
            val octetBytes = octetString.octets
            octetBytes.joinToString("") { "%02x".format(it) }
        }
        return offlineKey
    }

    fun deleteKey() {
        if (keyStore.containsAlias(AKS_KEY_ALIAS)) {
            keyStore.deleteEntry(AKS_KEY_ALIAS)
        }
    }

    /**
     * 判断证书是否存在
     * @return Boolean
     */
    fun isCertificateExists(): Boolean {
        return try {
            getCertificateFromKeyStore()?.let {
                CertificateUtils.getIssuerCommonName(it).equals("Timkettle Root CA")
                        && CertificateUtils.getSubjectCommonName(it).equals(DeviceUtil.getSerialNumber())
            } ?: false
        } catch (e: Exception) {
            e.printStackTrace()
            false
        }
    }

    fun getOfflineKey(): String {
        getCertificateFromKeyStore()?.let {
            return readExtendValue(it, "1.3.6.1.4.1.9999.1.1") ?: ""
        }
        return ""
    }

    fun getPKCS12Key(): String {
        getCertificateFromKeyStore()?.let {
            return readExtendValue(it, "1.3.6.1.4.1.9999.1.2") ?: ""
        }
        return ""
    }

    /**
     * 从无密码的 KeyStore 获取 X509KeyManager
     */
    fun getOriginalKeyManager(): X509KeyManager {
        val keyManagerFactory =
            KeyManagerFactory.getInstance(KeyManagerFactory.getDefaultAlgorithm())
        val mKeyStore: KeyStore = KeyStore.getInstance("AndroidKeyStore").apply { load(null, null) }
        // keyStore 没有密码，传入 null
        keyManagerFactory.init(mKeyStore, null)
        return keyManagerFactory.keyManagers.first() as X509KeyManager
    }

    /**
     * 获取 X509TrustManager
     * 若传入 trustStore 为 null，则使用系统默认的信任管理器
     */
    fun getTrustManager(): X509TrustManager {
        val tmf = TrustManagerFactory.getInstance(TrustManagerFactory.getDefaultAlgorithm())
        val mKeyStore: KeyStore = KeyStore.getInstance("AndroidKeyStore").apply { load(null, null) }
        tmf.init(mKeyStore)
        return tmf.trustManagers.first() as X509TrustManager
    }

    fun readCertificateChain() {
        val originalKeyManager = getOriginalKeyManager()
        val privateKey = originalKeyManager.getPrivateKey(AKS_KEY_ALIAS)
//        Log.d(TAG, "readCertificateChain alias:$AKS_KEY_ALIAS privateKey:${privateKey} algorithm:${privateKey?.algorithm}")
        val certificateChain = originalKeyManager.getCertificateChain(AKS_KEY_ALIAS)
//        Log.d(TAG, "size:${certificateChain.size} readCertificateChain:${Gson().toJson(certificateChain)}")
    }
}