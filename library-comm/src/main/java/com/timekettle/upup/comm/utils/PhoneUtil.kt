package com.timekettle.upup.comm.utils

import android.media.RingtoneManager
import android.net.Uri
import android.util.Log
import com.timekettle.upup.base.BaseApp

object PhoneUtil {

    fun getRing(title: String): Uri? {
        val ringtoneManager = RingtoneManager(BaseApp.application)
        ringtoneManager.setType(RingtoneManager.TYPE_RINGTONE)
        val cursor = ringtoneManager.cursor
        var ring: Uri? = null
        if (cursor != null && cursor.moveToFirst()) {
            do {
                val ringtoneTitle = cursor.getString(RingtoneManager.TITLE_COLUMN_INDEX)
                if (ringtoneTitle != null && ringtoneTitle == title) {
                    val ringtonePosition = cursor.position
                    val ringtoneUri = ringtoneManager.getRingtoneUri(ringtonePosition)

                    // 处理铃声 URI
                    Log.d("Ringtone", "URI: $ringtoneUri")
                    ring = ringtoneUri

                    break
                }
            } while (cursor.moveToNext())
        }

        cursor.close()

        return ring
    }

}