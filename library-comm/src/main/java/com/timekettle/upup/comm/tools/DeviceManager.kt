package com.timekettle.upup.comm.tools

import co.timekettle.sip.utils.GsonUtil
import com.blankj.utilcode.util.GsonUtils
import com.google.gson.Gson
import com.google.gson.reflect.TypeToken
import com.timekettle.upup.base.utils.SpUtils
import com.timekettle.upup.base.utils.logD
import com.timekettle.upup.comm.constant.TmkProductType
import com.timekettle.upup.comm.model.DfuNotifyMsg
import java.util.*

/**
 *
 * @author: licoba
 * @date: 2022/7/11
 */
object DeviceManager {
    private const val USER_PRODUCT = "HomeUserProduct"
    private const val LOCAL_BLE_MAC = "本地BLE设备"
    private const val LOCAL_OTA_MSG = "LOCAL_OTA_MSG"
    private var FIRST_DEVICE_MAC = ""
    private const val MEETING_NICKNAME = "MEETING_NICKNAME" // 对方会议昵称
    private const val MEETING_LANGUAGE_CODE = "MEETING_FOREIGN_LANGUAGE_CODE" // 对方的上次使用的会议语言
    private const val Algorithm_Parameter = "Algorithm_Parameter"  // 耳机算法参数

    fun getUserProduct(): TmkProductType {
        return SpUtils.getParcelable(USER_PRODUCT, TmkProductType::class.java)
            ?: TmkProductType.UNKNOWN
    }

    //缓存设备mac地址
    fun saveBleMac(macAddress: String) {
        var macList = getBleMacList().toMutableList()
        if (macAddress in macList) return
        macList.add(macAddress)
    }

    fun getBleMacList(): List<String> {
        val jsonString = SpUtils.getString(LOCAL_BLE_MAC, "[]")
//        logD("本地的ble mac列表：${jsonString}")
        val macArray: Array<String> =
            Gson().fromJson(jsonString, Array<String>::class.java) // 数组形式
        return macArray.toList()
    }

    fun removeBleMac(macAddress: String) {
        var macList = getBleMacList().toMutableList()
        macList.remove(macAddress)
    }


    fun saveNotifyMsg(msg: DfuNotifyMsg) {
        SpUtils.putString(LOCAL_OTA_MSG, GsonUtils.toJson(msg))
    }

    fun removeNotifyMsg() {
        SpUtils.putString(LOCAL_OTA_MSG, "")
    }

    fun getNotifyMsg(): DfuNotifyMsg? {
        val localString = SpUtils.getString(LOCAL_OTA_MSG, "")
        if (localString.isEmpty()) return null
        return Gson().fromJson(localString, DfuNotifyMsg::class.java) // 数组形式
    }

    fun saveFirstMac(mac: String) {
        FIRST_DEVICE_MAC = mac
    }

    fun getFirstMac(): String {
        return FIRST_DEVICE_MAC
    }

    //保存会议使用的昵称
    fun saveMeetingNickname(nickName: String) {
        SpUtils.put(MEETING_NICKNAME, nickName);
    }

    fun getMeetingNickname(): String {
        return SpUtils.getString(MEETING_NICKNAME, "")
    }

    // 获取会议上次使用的语言
    fun getLastlyMeetingUseLanguage(): String {
        return SpUtils.getString(MEETING_LANGUAGE_CODE, TransLanguageTool.getDefaultSelfLan())
    }

    // 保存上次会议使用的语言
    fun saveLastlyMeetingUseLanguage(str: String) {
        SpUtils.put(MEETING_LANGUAGE_CODE, str);
    }

    // 保存算法参数
    fun saveAlgorithmParameter(parameter: Map<String, String>) {
        logD("保存算法参数${parameter}")
        val localResult = getAllAlgoParameter().toMutableMap()
        // 删除 localResult 中 parameter 的所有键
        parameter.keys.forEach { key ->
            localResult.remove(key)
        }
        // 将新的参数添加到 localResult 中
        localResult.putAll(parameter)
        val str = GsonUtil.toJson(localResult)
        SpUtils.putString(Algorithm_Parameter, str)
    }

    // 通过Mac地址获取算法参数
    fun getAlgoParameter(macAddress: String?): String? {
        if(macAddress == null) return null
//        logD("获取算法参数，耳机Mac地址 $macAddress")
        val localResult = getAllAlgoParameter()
        val ret = localResult[macAddress]
//        logD("$macAddress 获取到的算法参数：$ret")
        return ret
    }

    private fun getAllAlgoParameter(): Map<String, String> {
        val str = SpUtils.getString(Algorithm_Parameter, "")
        val type = object : TypeToken<Map<String, String>?>() {}.type
        return GsonUtil.fromJson(str, type) ?: hashMapOf()
    }
}

