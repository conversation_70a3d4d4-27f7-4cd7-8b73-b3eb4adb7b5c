package com.timekettle.upup.comm.widget

import android.content.BroadcastReceiver
import android.content.Context
import android.content.Intent
import android.content.IntentFilter
import android.os.BatteryManager
import android.os.Build
import android.util.AttributeSet
import android.view.LayoutInflater
import android.view.View
import androidx.constraintlayout.widget.ConstraintLayout
import androidx.constraintlayout.widget.ConstraintSet
import androidx.lifecycle.findViewTreeLifecycleOwner
import com.blankj.utilcode.util.ConvertUtils
import com.timekettle.upup.base.ktx.getAppViewModel
import com.timekettle.upup.base.ktx.gone
import com.timekettle.upup.base.ktx.visible
import com.timekettle.upup.base.utils.logD
import com.timekettle.upup.comm.R
import com.timekettle.upup.comm.databinding.CommWifiBatteryBinding
import com.timekettle.upup.comm.receiver.BatteryReceiver
import com.timekettle.upup.comm.viewmodel.VMWifiBattery

/**
 *
 * @Author:         licoba
 * @CreateDate:     2023/6/10 10:22
 * @Description:    Wifi+ 电池组合控件
 */


class WifiBatteryView @JvmOverloads constructor(
    mContext: Context,
    attrs: AttributeSet? = null,
    defStyleAttr: Int = 0
) : ConstraintLayout(mContext, attrs, defStyleAttr) {

    private val viewModel: VMWifiBattery by lazy { getAppViewModel() }
    private var binding: CommWifiBatteryBinding
    private var mBatteryDrawable: CustomBatteryDrawable
    private var currentPower = 60 // 当前电量
    private var isCharging = false // 是否正在充电

    init {
        val view = LayoutInflater.from(context)
            .inflate(R.layout.comm_wifi_battery, this)
        binding = CommWifiBatteryBinding.bind(view)
        mBatteryDrawable = CustomBatteryDrawable.fromContext(context)
        addBattery() // 添加电池图标
        updateBattery()  // 更新电量

    }


    // 更新电量（状态+值）
    private fun updateBattery() {
        try {
            val manager = context.getSystemService(Context.BATTERY_SERVICE) as BatteryManager
            currentPower =
                manager.getIntProperty(BatteryManager.BATTERY_PROPERTY_CAPACITY) ///当前电量百分比
            val status = manager.getIntProperty(BatteryManager.BATTERY_PROPERTY_STATUS)
            isCharging =
                status == BatteryManager.BATTERY_STATUS_CHARGING
        } catch (e: Exception) {
            e.printStackTrace()
        }
        mBatteryDrawable.setBatteryLevel(currentPower)
        mBatteryDrawable.setCharging(isCharging)
    }

    private fun addBattery() {
        mBatteryDrawable.apply {
            id = View.generateViewId()
            layoutParams = ConstraintLayout.LayoutParams(
                ConstraintLayout.LayoutParams.WRAP_CONTENT,
                ConstraintLayout.LayoutParams.WRAP_CONTENT,
            ).apply {
                endToEnd = ConstraintSet.PARENT_ID
                bottomToBottom = ConstraintSet.PARENT_ID
                topToTop = ConstraintSet.PARENT_ID
            }
        }
        binding.vEmptyBattery.addView(mBatteryDrawable)
    }


    private fun initData() {
    }


    // 处理新的数据
    private fun processData(s: String?) {
    }


    companion object {
        const val TAG = "WifiBatteryView"
    }


    override fun onAttachedToWindow() {
        super.onAttachedToWindow()
        viewModel.data.observe(findViewTreeLifecycleOwner()!!, ::processData)
        // ViewModel一定要在onAttachedToWindow之后才能初始化，不然ViewTreeViewModelStoreOwner.get(this)!!就会是空的
        initData()
        BatteryReceiver.addChangeListener(mBatteryListener)
    }


    override fun onDetachedFromWindow() {
        BatteryReceiver.removeChangeListener (mBatteryListener)
        super.onDetachedFromWindow()
        findViewTreeLifecycleOwner()?.let {
            viewModel.data.removeObservers(it)
        }
    }

    private val mBatteryListener = {
        updateBattery()
    }

    fun setWifiVisible(b: Boolean) {
        if (b) {
            binding.ifWifi.visible()
        } else {
            binding.ifWifi.gone()
        }
    }


    fun setWifiClickable(b: Boolean) {
        binding.ifWifi.setWifiClickable(b)
    }


}