package com.timekettle.upup.comm.tools

import android.content.BroadcastReceiver
import android.content.Context
import android.content.Intent
import android.content.IntentFilter
import android.view.KeyEvent
import co.timekettle.btkit.BleUtil
import com.timekettle.upup.base.ktx.getAppViewModel
import com.timekettle.upup.base.utils.EventBusUtils
import com.timekettle.upup.base.utils.logD
import com.timekettle.upup.base.utils.logE
import com.timekettle.upup.comm.model.BrokenReason
import com.timekettle.upup.comm.model.DrawerInOutEvent
import com.timekettle.upup.comm.model.HallInOutEvent
import com.timekettle.upup.comm.model.X1Status
import com.timekettle.upup.comm.utils.BtUtil
import com.timekettle.upup.comm.utils.HallUtil
import com.timekettle.upup.comm.utils.MyActivityUtil
import com.timekettle.upup.comm.viewmodel.VMTopDevice
import com.tmk.libserialhelper.serialport.TmkSerialHelper
import com.tmk.libserialhelper.tmk.util.VoltageControlUtil

/**
 * 全局的根据Hall开关去控制蓝牙扫描的类
 */
object HallScanManager {

    private const val TAG = "HallScanManager"
    private val vmTopDevice: VMTopDevice by lazy { getAppViewModel() }
    private const val KEY_EVENT_ACTION = "nstart.intent.action.SendKeyCode"
    var isScreenOn = true

    fun registerReceiver(context: Context) {
        context.registerReceiver(keyEventReceiver, IntentFilter(KEY_EVENT_ACTION))
    }

    private val keyEventReceiver: BroadcastReceiver = object : BroadcastReceiver() {
        override fun onReceive(p0: Context?, p1: Intent?) {
            val keyCode = p1?.getIntExtra("keycode", 0)
            when (keyCode) {
                KeyEvent.KEYCODE_F1 -> {
                    logD("出入仓事件：左耳入仓", TAG)
                    _onHallInOut(HallInOutEvent(isLeft = true, isIn = true))
                }

                KeyEvent.KEYCODE_F2 -> {
                    logD("出入仓事件：左耳出仓", TAG)
                    if (!MyActivityUtil.isUpgradingAty()) {
                        VoltageControlUtil.changeToChargeL()  // 出仓时，恢复电压，并开始扫描
                        startBleScan()
                    }
                    _onHallInOut(HallInOutEvent(isLeft = true, isIn = false))
                }

                KeyEvent.KEYCODE_F3 -> {
                    logD("出入仓事件：右耳入仓", TAG)
                    _onHallInOut(HallInOutEvent(isLeft = false, isIn = true))
                }

                KeyEvent.KEYCODE_F4 -> {
                    logD("出入仓事件：右耳出仓", TAG)
                    if (!MyActivityUtil.isUpgradingAty()) {
                        VoltageControlUtil.changeToChargeR()  // 出仓时，恢复电压，并开始扫描
                        startBleScan()
                    }
                    _onHallInOut(HallInOutEvent(isLeft = false, isIn = false))
                }

                KeyEvent.KEYCODE_F5 -> {
                    logD("抽屉事件：抽屉闭合", TAG)
                    EventBusUtils.postEvent(DrawerInOutEvent(isIn = true))
                    if (HallUtil.isLeftIn()) {
                        VoltageControlUtil.changeToChargeL()
                    }
                    if (HallUtil.isRightIn()) {
                        VoltageControlUtil.changeToChargeR()
                    }
                    if (HallUtil.isLeftRightIn())
                        stopBleScan()
                    TmkSerialHelper.cancelReadInfoFailJob(true)
                    TmkSerialHelper.cancelReadInfoFailJob(false)
                }

                KeyEvent.KEYCODE_F6 -> {
                    logD("抽屉事件：抽屉打开", TAG)
                    EventBusUtils.postEvent(DrawerInOutEvent(isIn = false))
                    if (HallUtil.isLeftIn()) {
                        VoltageControlUtil.changeToDisconnectL()
                    }
                    if (HallUtil.isRightIn()) {
                        VoltageControlUtil.changeToDisconnectR()
                    }
                    TmkSerialHelper.cancelReadInfoJob(isLeft = true, restoreToChange = false)
                    TmkSerialHelper.cancelReadInfoJob(isLeft = false, restoreToChange = false)
                    startBleScan()
                }

                else -> {
                }
            }
        }
    }


    fun startBleScan() {
        if (!isScreenOn) {
            logD("未处于亮屏状态，不开始BLE扫描", TAG)
            return
        }
        logD("开始BLE扫描", TAG)
        if (!BtUtil.isBluetoothEnabled()) {
            BtUtil.openBluetoothBySilence()
        }
        vmTopDevice.judgeStartScan()
    }

    fun judgeStartOrStopBleScan() {
        if (HallUtil.isLeftRightIn() && HallUtil.isBoxDrawerIn()) {
            logD("抽屉入盒，耳机在仓内，停止扫描", TAG)
            stopBleScan()
        } else {
            logD("抽屉在外面/耳机在仓外，开始扫描", TAG)
            startBleScan()
        }
    }


    private fun _onHallInOut(event: HallInOutEvent) {
        EventBusUtils.postEvent(event)
        if (!event.isIn) { //  出仓
            vmTopDevice.updateDeviceState(event.isLeft, X1Status.SearchConnecting)
            vmTopDevice.startConnTimeoutJob(event.isLeft)
            TmkSerialHelper.cancelReadInfoJob(event.isLeft)
        } else {  // 耳机入盒的时候，需要去读取一遍耳机的信息
            vmTopDevice.disConnectDeviceBle(event.isLeft)
            vmTopDevice.stopConnTimeoutJob(event.isLeft)
            if (!MyActivityUtil.isUpgradingAty()) { // 升级的时候千万不要去读取信息
                TmkSerialHelper.readHeadsetInfo(event.isLeft, onFailCallBack = {
                    vmTopDevice.updateDeviceState(it, X1Status.Broken.apply {
                        desc = BrokenReason.ReadInfoFail.reasonDesc
                    })
                    logE("${if (it) "左耳" else "右耳"}信息读取失败了")
                })
            }
        }
    }


    fun stopBleScan() {
        logD("停止BLE扫描", TAG)
        BleUtil.shared.stopScan()
    }

}