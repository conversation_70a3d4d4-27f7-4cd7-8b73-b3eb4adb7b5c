package com.timekettle.upup.comm.net.helper

import com.timekettle.upup.base.utils.showDebugToast
import com.timekettle.upup.base.utils.showToast
import com.timekettle.upup.comm.base.BaseResponse
import com.timekettle.upup.comm.net.helper.ResponseCodeEnum as ExceptionType

/**
 * 响应code异常统一处理
 *
 * 该方法主要做两件事:
 *
 * - 1.做统一的code码处理
 * - 2.未进行统一处理的code码会被转换为自定义异常[ResponseException]抛出
 *
 * 使用方式为：进行统一处理的异常进行抛出[ResponseEmptyException]，未进行处理的code抛出[ResponseException]
 *
 * @param code Int code码
 * @param msg String? 错误信息
 * @param successBlock suspend () -> Unit 没有异常的情况下执行的方法体 可以在此处进行数据的发射
 * @throws ResponseException 未进行处理的异常会进行抛出，让ViewModel去做进一步处理
 */
@JvmOverloads
@Throws(ResponseException::class)
suspend fun responseCodeExceptionHandler(
    success: Boolean,
    code: Int,
    msg: String?,
    successBlock: suspend () -> Unit = {}
) {
    // 进行异常的处理
    when (code) {
        // 100-处理失败
        ExceptionType.ERROR.getCode() -> {
            // 先弹Toast 再抛异常
            showToast(msg ?: ExceptionType.ERROR.getMessage())
            throw ResponseException(ExceptionType.ERROR, msg ?: "")
        }
        // 200-成功
        ExceptionType.SUCCESS.getCode() -> successBlock.invoke()
        // 2000-接口返回成功
        ExceptionType.RESP_SUCCESS.getCode() -> successBlock.invoke()
        else -> throw ResponseException(ExceptionType.ERROR, msg ?: "")
    }
}


@JvmOverloads
@Throws(ResponseException::class)
suspend fun <T> responseCodeExceptionHandler(
    response: BaseResponse<T>,
    successBlock: suspend () -> Unit = {}
) {
    if (response.success) {
        successBlock.invoke()
    } else {
        // 进行异常的处理
        when (response.reasonCode) {
            ExceptionType.ERROR.getCode() -> successBlock.invoke()
            ExceptionType.SUCCESS.getCode() -> successBlock.invoke()
            ExceptionType.RESP_SUCCESS.getCode() -> successBlock.invoke()
            else -> {
                // 先弹Toast 再抛异常
                showDebugToast(response.reason + "(" + response.reasonCode + ")")
                throw ReasonException(response.reasonCode ?: -1, response.reason ?: "no reason",response.data)
            }
        }
    }
}