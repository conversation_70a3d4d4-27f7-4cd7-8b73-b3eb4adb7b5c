package com.timekettle.upup.comm.widget;

import android.content.Context;
import android.content.res.TypedArray;
import android.util.AttributeSet;
import android.widget.ScrollView;

import com.blankj.utilcode.util.ConvertUtils;
import com.timekettle.upup.comm.R;

/**
 * <AUTHOR>
 * @date 2023/1/2 11:16
 * @email <EMAIL>
 * @desc 可以设置最大高度的scrollView
 * https://www.jianshu.com/p/57e7ebea91f4
 */
public class MaxHeightScrollView extends ScrollView {

    private int maxHeight;

    public MaxHeightScrollView(Context context) {
        this(context, null);
    }

    public MaxHeightScrollView(Context context, AttributeSet attrs) {
        this(context, attrs, 0);
    }

    public MaxHeightScrollView(Context context, AttributeSet attrs, int defStyleAttr) {
        super(context, attrs, defStyleAttr);

        TypedArray typedArray = context.obtainStyledAttributes(attrs, R.styleable.MaxHeightScrollView);
        maxHeight = typedArray.getInt(R.styleable.MaxHeightScrollView_mhsv_max_height,
             200);
        maxHeight = ConvertUtils.dp2px((maxHeight));
        typedArray.recycle();

    }

    @Override
    protected void onMeasure(int widthMeasureSpec, int heightMeasureSpec) {
        super.onMeasure(widthMeasureSpec, MeasureSpec.makeMeasureSpec(maxHeight, MeasureSpec.AT_MOST));
    }
}