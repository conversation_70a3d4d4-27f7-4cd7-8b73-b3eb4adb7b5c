package com.timekettle.upup.comm.utils

import android.annotation.SuppressLint
import android.content.Context
import android.media.AudioManager
import android.net.wifi.WifiManager
import android.os.Build
import android.os.Environment
import android.os.PowerManager
import android.provider.Settings
import android.util.Log
import com.blankj.utilcode.util.FileUtils
import com.blankj.utilcode.util.PathUtils
import com.blankj.utilcode.util.SPUtils
import com.timekettle.upup.base.BaseApp
import com.timekettle.upup.base.utils.SpUtils
import com.timekettle.upup.comm.constant.GlobalConstant
import com.timekettle.upup.comm.tools.AKSManager
import com.timekettle.upup.comm.tools.PKCS12Manager
import java.io.File
import java.util.Objects

object DeviceUtil {
    // 机器生产带的默认SN码，生产流程关键步骤所需信息，用于跳过认证步骤
    const val DEFAULT_SN = "X1KSN2025060415"

    fun getSerialNumber(): String {
        var serial = ""
        try {
            serial = Build.getSerial()
        } catch (e: Exception) {
            Log.d("DeviceUtil", "获取序列号失败: $e")
        }
        return serial
    }

    fun hasCert(): Boolean {
        val hasCert = AKSManager.isCertificateExists() && PKCS12Manager.isCertificateExists()
        return hasCert || getSerialNumber() == DEFAULT_SN
    }

    // 生成随机序列号，规则：12位，并且必须要大小写字母和数字
    fun generateRandomSerial(): String {
        val upperCase = "ABCDEFGHIJKLMNOPQRSTUVWXYZ"
        val lowerCase = "abcdefghijklmnopqrstuvwxyz"
        val numbers = "0123456789"
        // 确保至少包含一个大写字母、一个小写字母和一个数字
        val result = StringBuilder()
        result.append(upperCase.random())
        result.append(lowerCase.random())
        result.append(numbers.random())
        // 所有可能的字符
        val allChars = upperCase + lowerCase + numbers
        // 填充剩余的9个字符
        repeat(9) {
            result.append(allChars.random())
        }
        // 打乱字符顺序
        val finalResult = result.toString().toCharArray().apply { shuffle() }.joinToString("")
        Log.d("DeviceUtil", "生成的随机序列号: $finalResult")
        return finalResult
    }

    /**
     * 删除数据，并重启
     * @param context Context
     */
    fun resetReboot(context: Context) {
        resetData(context)
        reboot(context)
    }

    /**
     * 删除数据，并关机
     * @param context Context
     */
    fun resetShutdown(context: Context) {
        resetData(context)
        shutdown(context)
    }

    /**
     * 删除数据
     * @param context Context
     */
    fun resetData(context: Context) {
        deleteSp()
        deleteDatabase(context)
        deleteDirData(context)
        deleteHistoryData()
        resetSystemConfigs(context)
    }

    /**
     * 删除数据库
     */
    private fun deleteDatabase(context: Context) {
        context.deleteDatabase(GlobalConstant.TRANS_DB_NAME)
    }

    /**
     * 清空部分文件夹
     * data/data/{packageName}，过滤certs文件
     * Android/data/{packageName}
     * sdcard/ocr
     * sdcard/Download
     * sdcard/Document
     */
    private fun deleteDirData(context: Context) {
        try {
            // data/data沙箱目录删除
            val appDataDir = File(context.filesDir.getParent())
            val files = appDataDir.listFiles()
            if (files != null) {
                for (file in files) {
                    // 排除 certs 目录
                    if (file.name != "certs") {
                        FileUtils.delete(file)
                    }
                }
            }
            // Android/data目录
            val externalDataDir =
                Objects.requireNonNull<File>(BaseApp.context.getExternalFilesDir(null)).parentFile
            FileUtils.deleteAllInDir(externalDataDir)
            // ocr鉴权目录
            val ocrDir =
                File(Environment.getExternalStorageDirectory().absoluteFile.toString() + "/ocr")
            FileUtils.deleteAllInDir(ocrDir)
            // Download目录
            val downloadDir =
                Environment.getExternalStoragePublicDirectory(Environment.DIRECTORY_DOWNLOADS)
            FileUtils.deleteAllInDir(downloadDir)
            // Document目录
            val documentDir =
                Environment.getExternalStoragePublicDirectory(Environment.DIRECTORY_DOCUMENTS)
            FileUtils.deleteAllInDir(documentDir)
        } catch (e: java.lang.Exception) {
            e.printStackTrace()
        }
    }

    /**
     * 删除历史导出文件
     */
    private fun deleteHistoryData() {
        val dirList = listOf(
            PathUtils.getExternalStoragePath() + "/Timekettle X1",
            PathUtils.getExternalStoragePath() + "/WifiLog"
        )
        dirList.forEach {
            FileUtils.delete(it)
        }
    }

    /**
     * 删除SharePreference、mmkv
     */
    private fun deleteSp() {
        SPUtils.getInstance().clear()
        SpUtils.clearAll()
    }

    /**
     * 恢复系统设置
     * wifi、音量、休眠时间、亮度
     */
    @SuppressLint("ServiceCast")
    fun resetSystemConfigs(context: Context) {
        val wifiManager = context.getSystemService(Context.WIFI_SERVICE) as WifiManager?
        val audioManager = context.getSystemService(Context.AUDIO_SERVICE) as AudioManager?
        try {
            // 关闭adb
            Settings.Global.putInt(context.contentResolver, Settings.Global.ADB_ENABLED, 0)
            // 重置Wifi
            if (wifiManager != null) {
                val factoryResetMethod = wifiManager.javaClass.getMethod("factoryReset")
                factoryResetMethod.invoke(wifiManager)
                wifiManager.isWifiEnabled = true
            }
            if (audioManager != null) {
                // 设置系统音量
                audioManager.setStreamVolume(AudioManager.STREAM_MUSIC, 13, 0)
                // 设置铃声音量到最大
                val maxRingVolume = audioManager.getStreamMaxVolume(AudioManager.STREAM_RING)
                audioManager.setStreamVolume(AudioManager.STREAM_RING, maxRingVolume, 0)
            }
            // 休眠时间恢复
            Settings.System.putInt(BaseApp.context.contentResolver, Settings.System.SCREEN_OFF_TIMEOUT, 120000)
            // 亮度恢复
            Settings.System.putInt(BaseApp.context.contentResolver, Settings.System.SCREEN_BRIGHTNESS, 102)
        } catch (e: java.lang.Exception) {
            e.printStackTrace()
        }
    }

    /**
     * 重启
     */
    @SuppressLint("MissingPermission")
    fun reboot(context: Context) {
        (context.getSystemService(Context.POWER_SERVICE) as PowerManager).reboot(null)
    }

    /**
     * 关机
     * @param context Context
     */
    fun shutdown(context: Context) {
        try {
            // 1. 获取 PowerManager 实例
            val powerManager = context.getSystemService(Context.POWER_SERVICE) as PowerManager
            // 2. 反射调用 shutdown(confirm: Boolean, reason: String, wait: Boolean)
            val shutdownMethod = powerManager.javaClass.getMethod(
                "shutdown",
                Boolean::class.javaPrimitiveType,
                String::class.java,
                Boolean::class.javaPrimitiveType
            )
            // 3. 执行关机（参数：confirm=false, reason="Reflection Shutdown", wait=false）
            shutdownMethod.invoke(powerManager, false, "Reflection Shutdown", false)

        } catch (e: Exception) {
            e.printStackTrace()
            // 普通应用会抛出 SecurityException（缺少权限）
        }
    }

}