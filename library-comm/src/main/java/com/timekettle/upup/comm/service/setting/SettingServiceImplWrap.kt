package com.timekettle.upup.comm.service.setting

import co.timekettle.btkit.BleUtil
import com.alibaba.android.arouter.facade.annotation.Autowired
import com.alibaba.android.arouter.launcher.ARouter
import com.blankj.utilcode.util.SPUtils
import com.timekettle.upup.base.utils.SpUtils
import com.timekettle.upup.base.utils.logD
import com.timekettle.upup.comm.constant.RouteUrl
import com.timekettle.upup.comm.constant.TmkProductType
import com.timekettle.upup.comm.model.OfflineEligibility
import com.timekettle.upup.comm.service.home.HomeService

/**
 **  设置（Setting）模块对其它模块的接口暴露
 **
 */
object SettingServiceImplWrap {

    @Autowired(name = RouteUrl.Setting.SettingService)
    lateinit var service: SettingService

    fun testPrint() {
        service.testPrint()
    }


    fun initSerialPort() {
        service.initSerialPort()
    }

    fun updateBleVolume(value: Int? = null) {
        service.updateBleVolume(value)
    }

    fun getBleVolume(): Int {
        return service.getBleVolume()
    }


    fun startReadInfoJob() {
        service.startReadInfoJob()
    }


    fun cancelReadInfoJob() {
        service.cancelReadInfoJob()
    }


    init {
        logD("SettingServiceImplWrap 初始化")
        ARouter.getInstance().inject(this)
    }

}