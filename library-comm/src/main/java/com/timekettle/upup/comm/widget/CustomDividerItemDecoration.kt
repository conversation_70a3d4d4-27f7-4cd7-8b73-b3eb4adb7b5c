package com.timekettle.upup.comm.widget

import android.content.Context
import android.graphics.Canvas
import android.graphics.Paint
import android.graphics.Rect
import android.view.View
import androidx.core.content.ContextCompat
import androidx.recyclerview.widget.RecyclerView
import com.blankj.utilcode.util.ConvertUtils

/**
 * <AUTHOR>
 * @date 2022/8/4
 * @desc 参考DivierItemDecoration
 */
class CustomDividerItemDecoration(context: Context, marginHorizontal: Float = 16F) :
    RecyclerView.ItemDecoration() {

    private val dividerMarginHorizontal = ConvertUtils.dp2px(marginHorizontal)
    private val mPaint: Paint = Paint().apply {
        style = Paint.Style.STROKE
        strokeWidth = ConvertUtils.dp2px(0.5F).toFloat()
        strokeCap = Paint.Cap.ROUND
        color = ContextCompat.getColor(context, com.timekettle.upup.comm.R.color.comm_line3)
        isAntiAlias = true // 抗锯齿
    }

    private val mBounds = Rect()

    override fun onDraw(c: Canvas, parent: RecyclerView, state: RecyclerView.State) {
        super.onDraw(c, parent, state)

        c.save()
        val left: Int
        val right: Int
        //noinspection AndroidLintNewApi - NewApi lint fails to handle overrides.
        if (parent.clipToPadding) {
            left = parent.paddingLeft + dividerMarginHorizontal
            right = parent.width - parent.paddingRight - dividerMarginHorizontal
            c.clipRect(
                left, parent.paddingTop, right,
                parent.height - parent.paddingBottom
            )
        } else {
            left = dividerMarginHorizontal
            right = parent.width - dividerMarginHorizontal
        }

        val childCount = parent.childCount
        for (i in 0 until childCount - 1) {
            val child = parent.getChildAt(i)
            parent.getDecoratedBoundsWithMargins(child, mBounds)
            val bottom: Int = mBounds.bottom + Math.round(child.translationY)
            val top: Int = bottom - mPaint.strokeWidth.toInt()
            mBounds.set(left, top, right, bottom)
            c.drawRect(mBounds, mPaint)
        }
        c.restore()
    }

    override fun getItemOffsets(
        outRect: Rect,
        view: View,
        parent: RecyclerView,
        state: RecyclerView.State
    ) {
        super.getItemOffsets(outRect, view, parent, state)
        outRect.bottom = ConvertUtils.dp2px(0.5F)
    }
}