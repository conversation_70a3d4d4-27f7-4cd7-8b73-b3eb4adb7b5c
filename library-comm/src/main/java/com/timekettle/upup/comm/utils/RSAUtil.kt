package com.timekettle.upup.comm.utils

import android.util.Base64
import java.security.spec.PKCS8EncodedKeySpec
import javax.crypto.Cipher
import java.security.spec.X509EncodedKeySpec
import java.io.IOException
import java.security.*
import java.util.*

object RSAUtil {
    //    var privateKey: PrivateKey
//    var publicKey: PublicKey
    private const val PUBLIC_KEY =
        "MIGfMA0GCSqGSIb3DQEBAQUAA4GNADCBiQKBgQDZ1oZ2mgwr4UFiV5+S3ayZtUXhM2b9BwRuQXSL" +
                "/ZTLrLWLgYPVsY7pWGSWWhf3aK/OlU6uFPyv9uVu0qyLcdVrwG3fNmzxMLG7f7IgdIYhI9gDQPlC" +
                "jMMc0raqdmDv20ppQIYN3Mn4lXaS46iUINDGH4JVxury88poVsSuzU1EkQIDAQAB"
    private const val PRIVATE_KEY =
        "MIICeAIBADANBgkqhkiG9w0BAQEFAASCAmIwggJeAgEAAoGBANnWhnaaDCvhQWJXn5LdrJm1ReEz" +
                "Zv0HBG5BdIv9lMustYuBg9WxjulYZJZaF/dor86VTq4U/K/25W7SrItx1WvAbd82bPEwsbt/siB0" +
                "hiEj2ANA+UKMwxzStqp2YO/bSmlAhg3cyfiVdpLjqJQg0MYfglXG6vLzymhWxK7NTUSRAgMBAAEC" +
                "gYAQ5ulF+IvrN64YEE5sWdDTmFIUuCVMiqfYWjHNzt4ls3/elC0DfqRlDJu5YsIrSy8g60rQjQZS" +
                "LOj+YVBL/YgekIdorHXJ3cmDtgXnkKuyMYt8GZKXb0xY9vQQyC1TH1LQgl1YYW3DkTG6qCK87yFf" +
                "6T2qVJNhPEWgZVcHvWK1sQJBAPDy4j142I5zvecMqvtI5mYHd/yFXAjFxcbMR5FLhtO9tX33sJ1H" +
                "4871tguJiap7fSohu+AZ/iKyTC/YpeKHQWUCQQDnchEqxVxEbowxtj6E5+codbDSPMXyztvwoKU8" +
                "ecgL7sfwYGmxyDX0TTeSOURDkhlAaVURrHdgblDdMlyRcLm9AkEAu+hPdC1Yhc/R2+zPM2Vo+Uz9" +
                "0BuY6OaEFUOe1LsYDO24KuMOA7ra+L47GMhMan4f4MmqIV1kCl8Od4n35LAi2QJBANe1h2x+nz0k" +
                "b0OR4HkMjnQ2JIP9olxoEbMWdoqD0j/U38IYicnpmOfF5ApDl20GyMQwp9meDXDxwCo42u+uyV0C" +
                "QQCmVWWEIa5FEm1dDSTGyw5gTQDT9+cr3Z1/WJbdUuRwIQjVh/YRDFnV10vNFOTJ73nNkSjwKQ1p" +
                "r2Z6Y8JnrRv9"

    // convert String publickey to Key object
    @Throws(GeneralSecurityException::class, IOException::class)
    fun loadPublicKey(stored: String): Key {
        val data: ByteArray =
            Base64.decode(stored.toByteArray(), Base64.DEFAULT)
        val spec = X509EncodedKeySpec(data)
        val fact = KeyFactory.getInstance("RSA")
        return fact.generatePublic(spec)
    }

    // Encrypt using publickey
    @Throws(Exception::class)
    fun encrypt(plainText: String, publicKey: String = PUBLIC_KEY): String {
        val cipher = Cipher.getInstance("RSA/ECB/PKCS1Padding")
        cipher.init(Cipher.ENCRYPT_MODE, loadPublicKey(publicKey))
        return Base64.encodeToString(
            cipher.doFinal(plainText.toByteArray()),
            Base64.DEFAULT
        )
    }

    // Decrypt using privatekey
    @Throws(Exception::class)
    fun decrypt(encryptedText: String?, privateKey: String): String {
        val cipher = Cipher.getInstance("RSA/ECB/PKCS1Padding")
        cipher.init(Cipher.DECRYPT_MODE, loadPrivateKey(privateKey))
        return String(
            cipher.doFinal(
                Base64.decode(encryptedText, Base64.DEFAULT)
            )
        )
    }

    // Convert String private key to privateKey object
    @Throws(GeneralSecurityException::class)
    fun loadPrivateKey(key64: String): PrivateKey {
        val clear: ByteArray = Base64.decode(key64.toByteArray(), Base64.DEFAULT)
        val keySpec = PKCS8EncodedKeySpec(clear)
        val fact = KeyFactory.getInstance("RSA")
        val priv = fact.generatePrivate(keySpec)
        Arrays.fill(clear, 0.toByte())
        return priv
    }

//    init {
//        val keyGen = KeyPairGenerator.getInstance("RSA")
//        keyGen.initialize(1024)
//        val pair = keyGen.generateKeyPair()
//        privateKey = pair.private
//        publicKey = pair.public
//    }

    @Throws(Exception::class)
    fun test() {
        val secretText = "test"
        // Generate private and public key
//        val privateKey: String = Base64.encodeToString(RSAUtil2.privateKey.encoded, Base64.DEFAULT)
//        val publicKey: String = Base64.encodeToString(RSAUtil2.publicKey.encoded, Base64.DEFAULT)
//        println("Private Key: $privateKey")
//        println("Public Key: $publicKey")
        // Encrypt secret text using public key
        val encryptedValue = encrypt(secretText, PUBLIC_KEY)
        println("Encrypted Value: $encryptedValue")
        // Decrypt
        val decryptedText = decrypt(encryptedValue, PRIVATE_KEY)
        println("Decrypted output: $decryptedText")
    }
}