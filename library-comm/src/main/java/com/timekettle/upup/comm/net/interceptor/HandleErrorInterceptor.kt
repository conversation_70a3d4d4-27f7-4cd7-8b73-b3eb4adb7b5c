package com.timekettle.upup.comm.net.interceptor

import com.jeremyliao.liveeventbus.LiveEventBus
import com.timekettle.upup.base.utils.EventBusKey
import com.timekettle.upup.base.utils.logE
import com.timekettle.upup.comm.net.helper.ReasonCodeEnum
import com.timekettle.upup.comm.net.helper.ReasonException
import okhttp3.Response
import org.json.JSONObject

/**
 * 处理异常的拦截器，只有在success为true的情况下，才去解析data字段，否则直接抛出ReasonException
 * https://www.jianshu.com/p/3435c576cf70
 * @author: licoba
 * @date: 2022/6/1
 */
class HandleErrorInterceptor : ResponseBodyInterceptor() {

    override fun intercept(response: Response, url: String, body: String): Response {
        var jsonObject: JSONObject? = null
        try {
            jsonObject = JSONObject(body)
        } catch (e: Exception) {
            e.printStackTrace()
        }
        if (jsonObject != null) {
            // success字段为true，才去解析data里面的数据
            if (!jsonObject.optBoolean("success", false)) {

                if (jsonObject.optString("status") == "success") { // 兼容第三方API
                    return response
                }

                if (jsonObject.optString("timezone").isNotEmpty()) { // 兼容时区请求API
                    return response
                }


                logE(jsonObject.toString(), "请求未通过")

                val reasonCode = jsonObject.optInt("reasonCode", -1)
                when (reasonCode) {
                    ReasonCodeEnum.TokenEmpty.reasonCode,
                    ReasonCodeEnum.TokenInvalid.reasonCode,
                    ReasonCodeEnum.TokenExpired.reasonCode -> {
                        LiveEventBus.get<String>(EventBusKey.TO_LOGIN)
                            .post(EventBusKey.TOKEN_EXPIRED)
                    }
                    ReasonCodeEnum.RemotingLoginExpired.reasonCode -> {
                        LiveEventBus.get<String>(EventBusKey.TO_LOGIN)
                            .post(EventBusKey.LOGIN_EXPIRED)
                    }
                    else -> {
                        throw ReasonException(
                            reasonCode,
                            jsonObject.optString("reason", "no reason"),
                            jsonObject.opt("data"),
                            code = jsonObject.optInt("code", -1)
                        )
                    }
                }
            }
        }
        return response
    }
}
