package com.timekettle.upup.comm.utils

import com.timekettle.upup.comm.BuildConfig
import java.io.File
import java.security.cert.X509Certificate
import javax.security.auth.x500.X500Principal

/**
 * author: weiconglee
 **/
object CertificateUtils {

    const val TAG = "CertificateUtils"
    fun getIssuerCommonName(certificate: X509Certificate): String? {
        val issuerPrincipal: X500Principal = certificate.issuerX500Principal
        val issuerName = issuerPrincipal.name
        // 解析常用名称（CN）
        val str = issuerName.split(",")
            .find { it.trim().startsWith("CN=") }
            ?.substringAfter("CN=")
        return str
    }

    fun getSubjectCommonName(certificate: X509Certificate): String? {
        val subjectPrincipal: X500Principal = certificate.subjectX500Principal
        val subjectName = subjectPrincipal.name
        // 解析常用名称（CN）
        val str = subjectName.split(",")
            .find { it.trim().startsWith("CN=") }
            ?.substringAfter("CN=")
        return str
    }

    /**
     * 保存证书文件到本地
     * @param csrData String
     * @param fileName String
     */
    fun saveCertificate(csrData: String, fileName: String) {
        if (!BuildConfig.DEBUG) return
        val file = File("/sdcard/Download/$fileName")
        file.writeText(csrData)
    }
}