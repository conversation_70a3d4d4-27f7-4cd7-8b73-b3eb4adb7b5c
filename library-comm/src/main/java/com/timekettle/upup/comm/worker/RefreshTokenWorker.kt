package com.timekettle.upup.comm.worker

import android.content.Context
import androidx.work.CoroutineWorker
import androidx.work.WorkerParameters
import co.timekettle.agora.SpeechManager
import com.timekettle.upup.base.utils.logD
import com.timekettle.upup.comm.net.helper.BusinessManager
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.withContext

class RefreshTokenWorker(context: Context, params: WorkerParameters) : CoroutineWorker(context, params) {
    companion object {
        private const val TAG = "RefreshTokenWorker"
        const val FAIL_RETRY_INTERVAL = 10L
        const val MIN_SUCCESS_RETRY_INTERVAL = 5 * 60L
        const val WORK_NAME = "RefreshToken"
    }
    override suspend fun doWork(): Result {
        return withContext(Dispatchers.IO) {
            logD("任务开始：获取 business token", TAG)
            val tokenBean = BusinessManager.fetchAvailableToken()

            if (tokenBean != null) {
                val nextTime = if (tokenBean.expireIn >= 2 * MIN_SUCCESS_RETRY_INTERVAL) {
                    // 如果剩余时间足够减去 MIN_SUCCESS_RETRY_INTERVAL，则取 expireIn - MIN_SUCCESS_RETRY_INTERVAL
                    tokenBean.expireIn.toLong() - MIN_SUCCESS_RETRY_INTERVAL
                } else {
                    // 否则，直接使用 MIN_SUCCESS_RETRY_INTERVAL（确保最小间隔）
                    MIN_SUCCESS_RETRY_INTERVAL
                }
                logD("获取 business token 成功，${nextTime}s 后刷新", TAG)
                SpeechManager.setBusinessToken(tokenBean.token)
                BusinessManager.tokenFlow.emit(tokenBean.token)
                BusinessManager.submitTokenWorker(nextTime)
                Result.success()
            } else {
                logD("获取 business token 失败，${FAIL_RETRY_INTERVAL}s 后重试", TAG)
                BusinessManager.submitTokenWorker(FAIL_RETRY_INTERVAL)
                Result.failure()
            }
        }
    }
}