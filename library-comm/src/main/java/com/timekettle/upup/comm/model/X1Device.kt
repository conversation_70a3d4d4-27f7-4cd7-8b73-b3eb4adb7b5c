package com.timekettle.upup.comm.model

import co.timekettle.btkit.bean.WT2BlePeripheral
import com.tmk.libserialhelper.serialport.TmkSerial
import com.tmk.libserialhelper.tmk.bean.W3ProSerial

// X1设备的包裹类，X1的设备信息是由BLE设备+串口设备组合起来的
data class X1Device(
    var bleDevice: WT2BlePeripheral? = null,  // 是有可能没有设备的(有一只耳机设备丢了)
    var serialDevice: W3ProSerial? = null,   // 串口的设备
    var state: X1Status = X1Status.SearchConnecting,
    var x1Role: X1Role = X1Role.None
)

// X1耳机的状态
enum class X1Status(var desc: String) {
    None("未定义状态"),
    ChargingInBox("在充电盒中充电"),
    NeedTakeOut("需要取出"),
    SearchConnecting("已出盒，在搜索/连接BLE状态"),
    Connected("BLE已连接成功"),
    NeedUpdated("需要更新固件"),
    Broken("耳机异常！（连接超时、串口升级失败等）"),
}

enum class BrokenReason(var reasonDesc: String) {
    ReadInfoFail("读取耳机信息失败了"),
    UpgradeFailToBrick("升级过程中取出，变成砖头了！"),
    HeadsetOutWhenStartApp("开机时耳机已经在仓外了！"),
    ConnTimeOut("连接超时！超过10秒钟还没连接上"),
}

// X1耳机的左右，不管有没有，都会是固定的两个
enum class X1Role {
    Left, Right, None
}


