package com.timekettle.upup.comm.dialog;


import com.google.android.material.bottomsheet.BottomSheetDialog;

import android.app.Activity;
import android.content.Context;
import android.content.res.Resources;
import android.os.Bundle;
import android.util.DisplayMetrics;
import android.view.ViewGroup;

import androidx.annotation.NonNull;
import androidx.annotation.StyleRes;

import com.google.android.material.bottomsheet.BottomSheetDialog;
import com.timekettle.upup.base.utils.BarUtils;
import com.timekettle.upup.base.utils.StatusBarUtil;

/**
 * 禁止状态栏变色
 *
 * @author: licoba
 * @date: 2022/6/24
 */
public class FixedBottomSheetDialog extends BottomSheetDialog {

    public FixedBottomSheetDialog(@NonNull Context context) {
        super(context);
    }

    public FixedBottomSheetDialog(@NonNull Context context, @StyleRes int theme) {
        super(context, theme);
    }

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        int screenHeight = getScreenHeight(getOwnerActivity());
        int statusBarHeight = StatusBarUtil.getStatusBarHeight(getContext());
        int dialogHeight = screenHeight - statusBarHeight + BarUtils.getNavBarHeight();
        if (getWindow() != null) {
            getWindow().setLayout(ViewGroup.LayoutParams.MATCH_PARENT, dialogHeight == 0 ? ViewGroup.LayoutParams.MATCH_PARENT : dialogHeight);
        }
    }

    private static int getScreenHeight(Activity activity) {
        DisplayMetrics displaymetrics = new DisplayMetrics();
        activity.getWindowManager().getDefaultDisplay().getMetrics(displaymetrics);
        return displaymetrics.heightPixels;
    }


}
