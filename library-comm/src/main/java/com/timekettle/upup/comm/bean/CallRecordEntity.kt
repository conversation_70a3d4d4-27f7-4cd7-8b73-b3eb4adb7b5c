package com.timekettle.upup.comm.bean

import android.os.Parcelable
import androidx.room.Entity
import androidx.room.Ignore
import androidx.room.PrimaryKey
import kotlinx.parcelize.Parcelize
import java.util.Date

@Parcelize
@Entity
data class CallRecordEntity(
    @PrimaryKey
    var date: Date,
    var phoneNumber: String,
    var phoneType: Int, //(0去电，1来电)
    var phoneState: Int, //(0接通，1未接通)
    @Ignore var phoneMessages: MutableList<MsgBean> = mutableListOf() //消息列表
): Parcelable {
    constructor(date: Date, phoneNumber: String, phoneType: Int, phoneState: Int):this(date, phoneNumber, phoneType, phoneState, mutableListOf())
}