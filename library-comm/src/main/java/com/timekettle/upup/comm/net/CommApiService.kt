package com.timekettle.upup.comm.net

import com.timekettle.upup.comm.base.BaseResponse
import com.timekettle.upup.comm.model.DfuNotifyMsg
import com.timekettle.upup.comm.model.TimeZoneBean
import okhttp3.RequestBody
import retrofit2.Response
import retrofit2.http.*

/**
 * 公共接口代理类
 *
 * <AUTHOR>
 * @since 2022/04/15
 */
interface CommApiService {
    @GET("user/auth/refreshToken")
    suspend fun refreshToken(
        @Header("RefreshToken") refreshToken: String,
        @Query("userId") userId: Long,
    ): Response<BaseResponse<Any>>


    @GET("user/auth/refreshToken")
    fun refreshTokenSync(
        @Header("RefreshToken") refreshToken: String,
        @Query("userId") userId: Long,
    ): Response<BaseResponse<Any>>

    /**
     * 用户使用时长-提交
     */
    @Headers("Content-Type:application/json")
    @POST("user/userCount/save")
    suspend fun saveUserUsageDuration(@Body params: RequestBody): BaseResponse<Any>

    @POST("user/notify/dfu")
    suspend fun getDfuNotify(
        @Body params: RequestBody,
    ): BaseResponse<DfuNotifyMsg?>


    @GET("markov/user/w3pro/notify")
    suspend fun getW3ProDfuNotify(
        @Query("appVersion") appVersion: String, // 版本号
    ): BaseResponse<DfuNotifyMsg?>


    @GET("http://ip-api.com/json/?fields=timezone")
    suspend fun getTimeZone(
    ): Response<TimeZoneBean>

}