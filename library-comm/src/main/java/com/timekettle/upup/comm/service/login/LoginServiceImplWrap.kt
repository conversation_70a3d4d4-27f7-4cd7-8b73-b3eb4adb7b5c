package com.timekettle.upup.comm.service.login

import android.content.Context
import com.alibaba.android.arouter.facade.annotation.Autowired
import com.alibaba.android.arouter.launcher.ARouter
import com.timekettle.upup.comm.constant.RouteUrl
import com.timekettle.upup.comm.model.LoginMethod
import com.timekettle.upup.comm.model.UserBean

/**
 * 登录模块对其它模块的接口暴露，都通过 LoginServiceImplWrap 来调用
 * Create by licoba on 20202/4/25
 */
object LoginServiceImplWrap {

    @Autowired(name = RouteUrl.Login.LoginService) //  发现LoginService服务
    lateinit var service: LoginService

    init {
        ARouter.getInstance().inject(this)
    }

    fun isLogin(): Boolean {
        return service.isLogin()
    }

    fun getUserInfo(): UserBean? {
        return service.getUserInfo()
    }

    fun saveUser(userBean: UserBean) {
        return service.saveUser(userBean)
    }

    fun removeUserInfo() {
        service.removeUserInfo()
    }

    fun start(context: Context) {
        service.start(context)
    }

    fun getLoginMethod(): LoginMethod {
        return service.getLoginMethod()
    }

    fun startThirdLogin(loginMethod: LoginMethod) {
        service.startThirdLogin(loginMethod)
    }

    fun logout() {
        service.logout()
    }

    fun addOnLogoutListener(onLogoutListener: () -> Unit) {
        service.addOnLogoutListener(onLogoutListener)
    }

    fun isFishRechargeAvailable(): Boolean =
        service.isFishRechargeAvailable()

    fun setFishRechargeAvailable(isAvailable: Boolean) =
        service.setFishRechargeAvailable(isAvailable)


    fun isPolicyAgreed(): Boolean =
        service.isPolicyAgreed()

}