package com.timekettle.upup.comm.service.home

import com.alibaba.android.arouter.facade.template.IProvider
import com.timekettle.upup.comm.constant.TmkProductType
import com.timekettle.upup.comm.constant.TranslateMode
import com.timekettle.upup.comm.model.AccountBean
import com.timekettle.upup.comm.model.DfuNotifyMsg
import com.timekettle.upup.comm.model.IpBean

/**
 **  设备（Home）模块提供的服务
 **
 */
interface HomeService : IProvider {

    fun getUserProduct(): TmkProductType

//    fun getLastlyUseLanguageSelf(): String
//
//    fun getLastlyUseLanguageOther(): String

    fun saveUserModel(model: TranslateMode)

    fun getUserModel(): TranslateMode

    fun getDfuNotifyMsg(): DfuNotifyMsg?

    fun getSipAccount(): AccountBean?

    fun saveSipAccount(accountBean: AccountBean)

    fun saveFirstDeviceMac(mac: String)

    fun getFirstDeviceMac(): String

    fun setPhoneMode(value: Boolean)

    fun isPhoneMode(): Boolean

    fun getSipIp(): IpBean?

    fun saveSipIp(ipBean: IpBean)

    fun getPhoneState(): Int

    fun savePhoneState(state: Int)

    fun initBleConnect()

    fun setInMode(value: Boolean)

    fun isInMode(): Boolean

    class SettingType(
        electric: Int,
        volume: Int,
        light: Boolean
    )


}