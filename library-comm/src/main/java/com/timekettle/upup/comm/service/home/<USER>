package com.timekettle.upup.comm.service.home

import com.alibaba.android.arouter.facade.annotation.Autowired
import com.alibaba.android.arouter.launcher.ARouter
import com.timekettle.upup.comm.constant.RouteUrl
import com.timekettle.upup.comm.constant.TmkProductType
import com.timekettle.upup.comm.constant.TranslateMode
import com.timekettle.upup.comm.model.AccountBean
import com.timekettle.upup.comm.model.DfuNotifyMsg
import com.timekettle.upup.comm.model.IpBean

/**
 **  设备（Home）模块对其它模块的接口暴露
 **
 */

object HomeServiceImplWrap {

    @Autowired(name = RouteUrl.Home.HomeService)
    lateinit var service: HomeService

    init {
        ARouter.getInstance().inject(this)
    }

    fun getUserProduct(): TmkProductType {
        return service.getUserProduct()
    }

//    fun getLastlyUseLanguageSelf():String {
//        return  service.getLastlyUseLanguageSelf()
//    }
//
//    fun getLastlyUseLanguageOther():String{
//        return service.getLastlyUseLanguageOther()
//    }

    fun saveUserModel(model: TranslateMode) {
        return service.saveUserModel(model)
    }

    fun getUserModel() :TranslateMode{
        return service.getUserModel()
    }


    fun getDfuNotifyMsg() :DfuNotifyMsg?{
        return service.getDfuNotifyMsg()
    }

    fun getSipAccount() :AccountBean?{
        return service.getSipAccount()
    }

    fun saveSipAccount(accountBean: AccountBean){
        return service.saveSipAccount(accountBean)
    }

    fun saveFirstDeviceMac(mac: String) {
        service.saveFirstDeviceMac(mac)
    }

    fun getFirstDeviceMac(): String {
        return service.getFirstDeviceMac()
    }

    fun setPhoneMode(value: Boolean) {
        service.setPhoneMode(value)
    }

    fun isPhoneMode(): Boolean {
        return service.isPhoneMode()
    }

    fun getSipIp(): IpBean? {
        return service.getSipIp()
    }

    fun saveSipIp(ipBean: IpBean) {
        service.saveSipIp(ipBean)
    }

    fun getPhoneState(): Int {
        return service.getPhoneState()
    }

    fun savePhoneState(state: Int) {
        service.savePhoneState(state)
    }

    fun initBleConnect(){
        service.initBleConnect()
    }

    fun setInMode(value: Boolean) {
        service.setPhoneMode(value)
    }

    fun isInMode(): Boolean {
        return service.isPhoneMode()
    }

}