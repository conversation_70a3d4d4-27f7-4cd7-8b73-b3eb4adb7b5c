package com.timekettle.upup.comm.viewmodel

import co.timekettle.sip.utils.GsonUtil
import co.timekettle.speech.EngineHost
import com.blankj.utilcode.util.ActivityUtils
import com.google.gson.reflect.TypeToken
import com.timekettle.upup.base.mvvm.vm.BaseViewModel
import com.timekettle.upup.base.utils.SpUtils
import com.timekettle.upup.base.utils.logD
import com.timekettle.upup.base.utils.logE
import com.timekettle.upup.base.utils.showToast
import com.timekettle.upup.comm.constant.SpKey
import com.tmk.enginedetect.DetectionManager
import com.tmk.enginedetect.DetectionParams
import com.tmk.enginedetect.DetectionStatus
import com.tmk.enginedetect.checker.CheckItemType
import com.tmk.enginedetect.checker.SpeechTestLengthType
import com.tmk.enginedetect.resultbean.EngineDetectionNodeResult
import com.tmk.enginedetect.resultbean.HardwareInfoResult
import com.tmk.enginedetect.resultbean.NetworkInfoResult
import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import javax.inject.Inject

/**
 * 延迟检测 Viewmodel
 *
 * @author: Pengwei Wang
 * @date: 2024/8/14
 */
@HiltViewModel
class VMNetTest @Inject constructor() : BaseViewModel() {
    private val _logs = MutableStateFlow("")
    var logs: StateFlow<String> = _logs
    private lateinit var detectionManager: DetectionManager
    private var hardwareInfo: HardwareInfoResult? = null
    private var networkInfoResult: NetworkInfoResult? = null
    private var speechNodeResult: Map<CheckItemType, EngineDetectionNodeResult>? = null
    val checkStatus = MutableStateFlow(CheckStatus.None)

    fun clearLogs() {
        _logs.value = ""
    }

    fun doInit() {
        detectionManager = DetectionManager(ActivityUtils.getTopActivity().applicationContext)
        detectionManager.init()
        detectionManager.onHardwareInfoUpdated = {
            hardwareInfo = it
            _logs.value = buildLog()
        }
        detectionManager.onNetworkInfoUpdated = {
            networkInfoResult = it
            _logs.value = buildLog()
        }
        detectionManager.onEngineNodeUpdated = {
            speechNodeResult = it
            _logs.value = buildLog()
        }
        detectionManager.onError = {
            logE("检测失败: ${it.message}")
            showToast(it.message)
            checkStatus.value = CheckStatus.Error
        }
        detectionManager.onFinish = {
            _logs.value += "✅全部检测完成 \n"
            checkStatus.value = CheckStatus.Finished
            showToast("✅全部检测完成")
            logD(_logs.value)
//            if (curCheckCount.value < autoTestCount.value) {
//                showToast("第${curCheckCount.value}次检测完成！")
//                startCheck()
//            } else {
//                showToast("全部检测完成！")
//                curCheckCount.value = 0
//            }
        }
    }

    fun startCheck() {
        // 创建检测参数
        val checkParams = DetectionParams().apply {
            checkItemTypes.add(CheckItemType.SingleRecognize)
            checkItemTypes.add(CheckItemType.SingleTranslate)
            checkItemTypes.add(CheckItemType.SingleTTS)
            checkItemTypes.add(CheckItemType.SpeechTranslation)
            autoUploadReport = true     // 日志上传，暂时没用
            needCheckNetSpeed = true  // 是否需要检测网速
            speechTestLengthType = SpeechTestLengthType.SHORT
        }
        // 设置引擎
        val specificHost = SpUtils.getString(SpKey.TMK_ENGINE_HOST_IP, "")
        if (specificHost.isNotEmpty()) {
            checkParams.speechEngineHost = EngineHost(specificHost)
        } else {
            //查看ip列表缓存
            val type = object : TypeToken<ArrayList<EngineHost>>() {}.type
            val hosts: ArrayList<EngineHost> = GsonUtil.fromJson(SpUtils.getString(SpKey.CATCH_HOSTS,"[]"), type)
            if (hosts.isNotEmpty()) {
                checkParams.speechEngineHost = hosts[0]
            }
        }
        // 开始检测
        if (detectionManager.isChecking()) {
            showToast("请先停止检测")
            return
        }
        // 清除ViewModel自己的局部变量
        hardwareInfo = null
        networkInfoResult = null
        speechNodeResult = null
        detectionManager.startCheck(checkParams)
        checkStatus.value = CheckStatus.Checking
    }

    fun stopCheck() {
        detectionManager.stopCheck()
        checkStatus.value = CheckStatus.Finished
    }


    private fun buildLog(
        hardwareInfo: HardwareInfoResult? = this.hardwareInfo,
        networkInfoResult: NetworkInfoResult? = this.networkInfoResult,
        speechNodeResult: Map<CheckItemType, EngineDetectionNodeResult>? = this.speechNodeResult
    ): String {
        val sb = StringBuilder()
        sb.append("========================================\n")
        sb.append("检测库版本：${detectionManager.version}\n")
        sb.append("========================================\n\n\n")
        if (hardwareInfo != null) {
            sb.append("============ 硬件信息 Start ===========\n")
            sb.append(hardwareInfo.printInfo())
            if (hardwareInfo.isCheckDone()) sb.append("============ 硬件信息 End ===========\n")
            sb.append("\n\n")
        }
        if (networkInfoResult != null) {
            sb.append("=========== 网络信息 Start ===========\n")
            sb.append(networkInfoResult.printInfo())
            if (networkInfoResult.isCheckDone()) sb.append("============ 网络信息 End ============\n")
            sb.append("\n\n")
        }
        if (speechNodeResult != null) {
            sb.append("=========== Speech库 Start ===========\n")
            sb.append(speechNodeResult.printEngineNodeInfo())
            if (speechNodeResult.values.all { it.isCheckDone() }) sb.append("=========== Speech库 End ============\n")
            sb.append("\n\n")
        }
        return sb.toString()
    }


    private fun Map<CheckItemType, EngineDetectionNodeResult>.printEngineNodeInfo(): String {
        val sb = StringBuilder()
        if (this[CheckItemType.SingleRecognize]?.hasResult() == true) {
            sb.append("[${CheckItemType.SingleRecognize.checkName}]测试结果：\n")
            sb.append("${this[CheckItemType.SingleRecognize]?.printInfo()}")
            if (this[CheckItemType.SingleRecognize]?.detectionStatus == DetectionStatus.Finished) {
                sb.append("第一次收到响应耗时：${this[CheckItemType.SingleRecognize]?.costTimeFirstReceive}ms\n")
                sb.append("全部响应完成总耗时：${this[CheckItemType.SingleRecognize]?.costTimeTotal}ms\n")
                sb.append("----------------------------------------------\n")
            }
        }
        if (this[CheckItemType.SingleTranslate]?.hasResult() == true) {
            sb.append("[${CheckItemType.SingleTranslate.checkName}]测试结果：\n")
            sb.append("${this[CheckItemType.SingleTranslate]?.printInfo()}")
            if (this[CheckItemType.SingleTranslate]?.detectionStatus == DetectionStatus.Finished) {
                sb.append("第一次收到响应耗时：${this[CheckItemType.SingleTranslate]?.costTimeFirstReceive}ms\n")
                sb.append("全部响应完成总耗时：${this[CheckItemType.SingleTranslate]?.costTimeTotal}ms\n")
                sb.append("----------------------------------------------\n")
            }
        }
        if (this[CheckItemType.SingleTTS]?.hasResult() == true) {
            sb.append("[${CheckItemType.SingleTTS.checkName}]测试结果：\n")
            sb.append("${this[CheckItemType.SingleTTS]?.printInfo()}")
            if (this[CheckItemType.SingleTTS]?.detectionStatus == DetectionStatus.Finished) {
                sb.append("第一次收到响应耗时：${this[CheckItemType.SingleTTS]?.costTimeFirstReceive}ms\n")
                sb.append("全部响应完成总耗时：${this[CheckItemType.SingleTTS]?.costTimeTotal}ms\n")
                sb.append("----------------------------------------------\n")
            }
        }
        if (this[CheckItemType.SpeechTranslation]?.hasResult() == true) {
            sb.append("[${CheckItemType.SpeechTranslation.checkName}]测试结果：\n")
            sb.append("${this[CheckItemType.SpeechTranslation]?.printInfo()}")
            if (this[CheckItemType.SpeechTranslation]?.detectionStatus == DetectionStatus.Finished) {
                sb.append("第一次收到响应耗时：${this[CheckItemType.SpeechTranslation]?.costTimeFirstReceive}ms\n")
                sb.append("全部响应完成总耗时：${this[CheckItemType.SpeechTranslation]?.costTimeTotal}ms\n")
                sb.append("----------------------------------------------\n")
            }
        }
        return sb.toString()
    }


    enum class CheckStatus(var showText: String) {
        None("未开始　"),
        Checking("检测中　"),
        Error("检测错误"),
        Finished("检测完成")
    }
}