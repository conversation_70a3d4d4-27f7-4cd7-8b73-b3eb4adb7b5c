package com.timekettle.upup.comm.di

import com.chuckerteam.chucker.api.ChuckerInterceptor
import com.timekettle.upup.base.BaseApp
import com.timekettle.upup.base.BuildConfig
import com.timekettle.upup.base.constant.VersionStatus
import com.timekettle.upup.base.utils.SpUtils
import com.timekettle.upup.comm.constant.NetUrl
import com.timekettle.upup.comm.constant.SpKey
import com.timekettle.upup.comm.net.interceptor.HandleErrorInterceptor
import com.timekettle.upup.comm.net.interceptor.TokenInterceptor
import dagger.Module
import dagger.Provides
import dagger.hilt.InstallIn
import dagger.hilt.components.SingletonComponent
import okhttp3.OkHttpClient
import okhttp3.logging.HttpLoggingInterceptor
import retrofit2.Retrofit
import retrofit2.converter.gson.GsonConverterFactory
import java.util.concurrent.TimeUnit
import javax.inject.Singleton

/**
 * 全局作用域的网络层的依赖注入模块
 *
 * <AUTHOR>
 * @since 2022/04/15
 */
@Module
@InstallIn(SingletonComponent::class)
class DINetworkModule {

    /**
     * [OkHttpClient]依赖提供方法
     *  https://www.jianshu.com/p/3435c576cf70 后台返回的data数据类型不一致
     *  办法：在 gson 解析之前拿到 ResponseBody
     * @return OkHttpClient
     */
    @Singleton
    @Provides
    fun provideLocalOkHttpClient(): OkHttpClient {
        // 日志拦截器部分
        val level =
            if (BuildConfig.VERSION_TYPE != VersionStatus.RELEASE) HttpLoggingInterceptor.Level.BODY else HttpLoggingInterceptor.Level.NONE
        val logInterceptor = HttpLoggingInterceptor().setLevel(level)

        return OkHttpClient.Builder()
            .addInterceptor(logInterceptor)
            .addInterceptor(TokenInterceptor())
            .addInterceptor(HandleErrorInterceptor())
            .addInterceptor(ChuckerInterceptor(BaseApp.context))
            .connectTimeout(10L * 1000L, TimeUnit.MILLISECONDS)
            .readTimeout(10L * 1000L, TimeUnit.MILLISECONDS)
            .retryOnConnectionFailure(true)
            .build()
    }

    /**
     * [Retrofit]依赖提供方法
     * @param okHttpClient OkHttpClient OkHttp客户端
     * @return Retrofit
     */
    @Singleton
    @Provides
    fun provideLocalRetrofit(okHttpClient: OkHttpClient): Retrofit {
        return try {
            getRetrofit(okHttpClient)
        } catch (e: Exception) {
            e.printStackTrace()
            SpUtils.putString(SpKey.BASE_URL, NetUrl.RELEASE_URL)
            getRetrofit(okHttpClient)
        }
    }

    private fun getRetrofit(okHttpClient: OkHttpClient): Retrofit {
        return Retrofit.Builder()
            .baseUrl(SpUtils.getString(SpKey.BASE_URL, NetUrl.RELEASE_URL))
            .addConverterFactory(GsonConverterFactory.create())
            .client(okHttpClient)
            .build()
    }

}