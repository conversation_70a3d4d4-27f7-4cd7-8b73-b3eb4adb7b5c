package com.timekettle.upup.comm.constant

/**
 * 本地存储的键 放在此类中
 *
 * <AUTHOR>
 * @since 2022/04/15
 */
object CommIntentKey{
    const val RouteUrl = "RouteUrl"   // RouteUrl
    const val CURRENT_PRODUCT = "CURRENT_PRODUCT"  // 当前产品，传到离线购买页面的时候会用到
    const val SELECTED_COUPON_ID = "SELECTED_COUPON_ID" // 优惠券列表点击"去使用"优惠券的ID
    const val FROM_PAGE = "FROM_PAGE"  // 从哪个页面来、从哪个页面跳转的
    const val CURRENT_MODE = "CURRENT_MODE"  // 当前模式，公共key，跳转用


    const val KEY_PERMISSION = "KEY_PERMISSION" //textspan中跳转时的传递标识
    const val USER_USAGE_AGREEMENT = "USER_USAGE_AGREEMENT"//用户使用协议
    const val PRIVACY_POLICY= "PRIVACY_POLICY"//隐私政策

    const val POLICY_TO_TYPE = "POLICY_TO_TYPE" //同意隐私政策后需要跳转到哪里
}

object Constant {
    const val MeetingUserName="user"//初始化默认的昵称
    var isMeeting = false //是否在会议模式
}