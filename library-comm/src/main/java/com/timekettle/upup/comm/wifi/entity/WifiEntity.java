package com.timekettle.upup.comm.wifi.entity;

import android.content.Context;
import android.net.wifi.ScanResult;
import android.net.wifi.WifiConfiguration;
import android.text.TextUtils;

import java.util.List;

public class WifiEntity {

    public String name;
    public String SSID;
    public boolean isEncrypt;
    public boolean isSaved;
    public boolean isConnected;
    public String encryption;
    public String description;
    public String capabilities;
    public String ip;
    public String state;
    public int level;

    public static WifiEntity create(Context context, ScanResult result, List<WifiConfiguration> configurations, String connectedSSID, int ipAddress) {

        if (TextUtils.isEmpty(result.SSID)) return null;

        WifiEntity wifi = new WifiEntity();
        wifi.isConnected = false;
        wifi.isSaved = false;
        wifi.name = result.SSID;
        wifi.SSID = "\"" + result.SSID + "\"";
        wifi.isConnected = wifi.SSID.equals(connectedSSID);
        wifi.capabilities = result.capabilities;
        wifi.isEncrypt = true;
        wifi.encryption = "";
        wifi.level = result.level;
        wifi.ip = wifi.isConnected ? String.format("%d.%d.%d.%d", (ipAddress & 0xff), (ipAddress >> 8 & 0xff), (ipAddress >> 16 & 0xff), (ipAddress >> 24 & 0xff)) : "";

        if (wifi.capabilities.toUpperCase().contains("WPA2-PSK") && wifi.capabilities.toUpperCase().contains("WPA-PSK")) {
            wifi.encryption = "WPA/WPA2";
        } else if (wifi.capabilities.toUpperCase().contains("WPA-PSK")) {
            wifi.encryption = "WPA";
        } else if (wifi.capabilities.toUpperCase().contains("WPA2-PSK")) {
            wifi.encryption = "WPA2";
        } else {
            wifi.isEncrypt = false;
        }

//        wifi.description = wifi.encryption;

        for (WifiConfiguration configuration : configurations) {
            if (configuration.SSID.equals(wifi.SSID)) {
                wifi.isSaved = true;
                break;
            }
        }
        if (wifi.isSaved) {
//            wifi.description = "已保存";
            wifi.state = context.getString(com.timekettle.upup.comm.R.string.common_saved);
        }
        if (wifi.isConnected) {
//            wifi.description = "已连接";
            wifi.state = context.getString(com.timekettle.upup.comm.R.string.common_connected);
        }

        return wifi;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getSSID() {
        return SSID;
    }

    public void setSSID(String SSID) {
        this.SSID = SSID;
    }

    public boolean isEncrypt() {
        return isEncrypt;
    }

    public void setEncrypt(boolean encrypt) {
        isEncrypt = encrypt;
    }

    public boolean isSaved() {
        return isSaved;
    }

    public void setSaved(boolean saved) {
        isSaved = saved;
    }

    public boolean isConnected() {
        return isConnected;
    }

    public void setConnected(boolean connected) {
        isConnected = connected;
    }

    public String getEncryption() {
        return encryption;
    }

    public void setEncryption(String encryption) {
        this.encryption = encryption;
    }

    public String getDescription() {
        return description;
    }

    public void setDescription(String description) {
        this.description = description;
    }

    public String getCapabilities() {
        return capabilities;
    }

    public void setCapabilities(String capabilities) {
        this.capabilities = capabilities;
    }

    public String getIp() {
        return ip;
    }

    public void setIp(String ip) {
        this.ip = ip;
    }

    public String getState() {
        return state;
    }

    public void setState(String state) {
        this.state = state;
    }

    public int getLevel() {
        return level;
    }

    public void setLevel(int level) {
        this.level = level;
    }

    public WifiEntity merge(WifiEntity merge) {
        isSaved = merge.isSaved();
        isConnected = merge.isConnected();
        ip = merge.ip;
        state = merge.state;
        level = merge.level;
        description = merge.description;
        return this;
    }

    @Override
    public boolean equals(Object obj) {
        if (obj == null || (!(obj instanceof WifiEntity))) return false;
        return ((WifiEntity) obj).SSID.equals(this.SSID);
    }

    @Override
    public String toString() {
        return "WifiEntity{" +
                "name='" + name + '\'' +
                ", SSID='" + SSID + '\'' +
                ", isEncrypt=" + isEncrypt +
                ", isSaved=" + isSaved +
                ", isConnected=" + isConnected +
                ", encryption='" + encryption + '\'' +
                ", description='" + description + '\'' +
                ", capabilities='" + capabilities + '\'' +
                ", ip='" + ip + '\'' +
                ", state='" + state + '\'' +
                ", level=" + level +
                '}';
    }
}
