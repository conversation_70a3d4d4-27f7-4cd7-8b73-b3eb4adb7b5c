package com.timekettle.upup.comm.widget

import android.content.Context
import android.util.Log
import android.view.Gravity
import android.view.View
import co.timekettle.sip.call.CallManager
import com.timekettle.upup.comm.R
import com.timekettle.upup.comm.databinding.LayoutCallComingPopBinding
import com.timekettle.upup.comm.service.trans.TransServiceImplWrap
import com.timekettle.upup.comm.tools.TransLanguageTool
import razerdp.basepopup.BasePopupWindow

class PhoneComingPop(var context: Context, var dstCode: String, var account: String) :
    BasePopupWindow(context) {

    lateinit var mBinding: LayoutCallComingPopBinding
    var selfLang = TransServiceImplWrap.getPhoneModelLang()

    init {
        popupGravity = Gravity.CENTER
        isOutSideTouchable = true
        setContentView(R.layout.layout_call_coming_pop)
        setOutSideDismiss(true)
        setBackPressEnable(true)
    }

    override fun onViewCreated(contentView: View) {
        mBinding = LayoutCallComingPopBinding.bind(contentView)
        Log.d("sadfasdfasdfer", "selfLang $selfLang")
        Log.d("sadfasdfasdfer", "dstCode $dstCode")
        mBinding.tvSelfLang.text = TransLanguageTool.getLanguageNameByCode(selfLang)
        mBinding.tvOtherLang.text =  TransLanguageTool.getLanguageNameByCode(dstCode)
        mBinding.tvPhoneNumber.text = account

        mBinding.ivCallingRefuse.setOnClickListener {
            //挂断
            CallManager.hangupCall()
            this.dismiss()
        }

        mBinding.ivCallingListen.setOnClickListener {
            //接听
            CallManager.accept()

        }
    }

}