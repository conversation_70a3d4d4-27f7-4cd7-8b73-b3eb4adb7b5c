package com.timekettle.upup.comm.widget

import android.content.Context
import android.text.TextUtils

import android.util.AttributeSet


/**
 * 跑马灯TextView
 */

class MarqueeTextView : androidx.appcompat.widget.AppCompatTextView {
    constructor(context: Context) : super(context) {
        initView(context)
    }

    constructor(context: Context, attrs: AttributeSet?) : super(context, attrs) {
        initView(context)
    }

    constructor(context: Context, attrs: AttributeSet?, defStyleAttr: Int) : super(
        context,
        attrs,
        defStyleAttr
    ) {
        initView(context)
    }

    private fun initView(context: Context) {
        this.ellipsize = TextUtils.TruncateAt.MARQUEE
        this.isSingleLine = true
        this.marqueeRepeatLimit = -1
    }

    //最关键的部分
    override fun isFocused(): <PERSON><PERSON><PERSON> {
        return true
    }
}

