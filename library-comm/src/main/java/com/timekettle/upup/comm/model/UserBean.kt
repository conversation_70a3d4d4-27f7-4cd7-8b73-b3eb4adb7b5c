package com.timekettle.upup.comm.model

import android.os.Parcelable
import kotlinx.android.parcel.Parcelize


/**
 * 用户bean类
 * @author: licoba
 * @date: 2022/4/25
 */
@Parcelize
data class UserBean(
    val authCode: String?,
    val authToken: String?,
    val avatar: String?,
    val code: String?,
    val email: String?,
    val gmtCreate: String?,
    val gmtModify: String?,
    val id: Long,
    val isDelete: Boolean?,
    val nickname: String?,
    val optType: String?,
    val password: String?,
    val registerType: Int,
    val type: Int,
    val gender: Int,
    val thirdUni: String,
    val thirdUniHash: Long,
    val logoffToken: String,
    val imId: String?,
    val scene: String? // 用户使用场景 1.旅游 2.导游 3.线下会议 4.远程会议 5.语言学习 6.留学 7.教学 8.日常工作 9.家庭交流 10.交友 11.其他
) : Parcelable {}