package com.timekettle.upup.comm.widget

import android.content.Context
import android.content.res.Resources
import android.graphics.Canvas
import android.util.AttributeSet
import android.view.View
import androidx.constraintlayout.widget.ConstraintLayout
import com.blankj.utilcode.util.ConvertUtils

/**
 *
 * @author: licoba
 * @date: 2022/9/22
 *
 *
 *
 *
 */


class EmptyHeaderView @JvmOverloads constructor(
    context: Context,
    private val heightDpValue: Float = 8f
) : ConstraintLayout(context, null, 0) {

    override fun onMeasure(widthMeasureSpec: Int, heightMeasureSpec: Int) {
        super.onMeasure(widthMeasureSpec, heightMeasureSpec)
        setMeasuredDimension(1,dp2px(heightDpValue))
    }

    private fun dp2px(dpValue: Float): Int {
        val scale = Resources.getSystem().displayMetrics.density
        return (dpValue * scale + 0.5f).toInt()
    }
}