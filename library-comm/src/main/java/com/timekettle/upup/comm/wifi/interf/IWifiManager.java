package com.timekettle.upup.comm.wifi.interf;

import com.timekettle.upup.comm.wifi.entity.WifiEntity;

import java.util.List;

public interface IWifiManager {

    boolean isOpened();

    void openWifi();

    void closeWifi();

    void scanWifi();

    boolean disConnectWifi();

    boolean connectEncryptWifi(WifiEntity wifi, String password);

    boolean connectSavedWifi(WifiEntity wifi);

    boolean connectOpenWifi(WifiEntity wifi);

    boolean removeWifi(WifiEntity wifi);

    List<WifiEntity> getWifi();

    void setOnWifiConnectListener(OnWifiConnectListener onWifiConnectListener);

    void setOnWifiStateChangeListener(OnWifiStateChangeListener onWifiStateChangeListener);

    void setOnWifiChangeListener(OnWifiChangeListener onWifiChangeListener);

    void destroy();

    int getWifiState();
    
}
