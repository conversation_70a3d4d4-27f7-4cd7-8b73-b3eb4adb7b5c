package com.timekettle.upup.comm.widget

import android.content.Context
import android.content.Intent
import android.net.ConnectivityManager
import android.net.NetworkInfo
import android.net.wifi.SupplicantState
import android.net.wifi.WifiInfo
import android.net.wifi.WifiManager
import android.provider.Settings
import android.util.AttributeSet
import android.view.LayoutInflater
import androidx.constraintlayout.widget.ConstraintLayout
import com.blankj.utilcode.util.NetworkUtils
import com.timekettle.upup.base.BaseApp
import com.timekettle.upup.base.ktx.clickDelay
import com.timekettle.upup.base.ktx.gone
import com.timekettle.upup.base.ktx.invisible
import com.timekettle.upup.base.ktx.visible
import com.timekettle.upup.base.utils.logD
import com.timekettle.upup.comm.R
import com.timekettle.upup.comm.databinding.TkStatusBarWifiBinding
import com.timekettle.upup.comm.utils.MyNetWorkUtil
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.Job
import kotlinx.coroutines.delay
import kotlinx.coroutines.launch
import kotlinx.coroutines.withContext


/**
 * 自定义Wifi控件
 */
class CustomWifiWidget @JvmOverloads constructor(
    context: Context, attrs: AttributeSet? = null, defStyleAttr: Int = 0,
    defStyleRes: Int = 0
) : ConstraintLayout(context, attrs, defStyleAttr, defStyleRes) {
    private var binding: TkStatusBarWifiBinding
    private val wifiManager: WifiManager =
        context.applicationContext.getSystemService(Context.WIFI_SERVICE) as WifiManager
    private var jobRefresh: Job = Job()
    private var whileFlag: Boolean = true
    private var wifiClickable: Boolean = true


    // 在协程中周期性地执行任务
    private suspend fun doTaskOnInterval(intervalMillis: Long, task: suspend () -> Unit) {
        while (whileFlag) { // 无限循环
            task() // 执行任务
            delay(intervalMillis) // 等待指定时间间隔
        }
    }


    init {
        val view = LayoutInflater.from(context)
            .inflate(R.layout.tk_status_bar_wifi, this)
        binding = TkStatusBarWifiBinding.bind(view)
        CoroutineScope(Dispatchers.IO).launch(jobRefresh) {
            doTaskOnInterval(1000L) { updateWifiStatus() }
        }
        binding.ivWifiOff.invisible()
//        binding.ivWifiOff.setOnClickListener {
//            context.startActivity(Intent(Settings.ACTION_WIFI_SETTINGS))
//        }
//        binding.ivWifi.setOnClickListener {
//            context.startActivity(Intent(Settings.ACTION_WIFI_SETTINGS))
//        }
        binding.root.clickDelay {
            context.startActivity(Intent(Settings.ACTION_WIFI_SETTINGS))
        }
    }


    fun setWifiClickable(b: Boolean) {
        wifiClickable = b
    }

    /**
     * 判断WiFi的开关是否是打开的
     */
    private fun isWifiOpen(): Boolean {
        val wifiManager = BaseApp.context.getSystemService(Context.WIFI_SERVICE) as WifiManager
        return wifiManager.isWifiEnabled
    }

    private suspend fun updateWifiStatus() {
        withContext(Dispatchers.IO) {
            val status = getWifiStatus()
//            logD("更新WiFi图标, Wifi状态：$status")
            withContext(Dispatchers.Main) {
                binding.ivWifi.setImageResource(getWifiIcon(status))
                when (status) {
                    WifiStatus.DISABLED -> {
                        binding.ivWifi.visibility = INVISIBLE
                        binding.ivWifiOff.visibility = VISIBLE
                        binding.lottieConnecting.gone()
                    }

                    WifiStatus.CONNECTING -> {
                        binding.lottieConnecting.visible()
                        binding.ivWifi.visibility = INVISIBLE
                        binding.ivWifiOff.visibility = INVISIBLE
                    }

                    else -> {
                        binding.ivWifi.visibility = VISIBLE
                        binding.ivWifiOff.visibility = INVISIBLE
                        binding.lottieConnecting.gone()
                    }
                }
            }
        }
    }


    private fun getWifiIcon(wifiStatus: WifiStatus): Int {
        return when (wifiStatus) {
            WifiStatus.ENABLED -> WifiIcons.QS_WIFI_NO_NETWORK
            WifiStatus.DISABLED -> WifiIcons.WIFI_NO_NETWORK
            WifiStatus.CONNECTED_HAS_INTERNET -> getConnectedWifiIcon()
            WifiStatus.CONNECTED_NO_INTERNET -> getConnectedNoInternetWifiIcon()
            WifiStatus.CONNECTING -> getConnectedNoInternetWifiIcon()
        }
    }

    private fun getConnectedWifiIcon(): Int {
        val wifiLevel = getWifiSignalLevel()// 获取Wi-Fi信号强度的逻辑，根据具体情况实现
        return if (wifiLevel in 0 until WifiIcons.WIFI_LEVEL_COUNT) {
            WifiIcons.WIFI_FULL_ICONS[wifiLevel]
        } else {
            WifiIcons.WIFI_FULL_ICONS.last()
        }
    }

    private fun getConnectedNoInternetWifiIcon(): Int {
        val wifiLevel = getWifiSignalLevel() // 获取Wi-Fi信号强度的逻辑，根据具体情况实现
        return if (wifiLevel in 0 until WifiIcons.WIFI_LEVEL_COUNT) {
            WifiIcons.WIFI_NO_INTERNET_ICONS[wifiLevel]
        } else {
            WifiIcons.WIFI_NO_INTERNET_ICONS.last()
        }
    }

    private fun getWifiSignalLevel(): Int {
        val wifiInfo: WifiInfo? = wifiManager.connectionInfo
        return WifiManager.calculateSignalLevel(wifiInfo?.rssi ?: 0, 5)
    }


    // 通过wifiManager判断wifi状态是否正在连接中/断开中
    private fun isWifiConnecting(): Boolean {
        val wifiInfo = wifiManager.connectionInfo
        val supplicantState = wifiInfo.supplicantState
        return (supplicantState == SupplicantState.ASSOCIATING ||
                supplicantState == SupplicantState.AUTHENTICATING)
    }

    private fun getWifiStatus(): WifiStatus {
        try {
            val isOpen = isWifiOpen()
            if (!isOpen) return WifiStatus.DISABLED
            val isConnecting = isWifiConnecting()
            if (isConnecting) return WifiStatus.CONNECTING
            val isConnected = NetworkUtils.isWifiConnected()  // 网络是否连接
            var hasInternet = false
            if (isConnected) {
                hasInternet = checkInternetConnection()  // 能否正常上网
            }
            return when {
                !isConnected -> WifiStatus.ENABLED
                isConnected && !hasInternet -> WifiStatus.CONNECTED_NO_INTERNET
                isConnected && hasInternet -> WifiStatus.CONNECTED_HAS_INTERNET
                else -> WifiStatus.DISABLED
            }
        } catch (e: Exception) {
            return WifiStatus.DISABLED
        }
    }

    // 返回 true 表示有互联网连接，返回 false 表示没有互联网连接，需要在子线程调用
    private fun checkInternetConnection(): Boolean {
        return MyNetWorkUtil.isOnline
    }


    override fun onDetachedFromWindow() {
        whileFlag = false
        jobRefresh.cancel()
        super.onDetachedFromWindow()
    }


    enum class WifiStatus {
        ENABLED,  // Wifi 打开了 有WiFi可用  但是没有连接
        DISABLED,  // Wifi 关闭了
        CONNECTED_HAS_INTERNET,  // Wifi已经连接并且有网
        CONNECTED_NO_INTERNET,  // Wifi已经连接并且没网
        CONNECTING  // Wifi正在连接
    }


    object WifiIcons {
        val WIFI_FULL_ICONS = intArrayOf(
            R.mipmap.sys_bars_icon_wifi05,
            R.mipmap.sys_bars_icon_wifi04,
            R.mipmap.sys_bars_icon_wifi03,
            R.mipmap.sys_bars_icon_wifi02,
            R.mipmap.sys_bars_icon_wifi01
        )
        val WIFI_NO_INTERNET_ICONS = intArrayOf(
            R.mipmap.sys_bars_icon_wifi12,
            R.mipmap.sys_bars_icon_wifi11,
            R.mipmap.sys_bars_icon_wifi10,
            R.mipmap.sys_bars_icon_wifi09,
            R.mipmap.sys_bars_icon_wifi08
        )
        val QS_WIFI_SIGNAL_STRENGTH = arrayOf(
            WIFI_NO_INTERNET_ICONS,
            WIFI_FULL_ICONS
        )
        val WIFI_SIGNAL_STRENGTH = QS_WIFI_SIGNAL_STRENGTH
        val QS_WIFI_DISABLED = R.mipmap.sys_bars_icon_wifi07
        val QS_WIFI_NO_NETWORK = R.mipmap.sys_bars_icon_wifi06
        val WIFI_NO_NETWORK = QS_WIFI_NO_NETWORK
        val WIFI_LEVEL_COUNT = WIFI_SIGNAL_STRENGTH[0].size
    }
}