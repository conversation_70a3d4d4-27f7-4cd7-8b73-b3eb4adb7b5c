package com.timekettle.upup.comm.converter

import androidx.room.TypeConverter
import com.google.gson.Gson
import com.timekettle.upup.comm.bean.MsgBean
import java.util.Date
import java.util.concurrent.ConcurrentLinkedDeque

class Converters {

    @TypeConverter
    fun fromTimestamp(value: Long?): Date? {
        return value?.let { Date(it) }
    }

    @TypeConverter
    fun dateToTimestamp(date: Date?): Long? {
        return date?.time?.toLong()
    }

    @TypeConverter
    fun msgListToJson(value: List<MsgBean>): String {
        val newList = ConcurrentLinkedDeque(value)
        return Gson().toJson(newList)
    }

    @TypeConverter
    fun jsonToMsgList(value: String) = Gson().fromJson(value, Array<MsgBean>::class.java).toList()

}