package com.timekettle.upup.comm.utils

import com.google.android.gms.common.ConnectionResult
import com.google.android.gms.common.GoogleApiAvailability
//import com.tencent.mm.opensdk.openapi.WXAPIFactory
import com.timekettle.upup.base.BaseApp

/**
 *
 * @author: licoba
 * @date: 2022/7/28
 */
object AppInstallChecker {
    fun checkWechat(): <PERSON><PERSON>an {
        return false
    }


    fun checkGoogle(): <PERSON><PERSON><PERSON> {
        val code =
            GoogleApiAvailability.getInstance().isGooglePlayServicesAvailable(BaseApp.context)
        return code == ConnectionResult.SUCCESS
    }


}