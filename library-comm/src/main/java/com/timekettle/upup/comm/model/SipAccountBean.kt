package com.timekettle.upup.comm.model

import android.os.Parcel
import android.os.Parcelable
import com.google.gson.annotations.SerializedName
import kotlinx.parcelize.Parcelize

@Parcelize
data class AccountBean(
    @SerializedName("sipName")
    val sipName: String?="",
    @SerializedName("sipPwd")
    val sipPwd: String?="",
    @SerializedName("accId")
    val accId: String?="",
    @SerializedName("registrar")
    val registrar: String?="",
): Parcelable
