package com.timekettle.upup.comm.utils

import android.animation.ObjectAnimator
import android.animation.ValueAnimator
import android.view.View
import android.view.animation.LinearInterpolator

/**
 *
 * @author: licoba
 * @date: 2022/8/26
 */
object AnimationUtil {
    // 模板方法，参照这个写
    private val defaultAnimationInterpolator = LinearInterpolator()
    fun translateView1(viewToAnimate: View, positionX: Float) {
        viewToAnimate.animate()
            .x(positionX)
            .setDuration(600L)
            .setInterpolator(defaultAnimationInterpolator)
            .start()
    }

    fun translateView2(viewToAnimate: View, positionX: Float) {
        ObjectAnimator.ofFloat(
            viewToAnimate,
            View.X,
            positionX
        ).apply {
            duration = 600
            interpolator = defaultAnimationInterpolator
        }.start()
    }


    fun translateView3(viewToAnimate: View, positionX: Float) {
        ValueAnimator.ofFloat(viewToAnimate.x, positionX).apply {
            duration = 2000
            interpolator = defaultAnimationInterpolator
            addUpdateListener {
                viewToAnimate.x = it.animatedValue as Float
            }
            start()
        }
    }
}