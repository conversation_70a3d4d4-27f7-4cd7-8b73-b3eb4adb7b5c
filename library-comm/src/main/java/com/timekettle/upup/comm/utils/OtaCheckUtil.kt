package com.timekettle.upup.comm.utils

import android.content.Context
import android.os.BatteryManager
import android.os.Build
import androidx.annotation.RequiresApi
import co.timekettle.btkit.BleUtil
import com.timekettle.upup.base.BaseApp
import com.timekettle.upup.base.utils.DateUtils
import com.timekettle.upup.base.utils.EventBusUtils
import com.timekettle.upup.base.utils.logD
import com.timekettle.upup.comm.model.DfuNotifyMsg
import com.timekettle.upup.comm.model.OtaReadyEvent
import com.timekettle.upup.comm.tools.DeviceManager
import com.timekettle.upup.comm.tools.DeviceTool
import com.tmk.libserialhelper.tmk.W3ProHelper
import com.tmk.libserialhelper.tmk.bean.W3ProSerial
import com.tmk.libserialhelper.tmk.ktx.findLeft
import com.tmk.libserialhelper.tmk.ktx.findRight
import java.time.LocalDateTime
import java.time.ZoneId
import java.time.format.DateTimeFormatter
import java.util.Date
import java.util.TimeZone

object OtaCheckUtil {

    private var isEnableLog = false

    private fun mLog(msg: String) {
        if (isEnableLog) logD(msg)
    }

    /**
     * 是否满足升级的条件
     */
    fun isMeetCondition(dfuNotifyMsg: DfuNotifyMsg): Boolean {
        var twoHeadsetInfo = W3ProHelper.getTwoHeadsetInfo()  //串口读取到的设备信息
        val leftDevice = twoHeadsetInfo.findLeft()
        val rightDevice = twoHeadsetInfo.findRight()
        if (leftDevice == null || rightDevice == null) {
            mLog("左耳/右耳 设备为空，不允许升级")
            return false
        }
        /**
         * 条件1：左耳|右耳任意一只需要升级，都需要弹窗提示
         * 条件2：左耳&右耳 都在仓内
         * 条件3：左耳&右耳 电量大于30
         * 条件4：主机电量大于30%
         * 条件5：扫描不到主机绑定的BLE设备（先不用，这个不准确）
         *
         */
        val condition1 =
            isVersionMeet(dfuNotifyMsg, leftDevice) || isVersionMeet(dfuNotifyMsg, rightDevice)
        if(!condition1) {
            mLog("左耳/右耳 版本不满足，不允许升级")
        }
        val condition2 = HallUtil.isLeftRightIn()
        if(!condition2) {
            mLog("左耳/右耳 不在舱内，不允许升级")
        }
        val condition3 = leftDevice.electric > 30 && rightDevice.electric > 30
        if(!condition3) {
            mLog("左耳/右耳 电量不够30，不允许升级")
        }
        val condition4 = isBoxElectricFit()
        if(!condition4) {
            mLog("盒子电量不够30，不允许升级")
        }
        return condition1 && condition2 && condition3 && condition4
    }


    private fun isVersionMeet(dfuNotifyMsg: DfuNotifyMsg, serial: W3ProSerial?): Boolean {
        if (serial == null) {
            mLog("serial设备为空，不能升级")
            return false // 版本不满足，不允许升级
        }
        val myVersionA = serial.lanXunFirmV
        val myVersionB = serial.bleFirmV
        mLog("本地版本a：v$myVersionA  本地版本B：v$myVersionB")
        if (!compareVersion(myVersionA, dfuNotifyMsg.versionA) &&
            !compareVersion(myVersionB, dfuNotifyMsg.versionB)
        ) {
            mLog("升级版本A和B都不满足升级条件：[A固件，本地:$myVersionA/远程:${dfuNotifyMsg.versionA}] [B固件，本地:$myVersionB/远程:${dfuNotifyMsg.versionB}]")
            return false // 版本不满足，不允许升级
        }
        return if (dfuNotifyMsg.localTimeZone) {
            mLog("需要定时升级")
            if (isReachedTimezone(dfuNotifyMsg)) {
                mLog("达到了定时升级的时间 ${dfuNotifyMsg.publishTime} ")
                true
            } else {
                mLog("未达到定时升级的时间 ${dfuNotifyMsg.publishTime}")
                false
            }
        } else {
            mLog("不需要定时升级，直接提示")
            true
        }
    }


    // 当前时区是否到达了后台定时的时间
    fun isReachedTimezone(dfuNotifyMsg: DfuNotifyMsg): Boolean {
        return if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
            try {
                val currentTimeZoneID = TimeZone.getDefault().id
                val currentDate = DateUtils.getCurrentTimeInTimeZone(currentTimeZoneID)
                mLog("当前时区：$currentTimeZoneID")
                mLog("当前时间：$currentDate")
                val dfuDateStr = dfuNotifyMsg.publishTime
                val formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")
                val localDateTime = LocalDateTime.parse(dfuDateStr, formatter)
                val dfuDate = Date.from(localDateTime.atZone(ZoneId.systemDefault()).toInstant())
                currentDate.after(dfuDate)
            } catch (e: Exception) {
                mLog("时间解析失败了！$e")
                false
            }

        } else {
            true
        }
    }


    // 主机的电量是否满足条件
    private fun isBoxElectricFit(): Boolean {
        return try {
            val manager =
                BaseApp.context.getSystemService(Context.BATTERY_SERVICE) as BatteryManager
            val currentPower =
                manager.getIntProperty(BatteryManager.BATTERY_PROPERTY_CAPACITY) ///当前电量百分比
            currentPower > 30
        } catch (e: Exception) {
            false
        }
    }



}