package com.timekettle.upup.comm.service.trans

import co.timekettle.btkit.LogUtil
import com.alibaba.android.arouter.facade.annotation.Autowired
import com.alibaba.android.arouter.launcher.ARouter
import com.timekettle.upup.comm.bean.MeetEngineHost
import com.timekettle.upup.comm.constant.RouteUrl
import com.timekettle.upup.comm.dao.CallRecordDao

/**
 **  翻译（Trans）模块对其它模块的接口暴露
 **
 */
object TransServiceImplWrap {

    @Autowired(name = RouteUrl.Translate.TransService)
    lateinit var service: TransService

    fun getDefaultSelfLan(): String {
        return service.getDefaultSelfLan()
    }

    fun getDefaultOtherLan(): String {
        return service.getDefaultOtherLan()
    }

    fun initService() {
        service.initService()
    }

    fun getPhoneModelLang():String{
        return service.getPhoneModelLang()
    }

    fun initTransConfig() = service.initTransConfig()

    fun setAccount(account: String) {
        service.setAccount(account)
    }

    fun getAccount(): String {
        return service.getAccount()
    }

    fun setMeetingId(id: String) {
        service.setMeetingId(id)
    }

    fun getMeetingId(): String {
        return service.getMeetingId()
    }

    fun setPassword(password: String) {
        service.setPassword(password)
    }

    fun getPassword(): String {
        return service.getPassword()
    }

    fun getStyledTextByCode(code:String): String {
        return service.getStyledTextByCode(code)
    }
    fun cleanAllTransHistory() {

         service.cleanAllTransHistory()
    }

    fun getCallRecordDao(): CallRecordDao {
        return service.getCallRecordDao()
    }

    fun fetchHosts() {
        service.fetchHosts()
    }

    fun fetchMeetHosts(callback: (code: Int, hosts: ArrayList<MeetEngineHost>) -> Unit) {
        service.fetchMeetHost(callback)
    }

    fun closeAllModeOffline() = service.closeAllModeOffline()

    fun getOnNFCReceive(): Boolean {
        return service.getOnNFCReceive()
    }

    fun setOnNFCReceive(value: Boolean) {
        service.setOnNFCReceive(value)
    }

    fun isInMeeting(): Boolean {
        return service.isInMeeting()
    }

    fun setInMeeting(value: Boolean) {
        service.setInMeeting(value)
    }

    fun getCallType(): Int {
        return service.getCallType()
    }

    fun setCallType(type: Int) {
        service.setCallType(type)
    }

    fun getSpeechBaseUrl(): String = service.getSpeechBaseUrl()


    init {
        ARouter.getInstance().inject(this)
    }

}