package com.timekettle.upup.comm.widget

import android.animation.Animator
import android.annotation.SuppressLint
import android.content.Context
import android.graphics.Color
import android.util.AttributeSet
import android.view.LayoutInflater
import android.view.MotionEvent
import android.view.View
import androidx.constraintlayout.widget.ConstraintLayout
import androidx.core.view.isVisible
import androidx.lifecycle.Lifecycle
import androidx.lifecycle.LifecycleOwner
import androidx.lifecycle.LifecycleRegistry
import androidx.lifecycle.Observer
import com.blankj.utilcode.util.ActivityUtils
import com.timekettle.upup.base.ktx.getAppViewModel
import com.timekettle.upup.base.ktx.gone
import com.timekettle.upup.base.ktx.invisible
import com.timekettle.upup.base.ktx.visible
import com.timekettle.upup.base.utils.logD
import com.timekettle.upup.comm.R
import com.timekettle.upup.comm.databinding.CommTopDeviceBinding
import com.timekettle.upup.comm.dialog.DeviceDialog
import com.timekettle.upup.comm.ktx.findLeft
import com.timekettle.upup.comm.ktx.findRight
import com.timekettle.upup.comm.ktx.isLeft
import com.timekettle.upup.comm.ktx.isRight
import com.timekettle.upup.comm.model.X1Device
import com.timekettle.upup.comm.model.X1Status
import com.timekettle.upup.comm.tools.DeviceTool
import com.timekettle.upup.comm.viewmodel.VMTopDevice

/**
 *
 * @Author:         licoba
 * @CreateDate:     2023/6/10 10:22
 * @Description:    顶部的电池View，顶部耳机状态栏，顶部状态栏，耳机状态栏
 */


class TopDeviceView @JvmOverloads constructor(
    context: Context,
    attrs: AttributeSet? = null,
    defStyleAttr: Int = 0
) : ConstraintLayout(context, attrs, defStyleAttr), View.OnClickListener, LifecycleOwner {


    private val lifecycleRegistry = LifecycleRegistry(this)

    override fun getLifecycle(): Lifecycle {
        return lifecycleRegistry
    }

    // ViewModelProvider 需要提供一个 ViewModelStoreOwner 对象
    // 方式一：自定义View实现ViewModelStoreOwner接口，然后ViewModelProvider(this)来调用
    // 方式二：获取最近的 ViewModelStoreOwner，通过ViewTreeViewModelStoreOwner.get(this)!!方法

    private val viewModel: VMTopDevice by lazy { getAppViewModel() }


    private var binding: CommTopDeviceBinding
    var onDevicesConnected: ((deviceSize: Int) -> Unit)? = null

    init {
        val view = LayoutInflater.from(context)
            .inflate(R.layout.comm_top_device, this)
        binding = CommTopDeviceBinding.bind(view)
    }


    //强制更新UI，拿到最新的信息
    fun refresh() {
        viewModel.getNewData()
    }

    override fun onAttachedToWindow() {
        super.onAttachedToWindow()
        viewModel.liveX1Devices.observeForever(observerDevices)
        viewModel.liveNeedUpgrade.observeForever(observerUpgrade)
    }


    // 更新是否需要升级的状态（与耳机的状态是独立的，没有关联）
    private val observerUpgrade = Observer<Boolean> { need ->
        with(binding) {
            if (need) {
                ivUpgrade.visible()
                ivCenterX.invisible()
            } else {
                ivCenterX.visible()
                ivUpgrade.invisible()
            }
        }
    }


    private val observerDevices = Observer<MutableList<X1Device>> { devices ->
//        logD("X1设备数据: $devices")
        val leftDevice = devices.findLeft()
        val rightDevice = devices.findRight()
        binding.ivDeviceLeft.setImageResource(R.mipmap.earbus_img_gray_l)
        binding.ivDeviceRight.setImageResource(R.mipmap.earbus_img_gray_r)

        if (leftDevice != null) {
            updateElectric(leftDevice)
            binding.updateX1Status(leftDevice)
        }
        if (rightDevice != null) {
            binding.updateX1Status(rightDevice)
            updateElectric(rightDevice)
        }


        if (devices.size in 1..2) {
            onDevicesConnected?.invoke(devices.size)
        }
    }


    @SuppressLint("SetTextI18n")
    private fun updateElectric(device: X1Device) {
        // 假装100的电
        var electric = if (device.state == X1Status.ChargingInBox) {
            device.serialDevice?.electric
        } else {
            device.bleDevice?.electric
        }
        if (electric == 0 || electric == null) electric = 0
        if (device.isLeft()) {
            binding.ivBatteryLeft.setProgress(electric / 100f)
            if (device.state == X1Status.ChargingInBox) { // 在盒子里面不需要百分号
                binding.tvBatteryLeft.text = "$electric"
            } else {
                binding.tvBatteryLeft.text = "$electric%"
            }
            if (electric <= 20) { // 低电
                binding.tvBatteryLeft.setTextColor(Color.parseColor("#FC6D74"))
            } else {
                if (device.state == X1Status.ChargingInBox) { // 在盒子里面是绿色
                    binding.tvBatteryLeft.setTextColor(Color.parseColor("#FF15FF4F"))
                } else {
                    binding.tvBatteryLeft.setTextColor(Color.parseColor("#FFFFFF"))
                }
            }
        } else if (device.isRight()) {
            binding.ivBatteryRight.setProgress(electric / 100f)
            if (device.state == X1Status.ChargingInBox) { // 在盒子里面不需要百分号
                binding.tvBatteryRight.text = "$electric"
            } else {
                binding.tvBatteryRight.text = "$electric%"
            }
            if (electric <= 20) { // 低电
                binding.tvBatteryRight.setTextColor(Color.parseColor("#FC6D74"))
            } else {
                if (device.state == X1Status.ChargingInBox) { // 在盒子里面是绿色
                    binding.tvBatteryRight.setTextColor(Color.parseColor("#FF15FF4F"))
                } else {
                    binding.tvBatteryRight.setTextColor(Color.parseColor("#FFFFFF"))
                }
            }
        }
    }


    companion object {
        const val TAG = "TopDeviceView"
    }

    override fun onTouchEvent(event: MotionEvent): Boolean {
        when (event.action) {
            MotionEvent.ACTION_DOWN -> {
                performClick() // 触发点击事件
                //可以在这里增加点击事件
                onClick(this)
                return true
            }
        }
        return super.onTouchEvent(event)
    }

    override fun onClick(p0: View?) {
        DeviceDialog(ActivityUtils.getTopActivity()).showPopupWindow()
    }

    private fun CommTopDeviceBinding.updateX1Status(device: X1Device) {

        if (device.isLeft()) {
            llBatteryStatusLeft.invisible()
            lottieChargingLeft.invisible()
            ivBrokenLeft.invisible()
            llDeviceUnusualLeft.invisible()
            lottieConnectingLeft.invisible()
            lottieReadingBatteryLeft.invisible()
        } else {
            llBatteryStatusRight.invisible()
            lottieChargingRight.invisible()
            ivBrokenRight.invisible()
            llDeviceUnusualRight.invisible()
            lottieConnectingRight.invisible()
            lottieReadingBatteryRight.invisible()
        }
        when (device.state) {
            X1Status.SearchConnecting -> {
                if (device.isLeft()) {
                    llDeviceUnusualLeft.visible()
                    lottieConnectingLeft.visible()
                } else {
                    llDeviceUnusualRight.visible()
                    lottieConnectingRight.visible()
                }
            }

            X1Status.Broken -> {
                if (device.isLeft()) {
                    llDeviceUnusualLeft.visible()
                    ivBrokenLeft.visible()
                } else {
                    llDeviceUnusualRight.visible()
                    ivBrokenRight.visible()
                }
            }


            X1Status.Connected -> {
                if (device.isLeft()) {
                    llBatteryStatusLeft.visible()
                    DeviceTool.asWSeries(device.bleDevice)?.let {
                        if (it.electric > 0) {  // 已经读取到电量了
                            if (lottieReadingBatteryLeft.isAnimating) return
                            lottieReadingBatteryLeft.invisible()
                            lottieReadingBatteryLeft.progress = 0f
                            lottieReadingBatteryLeft.removeAllUpdateListeners()
                            llBatteryLeft.visible()
                            ivBatteryLeft.visible()
                        } else {  // 没有读取到电量
                            llBatteryLeft.invisible()
                            ivBatteryLeft.invisible()
                            if (lottieReadingBatteryLeft.isVisible) return
//                            lottieReadingBatteryLeft.addAnimatorUpdateListener { animation ->
//                                if (animation.animatedValue as Float >= 0.6f) {
//                                    lottieReadingBatteryLeft.invisible()
//                                    llBatteryLeft.visible()
//                                    ivBatteryLeft.visible()
//                                }
//                            }
                            lottieReadingBatteryLeft.visible()
                            if (lottieReadingBatteryLeft.progress == 0f)
                                lottieReadingBatteryLeft.playAnimation()
                        }
                    }
                } else { // 右边的耳机已经连接上
                    llBatteryStatusRight.visible()
                    DeviceTool.asWSeries(device.bleDevice)?.let {
                        if (it.electric > 0) {  // 已经读取到电量了
//                            if (lottieReadingBatteryRight.isAnimating) return
                            lottieReadingBatteryRight.invisible()
                            lottieReadingBatteryRight.progress = 0f
                            lottieReadingBatteryRight.removeAllUpdateListeners()
                            llBatteryRight.visible()
                            ivBatteryRight.visible()
                        } else {
                            llBatteryRight.invisible()
                            ivBatteryRight.invisible()
                            if (lottieReadingBatteryRight.isVisible) return
//                            lottieReadingBatteryRight.addAnimatorUpdateListener { animation ->
//                                if (animation.animatedValue as Float >= 0.6f) {
//                                    lottieReadingBatteryRight.invisible()
//                                    llBatteryRight.visible()
//                                    ivBatteryRight.visible()
//                                }
//                            }
                            lottieReadingBatteryRight.visible()
                            if (lottieReadingBatteryRight.progress == 0f)
                                lottieReadingBatteryRight.playAnimation()
                        }
                    }
                }
            }

            X1Status.ChargingInBox -> { // 只有在盒子里，才会有需要升级的状态
                if (device.isLeft()) {
                    lottieChargingLeft.visible()
                } else {
                    lottieChargingRight.visible()
                }
            }

            else -> {

            }
        }
    }


    override fun onDetachedFromWindow() {
        viewModel.liveX1Devices.removeObserver(observerDevices)
        viewModel.liveNeedUpgrade.removeObserver(observerUpgrade)
        super.onDetachedFromWindow()
    }


}