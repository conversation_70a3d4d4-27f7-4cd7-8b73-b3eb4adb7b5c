package com.timekettle.upup.comm.service.setting

import com.alibaba.android.arouter.facade.template.IProvider

/**
 **  设置（Setting）模块提供的服务
 **
 */
interface SettingService : IProvider {

    fun testPrint()

    fun initSerialPort()


    /**
     * 更新音量，0-100
     */
    fun updateBleVolume(value: Int? = null)
    fun getBleVolume(): Int

    // 开始定时读取耳机的电量任务
    fun startReadInfoJob()

    // 取消定时读取耳机电量任务的job
    fun cancelReadInfoJob()

}