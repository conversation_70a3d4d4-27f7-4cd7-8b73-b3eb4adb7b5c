package com.timekettle.upup.comm.log;

import android.os.Handler;
import android.os.HandlerThread;
import android.os.Looper;
import android.os.Message;
import android.util.Log;

import java.io.File;
import java.io.FileWriter;
import java.io.IOException;
import java.text.DateFormat;
import java.text.SimpleDateFormat;
import java.util.Date;

/**
 * <AUTHOR>
 * @date 2022/10/13 10:02
 * @email <EMAIL>
 * @desc
 */
public class MyDiskLogHandler extends Handler {

    public MyDiskLogHandler(int maxFileSize) {
        this(getDefaultLooper(), maxFileSize);
    }

    public MyDiskLogHandler(Looper looper, int maxFileSize) {
        super(looper);
    }


    private static Looper getDefaultLooper() {
        HandlerThread ht = new HandlerThread("AndroidFileLogger");
        ht.start();
        return ht.getLooper();
    }

    @SuppressWarnings("checkstyle:emptyblock")
    @Override
    public void handleMessage(Message msg) {
        String content = (String) msg.obj;
        FileWriter fileWriter = null;
        content = content.replaceAll("Tmk-", "");
//        Log.e("日志处理","收到logger的日志消息："+content);
        try {
            fileWriter = new FileWriter(LogUtil.INSTANCE.getLogFile(), true);
            writeLog(fileWriter, content + "\n");
            fileWriter.flush();
            fileWriter.close();
        } catch (IOException e) {

            Log.e("TAG","无法写日志！IO异常" +  LogUtil.INSTANCE.getLogFile().getName()+ "," +e.getMessage());
            if (fileWriter != null) {
                try {
                    fileWriter.flush();
                    fileWriter.close();
                } catch (IOException e1) { /* fail silently */ }
            }
        }
    }

    /**
     * This is always called on a single background thread.
     * Implementing classes must ONLY write to the fileWriter and nothing more.
     * The abstract class takes care of everything else including close the stream and catching IOException
     *
     * @param fileWriter an instance of FileWriter already initialised to the correct file
     */
    private void writeLog(FileWriter fileWriter, String content) throws IOException {
        DateFormat dateFormat = new SimpleDateFormat("MM-dd HH:mm:ss.SSS");
        String timeStr = dateFormat.format(new Date());
        fileWriter.append(timeStr + " " + content);
    }


}