package com.timekettle.upup.comm.button;

/**
 * @author: licoba
 * @date: 2022/5/16
 */

import android.content.Context;
import android.content.res.ColorStateList;
import android.content.res.TypedArray;
import android.graphics.Color;
import android.graphics.drawable.Drawable;
import android.graphics.drawable.GradientDrawable;
import android.graphics.drawable.LayerDrawable;
import android.graphics.drawable.StateListDrawable;
import android.util.AttributeSet;

import android.content.Context;
import android.content.res.TypedArray;
import android.graphics.Rect;
import android.graphics.drawable.Drawable;
import android.text.TextPaint;
import android.util.AttributeSet;
import android.view.View;

import androidx.appcompat.widget.AppCompatButton;

import com.timekettle.upup.comm.R;

/**
 * 可以加图标的Button
 * https://github.com/yndongyong/IconButton
 * <AUTHOR> 暂时未使用到，作为资料参考
 */
public class IconButton extends AppCompatButton {

    //图片和文本一起居中
    public static final int CENTER_ANCHOR_ALL = 0;
    //文本居中
    public static final int CENTER_ANCHOR_TEXT = 1;

    protected int drawableWidth;
    protected DrawablePositions drawablePosition;
    // Cached to prevent allocation during onLayout
    protected Rect bounds = new Rect();
    private int drawablePadding;
    private int centerAnchor = CENTER_ANCHOR_ALL;
    private CenterStrategy mCenterStrategy;


    public IconButton(Context context) {
        this(context, null);
    }

    public IconButton(Context context, AttributeSet attrs) {
        this(context, attrs, 0);
    }

    public IconButton(Context context, AttributeSet attrs, int defStyleAttr) {
        super(context, attrs, defStyleAttr);
        TypedArray typedArray = getContext().obtainStyledAttributes(attrs, R.styleable.IconButtonStyle);
        centerAnchor = typedArray.getInt(R.styleable.IconButtonStyle_dy_centerAnchor,0);
        typedArray.recycle();
        int iconPadding = getCompoundDrawablePadding();
        setDrawablePadding(iconPadding);
        if (centerAnchor == CENTER_ANCHOR_TEXT) {
            mCenterStrategy = new TextCenterStrategy();
        } else {
            mCenterStrategy = new DrawableAndTextCenterStrategy();
        }
    }

    public void setDrawablePadding(int drawablePadding) {
        this.drawablePadding = drawablePadding;
        requestLayout();
    }

    @Override
    public void setCompoundDrawables(Drawable left, Drawable top, Drawable right, Drawable bottom) {
        super.setCompoundDrawables(left, top, right, bottom);
        if (left != null && right != null) {
            drawableWidth = left.getIntrinsicWidth() + right.getIntrinsicWidth();
            drawablePosition = DrawablePositions.BOTH;
        } else if (left != null) {
            drawableWidth = left.getIntrinsicWidth();
            drawablePosition = DrawablePositions.LEFT;
        } else if (right != null) {
            drawableWidth = right.getIntrinsicWidth();
            drawablePosition = DrawablePositions.RIGHT;
        } else {
            drawablePosition = DrawablePositions.NONE;
        }
    }

    @Override
    protected void onLayout(boolean changed, int left, int top, int right, int bottom) {
        super.onLayout(changed, left, top, right, bottom);

        TextPaint textPaint = getPaint();
        String textStr = getText().toString();
        textPaint.getTextBounds(textStr, 0, textStr.length(), bounds);
        int textWidth = bounds.width();

        int horizontalPadding = mCenterStrategy.applyStyle(this, drawablePosition, drawableWidth, textWidth, drawablePadding);
        setCompoundDrawablePadding(-horizontalPadding + drawablePadding);
    }

    public enum DrawablePositions {
        NONE,
        LEFT,
        RIGHT,
        BOTH
    }

    public interface CenterStrategy {

        int applyStyle(View view, DrawablePositions drawablePosition, int drawableWidth, int textWidth, int iconPadding);
    }

    public class TextCenterStrategy implements CenterStrategy {

        @Override
        public int applyStyle(View view, DrawablePositions drawablePosition, int drawableWidth, int textWidth, int drawablePadding) {

            int horizontalPadding = (view.getWidth() - textWidth) / 2;
            int childOffsetX;

            switch (drawablePosition) {
                case LEFT:
                    childOffsetX = horizontalPadding - drawablePadding - drawableWidth;
                    view.setPadding(childOffsetX, view.getPaddingTop(), 0, view.getPaddingBottom());
                    break;
                case RIGHT:
                    childOffsetX = view.getWidth() - (horizontalPadding + textWidth + drawablePadding + drawableWidth);
                    view.setPadding(0, view.getPaddingTop(), childOffsetX, view.getPaddingBottom());
                    break;
                case BOTH:
                    childOffsetX = horizontalPadding = (view.getWidth() - (drawableWidth + drawablePadding *2 + textWidth)) / 2;
                    view.setPadding(childOffsetX, view.getPaddingTop(), childOffsetX, view.getPaddingBottom());
                    break;
                default:
                    view.setPadding(0, view.getPaddingTop(), 0, view.getPaddingBottom());
                    break;
            }
            return horizontalPadding;

        }
    }

    public class DrawableAndTextCenterStrategy implements CenterStrategy {

        @Override
        public int applyStyle(View view, DrawablePositions drawablePosition, int drawableWidth, int textWidth, int iconPadding) {

            int childOffsetX  = (view.getWidth() - (drawableWidth + iconPadding + textWidth)) / 2;
            switch (drawablePosition) {
                case LEFT:
                    view.setPadding(childOffsetX, view.getPaddingTop(), 0, view.getPaddingBottom());
                    break;
                case RIGHT:
                    view.setPadding(0, view.getPaddingTop(), childOffsetX, view.getPaddingBottom());
                    break;
                case BOTH:
                    view.setPadding(childOffsetX, view.getPaddingTop(), childOffsetX, view.getPaddingBottom());
                    break;
                default:
                    view.setPadding(0, view.getPaddingTop(), 0, view.getPaddingBottom());
                    break;
            }
            return childOffsetX;
        }
    }




}


