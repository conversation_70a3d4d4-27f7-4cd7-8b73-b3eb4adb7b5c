package com.timekettle.upup.comm.utils

import android.content.res.AssetManager
import android.view.animation.DecelerateInterpolator
import android.widget.ImageView
import androidx.core.view.ViewCompat
import com.timekettle.upup.base.app.ActivityStackManager
import com.timekettle.upup.comm.net.helper.TokenManager
import com.timekettle.upup.comm.service.login.LoginServiceImplWrap
import kotlinx.coroutines.delay
import java.util.regex.Pattern
import kotlin.math.abs


/**
 * 控制箭头的上下变化
 *
 * @param vArrowIv ImageView 展示图片的ImageView
 * @param arrTag Boolean 箭头的状态
 * @param isAnimate Boolean 是否需要动画
 */
fun setAnimatorArrowTag(vArrowIv: ImageView, arrTag: Boolean, isAnimate: Boolean) {
    if (arrTag) {
        if (isAnimate) {
            ViewCompat.animate(vArrowIv).setDuration(200)
                .setInterpolator(DecelerateInterpolator())
                .rotation(180f)
                .start()
        } else {
            vArrowIv.rotation = 180f
        }
    } else {
        if (isAnimate) {
            ViewCompat.animate(vArrowIv).setDuration(200)
                .setInterpolator(DecelerateInterpolator())
                .rotation(0f)
                .start()
        } else {
            vArrowIv.rotation = 0f
        }
    }
}

/**
 * 退出
 */
fun exit() {
    ActivityStackManager.finishAllActivity()
}

fun AssetManager.readAssetsFile(fileName: String): String =
    open(fileName).bufferedReader().use { it.readText() }


/**
 * 比较两个版本的大小，v2>v1就返回true
 * @param v1 String 设备版本
 * @param v2 String 远程版本
 * @return Boolean return true if v1<v2 else false
 */
fun compareVersion(para1: String?, para2: String?): Boolean {

    if (para1 == null) return false
    if (para2 == null) return false
    var v1 = para1
    var v2 = para2
    if (v1.startsWith("v", true)) v1 = v1.substring(0)
    if (v2.startsWith("v", true)) v2 = v2.substring(0)
    val regEx = "[^0-9]"
    val p = Pattern.compile(regEx)
    var s1: String = p.matcher(v1).replaceAll("").trim()
    var s2: String = p.matcher(v2).replaceAll("").trim()

    val cha: Int = s1.length - s2.length
    var buffer = StringBuffer()
    var i = 0
    while (i < abs(cha)) {
        buffer.append("0")
        ++i
    }

    if (cha > 0) {
        buffer.insert(0, s2)
        s2 = buffer.toString()
    } else if (cha < 0) {
        buffer.insert(0, s1)
        s1 = buffer.toString()
    }

    val s1Int = s1.toInt()
    val s2Int = s2.toInt()

    return s2Int > s1Int
}


// 每个模块都退出登录
fun fullLogOut() {
    LoginServiceImplWrap.logout()
}

suspend fun doTaskOnInterval(intervalMillis: Long, task: suspend () -> Unit) {
    while (true) { // 无限循环
        task() // 执行任务
        delay(intervalMillis) // 等待指定时间间隔
    }
}