package com.timekettle.upup.comm.bean

import co.timekettle.btkit.bean.RawBlePeripheral

/**
 *
 * @author: licoba
 * @date: 2022/7/12
 */
data class BleDeviceBean(
    val peripheral: RawBlePeripheral,
    var connectState: ConnectState = ConnectState.NONE,
)

enum class ConnectState(val value: Int) {
    NONE(0),  // 未连接
    CONN_BT_ING(0),  // 正在连接BT
    CONN_BLE_ING(0),  //  正在连接BLE / 正在连接SPP
    ALL_ALREADY(3),  // 全部已经连接（如果是M3，那就是BT跟BLE/SPP；如果是W系列，那就是只连接了BLE就可以到这个状态了）
//    NOT_AVAILABLE(4),  // 未连接并且不可点击
}