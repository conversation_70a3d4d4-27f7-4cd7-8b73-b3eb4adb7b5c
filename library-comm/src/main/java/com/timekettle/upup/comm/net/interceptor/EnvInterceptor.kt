package com.timekettle.upup.comm.net.interceptor

import okhttp3.Interceptor
import okhttp3.Response


/**
 * https://www.jianshu.com/p/2919bdb8d09a
 * @author: licoba
 * @date: 2022/6/1
 * 环境拦截器，用于切换BaseUrl，暂时未用到，采用杀掉App的方式来切换环境，不需要动态切换了
 */
class EnvInterceptor : Interceptor {

    override fun intercept(chain: Interceptor.Chain): Response {
        var request = chain.request()
        val requestBuilder = request.newBuilder()
        // 这里是拦截下来的 url ，注意：这个 url 是全量url，也就是完整的url，并不是单纯的主域名地址。
        var url = request.url.toString()
        if (true) {
            // 如果是测试服并且拦截下来的url中还包含正式服，将url前缀替换成测试服的url前缀
            if (url.contains("demon")) {
                url = url.replace("demon", "test")
            }
        }
        // 将新的 url 再设置回去
        request = requestBuilder.url(url).build();
        return chain.proceed(request);
    }

}
