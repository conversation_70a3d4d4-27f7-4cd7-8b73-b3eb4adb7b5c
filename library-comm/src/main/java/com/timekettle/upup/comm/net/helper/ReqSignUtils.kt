package com.timekettle.upup.comm.net.helper

import com.blankj.utilcode.util.EncodeUtils
import com.blankj.utilcode.util.StringUtils
import java.security.GeneralSecurityException
import java.security.KeyFactory
import java.security.Signature
import java.security.spec.PKCS8EncodedKeySpec
import java.security.spec.X509EncodedKeySpec
import java.util.Arrays
import java.util.Objects

/**
 * X1的请求 签名验签工具类
 *
 * <AUTHOR>
 */
object ReqSignUtils {

    private const val AND_CHAR = "&"
    private const val EQUAL_CHAR = "="

    /**
     * 签名
     *
     * @param content    文本
     * @param privateKey 私钥
     * @return the base64 String of the signing operation's result.
     * @throws GeneralSecurityException
     */
    @Throws(GeneralSecurityException::class)
    fun sign(content: String, privateKey: String?): String {
        val signature = Signature.getInstance("MD5withRSA")
        var pkcs8EncodedKeySpec: PKCS8EncodedKeySpec? = null
        pkcs8EncodedKeySpec = PKCS8EncodedKeySpec(EncodeUtils.base64Decode(privateKey))
        val keyFactory = KeyFactory.getInstance("RSA")
        val key = keyFactory.generatePrivate(pkcs8EncodedKeySpec)
        signature.initSign(key)
        signature.update(content.toByteArray())
        //签名
        val sign = signature.sign()
        return EncodeUtils.base64Encode2String(sign)
    }

    /**
     * 验签
     *
     * @param content      签名文本
     * @param terminalSign 签名
     * @param publicKey    公钥
     * @return true if the signature was verified, false if not.
     * @throws GeneralSecurityException exception
     */
    @Throws(GeneralSecurityException::class)
    fun verify(content: String, terminalSign: String?, publicKey: String?): Boolean {
        val signature = Signature.getInstance("MD5withRSA")
        val x509EncodedKeySpec = X509EncodedKeySpec(EncodeUtils.base64Decode(publicKey))
        val keyFactory = KeyFactory.getInstance("RSA")
        val key = keyFactory.generatePublic(x509EncodedKeySpec)
        signature.initVerify(key)
        signature.update(content.toByteArray())
        return signature.verify(EncodeUtils.base64Decode(terminalSign))
    }

    fun toSignedContent(signParam: Map<String, Any?>): String {
        // 参数按key排序
        val keys = signParam.keys.toTypedArray<String>()
        Arrays.sort(keys)

        // 参数按 key1=value1&key2=value2 格式拼接
        val stringBuilder = StringBuilder()
        for (key in keys) {
            val o = signParam[key]
            // value为空、null等值不参与生成签名
            if (Objects.isNull(o) || StringUtils.isEmpty(o.toString())) {
                continue
            }
            stringBuilder.append(key).append(EQUAL_CHAR).append(o.toString().trim { it <= ' ' })
                .append(
                    AND_CHAR
                )
        }
        var joinParam = stringBuilder.toString()
        if (joinParam.endsWith(AND_CHAR)) {
            joinParam = joinParam.substring(0, joinParam.length - 1)
        }
        return joinParam
    }

    val PRIVATE_KEY = """
MIICdgIBADANBgkqhkiG9w0BAQEFAASCAmAwggJcAgEAAoGBAKI+hu4/fq//NqoA
GfgGVM4ccVM5IxelIoTC/mkR8+tm/Sg4dzr+46YoNYJGNBmd3z4Wz2ZUA1b764ui
8cy8hWTswdtAa8F7mv3zmI5JGVwVukX+3hTRvOeIezP4fR9c+/G6nE4yYJNG/0QM
4wKNJrVXkbp3Oi+w+KIWSKmrazxBAgMBAAECgYBkP/yB1GwEdc8iJiPltIvMO5ju
kpyTbK8yl4Y6CCTO7Pk6VAGgS4UA01GU6KoitgEOTWvjz7+sjMcTpiCAoHBPW0QU
YPe8vh5HW2b52ULiuy5YYIlxJ0OLW7/1gdD/QpaveoBoAb1daCY/aYYdulTX5fLc
AvhZNVA7tFBKxMBAAQJBANR4pRSKl2yaFYvGavm8HNeRXtmq3Bh1chdy7b7Ri1Ui
h2tCfND8yr3JxPLJA1zYp9AvBHTc1Ya+VQyk2UAi/wECQQDDe6e93g5Jue/I+l46
Npokocphy523QhGUvfKaM0/8W01JGW8M/EnrnAaUMmQUztNoZ3V4rJ0ITWYc1tFR
BX1BAkAmg9go6RaZ8TSGqekqbBcuXIO9IWPUazXLWmIOfR2syR2TK/JEUA7QaIrz
F80sP3nsGxnLtVxfmJECQb/ACJgBAkEAg+ADxX7sPKiHLxpYPwVe75+GHHt0ppCX
s7SA2a4MI76kYNAbrrL0W1Iizg9AnRj6XdCN2x73wQYWARaalwIRAQJAf6A41FoC
wM6zR4zQADXcYV+KrdTFbr5LSg5V3QXprL4XvFm7YzAXbjKGw5R7XZM0+3s4CGoD
OQm+LZCf5pmIbA==
""".trimIndent()

}
