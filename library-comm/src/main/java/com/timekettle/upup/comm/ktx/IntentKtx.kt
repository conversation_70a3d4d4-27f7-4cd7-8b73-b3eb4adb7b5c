package com.timekettle.upup.comm.ktx

import android.content.Context
import android.content.Intent
import android.util.Log
import android.widget.Button
import android.widget.TextView
import com.timekettle.upup.base.BaseApp
import java.util.Locale

//fun Intent.setLedLevel(context: Context, level: Int) {
//    this.putExtra("level", level)
//    context.sendBroadcast(this)
//}

fun setLedLevel(context: Context, level: Int) = Intent("nstart.intent.action.LedLight").apply {
    this.putExtra("level", level)
    context.sendBroadcast(this)
}

fun TextView.setTextSizeByLanguage(size: Float, vararg languages: String) {
    if (languages.contains(BaseApp.context.resources.configuration.locale.language)) {
        this.textSize = size
    }
}