package com.timekettle.upup.comm.utils

import android.content.Context
import android.net.ConnectivityManager
import android.net.Network
import android.net.NetworkCapabilities
import android.net.NetworkRequest
import android.os.PowerManager
import android.util.Log
import com.blankj.utilcode.util.NetworkUtils
import com.timekettle.upup.base.BaseApp
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.delay
import kotlinx.coroutines.launch


object MyNetWorkUtil {

    // 使用NetWorkUtils的ping方法，ping一次得到结果，如果ping不通，返回false，如果ping通，返回true
    @Volatile
    var isOnline = true
    var isCaptivePortal = false
    private var failCount = 0

    init {
        val connectivityManager = BaseApp.context.getSystemService(Context.CONNECTIVITY_SERVICE) as ConnectivityManager
        // 创建一个网络请求构建器
        val builder = NetworkRequest.Builder()
        // 添加Wi-Fi传输类型
        builder.addTransportType(NetworkCapabilities.TRANSPORT_WIFI)
        // 注册网络回调
        connectivityManager.registerNetworkCallback(
            builder.build(),
            object : ConnectivityManager.NetworkCallback() {

                override fun onAvailable(network: Network) {
                    Log.d("NetworkUtil", "onNetwork Available = $network")
                    // 获取网络能力
                    val networkCapabilities = connectivityManager.getNetworkCapabilities(network)
                    isCaptivePortal = networkCapabilities != null && networkCapabilities.hasCapability(
                            NetworkCapabilities.NET_CAPABILITY_CAPTIVE_PORTAL
                        )
                }

                override fun onCapabilitiesChanged(
                    network: Network,
                    networkCapabilities: NetworkCapabilities
                ) {
                    Log.d("NetworkUtil", "onCapabilitiesChanged = ${
                        networkCapabilities.hasCapability(
                            NetworkCapabilities.NET_CAPABILITY_CAPTIVE_PORTAL
                        )
                    }")
                    isCaptivePortal = networkCapabilities.hasCapability(
                        NetworkCapabilities.NET_CAPABILITY_CAPTIVE_PORTAL
                    )
                }
            })

        BaseApp.mCoroutineScope.launch(Dispatchers.IO) {
            delay(1000)
            while (true){
                checkNetWork()
            }
        }
    }

    private suspend fun checkNetWork() {
        if (!isScreenOn()) {
//            logD("已经锁屏了，不需要检查网络是否上网")
            delay(4000)
            return
        }
        if (checkNetworkAvailable()) {
            isOnline = !isCaptivePortal
//            logD("是否可以正常上网？true🤣")
            delay(3000)
        } else {
            failCount++
            if (failCount > 3) {
                failCount = 3
                isOnline = false
            }
//            logD("是否可以正常上网？false😭")
            delay(1500)
        }
    }


    private fun isScreenOn(): Boolean {
        val pm =
            BaseApp.context.getSystemService(Context.POWER_SERVICE) as PowerManager
        return pm.isScreenOn //如果为true，则表示屏幕“亮”了，否则屏幕“暗”了。
    }

    private fun isWifiOnline(): Boolean {
        return NetworkUtils.isAvailableByPing("1.1.1.1")
    }

    private fun checkNetworkAvailable(): Boolean {
        var isNetUsable = false
        val manager = BaseApp.context.getSystemService(Context.CONNECTIVITY_SERVICE) as ConnectivityManager
        val networkCapabilities = manager.getNetworkCapabilities(manager.activeNetwork)
        if (networkCapabilities != null) {
            isNetUsable =
                networkCapabilities.hasCapability(NetworkCapabilities.NET_CAPABILITY_VALIDATED)
        }
        return isNetUsable
    }


}