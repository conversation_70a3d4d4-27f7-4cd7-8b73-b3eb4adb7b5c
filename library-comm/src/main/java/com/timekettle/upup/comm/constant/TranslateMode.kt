package com.timekettle.upup.comm.constant

import android.os.Parcelable
import com.timekettle.upup.base.BaseApp
import com.timekettle.upup.comm.R
import kotlinx.android.parcel.Parcelize

/**
 * 本地存储的键 放在此类中
 *
 * <AUTHOR>
 * @since 2022/04/15
 * @note : DBName 不要随意更改！
 */

@Parcelize
enum class TranslateMode(private var _modeName: String) : Parcelable {

    SIMUL(BaseApp.context.getString(R.string.home_one_to_one)),
    PHONE(BaseApp.context.getString(R.string.home_voice_call)),
    MEETING(BaseApp.context.getString(R.string.home_meeting)),
    SPEAKER(BaseApp.context.getString(R.string.home_ask_go)),
    KEYPAD(BaseApp.context.getString(R.string.home_one_touch)),
    VIDEO(BaseApp.context.getString(R.string.home_audio_and_video)),
    SPEECH(BaseApp.context.getString(R.string.home_speech_translation)),
    SETTING(BaseApp.context.getString(R.string.home_seeting)),
    UNKNOWN("UnKnown");

    // 可变属性来保存 modeName 的值，避免切换语言后国际化不更新
    var modeName: String
        get() = when (this) {
            SIMUL -> BaseApp.context.getString(R.string.home_one_to_one)
            PHONE -> BaseApp.context.getString(R.string.home_voice_call)
            MEETING -> BaseApp.context.getString(R.string.home_meeting)
            SPEAKER -> BaseApp.context.getString(R.string.home_ask_go)
            KEYPAD -> BaseApp.context.getString(R.string.home_one_touch)
            VIDEO -> BaseApp.context.getString(R.string.home_audio_and_video)
            SPEECH -> BaseApp.context.getString(R.string.home_speech_translation)
            SETTING -> BaseApp.context.getString(R.string.home_seeting)
            else -> "UnKnown"
        }
        set(value) {
            _modeName = value
        }
    // 数据库表中的字段名，不要随意更改！
    fun getDBName(): String {
        return this.name
    }

}