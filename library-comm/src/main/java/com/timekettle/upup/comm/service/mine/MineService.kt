package com.timekettle.upup.comm.service.mine

import com.alibaba.android.arouter.facade.template.IProvider
import com.timekettle.upup.base.utils.SpUtils
import com.timekettle.upup.comm.constant.TmkProductType
import com.timekettle.upup.comm.model.OfflineEligibility

/**
 **  我的（Mine）模块提供的服务
 **
 */
interface MineService : IProvider {

    fun saveOfflineBuyList(data: OfflineEligibility)

    fun getOfflineEligibility(): OfflineEligibility?


    fun clearMineBean()

}