package com.timekettle.upup.comm.net.helper

import com.timekettle.upup.base.utils.SpUtils
import com.timekettle.upup.comm.constant.SpKey

/**
 * token管理者
 *
 */
object TokenManager {

    var accessToken: String = SpUtils.getString(SpKey.ACCESS_TOKEN, "")
        set(value) {
            field = value
            SpUtils.putString(SpKey.ACCESS_TOKEN, value)
        }
    var refreshToken: String = SpUtils.getString(SpKey.REFRESH_TOKEN, "")
        set(value) {
            field = value
            SpUtils.putString(SpKey.REFRESH_TOKEN, value)
        }

    fun clearAllTokens() {
        accessToken = ""
        refreshToken = ""
    }


//    /**
//     * 从缓存中获取token
//     * @return String token
//     */
//    fun getAccessToken(): String = mAccessToken
//
//    /**
//     * 将token保存至MMKV，并刷新缓存到内存的token
//     *
//     * @param token String 新的token
//     * @return Unit
//     */
//    fun saveAccessToken(token: String) {
//        mAccessToken = token
//        SpUtils.putString(SpKey.ACCESS_TOKEN, token)
//    }
}