package com.timekettle.upup.comm.ui

import SpConstant
import android.annotation.SuppressLint
import android.bluetooth.BluetoothAdapter
import android.content.Intent
import android.net.Uri
import android.provider.Settings
import android.util.Log
import android.widget.RadioButton
import androidx.activity.viewModels
import androidx.core.widget.addTextChangedListener
import androidx.lifecycle.lifecycleScope
import co.timekettle.agora.SpeechManager
import co.timekettle.agora.common.enums.EnvType
import co.timekettle.btkit.BleCmdContant.AppCmdId
import co.timekettle.btkit.BleUtil
import com.alibaba.android.arouter.facade.annotation.Route
import com.blankj.utilcode.util.ConvertUtils
import com.blankj.utilcode.util.FileUtils
import com.blankj.utilcode.util.PathUtils
import com.blankj.utilcode.util.ZipUtils
import com.kongzue.dialogx.dialogs.WaitDialog
import com.tencent.bugly.crashreport.CrashReport
import com.timekettle.upup.base.BaseApp
import com.timekettle.upup.base.ktx.clickDelay
import com.timekettle.upup.base.ktx.observeLiveData
import com.timekettle.upup.base.utils.DateUtils
import com.timekettle.upup.base.utils.SpUtils
import com.timekettle.upup.base.utils.logD
import com.timekettle.upup.base.utils.showToast
import com.timekettle.upup.comm.base.BaseActivity
import com.timekettle.upup.comm.bean.MeetEngineHost
import com.timekettle.upup.comm.constant.NetUrl
import com.timekettle.upup.comm.constant.RouteUrl
import com.timekettle.upup.comm.constant.SpKey
import com.timekettle.upup.comm.constant.SpKey.SHOW_TMK_ENGINE_HOST_IP_IN_CHAT
import com.timekettle.upup.comm.databinding.CommActivityControlBinding
import com.timekettle.upup.comm.dialog.DialogFactory
import com.timekettle.upup.comm.model.IpBean
import com.timekettle.upup.comm.model.TmkEngineBean
import com.timekettle.upup.comm.service.home.HomeServiceImplWrap
import com.timekettle.upup.comm.service.trans.TransServiceImplWrap
import com.timekettle.upup.comm.tools.AKSManager
import com.timekettle.upup.comm.tools.PKCS12Manager
import com.timekettle.upup.comm.utils.DeviceUtil
import com.timekettle.upup.comm.viewmodel.VMCommonControl
import com.timekettle.upup.comm.widget.KeyboardUtil
import com.timekettle.upup.comm.widget.MyKeyboardView
import com.tmk.libserialhelper.tmk.W3ProHelper
import com.tmk.libserialhelper.tmk.util.DongoControlUtil
import kotlinx.coroutines.launch

/**
 * 公共的
 * @Desc 调试面板页、控制页面、调试控制台、调试面板界面，调试界面
 */
//@AndroidEntryPoint
@Route(path = RouteUrl.Comm.ControlPanel)
class CommControlActivity : BaseActivity<CommActivityControlBinding, VMCommonControl>() {

    private lateinit var keyboardUtil: KeyboardUtil
    override val mViewModel: VMCommonControl by viewModels()

    companion object {}

    override fun CommActivityControlBinding.initView() {
        keyboardUtil = KeyboardUtil(this@CommControlActivity)

        vTitleBar.run {
            vTitleTv.text = "调试面板"
        }

        val localUrl = SpUtils.getString(SpKey.BASE_URL, NetUrl.RELEASE_URL)
        textEnvCur.text = textEnvCur.text.toString().replace("xxx", localUrl)
        when (localUrl) {
            NetUrl.DEV_URL -> radioLianTiao.isChecked = true
            NetUrl.TEST_URL_X1_NEW -> radioX1test.isChecked = true
            NetUrl.UAT_URL -> radioUat.isChecked = true
            NetUrl.RELEASE_URL -> radioRelease.isChecked = true
            else -> {
                etCustomUrl.setText(localUrl)
                radioCustom.isChecked = true
            }
        }
        mBinding.etCustomUrl.isEnabled = mBinding.radioCustom.isChecked

        val businessBaseUrl = SpUtils.getString(SpKey.BUSINESS_BASE_URL, NetUrl.BUSINESS_RELEASE_URL)
        val captainBaseUrl = SpUtils.getString(SpKey.CAPTAIN_BASE_URL, NetUrl.BUSINESS_RELEASE_URL)
        when (businessBaseUrl) {
            NetUrl.BUSINESS_DEV_URL -> radioDev.isChecked = true
            NetUrl.BUSINESS_SINGAPORE_DEV_URL -> radioSingaporeDev.isChecked = true
            NetUrl.BUSINESS_TEST_URL -> radioTest.isChecked = true
            NetUrl.BUSINESS_UAT_URL -> radioUat2.isChecked = true
            NetUrl.BUSINESS_AMERICA_RELEASE_URL -> radioAmericaDev.isChecked = true
            NetUrl.BUSINESS_RELEASE_URL -> radioRelease2.isChecked = true
            else -> {
                etCustomUrl2.setText("$businessBaseUrl;$captainBaseUrl")
                radioCustom2.isChecked = true
            }
        }
        mBinding.etCustomUrl2.isEnabled = mBinding.radioCustom2.isChecked

        when (SpUtils.getString(SpKey.SA_URL, NetUrl.RELEASE_SA_SERVER_URL)) {
            NetUrl.TEST_SA_SERVER_URL -> saRadioTest.isChecked = true
            NetUrl.RELEASE_SA_SERVER_URL -> saRadioRelease.isChecked = true
        }

        checkRecordAudio.isChecked = SpUtils.getBoolean(SpKey.IS_RECORD_AUDIO_OPEN, false)
        checkCacheAll.isChecked = SpUtils.getBoolean(SpKey.CACHE_ALL_FILE, false)
        swShowEngine.isChecked = SpUtils.getBoolean(SpKey.SHOW_TMK_ENGINE_HOST_IP_IN_CHAT, false)
        // Mac地址调试用
        val debugMacString = SpUtils.getString(SpKey.NEED_CONN_TEST_MAC, "")
        etConnMac.setText(debugMacString)
        // 耳机算法版本
        val serials = W3ProHelper.getTwoHeadsetInfo()
        if (serials.size > 0) {
            val stringBuilder = StringBuilder().apply {
                for (serial in serials) {
                    val hexString = String.format("%04x", serial.voltage)
                    append(
                        String.format("%s( %s %s.%s );  ",
                            if (serial.headsetRole == 1) "左耳"
                            else if (serial.headsetRole == 2) "右耳"
                            else "未知",
                            if (hexString[0] == '0') "正式" else "测试",
                            hexString[1].digitToInt(16),
                            hexString.substring(2, 4).toInt(16)
                        )
                    )
                }
            }
            headsetVersion.text = stringBuilder.toString()
        }
    }

    private fun changeEnv(env: String) {
        var extraMsg = ""
        mBinding.etCustomUrl.isEnabled = mBinding.radioCustom.isChecked
        when (env) {
            "liantiao" -> SpUtils.putString(SpKey.BASE_URL, NetUrl.DEV_URL);
            "test" -> SpUtils.putString(SpKey.BASE_URL, NetUrl.TEST_URL_X1_NEW)
            "uat" -> SpUtils.putString(SpKey.BASE_URL, NetUrl.UAT_URL)
            "release" -> SpUtils.putString(SpKey.BASE_URL, NetUrl.RELEASE_URL)
            "mock" -> SpUtils.putString(SpKey.BASE_URL, NetUrl.MOCK_URL)
            "custom" -> {
            }
        }

        SpUtils.putString(SpKey.TMK_SIP_HOST_IP, "")
        showToast("切换环境后重启生效$extraMsg")
    }

    private fun changeLingCaseEnv(env: String) {
        mBinding.etCustomUrl2.isEnabled = mBinding.radioCustom2.isChecked
        when (env) {
            "dev" -> {
                SpUtils.putString(SpKey.BUSINESS_BASE_URL, NetUrl.BUSINESS_DEV_URL)
                SpUtils.putString(SpKey.CAPTAIN_BASE_URL, NetUrl.CAPTAIN_DEV_URL)
            }

            "singaporeDev" -> {
                SpUtils.putString(SpKey.BUSINESS_BASE_URL, NetUrl.BUSINESS_SINGAPORE_DEV_URL)
                SpUtils.putString(SpKey.CAPTAIN_BASE_URL, NetUrl.CAPTAIN_SINGAPORE_DEV_URL)
            }

            "test" -> {
                SpUtils.putString(SpKey.BUSINESS_BASE_URL, NetUrl.BUSINESS_TEST_URL)
                SpUtils.putString(SpKey.CAPTAIN_BASE_URL, NetUrl.BUSINESS_TEST_URL)
            }

            "uat" -> {
                SpUtils.putString(SpKey.BUSINESS_BASE_URL, NetUrl.BUSINESS_UAT_URL)
                SpUtils.putString(SpKey.CAPTAIN_BASE_URL, NetUrl.BUSINESS_UAT_URL)
            }

            "americaRelease" -> {
                SpUtils.putString(SpKey.BUSINESS_BASE_URL, NetUrl.BUSINESS_AMERICA_RELEASE_URL)
                SpUtils.putString(SpKey.CAPTAIN_BASE_URL, NetUrl.BUSINESS_AMERICA_RELEASE_URL)
            }

            "release" -> {
                SpUtils.putString(SpKey.BUSINESS_BASE_URL, NetUrl.BUSINESS_RELEASE_URL)
                SpUtils.putString(SpKey.CAPTAIN_BASE_URL, NetUrl.BUSINESS_RELEASE_URL)
            }

            "custom" -> {
            }
        }

        reset()
        showToast("切换环境后重启生效")
    }

    private fun changeSaEnv(env: String) {
        when (env) {
            "test" -> {
                SpUtils.putString(SpKey.SA_URL, NetUrl.TEST_SA_SERVER_URL)
            }

            "release" -> {
                SpUtils.putString(SpKey.SA_URL, NetUrl.RELEASE_SA_SERVER_URL)
            }
        }

        showToast("切换环境后重启生效")
    }

    override fun initListener() {

        with(mBinding) {
            mBinding.radioLianTiao.setOnClickListener { changeEnv("liantiao") }
            mBinding.radioX1test.setOnClickListener { changeEnv("test") }
            mBinding.radioUat.setOnClickListener { changeEnv("uat") }
            mBinding.radioRelease.setOnClickListener { changeEnv("release") }
            mBinding.radioCustom.setOnClickListener { changeEnv("custom") }

            mBinding.radioDev.setOnClickListener { changeLingCaseEnv("dev") }
            mBinding.radioSingaporeDev.setOnClickListener { changeLingCaseEnv("singaporeDev") }
            mBinding.radioTest.setOnClickListener { changeLingCaseEnv("test") }
            mBinding.radioUat2.setOnClickListener { changeLingCaseEnv("uat") }
            mBinding.radioAmericaDev.setOnClickListener { changeLingCaseEnv("americaRelease") }
            mBinding.radioRelease2.setOnClickListener { changeLingCaseEnv("release") }
            mBinding.radioCustom2.setOnClickListener { changeLingCaseEnv("custom") }

            mBinding.saRadioTest.setOnClickListener { changeSaEnv("test") }
            mBinding.saRadioRelease.setOnClickListener { changeSaEnv("release") }

            mBinding.btnRestartApp.setOnClickListener { restartApplication() }
            mBinding.btnKillApp.setOnClickListener { killApplication() }
            mBinding.btnExportLog.clickDelay { exportLog() }
            mBinding.btnGoToDetail.clickDelay { goToDetail() }
            mBinding.btnOpenAdb.clickDelay { openAdb() }
            mBinding.btnTestNet.clickDelay { goToNetTest() }
            mBinding.swShowEngine.setOnCheckedChangeListener{  _, b ->
                SpUtils.putBoolean(SHOW_TMK_ENGINE_HOST_IP_IN_CHAT, b)
            }

            etCustomUrl.addTextChangedListener {
                if (!it.toString().contains("http://") && !it.toString().contains("https://")) {
                    etCustomUrl.error = "需加入http/https头"
                } else if (!it.toString().endsWith("/")) {
                    etCustomUrl.error = "需加入斜杠/结尾"
                } else {
                    etCustomUrl.error = null
                    SpUtils.putString(SpKey.BASE_URL, it.toString().trim())
                }
            }

            etCustomUrl2.addTextChangedListener {
                if (!it.toString().contains("http://") && !it.toString().contains("https://")) {
                    etCustomUrl2.error = "需加入http/https头"
                } else {
                    etCustomUrl2.error = null
                    val urls = it.toString().split(";")
                    if (urls.size == 2) {
                        SpUtils.putString(SpKey.BUSINESS_BASE_URL, urls[0])
                        SpUtils.putString(SpKey.CAPTAIN_BASE_URL, urls[1])
                    }
                }
            }

            checkRecordAudio.setOnCheckedChangeListener { _, b ->
                SpUtils.putBoolean(SpKey.IS_RECORD_AUDIO_OPEN, b)
            }

            checkCacheAll.setOnCheckedChangeListener { _, b ->
                SpUtils.putBoolean(SpKey.CACHE_ALL_FILE, b)
                showToast("重启应用后生效")
            }

            etConnMac.addTextChangedListener {
                SpUtils.putString(SpKey.NEED_CONN_TEST_MAC, it.toString().trim())
            }

            etConnMac.setOnClickListener {
                keyboardUtil?.initKeyboard(MyKeyboardView.KEYBOARDTYPE_ABC)
            }

            keyboardUtil?.setOnConnectBtnClickListener { content ->
                etConnMac.setText(content)
            }

            btnCrashJava.setOnClickListener {
                CrashReport.testJavaCrash()
            }
            btnCrashNative.setOnClickListener {
                CrashReport.testNativeCrash()
            }
            btnGoToBt.setOnClickListener {
                val bluetoothSettingsIntent = Intent(Settings.ACTION_BLUETOOTH_SETTINGS)
                startActivity(bluetoothSettingsIntent)
            }
            btnDeleteCertificate.setOnClickListener {
                PKCS12Manager.delete()
                AKSManager.deleteKey()
                DeviceUtil.reboot(applicationContext)
            }
            btnOpenBt.setOnClickListener { openBt() }
            btnCloseBt.setOnClickListener { closeBt() }
            btnHeadsetNoiseOn.setOnClickListener { openNoiseFun() }
            btnHeadsetNoiseOff.setOnClickListener { closeNoiseFun() }
            btnOpenDongo.setOnClickListener { openDongo() }
            btnCloseDongo.setOnClickListener { closeDongo() }
        }

    }

    private fun openNoiseFun() {
        showToast("耳机降噪算法：开启")
        BleUtil.shared.connectedPeripherals.forEach {
            BleUtil.shared.sendCmdToQueue(it,  AppCmdId.TestSettingsNREnable)
        }
    }

    private fun closeNoiseFun() {
        showToast("耳机降噪算法：关闭")
        BleUtil.shared.connectedPeripherals.forEach {
            BleUtil.shared.sendCmdToQueue(it, AppCmdId.TestSettingsNRDisable)
        }
    }

    @SuppressLint("MissingPermission")
    private fun openBt() {
        val bluetoothAdapter = BluetoothAdapter.getDefaultAdapter()
        bluetoothAdapter.enable()
    }

    @SuppressLint("MissingPermission")
    private fun closeBt() {
        val bluetoothAdapter = BluetoothAdapter.getDefaultAdapter()
        bluetoothAdapter.disable()
    }

    private fun openDongo(){
        DongoControlUtil.openDongo()
        showToast("打开Dongo会导致USB有线调试无法使用，建议先连接无线调试。蓝牙Dongo已打开✅可以搜索到蓝牙了")
    }

    private fun closeDongo(){
        DongoControlUtil.closeDongo()
        showToast("蓝牙Dongo已关闭❎蓝牙无法被搜索到")
    }

    private fun goToNetTest() {
        startActivity(Intent(this, CommNetTestActivity::class.java))
    }

    // 重启应用程序
    private fun restartApplication() {
        val context = this@CommControlActivity
        val packageManager = context.packageManager
        val intent = packageManager.getLaunchIntentForPackage(context.packageName)
        val componentName = intent?.component
        val mainIntent = Intent.makeRestartActivityTask(componentName)
        context.startActivity(mainIntent)
        System.exit(0)
    }

    private fun reset() {
        SpUtils.put(SpKey.ROOM_NUMBER, "")
        SpUtils.put(SpKey.ROOM_QR_CODE, "")
        SpeechManager.setAgoraToken("")
        SpeechManager.setCaptainToken("")
    }

    // 退出应用程序
    private fun killApplication() {
        lifecycleScope.launch {
//            ActivityUtils.finishAllActivities()
//            val homeIntent = Intent(Intent.ACTION_MAIN)
//            homeIntent.addCategory(Intent.CATEGORY_HOME)
//            homeIntent.flags = Intent.FLAG_ACTIVITY_NEW_TASK
//            <EMAIL>(homeIntent)
//            exitProcess(0)
//            ActivityUtils.finishAllActivities()

            // https://www.ctyun.cn/developer/article/362875683246149  这个和强行停止的作用是一样的
            val method = Class.forName("android.app.ActivityManager").getMethod(
                "forceStopPackage",
                String::class.java
            )
            method.invoke(
                BaseApp.context.getSystemService(ACTIVITY_SERVICE),
                packageName
            )


        }
    }

    private fun goToDetail() {
        val packageName = packageName
        val intent = Intent(
            android.provider.Settings.ACTION_APPLICATION_DETAILS_SETTINGS,
            Uri.parse("package:$packageName")
        )
        intent.addCategory(Intent.CATEGORY_DEFAULT)
        intent.flags = Intent.FLAG_ACTIVITY_NEW_TASK
        startActivity(intent)
    }

    private fun openAdb() {
        if (Settings.Global.getInt(contentResolver, Settings.Global.ADB_ENABLED, 0) == 1) {
            showToast("ADB调试已打开✅")
            return
        } else {
            Settings.Global.putInt(contentResolver, Settings.Global.ADB_ENABLED, 1)
            showToast("ADB调试打开成功😊")
        }
    }

    private fun exportLog() {
        logD("正在导出日志...")
        var speechLogDir = if (SpUtils.getBoolean(SpKey.CACHE_ALL_FILE, false)) {
            PathUtils.getExternalAppCachePath() + "/userlog"
        } else {
            PathUtils.getInternalAppCachePath() + "/Log"
        }
        val wifiLogDir = PathUtils.getExternalStoragePath() + "/WifiLog"
        FileUtils.createOrExistsDir(wifiLogDir)

        val rootDir = PathUtils.getExternalStoragePath() + "/Timekettle X1/Log"
        FileUtils.createOrExistsDir(rootDir)

        val logDstName = "日志${DateUtils.getCurLogDateTime()}.zip"
        val zipFilePath = "$rootDir/$logDstName"
        FileUtils.delete(zipFilePath)

        val recordPath = PathUtils.getExternalAppCachePath() + "/TK_Record"
        FileUtils.createOrExistsDir(recordPath)
        val exportRecordPath = "$rootDir/录音文件/"
        FileUtils.createOrExistsDir(exportRecordPath)

        try {
//            if (ZipUtils.zipFiles(mutableListOf(speechLogDir, recordPath), zipFilePath)) {
            FileUtils.copy(recordPath, exportRecordPath)
            if (ZipUtils.zipFiles(mutableListOf(speechLogDir, wifiLogDir), zipFilePath)) {
                logD("日志导出成功 $zipFilePath")
                WaitDialog.dismiss()
                DialogFactory.createTipsDialog(
                    this@CommControlActivity,
                    content = "日志导出成功，用USB线连接到电脑，打开Timekettle X1文件夹就能够看到了（$logDstName）",
                ).show()

            } else {
                DialogFactory.createTipsDialog(
                    this@CommControlActivity,
                    content = "日志导出失败1",
                ).show()
                WaitDialog.dismiss()
            }
        } catch (e: Exception) {
            DialogFactory.createTipsDialog(
                this@CommControlActivity,
                content = "日志导出失败2 $e",
            ).show()
        }

    }

    override fun initObserve() {
        observeLiveData(mViewModel.liveEngineIpList, ::processEngineIpList)
        observeLiveData(mViewModel.liveSipIpList, ::processSipIpList)
    }

    private fun processSipIpList(ipEntities: MutableList<MeetEngineHost>) {
        val sipIps = mutableListOf<MeetEngineHost>()
        sipIps.add(0, MeetEngineHost(0,""))
        sipIps.addAll(ipEntities)

        val sipIpsName = ipEntities.map { it.ip }.toMutableList().also {
            it.add(0, "由程序选择最快的")
        }

        sipIps.forEach {
            Log.d("CommControlActivity","id ${it.id}---ip ${it.ip}")
        }

        val curSipIp = SpUtils.getString(SpKey.TMK_SIP_HOST_IP, "")
        mBinding.textSipIP.text = "当前: ${curSipIp.ifEmpty { "自动选择" }}"

        for (i in sipIpsName.indices) {
            val button = RadioButton(this).apply {
                text = sipIpsName[i]
                id = i
                isChecked = SpUtils.getString(SpKey.TMK_SIP_HOST_IP, "") == sipIps[i].ip
                setOnClickListener {
                    SpUtils.putString(SpKey.TMK_SIP_HOST_IP, sipIps[i].ip)
                    if (sipIps[i].ip.isNotEmpty()) {
                        HomeServiceImplWrap.saveSipIp(IpBean(sipIps[i].id, sipIps[i].ip + ":5166;transport=tcp"))
                    }

                    showToast("切换环境后重启生效")
                }
                height = ConvertUtils.dp2px(24f)
            }

            mBinding.rgSipIp.addView(button, i)
        }
    }

    private fun processEngineIpList(tmkEngineBeans: MutableList<TmkEngineBean>) {
        val hostIps = tmkEngineBeans.map { it.ip }.toMutableList().also {
            it.add(0, "")
        }
        val hostIpsName = tmkEngineBeans.map { it.toRadioString() }.toMutableList().also {
            it.add(0, "由程序选择最快的")
        }
        val curHostIp = SpUtils.getString(SpKey.TMK_ENGINE_HOST_IP, "")
        mBinding.textHostIP.text = "当前: ${curHostIp.ifEmpty { "自动选择" }}"
        var isMyInputIp = true
        for (i in hostIpsName.indices) {
            val button = RadioButton(this).apply {
                text = hostIpsName[i]
                id = i
                isChecked = SpUtils.getString(SpKey.TMK_ENGINE_HOST_IP, "") == hostIps[i]
                setOnClickListener {
                    SpUtils.putString(SpKey.TMK_ENGINE_HOST_IP, hostIps[i])
                }
                height = ConvertUtils.dp2px(24f)
            }
            if (button.isChecked) {
                isMyInputIp = false
            }
            mBinding.rgIp.addView(button, i)
        }
        if (isMyInputIp && SpUtils.getString(SpKey.TMK_ENGINE_HOST_IP, "").isNotEmpty()) {
            mBinding.hostIpCustom.isChecked = true
            mBinding.edHostIpCustom.setText(SpUtils.getString(SpKey.TMK_ENGINE_HOST_IP, ""))
        }
        mBinding.edHostIpCustom.addTextChangedListener {
            if (mBinding.hostIpCustom.isChecked) {
                SpUtils.putString(SpKey.TMK_ENGINE_HOST_IP, it.toString().trim())
            }
        }
    }

    override fun initRequestData() {
        mViewModel.fetchHosts(TransServiceImplWrap.getSpeechBaseUrl() + SpConstant.HOST_ROUTES)
        mViewModel.fetchSipHosts()
    }

}