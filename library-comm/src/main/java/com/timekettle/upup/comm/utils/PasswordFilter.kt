package com.timekettle.upup.comm.utils

import android.text.InputFilter
import android.text.Spanned
import com.timekettle.upup.comm.constant.CommonRegex

/**
 * <AUTHOR>
 * @date 2022/10/11
 * @desc
 */
class PasswordFilter : InputFilter {
    override fun filter(
        source: CharSequence,
        start: Int,
        end: Int,
        dest: Spanned,
        dstart: Int,
        dend: Int
    ): CharSequence {
        return CommonRegex.regexIllegalPasswordInput.replace(source, "")
    }
}