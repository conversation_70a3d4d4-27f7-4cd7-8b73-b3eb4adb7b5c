package com.timekettle.upup.comm.net.interceptor

import android.util.Log
import com.timekettle.upup.base.utils.SpUtils
import com.timekettle.upup.base.utils.logD
import com.timekettle.upup.base.utils.logE
import com.timekettle.upup.comm.constant.NetUrl
import com.timekettle.upup.comm.constant.SpKey
import com.timekettle.upup.comm.net.CommApiService
import com.timekettle.upup.comm.net.helper.ReasonCodeEnum
import com.timekettle.upup.comm.net.helper.ReqSignUtils
import com.timekettle.upup.comm.net.helper.ResponseCodeEnum
import com.timekettle.upup.comm.net.helper.TokenManager
import com.timekettle.upup.comm.service.login.LoginServiceImplWrap
import com.timekettle.upup.comm.tools.DeviceTool
import okhttp3.*
import okhttp3.ResponseBody.Companion.toResponseBody
import okio.Buffer
import org.json.JSONObject
import retrofit2.Retrofit
import java.io.IOException
import javax.inject.Inject

/**
 *  自定义token拦截器
 *  功能：在token过期之后，通过refreshToken，再获取一个新的token
 * <AUTHOR>
 * @since 2021/7/9
 */
class TokenInterceptor : Interceptor {


    @Inject
    lateinit var retrofit: Retrofit

    override fun intercept(chain: Interceptor.Chain): Response {
        val baseUrl = SpUtils.getString(SpKey.BASE_URL, NetUrl.RELEASE_URL)
        val originalRequest = chain.request() // 原始请求
        val timestamp = System.currentTimeMillis().toString()
        val fullPath = originalRequest.url.encodedPath
//        Log.d("TAG","请求路径 : $fullPath")
        val paramsMap: MutableMap<String, String> = mutableMapOf()
        if (originalRequest.method == "POST") {
            if (originalRequest.body is FormBody) {
                val formBody = originalRequest.body as FormBody
                for (i in 0 until formBody.size) {
                    paramsMap[formBody.encodedName(i)] = formBody.encodedValue(i)
                }
            }else{
                originalRequest.body?.let { body ->
                    val buffer = Buffer()
                    body.writeTo(buffer)
                    val json = buffer.readUtf8()
                    val jsonObject = JSONObject(json)
                    jsonObject.keys().forEach { key ->
                        paramsMap[key] = jsonObject.getString(key)
                    }
                }
            }
        } else if (originalRequest.method == "GET") {
            originalRequest.url.queryParameterNames.forEach { key ->
                paramsMap[key] = originalRequest.url.queryParameter(key) ?: ""
            }
        }

//        Log.d("TAG", "参数 : $paramsMap")

        // 获取API路径的特定部分
        val apiPath = fullPath.replace(baseUrl, "/")
        val tokenRequest = originalRequest.newBuilder()
            .addHeader("Sn-Code", getSNNumber())
            .addHeader("Timestamp", timestamp)
            .addHeader("Sign", genSign(apiPath, timestamp, paramsMap))
            .build()  // 加上token 之后的的 带token的请求
        return chain.proceed(tokenRequest)
    }


    private fun genSign(apiPath: String, timestamp: String, params:MutableMap<String, String>): String {
        val headerMap = mutableMapOf<String, String>()
        headerMap["snCode"] = getSNNumber()
        headerMap["timestamp"] = timestamp
        headerMap["apiPath"] = apiPath
        headerMap.putAll(params) // 把参数添加进来
        var signContent = ReqSignUtils.toSignedContent(headerMap)
//        Log.d("TAG", "签名内容 : $signContent")
        // 移除开始和结束的标记，以及换行
        val strippedPrivateKey = ReqSignUtils.PRIVATE_KEY
        var sign = ReqSignUtils.sign(signContent, strippedPrivateKey)
        return sign
    }

    private fun getSNNumber(): String {
        return DeviceTool.getSerialNumber()
    }


    /**
     * token是否过期
     * @param resp Response
     */
    private fun isTokenExpired(resp: Response): Boolean {
        try {
            val responseBody: ResponseBody? = resp.body
            val body = responseBody?.string()
            val jsonObject = JSONObject(body)
            if (jsonObject != null) {
                // success字段为true，才去解析data里面的数据
                if (!jsonObject.optBoolean("success", false)) {
                    when (jsonObject.optInt("reasonCode", -1)) {
                        ReasonCodeEnum.TokenInvalid.reasonCode,
                        ReasonCodeEnum.TokenExpired.reasonCode,
                        ReasonCodeEnum.RemotingLoginExpired.reasonCode -> {
                            logE(jsonObject.toString(), "Token过期了！")
                            return true
                        }

                        else -> {
                            logD("Token没过期")
                        }
                    }
                }
            }
        } catch (e: IOException) {
            e.printStackTrace()
        }
        return false
    }

}