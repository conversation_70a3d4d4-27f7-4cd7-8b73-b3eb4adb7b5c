package com.timekettle.upup.comm

import android.annotation.SuppressLint
import android.app.Application
import android.content.Context
import android.content.Intent
import android.content.IntentFilter
import android.os.Looper
import android.util.Log
import co.timekettle.agora.SpeechManager
import co.timekettle.agora.common.enums.EnvType
import co.timekettle.agora.common.enums.TmkProjectType
import co.timekettle.agora.common.models.BusinessParams
import co.timekettle.agora.common.models.LingCastParams
import co.timekettle.btkit.BleUtil
import com.alibaba.android.arouter.launcher.ARouter
import com.blankj.utilcode.util.DeviceUtils
import com.google.auto.service.AutoService
import com.orhanobut.logger.*
import com.sensorsdata.analytics.android.sdk.SAConfigOptions
import com.sensorsdata.analytics.android.sdk.SensorsAnalyticsAutoTrackEventType
import com.sensorsdata.analytics.android.sdk.SensorsDataAPI
import com.tencent.bugly.crashreport.CrashReport
import com.tencent.smtt.export.external.TbsCoreSettings
import com.tencent.smtt.sdk.QbSdk
import com.timekettle.upup.base.BaseApp
import com.timekettle.upup.base.app.ApplicationLifecycle
import com.timekettle.upup.base.constant.LogTAG
import com.timekettle.upup.base.constant.VersionStatus
import com.timekettle.upup.base.listener.network.NetworkStateClient
import com.timekettle.upup.base.utils.ProcessUtils
import com.timekettle.upup.base.utils.SpUtils
import com.timekettle.upup.base.utils.logE
import com.timekettle.upup.base.utils.showDebugToast
import com.timekettle.upup.comm.constant.NetUrl
import com.timekettle.upup.comm.constant.SpKey
import com.timekettle.upup.comm.constant.SpKey.ALREADY_READ_PRIVACY
import com.timekettle.upup.comm.log.LogUtil
import com.timekettle.upup.comm.log.MyDiskLogHandler
import com.timekettle.upup.comm.log.MyDiskLogStrategy
import com.timekettle.upup.comm.log.MyPrettyFormatStrategy
import com.timekettle.upup.comm.net.helper.BusinessManager
import com.timekettle.upup.comm.receiver.BatteryReceiver
import com.timekettle.upup.comm.service.home.HomeServiceImplWrap
import com.timekettle.upup.comm.service.setting.SettingServiceImplWrap
import com.timekettle.upup.comm.tools.DeviceTool
import com.timekettle.upup.comm.tools.HallScanManager
import com.timekettle.upup.comm.utils.MyActivityUtil
import com.timekettle.upup.comm.utils.USBHIDController
import com.tmk.libserialhelper.serialport.OnSerialValidDataListener
import com.tmk.libserialhelper.serialport.TmkSerial
import com.tmk.libserialhelper.serialport.TmkSerialHelper
import com.tmk.libserialhelper.tmk.W3ProUpgradeCMD
import com.tmk.libserialhelper.tmk.util.VoltageControlUtil
import org.json.JSONException
import org.json.JSONObject


/**
 * 公共模块的伪 Application
 *
 * <AUTHOR>
 * @since 2022/04/15
 */
@AutoService(ApplicationLifecycle::class)
class CommApp : ApplicationLifecycle {


    companion object {
        //静态代码段可以防止内存泄露
        init {

        }

        // 全局CommonApplication
        @SuppressLint("StaticFieldLeak")
        lateinit var mCommonApplication: CommApp

        var isSensorsInit = false // 神策是否已经初始化完成
        var isBuglyInit = false // Bugly是否已经初始化完成，这两个都是需要再用户同意之后才能初始化的
        var isWebViewInit = false // X5 WebView是否已经初始化

    }

    override fun onAttachBaseContext(context: Context) {
        mCommonApplication = this
    }

    override fun onCreate(application: Application) {}

    override fun onTerminate(application: Application) {}


    /**
     * 主线程前台初始化
     * @return MutableList<() -> String> 初始化方法集合
     */
    override fun initByFrontDesk(): MutableList<() -> String> {
        val list = mutableListOf<() -> String>()
        // 以下只需要在主进程当中初始化 按需要调整
        if (ProcessUtils.isMainProcess(BaseApp.context)) {
            list.add { initMMKV() }
            list.add { initLogger() }
            list.add { initARouter() }
            list.add { initNetworkStateClient() }
            list.add { initSensorsDataSDK() }
            list.add { initBatteryReceiver() }
            list.add { initUSBHIDController() }
            list.add { initSpeechManager() }
            list.add { initBusinessTokenWorker() }
            list.add {
                BleUtil.shared.init(BaseApp.context, SpUtils.getBoolean("CACHE_ALL_FILE",false))
                "initBleUtil -->> init complete"
            }
            TmkSerialHelper.openAllPorts()
            TmkSerialHelper.addDataReceiveListener(mSerialListener)
            VoltageControlUtil.changeToChargeAll() // 转为通信状态
            HallScanManager.registerReceiver(BaseApp.context)
//            list.add { initSensorsDataSDK() }

        }
        return list
    }


    /**
     * 不立即使用的后台线程进行初始化
     */
    override fun initByBackstage() {
//        initX5WebViewCore()
        initTencentBugly()
        LogUtil.getUserDeviceInfo()
        SettingServiceImplWrap.initSerialPort()
        //初始化ble连接，并提供全局的viewModel
        HomeServiceImplWrap.initBleConnect()
    }

    /**
     * 阿里路由 ARouter 初始化
     */
    private fun initARouter(): String {
        // 测试环境下打开ARouter的日志和调试模式 正式环境需要关闭
        if (BuildConfig.VERSION_TYPE != VersionStatus.RELEASE) {
            // 打印日志
            ARouter.openLog()
            // 开启调试模式(如果在InstantRun模式下运行，必须开启调试模式！线上版本需要关闭,否则有安全风险)
            ARouter.openDebug()
        }
        ARouter.init(BaseApp.application)
        Log.d(LogTAG.APP_CREATE_TAG, "ARouter -->> init complete")
        return "ARouter -->> init complete"
    }

    /**
     * 初始化 腾讯Bugly
     * 测试环境应该与正式环境的日志收集渠道分隔开
     * 目前有两个渠道 测试版本/正式版本
     */
    fun initTencentBugly(): String {

        val appId = "d74fb7a6d7"
        Log.d(LogTAG.APP_CREATE_TAG, "initTencentBugly: appId:$appId")
        // 如果App使用了多进程且各个进程都会初始化Bugly（例如在Application类onCreate()中初始化Bugly）
        // 那么每个进程下的Bugly都会进行数据上报，造成不必要的资源浪费。
        // 因此，为了节省流量、内存等资源，建议初始化的时候对上报进程进行控制，只在主进程下上报数据
        // 判断是否是主进程（通过进程名是否为包名来判断），并在初始化Bugly时增加一个上报进程的策略配置。
        // 设置上报进程为主进程
        val userStrategy = CrashReport.UserStrategy(BaseApp.context).apply {
            // 设置是否是上传进程
            isUploadProcess = ProcessUtils.isMainProcess(BaseApp.context)
            // 设置渠道
            appChannel = "X1App"
            // 设置 联网上传数据的延迟时间
            appReportDelay = 10L * 1000L
            // 设置设备型号，隐私政策更改之后，需要用户手动上传型号
            deviceModel = DeviceUtils.getModel()
            // 设置设备ID，Bugly不会主动获取ID了，也需要手动上报
            deviceID = DeviceUtils.getUniqueDeviceId()
        }

        userStrategy.setCrashHandleCallback(object : CrashReport.CrashHandleCallback() {
            @Synchronized
            override fun onCrashHandleStart(crashType: Int, errorType: String, errorMessage: String, errorStack: String): Map<String, String>? {
                // 异常发生时的回调
                // 在这里你可以进行一些处理，例如记录日志或者上传异常信息
                // 返回null表示使用Bugly默认的处理方式，返回非null表示使用自定义的处理方式

                // 示例：记录异常信息到日志
                logE("=============================Crash Info=============================")
                logE(errorType, "Crash Type")
                logE(errorMessage, "Crash Message")
                logE(errorStack, "Crash Stack")
                logE("=============================Crash Info=============================")

                BaseApp.application.removeAllActivity()

                // 返回null表示使用Bugly默认的处理方式
                return null
            }
        })

        // 第三个参数为SDK调试模式开关
        CrashReport.initCrashReport(
            BaseApp.context,
            appId,
            BuildConfig.VERSION_TYPE != VersionStatus.RELEASE,
            userStrategy
        )
        CrashReport.setUserId(DeviceTool.getSerialNumber()) // 设置用户ID
        Log.d(LogTAG.APP_CREATE_TAG, "Bugly -->> init complete")

        isSensorsInit = true

        return "Bugly -->> init complete"
    }


    /**
     * 腾讯 MMKV 初始化
     */
    private fun initMMKV(): String {
        val result = SpUtils.initMMKV(BaseApp.context)
        Log.d(LogTAG.APP_CREATE_TAG, "MMKV -->> $result")
        return "MMKV -->> $result"
    }

    /**
     * 初始化网络状态监听客户端
     * @return Unit
     */
    @SuppressLint("MissingPermission")
    private fun initNetworkStateClient(): String {
        NetworkStateClient.register()
        Log.d(LogTAG.APP_CREATE_TAG, "NetworkStateClient -->>init complete")
        return "NetworkStateClient -->> init complete"
    }

    private fun initSensorsDataSDK(): String {
        // 延迟初始化 SDK 会导致全埋点采集不准确和可视化全埋点、点击分析功能异常
        // 初始化配置
        if (isSensorsInit) {
            return "SensorsDataSDK -->> 已经初始化！"
        }
        val saConfigOptions = SAConfigOptions(SpUtils.getString(SpKey.SA_URL, NetUrl.RELEASE_SA_SERVER_URL)).enableLog(true) // debug模式下打印信息

        // 需要在主线程初始化神策 SDK
        SensorsDataAPI.startWithConfigOptions(BaseApp.context, saConfigOptions)
        // 初始化 SDK 之后，开启自动采集 Fragment 页面浏览事件
        SensorsDataAPI.sharedInstance().trackFragmentAppViewScreen()
        isSensorsInit = true
        // 设置事件公共属性
        try {
            val properties = JSONObject().apply {
                put("AppName", "X1") // 将应用名称作为事件公共属性，后续所有 track() 追踪的事件都会自动带上 "AppName" 属性
            }
            SensorsDataAPI.sharedInstance().registerSuperProperties(properties)
        } catch (e: JSONException) {
            e.printStackTrace()
            return "SensorsDataSDK -->> init error！"
        }
        return "SensorsDataSDK -->> init complete"
    }

    private fun initBatteryReceiver(): String {
        val intentFilter = IntentFilter().apply {
            addAction(Intent.ACTION_POWER_CONNECTED)
            addAction(Intent.ACTION_BATTERY_CHANGED)
            addAction(Intent.ACTION_POWER_DISCONNECTED)
            addAction(Intent.ACTION_CONFIGURATION_CHANGED)
        }
        BaseApp.context.registerReceiver(BatteryReceiver, intentFilter)
        return "initBatteryReceiver -->> init complete"
    }

    private fun initUSBHIDController(): String {
        // 初始化USBHIDController
        USBHIDController.init(BaseApp.application)
        return "initUSBHIDController -->> init complete"
    }

    private fun initSpeechManager(): String {
//        SpeechManager.init(
//            BaseApp.application,
//            SpUtils.getString(SpKey.BUSINESS_BASE_URL, NetUrl.BUSINESS_RELEASE_URL),
//            SpUtils.getString(SpKey.CAPTAIN_BASE_URL, NetUrl.BUSINESS_RELEASE_URL)
//        )
//        SpUtils.putString(SpKey.BUSINESS_BASE_URL, "http://8.135.239.158:24724")
//        SpUtils.putString(SpKey.CAPTAIN_BASE_URL, "http://8.135.239.158:9171")
        SpeechManager.init(
            BaseApp.application,
            SpUtils.getString(SpKey.CAPTAIN_BASE_URL, NetUrl.BUSINESS_RELEASE_URL) + "/",
            BusinessParams(
                tmkProjectType = TmkProjectType.X1,
                businessUrl = SpUtils.getString(SpKey.BUSINESS_BASE_URL, NetUrl.BUSINESS_RELEASE_URL) + "/app/"
            ),
            LingCastParams()
        )
//        SpeechManager.init(BaseApp.application, "http://8.135.239.158:24724", "http://8.135.239.158:9171")
//        SpeechManager.init(BaseApp.application, "http://8.219.221.152:24724", "http://8.219.221.152:9171")

        return "initSpeechManager -->> init complete"
    }

    private fun initBusinessTokenWorker(): String {
        BusinessManager.submitTokenWorker(0L)
        return "initBusinessTokenWorker -->> init complete"
    }

    /**
     * 日志打印 初始化
     */
    private fun initLogger(): String {
        val formatStrategy = MyPrettyFormatStrategy.newBuilder() // 在LogCat打印时的输出
            .showThreadInfo(false)       // 是否显示线程信息
            .methodCount(0)            // 打印的方法栈深度
            .methodOffset(2)            // 方法偏移
            .tag(LogTAG.DEF_PROJECT_TAG)    // 默认的全局TAG
            .build()
        val diskFormatStrategy = MyPrettyFormatStrategy.newBuilder()
            .tag(LogTAG.DEF_PROJECT_TAG)    // 默认的全局TAG
            .showThreadInfo(false)       // 是否显示线程信息
            .methodCount(0)            // 打印的方法栈深度
            .methodOffset(0)            // 方法偏移
            .logStrategy(
                MyDiskLogStrategy(
                    MyDiskLogHandler(
                        1024 * 1024 * 100
                    )
                )
            )
            .build()
        Logger.addLogAdapter(object : AndroidLogAdapter(formatStrategy) {
            override fun isLoggable(priority: Int, tag: String?): Boolean {
                if (BuildConfig.DEBUG) {
                    return true;
                } else if (BuildConfig.VERSION_TYPE != VersionStatus.RELEASE) {
                    return priority >= Logger.DEBUG
                }
                return true
            }
        })
        Logger.addLogAdapter(object : DiskLogAdapter(diskFormatStrategy) {
            override fun isLoggable(
                priority: Int,
                tag: String?
            ): Boolean { // 日志级别大于V优先级，并且TAG在控制范围内
                if (priority < Logger.DEBUG) return false
                return true
            }
        })
        Log.d(LogTAG.APP_CREATE_TAG, "Logger -->> init complete")
        return "Logger -->> init complete"
    }


    /**
     * 腾讯TBS WebView X5 内核初始化
     */
    private fun initX5WebViewCore() {
        // dex2oat优化方案
        if (!SpUtils.getBoolean(ALREADY_READ_PRIVACY, false)) {
            Log.d(LogTAG.APP_CREATE_TAG, "TBS X5 -->> 未同意隐私政策，不能初始化!")
            return
        }
        if (isWebViewInit) {
            Log.d(LogTAG.APP_CREATE_TAG, "TBS X5 -->> 已经初始化！")
            return
        }
        val map = HashMap<String, Any>()
        map[TbsCoreSettings.TBS_SETTINGS_USE_SPEEDY_CLASSLOADER] = true
        map[TbsCoreSettings.TBS_SETTINGS_USE_DEXLOADER_SERVICE] = true
        QbSdk.initTbsSettings(map)
        // 允许使用非wifi网络进行下载内核
        QbSdk.setDownloadWithoutWifi(true)
        // 初始化
        QbSdk.initX5Environment(BaseApp.context, object : QbSdk.PreInitCallback {

            override fun onCoreInitFinished() {
                isWebViewInit = true
                Log.d(LogTAG.APP_CREATE_TAG, " TBS X5 init finished")
                Log.d(LogTAG.APP_CREATE_TAG, "当前线程： ${Thread.currentThread().id}")
                Log.d(
                    LogTAG.APP_CREATE_TAG,
                    "主线程： ${Looper.getMainLooper().getThread().getId()}"
                )
            }

            override fun onViewInitFinished(isSuccee: Boolean) {
                // 初始化完成的回调，为true表示x5内核加载成功，否则表示x5内核加载失败，会自动切换到系统内核
                Log.d(LogTAG.APP_CREATE_TAG, "TBS X5 init is $isSuccee")
            }
        })
    }


    // 额外的串口数据监听
    private val mSerialListener = object : OnSerialValidDataListener {
        override fun onDataReceived(serial: TmkSerial, data: String) {
            if (data == W3ProUpgradeCMD.RECEIVE_START.hexContent && !MyActivityUtil.isUpgradingAty()) {
                showDebugToast("需要升级！${serial.portName}")
            }
        }

        override fun onDataError(e: Exception?) {
        }

    }



}