package com.timekettle.upup.comm.dialog

import android.app.Dialog
import android.content.Context
import android.content.res.Configuration
import android.graphics.Bitmap
import android.graphics.Color
import android.graphics.Typeface
import android.graphics.drawable.ColorDrawable
import android.text.SpannableString
import android.text.style.StyleSpan
import android.text.style.UnderlineSpan
import android.view.Gravity
import android.view.View
import android.view.View.GONE
import android.view.View.VISIBLE
import android.view.WindowManager
import android.widget.ImageView
import android.widget.TextView
import androidx.annotation.DrawableRes
import androidx.constraintlayout.widget.ConstraintLayout
import androidx.core.content.ContextCompat
import androidx.core.graphics.toColorInt
import com.timekettle.upup.base.BaseApp
import com.timekettle.upup.base.ktx.gone
import com.timekettle.upup.base.ktx.visible
import com.timekettle.upup.comm.R
import com.timekettle.upup.comm.button.CommonButton
import com.timekettle.upup.comm.databinding.*
import io.github.armcha.autolink.MODE_CUSTOM
import androidx.core.graphics.drawable.toDrawable
import com.timekettle.upup.comm.utils.RichTextUtils

/**
 * 统一弹窗
 * <AUTHOR>
 * @since 2021/7/22
 *
 */
class DialogFactory {

    companion object {

        /**
         * 提示的Dialog(富文本)
         * @param context Context 上下文
         * @param titleText String 标题内容
         * @param content SpannableString 提示文本
         * @param sureText String 确定文本
         * @param canceledOnTouchOutside Boolean 点击外面是否取消显示 默认会取消
         * @param onConfirmListener () -> Unit 确认回调
         * @return Dialog
         */
        inline fun createTipsDialog(
            context: Context,
            titleText: String = BaseApp.context.getString(R.string.common_alert_tip),
            content: SpannableString,
            sureText: String = context.getString(R.string.common_confirm),
            canceledOnTouchOutside: Boolean = true,
            cancelable: Boolean = true,
            contentGravity: Int = Gravity.CENTER,
            crossinline onConfirmListener: () -> Unit = {}
        ): Dialog {
            val dialog = Dialog(context, R.style.comm_transparent_dialog)
            dialog.setCanceledOnTouchOutside(canceledOnTouchOutside)
            dialog.setCancelable(cancelable)
            CommDialogTipsBinding.inflate(dialog.layoutInflater).apply {
                vContentTv.text = content
                vContentTv.gravity = contentGravity
                vSureBtn.text = sureText
                vTitleTv.text = titleText
                vSureBtn.setOnClickListener {
                    onConfirmListener()
                    dialog.dismiss()
                }
                if (titleText.isNotEmpty())
                    vTitleTv.visible()
                else
                    vTitleTv.gone()
                dialog.setContentView(root)
            }
            return dialog
        }


        /**
         * 提示的Dialog
         * @param context Context 上下文
         * @param titleText String 标题内容
         * @param content String 提示文本
         * @param sureText String 确定文本
         * @param canceledOnTouchOutside Boolean 点击外面是否取消显示 默认会取消
         * @param onConfirmListener () -> Unit 确认回调
         * @return Dialog
         */
        inline fun createTipsDialog(
            context: Context,
            titleText: String = BaseApp.context.getString(R.string.common_alert_tip),
            content: String,
            sureText: String = context.getString(R.string.common_confirm),
            canceledOnTouchOutside: Boolean = true,
            cancelable: Boolean = true,
            contentGravity: Int = Gravity.CENTER,
            crossinline onConfirmListener: () -> Unit = {}
        ): Dialog {
            val dialog = Dialog(context, R.style.comm_transparent_dialog)
            dialog.setCanceledOnTouchOutside(canceledOnTouchOutside)
            dialog.setCancelable(cancelable)
            CommDialogTipsBinding.inflate(dialog.layoutInflater).apply {
                vContentTv.text = content
                vContentTv.gravity = contentGravity
                vSureBtn.text = sureText
                vTitleTv.text = titleText
                vSureBtn.setOnClickListener {
                    onConfirmListener()
                    dialog.dismiss()
                }
                if (titleText.isNotEmpty())
                    vTitleTv.visible()
                else
                    vTitleTv.gone()
                dialog.setContentView(root)
            }
            return dialog
        }

        /**
         * 创建一个带有确认和取消按键的Dialog
         * @param context Context 上下文
         * @param titleText String 标题文本
         * @param content String 内容文本
         * @param confirmText String 确认文本
         * @param cancelText String 取消文本
         * @param canceledOnTouchOutside Boolean
         * @param confirmCall () -> Unit 确认回调
         * @param cancelCall () -> Unit 取消回调
         * @return Dialog
         */
        inline fun createConfirmCancelDialog(
            context: Context,
            titleText: String = BaseApp.context.getString(R.string.common_alert_tip),
            content: CharSequence,
            confirmText: String = BaseApp.context.getString(R.string.common_confirm),
            cancelText: String = BaseApp.context.getString(R.string.common_cancel),
            canceledOnTouchOutside: Boolean = false,
            cancelable: Boolean = true,
            crossinline confirmCall: () -> Unit,
            crossinline cancelCall: () -> Unit = {},
            crossinline closeCall: () -> Unit = {}
        ): Dialog {
            val dialog = Dialog(context, R.style.comm_transparent_dialog)
            dialog.setCanceledOnTouchOutside(canceledOnTouchOutside)
            dialog.setCancelable(cancelable)
            val orientation: Int = context.resources.configuration.orientation
            val binding = CommDialogConfirmCancelBinding.inflate(dialog.layoutInflater).apply {
                if (orientation == Configuration.ORIENTATION_PORTRAIT) {
                    // 当前为竖屏模式
                } else {
                    // 当前为横屏模式
                }
            }
            binding.apply {
                vTitleTv.text = titleText
                if (titleText.isEmpty()) {
                    vTitleTv.gone()
                }
                vContentTv.text = content
                vCancelTv.text = cancelText
                vCloseIv.visibility = GONE
                vConfirmTv.text = confirmText
                vConfirmTv.setOnClickListener {
                    confirmCall()
                    dialog.dismiss()
                }
                vCancelTv.setOnClickListener {
                    cancelCall()
                    dialog.dismiss()
                }
                vCloseIv.setOnClickListener {
                    closeCall()
                    dialog.dismiss()
                }

                dialog.setContentView(root)
            }
            return dialog
        }

        inline fun createShareDialog(
            context: Context,
            bitmap: Bitmap,
            id: String,
            confirmText: String = BaseApp.context.getString(R.string.common_confirm),
            canceledOnTouchOutside: Boolean = false,
            cancelable: Boolean = true,
            crossinline confirmCall: () -> Unit
        ): Dialog {
            val dialog = Dialog(context, R.style.comm_transparent_dialog)
            dialog.setCanceledOnTouchOutside(canceledOnTouchOutside)
            dialog.setCancelable(cancelable)
            val binding = CommDialogShareBinding.inflate(dialog.layoutInflater)
            binding.apply {
                tvPcDesc.text = RichTextUtils.getHighLightWordsArray(
                    context.getString(R.string.speech_guide_share_info),
                    mutableListOf("https://presentation.timekettle.co"),
                    Color.parseColor("#FF43A5FF"))
                vConfirmTv.text = confirmText
                tvId.text = "ID: $id"
                tvPcId.text = "ID: $id"
                imgCode.setImageBitmap(bitmap)

                vConfirmTv.setOnClickListener {
                    confirmCall()
                }

                tvPhoneShare.setOnClickListener {
                    clPhoneTab.visible()
                    clPcTab.gone()
                    tvPhoneShare.setBackgroundResource(R.drawable.bg_speech_help_tv)
                    tvPhoneShare.setTextColor("#43A5FF".toColorInt())
                    tvPcShare.setBackgroundResource(0)
                    tvPcShare.setTextColor("#EBEBEB".toColorInt())
                }

                tvPcShare.setOnClickListener {
                    clPhoneTab.gone()
                    clPcTab.visible()
                    tvPhoneShare.setBackgroundResource(0)
                    tvPhoneShare.setTextColor("#EBEBEB".toColorInt())
                    tvPcShare.setBackgroundResource(R.drawable.bg_speech_help_tv)
                    tvPcShare.setTextColor("#FF43A5FF".toColorInt())
                }

                dialog.setContentView(root)
            }
            return dialog
        }


        /**
         * 创建一个带有确认和取消按键的清除配对记录Dialog
         * @param context Context 上下文
         * @param titleText String 标题文本
         * @param content String 内容文本
         * @param confirmText String 确认文本
         * @param cancelText String 取消文本
         * @param canceledOnTouchOutside Boolean
         * @param confirmCall () -> Unit 确认回调
         * @param cancelCall () -> Unit 取消回调
         * @return Dialog
         */
        inline fun createClearDongleDialog(
            context: Context,
            titleText: String = BaseApp.context.getString(R.string.common_alert_tip),
            content: CharSequence,
            confirmText: String = BaseApp.context.getString(R.string.common_confirm),
            cancelText: String = BaseApp.context.getString(R.string.common_cancel),
            canceledOnTouchOutside: Boolean = false,
            cancelable: Boolean = true,
            crossinline confirmCall: () -> Unit,
            crossinline cancelCall: () -> Unit = {},
            crossinline closeCall: () -> Unit = {}
        ): Dialog {
            val dialog = Dialog(context, R.style.comm_transparent_dialog)
            dialog.setCanceledOnTouchOutside(canceledOnTouchOutside)
            dialog.setCancelable(cancelable)
            CommDialogConfirmCancelBinding.inflate(dialog.layoutInflater).apply {
                vTitleTv.text = titleText
                if (titleText.isEmpty()) {
                    vTitleTv.gone()
                }
                vContentTv.text = content
                vContentTv.gravity = Gravity.CENTER
                vCancelTv.text = cancelText
                vCloseIv.visibility = GONE
                vConfirmTv.text = confirmText
                vConfirmTv.setTypeface(null, Typeface.BOLD)
                vConfirmTv.setTextColor(Color.RED)
                vConfirmTv.setTheme(CommonButton.Theme.THEME_NORMAL)
                vConfirmTv.setOnClickListener {
                    confirmCall()
                    dialog.dismiss()
                }
                vCancelTv.setTheme(CommonButton.Theme.THEME_NORMAL)
                vCancelTv.setOnClickListener {
                    cancelCall()
                    dialog.dismiss()
                }
                vCloseIv.setOnClickListener {
                    closeCall()
                    dialog.dismiss()
                }

                dialog.setContentView(root)
            }
            return dialog
        }

        inline fun createKnowDialog(
            context: Context,
            titleText: String = BaseApp.context.getString(R.string.common_alert_tip),
            content: CharSequence,
            knowText: String = BaseApp.context.getString(R.string.translate_meeting_got_it),
            canceledOnTouchOutside: Boolean = false,
            cancelable: Boolean = true,
            crossinline knowCall: () -> Unit = {},
        ): Dialog {
            val dialog = Dialog(context, R.style.comm_transparent_dialog)
            dialog.setCanceledOnTouchOutside(canceledOnTouchOutside)
            dialog.setCancelable(cancelable)
            val orientation: Int = context.resources.configuration.orientation
            val binding = CommDialogKnowBinding.inflate(dialog.layoutInflater).apply {
                if (orientation == Configuration.ORIENTATION_PORTRAIT) {
                    // 当前为竖屏模式
                } else {
                    // 当前为横屏模式
                }
            }
            binding.apply {
                vTitleTv.text = titleText
                if (titleText.isEmpty()) {
                    vTitleTv.gone()
                }
                vContentTv.text = content
                vKnowTv.text = knowText

                vKnowTv.setOnClickListener {
                    knowCall()
                    dialog.dismiss()
                }

                dialog.setContentView(root)
            }
            return dialog
        }

        /**
         * 创建一个带有确认和取消按键的，可以点击内部链接的 Dialog
         * @param context Context 上下文
         * @param titleText String 标题文本
         * @param content String 内容文本
         * @param confirmText String 确认文本
         * @param cancelText String 取消文本
         * @param canceledOnTouchOutside Boolean
         * @param confirmCall () -> Unit 确认回调
         * @param cancelCall () -> Unit 取消回调
         * @return Dialog
         */
        inline fun createConfirmCancelLinkDialog(
            context: Context,
            titleText: String,
            content: String,
            confirmText: String = BaseApp.context.getString(R.string.common_confirm),
            cancelText: String = BaseApp.context.getString(R.string.common_cancel),
            canceledOnTouchOutside: Boolean = false,
            customMode: MODE_CUSTOM,
            crossinline confirmCall: () -> Unit,
            crossinline cancelCall: () -> Unit,
            crossinline closeCall: () -> Unit = {},
            crossinline linkClicked: (clickText: String) -> Unit
        ): Dialog {
            val dialog = Dialog(context, R.style.comm_transparent_dialog)
            dialog.setCanceledOnTouchOutside(canceledOnTouchOutside)
            CommDialogConfirmCancelLinkBinding.inflate(dialog.layoutInflater).apply {
                vTitleTv.text = titleText
                vContentTv.addAutoLinkMode(
                    customMode
                )
                vContentTv.addSpan(customMode, StyleSpan(Typeface.BOLD), UnderlineSpan())
                vContentTv.customModeColor = ContextCompat.getColor(context, R.color.comm_blue)
                vContentTv.text = content
                vContentTv.onAutoLinkClick { linkClicked(it.originalText) }

                vCancelTv.text = cancelText
                vConfirmTv.text = confirmText
                vConfirmTv.setOnClickListener {
                    confirmCall()
                    dialog.dismiss()
                }
                vCancelTv.setOnClickListener {
                    cancelCall()
                    dialog.dismiss()
                }
                vCloseIv.visibility = GONE;
                vCloseIv.setOnClickListener {
                    closeCall()
                    dialog.dismiss()
                }
                dialog.setContentView(root)
            }
            return dialog
        }


        /**
         * 创建一个带有确认和取消按键的Dialog
         * @param context Context 上下文
         * @param titleText String 标题文本
         * @param content String 内容文本
         * @param confirmText String 确认文本
         * @param canceledOnTouchOutside Boolean
         * @param confirmCall () -> Unit 确认回调
         * @return Dialog
         */
        inline fun createConfirmDialog(
            context: Context,
            titleText: String = BaseApp.context.getString(R.string.common_alert_tip),
            content: String,
            confirmText: String = BaseApp.context.getString(R.string.common_confirm),
            canceledOnTouchOutside: Boolean = true,
            cancelable: Boolean = true,
            closeIconVisible: Boolean = false, // 右上角的XX是否可见
            crossinline confirmCall: () -> Unit,
            crossinline closeCall: () -> Unit = {}
        ): Dialog {
            val dialog = Dialog(context, R.style.comm_transparent_dialog)
            dialog.setCanceledOnTouchOutside(canceledOnTouchOutside)
            dialog.setCancelable(cancelable)
            CommDialogConfirmBinding.inflate(dialog.layoutInflater).apply {
                if (closeIconVisible) vCloseIv.visible()
                else vCloseIv.gone()
                if (titleText.isEmpty()) vTitleTv.height = 0
                vTitleTv.text = titleText
                vContentTv.text = content
                vConfirmTv.text = confirmText
                vConfirmTv.setOnClickListener {
                    confirmCall()
                    dialog.dismiss()
                }
                vCloseIv.setOnClickListener {
                    closeCall()
                    dialog.dismiss()
                }
                dialog.setContentView(root)
            }
            return dialog
        }

        /**
         * 创建一个带有确认和取消按键的Dialog
         * @param context Context 上下文
         * @param titleText String 标题文本
         * @param content String 内容文本
         * @param confirmText String 确认文本
         * @param canceledOnTouchOutside Boolean
         * @param confirmCall () -> Unit 确认回调
         * @return Dialog
         */
        inline fun createUpgradeConfirmDialog(
            context: Context,
            titleText: String = BaseApp.context.getString(R.string.common_alert_tip),
            content: String,
            confirmText: String = BaseApp.context.getString(R.string.common_confirm),
            canceledOnTouchOutside: Boolean = true,
            cancelable: Boolean = true,
            closeIconVisible: Boolean = false, // 右上角的XX是否可见
            crossinline confirmCall: () -> Unit,
            crossinline closeCall: () -> Unit = {}
        ): Dialog {
            val dialog = Dialog(context, R.style.comm_transparent_dialog)
            dialog.setCanceledOnTouchOutside(canceledOnTouchOutside)
            dialog.setCancelable(cancelable)
            CommDialogUpgradeConfirmBinding.inflate(dialog.layoutInflater).apply {
                if (closeIconVisible) vCloseIv.visible()
                else vCloseIv.gone()
                if (titleText.isEmpty()) vTitleTv.height = 0
                vTitleTv.text = titleText
                vContentTv.text = content
                vConfirmTv.text = confirmText
                vConfirmTv.setOnClickListener {
                    confirmCall()
                    dialog.dismiss()
                }
                vCloseIv.setOnClickListener {
                    closeCall()
                    dialog.dismiss()
                }
                dialog.setContentView(root)
            }
            return dialog
        }

        /**
         * 创建一个没有标题的确认取消弹窗
         * @param context Context 上下文
         * @param content String 内容
         * @param confirmText String 确认文案
         * @param cancelText String 取消文案
         * @param canceledOnTouchOutside Boolean 是否点击其他区域消失
         * @param confirmCall () -> Unit 确认回调
         * @param cancelCall () -> Unit 取消回调
         * @param closeCall () -> Unit 关闭回调
         * @return Dialog
         */
        inline fun createConfirmCancelNoTitleDialog(
            context: Context,
            content: String,
            confirmText: String = BaseApp.context.getString(R.string.common_confirm),
            cancelText: String = BaseApp.context.getString(R.string.common_cancel),
            canceledOnTouchOutside: Boolean = true,
            crossinline confirmCall: () -> Unit,
            crossinline cancelCall: () -> Unit,
            crossinline closeCall: () -> Unit = {}
        ): Dialog {
            val dialog = Dialog(context, R.style.comm_transparent_dialog)
            dialog.setCanceledOnTouchOutside(canceledOnTouchOutside)
            CommDialogConfirmCancelNoTitleBinding.inflate(dialog.layoutInflater).apply {
                vContentTv.text = content
                vCancelTv.text = cancelText
                vConfirmTv.text = confirmText
                vCloseIv.visibility = GONE
                vConfirmTv.setOnClickListener {
                    confirmCall()
                    dialog.dismiss()
                }
                vCancelTv.setOnClickListener {
                    cancelCall()
                    dialog.dismiss()
                }
                vCloseIv.setOnClickListener {
                    closeCall()
                    dialog.dismiss()
                }
                dialog.setContentView(root)
            }
            return dialog
        }

        // 创建Loading/处理中的弹窗
        fun createProcessDialog(
            context: Context,
            text: String = "",
        ): Dialog {
            val dialog = Dialog(context, R.style.comm_transparent_dialog)
            dialog.setCanceledOnTouchOutside(false)
            dialog.setCancelable(false)
            val binding = CommDialogProgressBinding.inflate(dialog.layoutInflater).apply {
                if (text.isEmpty()) {
                    tvProgress.visibility = GONE;
                } else {
                    tvProgress.visibility = VISIBLE
                    tvProgress.text = text
                }
                dialog.setContentView(root)
            }
            return dialog
        }


        /**
         * 可以选择并且点击的item弹窗
         */
        inline fun createItemClickDialog(
            context: Context,
            @DrawableRes iconId: Int,
            content: String,
            crossinline confirmCall: () -> Unit,
        ): Dialog {
            val dialog = Dialog(context, R.style.comm_transparent_dialog)
            dialog.setCanceledOnTouchOutside(false)
            CommDialogItemClickBinding.inflate(dialog.layoutInflater).apply {
                vCloseIv.setOnClickListener {
                    dialog.dismiss()
                }
                ivIcon.setImageResource(iconId)
                tvContent.text = content
                llItemContent.setOnClickListener {
                    dialog.dismiss()
                    confirmCall()
                }
                dialog.setContentView(root)
            }
            return dialog
        }


        // 创建全屏的Dialog
        fun createFullScreenDialog(
            context: Context,
            view: View,
            cancelable: Boolean = false,
        ): Dialog {
            val dialog: Dialog = FullScreenDialog(
                context,
                android.R.style.Theme_DeviceDefault_Light_NoActionBar, view
            )
            dialog.setCancelable(cancelable)
            return dialog
        }

    }
}