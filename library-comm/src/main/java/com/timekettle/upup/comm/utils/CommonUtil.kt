package com.timekettle.upup.comm.utils

import kotlin.random.Random

object CommonUtil {

    //随机生成六位数字密码
    fun generateRandomPassword(length: Int = 6): String {
        val characters = "0123456789"
        val sb = StringBuilder()
        val random = Random

        repeat(length) {
            val index = random.nextInt(characters.length)
            val randomChar = characters[index]
            sb.append(randomChar)
        }

        return sb.toString()
    }

}