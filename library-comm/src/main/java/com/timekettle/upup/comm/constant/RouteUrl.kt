package com.timekettle.upup.comm.constant

/**
 * 路由地址
 *
 * <AUTHOR>
 * @since 2022/04/15
 */
object RouteUrl {

    /**
     * 公共模块
     */
    object Comm {

        /**
         * 公共的 web 页面
         */
        const val CommWebActivity = "/library_comm/CommWebActivity"
        const val ControlPanel = "/library_comm/ControlPanel"
    }

    /**
     * 示例 模块1
     */
    object One {

        /**
         * OneHomeActivity
         */
        const val OneHomeActivity = "/module_example/OneHomeActivity"
    }

    /**
     * 示例 模块2
     */
    object Two {

        /**
         * TwoHomeActivity
         */
        const val TwoHomeActivity = "/module_example2/TwoHomeActivity"
    }

    object Main {
        const val MainService = "/module_main/MainService"
        const val MainTextActivity = "/module_main/MainTextActivity"
        const val MainTabActivity = "/module_main/MainTabActivity"
        const val MainSplashActivity = "/module_main/MainSplashActivity"
        const val PolicyActivity = "/module_main/PolicyActivity"
    }

    /**
     * 主页
     */
    object Home {
        const val HomeService = "/module_home/HomeService"  // 提供服务
        const val Welcome = "/module_home/Welcome"
        const val HomeActivityMain = "/module_home/HomeActivityMain"
        const val CertificateActivity = "/module_home/CertificateActivity"
        const val PhoneActivityGuide = "/module_home/PhoneActivityGuide"
        const val InterviewActivityGuide = "/module_home/InterviewActivityGuide"
        const val KeypadActivityGuide = "/module_home/KeypadActivityGuide"
        const val SimulActivityGuide = "/module_home/SimulActivityGuide"
        const val VideoActivityGuide = "/module_home/VideoActivityGuide"
        const val SpeechActivityGuide = "/module_home/SpeechActivityGuide"
        const val MeetingActivityGuide = "/module_translate/MeetingActivityGuide"

        const val ChooseProduct = "/module_home/ChooseProduct"
        const val Device = "/module_home/HomeDevice"
        const val DeviceHeaderZero = "/module_home/DeviceHeaderZero"
        const val DeviceHeaderWT2 = "/module_home/DeviceHeaderWT2"
        const val DeviceHeaderSimu = "/module_home/DeviceHeaderSimu"
        const val DeviceHeaderM2 = "/module_home/DeviceHeaderM2"
        const val DeviceHeaderM3 = "/module_home/DeviceHeaderM3"
        const val ProductUsage = "/module_home/ProductUsage"
    }

    /**
     * 我的
     */
    object Mine {
        const val MineService = "/module_mine/MineService"
        const val MineFragment = "/module_mine/MineFragment"
        const val OfflinePkgActivity = "/module_mine/OfflinePkgActivity"
    }

    /**
     * 翻译
     */
    object Translate {
        const val TranslateActivityMain = "/module_translate/TranslateActivityMain"
        const val TranslateActivity1v1 = "/module_translate/TranslateActivity1v1"
        const val TranslateActivityHome = "/module_translate/TranslateActivityHome"
        const val TranslateActivityOffline = "/module_translate/TranslateActivityOffline"
        const val TransService = "/module_translate/TransService"
        const val TranslateFragment = "/module_translate/TranslateFragment"
        const val MeetingActivityHome = "/module_translate/MeetingActivityHome"
        const val MeetingActivityGuide = "/module_translate/MeetingActivityGuide"
        const val ConnectActivity = "/module_translate/ConnectActivity"
        const val PhoneActivityHome = "/module_translate/PhoneActivityHome"
        const val InterviewActivityHome = "/module_translate/InterviewActivityHome"
        const val VideoActivityHome = "/module_translate/VideoActivityHome"
        const val SpeechActivityHome = "/module_translate/SpeechActivityHome"
        const val SpeechActivityMain = "/module_translate/SpeechActivityMain"
        const val InterviewActivityMain = "/module_translate/InterviewActivityMain"
        const val VideoActivityMain = "/module_translate/VideoActivityMain"
        const val VideoActivityConnect = "/module_translate/VideoActivityConnect"
        const val KeypadActivityMain = "/module_translate/KeypadActivityMain"
        const val PhoneActivityComing = "/module_translate/PhoneActivityComing"
        const val PhoneActivityMain = "/module_translate/PhoneActivityMain"
        const val MeetingActivityMain = "/module_translate/MeetingActivityMain"
        const val SendActivity = "/module_translate/SendActivity"
        const val DongleUpgradeActivity = "/module_translate/dongleUpgradeActivity"
        const val TranslateFragmentSameSide = "/module_translate/TranslateFragmentSameSide"
        const val TranslateFragmentListen = "/module_translate/TranslateFragmentListen"
        const val ModeMSeries = "/module_translate/ModeMSeries"
        const val ModeWSeries = "/module_translate/ModeWSeries"
        const val ModeZero = "/module_translate/ModeZero"
        const val History = "/module_translate/History"
        const val HistoryQuery = "/module_translate/HistoryQuery"
        const val CustomTranslation = "/custom_translation/CustomTranslation"
        const val CreateEntry = "/custom_translation/CreateEntry"
    }

    /**
     * 设置
     */
    object Setting {
        const val SettingService = "/module_setting/SettingService"
        const val SettingActivityMain = "/module_setting/SettingActivityMain"
        const val SettingActivityTest= "/module_setting/SettingActivityTest"
        const val SettingActivityUpgrade= "/module_setting/SettingActivityUpgrade"
        const val SettingActivityStorage = "/module_setting/SettingActivityStorage"
    }


    /**
     * 设备
     */
    object Device {
        const val DeviceFragment = "/module_device/DeviceFragment"
    }

    // 登录模块
    object Login {
        const val LoginService = "/module_login/LoginService"  // 提供服务
        const val LoginActivity = "/module_login/LoginActivity" // 登录页面
        const val Welcome = "/module_login/Welcome" // 欢迎页
        const val NickName = "/module_login/NickName" // 用户昵称页面
    }


}