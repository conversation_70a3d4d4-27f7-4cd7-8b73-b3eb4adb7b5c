package com.timekettle.upup.comm.constant

import android.graphics.Color
import android.os.Parcelable
import android.text.Html
import android.text.SpannableString
import android.text.Spanned
import androidx.core.text.HtmlCompat
import com.timekettle.upup.base.BaseApp
import com.timekettle.upup.comm.R
import com.timekettle.upup.comm.utils.LanguageUtil
import com.timekettle.upup.comm.utils.RichTextUtils.getHighLightWordsArray
import kotlinx.parcelize.Parcelize
import java.util.*

/**
 * 本地存储的键 放在此类中
 *
 * <AUTHOR>
 * @since 2022/04/15
 */

@Parcelize
enum class TmkProductType(
    var code: String,
    var productName: String,
) : Parcelable {
    W3("004", "W3"),
    M3("005", "M3"),
    UNKNOWN("XXX", "UNKNOWN");

    companion object {
        operator fun get(code: String) =
            values().find { it.code == code }
    }


}
