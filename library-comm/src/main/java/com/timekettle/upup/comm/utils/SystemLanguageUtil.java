package com.timekettle.upup.comm.utils;

import android.app.Activity;
import android.app.backup.BackupManager;
import android.content.Context;
import android.content.res.Configuration;
import android.os.Build;
import android.os.LocaleList;
import android.util.DisplayMetrics;



import java.lang.reflect.Field;
import java.lang.reflect.Method;

import java.util.Locale;

public class SystemLanguageUtil {

    public static void setLanguage(Context context, Locale locale) {
        try {
            Class<?> activityManagerNativeClass = Class.forName("android.app.ActivityManagerNative");
            Method getDefaultMethod = activityManagerNativeClass.getDeclaredMethod("getDefault");
            Object iActivityManagerObj = getDefaultMethod.invoke(activityManagerNativeClass);

            Class<?> iActivityManagerClass = Class.forName("android.app.IActivityManager");
            Method getConfigurationMethod = iActivityManagerClass.getDeclaredMethod("getConfiguration");
            Configuration config = (Configuration) getConfigurationMethod.invoke(iActivityManagerObj);
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.N) {
                config.setLocales(new LocaleList(locale));
            } else {
                config.setLocale(locale);
            }
            Class<?> configClass = Class.forName("android.content.res.Configuration");
            Field userSetLocaleField = configClass.getField("userSetLocale");
            userSetLocaleField.set(config, true);
            Class[] clzParams = {Configuration.class};
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.M) {
                Method updatePersistentConfigurationMethod = iActivityManagerClass.getDeclaredMethod("updatePersistentConfiguration", clzParams);
                updatePersistentConfigurationMethod.invoke(iActivityManagerObj, config);
            } else {
                Configuration configuration = context.getResources().getConfiguration();
                // 获取想要切换的语言类型
                configuration.setLocale(locale);
                // updateConfiguration
                DisplayMetrics dm = context.getResources().getDisplayMetrics();
                context.getResources().updateConfiguration(configuration, dm);
                // 下面的代码决定是否修改系统语言设置（需要有系统签名和sharedUserId）
                Method updateConfigurationMethod = iActivityManagerClass.getDeclaredMethod("updateConfiguration", clzParams);
                updateConfigurationMethod.invoke(iActivityManagerObj, config);
            }
            BackupManager.dataChanged("com.android.providers.settings");
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

}
