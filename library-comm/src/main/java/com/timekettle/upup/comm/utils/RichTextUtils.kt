package com.timekettle.upup.comm.utils

import android.graphics.Color
import android.graphics.Typeface
import android.text.SpannableString
import android.text.Spanned
import android.text.style.ForegroundColorSpan
import android.text.style.StyleSpan
import java.util.regex.Matcher
import java.util.regex.Pattern

/**
 * <AUTHOR>
 * @date 2022/10/20 15:23
 * @email <EMAIL>
 * @desc 富文本工具类
 */
object RichTextUtils {


    /***
     * 指定关键字数组高亮
     * @param originString 原字符串
     * @param keyWords   高亮字符串数组
     * @param highLightColor 高亮色值
     * @return 高亮后的字符串
     */
    fun getHighLightWordsArray(
        originString: String,
        keyWords: MutableList<String>,
        highLightColor: Int = Color.parseColor("#0A85FF"),
        defaultColor: Int = Color.WHITE,// 设置默认字体颜色为白色
        boldKeyWords: Boolean = false     // 是否需要粗体？
    ): SpannableString {
        val originSpannableString = SpannableString(originString)
        originSpannableString.setSpan(
            ForegroundColorSpan(defaultColor), 0, originString.length,
            Spanned.SPAN_EXCLUSIVE_EXCLUSIVE
        )
        for (i in keyWords.indices) {
            val p: Pattern = Pattern.compile(keyWords[i])
            val m: Matcher = p.matcher(originSpannableString)
            while (m.find()) {
                val start: Int = m.start()
                val end: Int = m.end()
                originSpannableString.setSpan(
                    ForegroundColorSpan(highLightColor), start, end,
                    Spanned.SPAN_EXCLUSIVE_EXCLUSIVE
                )
                if (boldKeyWords) {
                    originSpannableString.setSpan(
                        StyleSpan(Typeface.BOLD), start, end,
                        Spanned.SPAN_EXCLUSIVE_EXCLUSIVE
                    )
                }
                break // 加了这个break就只找第一个，不加就是找出所有的
            }
        }
        return originSpannableString
    }

    /***
     * 指定关键字数组高亮
     * @param originString 原字符串
     * @param keyWords   高亮字符串数组
     * @param highLightColor 高亮色值
     * @return 高亮后的字符串
     */
    fun getAllHighLightWordsArray(
        originString: String,
        keyWords: MutableList<String>,
        highLightColor: Int = Color.parseColor("#0A85FF"),
        defaultColor: Int = Color.WHITE,// 设置默认字体颜色为白色
        boldKeyWords: Boolean = false     // 是否需要粗体？
    ): SpannableString {
        val originSpannableString = SpannableString(originString)
        originSpannableString.setSpan(
            ForegroundColorSpan(defaultColor), 0, originString.length,
            Spanned.SPAN_EXCLUSIVE_EXCLUSIVE
        )
        for (i in keyWords.indices) {
            val p: Pattern = Pattern.compile(keyWords[i])
            val m: Matcher = p.matcher(originSpannableString)
            while (m.find()) {
                val start: Int = m.start()
                val end: Int = m.end()
                originSpannableString.setSpan(
                    ForegroundColorSpan(highLightColor), start, end,
                    Spanned.SPAN_EXCLUSIVE_EXCLUSIVE
                )
                if (boldKeyWords) {
                    originSpannableString.setSpan(
                        StyleSpan(Typeface.BOLD), start, end,
                        Spanned.SPAN_EXCLUSIVE_EXCLUSIVE
                    )
                }
//                break // 加了这个break就只找第一个，不加就是找出所有的
            }
        }
        return originSpannableString
    }


    fun getBlueRichText(rawText: String): SpannableString {
        return getHighLightWordsArray(rawText, mutableListOf(rawText), Color.parseColor("#0A85FF"))
    }


}