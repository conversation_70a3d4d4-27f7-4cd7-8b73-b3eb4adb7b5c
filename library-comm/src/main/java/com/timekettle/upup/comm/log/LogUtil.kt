package com.timekettle.upup.comm.log;

import android.annotation.SuppressLint
import android.content.Context
import android.util.Log
import com.blankj.utilcode.util.AppUtils
import com.blankj.utilcode.util.DeviceUtils
import com.blankj.utilcode.util.FileUtils
import com.blankj.utilcode.util.PathUtils
import com.blankj.utilcode.util.ScreenUtils
import com.timekettle.upup.base.BaseApp
import com.timekettle.upup.base.utils.SpUtils
import com.timekettle.upup.base.utils.logD
import com.timekettle.upup.comm.constant.SpKey
import com.timekettle.upup.comm.service.login.LoginServiceImplWrap
import java.io.File
import java.io.IOException
import java.text.SimpleDateFormat
import java.util.*

object LogUtil {

    private const val TAG = "日志工具"
    private const val maxSize = 1024 * 1024 * 10 // 10M
    private const val maxLogNum = 8

    @SuppressLint("SimpleDateFormat")
    private fun createLogFile(): File {
        return try {
            val now = Date()
            val dateFormat = SimpleDateFormat("yyyyMMdd-HH_mm_ss")
            val timeStr = dateFormat.format(now)
            val cacheAllFile = SpUtils.getBoolean(SpKey.CACHE_ALL_FILE, false)
            val filePath = if (cacheAllFile) {
                // 打开调试开关就把日志存在外部沙箱
                "${PathUtils.getExternalAppCachePath()}/userlog/$timeStr.log"
            } else {
                "${PathUtils.getInternalAppCachePath()}/Log/$timeStr.log"
            }
            val file = File(filePath)
            FileUtils.createOrExistsFile(filePath)
            file.parent?.let {
                if (cacheAllFile) {
                    Log.d(TAG, "开关开启，缓存所有日志")
                } else {
                    deleteOldRecord(it)
                }
            }
            file
        } catch (e: IOException) {
            e.printStackTrace()
            if (SpUtils.getBoolean(SpKey.CACHE_ALL_FILE, false)) {
                File("${PathUtils.getExternalAppCachePath()}/userlog/error.log")
            } else {
                File("${PathUtils.getInternalAppCachePath()}/Log/error.log")
            }
        }
    }


    // 在LOG_DIR下，寻找最新的日志文件；
    // 如果没有，就创建一个新的
    // 如果只有一个，就返回这个文件
    fun getLogFile(): File {
        val dirPath = if (SpUtils.getBoolean(SpKey.CACHE_ALL_FILE, false)) {
            "${PathUtils.getExternalAppCachePath()}/userlog/"
        } else {
            "${PathUtils.getInternalAppCachePath()}/Log/"
        }
        val logDir = File(dirPath)
        if (!logDir.exists()) {
            logDir.mkdirs()
        }

        val files = logDir.listFiles { file -> file.isFile }
        if (files != null && files.isNotEmpty()) {
            val sortedFiles = files.sortedBy { it.lastModified() }
            val lastFile = sortedFiles.last()
            if (lastFile.length() > maxSize) {
                return createLogFile()
            }
            return sortedFiles.last()
        } else {
            return createLogFile()
        }
    }


    //删除旧的文件
    private fun deleteOldRecord(dir: String) {
        // FileTreeWalk 继承自 Sequence（序列）
        // walkTopDown 方法，会连着文件夹名称也遍历出来，所以需要用filter过滤一下
        // Sequence 根据 file.name进行排序，仍然返回Sequence
        // Sequence 实现了 Iterator 迭代器
        val sortFileSequence: Sequence<File> = File(dir).walkTopDown()
            .filter { it.name.endsWith(".log") }
            .sortedBy { it.name }
        if (sortFileSequence.toList().size > maxLogNum) {
            Log.e(
                TAG,
                "超过$maxLogNum 个日志了，删除最旧的log文件：${sortFileSequence.minOrNull()?.name}"
            )
            sortFileSequence.minOrNull()?.delete()
        }
    }

    /**
     * 获取用户的ID和设备信息
     * @param print Boolean 是否打印到控制台
     * @return String
     */

    fun getUserDeviceInfo(print: Boolean = true): String {
        val info = """
===============================================================
    用户本地时间 ${Date()}
    ========= DeviceInfo 设备信息 ======== 
    ===> 系统语言: ${Locale.getDefault()} 
    ===> 用户信息: ID:${LoginServiceImplWrap.getUserInfo()?.id}   Name:${LoginServiceImplWrap.getUserInfo()?.nickname}  IM_ID:${LoginServiceImplWrap.getUserInfo()?.imId}
    ===> 厂商型号: ${DeviceUtils.getManufacturer()}-${DeviceUtils.getModel()}
    ===> 系统版本: Android ${DeviceUtils.getSDKVersionName()}, API ${DeviceUtils.getSDKVersionCode()}, 架构${DeviceUtils.getABIs()[0]}
    ===> 应用版本: ${AppUtils.getAppVersionName()}(${AppUtils.getAppVersionCode()})
===============================================================
        """
        if (print) logD(info)
        return info
    }

}