package com.timekettle.upup.comm.tools

import java.net.Socket
import java.security.Principal
import java.security.PrivateKey
import java.security.cert.X509Certificate
import javax.net.ssl.X509KeyManager

/**
 * author: weiconglee
 **/
class CustomPKCS12KeyManager : X509KeyManager {
    private var currentAlias: String = PKCS12Manager.PKCS_KEY_ALIAS
    private var delegate: X509KeyManager? = null

    private fun getDelegate(): X509KeyManager? {
        if (delegate == null) {
            delegate = PKCS12Manager.getKeyManager()
        }
        return delegate
    }

    override fun getClientAliases(keyType: String?, issuers: Array<out Principal>?): Array<String> {
        return arrayOf(currentAlias)
    }

    override fun chooseClientAlias(
        keyType: Array<out String>?, issuers: Array<out Principal>?, socket: Socket?
    ): String? {
        return getDelegate()?.chooseClientAlias(keyType, issuers, socket)
    }

    override fun getServerAliases(
        keyType: String?, issuers: Array<out Principal>?
    ): Array<String>? {
        return null
    }

    override fun chooseServerAlias(
        keyType: String?, issuers: Array<out Principal>?, socket: Socket?
    ): String? {
        return null
    }

    override fun getCertificateChain(alias: String?): Array<X509Certificate>? {
        return getDelegate()?.getCertificateChain(alias)
    }

    override fun getPrivateKey(alias: String?): PrivateKey? {
        return getDelegate()?.getPrivateKey(alias)
    }


}