package com.timekettle.upup.comm.ui

import androidx.activity.viewModels
import com.timekettle.upup.base.mvvm.vm.EmptyViewModel
import com.timekettle.upup.comm.base.BaseWebActivity
import com.timekettle.upup.comm.databinding.LayoutChargingBinding
import dagger.hilt.android.AndroidEntryPoint

/**
 * 公共的 充电的View
 *
 */
@AndroidEntryPoint
class CommChargingActivity : BaseWebActivity<LayoutChargingBinding, EmptyViewModel>() {

    override val mViewModel: EmptyViewModel by viewModels()

    override fun LayoutChargingBinding.initView() {

    }

    override fun initObserve() {}

    override fun initRequestData() {}

}