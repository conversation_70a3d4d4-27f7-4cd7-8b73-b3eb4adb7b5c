package com.timekettle.upup.comm.net

import com.timekettle.upup.comm.base.BaseResponseV2
import com.timekettle.upup.comm.bean.MeetEngineHost
import com.timekettle.upup.comm.model.UserInfoRequest
import com.timekettle.upup.comm.model.CertificateBean
import com.timekettle.upup.comm.model.DfuNotifyMsg
import com.timekettle.upup.comm.model.EmailBean
import com.timekettle.upup.comm.model.PermissionBean
import com.timekettle.upup.comm.model.TokenBean
import com.timekettle.upup.comm.model.UploadBean
import okhttp3.MultipartBody
import okhttp3.RequestBody
import retrofit2.Response
import retrofit2.http.Body
import retrofit2.http.DELETE
import retrofit2.http.GET
import retrofit2.http.Header
import retrofit2.http.POST
import retrofit2.http.PUT
import retrofit2.http.Query
import retrofit2.http.Url

interface BusinessApi {
    @Deprecated("请使用 V2 接口获取 token")
    @POST("/app/token")
    suspend fun getToken(
        @Header("source") source: String,
        @Header("signature") signature: String,
        @Header("sncode") sncode: String,
        @Header("timestamp") timestamp: String,
        @Body params: RequestBody
    ): BaseResponseV2<TokenBean>

    @POST
    suspend fun getTokenV2(
        @Url url: String
    ): BaseResponseV2<TokenBean>

    /**
     * 证书签发接口
     */
    @POST
    suspend fun getCertificate(
        @Url url: String,
        @Body requestBody: HashMap<String,String>
    ): Response<BaseResponseV2<CertificateBean?>>

    @POST("/app/consents")
    suspend fun savePrivacyInfo(
        @Header("Authorization") auth: String,
        @Body params: RequestBody
    ): BaseResponseV2<Any>

    @GET("/app/firmware/messages")
    suspend fun getDfuNotify(
        @Header("Authorization") auth: String,
        @Query("app_version") appVersion: String, // 版本号
        @Query("product") product: String = "010" // 产品
    ): BaseResponseV2<DfuNotifyMsg?>

    @GET("/app/permission")
    suspend fun checkSN(
        @Header("Authorization") auth: String
    ): BaseResponseV2<PermissionBean?>

    @GET("/app/route/meeting")
    suspend fun getMeetingIpList(
        @Header("Authorization") auth: String
    ): BaseResponseV2<List<MeetEngineHost>?>

    @POST("/app/device-info")
    suspend fun saveUserInfo(
        @Header("Authorization") auth: String,
        @Body requestBody: UserInfoRequest
    ): BaseResponseV2<Any>

    @POST("/app/storage/upload")
    suspend fun getS3UploadUrl(
        @Header("Authorization") auth: String,
        @Body requestBody: Map<String,String>
    ): BaseResponseV2<UploadBean>

    @PUT
    suspend fun uploadFile(
        @Url url: String,
        @Body body: RequestBody,
        @Header("Content-Type") contentType: String
    ): Response<Unit>

    @POST("/app/x1/logs")
    suspend fun saveUploadFeedback(
        @Header("Authorization") auth: String,
        @Body requestBody: Map<String,String>
    ): BaseResponseV2<Any>

    @GET("/app/device-info/email")
    suspend fun getUserEmail(
        @Header("Authorization") auth: String
    ): BaseResponseV2<EmailBean>

    @DELETE("/app/device-info/email")
    suspend fun deleteUserEmail(
        @Header("Authorization") auth: String
    ): BaseResponseV2<Any>
}