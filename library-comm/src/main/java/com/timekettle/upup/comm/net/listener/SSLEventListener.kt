package com.timekettle.upup.comm.net.listener

import com.timekettle.upup.base.utils.logD
import okhttp3.Call
import okhttp3.EventListener
import okhttp3.Handshake
import java.net.InetSocketAddress
import java.net.Proxy

/**
 * author: weiconglee
 **/
class SSLEventListener : EventListener() {
    private val TAG = "SSLHandshake"
    override fun connectStart(
        call: Call,
        inetSocketAddress: InetSocketAddress,
        proxy: Proxy
    ) {
        logD("尝试连接到: ${inetSocketAddress.hostName}", TAG)
    }

    override fun secureConnectStart(call: Call) {
        logD("开始 SSL 握手", TAG)
    }

    override fun secureConnectEnd(call: Call, handshake: Handshake?) {
        logD("SSL 握手完成", TAG)
    }
}