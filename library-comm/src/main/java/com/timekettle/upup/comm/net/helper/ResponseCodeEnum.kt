package com.timekettle.upup.comm.net.helper

/**
 * 请求响应code枚举抽象
 *
 */
interface IResponseCode {

    /**
     * 获取该枚举的code码
     * @return Int
     */
    fun getCode(): Int

    /**
     * 获取该枚举的描述
     * @return String
     */
    fun getMessage(): String
}

enum class ReasonCodeEnum(val reasonCode: Int, val reason: String) {
//              Gateway层 业务信息.

    /**
     * 登录过期.
     */
    LoginExpired(1001, "Login Expired."),

//              Web层 业务信息.

    /**
     * 未捕捉到异常, 建议手机端打印code:reasonCode.
     */
    UnCaughtException(2000, "Un Caught Exception."),

    /**
     * 邮箱为空.
     */
    EmailEmpty(2001, "Email empty."),

    /**
     * 密码为空.
     */
    PassEmpty(2002, "Pass empty."),

    /**
     * 密码错误.
     */
    PassError(2003, "Pass Error."),

    /**
     * 验证码为空.
     */
    VerifyCodeEmpty(2004, "VerifyCode Empty."),

    /**
     * 验证码错误.
     */
    VerifyCodeError(2005, "VerifyCode Error."),

    /**
     * 昵称为空.
     */
    NicknameEmpty(2006, "Nickname Empty."),

    /**
     * 三方唯一标识为空.
     */
    ThirdUniEmpty(2007, "Third Uni Empty."),

    /**
     * 获取/验证验证码时候 optType为空.
     */
    OptTypeEmpty(2008, "OptType Empty."),

    /**
     * 用户邮箱 已注册.
     */
    EmailRegistered(2009, "Email Registered."),

    /**
     * id 为空(用户id, 其他表记录主键id).
     */
    IdEmpty(2010, "Id Empty."),

    /**
     * 头像七牛地址为空.
     */
    AvatarEmpty(2011, "Avatar Empty."),

    /**
     * token为空.
     */
    TokenEmpty(2012, "Token Empty."),

    /**
     * token过期.
     */
    TokenExpired(2013, "Token Expired."),

    /**
     * 验证码过期.
     */
    VerifyCodeExpired(2014, "VerifyCode Expired."),

    /**
     * 发送验证码限制.
     */
    VerifyCodeLimit(2015, "VerifyCode Limit."),

    /**
     * 邮箱超过50字符.
     */
    EmailLengthLimit(2016, "Email Length Limit."),

    /**
     * 邮箱密码登录次数限制.
     */
    EmailPassLoginLimit(2017, "Email Pass Login Limit."),

    /**
     * 邮箱密码登录-锁定.
     */
    EmailPassLoginLock(2018, "Email Pass Login Lock."),

    /**
     * 谷歌三方验证请求报错.
     */
    AuthTokenErrorByGoogle(2019, "Auth Token Error By Google."),

    /**
     * fb三方验证请求报错.
     */
    AuthTokenErrorByFacebook(2020, "Auth Token Error By Facebook."),

    /**
     * apple三方验证请求报错.
     */
    AuthTokenErrorByApple(2021, "Auth Token Error By Apple."),

    /**
     * wechat三方验证请求报错.
     */
    AuthTokenErrorByWechat(2022, "Auth Token Error By Wechat."),

    /**
     * 三方调用失败.
     */
    ReqErrorFromThird(2023, "Req Error From Third."),

    /**
     * 性别代码缺失.
     */
    GenderEmpty(2024, "Gender Empty."),

    /**
     * 性别代码错误.
     */
    GenderError(2025, "Gender Error."),

    /**
     * 用户ID缺失.
     */
    UserIdEmpty(2026, "UserId Empty."),

    /**
     * 充值商品ID缺失.
     */
    DepositGoodsIdEmpty(2027, "Deposit Goods Id Empty."),

    /**
     * 充值商品购买份数缺失.
     */
    DepositGoodsAmountEmpty(2028, "Deposit Goods Amount Empty."),

    /**
     * fish卡卡号为空.
     */
    FishCardNoEmpty(2029, "Fish Card No Empty."),

    /**
     * receipt为空.
     */
    ReceiptEmpty(2030, "Receipt Empty."),

    /**
     * 头像文件为空.
     */
    AvatarFileEmpty(2031, "Avatar File Empty."),

    /**
     * offline pkg goods id为空.
     */
    PkgGoodsIdEmpty(2032, "Pkg Goods Id Empty."),

    /**
     * fish 点数 不够购买下单 离线包.
     */
    FishNotEnough(2033, "Fish Not Enough."),

    /**
     * pkg coupon id/id列表为空.
     */
    PkgCouponIdEmpty(2034, "Pkg Coupon Id/Ids Empty."),

    /**
     * 购买充值商品份数 为空.
     */
    DepositGoodsQuantityEmpty(2035, "Deposit Goods Quantity Empty."),

    /**
     * apple 支付 验证调用接口出错.
     */
    DepositVerifyErrorByApple(2041, "Deposit Verify Error By Apple."),

    /**
     * google 支付 验证调用接口出错.
     */
    DepositVerifyErrorByGoogle(2042, "Deposit Verify Error By Google."),

    /**
     * wechat 支付 验证调用接口出错.
     */
    DepositVerifyErrorByWechat(2043, "Deposit Verify Error By Wechat."),

    /**
     * 充值商品ID错误.
     */
    DepositGoodsIdError(2044, "Deposit Goods Id Error."),

    /**
     * 离线包 分类 code 缺少.
     */
    PkgCategoryCodeEmpty(2045, "Pkg Category Code Empty."),

    /**
     * 用户优惠券 有误/有不可用.
     */
    PkgUserCouponIdHasProblem(2046, "Pkg User Coupon Id Has Problem."),

    /**
     * 该receipt 已经充值成功/重复充值.
     */
    ReceiptRepeatDeposit(2047, "Receipt Repeat Deposit."),

    /**
     * xls 文件为空/无记录.
     */
    XlsFileEmptyOrNoData(2048, "Xls File Empty Or No Data."),

    /**
     * 时长套餐为空.
     */
    DurationPkgIdEmpty(2049, "Duration Pkg Id Empty."),

    /**
     * T1 唯一标识号 已经激活/注册.
     */
    ThirdUniActivated(2050, "Third Uni Activated."),

    /**
     * 充值商品 mark为空.
     */
    DepositGoodsMarkEmpty(2051, "Deposit Goods Mark Empty."),

    /**
     * apple支付 transactionId为空.
     */
    TransactionIdEmpty(2052, "Transaction Id Empty."),

    /**
     * pc 拉新失败.
     */
    PullNewFailed(2053, "Pull New Failed."),

    /**
     * 邮箱 非法.
     */
    EmailInvalid(2054, "Email Invalid."),

    /**
     * 语言代码 为空.
     */
    LangEmpty(2055, "Lang Empty."),

    /**
     * config props resolve key invalid.
     */
    ConfigPropsResolveKeyInvalid(2056, "Config Props Resolve Key Invalid."),

    /**
     * 邀请码 为空.
     */
    InviteCodeEmpty(2057, "Invite Code Empty."),

    /**
     * 活动ID 为空.
     */
    ActivityIdEmpty(2058, "Activity Id  Empty."),

    /**
     * 活动请求 非法.
     */
    ActivityReqInvalid(2059, "Activity  Req Invalid."),

    /**
     * 用户ID 非法(拉新/被拉新 查不到用户记录).
     */
    UserIdInvalid(2061, "User Id Invalid."),

    /**
     * 起始时间为空.
     */
    StartTimeEmpty(2062, "Start Time Empty."),

    /**
     * 结束时间为空.
     */
    EndTimeEmpty(2063, "End Time Empty."),

    /**
     * 用户时长 禁用.
     */
    DurationForbid(2064, "Duration Forbid."),

    /**
     * 用户时长 不足.
     */
    DurationUnEnough(2065, "Duration Un Enough."),

    /**
     * 多个日期之间大小 非法.
     */
    TimesInvalid(2066, "Times Invalid."),

    /**
     * 时长上报完成, 不可再上报.
     */
    DurationModCompleted(2067, "Duration Mod Completed."),

    /**
     * 时长为空.
     */
    DurationEmpty(2068, "Duration Empty."),

    /**
     * 模型统计类型 为空.
     */
    ModelCountTypeEmpty(2069, "Model Count Type Empty."),

    /**
     * Paypal订单金额为空.
     */
    PaypalTotalEmpty(2070, "PayPal Total Empty."),

    /**
     * Paypal订单币种为空.
     */
    PaypalCurrencyEmpty(2071, "Paypal Currency Empty."),

    /**
     * orderList订单号为空
     */
    OrderNoEmpty(2072, "Order No. Empty."),

    /**
     * 被赠送时长用户的邮箱为空.
     */
    AssociatedEmailEmpty(2073, "Associated Email Empty."),

    /**
     * 赠送时长为空
     */
    TimeCostEmpty(2074, "Time Cost Empty."),

    /**
     * 幂等 限制.
     */
    IdempotentLimit(2075, "Idempotent Limit."),

    /**
     * 邀请码 错误.
     */
    InviteCodeError(2076, "Invite Code Error."),

    /**
     * 版本号为空.
     */
    VersionEmpty(2077, "Version Empty"),

    /**
     * 已经是最高版本.
     */
    AlreadyLatestVersion(2078, "Already Latest Version"),

    /**
     *
     */
    InvalidAssociatedEmail(2079, "Invalid Associated Email"),

    /**
     * paypal 验证支付失败.
     */
    DepositVerifyErrorByPaypal(2080, "Deposit Verify Error By Paypal"),

    /**
     * 充值方式 参数错误.
     */
    InvalidDepositType(2081, "Invalid Deposit Type"),

    /**
     * token 非法.
     */
    TokenInvalid(2082, "Token Invalid."),

    /**
     * 用户评分 非法.
     */
    ScoreInvalid(2088, "Score Invalid."),

    /**
     * 反馈类型为空.
     */
    FeedbackTopicEmpty(2089, "Feedback Topic Empty."),

    /**
     * 反馈次数超出限制.
     */
    FeedbackLimit(2090, "Feedback Limit."),

    /**
     * im 账号导入失败
     */
    ImImportError(2091, "IM account Import Error."),

    /**
     * 用户注销密码验证-锁定.
     */
    LogoffPassCheckLock(2092, "Logoff Pass Check Lock."),

    /**
     * 用户注销密码验证-次数限制.
     */
    LogoffPassCheckLimit(2093, "Logoff Pass Check limit."),

//              Service层 业务信息.

    /**
     * 查无数据 by Email.
     */
    NoDataByEmail(3001, "No data found by email."),

    /**
     * 查无数据 by Id.
     */
    NoDataById(3002, "No data found by id."),

    /**
     * Json 处理异常.
     */
    JsonOptError(3003, "Json opt error."),

    /**
     * 根据三方授权唯一标识查询不到信息.
     */
    NoDataByThirdUni(3004, "No data found by auth token."),

    /**
     * 查无数据 by 多条件查询.
     */
    NoDataByMultiCondition(3004, "No data found by multi Condition."),

    /**
     * 查无数据 by 多条件查询.
     */
    NoDataByReceipt(3005, "No data found by Receipt."),

    /**
     * 查无数据 by userId.
     */
    NoDataByUserId(3006, "No data found by userId."),

//              starter 业务信息.

    /**
     * 七牛获取token 错误.
     */
    OssFetchTokenError(4001, "Oss Fetch Token Error."),

    /**
     * 七牛上传file 错误.
     */
    OssUploadFileError(4002, "Oss Upload File Error."),

    //              third call error.
    EmailSendError(5001, "Email Send Error."),

    ReceiptNonePaid(2097, "Receipt None Paid."),
    ReceiptRefund(2098, "Receipt Has Refund."),

    /**
     * Token异地登录
     */
    RemotingLoginExpired(5006, "Remoting Login Expired.");
}


//一级响应码
enum class RespCodeEnum(val success: Boolean, val code: Int, val desc: String) {

    Success(true, 200, "Success."),

    ParamMissing(false, 301, "Param Missing."),
    ParamError(false, 302, "Param Error."),

    NoService(false, 401, "No Service."),
    DataNotFound(false, 404, "Data Not Found."),

    InternalError(false, 500, "Internal Error."),
    ThirdError(false, 501, "Third Called Error.");

}


/**
 * 请求响应code的枚举 , 废弃
 *
 */
enum class ResponseCodeEnum : IResponseCode {
    //登录超时
    TOKEN_INVALID {
        override fun getCode() = 403
        override fun getMessage() = "暂未登录或token已经过期"
    },

    //参数错误
    PARAMETER_ERROR {
        override fun getCode() = 102
        override fun getMessage() = "参数错误"
    },

    //用户不存在
    ERROR_NO_USER {
        override fun getCode() = 400
        override fun getMessage() = "用户不存在"
    },

    // 通用异常
    ERROR {
        override fun getCode() = 100
        override fun getMessage() = "处理失败"
    },

    // 登录成功
    SUCCESS {
        override fun getCode() = 200
        override fun getMessage() = "成功"
    },

    // 请求接口成功
    RESP_SUCCESS {
        override fun getCode() = 2000
        override fun getMessage() = "接口返回成功"
    },


}