package com.timekettle.upup.comm.tools

import android.os.Build
import android.util.Log
import co.timekettle.btkit.BleCmdContant
import co.timekettle.btkit.BleUtil
import co.timekettle.btkit.bean.RawBlePeripheral
import co.timekettle.btkit.bean.WT2BlePeripheral
import com.timekettle.upup.base.utils.logE
import com.timekettle.upup.comm.ktx.isBleLeft
import com.timekettle.upup.comm.net.helper.ReqSignUtils
import com.timekettle.upup.comm.service.home.HomeServiceImplWrap
import java.util.*

/**
 *
 * @author: licoba
 * @date: 2022/7/11
 */
object DeviceTool {

    const val TAG: String = "DeviceTool"

    /**
     * 是否为左边的设备
     */
    fun isLeft(device: RawBlePeripheral?): Boolean {
        if (device == null) return false
        if (device.name.uppercase(Locale.getDefault()).contains("-L")) {
            return true
        }
        asWSeries(device)?.let {
            if (it.role == RawBlePeripheral.Role.Left) return true
        }
        return false;
    }

    /**
     * 是否为右耳的设备
     */
    fun isRight(device: RawBlePeripheral?): Boolean {
        if (device == null) return false
        if (device.name.uppercase(Locale.getDefault()).contains("-R")) {
            return true
        }
        asWSeries(device)?.let {
            if (it.role == RawBlePeripheral.Role.Right) return true
        }
        return false;
    }

    fun findLeftDevice(list: MutableList<RawBlePeripheral>): RawBlePeripheral? {
        return list.find {
            it.name.uppercase(Locale.getDefault()).contains("-L") || it.name.uppercase(
                Locale.getDefault()
            ).contains("01")
        }
    }

    fun findRightDevice(list: MutableList<RawBlePeripheral>): RawBlePeripheral? {
        return list.find {
            it.name.uppercase(Locale.getDefault()).contains("-R") || it.name.uppercase(
                Locale.getDefault()
            ).contains("02")
        }
    }

    fun findDeviceById(id: String, list: MutableList<RawBlePeripheral>): RawBlePeripheral? {
        return list.find {
            it.id == id
        }
    }

    fun asWSeries(peripheral: RawBlePeripheral?): WT2BlePeripheral? {
        if (peripheral == null) return null
        return if (peripheral.productType in listOf(
                BleCmdContant.ProductType.WT2,
                BleCmdContant.ProductType.WT2_Edge,
                BleCmdContant.ProductType.W3_Pro,
            )
        ) {
            peripheral as WT2BlePeripheral
        } else {
            null
        }
    }


    fun connectDevice(
        tag: String,
        device: RawBlePeripheral?,
        onDeviceConnected: ((device: RawBlePeripheral?) -> Unit)? = null
    ) {
        when (BleUtil.shared.connectedPeripherals.size) {
            1 -> { //当前只连1只耳机
                val peripheral = BleUtil.shared.connectedPeripherals[0]
                asWSeries(peripheral)?.let {
                    HomeServiceImplWrap.saveFirstDeviceMac(it.macSuffix4)
                    onDeviceConnected?.invoke(device)
                    Log.d(
                        TAG,
                        "$tag, connect 当前拾音的耳机mac: ${HomeServiceImplWrap.getFirstDeviceMac()}"
                    )
                }
            }

            2 -> { //当连接第2只耳机，这只耳机不拾音，只播放(可以用来创建通道)

            }

            else -> {
                Log.d(
                    TAG,
                    "$tag, connect 连接的耳机数量: ${BleUtil.shared.connectedPeripherals.size}"
                )
            }
        }
    }

    fun disconnectDevice(tag: String, onDeviceChange: (() -> Unit)? = null) {
        val connectedList = BleUtil.shared.connectedPeripherals
        when (connectedList.size) {
            1 -> { //耳机断开连接，当前只连了1只耳机
                val peripheral = connectedList[0]
                asWSeries(peripheral)?.let {
                    if (it.macSuffix4 != HomeServiceImplWrap.getFirstDeviceMac()) {
                        HomeServiceImplWrap.saveFirstDeviceMac(it.macSuffix4)
                        onDeviceChange?.invoke()
                        Log.d(
                            TAG,
                            "$tag, disconnect 切换耳机拾音 当前拾音的耳机mac: ${HomeServiceImplWrap.getFirstDeviceMac()}"
                        )
                    }
                }
            }

            else -> {
                Log.d(
                    TAG,
                    "$tag, disconnect 连接的耳机数量: ${connectedList.size}"
                )
            }
        }
    }


    fun getSerialNumber(): String {
        var serial = ""
        try {
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
                serial = Build.getSerial()
            }
        } catch (e: Exception) {
            logE("获取SN异常 $e")
            e.printStackTrace()
        }
        return serial
    }

    fun genSign(timestamp: String): String {
        val headerMap = mutableMapOf<String, String>()
        headerMap["snCode"] = getSerialNumber()
        headerMap["timestamp"] = timestamp
        var signContent = ReqSignUtils.toSignedContent(headerMap)
        // 移除开始和结束的标记，以及换行
        return ReqSignUtils.sign(signContent, PRIVATE_KEY)
    }

    // 根据耳机的Mac地址判断是不是左耳机
    fun macIsLeftHeadset(macAddress:String):Boolean{
        BleUtil.shared.connectedPeripherals.forEach {
            DeviceTool.asWSeries(it)?.let {
                if (it.macSuffix4 == macAddress){
                    return it.isBleLeft()
                }
            }
        }
        return false;
    }

    private val PRIVATE_KEY = """
******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************
    """.trimIndent()

}

