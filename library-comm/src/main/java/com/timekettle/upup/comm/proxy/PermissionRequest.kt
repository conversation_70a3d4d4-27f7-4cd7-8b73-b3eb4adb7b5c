package com.timekettle.upup.comm.proxy

import android.Manifest
import androidx.fragment.app.FragmentActivity
import com.hjq.permissions.OnPermissionCallback
import com.hjq.permissions.XXPermissions

/**
 * 权限请求与界面进行解耦
 *
 */
class PermissionRequest {

    /**
     * 权限请求
     * @param activity FragmentActivity 当前申请权限请求的 activity
     * @param allGrantedCall allGrantedBlock: () -> Unit 全部授予回调
     * @param deniedCall deniedBlock: (deniedList: List<String>) -> Unit 有被拒绝回调
     */
    inline fun permissionRequest(
        activity: FragmentActivity,
        crossinline allGrantedCall: () -> Unit,
        crossinline deniedCall: (deniedList: List<String>) -> Unit
    ) {
        XXPermissions.with(activity)
            .permission(
                Manifest.permission.ACCESS_NETWORK_STATE
            )
            .permission(Manifest.permission.WRITE_EXTERNAL_STORAGE)
            .permission(Manifest.permission.READ_EXTERNAL_STORAGE)
            .permission(Manifest.permission.ACCESS_WIFI_STATE)
            .permission(Manifest.permission.READ_PHONE_STATE)
            .permission(Manifest.permission.ACCESS_COARSE_LOCATION)
            .permission(Manifest.permission.ACCESS_FINE_LOCATION)
            .permission(Manifest.permission.RECORD_AUDIO)
            .request(object : OnPermissionCallback {

                override fun onGranted(permissions: MutableList<String>, all: Boolean) {
                    if (all) {
                    } else {
                    }
                }

                override fun onDenied(permissions: MutableList<String>, never: Boolean) {
                    if (never) {
                        // 如果是被永久拒绝就跳转到应用权限系统设置页面
                    } else {
                    }
                }
            })
    }
}