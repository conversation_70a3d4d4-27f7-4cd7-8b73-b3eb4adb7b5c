package com.timekettle.upup.comm.base

import android.util.Log
import androidx.room.*
import androidx.sqlite.db.SimpleSQLiteQuery
import androidx.sqlite.db.SupportSQLiteQuery
import java.lang.reflect.ParameterizedType

/**
 * <AUTHOR>
 * @date 2022/11/11
 * @desc
 */
@Dao
abstract class BaseDao<T> {

    val tableName: String
        get() {
            val clazz = (javaClass.superclass.genericSuperclass as ParameterizedType)
                .actualTypeArguments[0] as Class<*>
            val tableName = clazz.simpleName
            Log.d("BaseDao", "tableName= $tableName")
            return tableName
        }

    @Insert(onConflict = OnConflictStrategy.REPLACE)
    abstract suspend fun insert(t: T): Long

    @Insert(onConflict = OnConflictStrategy.REPLACE)
    abstract suspend fun insert(list: List<T>): List<Long>

    @Delete
    abstract suspend fun delete(t: T): Int

    @Delete
    abstract suspend fun delete(list: List<T>)

    @Update
    abstract suspend fun update(t: T): Int

    @Update
    abstract suspend fun update(list: List<T>)

    @RawQuery
    abstract suspend fun deleteAll(query: SupportSQLiteQuery): Long

    suspend fun deleteAll(): Long {
        val query = SimpleSQLiteQuery("DELETE FROM $tableName")
        return deleteAll(query)
    }

    @RawQuery
    abstract suspend fun count(query: SupportSQLiteQuery): Int

    /**
     * 返回表中的记录数
     */
    suspend fun count(): Int {
        val query = SimpleSQLiteQuery("SELECT COUNT(*) FROM $tableName")
        return count(query)
    }

    @RawQuery
    abstract suspend fun findAll(query: SupportSQLiteQuery): List<T>

    suspend fun findAll(): List<T> {
        val query = SimpleSQLiteQuery("SELECT * FROM $tableName")
        return findAll(query)
    }
}