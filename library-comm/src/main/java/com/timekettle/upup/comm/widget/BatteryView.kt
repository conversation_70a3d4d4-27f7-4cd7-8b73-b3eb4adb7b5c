package com.timekettle.upup.comm.widget;

import android.content.Context
import android.graphics.Canvas
import android.graphics.Color
import android.graphics.Paint
import android.graphics.RectF
import android.util.AttributeSet
import android.view.View
import androidx.annotation.ColorInt
import androidx.annotation.ColorRes
import androidx.appcompat.widget.AppCompatImageView
import androidx.core.content.ContextCompat
import com.timekettle.upup.comm.R

/**
 * 电量的自定义View
 */
class BatteryView @JvmOverloads constructor(
    context: Context,
    attrs: AttributeSet? = null,
    defStyleAttr: Int = 0
) : View(context, attrs, defStyleAttr) {

    companion object {
        val BatteryColorNormal: Int = Color.parseColor("#15FF4F")
        val BatteryColorLow: Int = Color.parseColor("#FC6D74")
    }

    private var batteryColor: Int = BatteryColorNormal
    private var progress: Float = 0f

    private val backgroundPaint = Paint(Paint.ANTI_ALIAS_FLAG).apply {
        color = Color.BLACK
    }

    private val progressPaint = Paint(Paint.ANTI_ALIAS_FLAG).apply {
        style = Paint.Style.FILL
    }

    init {
        val typedArray =
            context.obtainStyledAttributes(attrs, R.styleable.BatteryView, defStyleAttr, 0)
        batteryColor = typedArray.getColor(R.styleable.BatteryView_batteryColor, BatteryColorNormal)
        progress = typedArray.getFloat(R.styleable.BatteryView_progress, 0f)
        typedArray.recycle()
    }

    override fun onDraw(canvas: Canvas) {
        super.onDraw(canvas)
        val width = width.toFloat()
        val height = height.toFloat()
        val halfHeight = height / 2

        // 计算圆角
        val cornerRadius = halfHeight * 1

        // 画背景
        val backgroundRect = RectF(0f, 0f, width, height)
        canvas.drawRoundRect(backgroundRect, cornerRadius, cornerRadius, backgroundPaint)

        // 保存画布状态
        canvas.save()

        // 剪裁进度
        val batteryWidth = width * progress
        val progressRect = RectF(0f, 0f, batteryWidth, height)
        canvas.clipRect(progressRect)

        // 画进度
        progressPaint.color = batteryColor
        canvas.drawRoundRect(backgroundRect, cornerRadius, cornerRadius, progressPaint)

        // 恢复画布状态
        canvas.restore()
    }

    fun setBatteryColor(color: Int) {
        batteryColor = color
        invalidate()
    }

    fun setProgress(progress: Float) {
        this.progress = progress
        if (progress <= 0.2f) {
            setBatteryColor(BatteryColorLow)
        } else {
            setBatteryColor(BatteryColorNormal)
        }
    }


}
