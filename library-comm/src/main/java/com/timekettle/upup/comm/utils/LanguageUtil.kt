package com.timekettle.upup.comm.utils

import android.annotation.TargetApi
import android.content.Context
import android.content.res.Configuration
import android.content.res.Resources
import android.os.Build
import android.os.LocaleList
import android.util.DisplayMetrics
import android.widget.Toast
import com.timekettle.upup.base.utils.logD
import java.util.*


/**
 *
 * @author: licoba
 * @date: 2022/5/17
 */
object LanguageUtil {
    fun isSimpleZh(): Boolean {
        val locale: Locale = Locale.getDefault();
        // 获取当前系统语言
        val ZH = "zh"
        val CN_HANS = "zh-Hans"//中文简体
        val CN_HANT = "zh-Hant" //中文繁体
        if (locale.language.equals(ZH)) {
            //中国 zh-Hans-CN
            //台湾 zh-Hans-TW
            //澳门 zh-Hans-MO
            //香港 zh-Hans-HK
            return if (locale.toLanguageTag().contains(CN_HANS)) {
//                logD("当前手机语言环境是 简体")
                true
            } else if (locale.toLanguageTag().contains(CN_HANT)) {
//                logD("当前手机语言环境是 繁体")
                false
            } else if (locale.country == "CN") {
//                logD("当前手机语言环境是 简体");
                true
            } else {
//                logD("当前手机语言环境是 繁体");
                false
            }
        } else {
//            logD("当前手机语言环境是 不是中文");
            return false;
        }
    }

    // 是繁体中文
    fun isUnSimpleZh(): Boolean {
        val locale: Locale = Locale.getDefault();
        // 获取当前系统语言
        val ZH = "zh"
        val CN_HANS = "zh-Hans"//中文简体
        val CN_HANT = "zh-Hant" //中文繁体
        return if (locale.language.equals(ZH)) {
            if (locale.toLanguageTag().contains(CN_HANS)) {
                false
            } else if (locale.toLanguageTag().contains(CN_HANT)) {
                true
            } else locale.country != "CN"
        } else {
            false
        }
    }


    // 根据想要的语言参数language，获取对应语言的Locale，更多语言请另行添加
    fun getLocaleByLanguage(language: String): Locale {
        var myLocale = when (language) {
            "en" -> {
                Locale.ENGLISH
            }

            else -> {
                Locale.CHINESE
            }
        }
        return myLocale
    }

    // 该方法为activity中的attachBaseContext提供继承内容，用于初始化切换语言
    fun attachBaseContext(context: Context, language: String): Context {
        return if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.N) {
            updateResources(context, language)
        } else {
            return context
        }
    }

    // 该方法用于页面初始化时的切换语言，返回配置信息的上下文context
    @TargetApi(Build.VERSION_CODES.N)
    private fun updateResources(context: Context, language: String): Context {
        var resources: Resources = context.getResources()
        var locale: Locale = getLocaleByLanguage(language)
        var configuration: Configuration = resources.getConfiguration()
        configuration.setLocale(locale)
        configuration.setLocales(LocaleList(locale))
        return context.createConfigurationContext(configuration)
    }


    // 获取当前语言显示名称
    fun getLanguageDisplayName(): String {
        val code = Locale.getDefault().language
        logD("code:$code")
        return when (code) {
            "zh" -> if (isSimpleZh()) "简体中文" else "繁體中文"
            "en" -> "English"
            "ja" -> "日本語"
            "de" -> "Deutsch"
            "es" -> "Español"
            "fr" -> "Français"
            "ko" -> "한국어"
            "ru" -> "Русский"
            "th" -> "ภาษาไทย"
            "uk" -> "Українська мова"
            "tr" -> "Türkçe"
            else -> ""
        }
    }

    fun isRu(): Boolean {
        return Locale.getDefault().language == "ru"
    }


    fun isJP(): Boolean {
        return Locale.getDefault().language == "ja"
    }

}
