package com.timekettle.upup.comm.dialog

import android.content.Context
import android.content.Intent
import android.graphics.Color
import android.view.Gravity
import android.view.View
import android.view.ViewGroup
import android.view.animation.Animation
import androidx.constraintlayout.widget.ConstraintLayout
import androidx.lifecycle.LifecycleOwner
import co.timekettle.btkit.BleUtil
import co.timekettle.btkit.bean.RawBlePeripheral
import co.timekettle.btkit.bean.WT2BlePeripheral
import co.timekettle.btkit.sample.OperationActivity
import com.alibaba.android.arouter.launcher.ARouter
import com.blankj.utilcode.util.GsonUtils
import com.google.gson.Gson
import com.timekettle.upup.base.ktx.animateAlpha1
import com.timekettle.upup.base.ktx.getAppViewModel
import com.timekettle.upup.base.ktx.gone
import com.timekettle.upup.base.ktx.invisible
import com.timekettle.upup.base.ktx.visible
import com.timekettle.upup.base.utils.SpUtils
import com.timekettle.upup.base.utils.logV
import com.timekettle.upup.base.utils.showToast
import com.timekettle.upup.comm.BuildConfig
import com.timekettle.upup.comm.R
import com.timekettle.upup.comm.constant.RouteKey
import com.timekettle.upup.comm.constant.RouteUrl
import com.timekettle.upup.comm.constant.SpKey
import com.timekettle.upup.comm.databinding.CommDialogDeviceBinding
import com.timekettle.upup.comm.ktx.findLeft
import com.timekettle.upup.comm.ktx.findRight
import com.timekettle.upup.comm.ktx.isLeft
import com.timekettle.upup.comm.ktx.isRight
import com.timekettle.upup.comm.model.X1Device
import com.timekettle.upup.comm.model.X1Status
import com.timekettle.upup.comm.service.home.HomeServiceImplWrap
import com.timekettle.upup.comm.utils.LanguageUtil
import com.timekettle.upup.comm.viewmodel.VMTopDevice
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.cancel
import kotlinx.coroutines.delay
import kotlinx.coroutines.launch
import org.libpag.PAGFile
import org.libpag.PAGScaleMode
import org.libpag.PAGView
import razerdp.basepopup.BasePopupWindow
import razerdp.util.animation.AlphaConfig
import razerdp.util.animation.AnimationHelper
import razerdp.util.animation.ScaleConfig

/**
 *
 * <AUTHOR>
 * @desc 设备大弹窗，全局设备弹窗，设备弹窗Dialog，全局大弹窗
 *
 */

open class DeviceDialog(context: Context) : BasePopupWindow(context) {

    lateinit var binding: CommDialogDeviceBinding
    private val mainCoroutineScope = CoroutineScope(Dispatchers.Main) // 需要自己管理生命周期
    private var needUpgrade = false

    //    private val viewModel by lazy(LazyThreadSafetyMode.NONE) {
//        ViewModelProvider(context as ViewModelStoreOwner)[VMTopDevice::class.java]
//    }
    private val viewModel: VMTopDevice by lazy { getAppViewModel() }
    lateinit var pagTakeOut: PAGView

    init {
        popupGravity = Gravity.CENTER
        isOutSideTouchable = true
        setContentView(R.layout.comm_dialog_device)
        setOutSideDismiss(true)
        setBackPressEnable(true)
        setOutSideTouchable(false)
        setBackgroundColor(Color.parseColor("#CC000000"))
        initData()
    }

    private fun initData() {
        viewModel.liveX1Devices.observe(context as LifecycleOwner, ::processDevices)
        viewModel.liveNeedUpgrade.observe(context as LifecycleOwner, ::processNeedUpgrade)
    }

    private fun processNeedUpgrade(need: Boolean) {
        this.needUpgrade = need
        if (need) {
            binding.btnConfirm.text = context.getString(R.string.device_upgrade_now)
        } else {
            binding.btnConfirm.text = context.getString(R.string.common_confirm)
        }
        if (need) {  // 需要升级，字体变小
            if (LanguageUtil.isSimpleZh() || LanguageUtil.isUnSimpleZh()) return
            binding.btnConfirm.textSize = 11f
        }
    }

    override fun onViewCreated(contentView: View) {
        binding = CommDialogDeviceBinding.bind(contentView)
        binding.initListener()
        // Pag的动效
        val pagView = PAGView(context).apply {
            layoutParams = ConstraintLayout.LayoutParams(
                ViewGroup.LayoutParams.MATCH_PARENT,
                ViewGroup.LayoutParams.MATCH_PARENT
            )
            composition = PAGFile.Load(context.assets, "ani_pop_earbus_light.pag")
            setRepeatCount(1)
            setScaleMode(PAGScaleMode.Stretch) // 拉伸填充
            play()
        }
        binding.llPagBgBorder.addView(pagView)

        pagTakeOut = PAGView(context).apply {
            layoutParams = ConstraintLayout.LayoutParams(
                ViewGroup.LayoutParams.MATCH_PARENT,
                ViewGroup.LayoutParams.MATCH_PARENT
            )
            composition = PAGFile.Load(context.assets, "ani_pop_takeout_new.pag")
            setRepeatCount(1)
            setScaleMode(PAGScaleMode.Stretch) // 拉伸填充
            play()
        }
        binding.llPagTakeout.addView(pagTakeOut)
        showCloseIcon()
    }

    private fun showCloseIcon() {
        mainCoroutineScope.launch {
            delay(200)
            binding.ivClose.animateAlpha1(duration = 300)
        }
    }

    private fun CommDialogDeviceBinding.initListener() {
        btnConfirm.setOnClickListener {
            dismiss()
            if (needUpgrade) {
                ARouter.getInstance().build(RouteUrl.Setting.SettingActivityUpgrade)
                    .withString(
                        RouteKey.DFU_MSG,
                        GsonUtils.toJson(HomeServiceImplWrap.getDfuNotifyMsg())
                    )
                    .navigation()
            }
        }
        ivClose.setOnClickListener { dismiss() }
        llDeviceLeft.setOnLongClickListener {
            openDeviceOpration(true)
            true
        }
        llDeviceRight.setOnLongClickListener {
            openDeviceOpration(false)
            true
        }
    }


    private fun openDeviceOpration(isLeft: Boolean) {
        if (!SpUtils.getBoolean(SpKey.IS_DEBUG_STATUS, false) && !BuildConfig.DEBUG)
            return
        val bleDevice = BleUtil.shared.connectedPeripherals.find {
            if (isLeft)
                (it as WT2BlePeripheral).role == RawBlePeripheral.Role.Left
            else
                (it as WT2BlePeripheral).role == RawBlePeripheral.Role.Right
        }
        if (bleDevice == null) {
            if (isLeft)
                showToast("请先连接左耳")
            else
                showToast("请先连接右耳")
            return
        }
        val intent = Intent(context, OperationActivity::class.java)
        intent.putExtra(OperationActivity.KEY_DATA, Gson().toJson(bleDevice))
        context.startActivity(intent)
    }


    private fun processDevices(devices: MutableList<X1Device>) {
        logV("BLE设备数据: $devices")
        val leftDevice = devices.findLeft()
        val rightDevice = devices.findRight()
        with(binding) {
            llDevice.visible()
            llPleaseTakeOut.gone()
            tvBottomTip.text = ""
            tvBottomTip.visible()
        }
        if (leftDevice != null) {
            binding.updateX1Status(leftDevice)
            updateElectric(leftDevice)
        }
        if (rightDevice != null) {
            binding.updateX1Status(rightDevice)
            updateElectric(rightDevice)
        }
    }


    private fun updateElectric(device: X1Device) {
        // 假装100的电
        var electric = if (device.state == X1Status.ChargingInBox) {
            device.serialDevice?.electric
        } else {
            device.bleDevice?.electric
        }
        if (electric == 0 || electric == null) electric = 100
        if (device.isLeft()) {
            binding.ivBatteryLeft.setProgress(electric / 100f)
            if (device.state == X1Status.ChargingInBox) { // 在盒子里面不需要百分号
                binding.ivChargingLeft.visible()
                binding.tvBatteryLeft.text = "$electric"
            } else {
                binding.ivChargingLeft.gone()
                binding.tvBatteryLeft.text = "$electric%"
            }
            if (electric <= 20) {
                binding.tvBatteryLeft.setTextColor(Color.parseColor("#FC6D74"))
            } else {
                if (device.state == X1Status.ChargingInBox) { // 在盒子里面是绿色
                    binding.tvBatteryLeft.setTextColor(Color.parseColor("#FF15FF4F"))
                } else {
                    binding.tvBatteryLeft.setTextColor(Color.parseColor("#FFFFFF"))
                }
            }
        } else if (device.isRight()) {
            binding.ivBatteryRight.setProgress(electric / 100f)
            if (device.state == X1Status.ChargingInBox) { // 在盒子里面不需要百分号
                binding.ivChargingRight.visible()
                binding.tvBatteryRight.text = "$electric"
            } else {
                binding.ivChargingRight.gone()
                binding.tvBatteryRight.text = "$electric%"
            }
            if (electric <= 20) {
                binding.tvBatteryRight.setTextColor(Color.parseColor("#FC6D74"))
            } else {
                if (device.state == X1Status.ChargingInBox) { // 在盒子里面是绿色
                    binding.tvBatteryRight.setTextColor(Color.parseColor("#FF15FF4F"))
                } else {
                    binding.tvBatteryRight.setTextColor(Color.parseColor("#FFFFFF"))
                }
            }
        }
    }

    private fun CommDialogDeviceBinding.updateX1Status(device: X1Device) {

        if (device.isLeft()) {
            ivBrokenLeft.invisible()
            llBatteryLeft.invisible()
            lottieChargingLeft.invisible()
            vLottieAnimationViewLeft.invisible()
        } else {
            ivBrokenRight.invisible()
            llBatteryRight.invisible()
            lottieChargingRight.invisible()
            vLottieAnimationViewRight.invisible()
        }

        when (device.state) {
            X1Status.SearchConnecting -> {
                if (device.isLeft()) {
                    vLottieAnimationViewLeft.visible()
                } else {
                    vLottieAnimationViewRight.visible()
                }
            }

            X1Status.Connected -> {
                if (device.isLeft()) {
                    llBatteryLeft.visible()
                } else {
                    llBatteryRight.visible()
                }
            }

            X1Status.ChargingInBox -> {
                if (device.isLeft()) {
                    lottieChargingLeft.visible()
                } else {
                    lottieChargingRight.visible()
                }
            }

            X1Status.Broken -> {
                if (device.isLeft()) {
                    ivBrokenLeft.visible()
                } else {
                    ivBrokenRight.visible()
                }
            }

            else -> {}
        }
    }

    override fun onCreateShowAnimation(): Animation {
        return AnimationHelper.asAnimation()
            .withScale(ScaleConfig().scale(0.25f, 1.0f))
            .withAlpha(AlphaConfig.IN)
            .toShow().apply {
                duration = 300
            }
    }

    override fun onCreateDismissAnimation(): Animation {
        return AnimationHelper.asAnimation()
//            .withScale(ScaleConfig())
            .withAlpha(AlphaConfig.OUT)
            .toDismiss().apply {
                duration = 200
            }
    }

    override fun onDestroy() {
        mainCoroutineScope.cancel()
        super.onDestroy()
    }


}