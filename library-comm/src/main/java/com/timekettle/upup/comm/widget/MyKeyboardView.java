package com.timekettle.upup.comm.widget;

import android.content.Context;
import android.graphics.Canvas;
import android.graphics.Color;
import android.inputmethodservice.Keyboard;
import android.inputmethodservice.KeyboardView;
import android.text.Editable;
import android.util.AttributeSet;
import android.util.Log;
import android.view.Gravity;
import android.view.MotionEvent;
import android.widget.EditText;
import android.widget.PopupWindow;
import android.widget.Toast;

import com.timekettle.upup.base.BaseApp;
import com.timekettle.upup.base.utils.EventBusRegister;
import com.timekettle.upup.base.utils.EventBusUtils;
import com.timekettle.upup.base.utils.UtilsKt;
import com.timekettle.upup.comm.R;
import com.timekettle.upup.comm.model.CallPhoneEvent;

import org.greenrobot.eventbus.EventBus;

import java.util.List;
import java.util.Random;

import razerdp.basepopup.BasePopupWindow;

/**
 * ****************************************************************
 * 文件名称: MyKeyboardView
 * 作    者: Created by gyd
 * 创建时间: 2018/11/29 17:20
 * 文件描述: 自定义键盘，支持多种键盘切换
 * 注意事项: 密码输入
 * ****************************************************************
 */
public class MyKeyboardView extends KeyboardView {
    public static final int KEYBOARDTYPE_Num = 0;//数字键盘
//    public static final int KEYBOARDTYPE_Num_Pwd = 1;//数字键盘（密码）
    public static final int KEYBOARDTYPE_ABC = 2;//字母键盘
    public static final int KEYBOARDTYPE_Symbol = 4;//符号键盘
    public static final int KEYBOARDTYPE_Only_Num_Pwd = 5;//纯数字键盘(不能切换其他键盘)
    public static final int KEYBOARDTYPE_Only_Phone_Num = 7;//纯数字键盘带拨号键(不能切换其他键盘)

    private final String strLetter = "abcdefghijklmnopqrstuvwxyz";//字母

    private EditText mEditText;
    private KeyBoardPop mWindow;

    private Keyboard keyboardNum;
    private Keyboard keyboardNumPwd;
    private Keyboard keyboardOnlyNumPwd;
    private Keyboard keyboardABC;
    private Keyboard keyboardSymbol;

    private Keyboard keyPhone;

    public boolean isSupper = false;//字母键盘 是否大写
    public boolean isPwd = false;//数字键盘 是否随机
    private int keyBoardType;//键盘类型

    public int getKeyBoardType() {
        return keyBoardType;
    }

    public MyKeyboardView(Context context, AttributeSet attrs) {
        super(context, attrs);
    }

    public MyKeyboardView(Context context, AttributeSet attrs, int defStyle) {
        super(context, attrs, defStyle);
    }

    public void init(EditText editText, KeyBoardPop window, int keyBoardType) {
        this.mEditText = editText;
        this.mWindow = window;
        this.keyBoardType = keyBoardType;
//        if (keyBoardType == KEYBOARDTYPE_Num_Pwd || keyBoardType == KEYBOARDTYPE_Only_Num_Pwd)
//            isPwd = true;
        setEnabled(true);
        setPreviewEnabled(false);
        setOnKeyboardActionListener(mOnKeyboardActionListener);
        setKeyBoardType(keyBoardType);
    }

    public EditText getEditText() {
        return mEditText;
    }

    /**
     * 设置键盘类型
     */
    public void setKeyBoardType(int keyBoardType) {
        switch (keyBoardType) {
            //主要看这个
            case KEYBOARDTYPE_Num:
                //数字键盘
                if (keyboardNum == null)
                    keyboardNum = new Keyboard(getContext(), R.xml.common_keyboard_number);
//                    keyboardNum = new Keyboard(getContext(), R.xml.keyboard_number_and_symbol);
                //将布局文件设置给了keyBoardview
                setKeyboard(keyboardNum);
                break;
                //主要
            case KEYBOARDTYPE_ABC:
                //字母键盘
                if (keyboardABC == null)
                    keyboardABC = new Keyboard(getContext(), R.xml.common_keyboard_abc);
                setKeyboard(keyboardABC);
                break;
//            case KEYBOARDTYPE_Num_Pwd:
//                if (keyboardNumPwd == null)
//                    keyboardNumPwd = new Keyboard(getContext(), R.xml.keyboard_number);
//                randomKey(keyboardNumPwd);
//                setKeyboard(keyboardNumPwd);
//                break;
            case KEYBOARDTYPE_Symbol:
                //标点符号键盘
                if (keyboardSymbol == null)
                    keyboardSymbol = new Keyboard(getContext(), R.xml.common_keyboard_symbol);
                setKeyboard(keyboardSymbol);
                break;
            case KEYBOARDTYPE_Only_Num_Pwd:
                if (keyboardOnlyNumPwd == null)
                    keyboardOnlyNumPwd = new Keyboard(getContext(), R.xml.common_keyboard_only_number);
//                randomKey(keyboardOnlyNumPwd);
                setKeyboard(keyboardOnlyNumPwd);
                break;
            case KEYBOARDTYPE_Only_Phone_Num:
                if (keyPhone == null){
                    keyPhone = new Keyboard(getContext(),R.xml.common_keyboard_phone_number);
                    setKeyboard(keyPhone);
                }
        }
    }

    @Override
    protected void onAttachedToWindow() {
        super.onAttachedToWindow();
//        EventBus.getDefault().register(this);
    }

    @Override
    public void onDetachedFromWindow() {
        super.onDetachedFromWindow();
//        EventBus.getDefault().unregister(this);
    }

    private OnKeyboardActionListener mOnKeyboardActionListener = new OnKeyboardActionListener() {

        @Override
        public void onPress(int primaryCode) {
        }

        @Override
        public void onRelease(int primaryCode) {

        }

        @Override
        public void onKey(int primaryCode, int[] keyCodes) {
            Editable editable = mEditText.getText();
            int start = mEditText.getSelectionStart();
            switch (primaryCode) {
                case Keyboard.KEYCODE_DELETE://回退
                    if (editable != null && editable.length() > 0) {
                        if (start > 0) {
                            editable.delete(start - 1, start);
                        }
                    }
                    break;
                case Keyboard.KEYCODE_SHIFT://大小写切换，这是系统的类型
                    changeKey();
                    setKeyBoardType(KEYBOARDTYPE_ABC);
                    break;
                case Keyboard.KEYCODE_CANCEL:// 隐藏
                case Keyboard.KEYCODE_DONE:// 确认
                    mWindow.dismiss();
                    break;
                case 123123://切换数字键盘
//                    if (isPwd) {
//                        setKeyBoardType(KEYBOARDTYPE_Num_Pwd);
//                    } else {
                        setKeyBoardType(KEYBOARDTYPE_Num);
//                    }
                    break;
                case 456456://切换字母键盘
                    if (isSupper)//如果当前为大写键盘，改为小写
                        changeKey();
                    setKeyBoardType(KEYBOARDTYPE_ABC);
                    break;
                case 789789://切换符号键盘
                    setKeyBoardType(KEYBOARDTYPE_Symbol);
                    break;
                case 666666://人名分隔符·
                    editable.insert(start, "·");
                    break;
                case 999999://电话翻译的拨号
                    EventBusUtils.INSTANCE.postEvent(new CallPhoneEvent(editable, mEditText.getText().toString()));
//                    EventBus.getDefault().post(new CallPhoneEvent(mEditText.getText().toString()));
                    break;
                default://字符输入
                    editable.insert(start, Character.toString((char) primaryCode));
            }
        }

        @Override
        public void onText(CharSequence text) {

        }

        @Override
        public void swipeLeft() {

        }

        @Override
        public void swipeRight() {

        }

        @Override
        public void swipeDown() {

        }

        @Override
        public void swipeUp() {

        }
    };

    private OnCallBtnClick  onCallBtnClick;
    public void setOnCallBtnClick(OnCallBtnClick onCallBtnClick){
        this.onCallBtnClick=onCallBtnClick;
    }

    public interface OnCallBtnClick{
        void onCall(String s);
    }

    /**
     * 键盘大小写切换
     */
    private void changeKey() {
        List<Keyboard.Key> keyList = keyboardABC.getKeys();
        if (isSupper) {// 大写切小写
            for (Keyboard.Key key : keyList) {
                if (key.codes[0] == -1) {
                    key.icon = getContext().getDrawable(R.mipmap.keyboard_icon_lowercase);
                }

                if (key.label != null && strLetter.contains(key.label.toString().toLowerCase())) {
                    key.label = key.label.toString().toLowerCase();
                    key.codes[0] = key.codes[0] + 32;
                }
            }
        } else {// 小写切大写
            for (Keyboard.Key key : keyList) {
                if (key.codes[0] == -1) {
                    key.icon = getContext().getDrawable(R.mipmap.keyboard_icon_capitalized);
                }

                if (key.label != null && strLetter.contains(key.label.toString().toLowerCase())) {
                    key.label = key.label.toString().toUpperCase();
                    key.codes[0] = key.codes[0] - 32;
                }
            }
        }
        isSupper = !isSupper;
    }

    /**
     * 数字键盘随机
     * code 48-57 (0-9)
     */
    public void randomKey(Keyboard pLatinKeyboard) {
        int[] ayRandomKey = {0, 1, 2, 3, 4, 5, 6, 7, 8, 9};
        Random random = new Random();
        for (int i = 0; i < ayRandomKey.length; i++) {
            int a = random.nextInt(ayRandomKey.length);
            int temp = ayRandomKey[i];
            ayRandomKey[i] = ayRandomKey[a];
            ayRandomKey[a] = temp;
        }
        List<Keyboard.Key> pKeyLis = pLatinKeyboard.getKeys();
        int index = 0;
        for (int i = 0; i < pKeyLis.size(); i++) {
            int code = pKeyLis.get(i).codes[0];
            if (code >= 48 && code <= 57) {
                pKeyLis.get(i).label = ayRandomKey[index] + "";
                pKeyLis.get(i).codes[0] = 48 + ayRandomKey[index];
                index++;
            }
        }
    }

    @Override
    public void onDraw(Canvas canvas) {
        super.onDraw(canvas);
        // 获取键盘布局的键
        if (keyBoardType == KEYBOARDTYPE_Only_Phone_Num) {
            List<Keyboard.Key> keys = keyPhone.getKeys();
            for (Keyboard.Key key : keys) {
                // 根据键的标签或代码找到需要设置背景的特定键
                if (key.codes[0] == 999999) {
                    // 设置特定键的背景
                    var drawable= getContext().getResources().getDrawable(R.drawable.bg_btn_phone);
                    drawable.setBounds(key.x, key.y, key.x + key.width, key.y + key.height);
                    drawable.draw(canvas);
                    int drawableX = key.x + (key.width - key.icon.getIntrinsicWidth()) / 2;
                    int drawableY = key.y + (key.height - key.icon.getIntrinsicHeight()) / 2;
                    key.icon.setBounds(drawableX, drawableY, drawableX + key.icon
                            .getIntrinsicWidth(), drawableY + key.icon.getIntrinsicHeight());
                    key.icon.draw(canvas);
                    break;
                }
            }
        }

//        if (keyBoardType == KEYBOARDTYPE_Only_Num_Pwd) {//纯数字键盘，删除按钮特殊绘制
//            List<Keyboard.Key> keys = getKeyboard().getKeys();
//            for (Keyboard.Key key : keys) {
//                if (key.codes[0] == -5) {//删除按钮
//                    Drawable dr = getContext().getResources().getDrawable(R.drawable
//                            .keyboard_keybg_gray);
//                    dr.setBounds(key.x, key.y, key.x + key.width, key.y + key.height);
//                    dr.draw(canvas);
//                    int drawableX = key.x + (key.width - key.icon.getIntrinsicWidth()) / 2;
//                    int drawableY = key.y + (key.height - key.icon.getIntrinsicHeight()) / 2;
//                    key.icon.setBounds(drawableX, drawableY, drawableX + key.icon
//                            .getIntrinsicWidth(), drawableY + key.icon.getIntrinsicHeight());
//                    key.icon.draw(canvas);
//                }
//            }
//        }
    }
}
