package com.timekettle.upup.comm.constant

/**
 * <AUTHOR>
 * @date 2022/11/3
 * @desc
 */
object CommonRegex {

    val regexIllegalEmailInput = "[^0-9a-zA-Z.@\\-_]".toRegex() // 反向匹配

    val regexIllegalPasswordInput = "[^0-9a-zA-Z.\\-_@#&*]".toRegex() // 反向匹配
    // https://blog.51cto.com/u_15334563/3473108
    val regexStrongPassword = "^(?=.*?[0-9])(?=.*?[a-zA-Z])[0-9A-Za-z.\\-_@#&*]+$".toRegex() // 至少1个英文字母，至少一个数字
}