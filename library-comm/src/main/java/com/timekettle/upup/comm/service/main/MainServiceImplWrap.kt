package com.timekettle.upup.comm.service.main

import com.alibaba.android.arouter.facade.annotation.Autowired
import com.alibaba.android.arouter.launcher.ARouter
import com.timekettle.upup.comm.constant.RouteUrl
import com.timekettle.upup.comm.model.DfuNotifyMsg


object MainServiceImplWrap {

    @Autowired(name = RouteUrl.Main.MainService) //  发现LoginService服务
    lateinit var service: MainService

    init {
        ARouter.getInstance().inject(this)
    }

    fun getPushMsgs(): MutableList<DfuNotifyMsg> {
        return service.getPushMsgs()
    }

}