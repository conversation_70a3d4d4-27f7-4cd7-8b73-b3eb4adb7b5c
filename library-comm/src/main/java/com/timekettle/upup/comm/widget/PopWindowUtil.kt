package com.timekettle.upup.comm.widget

import android.content.Context
import android.view.View
import android.widget.PopupWindow
import android.widget.RelativeLayout
import android.widget.TextView
import com.timekettle.upup.comm.R

/**
 * <AUTHOR>
 * @date 2022/11/15 14:15
 * @email <EMAIL>
 * @desc 顶部的PopWindow
 */
object PopWindowUtil {
    /**
     * 构建顶部通知
     * @param title String
     * @param content String
     */
    fun buildTopNotification(context: Context, title: String, content: String):PopupWindow {
        val customView: View =
            View.inflate(context, R.layout.comm_popup_top_msg, null)
        customView.findViewById<TextView>(R.id.tvTitle).text = title
        customView.findViewById<TextView>(R.id.tvContent).text = content
        val popupWindow = PopupWindow(
            customView, RelativeLayout.LayoutParams.MATCH_PARENT,
            RelativeLayout.LayoutParams.WRAP_CONTENT
        )
        popupWindow.isFocusable = true
        popupWindow.isOutsideTouchable = false
        popupWindow.animationStyle = R.style.popwindow_top_anim_style
        return popupWindow
    }
}