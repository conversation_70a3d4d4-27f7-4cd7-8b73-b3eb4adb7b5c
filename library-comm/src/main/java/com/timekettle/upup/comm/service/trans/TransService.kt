package com.timekettle.upup.comm.service.trans

import com.alibaba.android.arouter.facade.template.IProvider
import com.timekettle.upup.comm.bean.MeetEngineHost
import com.timekettle.upup.comm.dao.CallRecordDao

/**
 **  翻译（Trans）模块提供的服务
 **
 */
interface TransService : IProvider {

    fun getDefaultSelfLan(): String // 获取自己的默认语言
    fun getDefaultOtherLan(): String  // 获取对方的默认语言
    fun initService()
    fun getPhoneModelLang(): String
    fun initTransConfig() // 初始化翻译模式的参数
    fun setAccount(account: String)
    fun getAccount(): String
    fun setMeetingId(id: String)
    fun getMeetingId(): String
    fun setPassword(password: String)
    fun getPassword(): String
    fun getStyledTextByCode(code: String): String  // 根据code  zh<->en  获取显示的名字
    fun cleanAllTransHistory() // 删除所有的历史记录
    fun getCallRecordDao():CallRecordDao // 提供dao方法
    fun fetchHosts()
    fun fetchMeetHost(callback: (code: Int, hosts: ArrayList<MeetEngineHost>) -> Unit)
    fun closeAllModeOffline()
    fun getOnNFCReceive(): Boolean
    fun setOnNFCReceive(value: Boolean)
    fun isInMeeting(): Boolean
    fun setInMeeting(value: Boolean)
    fun getCallType(): Int
    fun setCallType(type: Int)

    // 根据App设置的BaseUrl，去设置语音识别的BaseUrl
    fun getSpeechBaseUrl(): String

}