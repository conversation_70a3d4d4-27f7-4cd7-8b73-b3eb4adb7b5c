package com.timekettle.upup.comm.constant

/**
 * 请求公共地址
 *
 * <AUTHOR>
 * @since 2022/04/15
 */
object NetUrl {
    // BASE_URL一定要以/结尾，否则会崩溃
    const val DEV_URL = "http://192.168.2.110:30013/"        // 联调测试地址
    const val TEST_URL_X1_NEW = "https://test-api.timekettle.net/"      // X1的测试地址
    const val UAT_URL = "http://47.245.55.239:30013/"      // UAT环境地址
//    const val RELEASE_URL = "https://internal-apis-and-pages2.timekettle.co:30013/"  // 正式服地址
    const val RELEASE_URL = "https://api-glb.timekettle.co/"  // 正式服地址
    const val MOCK_URL = "http://yapi.smart-xwork.cn/mock/151264/"      // Mock测试地址

    const val RELEASE_SA_SERVER_URL =  "https://shenceapi.timekettle.co/sa?project=production" // 神策数据接收地址正式
    const val TEST_SA_SERVER_URL =  "https://shenceapi.timekettle.co/sa?project=default" // 神策数据接收地址测试
    const val CUSTOM_TRANSLATE_URL = "http://**********:6011/" // 自定义翻译服务器地址

    const val agreementUrlZh: String = "https://cdn.timekettle.co/agreement.html"
    const val privacyUrlZh: String = "https://cdn.timekettle.co/privacy.html"
    const val agreementUrlEn: String = "https://cdn.timekettle.co/agreement_en.html"
    const val privacyUrlEn: String = "https://cdn.timekettle.co/privacy_en.html"
//    const val SipIP: String = "**************:5166"

    const val BUSINESS_DEV_URL = "http://*************:24724"
    const val CAPTAIN_DEV_URL = "http://*************:9171"

    const val BUSINESS_SINGAPORE_DEV_URL = "http://*************:24724"
    const val CAPTAIN_SINGAPORE_DEV_URL = "http://*************:9171"

    const val BUSINESS_TEST_URL = "https://test-api.timekettle.net"
    const val BUSINESS_UAT_URL = "https://uat-api.timekettle.net"
    const val BUSINESS_AMERICA_RELEASE_URL = "https://api-us-east.timekettle.co"
    const val BUSINESS_RELEASE_URL = "https://api-glb.timekettle.co"

    /**
     * 证书服签发服务器，Local为本地(工厂)，External为外网
     */
    const val LOCAL_COMPANY_AUTH_DOMAIN = "*************"
    const val LOCAL_FACTORY_AUTH_DOMAIN = "ca-local.timekettle.co"

    const val LOCAL_COMPANY_AUTH_ADDRESS = "http://*************/certificate/issue"
    const val LOCAL_FACTORY_AUTH_ADDRESS = "http://ca-local.timekettle.co/certificate/issue"
    const val EXTERNAL_AUTH_ADDRESS = "https://ca-client.timekettle.co/certificate/issue"

    /**
     * 获取 token 接口地址
     */
    const val TOKEN_ADDRESS_RELEASE = "https://ca.timekettle.co/certificate/token"
    const val TOKEN_ADDRESS_TEST = "https://test-ca.timekettle.net/certificate/token"

}