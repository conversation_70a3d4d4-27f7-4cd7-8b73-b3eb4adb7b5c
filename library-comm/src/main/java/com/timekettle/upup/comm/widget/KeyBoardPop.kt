package com.timekettle.upup.comm.widget

import android.content.Context
import android.graphics.Color
import android.view.Gravity
import android.view.View
import android.view.animation.Animation
import razerdp.basepopup.BasePopupWindow
import razerdp.util.animation.AnimationHelper
import razerdp.util.animation.TranslationConfig

class KeyBoardPop(context: Context) : BasePopupWindow(context) {

    init {
        popupGravity=  Gravity.CENTER
        isOutSideTouchable = true
        setOutSideDismiss(true)
        setBackPressEnable(true)
        setBackgroundColor(Color.parseColor("#CC000000"))

    }

    override fun onCreateShowAnimation(): Animation {
        return AnimationHelper.asAnimation()
            .withTranslation(TranslationConfig.FROM_BOTTOM)
            .toShow();
    }

    override fun onCreateDismissAnimation(): Animation {
        return AnimationHelper.asAnimation()
            .withTranslation(TranslationConfig.TO_BOTTOM)
            .toDismiss();
    }
}