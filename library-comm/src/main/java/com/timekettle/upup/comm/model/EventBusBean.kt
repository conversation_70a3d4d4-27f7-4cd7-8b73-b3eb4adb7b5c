package com.timekettle.upup.comm.model

import android.text.Editable
import co.timekettle.btkit.bean.RawBlePeripheral
import com.jeremyliao.liveeventbus.core.LiveEvent
import com.timekettle.upup.base.utils.EventBusKey
import com.timekettle.upup.comm.bean.DownloadStatus
import com.timekettle.upup.comm.constant.LanDirection
import com.timekettle.upup.comm.constant.TmkProductType
import com.timekettle.upup.comm.constant.TranslateMode

/**
 *
 * @author: licoba
 * @date: 2022/7/11
 */

// 产品选择事件
data class ProductChooseEvent(
    val name: String = EventBusKey.PRODUCT_CHOOSE,
    val productType: TmkProductType
)

data class SppCountChangeEvent(
    val name: String = EventBusKey.SPP_CONN_COUNT_CHANGE,
    val connCount: Int = 0
)

// 语言切换事件
data class LanguageUpdateEvent(
    val direction: LanDirection,
    val fromCode: String,//原语言
    val toCode: String//目标语言
)

data class MeetingLanguageUpdateEvent(
    val code: String,//会议模式自己选择的语言
)

data class MeetingUserNameUpdateEvent(
    val name: String,//会议模式修改名字的事件
)

// 三方登录授权完成之后的Token获取成功事件
data class ThirdAuthSuccessEvent(
    val loginMethod: LoginMethod,
    val token: String
)


// 音频焦点获取/丢失事件
data class AudioFocusEvent(
    val focusChangeValue: Int = -1,
    val name: String = "未知"
)

// 主页的Tab切换事件
data class SwitchTabEvent(
    val tabIndex: Int = 0,
)


// 退出登录事件
data class LogoutEvent(
    val isLogOut: Boolean = true
)


// 显示设备列表弹窗事件
data class ShowDeviceDialogListEvent(
    val model: TranslateMode = TranslateMode.UNKNOWN,
    val connectDoneToDo: (() -> Unit)? = null,
    val connectOneToDo: (() -> Unit)? = null,
    val show: Boolean = true
)

//nfc发送消息时进入会议
data class MeetingEvent(
    val meetingId: String,
    val password: String
)

data class CLoseSendEvent(
    val data: Int
)

data class CloseTranslateEvent(
    val msg: String
)

data class DongleConnectEvent(
    val isConnect: Boolean
)

// 004的设备昵称更新事件
data class DeviceNicknameUpdate(
    val device: RawBlePeripheral?,
    val deviceNickName: String = ""
)


// OTA 升级已经准备好
data class OtaReadyEvent(
    val dfuMsg: DfuNotifyMsg
)

//电话拨号按键事件
data class CallPhoneEvent(
    var editable: Editable,
    var phoneNum: String
)

//sip账号注册事件
//data class SipRegisterEvent(
//    var s: String = ""
//)

// 多文件下载事件
data class DownloadMultipleFileEvent(
    val offlinePkgId: Int,
    val downloadedBytes: Long,
    val totalBytes: Long,
    val status: DownloadStatus,
    val uncompressFilePathMap: Map<String, String>, // zip路径，解压路径
    var code: String
) : LiveEvent


// 解压、鉴权成功/失败事件
data class UnZipTryAuthResultEvent(
    val isSuccess: Boolean,  // 是否鉴权成功了
    val code: String,  // 语言对
    val msg: String = ""
) : LiveEvent


// 霍尔开关事件/耳机出入仓事件
data class HallInOutEvent(
    var isLeft: Boolean = false,  // 是否是左边，否则就是右边
    var isIn: Boolean = false   // 是否是入仓，否则就是出仓
)


// 抽屉出入仓事件
data class DrawerInOutEvent(
    var isIn: Boolean = false   // 是否是入仓，否则就是出仓
)



// 耳机升级成功事件
data class HeadsetUpgradeSuccess(
    var isSuccess: Boolean
)


// 检测耳机升级事件
data class CheckHeadsetUpdateEvent(
    var s: String = ""
)

// 认证成功事件
data class CertificateUpdateEvent(
    var s: String = ""
)


data class ScreenOnOffEvent(
    var isScreenOff: Boolean = false
)

data class PhoneStateEvent(
    var state: Int
)