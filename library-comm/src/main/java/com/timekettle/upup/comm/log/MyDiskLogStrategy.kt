package com.timekettle.upup.comm.log

import android.os.Handler
import android.os.Looper
import android.os.Message
import com.orhanobut.logger.LogStrategy
import java.io.File
import java.io.FileWriter
import java.io.IOException

/**
 * 日志打印处理策略
 *
 * Writes all logs to the disk with CSV format.
 */
class MyDiskLogStrategy(handler: <PERSON><PERSON>) : LogStrategy {

    private val handler: Handler

    init {
        this.handler = handler
    }

    override fun log(level: Int, tag: String?, message: String) {
        // do nothing on the calling thread, simply pass the tag/msg to the background thread
        val newTag = tag?.replace("Tmk-", "").toString().padEnd(20)
        handler.sendMessage(
            handler.obtainMessage(level, "${getLevelText(level)}: ${newTag}| $message")
        )
    }

    private fun getLevelText(level: Int): String {
        /**
         *
        public static final int VERBOSE = 2;
        public static final int DEBUG = 3;
        public static final int INFO = 4;
        public static final int WARN = 5;
        public static final int ERROR = 6;
        public static final int ASSERT = 7;
         */

        return when (level) {
            2 -> "V"
            3 -> "D"
            4 -> "I"
            5 -> "W"
            6 -> "E"
            7 -> "!"
            else -> "D"
        }
    }

}