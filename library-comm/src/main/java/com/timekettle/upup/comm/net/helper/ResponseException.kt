package com.timekettle.upup.comm.net.helper

import java.io.IOException

/**
 * 自定义响应异常的抽象类型
 *
 */
interface IResponseException

/**
 * 请求响应异常，主要为各种code码专门定义的异常
 *
 * @param type IResponseCode 异常类型枚举，用于标记该异常的类型
 * @param msg String 异常信息
 *
 */
class ResponseException(val type: IResponseCode, val msg: String) : Exception(), IResponseException

/**
 * {"success":false,"code":302,"desc":"Param Error.","reasonCode":2003,"reason":"Pass Error.","data":1,"total":null}
 * 有些请求的异常抛出，需要带上data，比如多次登录密码错误，data表示的就是剩余次数
 * 这里必须是IOException，否则会崩溃 https://stackoverflow.com/questions/53613615/fatal-exception-okhttp-dispatcher
 */
data class ReasonException(val reasonCode: Int, val reason: String, val data: Any? = null,val code:Int? = -1) : IOException(),
    IResponseException

/**
 * 空异常，表示该异常已经被处理过了，不需要再做额外处理了
 *
 */
class ResponseEmptyException : Exception(), IResponseException