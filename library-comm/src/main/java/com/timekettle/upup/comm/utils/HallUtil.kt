package com.timekettle.upup.comm.utils

import com.timekettle.upup.base.utils.logD
import com.timekettle.upup.base.utils.logV
import java.io.BufferedReader
import java.io.FileReader
import java.io.IOException

/**
 * 霍尔开关的工具类
 * 1：入仓   0：出仓
 *
 */
object HallUtil {

    // 是否是左耳机右耳机都在仓内
    fun isLeftRightIn(): Boolean {
        return isLeftIn() && isRightIn()
    }


    // 是否是左耳机和右耳机都出仓了
    fun isLeftRightOut(): Boolean {
        return !isLeftIn() && !isRightIn()
    }

    fun isLeftIn(): Boolean {
        val path = "/sys/devices/platform/bt_hp_ctrl/hall2_eint"
        return readFileNodeValue(path) == "1"
    }
    fun isLeftOut(): Boolean {
        val path = "/sys/devices/platform/bt_hp_ctrl/hall2_eint"
        return readFileNodeValue(path) == "0"
    }


    fun isRightIn(): Bo<PERSON>an {
        val path = "/sys/devices/platform/bt_hp_ctrl/hall1_eint"
        return readFileNodeValue(path) == "1"
    }

    fun isRightOut(): Boolean {
        val path = "/sys/devices/platform/bt_hp_ctrl/hall1_eint"
        return readFileNodeValue(path) == "0"
    }



    // 抽屉打开/闭合
    fun isBoxDrawerIn(): Boolean {
        val path = "/sys/devices/platform/bt_hp_ctrl/hall3_eint"
        return readFileNodeValue(path) == "1"
    }


    fun isBoxDrawerOut(): Boolean {
        val path = "/sys/devices/platform/bt_hp_ctrl/hall3_eint"
        return readFileNodeValue(path) == "0"
    }


    // 读取文件节点的值
    private fun readFileNodeValue(filePath: String): String {
        var value = ""
        try {
            val file = FileReader(filePath)
            val reader = BufferedReader(file)
            value = reader.readLine()
            reader.close()
        } catch (e: IOException) {
            logV("读取文件节点失败！${e.message}")
        }
        return value
    }

}