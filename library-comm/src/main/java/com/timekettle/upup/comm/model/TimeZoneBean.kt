package com.timekettle.upup.comm.model

import com.google.gson.annotations.SerializedName

/**
 *{
 *     "abbreviation":"HKT",
 *     "client_ip":"***************",
 *     "datetime":"2023-12-11T17:55:30.052677+08:00",
 *     "day_of_week":1,
 *     "day_of_year":345,
 *     "dst":false,
 *     "dst_from":null,
 *     "dst_offset":0,
 *     "dst_until":null,
 *     "raw_offset":28800,
 *     "timezone":"Asia/Hong_Kong",
 *     "unixtime":1702288530,
 *     "utc_datetime":"2023-12-11T09:55:30.052677+00:00",
 *     "utc_offset":"+08:00",
 *     "week_number":50
 * }
 */

data class TimeZoneBean(
    @SerializedName("abbreviation") var abbreviation: String? = null,
    @SerializedName("client_ip") var clientIp: String? = null,
    @SerializedName("datetime") var datetime: String? = null,
    @SerializedName("day_of_week") var dayOfWeek: Int? = null,
    @SerializedName("day_of_year") var dayOfYear: Int? = null,
    @SerializedName("dst") var dst: Boolean? = null,
    @SerializedName("dst_from") var dstFrom: String? = null,
    @SerializedName("dst_offset") var dstOffset: Int? = null,
    @SerializedName("dst_until") var dstUntil: String? = null,
    @SerializedName("raw_offset") var rawOffset: Int? = null,
    @SerializedName("timezone") var timezone: String? = null,
    @SerializedName("unixtime") var unixtime: Int? = null,
    @SerializedName("utc_datetime") var utcDatetime: String? = null,
    @SerializedName("utc_offset") var utcOffset: String? = null,
    @SerializedName("week_number") var weekNumber: Int? = null
)