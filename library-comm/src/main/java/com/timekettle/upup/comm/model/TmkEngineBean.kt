package com.timekettle.upup.comm.model


/**
 *
 * {
 * "ip":"************",
 * "port":5050,
 * "type":"TCP",
 * "code":"111_MD",
 * "enabled":true,
 * "region":"7"
 * }
 */
data class TmkEngineBean(
    val ip: String,
    val port: Int,
    val type: String,
    val code: String,
    val enabled: Boolean,
    val region: String
) {
    fun toRadioString():String{
        return "${code.replace("111_","")}-$ip"
    }


}
