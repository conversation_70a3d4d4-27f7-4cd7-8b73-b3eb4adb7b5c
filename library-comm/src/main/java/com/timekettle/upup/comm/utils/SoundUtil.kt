package com.timekettle.upup.comm.utils

import android.media.AudioAttributes
import android.media.AudioFocusRequest
import android.media.AudioManager
import android.media.AudioManager.AUDIOFOCUS_GAIN
import android.os.Build
import android.util.Log
import androidx.appcompat.app.AppCompatActivity
import com.timekettle.upup.base.BaseApp
import com.timekettle.upup.base.utils.EventBusUtils
import com.timekettle.upup.base.utils.logD
import com.timekettle.upup.comm.model.AudioFocusEvent

/**
 * <AUTHOR>
 * @date 2022/11/21 17:42
 * @email <EMAIL>
 * @desc
 */
object SoundUtil {

    fun requestAudioFocus() {
        val audioManager =
            BaseApp.context.getSystemService(AppCompatActivity.AUDIO_SERVICE) as AudioManager
        // 长期抢占焦点，避免引起音乐自动播放的bug
        val mPlaybackAttributes = AudioAttributes.Builder()
            .setUsage(AudioAttributes.USAGE_GAME)
            .setContentType(AudioAttributes.CONTENT_TYPE_MUSIC)
            .build();
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
            val mFocusRequest = AudioFocusRequest.Builder(AUDIOFOCUS_GAIN)
                .setAudioAttributes(mPlaybackAttributes)
                .setAcceptsDelayedFocusGain(true)
                .setOnAudioFocusChangeListener(afChangeListener)
                .build();
            audioManager.requestAudioFocus(mFocusRequest)
        } else {
            audioManager.requestAudioFocus(
                afChangeListener,
                AudioManager.STREAM_MUSIC,
                AudioManager.AUDIOFOCUS_GAIN
            )
        }
    }


    private val afChangeListener = AudioManager.OnAudioFocusChangeListener { focusChange ->
        when (focusChange) {
            AudioManager.AUDIOFOCUS_LOSS -> {
                // Permanent loss of audio focus
                // Pause playback immediately
                logD("音频焦点永久的丢失：AUDIOFOCUS_LOSS")
                // Wait 30 seconds before stopping playback
//                handler.postDelayed(delayedStopRunnable, TimeUnit.SECONDS.toMillis(30))
//                EventBusUtils.postEvent(AudioFocusEvent(focusChange, "音频焦点永久的丢失"))
            }
            AudioManager.AUDIOFOCUS_LOSS_TRANSIENT -> {
                logD("音频焦点短暂的丢失：AUDIOFOCUS_LOSS_TRANSIENT")
                EventBusUtils.postEvent(AudioFocusEvent(focusChange, "音频焦点短暂的丢失"))
                // Pause playback
            }
            AudioManager.AUDIOFOCUS_LOSS_TRANSIENT_CAN_DUCK -> {
                logD("音频焦点短暂的丢失：AUDIOFOCUS_LOSS_TRANSIENT_CAN_DUCK")
                EventBusUtils.postEvent(AudioFocusEvent(focusChange, "音频焦点短暂的丢失"))
                // Lower the volume, keep playing
            }
            AudioManager.AUDIOFOCUS_GAIN -> {
                // Your app has been granted audio focus again
                // Raise volume to normal, restart playback if necessary
                logD("音频焦点获取到了：AUDIOFOCUS_GAIN")

            }
        }

    }
}