package com.timekettle.upup.comm.model

import android.os.Parcelable
import com.timekettle.upup.comm.constant.TmkProductType
import kotlinx.parcelize.Parcelize
import org.intellij.lang.annotations.Language

/**
 * <AUTHOR>
 * @date 2022/11/5 11:26
 * @email <EMAIL>
 * @desc 离线资格
 */

@Parcelize
data class OfflineEligibility(
    // key:产品的code，data：所有的离线包列表
    var allData: MutableMap<String, MutableList<String>> = mutableMapOf(),
) : Parcelable {}