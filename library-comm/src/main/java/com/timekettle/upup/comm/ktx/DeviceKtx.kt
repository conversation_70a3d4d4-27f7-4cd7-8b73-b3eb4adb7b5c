package com.timekettle.upup.comm.ktx

import co.timekettle.btkit.bean.RawBlePeripheral
import co.timekettle.btkit.bean.WT2BlePeripheral
import com.timekettle.upup.comm.model.X1Device
import com.timekettle.upup.comm.model.X1Role

fun MutableList<X1Device>.findLeft(): X1Device? {
    return find { it.x1Role == X1Role.Left }
}


fun MutableList<X1Device>.findRight(): X1Device? {
    return find { it.x1Role == X1Role.Right }
}

fun X1Device.isLeft(): <PERSON><PERSON>an {
    return x1Role == X1Role.Left
}

fun X1Device.isRight(): Boolean {
    return x1Role == X1Role.Right
}

fun RawBlePeripheral.isBleLeft(): Boolean {
    if (this is WT2BlePeripheral) {
        return this.role == RawBlePeripheral.Role.Left
    }
    return false
}

fun RawBlePeripheral.isBleRight(): Boolean {
    if (this is WT2BlePeripheral) {
        return this.role == RawBlePeripheral.Role.Right
    }
    return false
}