package com.timekettle.upup.comm.widget

import android.content.Context
import android.graphics.Color
import android.graphics.drawable.AnimationDrawable
import android.util.AttributeSet
import android.view.LayoutInflater
import android.view.Surface
import android.view.WindowManager
import androidx.appcompat.app.AppCompatActivity
import androidx.constraintlayout.widget.ConstraintLayout
import com.blankj.utilcode.util.ConvertUtils
import com.timekettle.upup.base.BaseApp
import com.timekettle.upup.base.ktx.getAppViewModel
import com.timekettle.upup.base.ktx.gone
import com.timekettle.upup.base.ktx.invisible
import com.timekettle.upup.base.ktx.visible
import com.timekettle.upup.base.utils.logD
import com.timekettle.upup.base.utils.logV
import com.timekettle.upup.comm.R
import com.timekettle.upup.comm.databinding.CommTopDeviceBinding
import com.timekettle.upup.comm.databinding.LayoutChargingBinding
import com.timekettle.upup.comm.ktx.findLeft
import com.timekettle.upup.comm.ktx.findRight
import com.timekettle.upup.comm.ktx.isLeft
import com.timekettle.upup.comm.ktx.isRight
import com.timekettle.upup.comm.model.X1Device
import com.timekettle.upup.comm.model.X1Status
import com.timekettle.upup.comm.viewmodel.VMTopDevice
import org.libpag.PAGFile
import org.libpag.PAGView

/**
 *
 * @Author:         licoba
 * @CreateDate:     2023/10/17
 * @Description:    充电动画的View，全局悬浮窗
 */


class ChargingFloatView @JvmOverloads constructor(
    context: Context,  // context
    attrs: AttributeSet? = null, defStyleAttr: Int = 0
) : ConstraintLayout(context, attrs, defStyleAttr) {
    private val windowManager: WindowManager =
        context.getSystemService(Context.WINDOW_SERVICE) as WindowManager
    private var binding: LayoutChargingBinding
    private var isLandscape: Boolean = true // 默认应该是横屏的

    private val viewModel: VMTopDevice by lazy { getAppViewModel() }
    private lateinit var pagChargingLeft: PAGView
    private lateinit var pagChargingRight: PAGView

    var percent: Int = 50 // 电量百分比
        set(value) {
            field = value
            refreshBoxElectric()
        }

    init {
        val view = LayoutInflater.from(context).inflate(R.layout.layout_charging, this)
        binding = LayoutChargingBinding.bind(view)
        // 获取屏幕角度
        val rotation = windowManager.defaultDisplay.rotation
        logV("Rotation角度:$rotation") // 注意这里手机和X1是相反的，因为手机默认是竖屏，X1默认是横屏
        when (rotation) {
            Surface.ROTATION_0, Surface.ROTATION_180 -> { // 屏幕方向为竖屏
                isLandscape = true
            }
            Surface.ROTATION_90, Surface.ROTATION_270 -> { // 屏幕方向为横屏
                isLandscape = false
            }
        }
        changeRotation()
        showFrameAnim()
        addChargingPag()
        refreshBoxElectric()

    }

    private fun addChargingPag() {
        pagChargingLeft = PAGView(BaseApp.context).apply {
            layoutParams = ConstraintLayout.LayoutParams(
                ConvertUtils.dp2px(47f), ConvertUtils.dp2px(102f)
            )
            composition = PAGFile.Load(BaseApp.context.assets, "ani_charging_earbus_l.pag")
            setRepeatCount(0)
        }
        binding.ivLeftDevice.addView(pagChargingLeft)
        pagChargingLeft.play()
        pagChargingRight = PAGView(BaseApp.context).apply {
            layoutParams = ConstraintLayout.LayoutParams(
                ConvertUtils.dp2px(47f), ConvertUtils.dp2px(102f)
            )
            composition = PAGFile.Load(BaseApp.context.assets, "ani_charging_earbus_r.pag")
            setRepeatCount(0)
        }
        binding.ivRightDevice.addView(pagChargingRight)
        pagChargingRight.play()
    }

    //  调整视图的方向
    private fun changeRotation() {
        if (isLandscape) binding.llTotalContent.rotation = 270f
        else binding.llTotalContent.rotation = 0f
    }


    private fun initData() {
        if (context is AppCompatActivity) {
            viewModel.liveX1Devices.observe(context as AppCompatActivity, ::processDevices)
        }
    }


    //强制更新UI，拿到最新的信息
    private fun refreshBoxElectric() {
        binding.batteryFg.setImageResource(getBoxChargingFg(percent))
        binding.tvBoxBattery.text = percent.toString()
    }

    override fun onAttachedToWindow() {
        super.onAttachedToWindow()
        initData()
    }


    // 处理新的数据
    private fun processDevices(devices: MutableList<X1Device>) {
        val leftDevice = devices.findLeft()
        val rightDevice = devices.findRight()
        if (leftDevice != null) {
            binding.updateX1Status(leftDevice)
            updateHeadsetElectric(leftDevice)
        }
        if (rightDevice != null) {
            binding.updateX1Status(rightDevice)
            updateHeadsetElectric(rightDevice)
        }
    }


    private fun LayoutChargingBinding.updateX1Status(device: X1Device) {
        when (device.state) {
            X1Status.Broken -> {
                if (device.isLeft()) {
                    llLeftDeviceBroken.visible()
                    llLeftDevice.invisible()
                } else {
                    llRightDeviceBroken.visible()
                    llRightDevice.invisible()
                }
            }

            else -> {
                if (device.isLeft()) {
                    llLeftDeviceBroken.invisible()
                    llLeftDevice.visible()
                } else {
                    llRightDeviceBroken.invisible()
                    llRightDevice.visible()
                }
            }
        }
    }


    private fun updateHeadsetElectric(device: X1Device) {
        // 假装100的电
        var electric = if (device.state == X1Status.ChargingInBox) {
            device.serialDevice?.electric
        } else {
            device.bleDevice?.electric
        }
        if (electric == 0 || electric == null) electric = 100
        logV("耳机电量：$electric isLeft:${device.isLeft()}")
        if (device.isLeft()) {
            binding.vBatteryValueLeft.layoutParams.apply {
                height = ConvertUtils.dp2px(49 * electric / 100f)
            }
            binding.tvBatteryLeft.text = "$electric%"
            if (electric <= 20) { // 低电
                binding.tvBatteryLeft.setTextColor(Color.parseColor("#FC6D74"))
            } else {
                if (device.state == X1Status.ChargingInBox) { // 在盒子里面是绿色
                    pagChargingLeft.visible()
                    binding.ivChargingLeft.visible()
                    binding.tvBatteryLeft.setTextColor(Color.parseColor("#FF15FF4F"))
                } else {
                    pagChargingLeft.gone()
                    binding.ivChargingLeft.gone()
                    binding.tvBatteryLeft.setTextColor(Color.parseColor("#FFFFFF"))
                }
            }
        } else if (device.isRight()) {
            binding.vBatteryValueRight.layoutParams.height =
                ConvertUtils.dp2px(49 * electric / 100f)
            binding.tvBatteryRight.text = "$electric%"
            if (electric <= 20) { // 低电
                binding.tvBatteryRight.setTextColor(Color.parseColor("#FC6D74"))
            } else {
                if (device.state == X1Status.ChargingInBox) { // 在盒子里面是绿色
                    pagChargingRight.visible()
                    binding.ivChargingRight.visible()
                    binding.tvBatteryRight.setTextColor(Color.parseColor("#FF15FF4F"))
                } else {
                    pagChargingRight.gone()
                    binding.ivChargingRight.gone()
                    binding.tvBatteryRight.setTextColor(Color.parseColor("#FFFFFF"))
                }
            }
        }

        // 充电时不显示电量数值
        if (device.state == X1Status.ChargingInBox) {
            if (device.isLeft()) {
                binding.tvBatteryLeft.text = ""
            } else {
                binding.tvBatteryRight.text = ""
            }
        }
    }


    override fun onDetachedFromWindow() {
        if (context is AppCompatActivity) {
            viewModel.liveX1Devices.removeObservers(context as AppCompatActivity)
        }
        super.onDetachedFromWindow()
    }


    private fun showFrameAnim() {
        val animationDrawable = AnimationDrawable()
        for (i in 30..179) {
            val frameName = "ani_charging_bg_light${String.format("%03d", i)}"
            val resourceId = resources.getIdentifier(frameName, "drawable", context.packageName)
            val frameDrawable = resources.getDrawable(resourceId, null)
            val duration = 30 // 设置每一帧的持续时间，单位为毫秒
            animationDrawable.addFrame(frameDrawable, duration)
        }
        val animationImageView = binding.ivFrame
        animationImageView.setImageDrawable(animationDrawable)
        animationDrawable.start()
    }

    private fun getBoxChargingFg(percent: Int): Int {

        // 根据值的不同设置不同的图片资源
        return when (percent) {
            0 -> R.mipmap.ani_charging_bg_percent_00
            1 -> R.mipmap.ani_charging_bg_percent_01
            2 -> R.mipmap.ani_charging_bg_percent_02
            3 -> R.mipmap.ani_charging_bg_percent_03
            4 -> R.mipmap.ani_charging_bg_percent_04
            5 -> R.mipmap.ani_charging_bg_percent_05
            6 -> R.mipmap.ani_charging_bg_percent_06
            7 -> R.mipmap.ani_charging_bg_percent_07
            8 -> R.mipmap.ani_charging_bg_percent_08
            9 -> R.mipmap.ani_charging_bg_percent_09
            10 -> R.mipmap.ani_charging_bg_percent_10
            11 -> R.mipmap.ani_charging_bg_percent_11
            12 -> R.mipmap.ani_charging_bg_percent_12
            13 -> R.mipmap.ani_charging_bg_percent_13
            14 -> R.mipmap.ani_charging_bg_percent_14
            15 -> R.mipmap.ani_charging_bg_percent_15
            16 -> R.mipmap.ani_charging_bg_percent_16
            17 -> R.mipmap.ani_charging_bg_percent_17
            18 -> R.mipmap.ani_charging_bg_percent_18
            19 -> R.mipmap.ani_charging_bg_percent_19
            20 -> R.mipmap.ani_charging_bg_percent_20
            21 -> R.mipmap.ani_charging_bg_percent_21
            22 -> R.mipmap.ani_charging_bg_percent_22
            23 -> R.mipmap.ani_charging_bg_percent_23
            24 -> R.mipmap.ani_charging_bg_percent_24
            25 -> R.mipmap.ani_charging_bg_percent_25
            26 -> R.mipmap.ani_charging_bg_percent_26
            27 -> R.mipmap.ani_charging_bg_percent_27
            28 -> R.mipmap.ani_charging_bg_percent_28
            29 -> R.mipmap.ani_charging_bg_percent_29
            30 -> R.mipmap.ani_charging_bg_percent_30
            31 -> R.mipmap.ani_charging_bg_percent_31
            32 -> R.mipmap.ani_charging_bg_percent_32
            33 -> R.mipmap.ani_charging_bg_percent_33
            34 -> R.mipmap.ani_charging_bg_percent_34
            35 -> R.mipmap.ani_charging_bg_percent_35
            36 -> R.mipmap.ani_charging_bg_percent_36
            37 -> R.mipmap.ani_charging_bg_percent_37
            38 -> R.mipmap.ani_charging_bg_percent_38
            39 -> R.mipmap.ani_charging_bg_percent_39
            40 -> R.mipmap.ani_charging_bg_percent_40
            41 -> R.mipmap.ani_charging_bg_percent_41
            42 -> R.mipmap.ani_charging_bg_percent_42
            43 -> R.mipmap.ani_charging_bg_percent_43
            44 -> R.mipmap.ani_charging_bg_percent_44
            45 -> R.mipmap.ani_charging_bg_percent_45
            46 -> R.mipmap.ani_charging_bg_percent_46
            47 -> R.mipmap.ani_charging_bg_percent_47
            48 -> R.mipmap.ani_charging_bg_percent_48
            49 -> R.mipmap.ani_charging_bg_percent_49
            50 -> R.mipmap.ani_charging_bg_percent_50
            51 -> R.mipmap.ani_charging_bg_percent_51
            52 -> R.mipmap.ani_charging_bg_percent_52
            53 -> R.mipmap.ani_charging_bg_percent_53
            54 -> R.mipmap.ani_charging_bg_percent_54
            55 -> R.mipmap.ani_charging_bg_percent_55
            56 -> R.mipmap.ani_charging_bg_percent_56
            57 -> R.mipmap.ani_charging_bg_percent_57
            58 -> R.mipmap.ani_charging_bg_percent_58
            59 -> R.mipmap.ani_charging_bg_percent_59
            60 -> R.mipmap.ani_charging_bg_percent_60
            61 -> R.mipmap.ani_charging_bg_percent_61
            62 -> R.mipmap.ani_charging_bg_percent_62
            63 -> R.mipmap.ani_charging_bg_percent_63
            64 -> R.mipmap.ani_charging_bg_percent_64
            65 -> R.mipmap.ani_charging_bg_percent_65
            66 -> R.mipmap.ani_charging_bg_percent_66
            67 -> R.mipmap.ani_charging_bg_percent_67
            68 -> R.mipmap.ani_charging_bg_percent_68
            69 -> R.mipmap.ani_charging_bg_percent_69
            70 -> R.mipmap.ani_charging_bg_percent_70
            71 -> R.mipmap.ani_charging_bg_percent_71
            72 -> R.mipmap.ani_charging_bg_percent_72
            73 -> R.mipmap.ani_charging_bg_percent_73
            74 -> R.mipmap.ani_charging_bg_percent_74
            75 -> R.mipmap.ani_charging_bg_percent_75
            76 -> R.mipmap.ani_charging_bg_percent_76
            77 -> R.mipmap.ani_charging_bg_percent_77
            78 -> R.mipmap.ani_charging_bg_percent_78
            79 -> R.mipmap.ani_charging_bg_percent_79
            80 -> R.mipmap.ani_charging_bg_percent_80
            81 -> R.mipmap.ani_charging_bg_percent_81
            82 -> R.mipmap.ani_charging_bg_percent_82
            83 -> R.mipmap.ani_charging_bg_percent_83
            84 -> R.mipmap.ani_charging_bg_percent_84
            85 -> R.mipmap.ani_charging_bg_percent_85
            86 -> R.mipmap.ani_charging_bg_percent_86
            87 -> R.mipmap.ani_charging_bg_percent_87
            88 -> R.mipmap.ani_charging_bg_percent_88
            89 -> R.mipmap.ani_charging_bg_percent_89
            90 -> R.mipmap.ani_charging_bg_percent_90
            91 -> R.mipmap.ani_charging_bg_percent_91
            92 -> R.mipmap.ani_charging_bg_percent_92
            93 -> R.mipmap.ani_charging_bg_percent_93
            94 -> R.mipmap.ani_charging_bg_percent_94
            95 -> R.mipmap.ani_charging_bg_percent_95
            96 -> R.mipmap.ani_charging_bg_percent_96
            97 -> R.mipmap.ani_charging_bg_percent_97
            98 -> R.mipmap.ani_charging_bg_percent_98
            99 -> R.mipmap.ani_charging_bg_percent_99
            100 -> R.mipmap.ani_charging_bg_percent_99
            else -> R.mipmap.ani_charging_bg_percent_99
        }
    }


}