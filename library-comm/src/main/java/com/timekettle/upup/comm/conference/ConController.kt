package com.timekettle.upup.comm.conference

import android.util.Log
import co.timekettle.sip.call.CallManager
import co.timekettle.sip.call.encryptMeet
import co.timekettle.sip.call.getMeetFlag
import co.timekettle.sip.call.getMeetId
import co.timekettle.sip.call.getUserPwd
import co.timekettle.sip.call.sendSip
import co.timekettle.sip.entity.IpEntity
import co.timekettle.sip.entity.MeetFlagEntity
import co.timekettle.sip.entity.MeetIdEntity
import co.timekettle.sip.entity.PwdEntity
import co.timekettle.sip.entity.ReqEntity
import co.timekettle.sip.entity.ResEntity
import com.timekettle.upup.base.BaseApp.Companion.context
import com.timekettle.upup.comm.model.AccountBean
import com.timekettle.upup.comm.service.home.HomeServiceImplWrap
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch
import java.net.ConnectException
import java.net.SocketTimeoutException

/**
 * 创建或加入会议步骤：
 * 1. 获取sip账号和密码（会返回相关参数，需要保存，拨号的时候要用到）
 * 2. 获取会议id（保存会议id）
 * 3. 查询会议是否有密码(保存标志位)
 * 4. 拨号（如果会议已加密，拨号传递的参数group后面需要拼接会议密码）
 */
object ConController {

    private const val TAG = "ConController"
//     const val IP = "**************:5166"
//    private const val IP = "************:5060"
    val callStateBackList = mutableListOf<(Int, Int, String, String, String, String) -> Unit>()
    val callMessageBackList = mutableListOf<(String) -> Unit>()
    val callRegBackList = mutableListOf<(Int, String) -> Unit>()
    val logBackList = mutableListOf<(Int, String) -> Unit>()

    /**
     * 初始化
     * type: 1: 正式环境 2: 测试环境 3: 开发环境
     */
    fun init(type: Int): String {
        val url = CallManager.init(type)
        setCallBack()
        return url
    }

    /**
     * 设置数据或状态回调
     */
    private fun setCallBack() {
        CallManager.onCallStateBack = { state, role, stateText, lastReason, remoteUri, localUri ->
            callStateBackList.forEach {
                it.invoke(state, role, stateText, lastReason, remoteUri, localUri)
            }
        }

        CallManager.onCallMessageBack = { res ->
            callMessageBackList.forEach {
                it.invoke(res)
            }
        }

        CallManager.onRegStateBack = {code, msg ->
            callRegBackList.forEach {
                it.invoke(code, msg)
            }
        }

        CallManager.onSipLogBack = { code, msg ->
            logBackList.forEach {
                it.invoke(code, msg)
            }
        }
    }

//    fun getIpList(
//        macAddress: String,
//        scope: CoroutineScope,
//        callBack: (Code, List<IpEntity>?) -> Unit
//    ) {
//        scope.launch(Dispatchers.IO) {
//            try {
//                val response = CallManager.getIpList(macAddress)
//                if (response.isSuccessful) {
//                    val resEntity = response.body() as ResEntity<List<IpEntity>>
//                    if (resEntity.code == 0) {
//                        callBack.invoke(Code.SUCCESS, resEntity.data)
//                    } else {
//                        callBack.invoke(Code.FAIL, null)
//                    }
//                } else {
//                    callBack.invoke(Code.FAIL, null)
//                }
//            } catch (e: Exception) {
//                when (e) {
//                    is ConnectException ->{
//                        callBack.invoke(Code.NET_ERROR, null)
//                    }
//                    is SocketTimeoutException -> {
//                        callBack.invoke(Code.NET_ERROR, null)
//                    }
//                    else -> {
//                        callBack.invoke(Code.ERROR, null)
//                    }
//                }
//                e.printStackTrace()
//            }
//        }
//    }

    /**
     * 获取sip账号和密码
     * macAddress: 设备mac地址
     * lang: 母语
     * scope: 作用域
     * callBack: 回调（成功返回：{"sipName": "","sipPwd": "","accId": "","registrar": ""}）
     */
    fun getSipAccount(
        macAddress: String,
        lang: String,
        scope: CoroutineScope,
        callBack: (Code, AccountBean?, String) -> Unit
    ) {
        if (HomeServiceImplWrap.getSipIp() != null) {
            scope.launch(Dispatchers.IO) {
                try {
                    val response = CallManager.getUserPwd(macAddress, lang)
                    if (response.isSuccessful) {
                        val resEntity = response.body() as ResEntity<PwdEntity>
                        if (resEntity.code == 0) {
                            val pwdEntity = resEntity.data
                            Log.e(TAG, "name: ${pwdEntity.name}")
                            Log.e(TAG, "secret: ${pwdEntity.secret}")
                            val sipName = pwdEntity.name
                            val sipPwd = pwdEntity.secret
                            val accId = "sip:$sipName@${HomeServiceImplWrap.getSipIp()!!.ip}"
                            val registrar = "sip:${HomeServiceImplWrap.getSipIp()!!.ip}"
                            val account = AccountBean(sipName, sipPwd, accId, registrar)
                            callBack.invoke(Code.SUCCESS, account, "")
                        } else {
                            callBack.invoke(Code.FAIL, null, "")
                        }
                    } else {
                        callBack.invoke(Code.FAIL, null, "")
                    }
                } catch (e: Exception) {
                    Log.d("sdfiqwefqsadfasd","log: " + e.message)

                    when (e) {
                        is ConnectException ->{
                            callBack.invoke(Code.NET_ERROR, null,context.getString(com.timekettle.upup.comm.R.string.common_network_error_check_it))
                        }
                        is SocketTimeoutException -> {
                            callBack.invoke(Code.NET_ERROR, null, context.getString(com.timekettle.upup.comm.R.string.common_network_timeout))
                        }
                        else -> {
                            callBack.invoke(Code.ERROR, null, e.message.toString())
                        }
                    }
                    e.printStackTrace()
                }
            }
        } else {
            callBack.invoke(Code.ERROR, null, "ip is null")
        }
    }

    /**
     * 获取会议id
     * macAddress: 设备mac地址
     * scope: 作用域
     * callBack: 回调
     */
    fun getConferenceId(
        macAddress: String,
        id: Int,
        password: String,
        scope: CoroutineScope,
        callBack: (Code, String) -> Unit
    ) {
        scope.launch(Dispatchers.IO) {
            try {
                val response = CallManager.getMeetId(macAddress, password, id)
                if (response.isSuccessful) {
                    val resEntity = response.body() as ResEntity<MeetIdEntity>
                    if (resEntity.code == 0) {
                        val meetIdEntity = resEntity.data
                        callBack.invoke(Code.SUCCESS, meetIdEntity.group)
                    } else {
                        callBack.invoke(Code.FAIL, "")
                    }
                } else {
                    callBack.invoke(Code.FAIL, "")
                }
            } catch (e: Exception) {
                when (e) {
                    is ConnectException ->{
                        callBack.invoke(Code.NET_ERROR, context.getString(com.timekettle.upup.comm.R.string.common_network_error_check_it))
                    }
                    is SocketTimeoutException -> {
                        callBack.invoke(Code.NET_ERROR, context.getString(com.timekettle.upup.comm.R.string.common_network_timeout))
                    }
                    else -> {
                        callBack.invoke(Code.ERROR,e.message.toString())
                    }
                }
                e.printStackTrace()
            }
        }
    }

    /**
     * 获取会议是否加密
     * macAddress: 设备mac地址
     * group: 会议id
     * scope: 作用域
     * callBack: 1: 已加密1; 0: 未加密
     */
    fun getConferenceFlag(
        macAddress: String,
        group: String,
        scope: CoroutineScope,
        callBack: (Code, String, String) -> Unit
    ) {
        scope.launch(Dispatchers.IO) {
            try {
                val response = CallManager.getMeetFlag(macAddress, group)
                if (response.isSuccessful) {
                    val resEntity = response.body() as ResEntity<MeetFlagEntity>
                    if (resEntity.code == 0 || resEntity.code == -1) {
                        val meetFlagEntity = resEntity.data
                        callBack.invoke(Code.SUCCESS, meetFlagEntity.flag.toString(), meetFlagEntity.conf_ip + ":5166;transport=tcp")
                    }else {
                        callBack.invoke(Code.FAIL, "", "")
                    }
                } else {
                    callBack.invoke(Code.FAIL, "", "")
                }
            } catch (e: Exception) {
                when (e) {
                    is ConnectException ->{
                        callBack.invoke(Code.NET_ERROR,context.getString(com.timekettle.upup.comm.R.string.common_network_error_check_it), "")
                    }
                    is SocketTimeoutException -> {
                        callBack.invoke(Code.NET_ERROR, context.getString(com.timekettle.upup.comm.R.string.common_network_timeout), "")
                    }
                    else -> {
                        callBack.invoke(Code.ERROR, e.message.toString(), "")
                    }
                }
                e.printStackTrace()
            }
        }
    }

    /**
     * 会议验证
     * macAddress: 设备mac地址
     * group: 会议id
     * password: 会议密码（非空: 加锁; 空: 解锁）
     * scope: 作用域
     * callBack: 管理员：加锁或加锁成功与否；普通用户：密码校验成功与否
     */
    fun verifyConference(
        macAddress: String,
        group: String,
        password: String,
        scope: CoroutineScope,
        callBack: (Code, String) -> Unit
    ) {
        scope.launch(Dispatchers.IO) {
            try {
                val response = CallManager.encryptMeet(macAddress, group, password)
                if (response.isSuccessful) {
                    val resEntity = response.body() as ResEntity<String>
                    if (resEntity.code == 0) {
                        callBack.invoke(Code.SUCCESS, "")
                    } else if(resEntity.code == -2){
                        callBack.invoke(Code.PWD_ERROR, "")
                    } else {
                        callBack.invoke(Code.FAIL, "")
                    }
                } else {
                    callBack.invoke(Code.FAIL, "")
                }
            } catch (e: Exception) {
                when (e) {
                    is ConnectException ->{
                        callBack.invoke(Code.NET_ERROR,context.getString(com.timekettle.upup.comm.R.string.common_network_error_check_it))
                    }
                    is SocketTimeoutException -> {
                        callBack.invoke(Code.NET_ERROR, context.getString(com.timekettle.upup.comm.R.string.common_network_timeout))
                    }
                    else -> {
                        callBack.invoke(Code.ERROR, e.message.toString())
                    }
                }
                e.printStackTrace()
            }
        }
    }

    /**
     * 创建或加入会议
     * nickName: 昵称
     * sipName: sip账号
     * sipPwd: sip密码
     * accId: accId
     * registrar: 作用域
     * lang: 母语
     * group: 会议id（如果会议已加密，id后面需要拼接上会议密码）
     */
//    fun createConference(
//        nickName: String,
//        sipName: String,
//        sipPwd: String,
//        accId: String,
//        registrar: String,
//        lang: String,
//        group: String
//    ) {
//        CallManager.createAccount(nickName, sipName, sipPwd, accId, registrar)
//        val uri = "sip:$group@${NetUrl.SipIP}"
//        CallManager.makeCall(uri, lang)
//    }

    /**
     * 踢出群聊
     */
    fun kickOutGroup(sipName: String) {
        val req = ReqEntity(Order.KICK_OUT.ordinal, sipName)
        CallManager.sendSip(req)
    }

    /**
     * 设置语言
     */
    fun setLanguage(langCode: String) {
        val req = ReqEntity(Order.SET_LANGUAGE.ordinal, langCode)
        CallManager.sendSip(req)
    }

    /**
     * 转让管理员
     */
    fun transferAdmin(sipName: String) {
        val req = ReqEntity(Order.TRANSFER_ADMIN.ordinal, sipName)
        CallManager.sendSip(req)
    }

    /**
     * 设置会议模式（0：演讲模式；1：讨论模式）
     */
    fun setMode(mode: String) {
        val req = ReqEntity(Order.SET_MODE.ordinal, mode)
        CallManager.sendSip(req)
    }

    /**
     * 查询会议信息
     */
    fun getConferenceInfo() {
        val req = ReqEntity(Order.GET_CONFERENCE.ordinal, "0")
        CallManager.sendSip(req)
    }

    /**
     * 解散会议
     */
    fun endConference() {
        val req = ReqEntity(Order.END_CONFERENCE.ordinal, "0")
        CallManager.sendSip(req)
    }

    /**
     * 离开会议
     */
    fun leaveConference() {
        val req = ReqEntity(Order.LEAVE_CONFERENCE.ordinal, "0")
        CallManager.sendSip(req)
    }

    /**
     * 修改昵称
     */
    fun editNickName(nickName: String) {
        val req = ReqEntity(Order.EDIT_NAME.ordinal, nickName)
        CallManager.sendSip(req)
    }

    /**
     * 释放资源
     */
    fun deInit() {
        CallManager.deInit()
        removeCallBack()
    }

    private fun removeCallBack() {
        callStateBackList.clear()
        callMessageBackList.clear()

        CallManager.onRegStateBack = null
        CallManager.onCallStateBack = null
        CallManager.onCallMessageBack = null
    }

}