package com.timekettle.upup.comm.base

import android.view.View
import android.view.ViewGroup
import androidx.constraintlayout.widget.ConstraintLayout
import androidx.constraintlayout.widget.ConstraintSet
import androidx.viewbinding.ViewBinding
import com.blankj.utilcode.util.ConvertUtils
import com.timekettle.upup.base.mvvm.v.BaseFrameFragment
import com.timekettle.upup.base.mvvm.vm.BaseViewModel
import com.timekettle.upup.comm.widget.WifiBatteryView

/**
 * 项目相关的Fragment基类
 *
 * <AUTHOR>
 * @since 2022/04/15
 * wifiClickable : Wi-Fi图标是否可以点击
 */
abstract class BaseFragment<VB : ViewBinding, VM : BaseViewModel> : BaseFrameFragment<VB, VM>() {
    fun addWifiBatteryView(layout: ViewGroup, showWifi: Boolean = true,wifiClickable:Boolean = true) {
        val battery = WifiBatteryView(requireContext()).apply {
            id = View.generateViewId()
            layoutParams = ConstraintLayout.LayoutParams(
                ConstraintLayout.LayoutParams.WRAP_CONTENT,
                ConstraintLayout.LayoutParams.WRAP_CONTENT,
            ).apply {
                endToEnd = ConstraintSet.PARENT_ID
                bottomToBottom = ConstraintSet.PARENT_ID
                topToTop = ConstraintSet.PARENT_ID
                marginEnd = ConvertUtils.dp2px(4f)
            }
            setWifiVisible(showWifi)
            setWifiClickable(wifiClickable)
        }
        layout.addView(battery)
    }

}