
<animated-vector xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:aapt="http://schemas.android.com/aapt">
    <aapt:attr name="android:drawable">
        <vector
            android:width="432dp"
            android:height="432dp"
            android:viewportWidth="432"
            android:viewportHeight="432">
            <group android:name="_R_<PERSON>">
                <group
                    android:name="_R_G_L_11_G"
                    android:scaleX="0.392"
                    android:scaleY="0.392"
                    android:translateX="216"
                    android:translateY="215.958">
                    <path
                        android:name="_R_G_L_11_G_D_0_P_0"
                        android:fillAlpha="0"
                        android:fillColor="#9e9e9e"
                        android:fillType="nonZero"
                        android:pathData=" M226.64 -91.12 C226.64,-91.12 138.67,-114.77 138.67,-114.77 C138.67,-114.77 138.67,-180.11 138.67,-180.11 C138.67,-186.64 133.28,-192 126.69,-192 C126.69,-192 -126.69,-192 -126.69,-192 C-133.28,-192 -138.67,-186.64 -138.67,-180.11 C-138.67,-180.11 -138.67,-115.01 -138.67,-115.01 C-138.67,-115.01 -227.57,-82.67 -227.57,-82.67 C-233.31,-80.59 -236.13,-73.79 -233.87,-67.57 C-233.87,-67.57 -181.33,76.8 -181.33,76.8 C-181.33,76.8 -181.33,180 -181.33,180 C-181.33,186.59 -175.92,192 -169.33,192 C-169.33,192 169.33,192 169.33,192 C175.95,192 181.33,186.59 181.33,180 C181.33,180 181.33,120.75 181.33,120.75 C181.33,120.75 234.21,-76.64 234.21,-76.64 C235.95,-83.04 232.51,-89.55 226.64,-91.12c " />
                </group>
                <group
                    android:name="_R_G_L_10_G_T_1"
                    android:scaleX="0.0392"
                    android:scaleY="0"
                    android:translateX="216"
                    android:translateY="247.71">
                    <group
                        android:name="_R_G_L_10_G"
                        android:translateY="55.813">
                        <path
                            android:name="_R_G_L_10_G_D_0_P_0"
                            android:fillAlpha="0"
                            android:fillColor="#0c9d58"
                            android:fillType="nonZero"
                            android:pathData=" M138.67 68.48 C138.67,75.01 133.28,80.37 126.69,80.37 C126.69,80.37 -126.69,80.37 -126.69,80.37 C-133.28,80.37 -138.67,75.01 -138.67,68.48 C-138.67,68.48 -138.67,-180.11 -138.67,-180.11 C-138.67,-186.64 -133.28,-192 -126.69,-192 C-126.69,-192 126.69,-192 126.69,-192 C133.28,-192 138.67,-186.64 138.67,-180.11 C138.67,-180.11 138.67,68.48 138.67,68.48c " />
                        <path
                            android:name="_R_G_L_10_G_D_1_P_0"
                            android:fillAlpha="0"
                            android:fillColor="#ffffff"
                            android:fillType="nonZero"
                            android:pathData=" M126.69 -192 C126.69,-192 -126.69,-192 -126.69,-192 C-133.28,-192 -138.67,-186.64 -138.67,-180.11 C-138.67,-180.11 -138.67,-177.44 -138.67,-177.44 C-138.67,-183.97 -133.28,-189.33 -126.69,-189.33 C-126.69,-189.33 126.69,-189.33 126.69,-189.33 C133.28,-189.33 138.67,-183.97 138.67,-177.44 C138.67,-177.44 138.67,-180.11 138.67,-180.11 C138.67,-186.64 133.28,-192 126.69,-192c " />
                    </group>
                </group>
                <group
                    android:name="_R_G_L_9_G_N_5_T_1"
                    android:rotation="40"
                    android:scaleX="0.0784"
                    android:scaleY="0"
                    android:translateX="228.675"
                    android:translateY="226.542">
                    <group
                        android:name="_R_G_L_9_G_N_5_T_0"
                        android:translateX="63.12"
                        android:translateY="16.08">
                        <group android:name="_R_G_L_9_G">
                            <path
                                android:name="_R_G_L_9_G_D_0_P_0"
                                android:fillAlpha="0"
                                android:fillColor="#004d40"
                                android:fillType="nonZero"
                                android:pathData=" M4.59 -179.04 C4.59,-179.04 -139.23,-114.83 -139.23,-114.83 C-139.23,-114.83 19.71,-172.67 19.71,-172.67 C19.71,-172.67 19.71,-172.83 19.71,-172.83 C16.91,-178.83 10.11,-181.63 4.59,-179.04c " />
                        </group>
                    </group>
                </group>
                <group
                    android:name="_R_G_L_8_G_N_11_T_1"
                    android:scaleX="0.0392"
                    android:scaleY="0"
                    android:translateX="216"
                    android:translateY="247.71">
                    <group
                        android:name="_R_G_L_8_G_N_11_T_0"
                        android:translateY="55.813">
                        <group android:name="_R_G_L_8_G">
                            <path
                                android:name="_R_G_L_8_G_D_0_P_0"
                                android:fillAlpha="0"
                                android:fillColor="#004d40"
                                android:fillType="nonZero"
                                android:pathData=" M138.67 -128.11 C138.67,-128.11 43.71,-140.32 43.71,-140.32 C43.71,-140.32 138.67,-114.83 138.67,-114.83 C138.67,-114.83 138.67,-128.11 138.67,-128.11c " />
                        </group>
                    </group>
                </group>
                <group
                    android:name="_R_G_L_7_G_T_1"
                    android:rotation="-30"
                    android:scaleX="0.0588"
                    android:scaleY="0"
                    android:translateX="191.304"
                    android:translateY="239.936">
                    <group
                        android:name="_R_G_L_7_G"
                        android:translateX="-78.52"
                        android:translateY="7.893">
                        <path
                            android:name="_R_G_L_7_G_D_0_P_0"
                            android:fillAlpha="0"
                            android:fillColor="#ea4335"
                            android:fillType="nonZero"
                            android:pathData=" M-77.17 60.85 C-78.88,67.23 -75.47,73.76 -69.57,75.33 C-69.57,75.33 165.12,138.45 165.12,138.45 C171.01,140.03 177.23,136.11 178.93,129.71 C178.93,129.71 234.21,-76.64 234.21,-76.64 C235.92,-83.01 232.51,-89.55 226.61,-91.12 C226.61,-91.12 -8.08,-154.24 -8.08,-154.24 C-13.97,-155.81 -20.19,-151.89 -21.89,-145.49 C-21.89,-145.49 -77.17,60.85 -77.17,60.85c " />
                        <path
                            android:name="_R_G_L_7_G_D_1_P_0"
                            android:fillAlpha="0"
                            android:fillColor="#ffffff"
                            android:fillType="nonZero"
                            android:pathData=" M226.64 -91.12 C226.64,-91.12 -8.08,-154.24 -8.08,-154.24 C-13.97,-155.81 -20.19,-151.89 -21.89,-145.49 C-21.89,-145.49 -77.17,60.85 -77.17,60.85 C-77.25,61.2 -77.28,61.55 -77.36,61.89 C-77.36,61.89 -22.48,-142.88 -22.48,-142.88 C-20.77,-149.25 -14.56,-153.2 -8.67,-151.63 C-8.67,-151.63 226.03,-88.51 226.03,-88.51 C231.6,-87.01 234.91,-81.09 233.79,-75.07 C233.79,-75.07 234.21,-76.64 234.21,-76.64 C235.95,-83.04 232.51,-89.55 226.64,-91.12c " />
                    </group>
                </group>
                <group
                    android:name="_R_G_L_6_G_N_8_T_1"
                    android:rotation="-30"
                    android:scaleX="0.0588"
                    android:scaleY="0"
                    android:translateX="191.304"
                    android:translateY="239.936">
                    <group
                        android:name="_R_G_L_6_G_N_8_T_0"
                        android:translateX="-78.52"
                        android:translateY="7.893">
                        <group android:name="_R_G_L_6_G">
                            <path
                                android:name="_R_G_L_6_G_D_0_P_0"
                                android:fillAlpha="0"
                                android:fillColor="#3e2723"
                                android:fillType="nonZero"
                                android:pathData=" M181.33 -2.99 C181.33,-2.99 193.68,72 193.68,72 C193.68,72 181.33,118.08 181.33,118.08 C181.33,118.08 181.33,-2.99 181.33,-2.99c " />
                        </group>
                    </group>
                </group>
                <group
                    android:name="_R_G_L_5_G_N_5_T_1"
                    android:rotation="40"
                    android:scaleX="0.0784"
                    android:scaleY="0"
                    android:translateX="228.675"
                    android:translateY="226.542">
                    <group
                        android:name="_R_G_L_5_G_N_5_T_0"
                        android:translateX="63.12"
                        android:translateY="16.08">
                        <group android:name="_R_G_L_5_G">
                            <path
                                android:name="_R_G_L_5_G_D_0_P_0"
                                android:fillAlpha="0"
                                android:fillColor="#3e2723"
                                android:fillType="nonZero"
                                android:pathData=" M42.36 -121.5 C42.36,-121.5 53.43,-118.51 53.43,-118.51 C53.43,-118.51 60.31,-72.24 60.31,-72.24 C60.31,-72.24 42.36,-121.5 42.36,-121.5c " />
                        </group>
                    </group>
                </group>
                <group
                    android:name="_R_G_L_4_G_T_1"
                    android:rotation="40"
                    android:scaleX="0.0784"
                    android:scaleY="0"
                    android:translateX="228.675"
                    android:translateY="226.542">
                    <group
                        android:name="_R_G_L_4_G"
                        android:translateX="63.12"
                        android:translateY="16.08">
                        <path
                            android:name="_R_G_L_4_G_D_0_P_0"
                            android:fillAlpha="0"
                            android:fillColor="#ffc107"
                            android:fillType="nonZero"
                            android:pathData=" M107.65 35.44 C109.92,41.65 107.07,48.43 101.36,50.53 C101.36,50.53 -146.29,140.67 -146.29,140.67 C-152.03,142.75 -158.56,139.39 -160.83,133.17 C-160.83,133.17 -233.89,-67.6 -233.89,-67.6 C-236.16,-73.81 -233.31,-80.59 -227.6,-82.69 C-227.6,-82.69 20.05,-172.83 20.05,-172.83 C25.79,-174.91 32.32,-171.55 34.59,-165.33 C34.59,-165.33 107.65,35.44 107.65,35.44c " />
                        <path
                            android:name="_R_G_L_4_G_D_1_P_0"
                            android:fillAlpha="0"
                            android:fillColor="#ffffff"
                            android:fillType="nonZero"
                            android:pathData=" M-232.96 -65.07 C-235.23,-71.28 -232.37,-78.05 -226.67,-80.16 C-226.67,-80.16 20.99,-170.29 20.99,-170.29 C26.64,-172.35 33.09,-169.07 35.41,-162.99 C35.41,-162.99 34.56,-165.33 34.56,-165.33 C32.29,-171.55 25.76,-174.91 20.03,-172.83 C20.03,-172.83 -227.57,-82.67 -227.57,-82.67 C-233.31,-80.59 -236.13,-73.79 -233.87,-67.57 C-233.87,-67.57 -160.8,133.17 -160.8,133.17 C-160.77,133.25 -160.75,133.31 -160.72,133.36 C-160.72,133.36 -232.96,-65.07 -232.96,-65.07c " />
                    </group>
                </group>
                <group
                    android:name="_R_G_L_3_G_T_1"
                    android:scaleX="0.098"
                    android:scaleY="0"
                    android:translateX="216"
                    android:translateY="236.604">
                    <group
                        android:name="_R_G_L_3_G"
                        android:translateY="-52">
                        <path
                            android:name="_R_G_L_3_G_D_0_P_0"
                            android:fillAlpha="0"
                            android:fillColor="#4285f4"
                            android:fillType="nonZero"
                            android:pathData=" M181.33 180 C181.33,186.61 175.95,192 169.33,192 C169.33,192 -169.33,192 -169.33,192 C-175.95,192 -181.33,186.61 -181.33,180 C-181.33,180 -181.33,-76 -181.33,-76 C-181.33,-82.59 -175.92,-88 -169.33,-88 C-169.33,-88 169.33,-88 169.33,-88 C175.95,-88 181.33,-82.59 181.33,-76 C181.33,-76 181.33,180 181.33,180c " />
                        <path
                            android:name="_R_G_L_3_G_D_1_P_0"
                            android:fillAlpha="0"
                            android:fillColor="#1a237e"
                            android:fillType="nonZero"
                            android:pathData=" M169.33 189.33 C169.33,189.33 -169.33,189.33 -169.33,189.33 C-175.92,189.33 -181.33,183.92 -181.33,177.33 C-181.33,177.33 -181.33,180 -181.33,180 C-181.33,186.59 -175.92,192 -169.33,192 C-169.33,192 169.33,192 169.33,192 C175.95,192 181.33,186.59 181.33,180 C181.33,180 181.33,177.33 181.33,177.33 C181.33,183.92 175.92,189.33 169.33,189.33c " />
                        <path
                            android:name="_R_G_L_3_G_D_2_P_0"
                            android:fillAlpha="0"
                            android:fillColor="#ffffff"
                            android:fillType="nonZero"
                            android:pathData=" M-169.33 -85.33 C-169.33,-85.33 169.33,-85.33 169.33,-85.33 C175.92,-85.33 181.33,-79.92 181.33,-73.33 C181.33,-73.33 181.33,-76 181.33,-76 C181.33,-82.59 175.92,-88 169.33,-88 C169.33,-88 -169.33,-88 -169.33,-88 C-175.95,-88 -181.33,-82.59 -181.33,-76 C-181.33,-76 -181.33,-73.33 -181.33,-73.33 C-181.33,-79.92 -175.95,-85.33 -169.33,-85.33c " />
                    </group>
                </group>
                <group
                    android:name="_R_G_L_2_G_N_4_T_1"
                    android:scaleX="0.098"
                    android:scaleY="0"
                    android:translateX="216"
                    android:translateY="236.604">
                    <group
                        android:name="_R_G_L_2_G_N_4_T_0"
                        android:translateY="-52">
                        <group android:name="_R_G_L_2_G">
                            <path
                                android:name="_R_G_L_2_G_D_0_P_0"
                                android:fillAlpha="0"
                                android:fillColor="#1a237e"
                                android:fillType="nonZero"
                                android:pathData=" M-74.67 64.05 C-74.67,64.05 -74.67,66.72 -74.67,66.72 C-74.67,66.72 -40.8,66.72 -40.8,66.72 C-40.56,65.84 -40.32,64.96 -40.16,64.05 C-40.16,64.05 -74.67,64.05 -74.67,64.05c  M-74.67 114.67 C-109.57,114.67 -137.92,86.72 -138.64,52 C-138.64,52.45 -138.67,52.88 -138.67,53.33 C-138.67,88.67 -110,117.33 -74.67,117.33 C-37.71,117.33 -13.39,91.36 -13.39,54.8 C-13.39,54.51 -13.41,54.24 -13.41,53.95 C-14.21,89.55 -38.32,114.67 -74.67,114.67c  M-50.11 21.6 C-56.77,15.25 -65.23,12 -74.67,12 C-95.63,12 -112.61,29.71 -112.61,50.67 C-112.61,51.12 -112.56,51.55 -112.53,52 C-111.81,31.63 -95.17,14.67 -74.67,14.67 C-65.23,14.67 -56.77,17.92 -50.11,24.27 C-50.11,24.27 -50.11,24.27 -50.11,24.27 C-50.11,24.27 -30.51,4.67 -30.51,4.67 C-30.96,4.24 -31.47,3.87 -31.95,3.44 C-31.95,3.44 -50.11,21.6 -50.11,21.6 C-50.11,21.6 -50.11,21.6 -50.11,21.6c  M15.35 114.67 C15.35,114.67 15.33,114.67 15.33,114.67 C15.33,114.67 15.33,117.33 15.33,117.33 C15.33,117.33 15.35,117.33 15.35,117.33 C18.29,117.33 20.69,114.93 20.69,112 C20.69,112 20.69,109.33 20.69,109.33 C20.69,112.27 18.29,114.67 15.35,114.67c " />
                        </group>
                    </group>
                </group>
                <group
                    android:name="_R_G_L_1_G_N_4_T_1"
                    android:scaleX="0.098"
                    android:scaleY="0"
                    android:translateX="216"
                    android:translateY="236.604">
                    <group
                        android:name="_R_G_L_1_G_N_4_T_0"
                        android:translateY="-52">
                        <group android:name="_R_G_L_1_G">
                            <path
                                android:name="_R_G_L_1_G_D_0_P_0"
                                android:fillAlpha="0"
                                android:fillColor="#ffffff"
                                android:fillType="nonZero"
                                android:pathData=" M-75.33 40 C-75.33,40 -75.33,64.05 -75.33,64.05 C-75.33,64.05 -40.83,64.05 -40.83,64.05 C-43.68,78.72 -56.56,89.33 -75.33,89.33 C-96.29,89.33 -113.28,71.63 -113.28,50.67 C-113.28,29.71 -96.29,12 -75.33,12 C-65.89,12 -57.44,15.25 -50.77,21.6 C-50.77,21.6 -50.77,21.6 -50.77,21.6 C-50.77,21.6 -32.51,3.33 -32.51,3.33 C-43.6,-6.99 -58.08,-13.33 -75.33,-13.33 C-110.67,-13.33 -139.33,15.33 -139.33,50.67 C-139.33,86 -110.67,114.67 -75.33,114.67 C-38.37,114.67 -14.05,88.69 -14.05,52.13 C-14.05,47.95 -14.45,43.89 -15.07,40 C-15.07,40 -75.33,40 -75.33,40c  M15.34 114.67 C15.34,114.67 15.33,114.67 15.33,114.67 C15.33,114.67 15.33,88 15.33,88 C15.33,88 15.34,88 15.34,88 C18.27,88 20.67,90.4 20.67,93.33 C20.67,93.33 20.67,109.33 20.67,109.33 C20.67,112.27 18.27,114.67 15.34,114.67c " />
                        </group>
                    </group>
                </group>
                <group
                    android:name="_R_G_L_0_G"
                    android:scaleX="0.392"
                    android:scaleY="0"
                    android:translateX="216"
                    android:translateY="215.958" />
            </group>
            <group android:name="time_group" />
        </vector>
    </aapt:attr>
    <target android:name="_R_G_L_11_G_D_0_P_0">
        <aapt:attr name="android:animation">
            <set android:ordering="together">
                <objectAnimator
                    android:duration="52"
                    android:propertyName="fillAlpha"
                    android:startOffset="0"
                    android:valueFrom="0"
                    android:valueTo="0.2"
                    android:valueType="floatType">
                    <aapt:attr name="android:interpolator">
                        <pathInterpolator android:pathData="M 0.0,0.0 c0,0 0.67,1 1.0,1.0" />
                    </aapt:attr>
                </objectAnimator>
                <objectAnimator
                    android:duration="932"
                    android:propertyName="fillAlpha"
                    android:startOffset="52"
                    android:valueFrom="0.2"
                    android:valueTo="0.2"
                    android:valueType="floatType">
                    <aapt:attr name="android:interpolator">
                        <pathInterpolator android:pathData="M 0.0,0.0 c0.5,0 0.833,1 1.0,1.0" />
                    </aapt:attr>
                </objectAnimator>
                <objectAnimator
                    android:duration="17"
                    android:propertyName="fillAlpha"
                    android:startOffset="983"
                    android:valueFrom="0.2"
                    android:valueTo="0"
                    android:valueType="floatType">
                    <aapt:attr name="android:interpolator">
                        <pathInterpolator android:pathData="M 0.0,0.0 c0.167,0.167 0.833,0.833 1.0,1.0" />
                    </aapt:attr>
                </objectAnimator>
            </set>
        </aapt:attr>
    </target>
    <target android:name="_R_G_L_11_G">
        <aapt:attr name="android:animation">
            <set android:ordering="together">
                <objectAnimator
                    android:duration="0"
                    android:propertyName="scaleY"
                    android:startOffset="0"
                    android:valueFrom="0"
                    android:valueTo="0.392"
                    android:valueType="floatType" />
            </set>
        </aapt:attr>
    </target>
    <target android:name="_R_G_L_11_G">
        <aapt:attr name="android:animation">
            <set android:ordering="together">
                <objectAnimator
                    android:duration="0"
                    android:propertyName="scaleY"
                    android:startOffset="1000"
                    android:valueFrom="0.392"
                    android:valueTo="0"
                    android:valueType="floatType" />
            </set>
        </aapt:attr>
    </target>
    <target android:name="_R_G_L_10_G_D_0_P_0">
        <aapt:attr name="android:animation">
            <set android:ordering="together">
                <objectAnimator
                    android:duration="67"
                    android:propertyName="fillAlpha"
                    android:startOffset="0"
                    android:valueFrom="0"
                    android:valueTo="0"
                    android:valueType="floatType">
                    <aapt:attr name="android:interpolator">
                        <pathInterpolator android:pathData="M 0.0,0.0 c0.167,0.167 0.833,0.833 1.0,1.0" />
                    </aapt:attr>
                </objectAnimator>
                <objectAnimator
                    android:duration="17"
                    android:propertyName="fillAlpha"
                    android:startOffset="67"
                    android:valueFrom="0"
                    android:valueTo="1"
                    android:valueType="floatType">
                    <aapt:attr name="android:interpolator">
                        <pathInterpolator android:pathData="M 0.0,0.0 c0.167,0.167 0.833,0.833 1.0,1.0" />
                    </aapt:attr>
                </objectAnimator>
            </set>
        </aapt:attr>
    </target>
    <target android:name="_R_G_L_10_G_D_1_P_0">
        <aapt:attr name="android:animation">
            <set android:ordering="together">
                <objectAnimator
                    android:duration="67"
                    android:propertyName="fillAlpha"
                    android:startOffset="0"
                    android:valueFrom="0"
                    android:valueTo="0"
                    android:valueType="floatType">
                    <aapt:attr name="android:interpolator">
                        <pathInterpolator android:pathData="M 0.0,0.0 c0.167,0.167 0.833,0.833 1.0,1.0" />
                    </aapt:attr>
                </objectAnimator>
                <objectAnimator
                    android:duration="17"
                    android:propertyName="fillAlpha"
                    android:startOffset="67"
                    android:valueFrom="0"
                    android:valueTo="0.2"
                    android:valueType="floatType">
                    <aapt:attr name="android:interpolator">
                        <pathInterpolator android:pathData="M 0.0,0.0 c0.167,0.167 0.833,0.833 1.0,1.0" />
                    </aapt:attr>
                </objectAnimator>
            </set>
        </aapt:attr>
    </target>
    <target android:name="_R_G_L_10_G_T_1">
        <aapt:attr name="android:animation">
            <set android:ordering="together">
                <objectAnimator
                    android:duration="83"
                    android:pathData="M 216,247.71C 216,238.77200000000002 216,256.648 216,247.71"
                    android:propertyName="translateXY"
                    android:propertyXName="translateX"
                    android:propertyYName="translateY"
                    android:startOffset="0">
                    <aapt:attr name="android:interpolator">
                        <pathInterpolator android:pathData="M 0.0,0.0 c0,0 0,1 1.0,1.0" />
                    </aapt:attr>
                </objectAnimator>
                <objectAnimator
                    android:duration="917"
                    android:pathData="M 216,247.71C 216,238.77200000000002 216,203.018 216,194.08"
                    android:propertyName="translateXY"
                    android:propertyXName="translateX"
                    android:propertyYName="translateY"
                    android:startOffset="83">
                    <aapt:attr name="android:interpolator">
                        <pathInterpolator android:pathData="M 0.0,0.0 c0,0 0,1 1.0,1.0" />
                    </aapt:attr>
                </objectAnimator>
            </set>
        </aapt:attr>
    </target>
    <target android:name="_R_G_L_10_G_T_1">
        <aapt:attr name="android:animation">
            <set android:ordering="together">
                <objectAnimator
                    android:duration="83"
                    android:propertyName="scaleX"
                    android:startOffset="0"
                    android:valueFrom="0.0392"
                    android:valueTo="0.0392"
                    android:valueType="floatType">
                    <aapt:attr name="android:interpolator">
                        <pathInterpolator android:pathData="M 0.0,0.0 c0,0 0,0.746 1.0,1.0" />
                    </aapt:attr>
                </objectAnimator>
                <objectAnimator
                    android:duration="83"
                    android:propertyName="scaleY"
                    android:startOffset="0"
                    android:valueFrom="0.0392"
                    android:valueTo="0.0392"
                    android:valueType="floatType">
                    <aapt:attr name="android:interpolator">
                        <pathInterpolator android:pathData="M 0.0,0.0 c0,0 0,0.746 1.0,1.0" />
                    </aapt:attr>
                </objectAnimator>
                <objectAnimator
                    android:duration="367"
                    android:propertyName="scaleX"
                    android:startOffset="83"
                    android:valueFrom="0.0392"
                    android:valueTo="0.35011000000000003"
                    android:valueType="floatType">
                    <aapt:attr name="android:interpolator">
                        <pathInterpolator android:pathData="M 0.0,0.0 c0,0 0,0.746 1.0,1.0" />
                    </aapt:attr>
                </objectAnimator>
                <objectAnimator
                    android:duration="367"
                    android:propertyName="scaleY"
                    android:startOffset="83"
                    android:valueFrom="0.0392"
                    android:valueTo="0.35011000000000003"
                    android:valueType="floatType">
                    <aapt:attr name="android:interpolator">
                        <pathInterpolator android:pathData="M 0.0,0.0 c0,0 0,0.746 1.0,1.0" />
                    </aapt:attr>
                </objectAnimator>
                <objectAnimator
                    android:duration="550"
                    android:propertyName="scaleX"
                    android:startOffset="450"
                    android:valueFrom="0.35011000000000003"
                    android:valueTo="0.392"
                    android:valueType="floatType">
                    <aapt:attr name="android:interpolator">
                        <pathInterpolator android:pathData="M 0.0,0.0 c0.257,0.725 0.584,1 1.0,1.0" />
                    </aapt:attr>
                </objectAnimator>
                <objectAnimator
                    android:duration="550"
                    android:propertyName="scaleY"
                    android:startOffset="450"
                    android:valueFrom="0.35011000000000003"
                    android:valueTo="0.392"
                    android:valueType="floatType">
                    <aapt:attr name="android:interpolator">
                        <pathInterpolator android:pathData="M 0.0,0.0 c0.257,0.725 0.584,1 1.0,1.0" />
                    </aapt:attr>
                </objectAnimator>
            </set>
        </aapt:attr>
    </target>
    <target android:name="_R_G_L_10_G_T_1">
        <aapt:attr name="android:animation">
            <set android:ordering="together">
                <objectAnimator
                    android:duration="0"
                    android:propertyName="scaleY"
                    android:startOffset="83"
                    android:valueFrom="0"
                    android:valueTo="0.0392"
                    android:valueType="floatType" />
            </set>
        </aapt:attr>
    </target>
    <target android:name="_R_G_L_9_G_D_0_P_0">
        <aapt:attr name="android:animation">
            <set android:ordering="together">
                <objectAnimator
                    android:duration="450"
                    android:propertyName="fillAlpha"
                    android:startOffset="0"
                    android:valueFrom="0"
                    android:valueTo="0"
                    android:valueType="floatType">
                    <aapt:attr name="android:interpolator">
                        <pathInterpolator android:pathData="M 0.0,0.0 c0,0 0,1 1.0,1.0" />
                    </aapt:attr>
                </objectAnimator>
                <objectAnimator
                    android:duration="550"
                    android:propertyName="fillAlpha"
                    android:startOffset="450"
                    android:valueFrom="0"
                    android:valueTo="0.2"
                    android:valueType="floatType">
                    <aapt:attr name="android:interpolator">
                        <pathInterpolator android:pathData="M 0.0,0.0 c0,0 0,1 1.0,1.0" />
                    </aapt:attr>
                </objectAnimator>
            </set>
        </aapt:attr>
    </target>
    <target android:name="_R_G_L_9_G_N_5_T_1">
        <aapt:attr name="android:animation">
            <set android:ordering="together">
                <objectAnimator
                    android:duration="67"
                    android:pathData="M 228.675,226.542C 222.43900000000002,223.727 234.911,229.357 228.675,226.542"
                    android:propertyName="translateXY"
                    android:propertyXName="translateX"
                    android:propertyYName="translateY"
                    android:startOffset="0">
                    <aapt:attr name="android:interpolator">
                        <pathInterpolator android:pathData="M 0.0,0.0 c0,0 0,1 1.0,1.0" />
                    </aapt:attr>
                </objectAnimator>
                <objectAnimator
                    android:duration="933"
                    android:pathData="M 228.675,226.542C 222.43900000000002,223.727 197.493,212.47 191.257,209.655"
                    android:propertyName="translateXY"
                    android:propertyXName="translateX"
                    android:propertyYName="translateY"
                    android:startOffset="67">
                    <aapt:attr name="android:interpolator">
                        <pathInterpolator android:pathData="M 0.0,0.0 c0,0 0,1 1.0,1.0" />
                    </aapt:attr>
                </objectAnimator>
            </set>
        </aapt:attr>
    </target>
    <target android:name="_R_G_L_9_G_N_5_T_1">
        <aapt:attr name="android:animation">
            <set android:ordering="together">
                <objectAnimator
                    android:duration="67"
                    android:propertyName="scaleX"
                    android:startOffset="0"
                    android:valueFrom="0.0784"
                    android:valueTo="0.0784"
                    android:valueType="floatType">
                    <aapt:attr name="android:interpolator">
                        <pathInterpolator android:pathData="M 0.0,0.0 c0,0 0,0.779 1.0,1.0" />
                    </aapt:attr>
                </objectAnimator>
                <objectAnimator
                    android:duration="67"
                    android:propertyName="scaleY"
                    android:startOffset="0"
                    android:valueFrom="0.0784"
                    android:valueTo="0.0784"
                    android:valueType="floatType">
                    <aapt:attr name="android:interpolator">
                        <pathInterpolator android:pathData="M 0.0,0.0 c0,0 0,0.779 1.0,1.0" />
                    </aapt:attr>
                </objectAnimator>
                <objectAnimator
                    android:duration="383"
                    android:propertyName="scaleX"
                    android:startOffset="67"
                    android:valueFrom="0.0784"
                    android:valueTo="0.35988"
                    android:valueType="floatType">
                    <aapt:attr name="android:interpolator">
                        <pathInterpolator android:pathData="M 0.0,0.0 c0,0 0,0.779 1.0,1.0" />
                    </aapt:attr>
                </objectAnimator>
                <objectAnimator
                    android:duration="383"
                    android:propertyName="scaleY"
                    android:startOffset="67"
                    android:valueFrom="0.0784"
                    android:valueTo="0.35988"
                    android:valueType="floatType">
                    <aapt:attr name="android:interpolator">
                        <pathInterpolator android:pathData="M 0.0,0.0 c0,0 0,0.779 1.0,1.0" />
                    </aapt:attr>
                </objectAnimator>
                <objectAnimator
                    android:duration="550"
                    android:propertyName="scaleX"
                    android:startOffset="450"
                    android:valueFrom="0.35988"
                    android:valueTo="0.392"
                    android:valueType="floatType">
                    <aapt:attr name="android:interpolator">
                        <pathInterpolator android:pathData="M 0.0,0.0 c0.263,0.731 0.591,1 1.0,1.0" />
                    </aapt:attr>
                </objectAnimator>
                <objectAnimator
                    android:duration="550"
                    android:propertyName="scaleY"
                    android:startOffset="450"
                    android:valueFrom="0.35988"
                    android:valueTo="0.392"
                    android:valueType="floatType">
                    <aapt:attr name="android:interpolator">
                        <pathInterpolator android:pathData="M 0.0,0.0 c0.263,0.731 0.591,1 1.0,1.0" />
                    </aapt:attr>
                </objectAnimator>
            </set>
        </aapt:attr>
    </target>
    <target android:name="_R_G_L_9_G_N_5_T_1">
        <aapt:attr name="android:animation">
            <set android:ordering="together">
                <objectAnimator
                    android:duration="67"
                    android:propertyName="rotation"
                    android:startOffset="0"
                    android:valueFrom="40"
                    android:valueTo="40"
                    android:valueType="floatType">
                    <aapt:attr name="android:interpolator">
                        <pathInterpolator android:pathData="M 0.0,0.0 c0,0 0,1 1.0,1.0" />
                    </aapt:attr>
                </objectAnimator>
                <objectAnimator
                    android:duration="933"
                    android:propertyName="rotation"
                    android:startOffset="67"
                    android:valueFrom="40"
                    android:valueTo="0"
                    android:valueType="floatType">
                    <aapt:attr name="android:interpolator">
                        <pathInterpolator android:pathData="M 0.0,0.0 c0,0 0,1 1.0,1.0" />
                    </aapt:attr>
                </objectAnimator>
            </set>
        </aapt:attr>
    </target>
    <target android:name="_R_G_L_9_G_N_5_T_1">
        <aapt:attr name="android:animation">
            <set android:ordering="together">
                <objectAnimator
                    android:duration="0"
                    android:propertyName="scaleY"
                    android:startOffset="83"
                    android:valueFrom="0"
                    android:valueTo="0.0784"
                    android:valueType="floatType" />
            </set>
        </aapt:attr>
    </target>
    <target android:name="_R_G_L_8_G_D_0_P_0">
        <aapt:attr name="android:animation">
            <set android:ordering="together">
                <objectAnimator
                    android:duration="450"
                    android:propertyName="fillAlpha"
                    android:startOffset="0"
                    android:valueFrom="0"
                    android:valueTo="0"
                    android:valueType="floatType">
                    <aapt:attr name="android:interpolator">
                        <pathInterpolator android:pathData="M 0.0,0.0 c0,0 0,1 1.0,1.0" />
                    </aapt:attr>
                </objectAnimator>
                <objectAnimator
                    android:duration="550"
                    android:propertyName="fillAlpha"
                    android:startOffset="450"
                    android:valueFrom="0"
                    android:valueTo="0.2"
                    android:valueType="floatType">
                    <aapt:attr name="android:interpolator">
                        <pathInterpolator android:pathData="M 0.0,0.0 c0,0 0,1 1.0,1.0" />
                    </aapt:attr>
                </objectAnimator>
            </set>
        </aapt:attr>
    </target>
    <target android:name="_R_G_L_8_G_N_11_T_1">
        <aapt:attr name="android:animation">
            <set android:ordering="together">
                <objectAnimator
                    android:duration="83"
                    android:pathData="M 216,247.71C 216,238.77200000000002 216,256.648 216,247.71"
                    android:propertyName="translateXY"
                    android:propertyXName="translateX"
                    android:propertyYName="translateY"
                    android:startOffset="0">
                    <aapt:attr name="android:interpolator">
                        <pathInterpolator android:pathData="M 0.0,0.0 c0,0 0,1 1.0,1.0" />
                    </aapt:attr>
                </objectAnimator>
                <objectAnimator
                    android:duration="917"
                    android:pathData="M 216,247.71C 216,238.77200000000002 216,203.018 216,194.08"
                    android:propertyName="translateXY"
                    android:propertyXName="translateX"
                    android:propertyYName="translateY"
                    android:startOffset="83">
                    <aapt:attr name="android:interpolator">
                        <pathInterpolator android:pathData="M 0.0,0.0 c0,0 0,1 1.0,1.0" />
                    </aapt:attr>
                </objectAnimator>
            </set>
        </aapt:attr>
    </target>
    <target android:name="_R_G_L_8_G_N_11_T_1">
        <aapt:attr name="android:animation">
            <set android:ordering="together">
                <objectAnimator
                    android:duration="83"
                    android:propertyName="scaleX"
                    android:startOffset="0"
                    android:valueFrom="0.0392"
                    android:valueTo="0.0392"
                    android:valueType="floatType">
                    <aapt:attr name="android:interpolator">
                        <pathInterpolator android:pathData="M 0.0,0.0 c0,0 0,0.746 1.0,1.0" />
                    </aapt:attr>
                </objectAnimator>
                <objectAnimator
                    android:duration="83"
                    android:propertyName="scaleY"
                    android:startOffset="0"
                    android:valueFrom="0.0392"
                    android:valueTo="0.0392"
                    android:valueType="floatType">
                    <aapt:attr name="android:interpolator">
                        <pathInterpolator android:pathData="M 0.0,0.0 c0,0 0,0.746 1.0,1.0" />
                    </aapt:attr>
                </objectAnimator>
                <objectAnimator
                    android:duration="367"
                    android:propertyName="scaleX"
                    android:startOffset="83"
                    android:valueFrom="0.0392"
                    android:valueTo="0.35011000000000003"
                    android:valueType="floatType">
                    <aapt:attr name="android:interpolator">
                        <pathInterpolator android:pathData="M 0.0,0.0 c0,0 0,0.746 1.0,1.0" />
                    </aapt:attr>
                </objectAnimator>
                <objectAnimator
                    android:duration="367"
                    android:propertyName="scaleY"
                    android:startOffset="83"
                    android:valueFrom="0.0392"
                    android:valueTo="0.35011000000000003"
                    android:valueType="floatType">
                    <aapt:attr name="android:interpolator">
                        <pathInterpolator android:pathData="M 0.0,0.0 c0,0 0,0.746 1.0,1.0" />
                    </aapt:attr>
                </objectAnimator>
                <objectAnimator
                    android:duration="550"
                    android:propertyName="scaleX"
                    android:startOffset="450"
                    android:valueFrom="0.35011000000000003"
                    android:valueTo="0.392"
                    android:valueType="floatType">
                    <aapt:attr name="android:interpolator">
                        <pathInterpolator android:pathData="M 0.0,0.0 c0.257,0.725 0.584,1 1.0,1.0" />
                    </aapt:attr>
                </objectAnimator>
                <objectAnimator
                    android:duration="550"
                    android:propertyName="scaleY"
                    android:startOffset="450"
                    android:valueFrom="0.35011000000000003"
                    android:valueTo="0.392"
                    android:valueType="floatType">
                    <aapt:attr name="android:interpolator">
                        <pathInterpolator android:pathData="M 0.0,0.0 c0.257,0.725 0.584,1 1.0,1.0" />
                    </aapt:attr>
                </objectAnimator>
            </set>
        </aapt:attr>
    </target>
    <target android:name="_R_G_L_8_G_N_11_T_1">
        <aapt:attr name="android:animation">
            <set android:ordering="together">
                <objectAnimator
                    android:duration="0"
                    android:propertyName="scaleY"
                    android:startOffset="83"
                    android:valueFrom="0"
                    android:valueTo="0.0392"
                    android:valueType="floatType" />
            </set>
        </aapt:attr>
    </target>
    <target android:name="_R_G_L_7_G_D_0_P_0">
        <aapt:attr name="android:animation">
            <set android:ordering="together">
                <objectAnimator
                    android:duration="67"
                    android:propertyName="fillAlpha"
                    android:startOffset="0"
                    android:valueFrom="0"
                    android:valueTo="0"
                    android:valueType="floatType">
                    <aapt:attr name="android:interpolator">
                        <pathInterpolator android:pathData="M 0.0,0.0 c0.167,0.167 0.833,0.833 1.0,1.0" />
                    </aapt:attr>
                </objectAnimator>
                <objectAnimator
                    android:duration="17"
                    android:propertyName="fillAlpha"
                    android:startOffset="67"
                    android:valueFrom="0"
                    android:valueTo="1"
                    android:valueType="floatType">
                    <aapt:attr name="android:interpolator">
                        <pathInterpolator android:pathData="M 0.0,0.0 c0.167,0.167 0.833,0.833 1.0,1.0" />
                    </aapt:attr>
                </objectAnimator>
            </set>
        </aapt:attr>
    </target>
    <target android:name="_R_G_L_7_G_D_1_P_0">
        <aapt:attr name="android:animation">
            <set android:ordering="together">
                <objectAnimator
                    android:duration="67"
                    android:propertyName="fillAlpha"
                    android:startOffset="0"
                    android:valueFrom="0"
                    android:valueTo="0"
                    android:valueType="floatType">
                    <aapt:attr name="android:interpolator">
                        <pathInterpolator android:pathData="M 0.0,0.0 c0.167,0.167 0.833,0.833 1.0,1.0" />
                    </aapt:attr>
                </objectAnimator>
                <objectAnimator
                    android:duration="17"
                    android:propertyName="fillAlpha"
                    android:startOffset="67"
                    android:valueFrom="0"
                    android:valueTo="0.2"
                    android:valueType="floatType">
                    <aapt:attr name="android:interpolator">
                        <pathInterpolator android:pathData="M 0.0,0.0 c0.167,0.167 0.833,0.833 1.0,1.0" />
                    </aapt:attr>
                </objectAnimator>
            </set>
        </aapt:attr>
    </target>
    <target android:name="_R_G_L_7_G_T_1">
        <aapt:attr name="android:animation">
            <set android:ordering="together">
                <objectAnimator
                    android:duration="67"
                    android:pathData="M 191.304,239.936C 200.55,235.424 182.058,244.448 191.304,239.936"
                    android:propertyName="translateXY"
                    android:propertyXName="translateX"
                    android:propertyYName="translateY"
                    android:startOffset="0">
                    <aapt:attr name="android:interpolator">
                        <pathInterpolator android:pathData="M 0.0,0.0 c0,0 0,1 1.0,1.0" />
                    </aapt:attr>
                </objectAnimator>
                <objectAnimator
                    android:duration="933"
                    android:pathData="M 191.304,239.936C 200.55,235.424 237.534,217.376 246.78,212.864"
                    android:propertyName="translateXY"
                    android:propertyXName="translateX"
                    android:propertyYName="translateY"
                    android:startOffset="67">
                    <aapt:attr name="android:interpolator">
                        <pathInterpolator android:pathData="M 0.0,0.0 c0,0 0,1 1.0,1.0" />
                    </aapt:attr>
                </objectAnimator>
            </set>
        </aapt:attr>
    </target>
    <target android:name="_R_G_L_7_G_T_1">
        <aapt:attr name="android:animation">
            <set android:ordering="together">
                <objectAnimator
                    android:duration="67"
                    android:propertyName="scaleX"
                    android:startOffset="0"
                    android:valueFrom="0.0588"
                    android:valueTo="0.0588"
                    android:valueType="floatType">
                    <aapt:attr name="android:interpolator">
                        <pathInterpolator android:pathData="M 0.0,0.0 c0,0 0,0.758 1.0,1.0" />
                    </aapt:attr>
                </objectAnimator>
                <objectAnimator
                    android:duration="67"
                    android:propertyName="scaleY"
                    android:startOffset="0"
                    android:valueFrom="0.0588"
                    android:valueTo="0.0588"
                    android:valueType="floatType">
                    <aapt:attr name="android:interpolator">
                        <pathInterpolator android:pathData="M 0.0,0.0 c0,0 0,0.758 1.0,1.0" />
                    </aapt:attr>
                </objectAnimator>
                <objectAnimator
                    android:duration="383"
                    android:propertyName="scaleX"
                    android:startOffset="67"
                    android:valueFrom="0.0588"
                    android:valueTo="0.35531"
                    android:valueType="floatType">
                    <aapt:attr name="android:interpolator">
                        <pathInterpolator android:pathData="M 0.0,0.0 c0,0 0,0.758 1.0,1.0" />
                    </aapt:attr>
                </objectAnimator>
                <objectAnimator
                    android:duration="383"
                    android:propertyName="scaleY"
                    android:startOffset="67"
                    android:valueFrom="0.0588"
                    android:valueTo="0.35531"
                    android:valueType="floatType">
                    <aapt:attr name="android:interpolator">
                        <pathInterpolator android:pathData="M 0.0,0.0 c0,0 0,0.758 1.0,1.0" />
                    </aapt:attr>
                </objectAnimator>
                <objectAnimator
                    android:duration="550"
                    android:propertyName="scaleX"
                    android:startOffset="450"
                    android:valueFrom="0.35531"
                    android:valueTo="0.392"
                    android:valueType="floatType">
                    <aapt:attr name="android:interpolator">
                        <pathInterpolator android:pathData="M 0.0,0.0 c0.26,0.728 0.587,1 1.0,1.0" />
                    </aapt:attr>
                </objectAnimator>
                <objectAnimator
                    android:duration="550"
                    android:propertyName="scaleY"
                    android:startOffset="450"
                    android:valueFrom="0.35531"
                    android:valueTo="0.392"
                    android:valueType="floatType">
                    <aapt:attr name="android:interpolator">
                        <pathInterpolator android:pathData="M 0.0,0.0 c0.26,0.728 0.587,1 1.0,1.0" />
                    </aapt:attr>
                </objectAnimator>
            </set>
        </aapt:attr>
    </target>
    <target android:name="_R_G_L_7_G_T_1">
        <aapt:attr name="android:animation">
            <set android:ordering="together">
                <objectAnimator
                    android:duration="67"
                    android:propertyName="rotation"
                    android:startOffset="0"
                    android:valueFrom="-30"
                    android:valueTo="-30"
                    android:valueType="floatType">
                    <aapt:attr name="android:interpolator">
                        <pathInterpolator android:pathData="M 0.0,0.0 c0,0 0,1 1.0,1.0" />
                    </aapt:attr>
                </objectAnimator>
                <objectAnimator
                    android:duration="933"
                    android:propertyName="rotation"
                    android:startOffset="67"
                    android:valueFrom="-30"
                    android:valueTo="0"
                    android:valueType="floatType">
                    <aapt:attr name="android:interpolator">
                        <pathInterpolator android:pathData="M 0.0,0.0 c0,0 0,1 1.0,1.0" />
                    </aapt:attr>
                </objectAnimator>
            </set>
        </aapt:attr>
    </target>
    <target android:name="_R_G_L_7_G_T_1">
        <aapt:attr name="android:animation">
            <set android:ordering="together">
                <objectAnimator
                    android:duration="0"
                    android:propertyName="scaleY"
                    android:startOffset="83"
                    android:valueFrom="0"
                    android:valueTo="0.0588"
                    android:valueType="floatType" />
            </set>
        </aapt:attr>
    </target>
    <target android:name="_R_G_L_6_G_D_0_P_0">
        <aapt:attr name="android:animation">
            <set android:ordering="together">
                <objectAnimator
                    android:duration="450"
                    android:propertyName="fillAlpha"
                    android:startOffset="0"
                    android:valueFrom="0"
                    android:valueTo="0"
                    android:valueType="floatType">
                    <aapt:attr name="android:interpolator">
                        <pathInterpolator android:pathData="M 0.0,0.0 c0,0 0,1 1.0,1.0" />
                    </aapt:attr>
                </objectAnimator>
                <objectAnimator
                    android:duration="550"
                    android:propertyName="fillAlpha"
                    android:startOffset="450"
                    android:valueFrom="0"
                    android:valueTo="0.2"
                    android:valueType="floatType">
                    <aapt:attr name="android:interpolator">
                        <pathInterpolator android:pathData="M 0.0,0.0 c0,0 0,1 1.0,1.0" />
                    </aapt:attr>
                </objectAnimator>
            </set>
        </aapt:attr>
    </target>
    <target android:name="_R_G_L_6_G_N_8_T_1">
        <aapt:attr name="android:animation">
            <set android:ordering="together">
                <objectAnimator
                    android:duration="67"
                    android:pathData="M 191.304,239.936C 200.55,235.424 182.058,244.448 191.304,239.936"
                    android:propertyName="translateXY"
                    android:propertyXName="translateX"
                    android:propertyYName="translateY"
                    android:startOffset="0">
                    <aapt:attr name="android:interpolator">
                        <pathInterpolator android:pathData="M 0.0,0.0 c0,0 0,1 1.0,1.0" />
                    </aapt:attr>
                </objectAnimator>
                <objectAnimator
                    android:duration="933"
                    android:pathData="M 191.304,239.936C 200.55,235.424 237.534,217.376 246.78,212.864"
                    android:propertyName="translateXY"
                    android:propertyXName="translateX"
                    android:propertyYName="translateY"
                    android:startOffset="67">
                    <aapt:attr name="android:interpolator">
                        <pathInterpolator android:pathData="M 0.0,0.0 c0,0 0,1 1.0,1.0" />
                    </aapt:attr>
                </objectAnimator>
            </set>
        </aapt:attr>
    </target>
    <target android:name="_R_G_L_6_G_N_8_T_1">
        <aapt:attr name="android:animation">
            <set android:ordering="together">
                <objectAnimator
                    android:duration="67"
                    android:propertyName="scaleX"
                    android:startOffset="0"
                    android:valueFrom="0.0588"
                    android:valueTo="0.0588"
                    android:valueType="floatType">
                    <aapt:attr name="android:interpolator">
                        <pathInterpolator android:pathData="M 0.0,0.0 c0,0 0,0.758 1.0,1.0" />
                    </aapt:attr>
                </objectAnimator>
                <objectAnimator
                    android:duration="67"
                    android:propertyName="scaleY"
                    android:startOffset="0"
                    android:valueFrom="0.0588"
                    android:valueTo="0.0588"
                    android:valueType="floatType">
                    <aapt:attr name="android:interpolator">
                        <pathInterpolator android:pathData="M 0.0,0.0 c0,0 0,0.758 1.0,1.0" />
                    </aapt:attr>
                </objectAnimator>
                <objectAnimator
                    android:duration="383"
                    android:propertyName="scaleX"
                    android:startOffset="67"
                    android:valueFrom="0.0588"
                    android:valueTo="0.35531"
                    android:valueType="floatType">
                    <aapt:attr name="android:interpolator">
                        <pathInterpolator android:pathData="M 0.0,0.0 c0,0 0,0.758 1.0,1.0" />
                    </aapt:attr>
                </objectAnimator>
                <objectAnimator
                    android:duration="383"
                    android:propertyName="scaleY"
                    android:startOffset="67"
                    android:valueFrom="0.0588"
                    android:valueTo="0.35531"
                    android:valueType="floatType">
                    <aapt:attr name="android:interpolator">
                        <pathInterpolator android:pathData="M 0.0,0.0 c0,0 0,0.758 1.0,1.0" />
                    </aapt:attr>
                </objectAnimator>
                <objectAnimator
                    android:duration="550"
                    android:propertyName="scaleX"
                    android:startOffset="450"
                    android:valueFrom="0.35531"
                    android:valueTo="0.392"
                    android:valueType="floatType">
                    <aapt:attr name="android:interpolator">
                        <pathInterpolator android:pathData="M 0.0,0.0 c0.26,0.728 0.587,1 1.0,1.0" />
                    </aapt:attr>
                </objectAnimator>
                <objectAnimator
                    android:duration="550"
                    android:propertyName="scaleY"
                    android:startOffset="450"
                    android:valueFrom="0.35531"
                    android:valueTo="0.392"
                    android:valueType="floatType">
                    <aapt:attr name="android:interpolator">
                        <pathInterpolator android:pathData="M 0.0,0.0 c0.26,0.728 0.587,1 1.0,1.0" />
                    </aapt:attr>
                </objectAnimator>
            </set>
        </aapt:attr>
    </target>
    <target android:name="_R_G_L_6_G_N_8_T_1">
        <aapt:attr name="android:animation">
            <set android:ordering="together">
                <objectAnimator
                    android:duration="67"
                    android:propertyName="rotation"
                    android:startOffset="0"
                    android:valueFrom="-30"
                    android:valueTo="-30"
                    android:valueType="floatType">
                    <aapt:attr name="android:interpolator">
                        <pathInterpolator android:pathData="M 0.0,0.0 c0,0 0,1 1.0,1.0" />
                    </aapt:attr>
                </objectAnimator>
                <objectAnimator
                    android:duration="933"
                    android:propertyName="rotation"
                    android:startOffset="67"
                    android:valueFrom="-30"
                    android:valueTo="0"
                    android:valueType="floatType">
                    <aapt:attr name="android:interpolator">
                        <pathInterpolator android:pathData="M 0.0,0.0 c0,0 0,1 1.0,1.0" />
                    </aapt:attr>
                </objectAnimator>
            </set>
        </aapt:attr>
    </target>
    <target android:name="_R_G_L_6_G_N_8_T_1">
        <aapt:attr name="android:animation">
            <set android:ordering="together">
                <objectAnimator
                    android:duration="0"
                    android:propertyName="scaleY"
                    android:startOffset="83"
                    android:valueFrom="0"
                    android:valueTo="0.0588"
                    android:valueType="floatType" />
            </set>
        </aapt:attr>
    </target>
    <target android:name="_R_G_L_5_G_D_0_P_0">
        <aapt:attr name="android:animation">
            <set android:ordering="together">
                <objectAnimator
                    android:duration="450"
                    android:propertyName="fillAlpha"
                    android:startOffset="0"
                    android:valueFrom="0"
                    android:valueTo="0"
                    android:valueType="floatType">
                    <aapt:attr name="android:interpolator">
                        <pathInterpolator android:pathData="M 0.0,0.0 c0,0 0,1 1.0,1.0" />
                    </aapt:attr>
                </objectAnimator>
                <objectAnimator
                    android:duration="550"
                    android:propertyName="fillAlpha"
                    android:startOffset="450"
                    android:valueFrom="0"
                    android:valueTo="0.2"
                    android:valueType="floatType">
                    <aapt:attr name="android:interpolator">
                        <pathInterpolator android:pathData="M 0.0,0.0 c0,0 0,1 1.0,1.0" />
                    </aapt:attr>
                </objectAnimator>
            </set>
        </aapt:attr>
    </target>
    <target android:name="_R_G_L_5_G_D_0_P_0">
        <aapt:attr name="android:animation">
            <set android:ordering="together">
                <objectAnimator
                    android:duration="83"
                    android:propertyName="pathData"
                    android:startOffset="0"
                    android:valueFrom="M42.36 -121.5 C42.36,-121.5 53.43,-118.51 53.43,-118.51 C53.43,-118.51 60.31,-72.24 60.31,-72.24 C60.31,-72.24 42.36,-121.5 42.36,-121.5c "
                    android:valueTo="M42.36 -121.5 C42.36,-121.5 53.43,-118.51 53.43,-118.51 C53.43,-118.51 60.31,-72.24 60.31,-72.24 C60.31,-72.24 42.36,-121.5 42.36,-121.5c "
                    android:valueType="pathType">
                    <aapt:attr name="android:interpolator">
                        <pathInterpolator android:pathData="M 0.0,0.0 c0,0 0,1 1.0,1.0" />
                    </aapt:attr>
                </objectAnimator>
                <objectAnimator
                    android:duration="917"
                    android:propertyName="pathData"
                    android:startOffset="83"
                    android:valueFrom="M42.36 -121.5 C42.36,-121.5 53.43,-118.51 53.43,-118.51 C53.43,-118.51 60.31,-72.24 60.31,-72.24 C60.31,-72.24 42.36,-121.5 42.36,-121.5c "
                    android:valueTo="M44.8 -137.25 C44.8,-137.25 55.87,-134.27 55.87,-134.27 C55.87,-134.27 62.75,-88 62.75,-88 C62.75,-88 44.8,-137.25 44.8,-137.25c "
                    android:valueType="pathType">
                    <aapt:attr name="android:interpolator">
                        <pathInterpolator android:pathData="M 0.0,0.0 c0,0 0,1 1.0,1.0" />
                    </aapt:attr>
                </objectAnimator>
            </set>
        </aapt:attr>
    </target>
    <target android:name="_R_G_L_5_G_N_5_T_1">
        <aapt:attr name="android:animation">
            <set android:ordering="together">
                <objectAnimator
                    android:duration="67"
                    android:pathData="M 228.675,226.542C 222.43900000000002,223.727 234.911,229.357 228.675,226.542"
                    android:propertyName="translateXY"
                    android:propertyXName="translateX"
                    android:propertyYName="translateY"
                    android:startOffset="0">
                    <aapt:attr name="android:interpolator">
                        <pathInterpolator android:pathData="M 0.0,0.0 c0,0 0,1 1.0,1.0" />
                    </aapt:attr>
                </objectAnimator>
                <objectAnimator
                    android:duration="933"
                    android:pathData="M 228.675,226.542C 222.43900000000002,223.727 197.493,212.47 191.257,209.655"
                    android:propertyName="translateXY"
                    android:propertyXName="translateX"
                    android:propertyYName="translateY"
                    android:startOffset="67">
                    <aapt:attr name="android:interpolator">
                        <pathInterpolator android:pathData="M 0.0,0.0 c0,0 0,1 1.0,1.0" />
                    </aapt:attr>
                </objectAnimator>
            </set>
        </aapt:attr>
    </target>
    <target android:name="_R_G_L_5_G_N_5_T_1">
        <aapt:attr name="android:animation">
            <set android:ordering="together">
                <objectAnimator
                    android:duration="67"
                    android:propertyName="scaleX"
                    android:startOffset="0"
                    android:valueFrom="0.0784"
                    android:valueTo="0.0784"
                    android:valueType="floatType">
                    <aapt:attr name="android:interpolator">
                        <pathInterpolator android:pathData="M 0.0,0.0 c0,0 0,0.779 1.0,1.0" />
                    </aapt:attr>
                </objectAnimator>
                <objectAnimator
                    android:duration="67"
                    android:propertyName="scaleY"
                    android:startOffset="0"
                    android:valueFrom="0.0784"
                    android:valueTo="0.0784"
                    android:valueType="floatType">
                    <aapt:attr name="android:interpolator">
                        <pathInterpolator android:pathData="M 0.0,0.0 c0,0 0,0.779 1.0,1.0" />
                    </aapt:attr>
                </objectAnimator>
                <objectAnimator
                    android:duration="383"
                    android:propertyName="scaleX"
                    android:startOffset="67"
                    android:valueFrom="0.0784"
                    android:valueTo="0.35988"
                    android:valueType="floatType">
                    <aapt:attr name="android:interpolator">
                        <pathInterpolator android:pathData="M 0.0,0.0 c0,0 0,0.779 1.0,1.0" />
                    </aapt:attr>
                </objectAnimator>
                <objectAnimator
                    android:duration="383"
                    android:propertyName="scaleY"
                    android:startOffset="67"
                    android:valueFrom="0.0784"
                    android:valueTo="0.35988"
                    android:valueType="floatType">
                    <aapt:attr name="android:interpolator">
                        <pathInterpolator android:pathData="M 0.0,0.0 c0,0 0,0.779 1.0,1.0" />
                    </aapt:attr>
                </objectAnimator>
                <objectAnimator
                    android:duration="550"
                    android:propertyName="scaleX"
                    android:startOffset="450"
                    android:valueFrom="0.35988"
                    android:valueTo="0.392"
                    android:valueType="floatType">
                    <aapt:attr name="android:interpolator">
                        <pathInterpolator android:pathData="M 0.0,0.0 c0.263,0.731 0.591,1 1.0,1.0" />
                    </aapt:attr>
                </objectAnimator>
                <objectAnimator
                    android:duration="550"
                    android:propertyName="scaleY"
                    android:startOffset="450"
                    android:valueFrom="0.35988"
                    android:valueTo="0.392"
                    android:valueType="floatType">
                    <aapt:attr name="android:interpolator">
                        <pathInterpolator android:pathData="M 0.0,0.0 c0.263,0.731 0.591,1 1.0,1.0" />
                    </aapt:attr>
                </objectAnimator>
            </set>
        </aapt:attr>
    </target>
    <target android:name="_R_G_L_5_G_N_5_T_1">
        <aapt:attr name="android:animation">
            <set android:ordering="together">
                <objectAnimator
                    android:duration="67"
                    android:propertyName="rotation"
                    android:startOffset="0"
                    android:valueFrom="40"
                    android:valueTo="40"
                    android:valueType="floatType">
                    <aapt:attr name="android:interpolator">
                        <pathInterpolator android:pathData="M 0.0,0.0 c0,0 0,1 1.0,1.0" />
                    </aapt:attr>
                </objectAnimator>
                <objectAnimator
                    android:duration="933"
                    android:propertyName="rotation"
                    android:startOffset="67"
                    android:valueFrom="40"
                    android:valueTo="0"
                    android:valueType="floatType">
                    <aapt:attr name="android:interpolator">
                        <pathInterpolator android:pathData="M 0.0,0.0 c0,0 0,1 1.0,1.0" />
                    </aapt:attr>
                </objectAnimator>
            </set>
        </aapt:attr>
    </target>
    <target android:name="_R_G_L_5_G_N_5_T_1">
        <aapt:attr name="android:animation">
            <set android:ordering="together">
                <objectAnimator
                    android:duration="0"
                    android:propertyName="scaleY"
                    android:startOffset="83"
                    android:valueFrom="0"
                    android:valueTo="0.0784"
                    android:valueType="floatType" />
            </set>
        </aapt:attr>
    </target>
    <target android:name="_R_G_L_4_G_D_0_P_0">
        <aapt:attr name="android:animation">
            <set android:ordering="together">
                <objectAnimator
                    android:duration="52"
                    android:propertyName="fillAlpha"
                    android:startOffset="0"
                    android:valueFrom="0"
                    android:valueTo="0"
                    android:valueType="floatType">
                    <aapt:attr name="android:interpolator">
                        <pathInterpolator android:pathData="M 0.0,0.0 c0.167,0.167 0.833,0.833 1.0,1.0" />
                    </aapt:attr>
                </objectAnimator>
                <objectAnimator
                    android:duration="15"
                    android:propertyName="fillAlpha"
                    android:startOffset="52"
                    android:valueFrom="0"
                    android:valueTo="1"
                    android:valueType="floatType">
                    <aapt:attr name="android:interpolator">
                        <pathInterpolator android:pathData="M 0.0,0.0 c0.167,0.167 0.833,0.833 1.0,1.0" />
                    </aapt:attr>
                </objectAnimator>
            </set>
        </aapt:attr>
    </target>
    <target android:name="_R_G_L_4_G_D_1_P_0">
        <aapt:attr name="android:animation">
            <set android:ordering="together">
                <objectAnimator
                    android:duration="52"
                    android:propertyName="fillAlpha"
                    android:startOffset="0"
                    android:valueFrom="0"
                    android:valueTo="0"
                    android:valueType="floatType">
                    <aapt:attr name="android:interpolator">
                        <pathInterpolator android:pathData="M 0.0,0.0 c0.167,0.167 0.833,0.833 1.0,1.0" />
                    </aapt:attr>
                </objectAnimator>
                <objectAnimator
                    android:duration="15"
                    android:propertyName="fillAlpha"
                    android:startOffset="52"
                    android:valueFrom="0"
                    android:valueTo="0.2"
                    android:valueType="floatType">
                    <aapt:attr name="android:interpolator">
                        <pathInterpolator android:pathData="M 0.0,0.0 c0.167,0.167 0.833,0.833 1.0,1.0" />
                    </aapt:attr>
                </objectAnimator>
            </set>
        </aapt:attr>
    </target>
    <target android:name="_R_G_L_4_G_T_1">
        <aapt:attr name="android:animation">
            <set android:ordering="together">
                <objectAnimator
                    android:duration="67"
                    android:pathData="M 228.675,226.542C 222.43900000000002,223.727 234.911,229.357 228.675,226.542"
                    android:propertyName="translateXY"
                    android:propertyXName="translateX"
                    android:propertyYName="translateY"
                    android:startOffset="0">
                    <aapt:attr name="android:interpolator">
                        <pathInterpolator android:pathData="M 0.0,0.0 c0,0 0,1 1.0,1.0" />
                    </aapt:attr>
                </objectAnimator>
                <objectAnimator
                    android:duration="933"
                    android:pathData="M 228.675,226.542C 222.43900000000002,223.727 197.493,212.47 191.257,209.655"
                    android:propertyName="translateXY"
                    android:propertyXName="translateX"
                    android:propertyYName="translateY"
                    android:startOffset="67">
                    <aapt:attr name="android:interpolator">
                        <pathInterpolator android:pathData="M 0.0,0.0 c0,0 0,1 1.0,1.0" />
                    </aapt:attr>
                </objectAnimator>
            </set>
        </aapt:attr>
    </target>
    <target android:name="_R_G_L_4_G_T_1">
        <aapt:attr name="android:animation">
            <set android:ordering="together">
                <objectAnimator
                    android:duration="67"
                    android:propertyName="scaleX"
                    android:startOffset="0"
                    android:valueFrom="0.0784"
                    android:valueTo="0.0784"
                    android:valueType="floatType">
                    <aapt:attr name="android:interpolator">
                        <pathInterpolator android:pathData="M 0.0,0.0 c0,0 0,0.779 1.0,1.0" />
                    </aapt:attr>
                </objectAnimator>
                <objectAnimator
                    android:duration="67"
                    android:propertyName="scaleY"
                    android:startOffset="0"
                    android:valueFrom="0.0784"
                    android:valueTo="0.0784"
                    android:valueType="floatType">
                    <aapt:attr name="android:interpolator">
                        <pathInterpolator android:pathData="M 0.0,0.0 c0,0 0,0.779 1.0,1.0" />
                    </aapt:attr>
                </objectAnimator>
                <objectAnimator
                    android:duration="383"
                    android:propertyName="scaleX"
                    android:startOffset="67"
                    android:valueFrom="0.0784"
                    android:valueTo="0.35988"
                    android:valueType="floatType">
                    <aapt:attr name="android:interpolator">
                        <pathInterpolator android:pathData="M 0.0,0.0 c0,0 0,0.779 1.0,1.0" />
                    </aapt:attr>
                </objectAnimator>
                <objectAnimator
                    android:duration="383"
                    android:propertyName="scaleY"
                    android:startOffset="67"
                    android:valueFrom="0.0784"
                    android:valueTo="0.35988"
                    android:valueType="floatType">
                    <aapt:attr name="android:interpolator">
                        <pathInterpolator android:pathData="M 0.0,0.0 c0,0 0,0.779 1.0,1.0" />
                    </aapt:attr>
                </objectAnimator>
                <objectAnimator
                    android:duration="550"
                    android:propertyName="scaleX"
                    android:startOffset="450"
                    android:valueFrom="0.35988"
                    android:valueTo="0.392"
                    android:valueType="floatType">
                    <aapt:attr name="android:interpolator">
                        <pathInterpolator android:pathData="M 0.0,0.0 c0.263,0.731 0.591,1 1.0,1.0" />
                    </aapt:attr>
                </objectAnimator>
                <objectAnimator
                    android:duration="550"
                    android:propertyName="scaleY"
                    android:startOffset="450"
                    android:valueFrom="0.35988"
                    android:valueTo="0.392"
                    android:valueType="floatType">
                    <aapt:attr name="android:interpolator">
                        <pathInterpolator android:pathData="M 0.0,0.0 c0.263,0.731 0.591,1 1.0,1.0" />
                    </aapt:attr>
                </objectAnimator>
            </set>
        </aapt:attr>
    </target>
    <target android:name="_R_G_L_4_G_T_1">
        <aapt:attr name="android:animation">
            <set android:ordering="together">
                <objectAnimator
                    android:duration="67"
                    android:propertyName="rotation"
                    android:startOffset="0"
                    android:valueFrom="40"
                    android:valueTo="40"
                    android:valueType="floatType">
                    <aapt:attr name="android:interpolator">
                        <pathInterpolator android:pathData="M 0.0,0.0 c0,0 0,1 1.0,1.0" />
                    </aapt:attr>
                </objectAnimator>
                <objectAnimator
                    android:duration="933"
                    android:propertyName="rotation"
                    android:startOffset="67"
                    android:valueFrom="40"
                    android:valueTo="0"
                    android:valueType="floatType">
                    <aapt:attr name="android:interpolator">
                        <pathInterpolator android:pathData="M 0.0,0.0 c0,0 0,1 1.0,1.0" />
                    </aapt:attr>
                </objectAnimator>
            </set>
        </aapt:attr>
    </target>
    <target android:name="_R_G_L_4_G_T_1">
        <aapt:attr name="android:animation">
            <set android:ordering="together">
                <objectAnimator
                    android:duration="0"
                    android:propertyName="scaleY"
                    android:startOffset="67"
                    android:valueFrom="0"
                    android:valueTo="0.0784"
                    android:valueType="floatType" />
            </set>
        </aapt:attr>
    </target>
    <target android:name="_R_G_L_3_G_D_0_P_0">
        <aapt:attr name="android:animation">
            <set android:ordering="together">
                <objectAnimator
                    android:duration="33"
                    android:propertyName="fillAlpha"
                    android:startOffset="0"
                    android:valueFrom="0"
                    android:valueTo="0"
                    android:valueType="floatType">
                    <aapt:attr name="android:interpolator">
                        <pathInterpolator android:pathData="M 0.0,0.0 c0.167,0.167 0.833,0.833 1.0,1.0" />
                    </aapt:attr>
                </objectAnimator>
                <objectAnimator
                    android:duration="18"
                    android:propertyName="fillAlpha"
                    android:startOffset="33"
                    android:valueFrom="0"
                    android:valueTo="1"
                    android:valueType="floatType">
                    <aapt:attr name="android:interpolator">
                        <pathInterpolator android:pathData="M 0.0,0.0 c0.167,0.167 0.833,0.833 1.0,1.0" />
                    </aapt:attr>
                </objectAnimator>
            </set>
        </aapt:attr>
    </target>
    <target android:name="_R_G_L_3_G_D_1_P_0">
        <aapt:attr name="android:animation">
            <set android:ordering="together">
                <objectAnimator
                    android:duration="33"
                    android:propertyName="fillAlpha"
                    android:startOffset="0"
                    android:valueFrom="0"
                    android:valueTo="0"
                    android:valueType="floatType">
                    <aapt:attr name="android:interpolator">
                        <pathInterpolator android:pathData="M 0.0,0.0 c0.167,0.167 0.833,0.833 1.0,1.0" />
                    </aapt:attr>
                </objectAnimator>
                <objectAnimator
                    android:duration="18"
                    android:propertyName="fillAlpha"
                    android:startOffset="33"
                    android:valueFrom="0"
                    android:valueTo="0.2"
                    android:valueType="floatType">
                    <aapt:attr name="android:interpolator">
                        <pathInterpolator android:pathData="M 0.0,0.0 c0.167,0.167 0.833,0.833 1.0,1.0" />
                    </aapt:attr>
                </objectAnimator>
            </set>
        </aapt:attr>
    </target>
    <target android:name="_R_G_L_3_G_D_2_P_0">
        <aapt:attr name="android:animation">
            <set android:ordering="together">
                <objectAnimator
                    android:duration="33"
                    android:propertyName="fillAlpha"
                    android:startOffset="0"
                    android:valueFrom="0"
                    android:valueTo="0"
                    android:valueType="floatType">
                    <aapt:attr name="android:interpolator">
                        <pathInterpolator android:pathData="M 0.0,0.0 c0.167,0.167 0.833,0.833 1.0,1.0" />
                    </aapt:attr>
                </objectAnimator>
                <objectAnimator
                    android:duration="18"
                    android:propertyName="fillAlpha"
                    android:startOffset="33"
                    android:valueFrom="0"
                    android:valueTo="0.2"
                    android:valueType="floatType">
                    <aapt:attr name="android:interpolator">
                        <pathInterpolator android:pathData="M 0.0,0.0 c0.167,0.167 0.833,0.833 1.0,1.0" />
                    </aapt:attr>
                </objectAnimator>
            </set>
        </aapt:attr>
    </target>
    <target android:name="_R_G_L_3_G_T_1">
        <aapt:attr name="android:animation">
            <set android:ordering="together">
                <objectAnimator
                    android:duration="52"
                    android:pathData="M 216,236.604C 216,236.568 216,236.61100000000002 216,236.604"
                    android:propertyName="translateXY"
                    android:propertyXName="translateX"
                    android:propertyYName="translateY"
                    android:startOffset="0">
                    <aapt:attr name="android:interpolator">
                        <pathInterpolator android:pathData="M 0.0,0.0 c0,0 0,1 1.0,1.0" />
                    </aapt:attr>
                </objectAnimator>
                <objectAnimator
                    android:duration="948"
                    android:pathData="M 216,236.604C 216,236.568 216,236.34900000000002 216,236.342"
                    android:propertyName="translateXY"
                    android:propertyXName="translateX"
                    android:propertyYName="translateY"
                    android:startOffset="52">
                    <aapt:attr name="android:interpolator">
                        <pathInterpolator android:pathData="M 0.0,0.0 c0,0 0,1 1.0,1.0" />
                    </aapt:attr>
                </objectAnimator>
            </set>
        </aapt:attr>
    </target>
    <target android:name="_R_G_L_3_G_T_1">
        <aapt:attr name="android:animation">
            <set android:ordering="together">
                <objectAnimator
                    android:duration="52"
                    android:propertyName="scaleX"
                    android:startOffset="0"
                    android:valueFrom="0.098"
                    android:valueTo="0.098"
                    android:valueType="floatType">
                    <aapt:attr name="android:interpolator">
                        <pathInterpolator android:pathData="M 0.0,0.0 c0,0 0,0.789 1.0,1.0" />
                    </aapt:attr>
                </objectAnimator>
                <objectAnimator
                    android:duration="52"
                    android:propertyName="scaleY"
                    android:startOffset="0"
                    android:valueFrom="0.098"
                    android:valueTo="0.098"
                    android:valueType="floatType">
                    <aapt:attr name="android:interpolator">
                        <pathInterpolator android:pathData="M 0.0,0.0 c0,0 0,0.789 1.0,1.0" />
                    </aapt:attr>
                </objectAnimator>
                <objectAnimator
                    android:duration="398"
                    android:propertyName="scaleX"
                    android:startOffset="52"
                    android:valueFrom="0.098"
                    android:valueTo="0.36391"
                    android:valueType="floatType">
                    <aapt:attr name="android:interpolator">
                        <pathInterpolator android:pathData="M 0.0,0.0 c0,0 0,0.789 1.0,1.0" />
                    </aapt:attr>
                </objectAnimator>
                <objectAnimator
                    android:duration="398"
                    android:propertyName="scaleY"
                    android:startOffset="52"
                    android:valueFrom="0.098"
                    android:valueTo="0.36391"
                    android:valueType="floatType">
                    <aapt:attr name="android:interpolator">
                        <pathInterpolator android:pathData="M 0.0,0.0 c0,0 0,0.789 1.0,1.0" />
                    </aapt:attr>
                </objectAnimator>
                <objectAnimator
                    android:duration="550"
                    android:propertyName="scaleX"
                    android:startOffset="450"
                    android:valueFrom="0.36391"
                    android:valueTo="0.392"
                    android:valueType="floatType">
                    <aapt:attr name="android:interpolator">
                        <pathInterpolator android:pathData="M 0.0,0.0 c0.266,0.733 0.594,1 1.0,1.0" />
                    </aapt:attr>
                </objectAnimator>
                <objectAnimator
                    android:duration="550"
                    android:propertyName="scaleY"
                    android:startOffset="450"
                    android:valueFrom="0.36391"
                    android:valueTo="0.392"
                    android:valueType="floatType">
                    <aapt:attr name="android:interpolator">
                        <pathInterpolator android:pathData="M 0.0,0.0 c0.266,0.733 0.594,1 1.0,1.0" />
                    </aapt:attr>
                </objectAnimator>
            </set>
        </aapt:attr>
    </target>
    <target android:name="_R_G_L_3_G_T_1">
        <aapt:attr name="android:animation">
            <set android:ordering="together">
                <objectAnimator
                    android:duration="0"
                    android:propertyName="scaleY"
                    android:startOffset="50"
                    android:valueFrom="0"
                    android:valueTo="0.098"
                    android:valueType="floatType" />
            </set>
        </aapt:attr>
    </target>
    <target android:name="_R_G_L_2_G_D_0_P_0">
        <aapt:attr name="android:animation">
            <set android:ordering="together">
                <objectAnimator
                    android:duration="52"
                    android:propertyName="fillAlpha"
                    android:startOffset="0"
                    android:valueFrom="0"
                    android:valueTo="0"
                    android:valueType="floatType">
                    <aapt:attr name="android:interpolator">
                        <pathInterpolator android:pathData="M 0.0,0.0 c0,0 0,1 1.0,1.0" />
                    </aapt:attr>
                </objectAnimator>
                <objectAnimator
                    android:duration="948"
                    android:propertyName="fillAlpha"
                    android:startOffset="52"
                    android:valueFrom="0"
                    android:valueTo="0.2"
                    android:valueType="floatType">
                    <aapt:attr name="android:interpolator">
                        <pathInterpolator android:pathData="M 0.0,0.0 c0,0 0,1 1.0,1.0" />
                    </aapt:attr>
                </objectAnimator>
            </set>
        </aapt:attr>
    </target>
    <target android:name="_R_G_L_2_G_D_0_P_0">
        <aapt:attr name="android:animation">
            <set android:ordering="together">
                <objectAnimator
                    android:duration="52"
                    android:propertyName="pathData"
                    android:startOffset="0"
                    android:valueFrom=" M-74.67 64.05 C-74.67,64.05 -74.67,66.72 -74.67,66.72 C-74.67,66.72 -40.8,66.72 -40.8,66.72 C-40.56,65.84 -40.32,64.96 -40.16,64.05 C-40.16,64.05 -74.67,64.05 -74.67,64.05c  M-74.67 114.67 C-109.57,114.67 -137.92,86.72 -138.64,52 C-138.64,52.45 -138.67,52.88 -138.67,53.33 C-138.67,88.67 -110,117.33 -74.67,117.33 C-37.71,117.33 -13.39,91.36 -13.39,54.8 C-13.39,54.51 -13.41,54.24 -13.41,53.95 C-14.21,89.55 -38.32,114.67 -74.67,114.67c  M-50.11 21.6 C-56.77,15.25 -65.23,12 -74.67,12 C-95.63,12 -112.61,29.71 -112.61,50.67 C-112.61,51.12 -112.56,51.55 -112.53,52 C-111.81,31.63 -95.17,14.67 -74.67,14.67 C-65.23,14.67 -56.77,17.92 -50.11,24.27 C-50.11,24.27 -50.11,24.27 -50.11,24.27 C-50.11,24.27 -30.51,4.67 -30.51,4.67 C-30.96,4.24 -31.47,3.87 -31.95,3.44 C-31.95,3.44 -50.11,21.6 -50.11,21.6 C-50.11,21.6 -50.11,21.6 -50.11,21.6c M15.35 114.67 C15.35,114.67 15.33,114.67 15.33,114.67 C15.33,114.67 15.33,117.33 15.33,117.33 C15.33,117.33 15.35,117.33 15.35,117.33 C18.29,117.33 20.69,114.93 20.69,112 C20.69,112 20.69,109.33 20.69,109.33 C20.69,112.27 18.29,114.67 15.35,114.67c M15.33 64 C15.33,64 15.33,64 15.33,64 C15.33,64 15.33,66.67 15.33,66.67 C15.33,66.67 15.33,66.67 15.33,66.67 C18.27,66.67 20.67,64.27 20.67,61.33 C20.67,61.33 20.67,58.67 20.67,58.67 C20.67,61.6 18.27,64 15.33,64c M20.69 10.67 C20.69,10.67 20.69,8 20.69,8 C20.69,10.93 18.29,13.33 15.35,13.33 C15.35,13.33 15.33,13.33 15.33,13.33 C15.33,13.33 15.33,16 15.33,16 C15.33,16 15.35,16 15.35,16 C18.29,16 20.69,13.6 20.69,10.67c "
                    android:valueTo=" M-74.67 64.05 C-74.67,64.05 -74.67,66.72 -74.67,66.72 C-74.67,66.72 -40.8,66.72 -40.8,66.72 C-40.56,65.84 -40.32,64.96 -40.16,64.05 C-40.16,64.05 -74.67,64.05 -74.67,64.05c  M-74.67 114.67 C-109.57,114.67 -137.92,86.72 -138.64,52 C-138.64,52.45 -138.67,52.88 -138.67,53.33 C-138.67,88.67 -110,117.33 -74.67,117.33 C-37.71,117.33 -13.39,91.36 -13.39,54.8 C-13.39,54.51 -13.41,54.24 -13.41,53.95 C-14.21,89.55 -38.32,114.67 -74.67,114.67c  M-50.11 21.6 C-56.77,15.25 -65.23,12 -74.67,12 C-95.63,12 -112.61,29.71 -112.61,50.67 C-112.61,51.12 -112.56,51.55 -112.53,52 C-111.81,31.63 -95.17,14.67 -74.67,14.67 C-65.23,14.67 -56.77,17.92 -50.11,24.27 C-50.11,24.27 -50.11,24.27 -50.11,24.27 C-50.11,24.27 -30.51,4.67 -30.51,4.67 C-30.96,4.24 -31.47,3.87 -31.95,3.44 C-31.95,3.44 -50.11,21.6 -50.11,21.6 C-50.11,21.6 -50.11,21.6 -50.11,21.6c M15.35 114.67 C15.35,114.67 15.33,114.67 15.33,114.67 C15.33,114.67 15.33,117.33 15.33,117.33 C15.33,117.33 15.35,117.33 15.35,117.33 C18.29,117.33 20.69,114.93 20.69,112 C20.69,112 20.69,109.33 20.69,109.33 C20.69,112.27 18.29,114.67 15.35,114.67c M15.33 64 C15.33,64 15.33,64 15.33,64 C15.33,64 15.33,66.67 15.33,66.67 C15.33,66.67 15.33,66.67 15.33,66.67 C18.27,66.67 20.67,64.27 20.67,61.33 C20.67,61.33 20.67,58.67 20.67,58.67 C20.67,61.6 18.27,64 15.33,64c M20.69 10.67 C20.69,10.67 20.69,8 20.69,8 C20.69,10.93 18.29,13.33 15.35,13.33 C15.35,13.33 15.33,13.33 15.33,13.33 C15.33,13.33 15.33,16 15.33,16 C15.33,16 15.35,16 15.35,16 C18.29,16 20.69,13.6 20.69,10.67c "
                    android:valueType="pathType">
                    <aapt:attr name="android:interpolator">
                        <pathInterpolator android:pathData="M 0.0,0.0 c0,0 0,1 1.0,1.0" />
                    </aapt:attr>
                </objectAnimator>
                <objectAnimator
                    android:duration="948"
                    android:propertyName="pathData"
                    android:startOffset="52"
                    android:valueFrom=" M-74.67 64.05 C-74.67,64.05 -74.67,66.72 -74.67,66.72 C-74.67,66.72 -40.8,66.72 -40.8,66.72 C-40.56,65.84 -40.32,64.96 -40.16,64.05 C-40.16,64.05 -74.67,64.05 -74.67,64.05c  M-74.67 114.67 C-109.57,114.67 -137.92,86.72 -138.64,52 C-138.64,52.45 -138.67,52.88 -138.67,53.33 C-138.67,88.67 -110,117.33 -74.67,117.33 C-37.71,117.33 -13.39,91.36 -13.39,54.8 C-13.39,54.51 -13.41,54.24 -13.41,53.95 C-14.21,89.55 -38.32,114.67 -74.67,114.67c  M-50.11 21.6 C-56.77,15.25 -65.23,12 -74.67,12 C-95.63,12 -112.61,29.71 -112.61,50.67 C-112.61,51.12 -112.56,51.55 -112.53,52 C-111.81,31.63 -95.17,14.67 -74.67,14.67 C-65.23,14.67 -56.77,17.92 -50.11,24.27 C-50.11,24.27 -50.11,24.27 -50.11,24.27 C-50.11,24.27 -30.51,4.67 -30.51,4.67 C-30.96,4.24 -31.47,3.87 -31.95,3.44 C-31.95,3.44 -50.11,21.6 -50.11,21.6 C-50.11,21.6 -50.11,21.6 -50.11,21.6c M15.35 114.67 C15.35,114.67 15.33,114.67 15.33,114.67 C15.33,114.67 15.33,117.33 15.33,117.33 C15.33,117.33 15.35,117.33 15.35,117.33 C18.29,117.33 20.69,114.93 20.69,112 C20.69,112 20.69,109.33 20.69,109.33 C20.69,112.27 18.29,114.67 15.35,114.67c M15.33 64 C15.33,64 15.33,64 15.33,64 C15.33,64 15.33,66.67 15.33,66.67 C15.33,66.67 15.33,66.67 15.33,66.67 C18.27,66.67 20.67,64.27 20.67,61.33 C20.67,61.33 20.67,58.67 20.67,58.67 C20.67,61.6 18.27,64 15.33,64c M20.69 10.67 C20.69,10.67 20.69,8 20.69,8 C20.69,10.93 18.29,13.33 15.35,13.33 C15.35,13.33 15.33,13.33 15.33,13.33 C15.33,13.33 15.33,16 15.33,16 C15.33,16 15.35,16 15.35,16 C18.29,16 20.69,13.6 20.69,10.67c "
                    android:valueTo=" M-74.67 64.05 C-74.67,64.05 -74.67,66.72 -74.67,66.72 C-74.67,66.72 -40.8,66.72 -40.8,66.72 C-40.56,65.84 -40.32,64.96 -40.16,64.05 C-40.16,64.05 -74.67,64.05 -74.67,64.05c  M-74.67 114.67 C-109.57,114.67 -137.92,86.72 -138.64,52 C-138.64,52.45 -138.67,52.88 -138.67,53.33 C-138.67,88.67 -110,117.33 -74.67,117.33 C-37.71,117.33 -13.39,91.36 -13.39,54.8 C-13.39,54.51 -13.41,54.24 -13.41,53.95 C-14.21,89.55 -38.32,114.67 -74.67,114.67c  M-50.11 21.6 C-56.77,15.25 -65.23,12 -74.67,12 C-95.63,12 -112.61,29.71 -112.61,50.67 C-112.61,51.12 -112.56,51.55 -112.53,52 C-111.81,31.63 -95.17,14.67 -74.67,14.67 C-65.23,14.67 -56.77,17.92 -50.11,24.27 C-50.11,24.27 -50.11,24.27 -50.11,24.27 C-50.11,24.27 -30.51,4.67 -30.51,4.67 C-30.96,4.24 -31.47,3.87 -31.95,3.44 C-31.95,3.44 -50.11,21.6 -50.11,21.6 C-50.11,21.6 -50.11,21.6 -50.11,21.6c M116.67 114.67 C116.67,114.67 15.33,114.67 15.33,114.67 C15.33,114.67 15.33,117.33 15.33,117.33 C15.33,117.33 116.67,117.33 116.67,117.33 C119.6,117.33 122,114.93 122,112 C122,112 122,109.33 122,109.33 C122,112.27 119.6,114.67 116.67,114.67c M132.67 64 C132.67,64 15.33,64 15.33,64 C15.33,64 15.33,66.67 15.33,66.67 C15.33,66.67 132.67,66.67 132.67,66.67 C135.6,66.67 138,64.27 138,61.33 C138,61.33 138,58.67 138,58.67 C138,61.6 135.6,64 132.67,64c M122 10.67 C122,10.67 122,8 122,8 C122,10.93 119.6,13.33 116.67,13.33 C116.67,13.33 15.33,13.33 15.33,13.33 C15.33,13.33 15.33,16 15.33,16 C15.33,16 116.67,16 116.67,16 C119.6,16 122,13.6 122,10.67c "
                    android:valueType="pathType">
                    <aapt:attr name="android:interpolator">
                        <pathInterpolator android:pathData="M 0.0,0.0 c0,0 0,1 1.0,1.0" />
                    </aapt:attr>
                </objectAnimator>
            </set>
        </aapt:attr>
    </target>
    <target android:name="_R_G_L_2_G_N_4_T_1">
        <aapt:attr name="android:animation">
            <set android:ordering="together">
                <objectAnimator
                    android:duration="52"
                    android:pathData="M 216,236.604C 216,236.568 216,236.61100000000002 216,236.604"
                    android:propertyName="translateXY"
                    android:propertyXName="translateX"
                    android:propertyYName="translateY"
                    android:startOffset="0">
                    <aapt:attr name="android:interpolator">
                        <pathInterpolator android:pathData="M 0.0,0.0 c0,0 0,1 1.0,1.0" />
                    </aapt:attr>
                </objectAnimator>
                <objectAnimator
                    android:duration="948"
                    android:pathData="M 216,236.604C 216,236.568 216,236.34900000000002 216,236.342"
                    android:propertyName="translateXY"
                    android:propertyXName="translateX"
                    android:propertyYName="translateY"
                    android:startOffset="52">
                    <aapt:attr name="android:interpolator">
                        <pathInterpolator android:pathData="M 0.0,0.0 c0,0 0,1 1.0,1.0" />
                    </aapt:attr>
                </objectAnimator>
            </set>
        </aapt:attr>
    </target>
    <target android:name="_R_G_L_2_G_N_4_T_1">
        <aapt:attr name="android:animation">
            <set android:ordering="together">
                <objectAnimator
                    android:duration="52"
                    android:propertyName="scaleX"
                    android:startOffset="0"
                    android:valueFrom="0.098"
                    android:valueTo="0.098"
                    android:valueType="floatType">
                    <aapt:attr name="android:interpolator">
                        <pathInterpolator android:pathData="M 0.0,0.0 c0,0 0,0.789 1.0,1.0" />
                    </aapt:attr>
                </objectAnimator>
                <objectAnimator
                    android:duration="52"
                    android:propertyName="scaleY"
                    android:startOffset="0"
                    android:valueFrom="0.098"
                    android:valueTo="0.098"
                    android:valueType="floatType">
                    <aapt:attr name="android:interpolator">
                        <pathInterpolator android:pathData="M 0.0,0.0 c0,0 0,0.789 1.0,1.0" />
                    </aapt:attr>
                </objectAnimator>
                <objectAnimator
                    android:duration="398"
                    android:propertyName="scaleX"
                    android:startOffset="52"
                    android:valueFrom="0.098"
                    android:valueTo="0.36391"
                    android:valueType="floatType">
                    <aapt:attr name="android:interpolator">
                        <pathInterpolator android:pathData="M 0.0,0.0 c0,0 0,0.789 1.0,1.0" />
                    </aapt:attr>
                </objectAnimator>
                <objectAnimator
                    android:duration="398"
                    android:propertyName="scaleY"
                    android:startOffset="52"
                    android:valueFrom="0.098"
                    android:valueTo="0.36391"
                    android:valueType="floatType">
                    <aapt:attr name="android:interpolator">
                        <pathInterpolator android:pathData="M 0.0,0.0 c0,0 0,0.789 1.0,1.0" />
                    </aapt:attr>
                </objectAnimator>
                <objectAnimator
                    android:duration="550"
                    android:propertyName="scaleX"
                    android:startOffset="450"
                    android:valueFrom="0.36391"
                    android:valueTo="0.392"
                    android:valueType="floatType">
                    <aapt:attr name="android:interpolator">
                        <pathInterpolator android:pathData="M 0.0,0.0 c0.266,0.733 0.594,1 1.0,1.0" />
                    </aapt:attr>
                </objectAnimator>
                <objectAnimator
                    android:duration="550"
                    android:propertyName="scaleY"
                    android:startOffset="450"
                    android:valueFrom="0.36391"
                    android:valueTo="0.392"
                    android:valueType="floatType">
                    <aapt:attr name="android:interpolator">
                        <pathInterpolator android:pathData="M 0.0,0.0 c0.266,0.733 0.594,1 1.0,1.0" />
                    </aapt:attr>
                </objectAnimator>
            </set>
        </aapt:attr>
    </target>
    <target android:name="_R_G_L_2_G_N_4_T_1">
        <aapt:attr name="android:animation">
            <set android:ordering="together">
                <objectAnimator
                    android:duration="0"
                    android:propertyName="scaleY"
                    android:startOffset="50"
                    android:valueFrom="0"
                    android:valueTo="0.098"
                    android:valueType="floatType" />
            </set>
        </aapt:attr>
    </target>
    <target android:name="_R_G_L_1_G_D_0_P_0">
        <aapt:attr name="android:animation">
            <set android:ordering="together">
                <objectAnimator
                    android:duration="33"
                    android:propertyName="fillAlpha"
                    android:startOffset="0"
                    android:valueFrom="0"
                    android:valueTo="0"
                    android:valueType="floatType">
                    <aapt:attr name="android:interpolator">
                        <pathInterpolator android:pathData="M 0.0,0.0 c0.167,0.167 0.833,0.833 1.0,1.0" />
                    </aapt:attr>
                </objectAnimator>
                <objectAnimator
                    android:duration="18"
                    android:propertyName="fillAlpha"
                    android:startOffset="33"
                    android:valueFrom="0"
                    android:valueTo="1"
                    android:valueType="floatType">
                    <aapt:attr name="android:interpolator">
                        <pathInterpolator android:pathData="M 0.0,0.0 c0.167,0.167 0.833,0.833 1.0,1.0" />
                    </aapt:attr>
                </objectAnimator>
            </set>
        </aapt:attr>
    </target>
    <target android:name="_R_G_L_1_G_D_0_P_0">
        <aapt:attr name="android:animation">
            <set android:ordering="together">
                <objectAnimator
                    android:duration="52"
                    android:propertyName="pathData"
                    android:startOffset="0"
                    android:valueFrom=" M-75.33 40 C-75.33,40 -75.33,64.05 -75.33,64.05 C-75.33,64.05 -40.83,64.05 -40.83,64.05 C-43.68,78.72 -56.56,89.33 -75.33,89.33 C-96.29,89.33 -113.28,71.63 -113.28,50.67 C-113.28,29.71 -96.29,12 -75.33,12 C-65.89,12 -57.44,15.25 -50.77,21.6 C-50.77,21.6 -50.77,21.6 -50.77,21.6 C-50.77,21.6 -32.51,3.33 -32.51,3.33 C-43.6,-6.99 -58.08,-13.33 -75.33,-13.33 C-110.67,-13.33 -139.33,15.33 -139.33,50.67 C-139.33,86 -110.67,114.67 -75.33,114.67 C-38.37,114.67 -14.05,88.69 -14.05,52.13 C-14.05,47.95 -14.45,43.89 -15.07,40 C-15.07,40 -75.33,40 -75.33,40c M15.34 114.67 C15.34,114.67 15.33,114.67 15.33,114.67 C15.33,114.67 15.33,88 15.33,88 C15.33,88 15.34,88 15.34,88 C18.27,88 20.67,90.4 20.67,93.33 C20.67,93.33 20.67,109.33 20.67,109.33 C20.67,112.27 18.27,114.67 15.34,114.67c M15.35 63.99 C15.35,63.99 15.33,64 15.33,64 C15.33,64 15.33,37.33 15.33,37.33 C15.33,37.33 15.35,37.32 15.35,37.32 C18.29,37.32 20.69,39.72 20.69,42.66 C20.69,42.66 20.69,58.66 20.69,58.66 C20.69,61.59 18.29,63.99 15.35,63.99c M15.31 13.33 C15.31,13.33 15.33,13.33 15.33,13.33 C15.33,13.33 15.33,-13.33 15.33,-13.33 C15.33,-13.33 15.31,-13.34 15.31,-13.34 C18.24,-13.34 20.64,-10.94 20.64,-8.01 C20.64,-8.01 20.64,8 20.64,8 C20.64,10.93 18.24,13.33 15.31,13.33c "
                    android:valueTo=" M-75.33 40 C-75.33,40 -75.33,64.05 -75.33,64.05 C-75.33,64.05 -40.83,64.05 -40.83,64.05 C-43.68,78.72 -56.56,89.33 -75.33,89.33 C-96.29,89.33 -113.28,71.63 -113.28,50.67 C-113.28,29.71 -96.29,12 -75.33,12 C-65.89,12 -57.44,15.25 -50.77,21.6 C-50.77,21.6 -50.77,21.6 -50.77,21.6 C-50.77,21.6 -32.51,3.33 -32.51,3.33 C-43.6,-6.99 -58.08,-13.33 -75.33,-13.33 C-110.67,-13.33 -139.33,15.33 -139.33,50.67 C-139.33,86 -110.67,114.67 -75.33,114.67 C-38.37,114.67 -14.05,88.69 -14.05,52.13 C-14.05,47.95 -14.45,43.89 -15.07,40 C-15.07,40 -75.33,40 -75.33,40c M15.34 114.67 C15.34,114.67 15.33,114.67 15.33,114.67 C15.33,114.67 15.33,88 15.33,88 C15.33,88 15.34,88 15.34,88 C18.27,88 20.67,90.4 20.67,93.33 C20.67,93.33 20.67,109.33 20.67,109.33 C20.67,112.27 18.27,114.67 15.34,114.67c M15.35 63.99 C15.35,63.99 15.33,64 15.33,64 C15.33,64 15.33,37.33 15.33,37.33 C15.33,37.33 15.35,37.32 15.35,37.32 C18.29,37.32 20.69,39.72 20.69,42.66 C20.69,42.66 20.69,58.66 20.69,58.66 C20.69,61.59 18.29,63.99 15.35,63.99c M15.31 13.33 C15.31,13.33 15.33,13.33 15.33,13.33 C15.33,13.33 15.33,-13.33 15.33,-13.33 C15.33,-13.33 15.31,-13.34 15.31,-13.34 C18.24,-13.34 20.64,-10.94 20.64,-8.01 C20.64,-8.01 20.64,8 20.64,8 C20.64,10.93 18.24,13.33 15.31,13.33c "
                    android:valueType="pathType">
                    <aapt:attr name="android:interpolator">
                        <pathInterpolator android:pathData="M 0.0,0.0 c0,0 0,1 1.0,1.0" />
                    </aapt:attr>
                </objectAnimator>
                <objectAnimator
                    android:duration="948"
                    android:propertyName="pathData"
                    android:startOffset="52"
                    android:valueFrom=" M-75.33 40 C-75.33,40 -75.33,64.05 -75.33,64.05 C-75.33,64.05 -40.83,64.05 -40.83,64.05 C-43.68,78.72 -56.56,89.33 -75.33,89.33 C-96.29,89.33 -113.28,71.63 -113.28,50.67 C-113.28,29.71 -96.29,12 -75.33,12 C-65.89,12 -57.44,15.25 -50.77,21.6 C-50.77,21.6 -50.77,21.6 -50.77,21.6 C-50.77,21.6 -32.51,3.33 -32.51,3.33 C-43.6,-6.99 -58.08,-13.33 -75.33,-13.33 C-110.67,-13.33 -139.33,15.33 -139.33,50.67 C-139.33,86 -110.67,114.67 -75.33,114.67 C-38.37,114.67 -14.05,88.69 -14.05,52.13 C-14.05,47.95 -14.45,43.89 -15.07,40 C-15.07,40 -75.33,40 -75.33,40c M15.34 114.67 C15.34,114.67 15.33,114.67 15.33,114.67 C15.33,114.67 15.33,88 15.33,88 C15.33,88 15.34,88 15.34,88 C18.27,88 20.67,90.4 20.67,93.33 C20.67,93.33 20.67,109.33 20.67,109.33 C20.67,112.27 18.27,114.67 15.34,114.67c M15.35 63.99 C15.35,63.99 15.33,64 15.33,64 C15.33,64 15.33,37.33 15.33,37.33 C15.33,37.33 15.35,37.32 15.35,37.32 C18.29,37.32 20.69,39.72 20.69,42.66 C20.69,42.66 20.69,58.66 20.69,58.66 C20.69,61.59 18.29,63.99 15.35,63.99c M15.31 13.33 C15.31,13.33 15.33,13.33 15.33,13.33 C15.33,13.33 15.33,-13.33 15.33,-13.33 C15.33,-13.33 15.31,-13.34 15.31,-13.34 C18.24,-13.34 20.64,-10.94 20.64,-8.01 C20.64,-8.01 20.64,8 20.64,8 C20.64,10.93 18.24,13.33 15.31,13.33c "
                    android:valueTo=" M-75.33 40 C-75.33,40 -75.33,64.05 -75.33,64.05 C-75.33,64.05 -40.83,64.05 -40.83,64.05 C-43.68,78.72 -56.56,89.33 -75.33,89.33 C-96.29,89.33 -113.28,71.63 -113.28,50.67 C-113.28,29.71 -96.29,12 -75.33,12 C-65.89,12 -57.44,15.25 -50.77,21.6 C-50.77,21.6 -50.77,21.6 -50.77,21.6 C-50.77,21.6 -32.51,3.33 -32.51,3.33 C-43.6,-6.99 -58.08,-13.33 -75.33,-13.33 C-110.67,-13.33 -139.33,15.33 -139.33,50.67 C-139.33,86 -110.67,114.67 -75.33,114.67 C-38.37,114.67 -14.05,88.69 -14.05,52.13 C-14.05,47.95 -14.45,43.89 -15.07,40 C-15.07,40 -75.33,40 -75.33,40c M116.67 114.67 C116.67,114.67 15.33,114.67 15.33,114.67 C15.33,114.67 15.33,88 15.33,88 C15.33,88 116.67,88 116.67,88 C119.6,88 122,90.4 122,93.33 C122,93.33 122,109.33 122,109.33 C122,112.27 119.6,114.67 116.67,114.67c M132.67 64 C132.67,64 15.33,64 15.33,64 C15.33,64 15.33,37.33 15.33,37.33 C15.33,37.33 132.67,37.33 132.67,37.33 C135.6,37.33 138,39.73 138,42.67 C138,42.67 138,58.67 138,58.67 C138,61.6 135.6,64 132.67,64c M116.67 13.33 C116.67,13.33 15.33,13.33 15.33,13.33 C15.33,13.33 15.33,-13.33 15.33,-13.33 C15.33,-13.33 116.67,-13.33 116.67,-13.33 C119.6,-13.33 122,-10.93 122,-8 C122,-8 122,8 122,8 C122,10.93 119.6,13.33 116.67,13.33c "
                    android:valueType="pathType">
                    <aapt:attr name="android:interpolator">
                        <pathInterpolator android:pathData="M 0.0,0.0 c0,0 0,1 1.0,1.0" />
                    </aapt:attr>
                </objectAnimator>
            </set>
        </aapt:attr>
    </target>
    <target android:name="_R_G_L_1_G_N_4_T_1">
        <aapt:attr name="android:animation">
            <set android:ordering="together">
                <objectAnimator
                    android:duration="52"
                    android:pathData="M 216,236.604C 216,236.568 216,236.61100000000002 216,236.604"
                    android:propertyName="translateXY"
                    android:propertyXName="translateX"
                    android:propertyYName="translateY"
                    android:startOffset="0">
                    <aapt:attr name="android:interpolator">
                        <pathInterpolator android:pathData="M 0.0,0.0 c0,0 0,1 1.0,1.0" />
                    </aapt:attr>
                </objectAnimator>
                <objectAnimator
                    android:duration="948"
                    android:pathData="M 216,236.604C 216,236.568 216,236.34900000000002 216,236.342"
                    android:propertyName="translateXY"
                    android:propertyXName="translateX"
                    android:propertyYName="translateY"
                    android:startOffset="52">
                    <aapt:attr name="android:interpolator">
                        <pathInterpolator android:pathData="M 0.0,0.0 c0,0 0,1 1.0,1.0" />
                    </aapt:attr>
                </objectAnimator>
            </set>
        </aapt:attr>
    </target>
    <target android:name="_R_G_L_1_G_N_4_T_1">
        <aapt:attr name="android:animation">
            <set android:ordering="together">
                <objectAnimator
                    android:duration="52"
                    android:propertyName="scaleX"
                    android:startOffset="0"
                    android:valueFrom="0.098"
                    android:valueTo="0.098"
                    android:valueType="floatType">
                    <aapt:attr name="android:interpolator">
                        <pathInterpolator android:pathData="M 0.0,0.0 c0,0 0,0.789 1.0,1.0" />
                    </aapt:attr>
                </objectAnimator>
                <objectAnimator
                    android:duration="52"
                    android:propertyName="scaleY"
                    android:startOffset="0"
                    android:valueFrom="0.098"
                    android:valueTo="0.098"
                    android:valueType="floatType">
                    <aapt:attr name="android:interpolator">
                        <pathInterpolator android:pathData="M 0.0,0.0 c0,0 0,0.789 1.0,1.0" />
                    </aapt:attr>
                </objectAnimator>
                <objectAnimator
                    android:duration="398"
                    android:propertyName="scaleX"
                    android:startOffset="52"
                    android:valueFrom="0.098"
                    android:valueTo="0.36391"
                    android:valueType="floatType">
                    <aapt:attr name="android:interpolator">
                        <pathInterpolator android:pathData="M 0.0,0.0 c0,0 0,0.789 1.0,1.0" />
                    </aapt:attr>
                </objectAnimator>
                <objectAnimator
                    android:duration="398"
                    android:propertyName="scaleY"
                    android:startOffset="52"
                    android:valueFrom="0.098"
                    android:valueTo="0.36391"
                    android:valueType="floatType">
                    <aapt:attr name="android:interpolator">
                        <pathInterpolator android:pathData="M 0.0,0.0 c0,0 0,0.789 1.0,1.0" />
                    </aapt:attr>
                </objectAnimator>
                <objectAnimator
                    android:duration="550"
                    android:propertyName="scaleX"
                    android:startOffset="450"
                    android:valueFrom="0.36391"
                    android:valueTo="0.392"
                    android:valueType="floatType">
                    <aapt:attr name="android:interpolator">
                        <pathInterpolator android:pathData="M 0.0,0.0 c0.266,0.733 0.594,1 1.0,1.0" />
                    </aapt:attr>
                </objectAnimator>
                <objectAnimator
                    android:duration="550"
                    android:propertyName="scaleY"
                    android:startOffset="450"
                    android:valueFrom="0.36391"
                    android:valueTo="0.392"
                    android:valueType="floatType">
                    <aapt:attr name="android:interpolator">
                        <pathInterpolator android:pathData="M 0.0,0.0 c0.266,0.733 0.594,1 1.0,1.0" />
                    </aapt:attr>
                </objectAnimator>
            </set>
        </aapt:attr>
    </target>
    <target android:name="_R_G_L_1_G_N_4_T_1">
        <aapt:attr name="android:animation">
            <set android:ordering="together">
                <objectAnimator
                    android:duration="0"
                    android:propertyName="scaleY"
                    android:startOffset="50"
                    android:valueFrom="0"
                    android:valueTo="0.098"
                    android:valueType="floatType" />
            </set>
        </aapt:attr>
    </target>
    <target android:name="_R_G_L_0_G">
        <aapt:attr name="android:animation">
            <set android:ordering="together">
                <objectAnimator
                    android:duration="0"
                    android:propertyName="scaleY"
                    android:startOffset="50"
                    android:valueFrom="0"
                    android:valueTo="0.392"
                    android:valueType="floatType" />
            </set>
        </aapt:attr>
    </target>
    <target android:name="time_group">
        <aapt:attr name="android:animation">
            <set android:ordering="together">
                <objectAnimator
                    android:duration="100000"
                    android:propertyName="translateX"
                    android:startOffset="0"
                    android:valueFrom="0"
                    android:valueTo="1"
                    android:valueType="floatType" />
            </set>
        </aapt:attr>
    </target>
</animated-vector>