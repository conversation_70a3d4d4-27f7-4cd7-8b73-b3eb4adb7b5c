<?xml version="1.0" encoding="utf-8"?>
<!--<shape xmlns:android="http://schemas.android.com/apk/res/android">-->
<!--    <gradient-->
<!--        android:startColor="#26FFFFFF"-->
<!--        android:endColor="#1AFFFFFF"-->
<!--        android:angle="90"/>-->
<!--    <stroke-->
<!--        android:width="0.5dp"-->
<!--        android:startColor="#66FFFFFF"-->
<!--        android:endColor="#00FFFFFF"/>-->
<!--    <corners android:radius="4dp"/>-->
<!--</shape>-->

<layer-list xmlns:tools="http://schemas.android.com/tools"
xmlns:android="http://schemas.android.com/apk/res/android"
tools:ignore="ResourceName">

<item>
    <!-- 先创建渐变 -->
    <shape android:shape="rectangle">

        <corners android:radius="6dp" />
        <gradient
            android:angle="90"
            android:endColor="#66FFFFFF"
            android:startColor="#00FFFFFF" />

    </shape>
</item>
<!-- 再创建背景底色 -->
<item
    android:bottom="0.5dp"
    android:left="0.5dp"
    android:right="0.5dp"
    android:top="0.5dp">
    <shape android:shape="rectangle">
        <corners android:radius="6dp" />

        <gradient
            android:angle="90"
            android:endColor="#FF525664"
            android:startColor="#FF474C5B" />

    </shape>
</item>

</layer-list>