<?xml version="1.0" encoding="utf-8"?>

<com.timekettle.upup.comm.widget.CustomBatteryDrawable
    xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="46dp"
    android:layout_height="wrap_content"
    tools:background="@color/black">



    <FrameLayout
        android:id="@+id/battery_drawable"
        android:layout_width="19dp"
        android:layout_height="wrap_content"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintTop_toTopOf="parent">



    <ImageView
            android:id="@+id/battery_outer_frame"
            android:layout_width="19dp"
            android:layout_height="16dp"
            android:src="@drawable/tk_status_bar_battery_outer" />

        <ImageView
            android:id="@+id/battery_percentage_frame"
            android:layout_width="19dp"
            android:layout_height="16dp"
            android:paddingStart="2dp"
            android:paddingTop="5dp"
            android:paddingEnd="3dp"
            android:paddingBottom="5dp"
            android:src="@drawable/tk_status_bar_battery_percentage" />

        <ImageView
            android:id="@+id/battery_charging"
            android:layout_width="19dp"
            android:paddingStart="1dp"
            android:paddingEnd="1dp"
            android:layout_height="16dp"
            android:src="@mipmap/sys_bars_icon_battery_charging03"
            android:visibility="invisible" />


    </FrameLayout>

    <TextView
        android:id="@+id/battery_percentage"
        android:layout_width="26dp"
        android:layout_height="wrap_content"
        android:layout_marginBottom="1dp"
        android:text="100%"
        android:layout_marginLeft="2dp"
        android:textColor="#ffffffff"
        android:textSize="10sp"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintLeft_toRightOf="@+id/battery_drawable"
        app:layout_constraintTop_toTopOf="parent" />
</com.timekettle.upup.comm.widget.CustomBatteryDrawable>
