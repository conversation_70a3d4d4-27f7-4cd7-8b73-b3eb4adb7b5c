<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="wrap_content"
    android:layout_height="wrap_content"
    android:id="@+id/root_view"
    tools:background="@color/black">


    <ImageView
        android:id="@+id/iv_wifi_off"
        android:layout_width="49dp"
        android:layout_height="20dp"
        android:padding="2dp"
        android:src="@mipmap/sys_bars_icon_wifi_off"
        android:visibility="invisible"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <ImageView
        android:id="@+id/iv_wifi"
        android:layout_width="20dp"
        android:layout_height="20dp"
        android:padding="2dp"
        android:src="@mipmap/sys_bars_icon_wifi03"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toTopOf="parent" />


    <com.airbnb.lottie.LottieAnimationView
        android:id="@+id/lottie_connecting"
        android:layout_width="0dp"
        android:layout_height="0dp"
        android:visibility="gone"
        app:layout_constraintLeft_toLeftOf="@+id/iv_wifi"
        app:layout_constraintTop_toTopOf="@+id/iv_wifi"
        app:layout_constraintBottom_toBottomOf="@+id/iv_wifi"
        app:layout_constraintRight_toRightOf="@+id/iv_wifi"
        app:lottie_autoPlay="true"
        app:lottie_fileName="ani_nav_wifi.json"
        app:lottie_loop="true" />

</androidx.constraintlayout.widget.ConstraintLayout>
