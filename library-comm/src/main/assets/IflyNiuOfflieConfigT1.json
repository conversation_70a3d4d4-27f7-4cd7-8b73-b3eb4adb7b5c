{"readme": "1、「识别引擎」全部是用的讯飞，「中<->英日法西俄德」的翻译用的是讯飞, 「英<->日法西俄德韩+中韩」的翻译用的是小牛，总用有13个语言对 2、转换成下载链接时，需要把「baseUrlIfly」和「baseUrlNiu」替换成相应的链接", "baseUrlIfly": "https://cdn.timekettle.co/IflyOfflineResource2", "baseUrlNiuAndroid": "https://cdn.timekettle.co/mt/android/niu_trans", "baseUrlNiuIos": "https://cdn.timekettle.co/mt/ios/niu_trans", "iflyCommonUrls": ["baseUrlIfly/mt/config.zip", "baseUrlIfly/mt/res/isr_segment.zip", "baseUrlIfly/mt/res/isr_segment_cn.zip", "baseUrlIfly/mt/res/isr_segment_cnes.zip", "baseUrlIfly/mt/res/isr_segment_cnfr.zip", "baseUrlIfly/mt/res/isr_segment_ja.zip", "baseUrlIfly/mt/res/isr_segment_th.zip", "baseUrlIfly/mt/res/lang.zip", "baseUrlIfly/mt/res/nmt_core/nmt_core.home.zip", "baseUrlIfly/mt/res/nn_ner.zip", "baseUrlIfly/mt/res/regnum.zip", "baseUrlIfly/mt/res/sentencepiece_hi.zip", "baseUrlIfly/mt/res/sentencepiece_ot.zip", "baseUrlIfly/mt/res/sentencepiece_th.zip", "baseUrlIfly/asr/cnen.zip"], "languagePairUrl": [{"code": "zh<->en", "resCodes": ["zh", "en", "zhen", "enzh"], "urls": ["baseUrlIfly/mt/res/nmt_core/cnen.zip", "baseUrlIfly/mt/res/nmt_core/encn.zip"]}, {"code": "zh<->ja", "resCodes": ["zh", "ja", "zhja", "jazh"], "urls": ["baseUrlIfly/asr/ja.zip", "baseUrlIfly/mt/res/nmt_core/cnja.zip", "baseUrlIfly/mt/res/nmt_core/jacn.zip"]}, {"code": "zh<->fr", "resCodes": ["zh", "fr", "zhfr", "frzh"], "urls": ["baseUrlIfly/asr/fr.zip", "baseUrlIfly/mt/res/nmt_core/cnfr.zip", "baseUrlIfly/mt/res/nmt_core/frcn.zip"]}, {"code": "zh<->es", "resCodes": ["zh", "es", "zhes", "eszh"], "urls": ["baseUrlIfly/asr/es.zip", "baseUrlIfly/mt/res/nmt_core/cnes.zip", "baseUrlIfly/mt/res/nmt_core/escn.zip"]}, {"code": "zh<->ru", "resCodes": ["zh", "ru", "zhru", "ruzh"], "urls": ["baseUrlIfly/asr/ru.zip", "baseUrlIfly/mt/res/nmt_core/cnru.zip", "baseUrlIfly/mt/res/nmt_core/rucn.zip"]}, {"code": "zh<->de", "resCodes": ["zh", "de", "zhde", "dezh"], "urls": ["baseUrlIfly/asr/de.zip", "baseUrlIfly/mt/res/nmt_core/cnde.zip", "baseUrlIfly/mt/res/nmt_core/decn.zip"]}, {"code": "zh<->ko", "resCodes": ["zh", "ko", "zhko", "kozh"], "urls": ["baseUrlIfly/asr/ko.zip", "baseUrlNiu/zh2ko.zip", "baseUrlNiu/ko2zh.zip"]}, {"code": "en<->ja", "resCodes": ["en", "ja", "enja", "jaen"], "urls": ["baseUrlIfly/asr/ja.zip", "baseUrlNiu/en2ja.zip", "baseUrlNiu/ja2en.zip"]}, {"code": "en<->fr", "resCodes": ["en", "fr", "enfr", "fren"], "urls": ["baseUrlIfly/asr/fr.zip", "baseUrlNiu/en2fr.zip", "baseUrlNiu/fr2en.zip"]}, {"code": "en<->es", "resCodes": ["en", "es", "enes", "esen"], "urls": ["baseUrlIfly/asr/es.zip", "baseUrlNiu/en2es.zip", "baseUrlNiu/es2en.zip"]}, {"code": "en<->ru", "resCodes": ["en", "ru", "enru", "ruen"], "urls": ["baseUrlIfly/asr/ru.zip", "baseUrlNiu/en2ru.zip", "baseUrlNiu/ru2en.zip"]}, {"code": "en<->de", "resCodes": ["en", "de", "ende", "deen"], "urls": ["baseUrlIfly/asr/de.zip", "baseUrlNiu/en2de.zip", "baseUrlNiu/de2en.zip"]}, {"code": "en<->ko", "resCodes": ["en", "ko", "enko", "koen"], "urls": ["baseUrlIfly/asr/ko.zip", "baseUrlNiu/en2ko.zip", "baseUrlNiu/ko2en.zip"]}], "totalSizes": {"baseUrlIfly/asr/cnen.zip": 93652521, "baseUrlIfly/asr/de.zip": 79130377, "baseUrlIfly/asr/es.zip": 80368151, "baseUrlIfly/asr/fr.zip": 79817074, "baseUrlIfly/asr/ja.zip": 80532453, "baseUrlIfly/asr/ko.zip": 72197519, "baseUrlIfly/asr/ru.zip": 84791475, "baseUrlIfly/mt/config.zip": 1280, "baseUrlIfly/mt/res/isr_segment.zip": 5077828, "baseUrlIfly/mt/res/isr_segment_cn.zip": 3978772, "baseUrlIfly/mt/res/isr_segment_cnes.zip": 4466271, "baseUrlIfly/mt/res/isr_segment_cnfr.zip": 3583194, "baseUrlIfly/mt/res/isr_segment_ja.zip": 819560, "baseUrlIfly/mt/res/isr_segment_th.zip": 476221, "baseUrlIfly/mt/res/lang.zip": 5129191, "baseUrlIfly/mt/res/nn_ner.zip": 56390533, "baseUrlIfly/mt/res/regnum.zip": 50952, "baseUrlIfly/mt/res/sentencepiece_hi.zip": 457958, "baseUrlIfly/mt/res/sentencepiece_ot.zip": 832756, "baseUrlIfly/mt/res/sentencepiece_th.zip": 347581, "baseUrlIfly/mt/res/nmt_core/nmt_core.home.zip": 206, "baseUrlIfly/mt/res/nmt_core/cnde.zip": 103183221, "baseUrlIfly/mt/res/nmt_core/cnen.zip": *********, "baseUrlIfly/mt/res/nmt_core/cnes.zip": 97522080, "baseUrlIfly/mt/res/nmt_core/cnfr.zip": 97932538, "baseUrlIfly/mt/res/nmt_core/cnja.zip": *********, "baseUrlIfly/mt/res/nmt_core/cnru.zip": *********, "baseUrlIfly/mt/res/nmt_core/cnth.zip": 95768271, "baseUrlIfly/mt/res/nmt_core/decn.zip": 99019875, "baseUrlIfly/mt/res/nmt_core/encn.zip": *********, "baseUrlIfly/mt/res/nmt_core/escn.zip": *********, "baseUrlIfly/mt/res/nmt_core/frcn.zip": 99723147, "baseUrlIfly/mt/res/nmt_core/jacn.zip": *********, "baseUrlIfly/mt/res/nmt_core/rucn.zip": *********, "baseUrlIfly/mt/res/nmt_core/thcn.zip": 97610729, "baseUrlNiuAndroid/en2es.zip": 52680280, "baseUrlNiuAndroid/en2fr.zip": 50810084, "baseUrlNiuAndroid/en2ja.zip": 59839554, "baseUrlNiuAndroid/en2ko.zip": 58017956, "baseUrlNiuAndroid/en2ru.zip": 49504845, "baseUrlNiuAndroid/es2en.zip": 52725717, "baseUrlNiuAndroid/fr2en.zip": 50861274, "baseUrlNiuAndroid/ja2en.zip": 55322982, "baseUrlNiuAndroid/ko2en.zip": 56584821, "baseUrlNiuAndroid/ko2zh.zip": 64290184, "baseUrlNiuAndroid/ru2en.zip": 49518820, "baseUrlNiuAndroid/zh2ko.zip": 62230177, "baseUrlNiuAndroid/en2de.zip": 52191440, "baseUrlNiuAndroid/de2en.zip": 52177045, "baseUrlNiuIos/en2es.zip": 52686560, "baseUrlNiuIos/en2fr.zip": 50816048, "baseUrlNiuIos/en2ja.zip": 59845813, "baseUrlNiuIos/en2ko.zip": 58024213, "baseUrlNiuIos/en2ru.zip": 49511104, "baseUrlNiuIos/es2en.zip": 52732001, "baseUrlNiuIos/fr2en.zip": 50867533, "baseUrlNiuIos/ja2en.zip": 55329240, "baseUrlNiuIos/ko2en.zip": 56591079, "baseUrlNiuIos/ko2zh.zip": 64296441, "baseUrlNiuIos/ru2en.zip": 49525077, "baseUrlNiuIos/zh2ko.zip": 62236437, "baseUrlNiuIos/en2de.zip": 52191440, "baseUrlNiuIos/de2en.zip": 52177045}}