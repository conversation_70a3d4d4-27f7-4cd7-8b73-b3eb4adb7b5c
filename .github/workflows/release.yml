name: release
 
on:
  workflow_dispatch:
  push:
    tags:
      - 'v*'
 
jobs:
  release:
    runs-on: ubuntu-22.04

    permissions:
      contents: write

    steps:
    # 拉取主仓库代码和子模块
    - name: Checkout repository with submodules
      uses: actions/checkout@v3
      with:
        token: ${{ secrets.GH_ACCESS_TOKEN }}
        submodules: recursive  # 拉取子模块
        fetch-depth: 0    # 完整克隆，避免浅克隆导致子模块无法正确更新

    - name: Setup ruby env
      uses: ruby/setup-ruby@v1
      with:
        ruby-version: 3.0.0
        bundler-cache: true

    - name: Setup CMake 3.10.2
      uses: lukka/get-cmake@latest
      with:
        cmakeVersion: 3.10.2

  #  - name: Run tests
  #    run: bundle exec fastlane android test

  #  - name: Decode Service Account Key JSON File
  #    uses: timheuer/base64-to-file@v1
  #    id: service_account_json_file
  #    with:
  #      fileName: "serviceAccount.json"
  #      encodedString: ${{ secrets.GPLAY_SERVICE_ACCOUNT_KEY_JSON }}

    - name: Decode Keystore File
      uses: timheuer/base64-to-file@v1
      id: android_keystore
      with:
        fileName: "tmk_release.keystore"
        encodedString: ${{ secrets.KEYSTORE_FILE }}

    - name: Build Android release
      run: bundle exec fastlane android beta
      env:
        KEYSTORE_FILE: ${{ steps.android_keystore.outputs.filePath }}
        KEYSTORE_PASSWORD: ${{ secrets.KEYSTORE_PASSWORD }}
        KEY_ALIAS: ${{ secrets.KEY_ALIAS}}
        KEY_PASSWORD: ${{ secrets.KEY_PASSWORD }}
        PGYER_API_KEY: ${{ secrets.PGYER_API_KEY }}
      #  ANDROID_JSON_KEY_FILE: ${{ steps.service_account_json_file.outputs.filePath }}
    
    - name: Upload apk to pyger
      env:
        DINGTALK_WEBHOOK: ${{ secrets.DINGTALK_WEBHOOK }} # 如果不发dingtalk提醒, 则可以注释此行
        DINGTALK_SECRET: ${{ secrets.DINGTALK_SECRET }} # 如果不发dingtalk提醒, 则可以注释此行
        PGYER_API_KEY: ${{ secrets.PGYER_API_KEY }}
        APK_PATH: ${{ env.APK_PATH }} # 此处 APK_PATH 可以变为更优雅的方式: 如类似steps获取上一步的输出
      run: |
        python scripts/upload_apk.py \
        --path ${APK_PATH} \
        --key ${PGYER_API_KEY} \
        --password "" \
        --dingtalk-webhook "${DINGTALK_WEBHOOK}" \
        --dingtalk-secret "${DINGTALK_SECRET}"

    - name: Decode APP_UPDATE_DESC_BASE64
      id: decode_update_desc
      run: |
        {
          echo 'APP_UPDATE_DESC<<EOF'
          echo "${{ env.APP_UPDATE_DESC_BASE64 }}" | base64 --decode
          echo EOF
        } >> "$GITHUB_ENV"

    - name: Create GitHub Release
      id: create_release
      uses: actions/create-release@v1
      env:
        GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}
      with:
        tag_name: ${{ github.ref }}
        release_name: Release ${{ github.ref }}
        draft: false
        prerelease: true
        body: |
          ${{ env.APP_UPDATE_DESC }}

    # - name: Dingtalk robot message
    #   env:
    #     DINGTALK_WEBHOOK: ${{ secrets.DINGTALK_WEBHOOK }}
    #     DINGTALK_SECRET: ${{ secrets.DINGTALK_SECRET }}
    #   run: |
    #     #!/bin/bash
    #     dingtalk_title="Timekettle(Android)更新(GitHub Action Notification)"
    #     dingtalk_message="🚀 **GitHub Action Triggered**"
    #     python script/dingtalk_notify.py \
    #     --webhook "${DINGTALK_WEBHOOK}" \
    #     --secret "${DINGTALK_SECRET}" \
    #     --title "${dingtalk_message}" \
    #     --message "${dingtalk_message}"
    #   shell: bash