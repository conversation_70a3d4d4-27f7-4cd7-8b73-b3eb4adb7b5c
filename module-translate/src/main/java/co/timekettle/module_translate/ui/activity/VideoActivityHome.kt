package co.timekettle.module_translate.ui.activity

import android.annotation.SuppressLint
import android.util.Log
import androidx.activity.viewModels
import androidx.lifecycle.lifecycleScope
import co.timekettle.btkit.BleUtil
import co.timekettle.btkit.bean.RawBlePeripheral
import co.timekettle.btkit.bean.WT2BlePeripheral
import co.timekettle.module_translate.bean.LanguageJsonBeanChild
import co.timekettle.module_translate.constant.IntentKey
import co.timekettle.module_translate.databinding.ActivityVideoHomeBinding
import co.timekettle.module_translate.tools.TransManager
import co.timekettle.module_translate.ui.vm.VideoHomeVM
import co.timekettle.module_translate.ui.wrapper.NeviewManagerWrapper
import com.alibaba.android.arouter.facade.annotation.Route
import com.alibaba.android.arouter.launcher.ARouter
import com.timekettle.upup.base.ktx.gone
import com.timekettle.upup.base.ktx.observeLiveData
import com.timekettle.upup.base.ktx.openActivity1
import com.timekettle.upup.base.ktx.setClickEffect
import com.timekettle.upup.base.ktx.startKtxActivity
import com.timekettle.upup.base.utils.showDebugToast
import com.timekettle.upup.comm.R
import com.timekettle.upup.comm.base.BaseActivity
import com.timekettle.upup.comm.constant.LanDirection
import com.timekettle.upup.comm.constant.RouteUrl
import com.timekettle.upup.comm.constant.TranslateMode
import com.timekettle.upup.comm.model.SensorsCustomEvent
import com.timekettle.upup.comm.service.home.HomeServiceImplWrap
import com.timekettle.upup.comm.utils.SensorsUtil
import dagger.hilt.android.AndroidEntryPoint
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch

/**
 * 音视频模式首页
 * */
@AndroidEntryPoint
@Route(path = RouteUrl.Translate.VideoActivityHome)
class VideoActivityHome : BaseActivity<ActivityVideoHomeBinding, VideoHomeVM>() {

    var mDeviceList = mutableListOf<RawBlePeripheral>()
    var bleListener: BleUtil.Listener? = null
    override val mViewModel: VideoHomeVM by viewModels()

    override fun initObserve() {
        observeLiveData(mViewModel.liveSelfOtherLanguage, ::processLanguageUpdate)
    }

    private fun processLanguageUpdate(value: Array<LanguageJsonBeanChild>) {
        //更新btn上的text
        mBinding.btnRight.text = value[0].language//右耳是自己
        mBinding.btnLeft.text = value[1].language//左耳是其他
    }

    override fun initRequestData() {

    }

    @SuppressLint("SetTextI18n")
    override fun onResume() {
        super.onResume()
        NeviewManagerWrapper.initAudio()
        HomeServiceImplWrap.saveUserModel(TranslateMode.VIDEO)
        val setting = TransManager.getModelSetting(HomeServiceImplWrap.getUserModel())
        Log.d("qwjqweroqasdf", "code--- $setting")
        mViewModel.updateLanguage("", "")
    }

    override fun ActivityVideoHomeBinding.initView() {
        HomeServiceImplWrap.saveUserModel(TranslateMode.VIDEO)
        vTitleBar.vTitleTv.text = getString(R.string.home_audio_and_video)
        vTitleBar.vSettingIcon.gone()

        refreshDeviceList()

        setClickEffect(llLeft, llRight, ivExchange, llStart)
        llLeft.setOnClickListener { clickBtnLeft() }
        llRight.setOnClickListener { clickBtnRight() }
        llStart.setOnClickListener { clickBtnStart() }
        ivExchange.setOnClickListener { clickBtnExchange() }
    }

    private fun clickBtnExchange() {
        //点击切换按钮，将源语言与目标语言对调
        mViewModel.updateLanguage(mViewModel.otherCode, mViewModel.selfCode)
        //点击切换语言后此时model的selfCode及otherCode已经改变，重新保存一次
        TransManager.saveLastlyUseLanguage(mViewModel.otherCode, false)
        TransManager.saveLastlyUseLanguage(mViewModel.selfCode, true)
    }

    private fun clickBtnStart() {
        //跳转翻译界面
        ARouter.getInstance()
            .build(RouteUrl.Translate.VideoActivityMain)
            .withString("offlineSelfCode", mViewModel.offlineSelfCode)
            .withString("offlineOtherCode", mViewModel.offlineOtherCode)
            .navigation()

        SensorsUtil.trackEvent(SensorsCustomEvent.X1_MediaTranstionLanguageSelectionPagClickStart.name, null)
    }

    private fun clickBtnLeft() {
        //左边
        val param = IntentKey.LanguageType to LanDirection.Other
        openActivity1<ChooseLangActivity>(param)

    }

    private fun clickBtnRight() {
        //右边
        val param = IntentKey.LanguageType to LanDirection.Mine
        openActivity1<ChooseLangActivity>(param)
    }

    override fun initListener() {
        initBleListener()
        mBinding.vTitleBar.vTitleQuestionIcon.setOnClickListener {
            startKtxActivity<VideoActivityConnectHelp>()
        }
    }

    private fun initBleListener() {
        bleListener = object : BleUtil.Listener {
            override fun dispatchEvent(type: BleUtil.BleEventName, perip: RawBlePeripheral?) {
                when (type) {
                    BleUtil.BleEventName.BleDidStatusUpdate -> {
                        refreshDeviceList()
                    }

                    BleUtil.BleEventName.BleConnectStandby -> {
                        refreshDeviceList()
                    }

                    BleUtil.BleEventName.BleDisconnectedPeripheral -> {
                        refreshDeviceList()
                    }

                    BleUtil.BleEventName.BleDisconnectedSubPeripheral -> {}
                    BleUtil.BleEventName.BleConnectedSubPeripheral -> {}
                    BleUtil.BleEventName.BleProtocolChanged -> {}
                    BleUtil.BleEventName.BleStError -> {
                        val w = perip as WT2BlePeripheral
                        showDebugToast("${w.name} st 版本异常 ${w.stVersion}")
                    }

                    else -> {}
                }
            }

            override fun onBluetoothStateUpdate(state: Int) {
            }
        }

        BleUtil.shared.addListener(bleListener)
    }

    //初始化view就刷新了一次，然后ble监听状态也会刷新
    private fun refreshDeviceList() {
        mDeviceList.clear()
        for (peripheral in BleUtil.shared.sortedSearchedPeripherals) {
            if (peripheral.state in listOf(RawBlePeripheral.PeripheralState.DIDINIT)) {
                mDeviceList.add(peripheral)
            }
        }

        lifecycleScope.launch(Dispatchers.Main) {
            mBinding?.run {
//                //两只都连上了
//                llStart.isEnabled = mDeviceList.size >= 1
//                if (!llStart.isEnabled) { // 没连接
//
//                } else {
//
//                }
            }
        }
    }

//    @Subscribe(threadMode = ThreadMode.MAIN)
//    fun onLanguageUpdate(event: LanguageUpdateEvent) {
//        logD("onLanguageUpdate $event")
//
//        //发送了语言选择事件，先缓存，下次进界面先获取缓存
//        mViewModel.updateLanguage(event.fromCode, event.toCode)
//    }

    override fun onDestroy() {
        BleUtil.shared.removeListener(bleListener)
        bleListener = null
        NeviewManagerWrapper.closeAudio()
        super.onDestroy()
    }

}