package co.timekettle.module_translate.ui.activity

import android.animation.ValueAnimator
import android.annotation.SuppressLint
import android.content.Intent
import android.provider.Settings
import android.util.Log
import android.view.KeyEvent
import android.view.LayoutInflater
import android.view.MotionEvent
import android.view.View
import android.view.WindowManager
import androidx.activity.OnBackPressedCallback
import androidx.activity.result.ActivityResult
import androidx.activity.result.contract.ActivityResultContracts
import androidx.activity.viewModels
import androidx.constraintlayout.widget.ConstraintLayout
import androidx.core.view.isVisible
import androidx.core.view.updateLayoutParams
import androidx.lifecycle.lifecycleScope
import co.timekettle.btkit.BleUtil
import co.timekettle.btkit.bean.RawBlePeripheral
import co.timekettle.module_translate.bean.MontageEntity
import co.timekettle.module_translate.bean.OfflineUiEvent
import co.timekettle.module_translate.bean.SettingEnum
import co.timekettle.module_translate.databinding.VideoActivityMainBinding
import co.timekettle.module_translate.tools.TransLanguageTool
import co.timekettle.module_translate.tools.TransManager
import co.timekettle.module_translate.tools.TransStringUtil
import co.timekettle.module_translate.ui.adapter.MontageAdapter
import co.timekettle.module_translate.ui.adapter.VideoUpAdapter
import co.timekettle.module_translate.ui.ktx.startVideoSettingActivity
import co.timekettle.module_translate.ui.util.RecycleViewUtil
import co.timekettle.module_translate.ui.vm.VideoMainVM
import co.timekettle.speech.AiSpeechManager
import co.timekettle.speech.ISpeechConstant
import co.timekettle.speech.OfflineManager
import co.timekettle.speech.SpeakManager
import com.alibaba.android.arouter.facade.annotation.Autowired
import com.alibaba.android.arouter.facade.annotation.Route
import com.blankj.utilcode.util.ActivityUtils
import com.blankj.utilcode.util.ConvertUtils
import com.blankj.utilcode.util.NetworkUtils
import com.timekettle.upup.base.BaseApp
import com.timekettle.upup.base.ktx.animateAlpha0
import com.timekettle.upup.base.ktx.animateAlpha1
import com.timekettle.upup.base.ktx.gone
import com.timekettle.upup.base.ktx.observeLiveData
import com.timekettle.upup.base.ktx.visible
import com.timekettle.upup.base.utils.Debouncer
import com.timekettle.upup.base.utils.EventBusRegister
import com.timekettle.upup.base.utils.SpUtils
import com.timekettle.upup.base.utils.countDownCoroutines
import com.timekettle.upup.base.utils.logD
import com.timekettle.upup.base.utils.showToast
import com.timekettle.upup.comm.BuildConfig
import com.timekettle.upup.comm.R
import com.timekettle.upup.comm.base.BaseActivity
import com.timekettle.upup.comm.bean.MsgDirection
import com.timekettle.upup.comm.bean.MsgInterview
import com.timekettle.upup.comm.bean.MsgWrapper
import com.timekettle.upup.comm.bean.TextBean
import com.timekettle.upup.comm.constant.RouteUrl
import com.timekettle.upup.comm.constant.SpKey
import com.timekettle.upup.comm.dialog.DialogFactory
import com.timekettle.upup.comm.ktx.setLedLevel
import com.timekettle.upup.comm.model.DongleConnectEvent
import com.timekettle.upup.comm.model.SensorsCustomEvent
import com.timekettle.upup.comm.service.home.HomeServiceImplWrap
import com.timekettle.upup.comm.utils.SensorsUtil
import com.timekettle.upup.comm.utils.USBHIDController
import dagger.hilt.android.AndroidEntryPoint
import kotlinx.coroutines.Job
import org.greenrobot.eventbus.Subscribe
import org.greenrobot.eventbus.ThreadMode
import java.math.BigDecimal

/**
 * 音视频翻译页面
 */
@EventBusRegister
@AndroidEntryPoint
@Route(path = RouteUrl.Translate.VideoActivityMain)
class VideoActivityMain : BaseActivity<VideoActivityMainBinding, VideoMainVM>() {

    @JvmField
    @Autowired(name = "offlineSelfCode")
    var offlineSelfCode: String = ""

    @JvmField
    @Autowired(name = "offlineOtherCode")
    var offlineOtherCode: String = ""

    private var jobNeedScrollToEnd: Job? = null //是否需要滚动到底部
    private var jobPure: Job? = null //是否需要纯净
    private var statTime: Long = 0
    private var isSingle: Boolean = true //默认单向（非电话翻译）
    override val mViewModel: VideoMainVM by viewModels()
    private val bouncer = Debouncer(200) //设置延迟时间为200毫秒
    private var isCalling = false
    private var bleListener: BleUtil.Listener? = null
    private var x = 0f
    private var y = 0f
    private var isTouchUp = false
    private var isTouchDown = false
    private lateinit var upAdapter: VideoUpAdapter
    private lateinit var downAdapter: MontageAdapter
    private var upList = mutableListOf<TextBean>()
    private var downList = mutableListOf<MontageEntity>()

    private val dongleDisconnectDialog by lazy {
        DialogFactory.createConfirmDialog(
            context = ActivityUtils.getTopActivity(),
            titleText = BaseApp.context.getString(R.string.common_alert_tip),
            content = BaseApp.context.getString(R.string.dongle_disconnect_content),
            canceledOnTouchOutside = false,
            cancelable = false,
            confirmCall = {
                ActivityUtils.finishToActivity(VideoActivityConnect::class.java, false, true)
            },
        )
    }

    override fun initObserve() {
        observeLiveData(mViewModel.liveOfflineUiEvent, ::processOfflineUiEvent)
        observeLiveData(mViewModel.liveMsgBegin, ::processMsgBegin)
        observeLiveData(mViewModel.liveMsgEnd, ::processMsgEnd)
        observeLiveData(mViewModel.liveMsgSpeakStart, ::processMsgSpeakStart)
        observeLiveData(mViewModel.liveMsgSpeakStop, ::processMsgSpeakStop)
        observeLiveData(mViewModel.liveMsgRecognize, ::processMsgRecognize)
        observeLiveData(mViewModel.liveMsgTranslate, ::processMsgTranslate)
        observeLiveData(mViewModel.liveIsPause, ::processPauseOrResume)
        observeLiveData(mViewModel.liveFontEnum, ::processFontEnum)
        observeLiveData(mViewModel.isShowOriginal, ::processShowOriginal)
        observeLiveData(mViewModel.liveErrorClose, ::processErrorClose)
    }

    private fun processErrorClose(isError: Boolean) {
        if (isError) {
            logD("音视频模式，初始化usb模块失败，退出当前页面", TAG)
            finish()
        }
    }

    private fun processShowOriginal(isShowOriginal: Boolean) {
        if (isShowOriginal) {
            mBinding.rvDown.visible()
            mBinding.viewLine.visible()
        } else {
            mBinding.rvDown.gone()
            mBinding.viewLine.gone()
            mBinding.tvDownTip.gone()
        }
    }

    //更新字体大小
    private fun processFontEnum(fontSize: SettingEnum.FontSize) {
        val fontSp = fontSize.fontValue
        mBinding.tvUpTip.textSize = (fontSp + 6).toFloat()
        mBinding.tvDownTip.textSize = (fontSp + 6).toFloat()
        upAdapter.setFont(fontSp + 6)
        downAdapter.setFont(fontSp + 6)
    }

    override fun initRequestData() {
        lifecycleScope.launchWhenResumed {
            mBinding.ivContinueButton.performClick()
        }

        SensorsUtil.trackEvent(
            SensorsCustomEvent.X1_EnterXXMode.name, hashMapOf(
                "ModelType" to "音视频翻译"
            )
        )

        statTime = System.currentTimeMillis()
    }

    private val hidListener = object : USBHIDController.IReceiveDataListener {
        override fun onReceivePlayStateChange(isPlay: Boolean, event: USBHIDController.HIDPlayEvent) {
            if (event == USBHIDController.HIDPlayEvent.SCO) {
                isSingle = false
                isCalling = if (isPlay) {
                    mViewModel.startPlayback()
                    mViewModel.startRecord()
                    true
                } else {
                    logD("电话功能关闭", TAG)
                    mViewModel.stopPlayback()
                    mViewModel.stopRecord()
                    false
                }
            }
        }
    }

    private fun showDongleDialog() {
        if (!dongleDisconnectDialog.isShowing) {
            dongleDisconnectDialog.show()
        }
    }

    override fun VideoActivityMainBinding.initView() {
        window.addFlags(WindowManager.LayoutParams.FLAG_KEEP_SCREEN_ON) // 防止息屏
        HomeServiceImplWrap.setInMode(true)

        if (SpUtils.getBoolean(SpKey.HIDE_VIDEO_GUIDE, false)) {
            mBinding.layoutTip.gone()
            mBinding.lottieTip.cancelAnimation()
        }

        mBinding.ivPauseButton.visible()
        mViewModel.initVideo()
        USBHIDController.addListener(hidListener)
        USBHIDController.checkConnect()
        USBHIDController.checkPlayState()
        NetworkUtils.registerNetworkStatusChangedListener(netWorkListener)

        //初始化离线，否则用不了离线功能
        OfflineManager.getInstance().init(this@VideoActivityMain)

        mViewModel.setProductAndMode(offlineSelfCode, offlineOtherCode)

        vTitleBar.vTitleTv.text = getString(R.string.home_audio_and_video)
        tvUpTip.text = TransStringUtil.getVideoSpeakString(mViewModel.selfCode)
        tvDownTip.text = TransStringUtil.getVideoSpeakString(mViewModel.otherCode)

        vTitleBar.vTitleQuestionIcon.gone()

        upAdapter = VideoUpAdapter(upList)
        upAdapter.setFooterView(getEmptySpeakView())
        rvUp.adapter = upAdapter

        downAdapter = MontageAdapter(downList)
        downAdapter.setFooterView(getEmptySpeakView())
        rvDown.adapter = downAdapter

        setLedLevel(this@VideoActivityMain, 8)
    }

    private fun processBack() {
        DialogFactory.createConfirmCancelDialog(this@VideoActivityMain,
            content = getString(R.string.translate_exit_cur_mode_tip),
            confirmCall = {
                finish()
            }).show()
    }

    @SuppressLint("ClickableViewAccessibility")
    override fun initListener() {
        mBinding.vTitleBar.vBackIv.setOnClickListener {
            processBack()
        }

        mBinding.vTitleBar.vTitleTv.setOnClickListener {
            processBack()
        }

        mBinding.btnNoMore.setOnClickListener {
            SpUtils.put(SpKey.HIDE_VIDEO_GUIDE, true)
            mBinding.layoutTip.gone()
            mBinding.lottieTip.cancelAnimation()
        }

        onBackPressedDispatcher.addCallback(this@VideoActivityMain,
            object : OnBackPressedCallback(true) {
                override fun handleOnBackPressed() {
                    processBack()
                }
            })

        mBinding.ivContinueButton.setOnClickListener {
            mBinding.ivContinueButton.gone()
            mBinding.ivPauseButton.visible()
            mViewModel.pauseOrResume()
            lifecycleScope.launchWhenStarted {
                startPureJob { enterPureMode() }
            }

            if (isCalling) {
                mViewModel.startRecord()
                mViewModel.startPlayback()
            }
        }

        mBinding.ivPauseButton.setOnClickListener {
            mBinding.ivPauseButton.gone()
            mBinding.ivContinueButton.visible()
            mViewModel.pauseOrResume()

            if (isCalling) {
                mViewModel.stopRecord()
                mViewModel.stopPlayback()
            }
        }

        mBinding.vTitleBar.vSettingIcon.setOnClickListener {
            //跳转设置界面
            stopPureJob()
            startVideoSettingActivity(mViewModel.modeUtil)
        }

        mBinding.root.setOnClickListener { exchangePureMode() }

        mBinding.rvUp.setOnTouchListener { _, motionEvent ->

            when (motionEvent?.action) {
                MotionEvent.ACTION_DOWN -> {
                    isTouchUp = true
                    x = motionEvent.x
                    y = motionEvent.y
                }

                MotionEvent.ACTION_UP -> {
                    isTouchUp = false
                    if (motionEvent.x == x && motionEvent.y == y) {
                        exchangePureMode()
                    }
                }

                MotionEvent.ACTION_CANCEL -> {
                    isTouchUp = false
                }

                else -> {}
            }

            false
        }

        mBinding.rvDown.setOnTouchListener { _, motionEvent ->
            when (motionEvent?.action) {
                MotionEvent.ACTION_DOWN -> {
                    isTouchDown = true
                    x = motionEvent.x
                    y = motionEvent.y
                }

                MotionEvent.ACTION_UP -> {
                    isTouchDown = false
                    if (motionEvent.x == x && motionEvent.y == y) {
                        exchangePureMode()
                    }
                }

                MotionEvent.ACTION_CANCEL -> {
                    isTouchDown = false
                }

                else -> {}
            }

            false
        }

        bleListener = object : BleUtil.Listener {
            override fun dispatchEvent(type: BleUtil.BleEventName, perip: RawBlePeripheral?) {
                when (type) {

                    BleUtil.BleEventName.BleConnectStandby -> {
                        logD("模式内连接耳机", TAG)
                        if (BleUtil.shared.connectedPeripherals.size == 1) {
                            mViewModel.setSpeakType(1)
                            logD("耳机已连接，TTS音频从耳机播放", TAG)
                        }
                    }

                    BleUtil.BleEventName.BleDisconnectedPeripheral -> {
                        if (BleUtil.shared.connectedPeripherals.isEmpty()) {
                            logD("耳机连接断开，当前无已连接耳机", TAG)
                        }
                    }

                    else -> {}
                }
            }

            override fun onBluetoothStateUpdate(state: Int) {}
        }

        BleUtil.shared.addListener(bleListener)

        mBinding.layoutTip.apply {
            setOnClickListener {
                gone()
                mBinding.lottieTip.cancelAnimation()
            }
        }

    }

    private fun exchangePureMode() {
        bouncer.debounce {
            if (mBinding.ivPauseButton.isVisible || mBinding.ivContinueButton.isVisible) {
                enterPureMode()
            } else {
                exitPureMode(mViewModel.liveIsPause.value == false)
            }
        }
    }

    private var recognizeText = ""
    private var translateText = ""
    private var recognizeTempText = ""
    private var translateTempText = ""
    private var downEntity: MontageEntity? = null
    private var currentDirection = MsgDirection.UnKnown
    private var otherTextBean: TextBean? = null
    private var hostTextBean: TextBean? = null

    private fun processMsgSpeakStop(text: String) {
        try {
            upList.lastOrNull { it.text!!.contains(text) }?.let {
                it.showSpeak = false
                upAdapter.notifyDataSetChanged()
            }
        } catch (e: Exception) {
            e.printStackTrace()
        }
    }

    private fun processMsgSpeakStart(text: String) {
        try {
            upList.lastOrNull { it.text!!.contains(text) }?.let {
                it.showSpeak = true
                it.speakText = text
                upAdapter.notifyDataSetChanged()
            }
        } catch (e: Exception) {
            e.printStackTrace()
        }
    }

    private fun processMsgBegin(msgWrapper: MsgWrapper) {
        msgWrapper.data?.let {
            if (it.direction == MsgDirection.Left) {
                recognizeText += " ..."

                if (downEntity == null) {
                    downEntity = MontageEntity()
                    downEntity?.direction = MsgDirection.Right
                    downEntity?.let{ entity ->
                        downList.add(entity)
                    }
                }

                downEntity?.text = recognizeText
                downAdapter.notifyDataSetChanged()
            }
        }
    }

    private fun processMsgEnd(msgWrapper: MsgWrapper) {
        msgWrapper.data?.let {
            if (it.direction == MsgDirection.Left) {
                recognizeText = recognizeText.replace(" ...", "")

                downEntity?.text = recognizeText
                downAdapter.notifyDataSetChanged()
            }
        }
    }

    private fun processMsgRecognize(msg: MsgInterview) {
        msg.text?.let {
            if (msg.direction == MsgDirection.Left) {
                if (mBinding.tvDownTip.isVisible) {
                    mBinding.tvDownTip.gone()
                }

                recognizeText = if (msg.code != null && msg.code!!.contains("zh")) {
                    buildString {
                        append(recognizeTempText)
                        append(it)
                    }
                } else {
                    buildString {
                        append(recognizeTempText)
                        append(" ")
                        append(it)
                    }
                }

                if (downEntity == null) { //这一步可以省略，因为在MsgBegin的时候创建了，此处只是为了保险起见加的
                    downEntity = MontageEntity()
                    downEntity?.direction = MsgDirection.Right
                    downList.add(downEntity!!)
                }

                downEntity?.text = recognizeText
                downEntity?.currentText = it
                downEntity?.isLast = msg.isLast
                downAdapter.notifyDataSetChanged()

                if (!isTouchDown) {
                    RecycleViewUtil.scrollToBottom(mBinding.rvDown)
                }

                if (msg.isLast) {
                    recognizeTempText = buildString {
                        append(recognizeText)
                    }

                    if (recognizeTempText.length > MAX_LENGTH) {
                        logD("准备换行 down length ${recognizeTempText.length}", TAG)
                        downEntity?.isFinish = true
                        downEntity = null

                        recognizeTempText = buildString {
                            append("")
                        }
                    }
                }

            } else if (msg.direction == MsgDirection.Right) {
                if (mBinding.tvUpTip.isVisible) {
                    mBinding.tvUpTip.gone()
                }

                if (currentDirection != MsgDirection.Right) {
                    currentDirection = MsgDirection.Right
                    hostTextBean = null

                    translateTempText = buildString {
                        append("")
                    }
                }

                translateText = if (msg.code != null && msg.code!!.contains("zh")) {
                    buildString {
                        append(translateTempText)
                        append(it)
                    }
                } else {
                    buildString {
                        append(translateTempText)
                        append(" ")
                        append(it)
                    }
                }

                if (hostTextBean == null) {
                    hostTextBean = TextBean()
                    hostTextBean?.direction = MsgDirection.Right
                    upList.add(hostTextBean!!)
                }

                hostTextBean?.text = translateText
                upAdapter.notifyDataSetChanged()

                if (!isTouchUp) {
                    RecycleViewUtil.scrollToBottom(mBinding.rvUp)
                }

                if (msg.isLast) {
                    translateTempText = buildString {
                        append(translateText)
                    }

                    if (translateTempText.length > MAX_LENGTH) {
                        logD("准备换行 self up length ${translateTempText.length}", TAG)
                        hostTextBean = null

                        translateTempText = buildString {
                            append("")
                        }
                    }
                }
            }
        }
    }

    private fun processMsgTranslate(msg: MsgInterview) {
        msg.text?.let {
            if (mBinding.tvUpTip.isVisible) {
                mBinding.tvUpTip.gone()
            }

            if (msg.direction == MsgDirection.Left) {
                if (currentDirection != MsgDirection.Left) {
                    currentDirection = MsgDirection.Left
                    otherTextBean = null

                    translateTempText = buildString {
                        append("")
                    }
                }

                translateText = if (msg.code != null && msg.code!!.contains("zh")) {
                    buildString {
                        append(translateTempText)
                        append(it)
                    }
                } else {
                    buildString {
                        append(translateTempText)
                        append(" ")
                        append(it)
                    }
                }

                if (otherTextBean == null) {
                    otherTextBean = TextBean()
                    otherTextBean?.direction = MsgDirection.Left
                    upList.add(otherTextBean!!)
                }

                otherTextBean?.text = translateText
                upAdapter.notifyDataSetChanged()

                if (!isTouchUp) {
                    RecycleViewUtil.scrollToBottom(mBinding.rvUp)
                }

                if (msg.isLast) {
                    translateTempText = buildString {
                        append(translateText)
                    }

                    if (translateTempText.length > MAX_LENGTH) {
                        logD("准备换行 other up length ${translateTempText.length}", TAG)
                        otherTextBean = null

                        translateTempText = buildString {
                            append("")
                        }
                    }
                }
            }
        }
    }

    private fun processPauseOrResume(isPause: Boolean) {
        lifecycleScope.launchWhenStarted {
            if (isPause) { // 如果当前是暂停/停止状态
                stopPureJob()
                jobNeedScrollToEnd?.cancel()
            } else {
//                startPureJob(5) { enterPureMode() }
            }
        }
    }

    /**
     * 开启纯净模式倒计时
     */
    private fun startPureJob(totalSecond: Int = 5, todo: () -> Unit? = { enterPureMode() }) {
        jobPure?.cancel()
        jobPure = null
        jobPure = countDownCoroutines(totalSecond, lifecycleScope, onTick = { second ->
            if (second == 0) {
                enterPureMode()
                jobPure = null
            }
        })
    }

    private fun stopPureJob() {
        jobPure?.cancel()
        jobPure = null
    }

    // 进入纯净模式
    private fun enterPureMode() {
        if (!mBinding.ivPauseButton.isVisible && !mBinding.ivContinueButton.isVisible) return
        hideControlBar()
        mBinding.ivPauseButton.animateAlpha0()
        mBinding.ivContinueButton.animateAlpha0()
        mBinding.llTopControl.animateAlpha0()
    }

    // 退出纯净模式
    // needAutoEnter：是否需要再退出之后，又自动进入纯净模式
    private fun exitPureMode(needAutoEnter: Boolean = false) {
        showTopBar()
        mBinding.ivPauseButton.animateAlpha1()
        mBinding.ivContinueButton.animateAlpha1()
        mBinding.llTopControl.animateAlpha1()
        if (needAutoEnter) {
            startPureJob(5)
        }
    }

    private val netWorkListener = object : NetworkUtils.OnNetworkStatusChangedListener {
        override fun onDisconnected() {
            DialogFactory.createConfirmCancelDialog(this@VideoActivityMain,
                titleText = BaseApp.context.getString(R.string.common_alert_tip),
                BaseApp.context.getString(R.string.common_network_error_check_it),
                confirmText = BaseApp.context.getString(R.string.common_cancel),
                confirmCall = {},
                cancelText = BaseApp.context.getString(R.string.common_go_setting),
                cancelCall = {
                    wifiForResult.launch(Intent(Settings.ACTION_WIFI_SETTINGS))
                }).show()
        }

        override fun onConnected(networkType: NetworkUtils.NetworkType?) {

        }
    }

    private fun showTopBar() {
        val beginValue = -(mBinding.llTopControl.layoutParams as ConstraintLayout.LayoutParams).height
        val endValue = 0
        val animator = ValueAnimator.ofInt(beginValue, endValue)
        animator.addUpdateListener {
            mBinding.llContent.updateLayoutParams<ConstraintLayout.LayoutParams> {
                topMargin = it.animatedValue as Int
//                mBinding.llContent.requestLayout()
//                mBinding.llContent.invalidate()
//                logD("showtopMargin"+topMargin)
            }
        }

        animator.duration = 300L
        animator.start()
        mBinding.llTopControl.animate().translationY(-0f).alpha(1f).setDuration(300L).start()
        // 底部的控制按钮，向上移动
        val bottomTargetY = -ConvertUtils.dp2px(50f) // 向下移动100个像素
        val animatorBottom = ValueAnimator.ofInt(bottomTargetY, ConvertUtils.dp2px(6f))
        animatorBottom.apply {
            addUpdateListener {
                //根据animatedValue（就是begin和end的差值）的变化，上边距也跟着变化
                mBinding.ivPauseButton.updateLayoutParams<ConstraintLayout.LayoutParams> {
                    bottomMargin = it.animatedValue as Int
                }
                mBinding.ivContinueButton.updateLayoutParams<ConstraintLayout.LayoutParams> {
                    bottomMargin = it.animatedValue as Int
                }
            }
            duration = 300L
            start()
        }
    }

    private fun hideControlBar() {
        lifecycleScope.launchWhenResumed {
            val beginValue = 0
            val endValue = -(mBinding.llTopControl.layoutParams as ConstraintLayout.LayoutParams).height
            val animator = ValueAnimator.ofInt(beginValue, endValue)
            animator.addUpdateListener {
                //根据animatedValue（就是begin和end的差值）的变化，上边距也跟着变化
                mBinding.llContent.updateLayoutParams<ConstraintLayout.LayoutParams> {
                    topMargin = it.animatedValue as Int
                }
            }
            animator.duration = 350L
            animator.start()

            // 底部的控制按钮，向下移动
            val bottomTargetY = -ConvertUtils.dp2px(50f) // 向下移动100个像素
            val animatorBottom = ValueAnimator.ofInt(ConvertUtils.dp2px(6f), bottomTargetY)
            animatorBottom.apply {
                addUpdateListener {
                    //根据animatedValue（就是begin和end的差值）的变化，上边距也跟着变化
                    mBinding.ivPauseButton.updateLayoutParams<ConstraintLayout.LayoutParams> {
                        bottomMargin = it.animatedValue as Int
                    }
                    mBinding.ivContinueButton.updateLayoutParams<ConstraintLayout.LayoutParams> {
                        bottomMargin = it.animatedValue as Int
                    }
                }
                duration = 350L
                start()
            }

            mBinding.llTopControl.animate().translationY(-mBinding.llTopControl.height.toFloat())
                .alpha(0f).setDuration(300L).start()
        }

    }

    private fun getEmptySpeakView(): View {
        return LayoutInflater.from(this)
            .inflate(co.timekettle.module_translate.R.layout.layout_footer_meet, null)
    }

    override fun onKeyDown(keyCode: Int, event: KeyEvent?): Boolean {
        if (keyCode == KeyEvent.KEYCODE_BACK) {
            if (!isKeyCode) {
                isKeyCode = true

                if (mBinding.layoutTip.isVisible) {
                    mBinding.layoutTip.gone()
                    mBinding.lottieTip.cancelAnimation()
                }
            }
        } else if (keyCode == 24 || keyCode == 25) {
            return super.onKeyDown(keyCode, event)
        }

        return true
    }

    override fun onKeyUp(keyCode: Int, event: KeyEvent?): Boolean {
        if (keyCode == KeyEvent.KEYCODE_BACK) {
            if (!isKeyCode) {
                isKeyCode = true
            } else {
                if (mBinding.layoutTip.isVisible) {
                    mBinding.layoutTip.gone()
                    mBinding.lottieTip.cancelAnimation()
                } else {
                    onBackPressed()
                }
            }
        }

        return true
    }

    @Subscribe(threadMode = ThreadMode.MAIN)
    fun onDongleConnectEvent(event: DongleConnectEvent) {
        if (!event.isConnect) {
            showDongleDialog()
        }
    }

    //从设置界面返回去加载设置信息，恢复通道可用
    override fun onResume() {
        super.onResume()
        mViewModel.enterMode(this@VideoActivityMain)
        mViewModel.startMode()
        mViewModel.setSpeakType(1)

        val setting = TransManager.getModelSetting(HomeServiceImplWrap.getUserModel())
        if (SpUtils.getBoolean(SpKey.IS_DEBUG_STATUS, false) || BuildConfig.DEBUG) {
            AiSpeechManager.shareInstance().audioChannels.forEach {
                if (it.recorder == ISpeechConstant.RECORDER.PHONE.toString() &&
                    setting.minVadHostEnergy != "-1") {
                    it.minVadEnergy =
                        BigDecimal(100000).div(setting.minVadHostEnergy.toBigDecimal()).toLong()
                } else if (it.recorder == ISpeechConstant.RECORDER.BLE.toString() &&
                    setting.minVadHeadsetEnergy != "-1") {
                    it.minVadEnergy =
                        BigDecimal(100000).div(setting.minVadHeadsetEnergy.toBigDecimal()).toLong()
                }
            }
        }
        mViewModel.liveFontEnum.value = setting.fontSize
        mViewModel.isShowOriginal.value = setting.isShowOriginal
        mViewModel.modeUtil.setBreakTime((getBreakTimeValue(setting.breakTime) * 1000).toInt())
        AiSpeechManager.shareInstance().audioChannels.forEach {
            SpeakManager.shareInstance().update(it.speakerType, setting.ttsSpeed.value)
        }
        mViewModel.modeUtil.setVoice(setting.ttsVoiceIsManSelf, setting.ttsVoiceIsManOther)

        if (mViewModel.liveIsPause.value == false) {
            mViewModel.enableAllChannel()
        }

        if (BleUtil.shared.connectedPeripherals.size > 0) {
            mViewModel.setSpeakType(1)
//            showToast(getString(R.string.translate_headset_connected))
        }

        startPureJob(5)

        RecycleViewUtil.scrollToBottom(mBinding.rvUp)
        RecycleViewUtil.scrollToBottom(mBinding.rvDown)

    }

    private fun getBreakTimeValue(breakTimeEnum: SettingEnum.BreakTime): Float {
        val breakTimeList = mutableListOf(0.6f, 0.8f, 1.0f, 2.0f, 3.0f) // 单位秒
        return breakTimeList[SettingEnum.BreakTime.values().indexOf(breakTimeEnum)]
    }

    override fun onPause() {
        super.onPause()
        mViewModel.stopMode()
        mViewModel.exitMode()
    }

    override fun onDestroy() {
        if (mViewModel.liveIsPause.value == false) {
            AiSpeechManager.shareInstance().stopAllWorker() //先停止所有播放
        }
        BleUtil.shared.removeListener(bleListener)
        USBHIDController.removeListener(hidListener)
        setLedLevel(this, 0)

        HomeServiceImplWrap.setInMode(false)
        NetworkUtils.unregisterNetworkStatusChangedListener(netWorkListener)
        mViewModel.unInitVideo()

        SensorsUtil.trackEvent(
            SensorsCustomEvent.X1_ExitXXMode.name, hashMapOf(
                "ModelType" to "音视频翻译"
            )
        )

        SensorsUtil.trackEvent(
            SensorsCustomEvent.X1_MediaTranstionDuration.name, hashMapOf(
                "ModeDuration" to (System.currentTimeMillis() - statTime) / 1000
            )
        )

        val self = TransLanguageTool.getFullLanguageName(mViewModel.selfCode)
        val other = TransLanguageTool.getFullLanguageName(mViewModel.otherCode)
        SensorsUtil.trackEvent(
            SensorsCustomEvent.X1_LanguageSelectionMideaTranslation.name, hashMapOf(
                "SelectLanguage" to "$self-$other"
            )
        )

        SensorsUtil.trackEvent(
            SensorsCustomEvent.X1_MediaTranstionUseCase.name, hashMapOf(
                "MediaType" to if (isSingle) "单向" else "双向"
            )
        )
        super.onDestroy()
    }

    private fun processOfflineUiEvent(event: OfflineUiEvent) {
        when (event) {
            OfflineUiEvent.YouCanOpenOffline -> {
                DialogFactory.createConfirmCancelDialog(this@VideoActivityMain,
                    titleText = BaseApp.context.getString(R.string.common_alert_tip),
                    BaseApp.context.getString(R.string.common_network_error_check_it),
                    confirmText = BaseApp.context.getString(R.string.common_cancel),
                    confirmCall = {},
                    cancelText = BaseApp.context.getString(R.string.common_go_setting),
                    cancelCall = {
                        wifiForResult.launch(Intent(Settings.ACTION_WIFI_SETTINGS))
                    }).show()
            }

            OfflineUiEvent.NetWorkError -> {
                showToast(BaseApp.context.getString(R.string.common_network_error_check_it))
            }
        }
    }

    private val wifiForResult =
        registerForActivityResult(ActivityResultContracts.StartActivityForResult()) { result: ActivityResult ->
//            mViewModel.checkWifiAgain()
        }

    companion object {
        const val TAG = "VideoActivityMain"
        const val MAX_LENGTH = 5000
    }

}