package co.timekettle.module_translate.ui.wrapper

import android.content.Context
import android.util.Log
import co.timekettle.module_translate.ui.vm.VideoMainVM
import com.blankj.utilcode.util.FileIOUtils
import com.neviewtech.usbaudio.USBAudio
import com.timekettle.upup.base.BaseApp
import com.timekettle.upup.base.mvvm.vm.BaseViewModel
import com.timekettle.upup.base.utils.logD
import java.io.File
import java.lang.ref.WeakReference

/**
 * NeviewManager包装类，防止内存泄露问题
 *
 * @author: Pengwei Wang
 * @date: 2024/8/6
 */

object NeviewManagerWrapper {
    private var viewModelRef: WeakReference<VideoMainVM>? = null

    private val neviewManager by lazy {
        USBAudio(BaseApp.context,callBack)
    }

    private var r1 = false

    private var r2 = false


    private val callBack  = object : USBAudio.OnUsbCallback {
        override fun writeCapturePcmData(data: ByteArray) {
//                Log.d(TAG, "收到了音频数据 ${data.size}")
            viewModelRef?.get()?.let {
                // 使用 contextRef.get() 获取 context
//                it.audioTrack?.write(data, 0, data.size)
                val res = it.audioPlayChannel.trySend(data)
                if (res.isFailure) Log.d("wrapper", "发送音频失败")
//                it.channelReadCache.write(data)
                // Log.d("asdasdfasdfasdfasdf","size ${channelReadCache.readable()}")
            }
        }

        override fun readPlaybackPcmData(length: Int): ByteArray {

//            logD("asdasdfasdfasdfasdf","length $length")
            Log.d("asdasdfasdfasdfasdf","read once")

            // 播放到对方的音频必须是48K、立体声  值：192000
            viewModelRef?.get()?.let {
                while (it.channelTTSCache.readable() > 0) {
                    Log.d("asdasdfasdfasdfasdf","length ${it.channelTTSCache.readable()}")
                    val packet = ByteArray(length)
                    it.channelTTSCache.read(packet)
                    return packet
                }

                return ByteArray(length)
            }
            return ByteArray(length)
        }
    }


//    fun unInit() {
//        neviewManager.unInit()
//    }

    fun initAudio() {
        if (!r1) r1 = neviewManager.initAudio() >= 0
        if (!r2) r2 = neviewManager.startCapture() >= 0
    }

    fun closeAudio() {
        neviewManager.closeAudio().also {
            r1 = false
            r2 = false
        }
    }

    fun stopCapture(): Int {
        return neviewManager.stopCapture()
    }

    fun startPlayback(): Int {
        return neviewManager.startPlayback()
    }

    fun stopPlayback(): Int {
        return neviewManager.stopPlayback()
    }

    fun setViewModel(viewModel: VideoMainVM?) {
        viewModelRef = WeakReference(viewModel)
    }

    fun isInitSuccess() = r1 && r2
}