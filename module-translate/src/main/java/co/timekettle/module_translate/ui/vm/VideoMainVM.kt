package co.timekettle.module_translate.ui.vm

import android.annotation.SuppressLint
import android.content.Context
import android.media.AudioAttributes
import android.media.AudioFormat
import android.media.AudioRecord
import android.media.AudioTrack
import android.media.MediaRecorder
import android.util.Log
import android.view.Gravity
import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.viewModelScope
import co.timekettle.module_translate.bean.*
import co.timekettle.module_translate.tools.ModeUtil
import co.timekettle.module_translate.tools.OfflineTool
import co.timekettle.module_translate.tools.TransManager
import co.timekettle.module_translate.tools.VideoModeUtil
import co.timekettle.module_translate.ui.interf.TmkAiSpeechListener
import co.timekettle.module_translate.ui.repo.MsgBeanRepository
import co.timekettle.module_translate.ui.repo.MsgDetailRepository
import co.timekettle.module_translate.ui.wrapper.NeviewManagerWrapper
import co.timekettle.speech.AgcProcessor
import co.timekettle.speech.AiSpeechManager
import co.timekettle.speech.AudioChannel
import co.timekettle.speech.ISpeechConstant
import co.timekettle.speech.TestRecorder
import co.timekettle.speech.ispeech.algorithm.ResampleProcessor
import co.timekettle.speech.jni.TmkCustomTranslationJni
import co.timekettle.speech.utils.BytesTrans
import co.timekettle.speech.utils.RingBuffer
import com.blankj.utilcode.util.NetworkUtils
import com.google.gson.Gson
import com.timekettle.upup.base.BaseApp
import com.timekettle.upup.base.mvvm.vm.BaseViewModel
import com.timekettle.upup.base.utils.*
import com.timekettle.upup.comm.R
import com.timekettle.upup.comm.bean.MsgBean
import com.timekettle.upup.comm.bean.MsgDirection
import com.timekettle.upup.comm.bean.MsgInterview
import com.timekettle.upup.comm.bean.MsgWrapper
import com.timekettle.upup.comm.bean.RecognizeResultBean
import com.timekettle.upup.comm.bean.TranslateResultBean
import com.timekettle.upup.comm.constant.SpKey
import com.timekettle.upup.comm.service.home.HomeServiceImplWrap
import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.ExecutorCoroutineDispatcher
import kotlinx.coroutines.Job
import kotlinx.coroutines.asCoroutineDispatcher
import kotlinx.coroutines.channels.Channel
import kotlinx.coroutines.delay
import kotlinx.coroutines.isActive
import kotlinx.coroutines.launch
import kotlinx.coroutines.withContext
import java.io.IOException
import java.lang.reflect.Type
import java.nio.charset.StandardCharsets
import java.util.*
import java.util.concurrent.ArrayBlockingQueue
import java.util.concurrent.Executors
import javax.inject.Inject

/**
 *所有的历史记录
 * @author: licoba
 * @date: 2022/8/12
 * 使用 @HiltViewModel
 */
@HiltViewModel
class VideoMainVM @Inject constructor(
    private val repository: MsgBeanRepository,
    private val detailRepository: MsgDetailRepository
) : BaseViewModel(), TmkAiSpeechListener {

    private val audioDispatcher: ExecutorCoroutineDispatcher =
        Executors.newSingleThreadExecutor().asCoroutineDispatcher()

    var mTranslateMode = HomeServiceImplWrap.getUserModel()
    lateinit var modeUtil: VideoModeUtil
    var liveMsgBegin = MutableLiveData<MsgWrapper>()
    var liveMsgEnd = MutableLiveData<MsgWrapper>()
    var liveMsgSpeakStart = MutableLiveData<String>()
    var liveMsgSpeakStop = MutableLiveData<String>()
    var liveMsgRecognize = MutableLiveData<MsgInterview>()
    var liveMsgTranslate = MutableLiveData<MsgInterview>()
    var liveErrorClose = MutableLiveData<Boolean>()

    lateinit var setting: ProductSetting
    lateinit var selfCode: String
    lateinit var otherCode: String

    val liveIsPause = MutableLiveData(true) //初始是停止/暂停状态
    private var isClickedStartBtn = false  //是否点击过了拾音按钮，也就是使用过翻译功能
    var liveShowPleaseSpeak = MutableLiveData(false) //显示请说XXX的UI，给rv footer
    private var pleaseSpeakJob: Job? = null  //请说XXX
    private var mMsgList = mutableListOf<MsgBean>()
    private val sessionCreateTime = Date()  //会话创建的时间，历史记录时间也用这个作为Key
    var liveFontEnum = MutableLiveData<SettingEnum.FontSize>() //更新字体大小
    var isShowOriginal = MutableLiveData<Boolean>() //是否显示原文
    var liveOfflineUiEvent = MutableLiveData<OfflineUiEvent>() //没有开启离线、支持、断网的离线提示
    private var hasMsg: Boolean = false
    private var dstCode: String? = ""
    private var errCount5012 = 0

    override fun onVadBegin(channel: AudioChannel, session: Long) {
        logD("onVadBegin: session=$session", TAG)
        addOneMsg(createMsg(channel, session))
    }

    override fun onVadEnd(channel: AudioChannel, session: Long) {
        logD("onVadEnd: session=$session", TAG)
        mMsgList.find { it.session == session }.let {
            liveMsgEnd.postValue(MsgWrapper(it, it?.direction))
        }
    }

    override fun onActivity(channel: AudioChannel, session: Long, volume: Float) {

    }

    override fun onRecognizeResult(
        chkey: String,
        session: Long,
        srcCode: String?,
        dstCode: String?,
        isLast: Boolean,
        text: String,
        engine: String?,
        ctr: TmkCustomTranslationJni.TmkCustomTranslationResult?
    ) {
        logD("onRecognizeResult: session=$session isLast=$isLast text=$text srcCode=$srcCode dstCode=$dstCode chKey=$chkey", TAG)
        val bean = RecognizeResultBean(chkey, session, srcCode, dstCode, isLast, text, engine)
        hasMsg = true
        this.dstCode = dstCode
        updateRecognizeMsg(bean)
    }

    override fun onTranslateResult(
        chkey: String?,
        session: Long,
        isLast: Boolean,
        text: String?,
        engine: String?,
        ctr: TmkCustomTranslationJni.TmkCustomTranslationResult?
    ) {
        logD("onTranslateResult: session=$session isLast=$isLast text=$text chKey=$chkey", TAG)
        val bean = TranslateResultBean(chkey, session, isLast, text, engine)
        updateTranslateMsg(bean)

        //把我说的、翻译完成后文本，去合成音频，然后播放到对方
        if (isLast && text.toString().isNotEmpty() && chkey == VideoModeUtil.HOST_KEY) {
            Log.d("VideoMain", "合成自己的TTS:$text")
            syntheticTts(text.toString())
        }

        if (isLast) {
            hasMsg = false
        }
    }

    private fun syntheticTts(text: String) {
        val channel = AiSpeechManager.shareInstance().getAudioChannel(VideoModeUtil.HOST_KEY)
        AiSpeechManager.shareInstance().synthesizeTextBytes(BaseApp.context, otherCode, text, channel) {
            if (it != null) {
                val reSampleBytes = reSample16kTo48k(it)
                val resultData = monoToStereo(reSampleBytes)
//                var start = 0
//                Log.d("VideoMain", "合成自己的TTS长度:${resultData.size}")
//                while (start < resultData.size) {
//                    val end = (start + 7680).coerceAtMost(resultData.size)
//                    val chunk = resultData.copyOfRange(start, end)
//                    Log.d("VideoMain", "offer 一包数据:${chunk.size}")
//                    channelRecordCache.offer(chunk)
//                    start += 7680
//                }
                Log.d("VideoMain", "写入TTS长度:${resultData.size}")
                channelTTSCache.write(resultData)
            }
        }
    }

    override fun onSynthesizeCompleted(
        chkey: String?,
        session: Long,
        data: ByteArray?,
        engine: String?
    ) {

    }

    override fun onError(chkey: String?, session: Long, code: Int, message: String?) {
        logD("onError: session=$session code=$code message=$message", TAG)
        if (code == 5012) { //网络错误的2012错误码
            if (++errCount5012 % 3 == 0) {
                showToast(BaseApp.context.getString(R.string.trans_current_network_is_poor))
            }
        }
    }

    override fun onSpeakStart(
        chkey: String?,
        session: Long,
        text: String?,
        speakerType: String?,
        extData: Any?
    ) {
        logD("onSpeakStart: session=$session", TAG)
        text?.let {
            liveMsgSpeakStart.postValue(it)
        }
    }

    override fun onSpeakEnd(
        chkey: String?,
        session: Long,
        text: String?,
        speakerType: String?,
        extData: Any?
    ) {
        logD("onSpeakEnd: session=$session", TAG)
        text?.let {
            liveMsgSpeakStop.postValue(it)
        }
    }

    private fun createMsg(channel: AudioChannel, session: Long): MsgBean {
        val msgBean = MsgBean()
        msgBean.direction = convertMsgDirection(channel)//根据角色决定type
        msgBean.session = session
        msgBean.srcCode = channel.srcCode
        msgBean.dstCode = channel.dstCode
        return msgBean
    }

    private fun convertMsgDirection(
        channel: AudioChannel
    ): MsgDirection {
        return when (channel.srcCode) { //用角色名判断
            otherCode -> MsgDirection.Left
            else -> MsgDirection.Right
        }
    }

    private fun addOneMsg(msg: MsgBean) {
        mMsgList.add(msg)
        liveMsgBegin.postValue(MsgWrapper(msg, msg.direction))
    }

    private fun updateRecognizeMsg(bean: RecognizeResultBean) {
        val msgBean = mMsgList.find { it.session == bean.session }

        bean.text?.let {
            liveMsgRecognize.postValue(
                MsgInterview(
                    msgBean?.direction,
                    bean.srcCode,
                    bean.isLast,
                    bean.text
                )
            )
        }

        val lastAsrBean = msgBean?.recognizeResult
        if (lastAsrBean != null) { // 临时处理多条消息
            // 最后一条消息未结束, 则直接覆盖/替换(先删除后添加)
            if (!lastAsrBean.isLast) {
                val lastIndex = lastAsrBean.texts.size
                if (lastIndex > 0) {
                    lastAsrBean.texts.removeAt(lastIndex - 1)
                }
            }
            lastAsrBean.texts.add(bean.text ?: "")
            // 合并所有消息
            var allText = ""
            lastAsrBean.texts.forEach {
                allText += it
            }
            bean.text = allText
            bean.texts = lastAsrBean.texts
        }

        msgBean?.recognizeResult = bean

        if (bean.isLast) {
            insertOrUpdateMsgBean(msgBean)
        }

    }

    private fun updateTranslateMsg(bean: TranslateResultBean) {
        val msgBean = mMsgList.find { it.session == bean.session }

        bean.text?.let {
            liveMsgTranslate.postValue(
                MsgInterview(
                    msgBean?.direction,
                    this.dstCode,
                    bean.isLast,
                    bean.text
                )
            )
        }

        val lastMtBean = msgBean?.translateResult
        if (lastMtBean != null) { // 临时处理多条消息
            // 最后一条消息未结束, 则直接覆盖/替换(先删除后添加)
            if (!lastMtBean.isLast) {
                val lastIndex = lastMtBean.texts.size
                if (lastIndex > 0) {
                    lastMtBean.texts.removeAt(lastIndex - 1)
                }
            }
            lastMtBean.texts.add(bean.text ?: "")
            // 合并所有消息
            var allText = ""
            lastMtBean.texts.forEach {
                allText += it
            }
            bean.text = allText
            bean.texts = lastMtBean.texts
        }

        msgBean?.translateResult = bean

        if (bean.isLast) {
            insertOrUpdateMsgBean(msgBean)
        }

    }

    fun enterMode(context: Context) {
        modeUtil.enterMode(context, OfflineTool.getOfflineConfig())
    }

    fun setProductAndMode(offlineSelfCode: String, offlineOtherCode: String) {
        //初始化modelUtils
        modeUtil = VideoModeUtil()
        setting = TransManager.getModelSetting(HomeServiceImplWrap.getUserModel())
        selfCode = setting.motherLanguageCode
        otherCode = setting.foreignLanguageCode

//        selfCode = "en-US"
//        otherCode = "zh-CN"
    }

    fun startMode() {
        insetHistoryEntity()
        modeUtil.updateLang(selfCode, otherCode)//确定左右耳机的源语言和目标语言
        dealOffline()
        modeUtil.startMode(selfCode, otherCode, this@VideoMainVM)
    }

    private fun dealOffline() {
        viewModelScope.launch(Dispatchers.IO) {
            if (!NetworkUtils.isAvailable()) { // 网络不可用
                liveOfflineUiEvent.postValue(OfflineUiEvent.YouCanOpenOffline)
            }
        }
    }

    fun stopMode() {
        modeUtil.stopMode()
        clearHistoryIfEmpty()
    }

    fun exitMode() {
        modeUtil.exitMode()
    }

    fun pauseOrResume() {
        isClickedStartBtn = true
        if (liveIsPause.value == false) { //需要暂停对话
            disableAllChannel()
            hasMsg = false
            liveIsPause.postValue(true)
        } else { // 需要开始对话
            enableAllChannel()
            liveIsPause.postValue(false)
        }
    }

    private fun configLocalSetting() { // 根据本地的settings来配置
        modeUtil.setBreakTime((setting.breakTime.timeValue * 1000).toInt())
        if (setting.isOpenTts) ModeUtil.enableTtsAndPlay()
        else ModeUtil.disableTtsAndPlay()
        modeUtil.setSensitivity(setting.environmentNoise.value)
    }

    fun setSpeakType(type:Int) {
        modeUtil.setSpeakType(type)
    }

    fun enableAllChannel() {
        modeUtil.enableAllChannel()
    }

    private fun disableAllChannel() {
        modeUtil.disableAllChannel()
    }

    //点击播放按钮，更新这条消息的状态
    fun updateMsgBean(msgBean: MsgBean) {
        val index = mMsgList.indexOfFirst { it.session == msgBean.session }
        mMsgList[index] = msgBean
    }

    // 插入一条新的历史记录，在进入会话的时候执行
    private fun insetHistoryEntity() {
        viewModelScope.launch {
            repository.insert(
                HistoryEntity(
                    date = sessionCreateTime,
                    translateModeName = mTranslateMode.getDBName(),
                    title = DateUtils.getTimeShowText(sessionCreateTime).split(" ")[0],
                    messages = mMsgList,
                )
            )
        }
    }

    private fun clearHistoryIfEmpty() {// 如果没有说话，就删除掉这条历史记录
        if (mMsgList.isEmpty()) {
            BaseApp.mCoroutineScope.launch { // 不要使用lifeScope和viewModeScope，Activity销毁时会不执行，导致历史记录丢失
                repository.deleteHistoryByDate(sessionCreateTime)
            }
        }
    }

    private fun insertOrUpdateMsgBean(bean: MsgBean?) = viewModelScope.launch {
        if (bean == null) return@launch
        bean.hDate = sessionCreateTime
        detailRepository.insertOrUpdateMsgBean(bean)
    }

    // 在协程中周期性地执行任务
    private suspend fun doTaskOnInterval(intervalMillis: Long, task: suspend () -> Unit) {
        while (true) { // 无限循环
            task() // 执行任务
            delay(intervalMillis) // 等待指定时间间隔
        }
    }

    /*****************************************************音视频***************************************************************/

//    private var neviewManager = NeviewManagerWrapper(BaseApp.context, this)
    val channelReadCache = RingBuffer(20)
    // 创建一个 Channel 用于音频数据传递
    val audioPlayChannel = Channel<ByteArray>(256)
    var channelRecordCache = ArrayBlockingQueue<ByteArray>(32)
    private val channelRecognizeCache = RingBuffer(20)
    val channelTTSCache = RingBuffer(20)
    private var audioRecord: AudioRecord? = null
    private val resampleProcessor by lazy {
        ResampleProcessor()
    }
    private val originFileHandle by lazy {
        TestRecorder(BaseApp.context, "TK_Record", null,  "recorder-x1-origin", true)
    }
    private val afterFileHandle by lazy {
        TestRecorder(BaseApp.context, "TK_Record", null,  "recorder-x1-after-process", true)
    }

    private val selfAgc by lazy {
        AgcProcessor(ISpeechConstant.DefaultSampleRate, 256, 8.0f)
    }

    private val otherAgc by lazy {
        AgcProcessor(ISpeechConstant.DefaultSampleRate, 256, 4.0f)
    }

    var audioTrack: AudioTrack? = null
    private var audioTrackJob: Job? = null
    private var recordJob: Job? = null
    private var isRecording = false

    fun initVideo() {
//        val r1 = initAudio()
//        var r2 = startCapture()
//        logD("video initAudio $r1", TAG)
//        logD("video startCapture $r2", TAG)
        NeviewManagerWrapper.setViewModel(this)
        startWriteOther()
        startPlay()
        if (!NeviewManagerWrapper.isInitSuccess()) {
            liveErrorClose.postValue(true)
        }
    }

    fun unInitVideo() {
//        stopPlayback()
//        stopCapture()
        stopWriteOther()
        stopRecord()
        NeviewManagerWrapper.setViewModel(null)
//        closeAudio()
//        unInit()
//        setDongleOff()
        stopPlay()
    }

//    private fun unInit() {
//        neviewManager.unInit()
//    }

//    private fun initAudio(): Int {
//        return neviewManager.initAudio()
//    }
//
//    private fun closeAudio() {
//        neviewManager.closeAudio()
//    }
//
//    private fun startCapture(): Int {
//        return neviewManager.startCapture()
//    }
//
//    private fun stopCapture(): Int {
//        return neviewManager.stopCapture()
//    }

    fun startPlayback(): Int {
        return NeviewManagerWrapper.startPlayback()
    }

    fun stopPlayback(): Int {
        return NeviewManagerWrapper.stopPlayback()
    }

//    private fun setDongleOn() {
//        val res = DongleControlUtil.setDongleOn()
//    }
//
//    private fun setDongleOff() {
//        BaseApp.mCoroutineScope.launch {
//            DongleControlUtil.setDongleOff()
//        }
//    }

    @SuppressLint("MissingPermission")
    fun startRecord() {
        if (audioRecord == null) {
            val minBufferSize = AudioRecord.getMinBufferSize(
                16000,
                AudioFormat.CHANNEL_IN_MONO,
                AudioFormat.ENCODING_PCM_16BIT
            )

            audioRecord = AudioRecord(
                MediaRecorder.AudioSource.VOICE_COMMUNICATION, 16000,
                AudioFormat.CHANNEL_IN_MONO, AudioFormat.ENCODING_PCM_16BIT, minBufferSize
            ).apply {
                startRecording()
            }

            val audioData = ByteArray(minBufferSize)

            isRecording = true
            recordJob = Job()
            viewModelScope.launch(Dispatchers.IO + recordJob!!) {
                while (isRecording) {
                    audioRecord?.let {
                        val readSize = it.read(audioData, 0, minBufferSize)
                        if (readSize < 0) {
                            return@launch
                        }
//                        originFileHandle.write(audioData)
                        // 混合tts
//                        val mixedAudioData = if (channelTTSCache.readable() > 0) {
//                            val packet = ByteArray(readSize)
//                            channelTTSCache.read(packet)
//                            mixAudio(audioData, packet)
//                        } else audioData
//
//                        val result = monoToStereo(mixedAudioData)
//                        afterFileHandle.write(result)
//                        channelRecordCache.offer(result)
//                        Log.d("record", "put once")
//                        channelRecordCache.put(audioData)

                        channelRecognizeCache.write(audioData.copyOfRange(0, readSize))
                        while (channelRecognizeCache.readable() >= ISpeechConstant.DefaultBytesPerPacketInMono) {
                            val packet = ByteArray(ISpeechConstant.DefaultBytesPerPacketInMono)
                            channelRecognizeCache.read(packet)

//                            val downBytes = reSample48kTo16k(packet)
//                            AiSpeechManager.shareInstance().writeAudioToChannel(VideoModeUtil.HOST_KEY, downBytes)

                            val inputData = BytesTrans.getInstance().Bytes2Shorts(packet)
                            val outputData = ShortArray(inputData.size)
                            selfAgc.processAgc(outputData, inputData) //加8倍增益
                            AiSpeechManager.shareInstance().writeAudioToChannel(VideoModeUtil.HOST_KEY, outputData)
                        }
                    }
                }
            }
        }
    }

    fun stopRecord() {
        if (audioRecord != null) {
            audioRecord?.stop()
            audioRecord = null
            recordJob?.cancel()
            recordJob = null
            isRecording = false
            channelTTSCache.clear()
        }
    }

    private fun startPlay() {
        val bufferSize = AudioTrack.getMinBufferSize(
            48000,
            AudioFormat.CHANNEL_OUT_MONO,
            AudioFormat.ENCODING_PCM_16BIT
        )

        audioTrack = AudioTrack.Builder()
            .setAudioAttributes(
                AudioAttributes.Builder()
                    .setUsage(AudioAttributes.USAGE_MEDIA)
                    .setContentType(AudioAttributes.CONTENT_TYPE_MUSIC)
                    .build()
            )
            .setAudioFormat(
                AudioFormat.Builder()
                    .setSampleRate(48000)
                    .setChannelMask(AudioFormat.CHANNEL_OUT_MONO)
                    .setEncoding(AudioFormat.ENCODING_PCM_16BIT)
                    .build()
            )
            .setTransferMode(AudioTrack.MODE_STREAM)
            .setBufferSizeInBytes(bufferSize)
            .build()

        audioTrack?.let {
            if (it.state != AudioTrack.STATE_UNINITIALIZED && it.playState != AudioTrack.PLAYSTATE_PLAYING) {
                it.play()
            }
        }
    }

    private fun stopPlay() {
        audioPlayChannel.close()
        audioTrack?.let {
            it.stop()
            it.release()
        }
    }

    private fun startWriteOther() {
        audioTrackJob = Job()
        // 启动音频播放协程 - 使用专用的 audio 线程，保证实时性
        viewModelScope.launch(audioDispatcher + audioTrackJob!!) {
            while (isActive) {
                val packet = audioPlayChannel.receive()
                if (packet.all { it == 0.toByte() }) {
                    continue
                }
                audioTrack?.write(packet, 0, packet.size)
            }
        }
        viewModelScope.launch(Dispatchers.IO + audioTrackJob!!) {
            doTaskOnInterval(8L) {
                while (channelReadCache.readable() >= ISpeechConstant.DefaultBytesPerPacketInMono * 3) {
                    val packet = ByteArray(ISpeechConstant.DefaultBytesPerPacketInMono * 3)
                    channelReadCache.read(packet)

                    // 处理音频数据（计算密集型在默认调度器上执行）
                    val downBytes = withContext(Dispatchers.Default) {
                        reSample48kTo16k(packet)
                    }
                    AiSpeechManager.shareInstance().writeAudioToChannel(VideoModeUtil.OTHER_KEY, downBytes)

//                    Log.d("asdfasdfasdfawetewr","size ${channelReadCache.readable()}")
                }
            }
        }
    }

    private fun stopWriteOther() {
        audioTrackJob?.cancel()
        audioTrackJob = null
    }

    private fun reSample48kTo16k(inDataByteArray: ByteArray): ByteArray {
        val factor = 3
        val outDataArraySize = inDataByteArray.size / factor / 2

        // 转换输入和输出数据为 short 数组
        val inDataArray = BytesTrans.getInstance().Bytes2Shorts(inDataByteArray)
        val outDataArray = ShortArray(outDataArraySize)

        // 调用重采样处理器
        resampleProcessor.resampleDown(outDataArray, inDataArray, 48_000, 16_000, inDataArray.size)

        val outPutData = ShortArray(outDataArray.size)
        otherAgc.processAgc(outPutData, outDataArray) //加4倍增益

        // 转换输出数据为 byte 数组
        return BytesTrans.getInstance().Shorts2Bytes(outPutData)
    }

    private fun reSample16kTo48k(inDataByteArray: ByteArray): ByteArray {
        val factor = 3
        val outDataArraySize = inDataByteArray.size * factor / 2

        // 转换输入和输出数据为 short 数组
        val inDataArray = BytesTrans.getInstance().Bytes2Shorts(inDataByteArray)
        val outDataArray = ShortArray(outDataArraySize)

        // 调用重采样处理器
        resampleProcessor.resampleUp(inDataArray, inDataArray.size, outDataArray, IntArray(1))

        // 转换输出数据为 byte 数组
        return BytesTrans.getInstance().Shorts2Bytes(outDataArray)
    }

    // 双声道变单声道（只取左声道的值）
    private fun stereoToMonoLeftChannel(stereoData: ByteArray): ByteArray {
        // 立体声数据的长度应该是4的倍数，因为每4个字节代表一个立体声帧（2个字节左声道 + 2个字节右声道）
        if (stereoData.size % 4 != 0) {
            throw IllegalArgumentException("Stereo data length must be a multiple of 4.")
        }
        val monoData = ByteArray(stereoData.size / 2)
        for (i in monoData.indices step 2) {
            // 读取左声道的两个字节
            monoData[i] = stereoData[2 * i]
            monoData[i + 1] = stereoData[2 * i + 1]
        }
        return monoData
    }

    // 单声道变双声道（复制一遍）
    private fun monoToStereo(monoData: ByteArray): ByteArray {
        // 每个样本 2 个字节
        val sampleSizeInBytes = 2
        val monoSampleCount = monoData.size / sampleSizeInBytes

        // 立体声的字节数组大小是单声道的两倍
        val stereoData = ByteArray(monoData.size * 2)

        for (i in 0 until monoSampleCount) {
            // 读取单声道样本
            val sampleIndex = i * sampleSizeInBytes
            val sample = byteArrayOf(monoData[sampleIndex], monoData[sampleIndex + 1])

            // 将样本复制到两个声道中
            val stereoIndex = i * sampleSizeInBytes * 2
            stereoData[stereoIndex] = sample[0]
            stereoData[stereoIndex + 1] = sample[1]
            stereoData[stereoIndex + 2] = sample[0]
            stereoData[stereoIndex + 3] = sample[1]
        }

        return stereoData
    }

    private fun mixAudio(recordAudio: ByteArray, ttsAudio: ByteArray): ByteArray {
        val recordSampleCount = recordAudio.size / 2
        val ttsSampleCount = ttsAudio.size / 2

        val mixedAudio = ByteArray(recordAudio.size)

        for (i in 0 until recordSampleCount) {
            val recordSampleIndex = i * 2
            val ttsSampleIndex = (i % ttsSampleCount) * 2

            // 将字节转换为 Short
            val recordSample = ((recordAudio[recordSampleIndex + 1].toInt() shl 8) or (recordAudio[recordSampleIndex].toInt() and 0xFF)).toShort()
            val ttsSample = ((ttsAudio[ttsSampleIndex + 1].toInt() shl 8) or (ttsAudio[ttsSampleIndex].toInt() and 0xFF)).toShort()

            // 混合样本并防止溢出
            val mixedSample =
                (recordSample * 0.3f + ttsSample * 0.7f).coerceIn(Short.MIN_VALUE.toFloat(), Short.MAX_VALUE.toFloat())
                    .toInt()
                    .toShort()

            // 将 Short 转换回字节
            mixedAudio[recordSampleIndex] = (mixedSample.toInt() and 0xFF).toByte()
            mixedAudio[recordSampleIndex + 1] = ((mixedSample.toInt() shr 8) and 0xFF).toByte()
        }

        return mixedAudio
    }

    override fun onCleared() {
        super.onCleared()
        audioDispatcher.close()
    }

    companion object {
        const val TAG = "VideoMainVM"
    }

}