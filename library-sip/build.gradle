plugins {
    id 'com.android.library'
    id 'org.jetbrains.kotlin.android'
}

android {
    compileSdk 32

    defaultConfig {
        minSdk 23
        targetSdk 32

        testInstrumentationRunner "androidx.test.runner.AndroidJUnitRunner"
        consumerProguardFiles "consumer-rules.pro"
    }

    buildTypes {
        release {
            minifyEnabled false
            proguardFiles getDefaultProguardFile('proguard-android-optimize.txt'), 'proguard-rules.pro'
        }
    }

    compileOptions {
        sourceCompatibility JavaVersion.VERSION_1_8
        targetCompatibility JavaVersion.VERSION_1_8
    }

    kotlinOptions {
        jvmTarget = '1.8'
    }

    buildFeatures {
        viewBinding true
    }

}

dependencies {
//    compileOnly fileTree(include: ['*.jar', '*.aar'], dir: 'libs')
//    implementation fileTree(include: ['*.jar', '*.aar'], dir: 'libs')
//    implementation fileTree(include: ['*.jar', '*.aar'], dir: 'src/main/jniLibs')

    implementation fileTree(include: ['*.jar'], dir: 'libs')
    implementation 'com.timekettle.aar:pjsua2:1.0'
    implementation 'androidx.core:core-ktx:1.7.0'
    implementation 'androidx.appcompat:appcompat:1.5.0'
    implementation 'com.google.android.material:material:1.8.0'
    testImplementation 'junit:junit:4.13.2'
    androidTestImplementation 'androidx.test.ext:junit:1.1.4'
    androidTestImplementation 'androidx.test.espresso:espresso-core:3.5.0'
//    api project(path: ':lib-bluetooth')

    api 'com.squareup.okhttp3:okhttp:4.10.0'
    api 'androidx.core:core-ktx:1.7.0'
    api 'com.blankj:utilcodex:1.31.0'
    api 'com.squareup.retrofit2:retrofit:2.9.0'
    api 'com.squareup.retrofit2:converter-gson:2.9.0'
    api 'com.squareup.okhttp3:logging-interceptor:4.10.0'
    api 'com.google.code.gson:gson:2.9.0'
    api 'androidx.lifecycle:lifecycle-runtime-ktx:2.2.0'
    api 'org.greenrobot:eventbus:3.2.0'
}