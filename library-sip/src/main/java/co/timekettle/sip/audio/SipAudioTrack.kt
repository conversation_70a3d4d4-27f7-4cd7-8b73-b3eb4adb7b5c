package co.timekettle.sip.audio

import android.media.AudioFormat
import android.media.AudioManager
import android.media.AudioTrack
import android.util.Log
import org.greenrobot.eventbus.EventBus

class SipAudioTrack {

//    private val mAudioTrack = AudioTrack(
//        mStreamType,
//        mSampleRateInHz,
//        mChannelConfig,
//        mAudioFormat,
//        mMinBufferSize,
//        mMode
//    )

    init {
        //静音
//        mAudioTrack.setStereoVolume(0f, 0f)
//        mAudioTrack.setVolume(1f)

        mAudioTrack = AudioTrack(
            mStreamType,
            mSampleRateInHz,
            mChannelConfig,
            mAudioFormat,
            mMinBufferSize,
            mMode
        )
    }

    fun write(audioData: ByteArray) {
        mAudioTrack?.write(audioData, 0, audioData.size)
//        Log.d(TAG,"track write " + audioData.contentToString())

//        try {
//            Thread.sleep(10)
//        } catch (e: Exception) {
//            e.printStackTrace()
//        }

//        EventBus.getDefault().post(audioData)
    }

    fun start() {
        Log.d(TAG, "audio track start")
        mAudioTrack?.play()
    }

    fun stop() {
        Log.d(TAG, "audio track stop")
        mAudioTrack?.stop()
        mAudioTrack?.flush()
    }

    fun release() {
        Log.d(TAG, "audio track release")
        mAudioTrack?.release()
    }

    companion object {
        private const val TAG = "SipAudioTrack"

        /**
         * 音频流类型
         */
        private const val mStreamType = AudioManager.STREAM_MUSIC

        /**
         * 指定采样率 （MediaRecoder 的采样率通常是8000Hz AAC的通常是44100Hz。
         * 设置采样率为44100，目前为常用的采样率，官方文档表示这个值可以兼容所有的设置）
         */
        private const val mSampleRateInHz = 16000

        /**
         * 指定捕获音频的声道数目。在AudioFormat类中指定用于此的常量
         */
        private const val mChannelConfig = AudioFormat.CHANNEL_OUT_MONO //单声道

        /**
         * 指定音频量化位数 ,在AudioFormaat类中指定了以下各种可能的常量。
         * 通常我们选择ENCODING_PCM_16BIT和ENCODING_PCM_8BIT PCM代表的是脉冲编码调制，它实际上是原始音频样本。
         * 因此可以设置每个样本的分辨率为16位或者8位，16位将占用更多的空间和处理能力,表示的音频也更加接近真实。
         */
        private const val mAudioFormat = AudioFormat.ENCODING_PCM_16BIT

        /**
         * 指定缓冲区大小。调用AudioTrack类的getMinBufferSize方法可以获得。
         */
        private var mMinBufferSize = AudioTrack.getMinBufferSize(mSampleRateInHz, mChannelConfig, mAudioFormat)
//        private const val mMinBufferSize = 640

        /**
         * STREAM的意思是由用户在应用程序通过write方式把数据一次一次得写到audiotrack中。
         * 这个和我们在socket中发送数据一样，
         * 应用层从某个地方获取数据，例如通过编解码得到PCM数据，然后write到audiotrack。
         */
        private const val mMode = AudioTrack.MODE_STREAM

        private var mAudioTrack: AudioTrack? = null

        fun setVolume(gain: Float) {
            mAudioTrack?.setVolume(gain)
        }
    }

}