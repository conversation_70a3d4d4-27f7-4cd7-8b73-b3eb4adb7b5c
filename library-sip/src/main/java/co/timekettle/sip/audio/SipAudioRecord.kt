package co.timekettle.sip.audio

import android.util.Log
import co.timekettle.sip.call.SipConstant
import java.util.concurrent.ArrayBlockingQueue

class SipAudioRecord {

    private lateinit var audioRecord: BaseRecord

    init {
        Log.d(TAG,"record init")
        if (SipConstant.AUDIO_TYPE == 1) {
            audioRecord = MicRecord()
        } else if (SipConstant.AUDIO_TYPE == 2) {
            audioRecord = BleRecord()
        }
    }

    /**
     * audioData（大小为: 640字节）
     */
    fun read(audioData: ByteArray) {
        if (SipConstant.AUDIO_TYPE == 1) {
            val buffer = ByteArray(audioData.size)
            audioRecord.read(buffer)
            System.arraycopy(buffer, 0, audioData, 0, buffer.size)
        } else if (SipConstant.AUDIO_TYPE == 2) {
            if (!hangUp) {
                try {
                    val data = dataQue.take()
                    System.arraycopy(data, 0, audioData, 0, data.size)
//                    Log.d(TAG,"record take")
                } catch (e: Exception) {
                    e.printStackTrace()
                }
            } else {
//                Log.d(TAG,"record hang up")
            }
        }
    }

    fun start() {
        Log.d(TAG,"record start")
        currentThread = Thread.currentThread()
        audioRecord.start()
        dataQue.clear()
        hangUp = false
    }

    fun stop() {
        Log.d(TAG,"record stop")
        audioRecord.stop()
    }

    fun release() {
        Log.d(TAG,"record release")
        audioRecord.release()
    }

    companion object {
        private const val TAG = "SipAudioRecord"
        private var dataQue: ArrayBlockingQueue<ByteArray> = ArrayBlockingQueue<ByteArray>(16)
        private var currentThread: Thread? = null
        var hangUp: Boolean = false

        fun onDataReceive(data: ByteArray) {
            dataQue.put(data)
        }

        fun interrupt() {
            dataQue.clear()
            currentThread?.interrupt()
        }
    }

}