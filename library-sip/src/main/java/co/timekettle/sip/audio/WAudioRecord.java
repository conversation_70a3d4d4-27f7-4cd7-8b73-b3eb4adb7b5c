package co.timekettle.sip.audio;

import java.util.concurrent.ArrayBlockingQueue;

public class WAudioRecord {


    private ArrayBlockingQueue<byte[]> dataQue;

    public WAudioRecord() {
        dataQue = new ArrayBlockingQueue(16);
    }

    public void startRecording() {

    }

    public void read(byte[] audioData) {
        try {
            byte[] data = dataQue.take();
            System.arraycopy(data, 0, audioData, 0, data.length);
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    public void stop() {

    }

    public void release() {

    }

}
