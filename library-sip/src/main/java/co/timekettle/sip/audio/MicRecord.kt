package co.timekettle.sip.audio

import android.annotation.SuppressLint
import android.media.AudioRecord
import android.media.MediaRecorder

class MicRecord: BaseRecord {

    override fun read(buffer: ByteArray) {
        mAudioRecord.read(buffer, 0, buffer.size)
    }

    override fun start() {
        mAudioRecord.startRecording()
    }

    override fun stop() {
        mAudioRecord.stop()
    }

    override fun release() {
        mAudioRecord.release()
    }

    @SuppressLint("MissingPermission")
    private var mAudioRecord = AudioRecord(
        mAudioSource,
        mSampleRateInHz,
        mChannelConfig,
        mAudioFormat,
        mBufferSizeInBytes
    )

    companion object {
        //指定音频源 这个和MediaRecorder是相同的 MediaRecorder.AudioSource.MIC指的是麦克风
        private const val mAudioSource = MediaRecorder.AudioSource.MIC

        //指定采样率 （MediaRecoder 的采样率通常是8000Hz AAC的通常是44100Hz。设置采样率为44100，
        // 目前为常用的采样率，官方文档表示这个值可以兼容所有的设置）
        private const val mSampleRateInHz = 16000

        //指定捕获音频的声道数目。在AudioFormat类中指定用于此的常量
        private const val mChannelConfig: Int = 16 //单声道

        //指定音频量化位数 ,在AudioFormaat类中指定了以下各种可能的常量。通常我们选择ENCODING_PCM_16BIT
        // 和ENCODING_PCM_8BIT PCM代表的是脉冲编码调制，它实际上是原始音频样本。
        //因此可以设置每个样本的分辨率为16位或者8位，16位将占用更多的空间和处理能力,表示的音频也更加接近真实。
        private const val mAudioFormat: Int = 2

        //指定缓冲区大小。调用AudioRecord类的getMinBufferSize方法可以获得。
        private val mBufferSizeInBytes = AudioRecord.getMinBufferSize(mSampleRateInHz, mChannelConfig, mAudioFormat) //计算最小缓冲区
    }

}