package co.timekettle.sip.call

import android.util.Log
import org.pjsip.pjsua2.*

class SipAccount: Account() {

    var regStateBack: ((code: Int, msg: String) -> Unit)? = null
    var incomingCallBack: ((call: SipCall, msg: String) -> Unit)? = null

    companion object {
        const val TAG: String = "SipAccount"
    }

    override fun onRegState(prm: OnRegStateParam) {
        var msg = ""
        if (prm.code == 200) {
            val wholeMsg = prm.rdata.wholeMsg
            val fromIndex = wholeMsg.indexOf("From:")
            val toIndex = wholeMsg.indexOf("<sip:")
            msg = wholeMsg.substring(fromIndex + "From:".length, toIndex).trim()
        }

        Log.d(TAG, "msg: $msg")

        regStateBack?.invoke(prm.code, msg)
    }

    override fun onIncomingCall(prm: OnIncomingCallParam) {
        Log.d(TAG, "onIncomingCall")
//        val call = SipCall(this, prm.callId)
        val call = SipCall(this, prm.callId).apply {
            callStateBack = CallManager::callStateBack
            callMessageBack = CallManager::callMessageStateBack
        }
        incomingCallBack?.invoke(call, prm.rdata.wholeMsg)
//        CallManager.updateCall(call)
    }

    override fun onInstantMessage(prm: OnInstantMessageParam) {
        Log.d(TAG, "onInstantMessage")
    }

}