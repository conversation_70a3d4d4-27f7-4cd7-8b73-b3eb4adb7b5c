package co.timekettle.sip.call

import co.timekettle.sip.entity.*
import retrofit2.Response
import retrofit2.http.GET
import retrofit2.http.Query

interface SipApiService {

    @GET("get_iplist.php?")
    suspend fun getIpList(
        @Query("mac") mac: String
    ): Response<ResEntity<List<IpEntity>>>

    @GET("auth.php?")
    suspend fun getUserPwd(
        @Query("mac") mac: String,
        @Query("lang") lang: String,
    ): Response<ResEntity<PwdEntity>>

    @GET("conf.php?")
    suspend fun getMeetId(
        @Query("mac") mac: String,
        @Query("password") password: String,
        @Query("iplist_id") iplist_id: Int,
    ): Response<ResEntity<MeetIdEntity>>

    @GET("check.php?")
    suspend fun getMeetFlag(
        @Query("mac") mac: String,
        @Query("group") group: String
    ): Response<ResEntity<MeetFlagEntity>>

    @GET("passwd.php?")
    suspend fun encryptMeet(
        @Query("mac") mac: String,
        @Query("group") group: String,
        @Query("password") password: String
    ): Response<ResEntity<String>>

}