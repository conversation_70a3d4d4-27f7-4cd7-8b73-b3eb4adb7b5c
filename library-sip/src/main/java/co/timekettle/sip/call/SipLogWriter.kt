package co.timekettle.sip.call

import android.os.Environment
import android.util.Log
import org.pjsip.pjsua2.LogEntry
import org.pjsip.pjsua2.LogWriter
import java.io.File
import java.io.FileOutputStream
import java.io.IOException
import java.text.SimpleDateFormat
import java.util.Date
import java.util.Locale
import kotlin.reflect.KMutableProperty0

class SipLogWriter(private val callBack: (Int, String) -> Unit): LogWriter() {

    override fun write(entry: LogEntry?) {
        entry?.let {

            if (it.msg.contains(MASTER_SOUND_TAG)) return

//            val msg = entry.msg
//            if (msg.contains(START_TAG) && msg.contains(END_TAG)) {
//                val startIndex = msg.indexOf(START_TAG)
//                val endIndex = msg.indexOf(END_TAG)
//                val message = msg.substring(startIndex, endIndex + END_TAG.length)
//                callBack.invoke(2000, message)
//                Log.d("asdfjajsdfjasjdf"," $message")
//            }

//            if (it.msg.contains("Error sending RTCP: Network is unreachable")) {
//                callBack.invoke(3001, "Error sending RTCP: Network is unreachable")
//            }
        }
    }

    // 定义日志文件的名称和路径
    private val LOG_FILE_NAME = "log.txt"
    private val LOG_DIR_NAME = "sip-log"

    private fun writeLogToFile(message: String) {
        val logDir = File(Environment.getExternalStorageDirectory(), LOG_DIR_NAME)

        // 创建日志目录（如果不存在）
        if (!logDir.exists()) {
            logDir.mkdirs()
        }

        val format = SimpleDateFormat("yyyy-MM-dd-HH", Locale.getDefault()).format(Date())

        val logFile = File(logDir, "$format.txt")

        try {
            // 创建日志文件（如果不存在）
            if (!logFile.exists()) {
                logFile.createNewFile()
            }

            // 获取当前时间
            val timestamp = SimpleDateFormat("yyyy-MM-dd HH:mm:ss", Locale.getDefault()).format(Date())

            // 构建日志内容
            val logMessage = "$timestamp: $message\n"

            // 写入日志内容到文件
            val outputStream = FileOutputStream(logFile, true)
            outputStream.write(logMessage.toByteArray())
            outputStream.close()
        } catch (e: IOException) {
            e.printStackTrace()
        }
    }

    companion object {
        const val MASTER_SOUND_TAG= "Master/sound"
        const val START_TAG= "{\"type\":6"
        const val END_TAG= "}}}"
    }

}