package co.timekettle.sip.call

import android.os.Looper
import android.text.TextUtils
import android.util.Log
import okhttp3.OkHttpClient
import okhttp3.logging.HttpLoggingInterceptor
import org.pjsip.pjsua2.*
import retrofit2.Retrofit
import retrofit2.converter.gson.GsonConverterFactory
import java.util.concurrent.TimeUnit

object CallManager {

    const val TAG: String = "CallManager"
    private const val SIP_PORT = 6000L
    private const val LOG_LEVEL = 4L

    private val sipConfig = TransportConfig()
    private val epConfig = EpConfig()
    private var accCfg: AccountConfig? = null
    private var sipAccount: SipAccount? = null
    private var logWriter: SipLogWriter? = null

    // 日志拦截器部分
    private val logInterceptor = HttpLoggingInterceptor().setLevel(HttpLoggingInterceptor.Level.BODY)
    private var okHttpClient= OkHttpClient.Builder()
        .addInterceptor(logInterceptor)
        .connectTimeout(10L * 1000L, TimeUnit.MILLISECONDS)
        .readTimeout(10L * 1000L, TimeUnit.MILLISECONDS)
        .retryOnConnectionFailure(true)
        .build()
    var ep: Endpoint? = null
    var currentCall: SipCall? = null
    lateinit var sipApiService: SipApiService

    /**
     * 初始化
     * 注意：所有的调用都是由Endpoint发起的
     * type: 0: 正式环境 1: 测试环境
     */
    fun init(type: Int): String {
        var url: String
        when(type) {
            1 -> {
                url = SipConstant.RELEASE_URL
            }

            2 -> {
                url = SipConstant.TEST_URL
            }

            3 -> {
                url = SipConstant.DEV_URL
            }

            else -> {
                url = SipConstant.TEST_URL
            }
        }

        Log.d(TAG,"init url = $url")

        sipApiService = Retrofit.Builder()
            .addConverterFactory(GsonConverterFactory.create())
            .baseUrl(url)
            .client(okHttpClient)
            .build().create(SipApiService::class.java)

        logWriter?:run {
            logWriter = SipLogWriter(this::sipLogBack)

            try {
                ep = Endpoint().apply {
                    libCreate()
                    sipConfig.port = SIP_PORT
                    epConfig.logConfig.level = LOG_LEVEL
                    epConfig.logConfig.consoleLevel = LOG_LEVEL

                    val logConfig = epConfig.logConfig
                    logConfig.writer = logWriter
                    logConfig.decor = logConfig.decor and (pj_log_decoration.PJ_LOG_HAS_CR or pj_log_decoration.PJ_LOG_HAS_NEWLINE).inv().toLong()

                    val uaConfig = epConfig.uaConfig
                    uaConfig.userAgent = "pjsua2 android " + libVersion().full
                    libInit(epConfig)
                    transportCreate(pjsip_transport_type_e.PJSIP_TRANSPORT_TCP, sipConfig)

                    // 获取所有编解码器信息
                    val codecs: CodecInfoVector2? = codecEnum2()
                    codecs?.let {
                        it.forEach {codec ->
                            Log.d(TAG,"codecId ${codec.codecId}")
                            if (!codec.codecId.contains("opus", ignoreCase = true)) {
                                // 将除了opus编码器，其他编码器的优先级设置为 0，即移除这些编码器
                                codecSetPriority(codec.codecId, 0)
                            }
                        }
                    }

                    libStart()
                }
            } catch (e: Exception) {
                println(e)
            }
        }

        return url
    }

    /**
     * 创建账号
     * username: 用户名（如:2001）
     * password: 用户密码（如:2001）
     * accId: accId（如:sip:2001@************:5060）
     * registrar: registrar（如:sip:************:5060）
     */
    fun createAccount(nickName: String, username: String, password: String, accId: String, registrar: String) {
        sipAccount?: run {
            Log.d(TAG,"createAccount thread name ${Thread.currentThread().name}")
            assert(Thread.currentThread() == Looper.getMainLooper().thread){ "createAccount 方法必须在主线程中执行" }

            accCfg = AccountConfig().apply {
//                idUri = accId
                idUri = "$nickName <$accId>"
                regConfig.registrarUri = registrar
                val cReds = sipConfig.authCreds
                cReds.clear()
                if (!TextUtils.isEmpty(username)) {
                    cReds.add(AuthCredInfo("Digest", "*", username, 0, password))
                }
                natConfig.iceEnabled = true
                videoConfig.autoTransmitOutgoing = true
                videoConfig.autoShowIncoming = true
            }

            sipAccount = SipAccount().apply {
                create(accCfg)
                regStateBack = CallManager::regStateBack
                incomingCallBack = CallManager::incomingCallBack
            }
        }
    }

    fun modifyAccount(nickName: String, username: String, password: String, accId: String, registrar: String) {
        Log.d(TAG,"modifyAccount thread name ${Thread.currentThread().name}")
        assert(Thread.currentThread() == Looper.getMainLooper().thread){ "modifyAccount 方法必须在主线程中执行" }

        accCfg = AccountConfig().apply {
            idUri = "$nickName <$accId>"
            regConfig.registrarUri = registrar
            val cReds = sipConfig.authCreds
            cReds.clear()
            if (!TextUtils.isEmpty(username)) {
                cReds.add(AuthCredInfo("Digest", "*", username, 0, password))
            }
        }

        sipAccount?.modify(accCfg)
    }

    /**
     * 获取账号创建状态（true: 已创建，false：未创建）
     */
    fun accountIsInitialized(): Boolean {
        return sipAccount != null
    }

    /**
     * 创建账号之后，注册账号，如果注册失效，可调此方法重新注册
     * renew: 是否重新注册，true: 重新注册，false: 注销
     */
    fun setRegistration(renew: Boolean) {
        sipAccount?.setRegistration(renew)
    }

    /**
     * 获取注册状态
     */
    fun regIsActive(): Boolean {
        sipAccount?.let {
            return it.info.regIsActive
        }
        return false
    }

    /**
     * 添加联系人
     */
    fun addBuddy(uri: String) {
        sipAccount?.let {
            val cfg = BuddyConfig()
            cfg.uri = uri
//            var bud = SipBuddy()
            try {
//                bud.create(it, cfg)
//                bud.delete()
            }catch (e: Exception) {
//                bud.delete()
            }
        }
    }

    /**
     * 删除call
     */
    fun deleteCall(call: SipCall) {
        call.delete()
    }

    /**
     * 来电处理
     */
    fun updateCall(call: SipCall, callBack: (() -> Unit)? = null) {
        Log.d(TAG,"updateCall thread name ${Thread.currentThread().name}")
        assert(Thread.currentThread() == Looper.getMainLooper().thread){ "updateCall 方法必须在主线程中执行" }

        val prm = CallOpParam()
        if (currentCall != null) {
            call.delete()
            return
        }

        callBack?.invoke()

        prm.statusCode = pjsip_status_code.PJSIP_SC_RINGING
        try {
            call.answer(prm)
        } catch (e: Exception) {
            e.printStackTrace()
        }

        currentCall = call
    }

    /**
     * 接听来电
     */
    fun accept() {
        Log.d(TAG,"accept thread name ${Thread.currentThread().name}")
        assert(Thread.currentThread() == Looper.getMainLooper().thread){ "accept 方法必须在主线程中执行" }

        val prm = CallOpParam()
        prm.statusCode = pjsip_status_code.PJSIP_SC_OK
        try {
            currentCall?.answer(prm)
        } catch (e: Exception) {
            println(e)
        }
    }

    /**
     * uri: 呼叫号码
     * srcCode: 己方语言
     */
    fun makeCall(uri: String, srcCode: String = "zh-CN") {
        Log.d(TAG,"makeCall thread name ${Thread.currentThread().name}")
        assert(Thread.currentThread() == Looper.getMainLooper().thread){ "makeCall 方法必须在主线程中执行" }

        if (currentCall == null) {
            Log.e(TAG,"currentCall is null")
        } else {
            Log.e(TAG,"currentCall is not null")
            hangupCall()
        }

        currentCall?: run {
            sipAccount?.let {
                val call = SipCall(it, -1).apply {
                    callStateBack = CallManager::callStateBack
                    callMessageBack = CallManager::callMessageStateBack
                }

                val callParam = CallOpParam(true)
                val vector = SipHeaderVector()
                val header = SipHeader()

                header.hName = "rLANG"
                header.hValue = srcCode

                vector.add(header)
                callParam.txOption.headers = vector

                val config = ep?.codecOpusConfig
                config?.complexity = 10
                config?.sample_rate = 16000
                config?.channel_cnt = 1
                config?.bit_rate = 20000
                config?.cbr = true
                ep?.codecOpusConfig = config

//                Log.e("wsdfsdfsdfsdfdsf","complexity " + ep?.codecOpusConfig?.complexity)
//                Log.e("wsdfsdfsdfsdfdsf","sample_rate " + ep?.codecOpusConfig?.sample_rate)
//                Log.e("wsdfsdfsdfsdfdsf","channel_cnt " + ep?.codecOpusConfig?.channel_cnt)
//                Log.e("wsdfsdfsdfsdfdsf","bit_rate " + ep?.codecOpusConfig?.bit_rate)
//                Log.e("wsdfsdfsdfsdfdsf","cbr " + ep?.codecOpusConfig?.cbr)
//                Log.e("wsdfsdfsdfsdfdsf","frm_ptime " + ep?.codecOpusConfig?.frm_ptime)
//                Log.e("wsdfsdfsdfsdfdsf","packet_loss " + ep?.codecOpusConfig?.packet_loss)

                try {
                    call.makeCall(uri, callParam)
                } catch (e: Exception) {
                    println(e)
                    return
                }

                currentCall = call
            }
        }
    }

    /**
     * 挂断电话
     */
    fun hangupCall() {
        Log.d(TAG,"hangupCall thread name ${Thread.currentThread().name}")
        assert(Thread.currentThread() == Looper.getMainLooper().thread){ "hangupCall 方法必须在主线程中执行" }

        currentCall?.let {
            val callParam = CallOpParam()
            callParam.statusCode = pjsip_status_code.PJSIP_SC_DECLINE
            try {
                it.hangup(callParam)
            } catch (e: Exception) {
                println(e)
            }
        }
    }

    /**
     * 退出页面，释放资源
     */
    fun deInit() {
        Runtime.getRuntime().gc()
        ep?.let {
            try {
                it.libDestroy()
            } catch (e: Exception) {
                println(e)
            }
            it.delete()
        }
        ep = null
        logWriter = null
        accCfg = null
        sipAccount = null
        currentCall = null

//        Process.killProcess(Process.myPid())
    }

    var onCallStateBack: ((Int, Int, String, String, String, String) -> Unit)? = null
    var onCallMessageBack: ((String) -> Unit)? = null
    var onRegStateBack: ((Int, String) -> Unit)? = null
    var onSipLogBack: ((Int, String) -> Unit)? = null
    var onIncomingCallBack: ((SipCall, String) -> Unit)? = null

}