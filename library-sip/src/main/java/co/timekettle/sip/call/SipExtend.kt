package co.timekettle.sip.call

import android.os.Looper
import android.util.Log
import co.timekettle.sip.audio.SipAudioRecord
import co.timekettle.sip.entity.*
import co.timekettle.sip.utils.GsonUtil
import org.pjsip.pjsua2.*
import org.pjsip.pjsua2.pjsua_dtmf_method.PJSUA_DTMF_METHOD_SIP_INFO
import retrofit2.Response

/**
 * 发送指令到服务
 * request: 请求参数
 * return 发送结果（成功 或 失败）
 */
fun CallManager.sendSip(request: ReqEntity): Boolean {
    var result = false
    currentCall?.let {
        Log.d(TAG,"sendSip thread name ${Thread.currentThread().name}")
        assert(Thread.currentThread() == Looper.getMainLooper().thread){ "sendSip 方法必须在主线程中执行" }

        val messageParam = SendInstantMessageParam()
        messageParam.content = request.toJson()
        try {
            it.sendInstantMessage(messageParam)
            result = true
            Log.d(TAG, "Message sent successfully")
        } catch (e: Exception) {
            result = false
            e.printStackTrace()
            Log.e(TAG, "Error sending message: " + e.message)
        }
    }
    return result
}

/**
 * 回传原文
 */
fun CallManager.sendOrigin(entity: TextEntity): Boolean {
    var result = false
    currentCall?.let {
        Log.d(TAG,"sendOrigin thread name ${Thread.currentThread().name}")
        assert(Thread.currentThread() == Looper.getMainLooper().thread){ "sendOrigin 方法必须在主线程中执行" }

        val messageParam = SendInstantMessageParam()
        messageParam.content = entity.toJson()
        try {
            it.sendInstantMessage(messageParam)
            result = true
            Log.d(TAG, "origin sent successfully")
        } catch (e: Exception) {
            result = false
            e.printStackTrace()
            Log.e(TAG, "Error sending origin: " + e.message)
        }
    }
    return result
}

/**
 * 电话挂断之后的操作（仅限于ble）
 * 主要是为了解决阻塞队列卡死主线程的问题
 */
fun sendLastBleFrame() {
//    SipAudioRecord.onDataReceive(ByteArray(640))
    SipAudioRecord.hangUp = true
    SipAudioRecord.interrupt()
    Log.d("SipAudioRecord","record send true")

//    GlobalScope.launch(Dispatchers.IO) {
//        while (!SipConstant.IS_AUDIO_RELEASED) {
//            Log.d(TAG, "sendLastBleFrame")
//            SipAudioRecord.onDataReceive(ByteArray(640))
//            Thread.sleep(20)
//        }
//    }
}

/**
 * 发送音频帧
 * data: 音频帧
 */
fun sendFrame(data: ByteArray) {
    SipAudioRecord.onDataReceive(data)
}

/**
 * 发送心跳
 */
fun CallManager.sendHeart(): Boolean {
    var result = false
    currentCall?.let {
        Log.d(TAG,"sendHeart thread name ${Thread.currentThread().name}")
        assert(Thread.currentThread() == Looper.getMainLooper().thread){ "sendHeart 方法必须在主线程中执行" }

        val callParam = CallSendRequestParam()
        val sipTx = SipTxOption()
        callParam.method = "OPTIONS"
        callParam.txOption = sipTx
        try {
            it.sendRequest(callParam)
            result = true
            Log.d(TAG, "heart sent successfully")
        } catch (e: Exception) {
            result = false
            e.printStackTrace()
            Log.e(TAG, "Error sending heart: " + e.message)
        }
    }
    return result
}

/**
 * 开启音频流
 */
fun CallManager.startAudio() {
    currentCall?.let {call ->
        ep?.audDevManager()?.captureDevMedia?.startTransmit(call.audioMedia)
        call.audioMedia?.startTransmit(ep!!.audDevManager().playbackDevMedia)
    }
}

/**
 * 关闭音频流
 */
fun CallManager.stopAudio() {
    currentCall?.let {call ->
        ep?.audDevManager()?.captureDevMedia?.stopTransmit(call.audioMedia)
        call.audioMedia?.stopTransmit(ep!!.audDevManager().playbackDevMedia)
    }
}

fun CallManager.sendDtmf(value: String) {
    currentCall?.let {
        Log.d(TAG,"sendDtmf thread name ${Thread.currentThread().name}")
        assert(Thread.currentThread() == Looper.getMainLooper().thread){ "sendDtmf 方法必须在主线程中执行" }

        var prm = CallSendDtmfParam()
        prm.digits = value
        prm.method = PJSUA_DTMF_METHOD_SIP_INFO

        try {
            currentCall?.sendDtmf(prm)
        } catch (e: Exception) {
            e.printStackTrace()
            Log.e(TAG, "Error send dtmf: " + e.message)
        }
    }
}

fun CallManager.sendRtcp() {
    currentCall?.let { call ->
        call.sendRtcp()
    }
}

/**
 * 获取ip列表
 * mac: 设备mac地址
 */
suspend fun CallManager.getIpList(mac: String): Response<ResEntity<List<IpEntity>>> {
    return sipApiService.getIpList(mac)
}

/**
 * 获取用户密码
 * mac: 设备mac地址
 * lang: 己方语言
 */
suspend fun CallManager.getUserPwd(mac: String, lang: String): Response<ResEntity<PwdEntity>> {
    return sipApiService.getUserPwd(mac, lang)
}

/**
 * 获取会议id号
 * mac: 设备mac地址
 * iplist_id: ip地址对应的id号
 */
suspend fun CallManager.getMeetId(mac: String, password: String, iplist_id: Int): Response<ResEntity<MeetIdEntity>> {
    return sipApiService.getMeetId(mac, password, iplist_id)
}

/**
 * 获取入会是否需要输入密码
 * mac: 设备mac地址
 * group: 会议id
 */
suspend fun CallManager.getMeetFlag(mac: String, group: String): Response<ResEntity<MeetFlagEntity>> {
    return sipApiService.getMeetFlag(mac, group)
}

/**
 * 会议加密
 * mac: 设备mac地址
 * group: 会议id
 * password: 会议密码
 */
suspend fun CallManager.encryptMeet(mac: String, group: String, password: String): Response<ResEntity<String>> {
    return sipApiService.encryptMeet(mac, group, password)
}

/**
 * 获取当前通话状态
 */
fun CallManager.callStateBack(call: Call) {
    val ci = call.info
    if(ci.state == pjsip_inv_state.PJSIP_INV_STATE_CONFIRMED) {
        SipAudioRecord.hangUp = false
        Log.d("SipAudioRecord","record send false")
    }
    if (ci.state == pjsip_inv_state.PJSIP_INV_STATE_DISCONNECTED) {
        pjsip_inv_state.PJSIP_INV_STATE_EARLY
        currentCall?.delete()
        currentCall = null

        sendLastBleFrame()
    }

    onCallStateBack?.invoke(ci.state, ci.role, ci.stateText, ci.lastReason, ci.remoteUri, ci.localUri)
}

/**
 * 获取通话数据
 */
fun CallManager.callMessageStateBack(msg: String) {
    onCallMessageBack?.invoke(msg)
}

/**
 * 获取账号注册状态
 */
fun CallManager.regStateBack(code: Int, msg: String) {
    onRegStateBack?.invoke(code, msg)
}

/**
 * 获取sip层日志信息
 */
fun CallManager.sipLogBack(code: Int, msg: String) {
    onSipLogBack?.invoke(code, msg)
}

/**
 * 获取来电信息
 */
fun CallManager.incomingCallBack(call: SipCall, msg: String) {
    onIncomingCallBack?.invoke(call, msg)
}

/**
 * 设置录音来源（1: 系统录音；2: Ble）
 */
fun setAudioType(type: Int) {
    SipConstant.AUDIO_TYPE = type
}

fun CallManager.libRegisterThread(name: String) {
    ep?.libRegisterThread(name)
}

/**
 * 获取录音来源
 */

fun getAudioType(): Int {
    return SipConstant.AUDIO_TYPE;
}

fun ReqEntity.toJson(): String {
    return GsonUtil.toJson(this)
}

fun TextEntity.toJson(): String {
    return GsonUtil.toJson(this)
}
