package co.timekettle.sip.call

import android.util.Log
import org.pjsip.pjsua2.*

class SipCall(acc: SipAccount, callId: Int) : Call(acc, callId) {

    var audioMedia: AudioMedia? = null
    var callStateBack: ((call: Call) -> Unit)? = null
    var callMessageBack: ((msg: String) -> Unit)? = null

    companion object {
        const val TAG: String = "SipCall"
    }

    override fun onCallState(prm: OnCallStateParam) {
        Log.d(TAG, "onCallState remoteUri " + this.info.remoteUri)
        Log.d(TAG, "onCallState lastReason " + this.info.lastReason)
        Log.d(TAG, "onCallState stateText " + this.info.stateText)
        Log.d(TAG, "onCallState state " + this.info.state)
        Log.d(TAG, "onCallState callIdString " + this.info.callIdString)
        Log.d(TAG, "onCallState localContact " + this.info.localContact)
        Log.d(TAG, "onCallState localUri " + this.info.localUri)
        Log.d(TAG, "onCallState remoteContact " + this.info.remoteContact)
        Log.d(TAG, "onCallState accId " + this.info.accId)
        Log.d(TAG, "onCallState role " + this.info.role)
        callStateBack?.invoke(this)
    }

    override fun onCallMediaState(prm: OnCallMediaStateParam) {
//        Log.d(TAG, "onCallMediaState")
        val ci: CallInfo = try {
            this.info
        } catch (e: Exception) {
            return
        }

        val cMiv = ci.media

        for (i in cMiv.indices) {
            val cmi = cMiv[i]
            if (cmi.type == pjmedia_type.PJMEDIA_TYPE_AUDIO &&
                (cmi.status == pjsua_call_media_status.PJSUA_CALL_MEDIA_ACTIVE ||
                        cmi.status == pjsua_call_media_status.PJSUA_CALL_MEDIA_REMOTE_HOLD)) {
                try { // connect ports
                    audioMedia = getAudioMedia(i)
                    CallManager.ep!!.audDevManager().captureDevMedia.startTransmit(audioMedia)
                    audioMedia?.startTransmit(CallManager.ep!!.audDevManager().playbackDevMedia)
                } catch (e: Exception) {
                    println("Failed connecting media ports" + e.message)
                }
                break
            }
        }
    }

    override fun onInstantMessage(prm: OnInstantMessageParam) {
//        Log.d(TAG, "onInstantMessage ${prm.msgBody}")
        prm.msgBody?.let { callMessageBack?.invoke(it) }
    }

}