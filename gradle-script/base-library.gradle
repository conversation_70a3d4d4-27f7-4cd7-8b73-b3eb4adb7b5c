/**
 * 公共 library gradle 脚本
 *
 * <AUTHOR> @since 2022/04/15
 */
apply plugin: GradlePluginId.ANDROID_LIBRARY
apply plugin: GradlePluginId.KOTLIN_ANDROID
apply plugin: GradlePluginId.KOTLIN_KAPT
apply plugin: GradlePluginId.HILT
//apply plugin: GradlePluginId.SENSORS_DATA

android {
    compileSdkVersion AndroidConfig.COMPILE_SDK_VERSION
    buildToolsVersion AndroidConfig.BUILD_TOOLS_VERSION

    defaultConfig {
        minSdkVersion AndroidConfig.MIN_SDK_VERSION
        targetSdkVersion AndroidConfig.TARGET_SDK_VERSION
        versionCode AndroidConfig.VERSION_CODE
        versionName AndroidConfig.VERSION_NAME

        ndk {
            // 设置支持的SO库架构 'armeabi', 'x86', 'armeabi-v7a', 'x86_64', 'arm64-v8a'
            abiFilters 'arm64-v8a','armeabi-v7a'
        }

        javaCompileOptions {
            annotationProcessorOptions {
                // Room 配置
                // room.schemaLocation：配置并启用将数据库架构导出到给定目录中的 JSON 文件的功能。
                // room.incremental：启用 Gradle 增量注解处理器。
                // room.expandProjection：配置 Room 以重写查询，使其顶部星形投影在展开后仅包含 DAO 方法返回类型中定义的列。
                arguments += [
                        "room.incremental"     : "true",
                        "room.expandProjection": "true"]
            }
        }
    }

    compileOptions {
        sourceCompatibility JavaVersion.VERSION_11
        targetCompatibility JavaVersion.VERSION_11
    }

    kotlinOptions {
        jvmTarget = 11
    }

    buildTypes {
//        def version = AndroidConfig.Version
        String fieldName = "VERSION_TYPE"
        String fieldType = "String"

        // 开发环境
        debug {
            buildConfigField fieldType, fieldName, "\"${AppVersion.DEBUG}\""
            minifyEnabled false
            proguardFiles getDefaultProguardFile('proguard-android-optimize.txt'), 'proguard-rules.pro'
        }

        // 公开测试版
        beta {
            buildConfigField fieldType, fieldName, "\"${AppVersion.BETA}\""
            minifyEnabled false
            proguardFiles getDefaultProguardFile('proguard-android-optimize.txt'), 'proguard-rules.pro'
        }

        // 正式版
        release {
            buildConfigField fieldType, fieldName, "\"${AppVersion.RELEASE}\""
            minifyEnabled false
            proguardFiles getDefaultProguardFile('proguard-android-optimize.txt'), 'proguard-rules.pro'
        }
    }

    useLibrary 'org.apache.http.legacy'
}

kapt {
    arguments {
        arg("AROUTER_MODULE_NAME", project.getName())
    }
}