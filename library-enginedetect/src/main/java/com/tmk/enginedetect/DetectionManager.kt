package com.tmk.enginedetect

import android.content.Context
import com.tmk.enginedetect.checker.CheckItemType
import com.tmk.enginedetect.checker.HardwareChecker
import com.tmk.enginedetect.checker.SipLibChecker
import com.tmk.enginedetect.checker.SpeechLibChecker
import com.tmk.enginedetect.checker.StandardNetChecker
import com.tmk.enginedetect.resultbean.EngineDetectionNodeResult
import com.tmk.enginedetect.resultbean.HardwareInfoResult
import com.tmk.enginedetect.resultbean.NetworkInfoResult

/**
 * 检测工具的控制类
 */
class DetectionManager(private val context: Context) {

    private lateinit var hardwareChecker: HardwareChecker   // 硬件检测er
    private lateinit var standardNetChecker: StandardNetChecker // 通用网络检测er
    private lateinit var speechLibChecker: SpeechLibChecker // 语音库检测er
    private lateinit var sipLibChecker: SipLibChecker // 暂未开发Sip库检测的功能
    private var checkParams: DetectionParams = DetectionParams()  // 检测参数
    val version: String = "v1.0.1_20240625" // 库的版本号为1.0.0
    private var isInitialized: Boolean = false  // 初始化标志
    var onHardwareInfoUpdated: ((hardwareInfo: HardwareInfoResult) -> Unit)? = null  // 硬件信息结果更新回调
        set(value) {
            field = value
            if (this::hardwareChecker.isInitialized) {
                hardwareChecker.onHardwareInfoUpdated = value
            }
        }
    var onNetworkInfoUpdated: ((info: NetworkInfoResult) -> Unit)? = null  // 通用网络检测结果更新回调
        set(value) {
            field = value
            if (this::standardNetChecker.isInitialized) {
                standardNetChecker.onNetworkInfoUpdated = value
            }
        }
    var onEngineNodeUpdated: ((Map<CheckItemType, EngineDetectionNodeResult>) -> Unit)? =
        null  // 引擎节点更新回调
        set(value) {
            field = value
            if (this::speechLibChecker.isInitialized) {
                speechLibChecker.onCheckNodeUpdate = value
            }
        }

    var onFinish: (() -> Unit)? = null  // 监测完成回调
    var onError: ((err: DetectionError) -> Unit)? = null  // 检测途中的错误回调

    /**
     * 初始化检测SDK
     */
    fun init() {
        if (isInitialized) {
            DetectionLog.e("DetectionManager has been initialized")
            return
        }
        hardwareChecker = HardwareChecker(context)
        standardNetChecker = StandardNetChecker(context)
        speechLibChecker = SpeechLibChecker(context)
        sipLibChecker = SipLibChecker(context)
        isInitialized = true

        hardwareChecker.onFinished = { standardNetChecker.startCheck() }
        standardNetChecker.onFinished = { speechLibChecker.startCheck() }
        speechLibChecker.onFinished = { onFinish?.invoke() }

        hardwareChecker.onError = { onError?.invoke(it) }
        standardNetChecker.onError = { onError?.invoke(it) }
        speechLibChecker.onError = { onError?.invoke(it) }

        DetectionLog.d("DetectionManager initialized successfully")
    }

    /**
     * 开始检测
     */
    fun startCheck(params: DetectionParams) {
        if (!isInitialized) {
            DetectionLog.e("DetectionManager has not been initialized, please call init() first")
            onError?.invoke(DetectionError.SDKNotInit)
            return
        }
        if (isChecking()) {
            DetectionLog.e("DetectionManager is checking, please stop check first")
            onError?.invoke(DetectionError.StopCheckFirst)
            return
        }
        checkParams = params
        speechLibChecker.checkItemTypes = checkParams.checkItemTypes
        speechLibChecker.engineHost = checkParams.speechEngineHost
        speechLibChecker.speechTestLengthType = checkParams.speechTestLengthType
        standardNetChecker.checkNetSpeed = checkParams.needCheckNetSpeed
        hardwareChecker.startCheck()
    }

    /**
     * 停止检测
     */
    fun stopCheck() {
        // 停止检测逻辑
        hardwareChecker.stopCheck()
        standardNetChecker.stopCheck()
        speechLibChecker.stopCheck()
        sipLibChecker.stopCheck()
    }

    fun isChecking():Boolean{
        return hardwareChecker.isChecking || standardNetChecker.isChecking || speechLibChecker.isChecking || sipLibChecker.isChecking
    }

    /**
     * 提交检测报告
     */
    fun submitReport() {
        // 提交检测报告逻辑
    }

}
