package com.tmk.enginedetect

import co.timekettle.speech.EngineHost
import com.tmk.enginedetect.checker.CheckItemType
import com.tmk.enginedetect.checker.SpeechTestLengthType

/**
 * 检测参数类
 *
 * @property checkItemTypes 检测项类型列表
 * @property autoUploadReport 是否需要自动上传报告
 */
data class DetectionParams(
    val checkItemTypes: MutableList<CheckItemType> = mutableListOf(),  // 检测项类型列表
    var autoUploadReport: Boolean = false,  // 是否上传报告
    var needCheckNetSpeed: Boolean = true, // 是否检查网速，因为检测网速比较耗时，所以加了这个选项
    var speechEngineHost: EngineHost = EngineHost("*************"), // 语音引擎IP地址
    var speechTestLengthType : SpeechTestLengthType = SpeechTestLengthType.SHORT// 语音测试文本
)
