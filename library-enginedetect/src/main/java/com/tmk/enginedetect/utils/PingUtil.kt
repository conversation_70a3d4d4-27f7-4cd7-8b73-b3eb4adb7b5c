package com.tmk.enginedetect.utils

import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.withContext
import java.net.InetAddress

object PingUtil {
    suspend fun pingIpAddress(ip: String): PingResult {
        return withContext(Dispatchers.IO) {
            try {
                val inetAddress = InetAddress.getByName(ip)
                val startTime = System.currentTimeMillis()
                val isReachable = inetAddress.isReachable(5000) // 尝试在5秒内到达
                val endTime = System.currentTimeMillis()
                if (isReachable) {
                    val delay = endTime - startTime
                    PingResult(ip, delay, null)
                } else {
                    PingResult(ip, null, Exception("Cannot reach $ip"))
                }
            } catch (e: Exception) {
                PingResult(ip, null, e)
            }
        }
    }
}

/**
 * Ping 结果
 * @param ip IP地址
 * @param delay 延迟时间，单位毫秒
 * @param exception 异常
 */
data class PingResult(val ip: String, val delay: Long?, val exception: Exception?){
    fun printInfo(): String {
        return buildString {
            append("IP地址：$ip")
            if (delay != null) {
                append("，延迟：$delay ms")
            }
            if (exception != null) {
                append("，异常：${exception.message}")
            }
        }
    }
}


