package com.tmk.enginedetect.utils

import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.withContext
import java.net.InetAddress
import kotlin.system.measureTimeMillis

/**
 * DNS解析耗时工具类
 */
object DNSUtil {
    /**
     * 解析DNS地址
     * @param address DNS地址
     * @return DNSResult 解析结果
     */
    suspend fun resolveDNS(address: String): DNSResult {
        return withContext(Dispatchers.IO) {
            var ip: String? = null
            var exception: Exception? = null
            val delay = measureTimeMillis {
                try {
                    val inetAddress = InetAddress.getByName(address)
                    ip = inetAddress.hostAddress
                } catch (e: Exception) {
                    exception = e
                }
            }
            DNSResult(
                address = address,
                ip = ip ?: "",
                delay = if (exception == null) delay else null,
                exception = exception
            )
        }
    }
}

/**
 * DNS 解析结果
 * @param address DNS地址，也就是域名
 * @param ip   解析出来的IP地址
 * @param delay 延迟时间，单位毫秒
 * @param exception 异常
 */
data class DNSResult(val address: String, val ip:String, val delay: Long?, val exception: Exception?){
    fun printInfo(): String {
        return buildString {
            append("域名：$address")
//            append("，IP地址：$ip")
            if (delay != null) {
                append("，解析耗时：$delay ms")
            }
            if (exception != null) {
                append("，异常：${exception.message}")
            }
        }
    }
}


