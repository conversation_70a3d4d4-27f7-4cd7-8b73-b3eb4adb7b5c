package com.tmk.enginedetect.utils


/**
 * 上传下载工具
 */
import com.tmk.enginedetect.DetectionLog
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.isActive
import kotlinx.coroutines.withContext
import okhttp3.MediaType
import okhttp3.MediaType.Companion.toMediaTypeOrNull
import okhttp3.MultipartBody
import okhttp3.OkHttpClient
import okhttp3.Request
import okhttp3.RequestBody
import okio.BufferedSink
import java.io.BufferedInputStream
import java.io.ByteArrayInputStream
import java.io.IOException
import java.net.HttpURLConnection
import java.net.URL
import kotlin.system.measureTimeMillis

object UpDownloadUtil {

    // 测试下载速度的方法

    val TAG = "网速检测"

    suspend fun testDownloadSpeed(url: String, seconds: Int = 3): UpDownloadSpeedResult {
        return withContext(Dispatchers.IO) {
            var connection: HttpURLConnection? = null
            var inputStream: BufferedInputStream? = null
            var totalKBRead = 0L
            var maxSpeedKBps = 0f
            var averageSpeedKBps = 0f
            var exception: Exception? = null
            val startTime = System.currentTimeMillis()

            try {
                connection = URL(url).openConnection() as HttpURLConnection
                connection.connectTimeout = 10000
                connection.readTimeout = 10000
                connection.connect()

                if (connection.responseCode != HttpURLConnection.HTTP_OK) {
                    throw IOException("HTTP error code: ${connection.responseCode}")
                }

                inputStream = BufferedInputStream(connection.inputStream)
                val buffer = ByteArray(8 * 1024)
                var bytesRead: Int

                while (inputStream.read(buffer).also { bytesRead = it } != -1) {
                    if (!isActive) {
                        DetectionLog.d("下载速度测试协程被取消了",TAG)
                        throw IOException("Download cancelled")
                    }
                    totalKBRead += bytesRead / 1024
                    val currentTime = System.currentTimeMillis()
                    val elapsedTime = (currentTime - startTime) / 1000f

                    if (elapsedTime >= seconds) {
                        break
                    }

                    val currentSpeedKBps = totalKBRead / elapsedTime
                    if (currentSpeedKBps > maxSpeedKBps) {
                        maxSpeedKBps = currentSpeedKBps
                    }
                }

                val totalTime = (System.currentTimeMillis() - startTime) / 1000f
                averageSpeedKBps = totalKBRead / totalTime
            } catch (e: Exception) {
                exception = e
            } finally {
                inputStream?.close()
                connection?.disconnect()
            }

            UpDownloadSpeedResult(
                maxSpeedKBps,
                averageSpeedKBps,
                totalKBRead,
                (System.currentTimeMillis() - startTime) / 1000f,
                exception
            )
        }
    }


    /**
     * 上传网速测试方法
     * 如果上传文件时间超过了seconds，则直接返回结果，也就是，最多只允许上传seconds秒，避免网速过低，文件上传不完的问题
     * 使用 multipart/form-data 请求类型
     * Http请求参数只需要填充一个字段，就是file，参数类型是file类型，
     * 方法的大致逻辑是上传100MB的字节数组到url，但是切记不要直接申请100MB的字节数组，也不要每次都创建一个新的数组，否则会内存溢出
     * 所有的字节数组定义为Long类型，避免文件过大时数据计算溢出
     */
    suspend fun testUploadSpeed(url: String, seconds: Float = 3f): UpDownloadSpeedResult {
        return withContext(Dispatchers.IO) {
            val client = OkHttpClient()
            val fileSize = 10 * 1024 * 1024 // 10MB
            val byteArray = ByteArray(fileSize) { 0 }
            // 从字节数组创建一个ByteArrayInputStream
            val byteArrayInputStream = ByteArrayInputStream(byteArray)
            var totalBytesUploaded = 0L
            val startTime = System.currentTimeMillis() // 起始时间
            var exception:Exception? = null
            // 创建一个自定义的RequestBody，从ByteArrayInputStream读取数据
            val requestBody = object : RequestBody() {
                override fun contentType(): MediaType? {
                    return "multipart/form-data".toMediaTypeOrNull()
                }
                override fun writeTo(sink: BufferedSink) {
                    val buffer = ByteArray(4 * 1024) // 4KB缓冲区
                    var bytesRead: Int
                    try {
                        while (byteArrayInputStream.read(buffer).also { bytesRead = it } != -1) {
                            if (!isActive) {
                                DetectionLog.d("上传速度测试协程被取消了", TAG)
                                throw IOException("Upload cancelled")
                            }
                            if (System.currentTimeMillis() - startTime > seconds * 1000) {
                                DetectionLog.d("$seconds 秒超时了，退出", TAG)
                                break
                            }
                            sink.write(buffer, 0, bytesRead)
                            totalBytesUploaded += bytesRead
                        }
                    } catch (e: IOException) {
                        exception = e
                    } finally {
                        byteArrayInputStream.close()
                    }
                }
                override fun contentLength(): Long {
                    return fileSize.toLong()
                }
            }
            // 创建multipart body部分
            val multipartBody = MultipartBody.Builder()
                .setType(MultipartBody.FORM)
                .addFormDataPart("file", null, requestBody)
                .build()
            // 创建请求
            val request = Request.Builder()
                .url(url)
                .post(multipartBody)
                .build()
            // 测量上传文件所需的时间
            val timeTaken = measureTimeMillis {
                try {
                    client.newCall(request).execute().use { response ->
                        if (!response.isSuccessful) throw IOException("Unexpected code $response")
                    }
                } catch (e: IOException) {
                    // 超时会抛出这个异常  java.net.ProtocolException: unexpected end of stream
                    // 因为服务器想要得到完整的数据，但是客户端提前关闭了连接，这个异常可以忽略
                    DetectionLog.d("IO 异常 $e", TAG)
                    if(e.javaClass != java.net.ProtocolException::class.java)  exception = e
                }
            }
            val averageSpeedKBps = (totalBytesUploaded / 1024f) / (timeTaken / 1000f)
            val maxSpeedKBps = averageSpeedKBps
            val totalTime = timeTaken/ 1000f
            UpDownloadSpeedResult(
                maxSpeedKBps = maxSpeedKBps,
                averageSpeedKBps = averageSpeedKBps,
                totalKB = totalBytesUploaded / 1024,
                totalTime = totalTime,
                exception = exception
            )
        }
    }

}

data class UpDownloadSpeedResult(
    val maxSpeedKBps: Float, // 最大速度（KB/秒）
    val averageSpeedKBps: Float, // 平均速度（KB/秒）
    val totalKB: Long, // 总传输KB数
    val totalTime: Float, // 总传输时间（秒）
    val exception: Exception? = null // 可能发生的异常
) {
    fun printInfo(): String {
        return buildString {
            append("平均速度：$averageSpeedKBps KB/秒")
            append("，最大速度：$maxSpeedKBps KB/秒")
            append("，共传输：$totalKB KB")
            append("，总耗时：$totalTime 秒")
            if (exception != null) {
                append("，异常：${exception.message}")
            }
        }
    }

}