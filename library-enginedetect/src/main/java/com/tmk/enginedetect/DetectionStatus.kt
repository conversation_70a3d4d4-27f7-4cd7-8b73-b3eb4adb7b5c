package com.tmk.enginedetect

// 定义一个密封类 DetectionStatus，它包含所有可能的检测状态
sealed class DetectionStatus(val curStage: Int = 0, val totalStage: Int = 0) {


    // 正在检测状态，使用 class 表示可以携带参数
    class Detecting(curStage: Int = 0, totalStage: Int = 0) : DetectionStatus(curStage, totalStage)

    // 未检测状态，使用 object 表示单例
    object None : DetectionStatus()

    // 检测停止状态，使用 object 表示单例
    object Stopped : DetectionStatus()

    // 检测异常状态，使用 object 表示单例
    object Error : DetectionStatus()

    // 检测完成状态，使用 object 表示单例
    object Finished : DetectionStatus()

    // 打印当前状态的信息
    fun printInfo(): String {
        var preFix = "检测状态："
        if(this is Detecting && totalStage > 0) {
            preFix += "${this::class.simpleName}($curStage/$totalStage)"
        }else{
            preFix += this::class.simpleName
        }

        return preFix
    }
}
