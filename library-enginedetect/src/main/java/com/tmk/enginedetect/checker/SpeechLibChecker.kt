package com.tmk.enginedetect.checker

import android.content.Context
import android.text.TextUtils
import android.util.Log
import co.timekettle.example.AudioFileHandler
import co.timekettle.speech.EngineHost
import co.timekettle.tmkengine.JsonRequest
import co.timekettle.tmkengine.NetSessionContext
import co.timekettle.tmkengine.NetSessionContext.ContextListener
import co.timekettle.tmkengine.TmkSpeechClient
import co.timekettle.tmkengine.utils.TmkLogger
import com.tmk.enginedetect.DetectionError
import com.tmk.enginedetect.DetectionLog
import com.tmk.enginedetect.DetectionStatus
import com.tmk.enginedetect.resultbean.EngineDetectionNodeResult
import kotlinx.coroutines.CompletableDeferred
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.cancel
import kotlinx.coroutines.delay
import kotlinx.coroutines.launch
import java.io.ByteArrayOutputStream
import java.io.IOException
import java.util.concurrent.ConcurrentHashMap


/**
 * 语音库检测类
 * ************* 连接消耗时间时间: 212
 * ************** 连接消耗时间时间: 338
 * ************* 连接消耗时间时间: 191
 * ************** 连接消耗时间时间: 252
 * 13.125.141.247 连接消耗时间时间: 102
 * 18.132.135.24 连接消耗时间时间: 209
 * 3.91.126.180 连接消耗时间时间: 232
 * 18.118.182.109 连接消耗时间时间: 221
 * 52.9.224.190 连接消耗时间时间: 171
 * 13.238.225.95 连接消耗时间时间: 241
 * ************* 连接消耗时间时间: 29
 * 54.251.242.40 连接消耗时间时间: 67
 * 15.207.172.118 连接消耗时间时间: 233
 * 15.185.51.8 连接消耗时间时间: 266
 *
 */
class SpeechLibChecker(val context: Context) : AbstractChecker(context) {

    // 检测项列表
    var checkItemTypes: MutableList<CheckItemType> = mutableListOf()
    var speechTestLengthType: SpeechTestLengthType = SpeechTestLengthType.SHORT
    private var scope: CoroutineScope? = null

    // 检测节点更新回调（如果产品需要打印子节点信息，那么设置这个回调，这里面的是每个子任务时间戳）
    var onCheckNodeUpdate: ((Map<CheckItemType, EngineDetectionNodeResult>) -> Unit)? = null
    var engineHost = EngineHost("*************")  // 这里由CheckParams设置

    // 私有的检测项
    private var mapCheckItems = hashMapOf<CheckItemType, EngineDetectionNodeResult>()

    private var curCheckItem: CheckItemType? = null               // 当前正在进行的阶段
    private var srcCode: String = "en-US"
    private var dstCode: String = "zh-CN"

    private var recognitionComplete: CompletableDeferred<DetectionError?>? = null
    private var translationComplete: CompletableDeferred<DetectionError?>? = null
    private var ttsComplete: CompletableDeferred<DetectionError?>? = null
    private var speechTransComplete: CompletableDeferred<DetectionError?>? = null

    var netSessionContexts: ConcurrentHashMap<Long, NetSessionContext> = ConcurrentHashMap()
    lateinit var WorkDir: String

    companion object {
        const val TAG = "SpeechLibChecker"
    }

    init {
        initSpeechEngine()
    }

    override fun startCheck() {
        DetectionLog.d("开始检测", TAG)
        if (checkItemTypes.isEmpty()) {
            DetectionLog.d("未传检测项，返回", TAG)
            onError?.invoke(DetectionError.NoCheckItems)
            return
        }

        if (isChecking) return
        // 创建协程作用域并启动协程
        mapCheckItems.clear()
        checkItemTypes.forEach { mapCheckItems[it] = EngineDetectionNodeResult() }
        scope?.cancel()
        scope = CoroutineScope(Dispatchers.IO).apply {
            launch {
                isChecking = true
                if (checkItemTypes.contains(CheckItemType.SingleRecognize)) {
                    doSingleRecognizeTest()
                }
                if (checkItemTypes.contains(CheckItemType.SingleTranslate)) {
                    doSingleTranslateTest()
                }
                if (checkItemTypes.contains(CheckItemType.SingleTTS)) {
                    doSingleTTSTest()
                }
                if (checkItemTypes.contains(CheckItemType.SpeechTranslation)) {
                    doSpeechTranslationTest()
                }
                isChecking = false
                onFinished?.invoke()
            }
        }
    }

    override fun stopCheck() {
        DetectionLog.d("停止检测", TAG)
        if(!isChecking) {
            DetectionLog.d("还未开始检测", TAG)
            return
        }
        scope?.cancel()
        scope = null
        isChecking = false
        mapCheckItems[curCheckItem]?.detectionStatus = DetectionStatus.Stopped
        notifyResults()
    }


    // 初始化Speech引擎
    private fun initSpeechEngine() {
        TmkSpeechClient.shareInstance().createUtility(context, "9A5C1E41066458D50F91636A111FED89")
        TmkSpeechClient.shareInstance().setSpecificHost(engineHost)
        TmkLogger.setLogCallback { level: Int, tag: String?, msg: String ->
            Log.d(tag, msg)
        }
        TmkLogger.enableFileLogger(context, true)
        WorkDir = context.externalCacheDir?.absolutePath.toString()
    }


    // 单识别检测
    private suspend fun doSingleRecognizeTest() {
        DetectionLog.d("开始进行单识别检测", TAG)
        curCheckItem = CheckItemType.SingleRecognize
        mapCheckItems[curCheckItem]!!.detectionStatus = DetectionStatus.Detecting()
        mapCheckItems[curCheckItem]!!.note = "测试音频：$speechTestLengthType"
        notifyResults()
        recognitionComplete = CompletableDeferred()
        doTask(JsonRequest.Type.Recognize, "")
        val result =  recognitionComplete?.await()   // 有错误result才会有值
        DetectionLog.d("单识别检测结束", TAG)
        if(result == null){
            mapCheckItems[curCheckItem]!!.detectionStatus = DetectionStatus.Finished
            notifyResults()
        }
    }


    // 单翻译检测
    private suspend fun doSingleTranslateTest() {
        DetectionLog.d("单翻译检测-->开始", TAG)
        curCheckItem = CheckItemType.SingleTranslate
        mapCheckItems[curCheckItem]!!.detectionStatus = DetectionStatus.Detecting()
        mapCheckItems[curCheckItem]!!.note = "翻译文本：$speechTestLengthType"
        notifyResults()
        translationComplete = CompletableDeferred()
        doTask(JsonRequest.Type.Translate, speechTestLengthType.content)
        val result =   translationComplete?.await()
        DetectionLog.d("单翻译检测-->结束", TAG)
        if(result == null) {
            mapCheckItems[curCheckItem]!!.detectionStatus = DetectionStatus.Finished
            notifyResults()
        }
    }

    // 单合成检测
    private suspend fun doSingleTTSTest() {
        DetectionLog.d("开始进行单合成检测", TAG)
        curCheckItem = CheckItemType.SingleTTS
        mapCheckItems[curCheckItem]!!.detectionStatus = DetectionStatus.Detecting()
        mapCheckItems[curCheckItem]!!.note = "合成文本：$speechTestLengthType"
        notifyResults()
        ttsComplete = CompletableDeferred()
        doTask(JsonRequest.Type.Synthesize, speechTestLengthType.content)
        val result =   ttsComplete?.await()
        DetectionLog.d("单合成检测结束", TAG)
        if(result == null) {
            mapCheckItems[curCheckItem]!!.detectionStatus = DetectionStatus.Finished
            notifyResults()
        }
    }

    // 语音翻译检测
    private suspend fun doSpeechTranslationTest() {
        DetectionLog.d("开始进行语音翻译检测", TAG)
        curCheckItem = CheckItemType.SpeechTranslation
        mapCheckItems[curCheckItem]!!.detectionStatus = DetectionStatus.Detecting()
        mapCheckItems[curCheckItem]!!.note = "测试音频：$speechTestLengthType"

        notifyResults()
        speechTransComplete = CompletableDeferred()
        doTask(JsonRequest.Type.SpeechTranslation, "")
        val result =    speechTransComplete?.await()
        DetectionLog.d("语音翻译检测结束", TAG)
        if(result == null) {
            mapCheckItems[curCheckItem]!!.detectionStatus = DetectionStatus.Finished
            notifyResults()
        }
    }


    private fun clearSelf() {
        scope?.cancel()
        scope = null
        mapCheckItems.clear()
        isChecking = false
    }

    private suspend fun doTask(
        taskType: JsonRequest.Type,
        text: String?,
        hostStr: String = this.engineHost.ip + ":" + this.engineHost.port,
        enableOpus: Boolean = false,
        engine: String? = null,
    ) {
        if (hostStr.isNotEmpty()) {
            val valid =
                hostStr.matches("\\d{1,3}\\.\\d{1,3}\\.\\d{1,3}\\.\\d{1,3}:\\d{1,5}".toRegex())
            if (!valid) {
                DetectionLog.e("输入指定引擎格式错误, 请检查格式, 如: ************:5050", TAG)
                onError?.invoke(DetectionError.InvalidHostFormat)
                return
            }
            val ipAndPost = hostStr.split(":".toRegex()).dropLastWhile { it.isEmpty() }
                .toTypedArray()
            val host = EngineHost(ipAndPost[0], ipAndPost[1].toInt())
            TmkSpeechClient.shareInstance().setSpecificHost(host)
        } else {
            TmkSpeechClient.shareInstance().setSpecificHost(null)
        }
        TmkSpeechClient.shareInstance()
            .setSpecificEngine(if (!TextUtils.isEmpty(engine)) engine else null)
        val isAsr = taskType.isAsr
        if (isAsr) {
            val context = if (taskType == JsonRequest.Type.Recognize) {
                TmkSpeechClient.shareInstance()
                    .createRecognizer(srcCode, enableOpus, getListener(taskType))
            } else {
                TmkSpeechClient.shareInstance().createSpeechTranslation(
                    srcCode,
                    dstCode,
                    taskType,
                    enableOpus,
                    getListener(taskType)
                )
            }
            netSessionContexts[context.session] = context
            val path = "audio_samples/${speechTestLengthType.fileName}"
            val data: ByteArray = assets2ByteArray(path)
            context.start()
            run {
                val nLen = 640
                val frame = ByteArray(nLen)
                val sentCount = data.size / nLen
                var sentTime = 0.0
                DetectionLog.d("将发送文件数据: $path", TAG)
                updateTSendFirstPkg()
                for (i in 0 until sentCount) {
                    if (context.isInvalid) {
                        DetectionLog.d(
                            "网络请求已失效, 停止发送数据", TAG
                        )
                        break
                    }
                    System.arraycopy(data, (i * 640), frame, 0, 640)
                    context.writeAudio(frame)
                    delay(20)
                    sentTime = i * nLen / 2.0 / 16000.0
//                    if (sentTime % 1 == 0.0) DetectionLog.d(
//                        "已发送数据: " + sentTime + "s", TAG
//                    )
                    if (sentTime > 10) break
                }
                DetectionLog.d("发送结束, 已发送数据: " + sentTime + "s", TAG)
                updateTSendDone()
            }
            context.stop()
        } else if (taskType == JsonRequest.Type.Translate) {
            val context = TmkSpeechClient.shareInstance()
                .createTranslator(srcCode, dstCode, text, getListener(taskType))
            netSessionContexts[context.session] = context
            context.start()
        } else if (taskType == JsonRequest.Type.Synthesize) {
            val context = TmkSpeechClient.shareInstance()
                .createSynthesizer(srcCode, text, getListener(taskType))
            netSessionContexts[context.session] = context
            context.start()
        } else {
            DetectionLog.e("任务类型错误!!!", TAG)
        }
    }


    private fun assets2ByteArray(fileName: String): ByteArray {
        context.assets.open(fileName).use { inputStream ->
            ByteArrayOutputStream().use { byteArrayOutputStream ->
                val buffer = ByteArray(1024)
                var length: Int
                while (inputStream.read(buffer).also { length = it } != -1) {
                    byteArrayOutputStream.write(buffer, 0, length)
                }
                return byteArrayOutputStream.toByteArray()
            }
        }
    }

    private fun getListener(taskType: JsonRequest.Type): ContextListener {
        var audioWriter: AudioFileHandler? = null
        if (taskType == JsonRequest.Type.Synthesize) {
            try {
                audioWriter =
                    AudioFileHandler("synthesize", "$WorkDir/audio/")
            } catch (e: IOException) {
                e.printStackTrace()
            }
        }
        val finalAudioWriter = audioWriter
        val contextListener: ContextListener = object : ContextListener {
            override fun onRecognizeResult(
                context: NetSessionContext,
                session: Long,
                isLast: Boolean,
                srcCode: String?,
                rtext: String?,
                ttext: String?,
                engine: String
            ) {
                DetectionLog.d("onRecognizeResult $session  $engine $isLast | $srcCode | $rtext | $ttext", TAG)
                updateTReceiveFirstResp()
            }

            override fun onTranslateResult(
                context: NetSessionContext,
                session: Long,
                result: String,
                engine: String
            ) {
                DetectionLog.d("onTranslateResult $session engine $engine  $result", TAG)
                if (curCheckItem == CheckItemType.SingleTranslate) {
                    updateTReceiveFirstResp()
                    updateTAllRespReceived()
                    translationComplete?.complete(null)
                }

            }

            override fun onSynthesizeBuffer(
                context: NetSessionContext,
                session: Long,
                output: ByteArray,
                outputSize: Int
            ) {
                finalAudioWriter?.writeAndFlush(output)
                if (curCheckItem == CheckItemType.SingleTTS) {
                    updateTReceiveFirstResp()
                }
            }

            override fun onCompleted(context: NetSessionContext, session: Long, engine: String) {
                DetectionLog.d("全部响应完成", TAG)
                updateTAllRespReceived()
                finalAudioWriter?.close()
//                if (curCheckItem == CheckItemType.SingleRecognize)
                recognitionComplete?.complete(null)
//                if (curCheckItem == CheckItemType.SingleTranslate)
                translationComplete?.complete(null)
//                if (curCheckItem == CheckItemType.SingleTTS)
                ttsComplete?.complete(null)
//                if (curCheckItem == CheckItemType.SpeechTranslation)
                speechTransComplete?.complete(null)
            }

            override fun onError(
                context: NetSessionContext,
                session: Long,
                engine: String?,
                code: Int,
                message: String
            ) {
                DetectionLog.d("错误onError $session engine $engine  $code $message", TAG)
                val error = DetectionError.SpeechLibError.apply {
                    this.message = message
                }
                onError?.invoke(error)
                mapCheckItems[curCheckItem]?.detectionStatus = DetectionStatus.Error
                notifyResults()
                recognitionComplete?.complete(error)
                translationComplete?.complete(error)
                ttsComplete?.complete(error)
                speechTransComplete?.complete(error)
            }

            override fun onWillConnectServer(
                context: NetSessionContext?,
                session: Long,
                engine: String?
            ) {
                DetectionLog.d("准备连接服务器 $session engine $engine", TAG)
                mapCheckItems[curCheckItem]?.engineIpAddress = engine.toString()
                updateTWillConnServer()
            }

            override fun onDidConnectServer(
                context: NetSessionContext?,
                session: Long,
                engine: String?
            ) {
                DetectionLog.d("已连接服务器 $session engine $engine", TAG)
                updateTDidConnServer()
            }


            override fun onSendRequest(
                context: NetSessionContext?,
                session: Long,
                engine: String?
            ) {
                DetectionLog.d("将发送识别/翻译/合成请求 $session engine $engine", TAG)
                updateTSendRequest()
            }

            override fun onRequestReceiveResp(
                context: NetSessionContext?,
                session: Long,
                engine: String?
            ) {
                DetectionLog.d("服务已准备就绪 $session engine $engine", TAG)
                updateTRequestReceiveResp()
            }


        }
        return contextListener
    }


    private fun notifyResults() {
        onCheckNodeUpdate?.invoke(mapCheckItems)
    }


    // step1
    private fun updateTWillConnServer() {
        if (mapCheckItems[curCheckItem]!!.tWillConnectServer != null) return
        mapCheckItems[curCheckItem]!!.tWillConnectServer = System.currentTimeMillis()
        notifyResults()
    }

    // step2
    private fun updateTDidConnServer() {
        if (mapCheckItems[curCheckItem]!!.tDidConnectServer != null) return
        mapCheckItems[curCheckItem]!!.tDidConnectServer = System.currentTimeMillis()
        notifyResults()
    }

    // step3
    private fun updateTSendRequest() {
        if (mapCheckItems[curCheckItem]!!.tSendRequest != null) return
        mapCheckItems[curCheckItem]!!.tSendRequest = System.currentTimeMillis()
        notifyResults()
    }


    // step4
    private fun updateTRequestReceiveResp() {
        if (mapCheckItems[curCheckItem]!!.tReceiveReqResp != null) return
        mapCheckItems[curCheckItem]!!.tReceiveReqResp = System.currentTimeMillis()
        notifyResults()
    }


    private fun updateTSendFirstPkg() {
        if (mapCheckItems[curCheckItem]!!.tSendFirstPkg != null) return
        mapCheckItems[curCheckItem]!!.tSendFirstPkg = System.currentTimeMillis()
        notifyResults()
    }

    private fun updateTSendDone() {
        mapCheckItems[curCheckItem]!!.tSendDone = System.currentTimeMillis()
        notifyResults()
    }

    // 收到第一个数据包
    private fun updateTReceiveFirstResp() {
        if (mapCheckItems[curCheckItem]!!.tReceiveFirstResp != null) return
        mapCheckItems[curCheckItem]!!.tReceiveFirstResp = System.currentTimeMillis()
        notifyResults()
    }

    private fun updateTAllRespReceived() {
        if (mapCheckItems[curCheckItem]!!.tAllRespReceived == null) {
            mapCheckItems[curCheckItem]!!.tAllRespReceived = System.currentTimeMillis()
            notifyResults()
        }
    }

}