package com.tmk.enginedetect.checker

import android.Manifest
import android.content.Context
import android.content.pm.PackageManager
import android.net.ConnectivityManager
import android.net.NetworkCapabilities
import android.os.Build
import android.telephony.TelephonyManager
import androidx.core.content.ContextCompat
import com.tmk.enginedetect.DetectionLog
import com.tmk.enginedetect.DetectionStatus
import com.tmk.enginedetect.resultbean.HardwareInfoResult
import java.text.SimpleDateFormat
import java.util.Calendar
import java.util.Locale
import java.util.TimeZone

class HardwareChecker(val context: Context) : AbstractChecker(context) {

    // 硬件信息更新回调
    var onHardwareInfoUpdated: ((HardwareInfoResult) -> Unit)? = null

    // 硬件信息
    private var hardwareInfo: HardwareInfoResult? = null

    override fun startCheck() {
        DetectionLog.d("开始硬件检测...")
        isChecking = true
        val hardwareInfoResult = HardwareInfoResult()
        hardwareInfoResult.detectionStatus = DetectionStatus.Detecting() // 刷新状态
        onHardwareInfoUpdated?.invoke(hardwareInfoResult)
        hardwareInfoResult.apply {
            deviceModel = Build.MODEL
            deviceBrand = Build.BRAND
            manufacture = Build.MANUFACTURER
            systemVersion = getVersionInfo()
            carrier = getCarrier(context)
            timeZone = getCurrentTimeZone()
            localTime = getCurrentLocalTime()
        }
        DetectionLog.d("硬件检测完毕...$hardwareInfoResult")
        hardwareInfoResult.detectionStatus = DetectionStatus.Finished  // 刷新状态
        onHardwareInfoUpdated?.invoke(hardwareInfoResult)
        isChecking = false.also { onFinished?.invoke() }  // 告诉Manager检测完成了
    }


    private fun getVersionInfo(): String {
        val versionRelease = Build.VERSION.RELEASE
        val sdkInt = Build.VERSION.SDK_INT
        return "Android $versionRelease, API $sdkInt"
    }


    // 获取运营商名称
    private fun getCarrier(context: Context): String {
        // 检查权限
        val hasPermission = ContextCompat.checkSelfPermission(
            context,
            Manifest.permission.READ_PHONE_STATE
        ) == PackageManager.PERMISSION_GRANTED
        if (!hasPermission) {
            return "No Permission"
        }
        val telephonyManager =
            context.getSystemService(Context.TELEPHONY_SERVICE) as TelephonyManager
        // 检查 SIM 卡状态
        if (telephonyManager.simState == TelephonyManager.SIM_STATE_ABSENT) {
            return "Unknown"
        }
        return telephonyManager.networkOperatorName
    }

    // 获取时区
    private fun getCurrentTimeZone(): String {
        return TimeZone.getDefault().id
    }

    // 获取本地时间
    fun getCurrentLocalTime(): String {
        val calendar = Calendar.getInstance()
        val dateFormat = SimpleDateFormat("yyyy-MM-dd HH:mm:ss", Locale.getDefault())
        return dateFormat.format(calendar.time)
    }

    override fun stopCheck() {
    }
}