package com.tmk.enginedetect.checker

import android.content.Context
import co.timekettle.speech.utils.HttpsConnection
import com.blankj.utilcode.util.GsonUtils
import com.tmk.enginedetect.DetectionLog
import com.tmk.enginedetect.DetectionStatus
import com.tmk.enginedetect.resultbean.NetworkInfoResult
import com.tmk.enginedetect.utils.DNSUtil
import com.tmk.enginedetect.utils.DNetWorkUtil
import com.tmk.enginedetect.utils.PingUtil.pingIpAddress
import com.tmk.enginedetect.utils.UpDownloadUtil
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.cancel
import kotlinx.coroutines.coroutineScope
import kotlinx.coroutines.delay
import kotlinx.coroutines.isActive
import kotlinx.coroutines.launch

/**
 * 标准网络检测类
 */
class StandardNetChecker(val context: Context) : AbstractChecker(context) {
    // 网络信息更新回调
    var onNetworkInfoUpdated: ((NetworkInfoResult) -> Unit)? = null
    private var scope: CoroutineScope? = null

    // 通用网络信息
    private var mNetworkInfo: NetworkInfoResult = NetworkInfoResult()
    private var curStage: Int = 1  // 当前处于哪个阶段
    var checkNetSpeed = true  // 是否检测上传 / 下载网速
    private var TOTAL_STAGE = 6  // 总阶段数

    private fun launchTestJob() {
        // 如果scope已经存在，直接返回
        scope?.cancel().also { scope = null }
        TOTAL_STAGE = if (checkNetSpeed) 6 else 4
        // 创建协程作用域并启动协程
        scope = CoroutineScope(Dispatchers.IO)
        scope?.launch {
            doGetBasicInfo()
            doGetIpInfo()
            doPingTest()
            doDnsTest()
            if (checkNetSpeed) {
                doDownloadSpeedTest()
                doUploadSpeedTest()
            }
            mNetworkInfo.detectionStatus = DetectionStatus.Finished
            notifyResults()
            onFinished?.invoke()
            clearSelf()
        }
    }

    private suspend fun doGetIpInfo() {
        mNetworkInfo.detectionStatus =
            DetectionStatus.Detecting(curStage++, TOTAL_STAGE)
        notifyResults()
        getWordTimeIpResult()?.let {
            DetectionLog.d("获取到了IP信息 $it", TAG)
            mNetworkInfo.ipAddress = it.ip.toString()
            notifyResults()
        }
    }


    private suspend fun doDownloadSpeedTest() = coroutineScope {
        mNetworkInfo.detectionStatus = DetectionStatus.Detecting(curStage++, TOTAL_STAGE)
        notifyResults()
        DetectionLog.d("进行下载网速测试", TAG)
        // https://www.thinkbroadband.com/download
        val downloadUrl = "http://vipspeedtest8.wuhan.net.cn:8080/download?size=1073741824"
//         val downloadUrl = "http://proof.ovh.net/files/100Mb.dat"
//        val downloadUrl = "https://cdn.timekettle.co/test/100Mb.dat"
//        val downloadUrl = "http://*************/licoba/myfile/-/raw/main/10Mb.dat"
//        val downloadUrl = "http://speed.cloudflare.com/__down?bytes=104857600"
        val downloadResult = UpDownloadUtil.testDownloadSpeed(downloadUrl)
        DetectionLog.d("下载测试结果：$downloadResult", TAG)
        mNetworkInfo.downloadSpeed = downloadResult
        notifyResults()
    }

    // 进行网速测试
    private suspend fun doUploadSpeedTest() = coroutineScope {
        mNetworkInfo.detectionStatus =
            DetectionStatus.Detecting(curStage++, TOTAL_STAGE)
        notifyResults()
        DetectionLog.d("进行上传网速测试", TAG)
//        val uploadUrl = "https://file.io/upload"
        val uploadUrl = "https://httpbin.org/post"
//        val uploadUrl = "https://store1.gofile.io/contents/uploadfile"
        val uploadResult = UpDownloadUtil.testUploadSpeed(uploadUrl)
        DetectionLog.d("上传测试结果：$uploadResult", TAG)
        mNetworkInfo.uploadSpeed = uploadResult

        notifyResults()
    }


    // 获取网络基础信息
    private fun doGetBasicInfo() {
        mNetworkInfo.detectionStatus = DetectionStatus.Detecting(curStage++, TOTAL_STAGE)
        mNetworkInfo.networkType = DNetWorkUtil.getNetworkType(context)
        notifyResults()
        mNetworkInfo.isNetworkConnected = DNetWorkUtil.isNetworkConnected(context)
        notifyResults()

    }


    private suspend fun doPingTest() = coroutineScope {
        mNetworkInfo.detectionStatus = DetectionStatus.Detecting(curStage++, TOTAL_STAGE)
        notifyResults()
        DetectionLog.d("开始进行Ping测试", TAG)
        val ips = listOf(
            "baidu.com",
            "qq.com",
            "timekettle.co",
            "niutrans.com",
            "************",
            "**************",
            "**************"
        )
        ips.map { ip ->
            repeat(1) {
                if (!isActive) return@coroutineScope // 检查协程是否已取消
                val result = pingIpAddress(ip)
                DetectionLog.d("Ping结果 $result", TAG)
                mNetworkInfo.pingResults.add(result)
                notifyResults()
            }
        }
    }

    // 进行Dns测试
    private suspend fun doDnsTest() = coroutineScope {
        mNetworkInfo.detectionStatus = DetectionStatus.Detecting(curStage++, TOTAL_STAGE)
        notifyResults()
        val addressList = listOf(
            "www.baidu.com",
            "www.qq.com",
            "www.timekettle.co",
            "www.niutrans.com",
            "www.google.com",
        )
        addressList.map { address ->
            repeat(1) {
                if (!isActive) return@coroutineScope // 检查协程是否已取消
                val result = DNSUtil.resolveDNS(address)
                DetectionLog.d("DNS解析结果 $result", TAG)
                mNetworkInfo.dnsResults.add(result)
                notifyResults()
            }
        }
    }


    private fun notifyResults() {
        onNetworkInfoUpdated?.invoke(mNetworkInfo)
    }


    override fun startCheck() {
        DetectionLog.d("开始进行标准网络测试", TAG)
        if (isChecking) return
        isChecking = true
        mNetworkInfo = NetworkInfoResult()
        mNetworkInfo.detectionStatus = DetectionStatus.Detecting()
        launchTestJob()
    }

    override fun stopCheck() {
        scope?.cancel()
        scope = null
        mNetworkInfo.detectionStatus = DetectionStatus.Stopped
        notifyResults()
        clearSelf()
    }


    // 私有的清理函数
    private fun clearSelf() {
        isChecking = false
        curStage = 1
    }

    private suspend fun getWordTimeIpResult(): TimeZoneBean? {
        DetectionLog.d("开始向worldtimeapi请求IP")
        val httpResult = HttpsConnection.get("http://ip234.in/ip.json") ?: return null
        val resString = String(httpResult, Charsets.UTF_8)
        DetectionLog.d("IP请求响应结果: $resString")
        return GsonUtils.fromJson(resString, TimeZoneBean::class.java)
    }

    companion object {
        const val TAG = "StandardNetChecker"
    }

    private data class TimeZoneBean(
//        var abbreviation: String? = null,
//        var client_ip: String? = null,
//        var datetime: String? = null,
//        var raw_offset: Int? = null,
//        var timezone: String? = null,
//        var unixtime: Int? = null,
//        var utc_datetime: String? = null,
//        var utc_offset: String? = null,
        var ip: String? = null,
        var timezone: String? = null,
        var country: String? = null,
        var city: String? = null,
        var organization: String? = null,
        var country_code: String? = null,
    )


}