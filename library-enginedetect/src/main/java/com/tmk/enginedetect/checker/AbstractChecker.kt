package com.tmk.enginedetect.checker

import android.content.Context
import com.tmk.enginedetect.DetectionError

// 抽象类 AbstractChecker
abstract class AbstractChecker(context: Context) {

    // 是否正在检测
    var isChecking: Boolean = false

    // 检测完成的闭包
    var onFinished: (() -> Unit)? = null

    // 检测错误的闭包
    var onError: ((DetectionError) -> Unit)? = null

    // 开始检测
    abstract fun startCheck()

    // 停止检测
    abstract fun stopCheck()

}


/**
 * 单次检测类型枚举
 */

enum class CheckItemType(val checkName: String) {
    SingleRecognize("单识别"),   // 单识别
    SingleTranslate("单翻译"),   // 单翻译
    SingleTTS("单TTS合成"),         // 单TTS合成
    SpeechTranslation("语音翻译")  // 语音翻译
}


enum class SpeechTestLengthType(val title: String, val content: String, val fileName: String) {
    SHORT_SHORT("超短句", "Hello", "超短句.pcm"),
    SHORT("短句", "Hello, Can you hear me?", "短句.pcm"),
    NORMAL(
        "中句",
        "Hello, Can you hear me? Nice to meet you! I am a software engineer.",
        "中句.pcm"
    ),
    LONG(
        "长句",
        "Hello, Can you hear me? Nice to meet you! I am a software engineer, I am working on a project called NetworkDetector, it is a network detection tool.",
        "长句.pcm"
    ),
    LONG_LONG(
        "超长句",
        "Hello, Can you hear me? Nice to meet you! I am a software engineer, I am working on a project called NetworkDetector, it is a network detection tool. it can help you to detect the network status, such as the network speed, the network latency, the network packet loss rate, etc. It is very useful, you can try it!",
        "超长句.pcm"
    );

    override fun toString(): String {
        return "$title，内容：$content"
    }
}

