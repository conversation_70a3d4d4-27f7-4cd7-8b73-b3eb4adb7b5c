package com.tmk.enginedetect.resultbean

import com.tmk.enginedetect.DetectionStatus
import com.tmk.enginedetect.utils.DNSResult
import com.tmk.enginedetect.utils.PingResult
import com.tmk.enginedetect.utils.UpDownloadSpeedResult

/**
 * 数据类表示网络信息结果
 */
data class NetworkInfoResult(
    var detectionStatus: DetectionStatus = DetectionStatus.None,         // 检测状态
    var networkType: String = "",                                        // 网络类型（需要网络权限）
    var isNetworkConnected: Boolean? = null,                             // 网络类型（需要网络权限）
    var ipAddress: String? = null,                                       // IP 地址
    var pingResults: MutableList<PingResult> = mutableListOf(),          // ping 结果列表
    var dnsResults: MutableList<DNSResult> = mutableListOf(),            // DNS 解析时间，键为 DNS 地址，值为解析时间
    var downloadSpeed: UpDownloadSpeedResult? = null,                    // 下载速度
    var uploadSpeed: UpDownloadSpeedResult? = null,                      // 上传速度
) {
    fun printInfo(): String {
        return buildString {
            append("${detectionStatus.printInfo()}\n")
            if (networkType.isNotEmpty()) {
                append("网络类型：${networkType}\n")
            }
            if (isNetworkConnected != null) {
                append("连接状态：${if (isNetworkConnected == true) "已连接" else "未连接"}\n")
            }
            if (ipAddress == null && detectionStatus.curStage in listOf(2)) {
                append("正在查询IP地址，请稍等...\n")
            }
            if (ipAddress != null) {
                append("IP地址：${ipAddress}\n")
            }
            if (pingResults.isNotEmpty()) {
                append("Ping结果：\n")
                pingResults.forEach {
                    append("    ${it.printInfo()}\n")
                }
            }
            if (dnsResults.isNotEmpty()) {
                append("DNS解析结果：\n")
                dnsResults.forEach {
                    append("    ${it.printInfo()}\n")
                }
            }
            if(detectionStatus.curStage in listOf(4,5)){
                append("正在进行网速测试，请稍等....\n")
            }
            if (downloadSpeed != null) {
                append("下载测试结果：\n")
                append("    ${downloadSpeed?.printInfo()}\n")
            }
            if (uploadSpeed != null) {
                append("上传测试结果：\n")
                append("    ${uploadSpeed?.printInfo()}\n")
            }
        }
    }

    fun isCheckDone() = detectionStatus == DetectionStatus.Finished
}

