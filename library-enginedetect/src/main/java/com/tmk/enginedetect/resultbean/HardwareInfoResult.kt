package com.tmk.enginedetect.resultbean

import com.tmk.enginedetect.DetectionStatus

/**
 * 数据类表示硬件信息结果
 */
data class HardwareInfoResult(
    var detectionStatus: DetectionStatus = DetectionStatus.None, // 检测状态
    var deviceModel: String = "",              // 设备型号
    var deviceBrand: String = "",              // 设备品牌
    var manufacture: String = "",              // 制造商
    var systemVersion: String = "",            // 系统版本
    var carrier: String = "",                  // 运营商（需要设备状态权限）
    var timeZone: String = "",                 // 时区
    var localTime: String = "",                 // 当地时间
) {
    fun printInfo(): String {
        return buildString {
            append("${detectionStatus.printInfo()}\n")
            append("设备型号：${deviceModel}\n")
            append("设备品牌：${deviceBrand}\n")
            append("制造商：${manufacture}\n")
            append("系统版本：${systemVersion}\n")
            append("运营商：${carrier}\n")
            append("时区：${timeZone}\n")
            append("当地时间：${localTime}\n")
        }
    }

    fun isCheckDone() = detectionStatus == DetectionStatus.Finished
}
