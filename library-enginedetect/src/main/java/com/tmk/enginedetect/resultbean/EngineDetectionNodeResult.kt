package com.tmk.enginedetect.resultbean

import com.tmk.enginedetect.DetectionStatus
import java.text.SimpleDateFormat
import java.util.Date
import java.util.Locale

/**
 * 数据类表示引擎检测节点结果
 */
data class EngineDetectionNodeResult(
    var detectionStatus: DetectionStatus = DetectionStatus.None, // 检测状态
    var engineIpAddress: String = "",              // 引擎IP地址
    var tWillConnectServer: Long? = null,         // 客户端开始连接服务器
    var tDidConnectServer: Long? = null,          // 客户端连接服务器成功
    var tSendRequest: Long? = null,               // 客户端向服务器发起识别/翻译/TTS请求
    var tReceiveReqResp: Long? = null,            // 与服务器建立成功时间（缺）
    var tSendFirstPkg: Long? = null,              // 发送第一个包的时间
    var tSendDone: Long? = null,                  // 客户端发送完毕时间
    var tReceiveFirstResp: Long? = null,          // 客户端第一次收到响应时间
    var tAllRespReceived: Long? = null,           // 全部响应完成时间
    var note:String? = ""                         // 一些备注信息
) {
    fun printInfo(): String {
        val events = listOf(
            "准备连接服务器：　　" to timestampToDate(tWillConnectServer),
            "成功连接服务器：　　" to timestampToDate(tDidConnectServer),
            "请求服务：　　　　　" to timestampToDate(tSendRequest),
            "服务准备就绪：　　　" to timestampToDate(tReceiveReqResp),
            "发送第一包：　　　　" to timestampToDate(tSendFirstPkg),
            "数据包发送完毕：　　" to timestampToDate(tSendDone),
            "第一次收到数据：　　" to timestampToDate(tReceiveFirstResp),  // 这里回调是缺的
            "全部数据响应完成：　" to timestampToDate(tAllRespReceived)
        )
        return buildString {
            append("     引擎地址：$engineIpAddress，${detectionStatus.printInfo()}\n")
            append("     $note\n")
            for ((label, timestamp) in events) {
                append("     ->${label} $timestamp\n")
            }
        }
    }

    private fun timestampToDate(timestamp: Long?): String {
        if (timestamp == null) return "--"
        val date = Date(timestamp)
        val formatter = SimpleDateFormat("HH:mm:ss.SSS", Locale.getDefault())
        return formatter.format(date)
    }

    fun hasResult(): Boolean {
        return detectionStatus != DetectionStatus.None
    }

    fun isCheckDone(): Boolean {
        return detectionStatus == DetectionStatus.Finished
    }


    val costTimeFirstReceive: Long?  // 第一次收到数据耗时
        get() = if (tSendRequest != null && tReceiveFirstResp != null) {
            tReceiveFirstResp!! - tWillConnectServer!!
        } else {
            null
        }
    val costTimeTotal: Long?   // 总检测耗时，每个任务不一样。从开始检测 -> 检测完成所需要的时间，暂时保持预留
        get() = if (tWillConnectServer != null && tAllRespReceived != null) {
            tAllRespReceived!! - tWillConnectServer!!
        } else {
            null
        }

}
