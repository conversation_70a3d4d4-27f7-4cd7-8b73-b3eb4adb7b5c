package com.tmk.enginedetect

import android.util.Log

/**
 * 检测日志类
 */
object DetectionLog {
    private var logLevel = LogLevel.DEBUG
    private var isLogEnabled = true
    private const val TAG = "延迟检测"

    fun v(msg: String, tag: String = TAG) {
        if (isLogEnabled && logLevel <= LogLevel.VERBOSE) Log.v(tag, msg)
    }

    fun d(msg: String, tag: String = TAG) {
        if (isLogEnabled && logLevel <= LogLevel.DEBUG) Log.d(tag, msg)
    }

    fun i(msg: String, tag: String = TAG) {
        if (isLogEnabled && logLevel <= LogLevel.INFO) Log.i(tag, msg)
    }

    fun w(msg: String, tag: String = TAG) {
        if (isLogEnabled && logLevel <= LogLevel.WARN) Log.w(tag, msg)
    }

    fun e(msg: String, tag: String = TAG) {
        if (isLogEnabled && logLevel <= LogLevel.ERROR) Log.e(tag, msg)
    }

    enum class LogLevel {
        VERBOSE, DEBUG, INFO, WARN, ERROR, NONE
    }

}