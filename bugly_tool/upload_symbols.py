#!/usr/bin/env python
# -*- coding: UTF-8 -*-
from enum import Enum
import sys
import getopt
import os
import subprocess
import time


class Platform(Enum):
    Android = 'android'
    iOS = 'ios'
    All = 'ios+android'


platform = Platform.Android.value
version = 1.0
BASE_PATH = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
sysbols_path = os.path.join(BASE_PATH, 'library-speech/src/main/jniLibs/arm64-v8a/libspeech.so') # 默认为Android的so库路径

# 获取脚本参数


def get_opt(argv):
    global platform
    global version
    global sysbols_path
    try:
        opts, args = getopt.getopt(argv, "hv:p:l:", ["help", "version=", "platform="])
    except getopt.GetoptError:
        print('获取参数失败')
        sys.exit(2)
    for opt, arg in opts:
        # print("所有参数---------：", opt, arg)
        if opt in ('-h', '--help'):
            usage()
            sys.exit()
        elif opt in ('-v', '--version'):
            version = arg
            print("版本号：", arg)
        elif opt in ("-p", "--platform"):
            platform = arg
            print("平台：", arg)
        elif opt in ("-l", "--location"):
            sysbols_path = arg
            print("路径：", arg)

# 使用帮助


def usage():
    print('''
    
    ==========================
    |
    |    Bugly符号表上传工具   
    |    
    ==========================

    $ shell执行 `python3 upload_sysbols.py` -p android/ios/all

    😊 参数列表
    -h 查看使用帮助
    -v 查看脚本版本号
    -p 符号表上传平台 1.android(android平台) 2.ios(ios平台) 3.all(ios+android平台)
    ''')


def main(argv):
    os.chdir(os.path.join(BASE_PATH, sys.path[0]))  # 切换工作目录到当前目录
    get_opt(argv)
    do_upload()

# 上传bugly符号


def do_upload():
    usage()
    time.sleep(1)
    print(">>>>>>> 正在上传", platform, "平台符号表")
    print("项目路径:", BASE_PATH)
    platform_string = 'Android'
    upload_cmd = 'echo "没有执行任何操作" '

    if(platform == Platform.Android.value):
        platform_string = 'Android'
        upload_cmd = '''
            java -jar buglyqq-upload-symbol.jar \
                -appid 5e6de8d46f \
                -appkey ac9a0d6f-52c5-4e76-b43b-e682934e51f7 \
                -bundleid com.translation666 \
                -version '''+version+''' \
                -platform '''+platform_string+''' \
                -inputSymbol '''+sysbols_path+'''
            '''
    elif platform == Platform.iOS.value:
        platform_string = 'IOS'
        upload_cmd = '''
            java -jar buglyqq-upload-symbol.jar \
                -appid f565bbcfdd \
                -appkey 70c1d619-dcc8-4301-97b4-37526585b1dc \
                -bundleid co.timekettle.translation \
                -version '''+version+''' \
                -platform '''+platform_string+''' \
                -inputSymbol '''+sysbols_path+'''
            '''
    print(platform_string, " 符号文件路径: ", sysbols_path)
    print("上传命令: ", upload_cmd)    
    subprocess.run(upload_cmd, shell=True)
    print('''
    -----------------------------------------------------------------------
    |
    |  执行完毕！
    |  若在上方看到 retCode: 200 或 "msg":"success" 表示上传成功，否则失败
    |  
    -----------------------------------------------------------------------
    ''')


# start
if __name__ == "__main__":
    main(sys.argv[1:])
