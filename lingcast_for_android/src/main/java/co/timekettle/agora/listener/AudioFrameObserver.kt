package co.timekettle.agora.listener

import io.agora.rtc2.IAudioFrameObserver
import io.agora.rtc2.audio.AudioParams
import java.nio.ByteBuffer

// 定义监听器接口
interface OnAgoraAudioListener {

    fun onPlayBackFrameBeforeMixing(
        uid: String?,                       // uid，用于标识是哪个用户
        buffer: ByteArray,                  // 音频数据缓冲区
        samplesPerChannel: Int,             // 每个通道的采样数
        channels: Int,                      // 音频通道数（如：1=单声道，2=立体声）
        samplesPerSec: Int                  // 采样率（如：16000Hz, 44100Hz等）
    ) = Unit

    fun onPlaybackAudioFrame(
        channelId: String?,
        type: Int,
        samplesPerChannel: Int,
        bytesPerSample: Int,
        channels: Int,
        samplesPerSec: Int,
        data: ByteArray,
        renderTimeMs: Long,
    ) = Unit

    fun onRecordFrame(
        channelId: String?,
        type: Int,
        channels: Int,
        buffer: ByteBuffer?,
    ) = Unit
}

class AudioFrameObserver : IAudioFrameObserver {

    var onAgoraAudioListener: OnAgoraAudioListener? = null

    override fun onRecordAudioFrame(
        channelId: String?,
        type: Int,
        samplesPerChannel: Int,
        bytesPerSample: Int,
        channels: Int,
        samplesPerSec: Int,
        buffer: ByteBuffer?,
        renderTimeMs: Long,
        avsync_type: Int
    ): Boolean {
        onAgoraAudioListener?.onRecordFrame(channelId, type, channels, buffer)
        return false
    }

    override fun onPlaybackAudioFrame(
        channelId: String?,
        type: Int,
        samplesPerChannel: Int,
        bytesPerSample: Int,
        channels: Int,
        samplesPerSec: Int,
        buffer: ByteBuffer?,
        renderTimeMs: Long,
        avsync_type: Int
    ): Boolean {
        if (buffer == null) return false
        val data = ByteArray(buffer.remaining())
        buffer.get(data)
        onAgoraAudioListener?.onPlaybackAudioFrame(
            channelId,
            type,
            samplesPerChannel,
            bytesPerSample,
            channels,
            samplesPerSec,
            data,
            renderTimeMs
        )
        return false
    }

    override fun onMixedAudioFrame(
        channelId: String?,
        type: Int,
        samplesPerChannel: Int,
        bytesPerSample: Int,
        channels: Int,
        samplesPerSec: Int,
        buffer: ByteBuffer?,
        renderTimeMs: Long,
        avsync_type: Int
    ): Boolean {
        return false
    }

    override fun onEarMonitoringAudioFrame(
        type: Int,
        samplesPerChannel: Int,
        bytesPerSample: Int,
        channels: Int,
        samplesPerSec: Int,
        buffer: ByteBuffer?,
        renderTimeMs: Long,
        avsync_type: Int
    ): Boolean {
        return false
    }

    override fun onPlaybackAudioFrameBeforeMixing(
        channelId: String?,
        uid: Int,
        type: Int,
        samplesPerChannel: Int,
        bytesPerSample: Int,
        channels: Int,
        samplesPerSec: Int,
        buffer: ByteBuffer?,
        renderTimeMs: Long,
        avsync_type: Int,
        rtpTimestamp: Int
    ): Boolean {
        if (buffer == null) return false
        val data = ByteArray(buffer.remaining())
        buffer.get(data)
        onAgoraAudioListener?.onPlayBackFrameBeforeMixing(
            uid.toString(),
            data,
            samplesPerChannel,
            channels,
            samplesPerSec
        )
        return true
    }

    override fun getObservedAudioFramePosition(): Int {
        return 0
    }

    override fun getRecordAudioParams(): AudioParams? {
        return null
    }

    override fun getPlaybackAudioParams(): AudioParams? {
        return null
    }

    override fun getMixedAudioParams(): AudioParams? {
        return null
    }

    override fun getEarMonitoringAudioParams(): AudioParams? {
        return null
    }
}