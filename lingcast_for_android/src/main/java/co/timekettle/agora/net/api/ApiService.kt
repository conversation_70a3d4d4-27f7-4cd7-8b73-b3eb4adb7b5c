package co.timekettle.agora.net.api

import co.timekettle.agora.net.model.AddSpeakerReq
import co.timekettle.agora.net.model.AddSpeakerRes
import co.timekettle.agora.net.model.BaseResponse
import co.timekettle.agora.net.model.CreateDialogReq
import co.timekettle.agora.net.model.CreateDialogRes
import co.timekettle.agora.net.model.CreateRoomReq
import co.timekettle.agora.net.model.CreateRoomRes
import co.timekettle.agora.net.model.CustomVocabularyReq
import co.timekettle.agora.net.model.JoinRoomReq
import co.timekettle.agora.net.model.JoinRoomRes
import co.timekettle.agora.net.model.ResetRoomRes
import co.timekettle.agora.net.model.ShareInfoRes
import co.timekettle.agora.net.model.SwitchLocaleReq
import co.timekettle.agora.net.model.SwitchTTSGenderReq
import co.timekettle.agora.net.model.SwitchTTSReq
import co.timekettle.agora.net.model.SwitchTTSSpeedReq
import co.timekettle.agora.net.model.SwitchTTSVolumeReq
import co.timekettle.agora.net.model.TextToSpeechReq
import co.timekettle.agora.net.model.TranslateEngineReq
import retrofit2.http.Body
import retrofit2.http.DELETE
import retrofit2.http.GET
import retrofit2.http.POST
import retrofit2.http.PUT
import retrofit2.http.Path

interface ApiService {
    @POST("room")
    suspend fun createRoom(
        @Body createRoomReq: CreateRoomReq
    ): BaseResponse<CreateRoomRes>

    @POST("v2/room")
    suspend fun createRoomV2(
        @Body createRoomReq: CreateRoomReq
    ): BaseResponse<CreateRoomRes>


    @DELETE("room/{room_no}")
    suspend fun closeRoom(
        @Path("room_no") room_no: String
    ): BaseResponse<Any>

    @GET("room/{room_no}")
    suspend fun getRoomInfo(
        @Path("room_no") room_no: String
    ): BaseResponse<Any>

    @PUT("room/{room_no}")
    suspend fun updateRoomInfo(
        @Path("room_no") room_no: String
    ): BaseResponse<Any>

    @POST("room/{room_no}/join")
    suspend fun joinRoom(
        @Path("room_no") room_no: String,
        @Body joinRoomReq: JoinRoomReq
    ): BaseResponse<JoinRoomRes>

    @POST("room/{room_no}/speaker")
    suspend fun addSpeaker(
        @Path("room_no") room_no: String,
        @Body addSpeakerReq: AddSpeakerReq
    ): BaseResponse<AddSpeakerRes>



    @PUT("room/{room_no}/speaker/locale")
    suspend fun switchLanguage(
        @Path("room_no") room_no: String,
        @Body switchLocaleReq: SwitchLocaleReq
    ): BaseResponse<Any>


    @POST("room/{room_no}/reset")
    suspend fun resetRoom(
        @Path("room_no") room_no: String
    ): BaseResponse<ResetRoomRes>

    @GET("user/join-info")
    suspend fun getShareInfo(
    ): BaseResponse<ShareInfoRes>

    @PUT("room/{room_no}/speaker/tts")
    suspend fun setTtsEnabled(
        @Path("room_no") room_no: String,
        @Body reqList: List<SwitchTTSReq>
    ): BaseResponse<Any>


    @PUT("room/{room_no}/speaker/tts/gender")
    suspend fun setTtsGender(
        @Path("room_no") room_no: String,
        @Body reqList: List<SwitchTTSGenderReq>
    ): BaseResponse<Any>


    @PUT("room/{room_no}/speaker/tts/speed")
    suspend fun setTtsSpeed(
        @Path("room_no") room_no: String,
        @Body reqList: List<SwitchTTSSpeedReq>
    ): BaseResponse<Any>


    @PUT("room/{room_no}/speaker/tts/volume")
    suspend fun setTtsVolume(
        @Path("room_no") room_no: String,
        @Body reqList: List<SwitchTTSVolumeReq>
    ): BaseResponse<Any>

    @PUT("room/{room_no}/speaker/translate-engine")
    suspend fun setRoomTranslateEngine(
        @Path("room_no") room_no: String,
        @Body req: TranslateEngineReq
    ): BaseResponse<Any>


    @PUT("dialog/{room_no}/custom-vocabulary")
    suspend fun setCustomVocabulary(
        @Path("room_no") room_no: String,
        @Body req: CustomVocabularyReq
    ): BaseResponse<Any>

    @PUT("dialog/{room_no}/translate-engine")
    suspend fun setTranslateEngine(
        @Path("room_no") room_no: String,
        @Body req: TranslateEngineReq
    ): BaseResponse<Any>


    @POST("dialog/{room_no}/tts")
    suspend fun setTextToSpeech(
        @Path("room_no") room_no: String,
        @Body req: TextToSpeechReq
    ): BaseResponse<Any>


    @POST("dialog/{room_no}/custom-vocabulary/refresh")
    suspend fun refreshCustomVocabulary(
        @Path("room_no") room_no: String,
    ): BaseResponse<Any>




    @POST("dialog")
    suspend fun createDialog(
        @Body createDialogReq: CreateDialogReq
    ): BaseResponse<CreateDialogRes>




}