package co.timekettle.agora.net.model


// 语言切换请求
data class SwitchLocaleReq(
    val locale: String = "",
    val target_locale: List<String> = listOf()   // 右声道的语言作为List传，为了兼容演讲模式
)


// 切换TTS开关请求
data class SwitchTTSReq(
    val channel: String = SpeechChannel.NONE.channelName,
    val tts_on: Boolean = true
) {
    companion object {

        fun switchLeftRight(leftEnabled: <PERSON>olean, rightEnabled: <PERSON>olean) = listOf(
            SwitchTTSReq(SpeechChannel.LEFT.channelName, leftEnabled),
            SwitchTTSReq(SpeechChannel.RIGHT.channelName, rightEnabled)
        )
    }
}


// 切换TTS音色请求
data class SwitchTTSGenderReq(
    val channel: String = SpeechChannel.NONE.channelName,
    val gender: String = TTSGender.NONE.gender
) {
    companion object {
        fun switchLeft(value: String): SwitchTTSGenderReq =
            SwitchTTSGenderReq(SpeechChannel.LEFT.channelName, value)

        fun switchRight(value: String): SwitchTTSGenderReq =
            SwitchTTSGenderReq(SpeechChannel.RIGHT.channelName, value)
    }
}


// 切换TTS语速请求
data class SwitchTTSSpeedReq(
    val channel: String = SpeechChannel.NONE.channelName,
    val speed: Float = 1.0f
) {
    companion object {
        fun switchLeftRight(speed: Float) = listOf(
            SwitchTTSSpeedReq(SpeechChannel.LEFT.channelName, speed),
            SwitchTTSSpeedReq(SpeechChannel.RIGHT.channelName, speed)
        )
    }
}


// 切换自定义翻译开关请求
data class CustomVocabularyReq(
    val custom_vocabulary_enabled: Boolean = false
)

// 切换翻译引擎
data class TranslateEngineReq(
    val translate_engine: String = ""
)

// 文本转语音
data class TextToSpeechReq(
    val text_list: List<TextToSpeechItem> = listOf()
)

data class TextToSpeechItem(
    val locale: String = "",
    val text: String = "",
)

data class SwitchTTSVolumeReq(
    val channel: String = SpeechChannel.NONE.channelName,
    val volume: Int = 50
) {
    companion object {
        fun switchLeft(value: Int): SwitchTTSVolumeReq =
            SwitchTTSVolumeReq(SpeechChannel.LEFT.channelName, value)

        fun switchRight(value: Int): SwitchTTSVolumeReq =
            SwitchTTSVolumeReq(SpeechChannel.RIGHT.channelName, value)
    }
}


enum class SpeechChannel(val channelName: String) {
    LEFT("left"),
    RIGHT("right"),
    NONE("none")
}

enum class TTSGender(val gender: String) {
    MALE("male"),
    FEMALE("female"),
    NONE("none")
}