package co.timekettle.agora.net.model

data class SpeechMessage(
    val session_id: String, // 在1v1下，一个session_id就是一个气泡
    var bubble_id: String = "-1", // 气泡ID，用于1v1模式，bubble_id相同的是同一个气泡
    val state: String,
    val locale: String,
    var text: String,
    var channel: String,  // left或者right
    var is_end: Boolean,  //数据拆分用，如果数据超过最大长度，会被拆分。false代表未结束，true代表结束，大部分情况下都会是true
) {
    override fun toString(): String {
        return "bubble_id:$bubble_id $channel $state $locale is_end=$is_end $text "
//        return "session:$session_id, bubble:$bubble_id $state $locale is_end=$is_end $text "
    }
}
