package co.timekettle.agora.net.repository

import co.timekettle.agora.net.api.ApiService
import co.timekettle.agora.net.model.AddSpeakerReq
import co.timekettle.agora.net.model.AddSpeakerRes
import co.timekettle.agora.net.model.BaseResponse
import co.timekettle.agora.net.model.CreateDialogReq
import co.timekettle.agora.net.model.CreateDialogRes
import co.timekettle.agora.net.model.CreateRoomReq
import co.timekettle.agora.net.model.CreateRoomRes
import co.timekettle.agora.net.model.CustomVocabularyReq
import co.timekettle.agora.net.model.JoinRoomReq
import co.timekettle.agora.net.model.JoinRoomRes
import co.timekettle.agora.net.model.ResetRoomRes
import co.timekettle.agora.net.model.ShareInfoRes
import co.timekettle.agora.net.model.SwitchLocaleReq
import co.timekettle.agora.net.model.SwitchTTSGenderReq
import co.timekettle.agora.net.model.SwitchTTSReq
import co.timekettle.agora.net.model.SwitchTTSSpeedReq
import co.timekettle.agora.net.model.SwitchTTSVolumeReq
import co.timekettle.agora.net.model.TextToSpeechReq
import co.timekettle.agora.net.model.TranslateEngineReq
import co.timekettle.agora.net.retrofit.RetrofitClient

class ApiRepository {

    private val apiService = RetrofitClient.instance.create(ApiService::class.java)

    suspend fun createRoom(createRoomReq: CreateRoomReq): BaseResponse<CreateRoomRes> {
        return apiService.createRoom(createRoomReq)
    }

    suspend fun createDialog(createDialogReq: CreateDialogReq): BaseResponse<CreateDialogRes> {
        return apiService.createDialog(createDialogReq)
    }

    suspend fun createRoomV2(createRoomReq: CreateRoomReq): BaseResponse<CreateRoomRes> {
        return apiService.createRoomV2(createRoomReq)
    }

    suspend fun closeRoom(room_no: String): BaseResponse<Any> {
        return apiService.closeRoom(room_no)
    }

    suspend fun joinRoom(room_no: String, joinRoomReq: JoinRoomReq): BaseResponse<JoinRoomRes> {
        return apiService.joinRoom(room_no, joinRoomReq)
    }

    suspend fun addSpeaker(
        room_no: String,
        addSpeakerReq: AddSpeakerReq
    ): BaseResponse<AddSpeakerRes> {
        return apiService.addSpeaker(room_no, addSpeakerReq)
    }


    suspend fun switchLanguage(
        room_no: String,
        switchLocaleReq: SwitchLocaleReq
    ): BaseResponse<Any> {
        return apiService.switchLanguage(room_no, switchLocaleReq)
    }


    suspend fun setTtsSpeed(room_no: String, reqList: List<SwitchTTSSpeedReq>): BaseResponse<Any> {
        return apiService.setTtsSpeed(room_no, reqList)
    }

    suspend fun setTtsVolume(
        room_no: String,
        reqList: List<SwitchTTSVolumeReq>
    ): BaseResponse<Any> {
        return apiService.setTtsVolume(room_no, reqList)
    }

    suspend fun setTtsEnabled(room_no: String, reqList: List<SwitchTTSReq>): BaseResponse<Any> {
        return apiService.setTtsEnabled(room_no, reqList)
    }

    suspend fun setTtsGender(
        room_no: String,
        reqList: List<SwitchTTSGenderReq>
    ): BaseResponse<Any> {
        return apiService.setTtsGender(room_no, reqList)
    }


    suspend fun setCustomVocabulary(
        room_no: String,
        req: CustomVocabularyReq
    ): BaseResponse<Any> {
        return apiService.setCustomVocabulary(room_no, req)
    }


    suspend fun setTranslateEngine(
        room_no: String,
        req: TranslateEngineReq
    ): BaseResponse<Any> {
        return apiService.setTranslateEngine(room_no, req)
    }

    suspend fun setRoomTranslateEngine(
        room_no: String,
        req: TranslateEngineReq
    ): BaseResponse<Any> {
        return apiService.setRoomTranslateEngine(room_no, req)
    }


    suspend fun setTextToSpeech(
        room_no: String,
        req: TextToSpeechReq
    ): BaseResponse<Any> {
        return apiService.setTextToSpeech(room_no, req)
    }

    suspend fun refreshCustomVocabulary(
        room_no: String,
    ): BaseResponse<Any> {
        return apiService.refreshCustomVocabulary(room_no)
    }


    suspend fun resetRoom(room_no: String): BaseResponse<ResetRoomRes> {
        return apiService.resetRoom(room_no)
    }

    suspend fun getShareInfo(): BaseResponse<ShareInfoRes> {
        return apiService.getShareInfo()
    }

}