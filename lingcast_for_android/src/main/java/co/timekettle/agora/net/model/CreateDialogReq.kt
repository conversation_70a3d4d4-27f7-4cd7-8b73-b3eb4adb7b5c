package co.timekettle.agora.net.model

import co.timekettle.agora.CommandConfig

data class CreateDialogReq(
    var room_no: String = "",  // 房间号（可选，客户端如果传递，固定使用此会议号。建议客户端首次获取到会议号后保存本地，下次请求带上，防止会议号消耗过快要做回收操作）
    var scenario: String = "", // 场景 单识别：recognize  语音到文本：translate_speech_to_text 语音到语音：translate_speech_to_speech
    var vad_enabled: Boolean = false, // 是否启动vad，默认false
    var voice_setting: List<VoiceSetting> = listOf(),  // 音频设置
    var locale_list: List<LocaleItem> = listOf(), // 语言列表
    var custom_vocabulary_enabled: Boolean = false, // 是否开启自定义词条
    var translate_engine: String = TranslateEngine.default.engineName,  // 选用翻译引擎(大模型/机器翻译)
    var cloud_recording_enabled: Boolean = false // 是否启用云端录制（调试用）
) {
    data class LocaleItem(
        var channel: String = "",
        var locale: String = "",
    ) {
        companion object {
            fun withLeft(locale: String) =
                LocaleItem("left", locale)

            fun withRight(locale: String) =
                LocaleItem("right", locale)

            fun withLeftRight(
                localeLeft: String,
                localeRight: String
            ) = listOf(
                withLeft(localeLeft), withRight(localeRight)
            )
        }
    }

    data class VoiceSetting(
        var channel: String = "",
        var command: Command = Command()
    ) {
        data class Command(
            var gender: String = CommandConfig.DefaultVoiceGender,
            var is_open: Boolean = CommandConfig.DefaultTTSEnabled,
            var speed: Float = CommandConfig.DefaultTTSSpeed,
            var volume: Float = CommandConfig.DefaultTTSVolume.toFloat()
        )

        companion object {
            fun withLeft() = VoiceSetting().apply { channel = CommandConfig.ChannelLeft }
            fun withRight() = VoiceSetting().apply { channel = CommandConfig.ChannelRight }
            fun Default() = listOf(withLeft(), withRight())
        }
    }


    // 具体引擎的含义参照后端文档
    // 机器翻译选择g_001，大模型翻译选择o_001，暂时只用这两个
    enum class TranslateEngine(val engineName: String) {
        g_001("g_001"),
        o_001("o_001"),
        o_002("o_002"),
        default("")  // 留空让服务器自己选择
    }

}