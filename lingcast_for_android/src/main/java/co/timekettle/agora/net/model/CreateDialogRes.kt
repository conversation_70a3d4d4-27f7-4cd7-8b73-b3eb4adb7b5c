package co.timekettle.agora.net.model

data class CreateDialogRes(
    val connect_token: String,
    val connect_uid: String,
    val room_no: String,
    val speaker_identity_no: String,
    val token_info: TokenInfo,
    val translation_list: List<TranslationItem>
) {
    data class TokenInfo(
        val expires_in: Int,
        val token: String
    )

    data class TranslationItem(
        val locale: String,
        val subscribe_uid: String
    )
}