package co.timekettle.agora.net.interceptor

import co.timekettle.agora.SpeechManager
import co.timekettle.agora.common.constant.SpeechUrl
import co.timekettle.agora.common.utils.LogUtil
import co.timekettle.agora.net.helper.ResponseCodeEnum
import co.timekettle.agora.net.helper.ResponseException
import co.timekettle.agora.net.model.BaseResponse
import co.timekettle.agora.net.model.GetTokenRes
import com.google.gson.Gson
import com.google.gson.reflect.TypeToken
import okhttp3.Interceptor
import okhttp3.MediaType.Companion.toMediaTypeOrNull
import okhttp3.OkHttpClient
import okhttp3.Request
import okhttp3.RequestBody.Companion.toRequestBody
import okhttp3.Response
import okhttp3.logging.HttpLoggingInterceptor
import org.json.JSONObject
import java.util.concurrent.TimeUnit

class CaptainTokenInterceptor : Interceptor {

    override fun intercept(chain: Interceptor.Chain): Response {
        val originalRequest: Request = chain.request()
        // 更新语言用的是Speaker的Token
        // FIXME: 这里需要跟后台协商，换成Captain Token
        if (originalRequest.url.toString().contains("speaker/locale")) {
            val newRequest = originalRequest.newBuilder()
                .addHeader("Authorization", "Bearer ${SpeechManager.getAgoraToken()}")
                .build()
            return chain.proceed(newRequest)
        }
        // 第一次请求
        val firstRequest = originalRequest.newBuilder()
            .addHeader("Authorization", "Bearer ${SpeechManager.getCaptainToken()}")
            .build()
        val firstResponse = chain.proceed(firstRequest)
        // 如果不是401（正常的响应），直接返回原始响应，拦截器只拦截Token不合法的请求
        if (firstResponse.code != 401) {
            return firstResponse
        }
        // 处理401情况
        LogUtil.d(TAG, "captain token失效，刷新token....")
        val newCaptainToken = getNewCaptainToken()  // 根据Business Token，获取新的Captain Token，并保存到本地
        if (newCaptainToken.isEmpty()) {  // 这里直接抛出异常，交给SpeechManager来处理
            throw ResponseException(ResponseCodeEnum.REFRESH_CAPTAIN_TOKEN_FAILED)
        }
        // 获取到了新的captain token，重新执行请求
        firstResponse.close()
        val newAgainRequest = originalRequest.newBuilder()
            .addHeader("Authorization", "Bearer ${SpeechManager.getCaptainToken()}")
            .build()
        return chain.proceed(newAgainRequest)
    }


    private fun getNewCaptainToken(): String {
        return try {
            val newToken = refreshCaptainTokenSync()
            if (newToken.isNotEmpty()) {
                LogUtil.d(TAG, "captain token刷新成功：$newToken")
                SpeechManager.setCaptainToken(newToken)
            }
            newToken
        } catch (e: Exception) {
            LogUtil.e(TAG, "captain token刷新失败", e)
            ""
        }
    }


    // 同步刷新
    @Synchronized
    private fun refreshCaptainTokenSync(): String {
        val requestBody = JSONObject().toString()
            .toRequestBody("application/json; charset=utf-8".toMediaTypeOrNull())
        val request = Request.Builder()
            .url("${SpeechUrl.businessApiUrl}channel/token")
            .addHeader(
                "Authorization",
                "Bearer ${SpeechManager.getBusinessToken()}"
            )  // 这里传业务Token（获取captain token，要通过不同业务的Business token + baseUrl来获取）
            .addHeader("source", SpeechUrl.projectType.sourceToServer)
            .post(requestBody)
            .build()
        val builder = OkHttpClient.Builder().callTimeout(10, TimeUnit.SECONDS)
        // 只有当LogUtil.isLogEnabled()为true时，才添加HttpLoggingInterceptor
        if (LogUtil.isLogEnabled()) {
            builder.addInterceptor(HttpLoggingInterceptor().setLevel(HttpLoggingInterceptor.Level.BODY))
        }
        val client = builder.build()

        var captainToken = ""
        client.newCall(request).execute().use { response ->
            if (response.code == 401) {
                LogUtil.e(TAG, "获取Captain token失败，Business token失效")
            } else if (response.isSuccessful) {
                if (response.body != null) {
                    val json = response.body!!.string()
                    val type = object : TypeToken<BaseResponse<GetTokenRes>>() {}.type
                    val data: BaseResponse<GetTokenRes> = Gson().fromJson(json, type)
                    val token = data.data.token
                    captainToken = token
                }
            }
        }
        return captainToken
    }

    companion object {
        const val TAG = "OkHttp-Captain拦截器"
    }

}