package co.timekettle.agora.net.retrofit

import co.timekettle.agora.common.constant.SpeechUrl
import co.timekettle.agora.common.utils.LogUtil
import co.timekettle.agora.net.interceptor.CaptainTokenInterceptor
import okhttp3.OkHttpClient
import okhttp3.logging.HttpLoggingInterceptor
import retrofit2.Retrofit
import retrofit2.converter.gson.GsonConverterFactory
import java.util.concurrent.TimeUnit

object RetrofitClient {

    private val okHttpClient: OkHttpClient by lazy {
        val builder = OkHttpClient.Builder()
            .addInterceptor(CaptainTokenInterceptor())
            .callTimeout(10, TimeUnit.SECONDS)
//            .connectTimeout(10, TimeUnit.SECONDS)
//            .readTimeout(10, TimeUnit.SECONDS)
//            .writeTimeout(10, TimeUnit.SECONDS)
        
        // 只有当LogUtil.isLogEnabled()为true时，才添加HttpLoggingInterceptor
        if (LogUtil.isLogEnabled()) {
            builder.addInterceptor(HttpLoggingInterceptor().setLevel(HttpLoggingInterceptor.Level.BODY))
        }
        
        builder.build()
    }

    val instance: Retrofit by lazy {
        Retrofit.Builder()
            .baseUrl(SpeechUrl.captainTokenUrl)
            .client(okHttpClient)
            .addConverterFactory(GsonConverterFactory.create())
            .build()
    }

}