package co.timekettle.agora.speech.ext

import android.annotation.SuppressLint
import co.timekettle.agora.SpeechManager
import co.timekettle.agora.common.enums.TransModeType
import co.timekettle.agora.common.models.LingCastParams
import co.timekettle.agora.common.utils.LogUtil
import com.blankj.utilcode.util.FileIOUtils
import com.blankj.utilcode.util.FileUtils
import com.blankj.utilcode.util.PathUtils
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.SupervisorJob
import kotlinx.coroutines.launch
import java.text.SimpleDateFormat
import java.util.Date

var timestampRoom = ""  // 当前房间会话开始时的时间戳(字符串)

private val coroutineScope = CoroutineScope(SupervisorJob() + Dispatchers.IO)

enum class LCLogFileType(var path: String) {
    SEND_PCM(""),
    RECEIVE_PCM(""),
    NETWORK(""),
    RECOGNIZE_TRANSLATE(""),
}

fun saveAudioToFile(type: LCLogFileType, data: ByteArray) {
    if(type.path.isEmpty()) return
    coroutineScope.launch {
        LCLogFileType.values().first { type == it }.let {
            FileIOUtils.writeFileFromBytesByChannel(type.path, data, true, true)
        }
    }
}

@SuppressLint("SimpleDateFormat")
fun saveLogToFile(type: LCLogFileType, content: String) {
    if(type.path.isEmpty()) return
    coroutineScope.launch {
        val timestamp = SimpleDateFormat("yyyy-MM-dd HH:mm:ss.SSS").format(Date())
        LCLogFileType.values().first { type == it }.let {
            FileIOUtils.writeFileFromString(type.path, "$timestamp $content\n", true)
        }
    }
}


@SuppressLint("SimpleDateFormat")
fun initRoomLogDir(lingCastParams: LingCastParams, transModeType: TransModeType, roomNo: String) {
    val baseDir = lingCastParams.customLogFileDir.let { dir ->
        if (dir.endsWith("/")) dir else "$dir/"
    }
    val timeString = SimpleDateFormat("yyyyMMdd_HHmmss").format(Date())
    val curDir = "$baseDir$timeString-${transModeType.tmkModeName}-$roomNo"  // 当前正在使用的模式的dir（一次对话）
    FileUtils.createOrExistsDir(curDir)
    LogUtil.d(SpeechManager.TAG, "创建当前房间日志保存目录: $curDir")
    LCLogFileType.NETWORK.path = "$curDir/network_status.txt"
    LCLogFileType.SEND_PCM.path = "$curDir/发送音频.pcm"
    LCLogFileType.RECEIVE_PCM.path = "$curDir/合成音频.pcm"
    LCLogFileType.RECOGNIZE_TRANSLATE.path = "$curDir/识别翻译结果.txt"
}


fun resetRoomLogDir(){
    LCLogFileType.values().forEach { it.path = "" }
}


