package co.timekettle.agora.speech.interf

import co.timekettle.agora.net.model.SpeechError
import co.timekettle.agora.net.model.SpeechMessage
import co.timekettle.agora.speech.RtcState
import co.timekettle.agora.speech.VolumeInfo

interface SpeechCallback {

    fun onCloseRoom() {}

    fun onRecognize(msg: SpeechMessage) {}

    fun onTranslate(msg: SpeechMessage) {}

    fun onError(msg: SpeechError) {}

    fun onSuccess() {}

    fun onNetworkQuality(uid: Int, txQuality: Int, rxQuality: Int){}

    fun onRtcStats(state: RtcState){}

    // 用户音量提示回调 https://doc.shengwang.cn/api-ref/rtc/android/API/toc_audio_basic#onAudioVolumeIndication
    fun onAudioVolumeIndication(speakers: List<VolumeInfo>, totalVolume:Int){}

}