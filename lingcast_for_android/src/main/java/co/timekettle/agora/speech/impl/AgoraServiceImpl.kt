package co.timekettle.agora.speech.impl

import co.timekettle.agora.speech.Config
import co.timekettle.agora.speech.interf.AgoraService
import io.agora.rtc2.ChannelMediaOptions
import io.agora.rtc2.RtcEngine

class AgoraServiceImpl: AgoraService {

    private var engine: RtcEngine? = null

    override fun createEngine(config: Config): RtcEngine {
        engine = RtcEngine.create(config)
        return engine!!
    }

    override fun joinChannel(token: String, channelId: String, uid: Int, options: ChannelMediaOptions) {
        engine?.let {
            it.joinChannel(token,channelId,uid, options)

        } ?: throw IllegalStateException("Engine is not initialized")
    }

    override fun leaveChannel() {
        engine?.leaveChannel() ?: throw IllegalStateException("Engine is not initialized")
    }

}