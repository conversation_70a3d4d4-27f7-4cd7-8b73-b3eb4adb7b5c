package co.timekettle.agora.speech.ext

import co.timekettle.agora.speech.VolumeInfo
import io.agora.rtc2.IRtcEngineEventHandler.AudioVolumeInfo


fun Array<out AudioVolumeInfo>.toLogString(): String {
    val content = this.joinToString(separator = ",") {
        "uid:${it.uid} volume:${it.volume}"
    }
    return "[$content]"
}



fun Array<out AudioVolumeInfo>.toVolumeInfoList(): List<VolumeInfo> {
    return this.map {
        VolumeInfo(it.uid, it.volume)
    }
}