package co.timekettle.agora.speech

import io.agora.rtc2.ChannelMediaOptions
import io.agora.rtc2.Constants

class Option private constructor(
    clientRoleType: Int,
    autoSubscribeAudio: <PERSON><PERSON><PERSON>,
    autoSubscribeVideo: <PERSON><PERSON><PERSON>,
    publishMicrophoneTrack: <PERSON>olean,
    publishCustomAudioTrack: <PERSON>olean,
    enableAudioRecordingOrPlayOut: Boolean
): ChannelMediaOptions() {

    init {
        this.clientRoleType = clientRoleType
        this.autoSubscribeAudio = autoSubscribeAudio
        this.autoSubscribeVideo = autoSubscribeVideo
        this.publishMicrophoneTrack = publishMicrophoneTrack
        this.publishCustomAudioTrack = publishCustomAudioTrack
        this.enableAudioRecordingOrPlayout = enableAudioRecordingOrPlayOut
    }

    class Builder {
        private var clientRoleType: Int = Constants.CLIENT_ROLE_BROADCASTER
        private var autoSubscribeAudio: Boolean = false
        private var autoSubscribeVideo: Boolean = false
        private var publishMicrophoneTrack: Boolean = false // publishMicrophoneTrack true publishCustomAudioTrack false 声网内部采集
        private var publishCustomAudioTrack: Boolean =  true // publishMicrophoneTrack false publishCustomAudioTrack true 自定义采集
        private var enableAudioRecordingOrPlayOut: Boolean = true

        fun clientRoleType(clientRoleType: Int) = apply { this.clientRoleType = clientRoleType }
        fun autoSubscribeAudio(autoSubscribeAudio: Boolean) = apply { this.autoSubscribeAudio = autoSubscribeAudio }
        fun autoSubscribeVideo(autoSubscribeVideo: Boolean) = apply { this.autoSubscribeVideo = autoSubscribeVideo }
        fun publishMicrophoneTrack(publishMicrophoneTrack: Boolean) = apply { this.publishMicrophoneTrack = publishMicrophoneTrack }
        fun publishCustomAudioTrack(publishCustomAudioTrack: Boolean) = apply { this.publishCustomAudioTrack = publishCustomAudioTrack }
        fun build() = Option(clientRoleType, autoSubscribeAudio, autoSubscribeVideo, publishMicrophoneTrack, publishCustomAudioTrack, enableAudioRecordingOrPlayOut)
    }

}