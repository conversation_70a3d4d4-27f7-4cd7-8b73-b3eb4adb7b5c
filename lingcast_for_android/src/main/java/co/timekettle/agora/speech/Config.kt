package co.timekettle.agora.speech

import android.content.Context
import io.agora.rtc2.Constants
import io.agora.rtc2.IRtcEngineEventHandler
import io.agora.rtc2.RtcEngineConfig

class Config private constructor(
    context: Context,
    appId: String,
    channelProfile: Int,
    audioScenario: Int,
    areaCode: Int
): RtcEngineConfig(){

    init {
        this.mContext = context
        this.mAppId = appId
        this.mChannelProfile = channelProfile
        this.mAudioScenario = audioScenario
        this.mAreaCode = areaCode
    }

    fun setHandler(eventHandler: IRtcEngineEventHandler) {
        this.mEventHandler = eventHandler
    }

    data class Builder(
        private var context: Context
    ) {
        private var appId: String = "950181beb63c49f684e8b75c892173fb"
        private var channelProfile: Int = Constants.CHANNEL_PROFILE_LIVE_BROADCASTING
        private var audioScenario: Int =  Constants.AudioScenario.getValue(Constants.AudioScenario.DEFAULT)
        private var areaCode: Int = -1

        fun appId(appId: String) = apply { this.appId = appId }
        fun channelProfile(channelProfile: Int) = apply { this.channelProfile = channelProfile }
        fun audioScenario(audioScenario: Int) = apply { this.audioScenario = audioScenario }
        fun areaCode(areaCode: Int) = apply { this.areaCode = areaCode }
        fun build() = Config(context, appId, channelProfile, audioScenario, areaCode)
    }

}