package co.timekettle.agora


import co.timekettle.agora.common.utils.LogUtil
import co.timekettle.agora.net.helper.ResponseException
import co.timekettle.agora.net.model.CreateDialogReq.TranslateEngine
import co.timekettle.agora.net.model.CustomVocabularyReq
import co.timekettle.agora.net.model.SwitchLocaleReq
import co.timekettle.agora.net.model.SwitchTTSGenderReq
import co.timekettle.agora.net.model.SwitchTTSReq
import co.timekettle.agora.net.model.SwitchTTSSpeedReq
import co.timekettle.agora.net.model.SwitchTTSVolumeReq
import co.timekettle.agora.net.model.TextToSpeechReq
import co.timekettle.agora.net.model.TranslateEngineReq
import co.timekettle.agora.net.repository.ApiRepository

/**
 * 指令管理器
 * 负责处理TTS、语言切换等指令相关的操作
 */
object CommandManager {
    private val TAG = CommandManager::class.java.simpleName
    private val apiRepository: ApiRepository by lazy { ApiRepository() }
    var roomNo = "" // 房间号

    /**
     * 切换语言
     * @param locale 源语言
     * @param targetLocale 目标语言
     */
    suspend fun switchLanguage(locale: String, targetLocale: String = "") = switchLanguage(
        locale, listOf(targetLocale)
    )

    /**
     * 切换语言
     * @param locale 源语言
     * @param targetLocales 目标语言列表
     */
    suspend fun switchLanguage(locale: String, targetLocales: List<String>): Boolean {
        try {
            val req = SwitchLocaleReq(locale, targetLocales)
            LogUtil.d(TAG, "switchLanguage req: $req")
            val res = apiRepository.switchLanguage(roomNo, req)
            if (res.code == 0) {
                LogUtil.d(TAG, "switchLanguage success: $locale -> $targetLocales")
                return true
            } else {
                LogUtil.e(TAG, "switchLanguage failed: ${res.message}")
                return false
            }
        } catch (e: Exception) {
            processException(e)
            LogUtil.e(TAG, "switchLanguage error: $e")
            return false
        }
    }


    suspend fun setTtsSpeed(speed: Float): Boolean {
        try {
            val reqList = SwitchTTSSpeedReq.switchLeftRight(speed)
            val res = apiRepository.setTtsSpeed(roomNo, reqList)
            if (res.code == 0) {
                LogUtil.d(TAG, "setTtsSpeed success: -> $speed ")
                return true
            } else {
                LogUtil.e(TAG, "setTtsSpeed failed: ${res.message}")
                return false
            }
        } catch (e: Exception) {
            processException(e)
            LogUtil.e(TAG, "setTtsSpeed error: $e")
            return false
        }
    }


    suspend fun setTtsEnabled(enabled: Boolean): Boolean {
        try {
            val reqList = SwitchTTSReq.switchLeftRight(enabled, enabled)
            val res = apiRepository.setTtsEnabled(roomNo, reqList)
            if (res.code == 0) {
                LogUtil.d(TAG, "setTtsEnabled success: enabled-> $enabled ")
                return true
            } else {
                LogUtil.e(TAG, "setTtsEnabled failed: ${res.message}")
                return false
            }
        } catch (e: Exception) {
            processException(e)
            LogUtil.e(TAG, "setTtsEnabled error: $e")
            return false
        }
    }


    suspend fun setTtsEnabled(leftOpen: Boolean, rightOpen: Boolean): Boolean {
        try {
            val reqList = SwitchTTSReq.switchLeftRight(leftOpen, rightOpen)
            val res = apiRepository.setTtsEnabled(roomNo, reqList)
            if (res.code == 0) {
                LogUtil.d(TAG, "setTtsEnabled success: reqList -> $reqList ")
                return true
            } else {
                LogUtil.e(TAG, "setTtsEnabled failed: ${res.message}")
                return false
            }
        } catch (e: Exception) {
            processException(e)
            LogUtil.e(TAG, "setTtsEnabled error: $e")
            return false
        }
    }


    suspend fun setTtsGender(leftGender: String, rightGender: String): Boolean {
        try {
            val reqList = listOf(
                SwitchTTSGenderReq.switchLeft(leftGender),
                SwitchTTSGenderReq.switchRight(rightGender)
            )
            val res = apiRepository.setTtsGender(roomNo, reqList)
            if (res.code == 0) {
                LogUtil.d(TAG, "setTtsGender success: res -> $res ")
                return true
            } else {
                LogUtil.e(TAG, "setTtsGender failed: ${res.message}")
                return false
            }
        } catch (e: Exception) {
            processException(e)
            LogUtil.e(TAG, "setTtsGender error: $e")
            return false
        }
    }


    suspend fun setTtsVolume(leftVolume: Int, rightVolume: Int): Boolean {
        try {
            val reqList = listOf(
                SwitchTTSVolumeReq.switchLeft(leftVolume),
                SwitchTTSVolumeReq.switchRight(rightVolume)
            )
            val res = apiRepository.setTtsVolume(roomNo, reqList)
            if (res.code == 0) {
                LogUtil.d(TAG, "setTtsVolume success: res -> $res ")
                return true
            } else {
                LogUtil.e(TAG, "setTtsVolume failed: ${res.message}")
                return false
            }
        } catch (e: Exception) {
            processException(e)
            LogUtil.e(TAG, "setTtsVolume error: $e")
            return false
        }
    }


    suspend fun setCustomVocabulary(enabled: Boolean): Boolean {
        try {
            val req = CustomVocabularyReq(enabled)
            val res = apiRepository.setCustomVocabulary(roomNo, req)
            if (res.code == 0) {
                LogUtil.d(TAG, "setCustomVocabulary success: req -> $req ")
                return true
            } else {
                LogUtil.e(TAG, "setCustomVocabulary failed: ${res.message}")
                return false
            }
        } catch (e: Exception) {
            processException(e)
            LogUtil.e(TAG, "setCustomVocabulary error: $e")
            return false
        }
    }


    /**
     * 选择翻译引擎，可选大模型翻译/机器翻译，具体的模型值参考服务端文档
     */
    suspend fun setTranslateEngine(engine: TranslateEngine): Boolean {
        try {
            val res =
                apiRepository.setTranslateEngine(roomNo, TranslateEngineReq(engine.engineName))
            if (res.code == 0) {
                LogUtil.d(TAG, "setTranslateEngine success: engine -> $engine ")
                return true
            } else {
                LogUtil.e(TAG, "setTranslateEngine failed: ${res.message}")
                return false
            }
        } catch (e: Exception) {
            processException(e)
            LogUtil.e(TAG, "setTranslateEngine error: $e")
            return false
        }
    }

    /**
     * 对房间内 speaker 选择翻译引擎，可选大模型翻译/机器翻译，具体的模型值参考服务端文档
     */
    suspend fun setRoomTranslateEngine(engine: TranslateEngine): Boolean {
        try {
            val res =
                apiRepository.setRoomTranslateEngine(roomNo, TranslateEngineReq(engine.engineName))
            if (res.code == 0) {
                LogUtil.d(TAG, "setRoomTranslateEngine success: engine -> $engine ")
                return true
            } else {
                LogUtil.e(TAG, "setRoomTranslateEngine failed: ${res.message}")
                return false
            }
        } catch (e: Exception) {
            processException(e)
            LogUtil.e(TAG, "setRoomTranslateEngine error: $e")
            return false
        }
    }


    suspend fun setTextToSpeech(req: TextToSpeechReq): Boolean {
        try {
            val res =
                apiRepository.setTextToSpeech(roomNo, req)
            if (res.code == 0) {
                LogUtil.d(TAG, "setTextToSpeech success: req -> $req ")
                return true
            } else {
                LogUtil.e(TAG, "setTextToSpeech failed: ${res.message}")
                return false
            }
        } catch (e: Exception) {
            processException(e)
            LogUtil.e(TAG, "setTextToSpeech error: $e")
            return false
        }
    }

    // 刷新自定义词库
    suspend fun refreshCustomVocabulary(): Boolean {
        try {
            if (roomNo.isEmpty()) {
                LogUtil.d(TAG, "refreshCustomVocabulary failed: roomNo isEmpty ")
                return false
            }
            val res =
                apiRepository.refreshCustomVocabulary(roomNo)
            if (res.code == 0) {
                LogUtil.d(TAG, "refreshCustomVocabulary success ")
                return true
            } else {
                LogUtil.e(TAG, "refreshCustomVocabulary failed: ${res.message}")
                return false
            }
        } catch (e: Exception) {
            processException(e)
            LogUtil.e(TAG, "refreshCustomVocabulary error: $e")
            return false
        }
    }


    private fun processException(e: Exception, descPrefix: String = "") {
        if (e is ResponseException) {
            SpeechManager.processException(e, descPrefix)
        }
    }

    /**
     * 清除所有状态
     */
    fun clear() {
        roomNo = ""
    }
}

/**
 * TTS状态数据类
 */
data class TTSState(
    val enabled: Boolean,
    val speed: Float,
    val voiceId: String,
    val currentLocale: String,
    val targetLocale: String,
    val volume: Int
)


/**
 * 指令管理器配置类
 */
object CommandConfig {
    const val DefaultTTSEnabled: Boolean = true
    const val DefaultTTSSpeed: Float = 1.2f
    const val DefaultVoiceGender: String = "female"
    const val DefaultTTSVolume: Int = 100
    const val ChannelLeft: String = "left"
    const val ChannelRight: String = "right"
}