package co.timekettle.agora.common.constant

import co.timekettle.agora.common.enums.TmkProjectType
import co.timekettle.agora.common.utils.LogUtil

object SpeechUrl {

    const val BUSINESS_BASE_URL = "https://api-glb.timekettle.co" // 线上环境
    const val BUSINESS_AMERICA_BASE_URL = "https://api-us-east.timekettle.co" // 线上环境（美国）
    private var BASE_URL = BUSINESS_BASE_URL
    const val TAG = "SpeechUrl"

    var businessApiUrl = BUSINESS_BASE_URL
    var captainTokenUrl = BUSINESS_BASE_URL
    var projectType: TmkProjectType = TmkProjectType.NONE

    // businessUrl示例⬇ 收到businessUrl后，SDK将会在后面拼接上"/channel/token"，去拿新的captain token，可以带斜杠，也可以不带斜杠
    // X1 - http://8.135.239.158:24724/app
    // 时空壶app - https://test-api.timekettle.net/markov/user

    fun setProjectUrl(projectType: TmkProjectType,businessUrl: String, captainUrl: String): Boolean {
        LogUtil.d(TAG, "setUrl,project:$projectType businessUrl:$businessUrl ,captainUrl:$captainUrl")
        if (!checkUrlValid(businessUrl) || !checkUrlValid(captainUrl)) {
            return false
        }
        this.projectType = projectType
        BASE_URL = if (!businessUrl.endsWith("/")) "$businessUrl/" else businessUrl  // 添加斜杠
        businessApiUrl = if (!businessUrl.endsWith("/")) "$businessUrl/" else businessUrl  // 添加斜杠
        captainTokenUrl =
            if (!captainUrl.endsWith("/")) "$captainUrl/channel/" else "${captainUrl}channel/"
        return true
    }

    // Url必须要是http或者https开头，不合法会返回false
    private fun checkUrlValid(url: String): Boolean {
        val isValid = (url.startsWith("http://") || url.startsWith("https://"))
        if (!isValid) {
            LogUtil.d(TAG, "url不合法:[$url] 必须以http/https开头")
        }
        return isValid
    }

    fun getBaseUrl() = BASE_URL


}