package co.timekettle.agora.common.utils

import android.annotation.SuppressLint
import android.content.Context
import android.util.Log
import co.timekettle.agora.common.models.LingCastParams
import co.timekettle.agora.common.utils.FormatUtils.formatLogFileTime
import com.blankj.utilcode.util.FileUtils
import com.blankj.utilcode.util.PathUtils
import java.io.File
import java.io.FileWriter
import java.text.SimpleDateFormat
import java.util.Date
import java.util.Locale
import java.util.concurrent.ExecutorService
import java.util.concurrent.Executors
import java.util.concurrent.atomic.AtomicReference
import java.util.concurrent.locks.ReentrantLock
import java.util.concurrent.TimeUnit

/**
 * 日志工具类，仅在LingCast库内部使用
 *
 */
internal object LogUtil {
    private const val TAG_PREFIX = "LC-"
    private const val LOG_FILE_PREFIX = "lingcast_"
    private const val LOG_FILE_EXTENSION = ".log"
    private const val MAX_LOG_FILE_SIZE = 5 * 1024 * 1024 // 5MB
    private const val MAX_LOG_FILES = 8  // 最多保留8个日志文件
    private const val EXECUTOR_SHUTDOWN_TIMEOUT = 5L // 线程池关闭超时时间（秒）

    @Volatile
    private var enableLogToLogCat = true
    @Volatile
    private var enableLogToFile = true
    @Volatile
    private var isInitialized = false  // Add this line to declare the isInitialized variable
    private val fileLock = ReentrantLock()

    @SuppressLint("ConstantLocale")
    private val dateFormat = SimpleDateFormat("yyyy-MM-dd HH:mm:ss.SSS", Locale.getDefault())
    private var dirPath = ""
    private var isCustomPath = false
    private val currentLogFile = AtomicReference<File>()
    private val executorService: ExecutorService = Executors.newSingleThreadExecutor { r ->
        val thread = Thread(r, "LogUtil-Worker")
        thread.priority = Thread.NORM_PRIORITY - 1
        thread.uncaughtExceptionHandler = Thread.UncaughtExceptionHandler { t, e ->
            Log.e(formatTag("LogUtils"), "Uncaught exception in worker thread: ${e.message}", e)
        }
        thread
    }

    /**
     * 初始化日志工具
     */
    @Synchronized
    fun init(context: Context, lingCastParams: LingCastParams) {
        try {
            enableLogToLogCat = lingCastParams.enableLogToLogCat
            enableLogToFile = lingCastParams.enableLogToFile
            isCustomPath = lingCastParams.customLogFileDir.isNotEmpty()
            dirPath = if (isCustomPath) {
                lingCastParams.customLogFileDir.trim().let {
                    if (it.endsWith("/")) it else "$it/"
                }
            } else {
                PathUtils.getInternalAppCachePath() + "/Log/lingcast/"
            }

            if (enableLogToFile) {
                ensureLogDirectory()
                cleanupOldLogs()
                d("LogUtil", "日志系统初始化完成，日志目录：$dirPath")
            }
            isInitialized = true  // 标记初始化完成
        } catch (e: Exception) {
            Log.e(formatTag("LogUtils"), "初始化日志系统失败", e)
            enableLogToFile = false
            isInitialized = false  // 初始化失败
        }
    }

    fun d(tag: String = "", msg: String) {
        if (!isInitialized) {
            Log.d(formatTag("LogUtils"), "LogUtil未初始化，请先调用init方法")
            return
        }
        if (enableLogToLogCat) {
            Log.d(formatTag(tag), msg)
            writeToFile("D", tag, msg)
        }
    }

    fun i(tag: String, msg: String) {
        if (!isInitialized) {
            Log.i(formatTag("LogUtils"), "LogUtil未初始化，请先调用init方法")
            return
        }
        if (enableLogToLogCat) {
            Log.i(formatTag(tag), msg)
            writeToFile("I", tag, msg)
        }
    }

    fun w(tag: String, msg: String) {
        if (!isInitialized) {
            Log.w(formatTag("LogUtils"), "LogUtil未初始化，请先调用init方法")
            return
        }
        Log.w(formatTag(tag), msg)
        writeToFile("W", tag, msg)
    }

    fun e(tag: String, msg: String, throwable: Throwable? = null) {
        if (!isInitialized) {
            Log.e(formatTag("LogUtils"), "LogUtil未初始化，请先调用init方法")
            return
        }
        val logMsg = if (throwable != null) {
            "$msg\n${throwable.stackTraceToString()}"
        } else {
            msg
        }
        Log.e(formatTag(tag), logMsg, throwable)
        writeToFile("E", tag, logMsg)
    }

    private fun writeToFile(level: String, tag: String, msg: String) {
        if (!isInitialized || !enableLogToFile || dirPath.isEmpty()) return

        executorService.execute {
            fileLock.lock()
            try {
                var logFile = currentLogFile.get()
                if (logFile == null || !logFile.exists() || logFile.length() > MAX_LOG_FILE_SIZE) {
                    logFile = createNewLogFile()
                    currentLogFile.set(logFile)
                }

                if (!logFile.parentFile?.exists()!!) {
                    logFile.parentFile?.mkdirs()
                }

                val timestamp = dateFormat.format(Date())
                val logContent = "[$timestamp][$level][${tag.trim()}] $msg\n"

                FileWriter(logFile, true).use { writer ->
                    writer.write(logContent)
                    writer.flush()
                }
            } catch (e: Exception) {
                Log.e(formatTag("LogUtils"), "写入日志文件失败", e)
            } finally {
                fileLock.unlock()
            }
        }
    }

    private fun getLogDirectory(): File = File(dirPath)

    private fun createNewLogFile(): File {
        val timestamp = System.currentTimeMillis()
        val file = File(
            getLogDirectory(),
            "${LOG_FILE_PREFIX}${formatLogFileTime(timestamp)}${LOG_FILE_EXTENSION}"
        )
        try {
            FileUtils.createOrExistsFile(file)
            cleanupOldLogs()
        } catch (e: Exception) {
            Log.e(formatTag("LogUtils"), "创建日志文件失败: ${file.absolutePath}", e)
        }
        return file
    }

    private fun ensureLogDirectory() {
        val logDir = getLogDirectory()
        if (!logDir.exists() && !logDir.mkdirs()) {
            Log.d(formatTag("LogUtils"),"无法创建日志目录: ${logDir.absolutePath}")
        }
    }

    private fun cleanupOldLogs() {
        val logDir = getLogDirectory()
        if (!logDir.exists()) return
        try {
            val logFiles = logDir.listFiles { file ->
                file.isFile && file.name.startsWith(LOG_FILE_PREFIX) && file.name.endsWith(LOG_FILE_EXTENSION)
            }?.sortedBy { it.lastModified() } ?: return

            // 如果文件数量超过限制，删除最旧的文件
            if (logFiles.size > MAX_LOG_FILES) {
                logFiles.take(logFiles.size - MAX_LOG_FILES).forEach { file ->
                    try {
                        if (!FileUtils.delete(file)) {
                            Log.w(formatTag("LogUtils"), "删除旧日志文件失败: ${file.absolutePath}")
                        }
                    } catch (e: Exception) {
                        Log.e(formatTag("LogUtils"), "删除旧日志文件异常: ${file.absolutePath}", e)
                    }
                }
            }
        } catch (e: Exception) {
            Log.e(formatTag("LogUtils"), "清理旧日志文件失败", e)
        }
    }

    private fun formatTag(tag: String): String = "$TAG_PREFIX${tag.trim()}"

    /**
     * 获取日志是否启用
     */
    fun isLogEnabled(): Boolean = enableLogToLogCat

    /**
     * 关闭日志系统，确保所有日志都写入文件
     */
    fun shutdown() {
        try {
            executorService.shutdown()
            if (!executorService.awaitTermination(EXECUTOR_SHUTDOWN_TIMEOUT, TimeUnit.SECONDS)) {
                executorService.shutdownNow()
            }
        } catch (e: Exception) {
            Log.e(formatTag("LogUtils"), "关闭日志系统失败", e)
            executorService.shutdownNow()
        }
    }
}