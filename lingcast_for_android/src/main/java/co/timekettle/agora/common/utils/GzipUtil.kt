package co.timekettle.agora.common.utils

import java.io.ByteArrayInputStream
import java.io.ByteArrayOutputStream
import java.io.InputStreamReader
import java.io.OutputStreamWriter
import java.util.zip.GZIPInputStream
import java.util.zip.GZIPOutputStream

object GzipUtil {

    fun decompressGzipText(compressedData: ByteArray): String {
        ByteArrayInputStream(compressedData).use { byteArrayInputStream ->
            GZIPInputStream(byteArrayInputStream).use { gzipInputStream ->
                InputStreamReader(gzipInputStream, Charsets.UTF_8).use { reader ->
                    val output = StringBuilder()
                    val buffer = CharArray(1024)
                    var charsRead: Int
                    while (reader.read(buffer).also { charsRead = it } != -1) {
                        output.append(buffer, 0, charsRead)
                    }
                    return output.toString()
                }
            }
        }
    }

    fun compressTextToGzip(data: String): ByteArray {
        ByteArrayOutputStream().use { byteArrayOutputStream ->
            GZIPOutputStream(byteArrayOutputStream).use { gzipOutputStream ->
                OutputStreamWriter(gzipOutputStream, Charsets.UTF_8).use { writer ->
                    writer.write(data)
                }
            }
            return byteArrayOutputStream.toByteArray()
        }
    }

}