package co.timekettle.agora.common.models

import com.blankj.utilcode.util.PathUtils

class LingCastParams(
    var saveDebugAudio: Boolean = false, // 是否保存调试音频到本地 （LingCast库层，自己做保存，上线前需要关掉）
    var saveAgoraAudio: Boolean = false, // 是否保存调试音频到本地 （声网SDK自己的保存功能，上线前需要关掉）
    var enableLogToFile: Boolean = true, // 是否保存日志到文件
    var enableLogToLogCat: Boolean = true, // 是否打印日志到控制台
    var customLogFileDir: String = PathUtils.getInternalAppCachePath() + "/Log/lingcast/", // LingCast日志文件保存目录(包含音频)
)