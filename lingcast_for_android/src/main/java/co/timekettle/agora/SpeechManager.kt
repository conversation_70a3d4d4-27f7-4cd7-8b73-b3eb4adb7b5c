package co.timekettle.agora

import android.content.Context
import android.os.Handler
import android.os.Looper
import co.timekettle.agora.common.constant.SpKey.BUSINESS_TOKEN
import co.timekettle.agora.common.constant.SpKey.CAPTAIN_TOKEN
import co.timekettle.agora.common.constant.SpeechUrl
import co.timekettle.agora.common.enums.ErrorCode
import co.timekettle.agora.common.enums.EventType
import co.timekettle.agora.common.enums.NotifyType
import co.timekettle.agora.common.enums.SpeechType
import co.timekettle.agora.common.enums.TransModeType
import co.timekettle.agora.common.models.BusinessParams
import co.timekettle.agora.common.models.LingCastParams
import co.timekettle.agora.common.utils.FormatUtils.formatLogFileTime
import co.timekettle.agora.net.model.AddSpeakerReq
import co.timekettle.agora.net.model.AddSpeakerRes
import co.timekettle.agora.net.model.CreateRoomReq
import co.timekettle.agora.net.model.CreateRoomRes
import co.timekettle.agora.net.model.SpeechError
import co.timekettle.agora.net.model.ResetRoomRes
import co.timekettle.agora.net.model.ShareInfoRes
import co.timekettle.agora.net.model.SpeechMessage
import co.timekettle.agora.net.model.Translation
import co.timekettle.agora.net.repository.ApiRepository
import co.timekettle.agora.speech.Config
import co.timekettle.agora.speech.Option
import co.timekettle.agora.speech.RtcState
import co.timekettle.agora.speech.interf.SpeechCallback
import io.agora.rtc2.Constants
import io.agora.rtc2.IRtcEngineEventHandler
import io.agora.rtc2.RtcEngine
import io.agora.rtc2.audio.AudioTrackConfig
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch
import java.nio.ByteBuffer
import co.timekettle.agora.common.utils.LogUtil
import co.timekettle.agora.common.utils.SpUtils
import co.timekettle.agora.listener.AudioFrameObserver
import co.timekettle.agora.listener.OnAgoraAudioListener
import co.timekettle.agora.net.helper.ResponseException
import co.timekettle.agora.net.model.BaseMessage
import co.timekettle.agora.net.model.CreateDialogReq
import co.timekettle.agora.net.model.CreateDialogReq.TranslateEngine
import co.timekettle.agora.net.model.CreateDialogRes
import co.timekettle.agora.net.model.TextToSpeechReq
import co.timekettle.agora.speech.ext.LCLogFileType
import co.timekettle.agora.speech.ext.initRoomLogDir
import co.timekettle.agora.speech.ext.resetRoomLogDir
import co.timekettle.agora.speech.ext.saveAudioToFile
import co.timekettle.agora.speech.ext.saveLogToFile
import co.timekettle.agora.speech.ext.timestampRoom
import co.timekettle.agora.speech.ext.toLogString
import co.timekettle.agora.speech.ext.toVolumeInfoList
import com.google.gson.Gson
import com.google.gson.reflect.TypeToken
import io.agora.rtc2.Constants.RAW_AUDIO_FRAME_OP_MODE_READ_WRITE
import org.json.JSONObject

/**
 * 语音管理器（全局单例）
 *
 * 主要职责：
 * 1. 音频通信的顶层管理，包括音频采集、播放等
 * 2. 房间生命周期管理（创建、加入、离开、销毁）
 * 3. 声网引擎(RtcEngine)的初始化和配置
 * 4. 提供对外的业务接口（如切换语言、TTS订阅等）
 * 5. 管理各种Token和认证信息
 *
 * 依赖关系：
 * - 依赖 RTCRequestClient 处理信令通信
 * - 依赖 RTCSignalingManager 处理原始消息
 * - 依赖 ApiRepository 处理HTTP请求
 */
object SpeechManager {

    var engine: RtcEngine? = null
    private var customAudioTrack = -1
    var trans: List<Translation> = mutableListOf()
    private var callback: SpeechCallback? = null
    private val apiRepository: ApiRepository by lazy { ApiRepository() }
    private val scope: CoroutineScope by lazy { CoroutineScope(Dispatchers.IO) }
    private val handler = Handler(Looper.getMainLooper())
    private var agoraToken = ""
    var room_no = ""
    private var mAudioFrameObserver: AudioFrameObserver? = null
    private var audioObserverList = mutableListOf<OnAgoraAudioListener>()
    private var lingCastParams = LingCastParams()
    private var mTransModeType = TransModeType.NONE

    fun init(
        context: Context,
        captainUrl: String = SpeechUrl.BUSINESS_BASE_URL,
        businessParams: BusinessParams,
        lingCastParams: LingCastParams,
    ): Boolean {
        SpUtils.initMMKV(context)
        setBusinessToken(businessParams.businessToken)  // 保存businessToken到本地
        this.lingCastParams = lingCastParams
        LogUtil.init(context, lingCastParams)
        return SpeechUrl.setProjectUrl(
            businessParams.tmkProjectType,
            businessParams.businessUrl,
            captainUrl
        )
    }

    /**
     * 创建引擎
     * @param config 引擎配置
     * @param customPlay 是否自定义播放
     */
    fun createEngine(
        config: Config,
        customPlay: Boolean,
        sampleRate: Int,
        channels: Int = 2,
        transModeType: TransModeType = TransModeType.NONE,
        speechCallback: SpeechCallback,
    ) {
        callback = speechCallback
        config.setHandler(eventHandler)
        engine = RtcEngine.create(config)
        engine?.setClientRole(Constants.CLIENT_ROLE_BROADCASTER)
//        engine?.enableAudioVolumeIndication(2000, 3, false) // 启用声音大小回调（2000ms一次）
        setPrivateParams(transModeType)
        val audioTrackConfig = AudioTrackConfig()
        audioTrackConfig.enableLocalPlayback = false
        mTransModeType = transModeType
        customAudioTrack = engine!!.createCustomAudioTrack(
            Constants.AudioTrackType.AUDIO_TRACK_MIXABLE,
            audioTrackConfig
        )
        if (customPlay) {
            engine!!.setExternalAudioSink(true, sampleRate, channels)
        }
        timestampRoom = formatLogFileTime(System.currentTimeMillis())
        initAudioObserver(transModeType)
        LogUtil.d(TAG, "初始化声网RtcEngine完成, 是否自定义渲染:$customPlay")
    }

    // 如果需要传输立体音，必须设置声网提供的私参
    private fun setPrivateParams(mode: TransModeType) {
        LogUtil.d(TAG, "设置私参，mode: $mode")
        if (mode == TransModeType.LISTEN_FAR) { // 远场收音私参
            engine?.apply {
                engine?.setAudioScenario(Constants.AUDIO_SCENARIO_CHORUS)
                setParameters("{\"che.audio.aec.split_srate_for_48k\":16000}");
                setParameters("{\"che.audio.sf.enabled\":true}");
                setParameters("{\"che.audio.sf.ainlpToLoadFlag\":1}");
                setParameters("{\"che.audio.sf.nlpAlgRoute\":1}");
                setParameters("{\"che.audio.sf.ainlpModelPref\":10}")

                setParameters("{\"che.audio.sf.ainsToLoadFlag\":1}");
                setParameters("{\"che.audio.sf.nsngAlgRoute\":12}");
                setParameters("{\"che.audio.sf.ainsModelPref\":10}");
                setParameters("{\"che.audio.sf.nsngPredefAgg\":11}");
                setParameters("{\"che.audio.uplink_gain\":400}"); // 范围（0-400）
            }
        } else if (mode == TransModeType.ONE_TO_ONE) {
            // 设置立体音格式（必须）
            engine?.setAudioProfile(Constants.AUDIO_PROFILE_MUSIC_HIGH_QUALITY_STEREO)
            engine?.setAudioScenario(Constants.AUDIO_SCENARIO_GAME_STREAMING)
            // 声网提供的私参，为了支持"立体音"传输
            engine?.setParameters("{\"che.audio.aec.enable\":false}")
            engine?.setParameters("{\"che.audio.ans.enable\":false}")
            engine?.setParameters("{\"che.audio.agc.enable\":false}")
//            engine?.setParameters("{\"che.audio.custom_payload_type\":78}")
            engine?.setParameters("{\"che.audio.custom_bitrate\":32000}")
            engine?.setParameters("{\"che.audio.neteq.multichannel_plc\":true}");
//            engine?.setParameters("{\"che.audio.mute_playout_signal\":true}")   // 切断系统的播放功能
        }
        if (lingCastParams.saveAgoraAudio) {
            //dump音频和网络信息
            engine?.setParameters("{\"che.audio.frame_dump\":{\"location\":\"all\",\"action\":\"start\",\"max_size_bytes\":\"100000000\",\"uuid\":\"123456789\", \"duration\": \"150000\"}}")
            engine?.setParameters("{\"che.audio.neteq.dump_level\":1}");
        }

    }

    /**
     * 销毁资源
     */
    fun destroyEngine() {
        LogUtil.d(TAG, "销毁LingCast引擎\n\n\n\n")
        if (customAudioTrack != -1) {
            engine?.destroyCustomAudioTrack(customAudioTrack)
            customAudioTrack = -1
        }
        handler.post { RtcEngine.destroy() }
        engine = null
        callback = null
    }

    /**
     * 加入通道(1、创建房间得到房间号；2、通过房间号添加speaker；3、加入通道并订阅文本和消息)
     * @param mode 通道模式
     * @param srcCode 本地语言
     * @param dstCodes 目标语言
     * @param option 通道配置
     * @param cloudRecordingEnabled 是否启用云端录制（调试用）
     */
    suspend fun joinChannel(
        mode: String,
        roomNo: String,
        scenario: String,
        srcCode: String,
        dstCodes: List<String>,
        cloudRecordingEnabled: Boolean = false,
        translateEngine: String = TranslateEngine.default.engineName,
        options: Option
    ): String {
        val createRoomRes = createRoomV2(mode, roomNo, scenario, dstCodes)
        if (createRoomRes != null) {
            room_no = createRoomRes.room_no
            CommandManager.roomNo = room_no  // 设置CommandManager的roomNo
            val addSpeakerRes = addSpeaker(createRoomRes.room_no, srcCode, cloudRecordingEnabled, translateEngine)
            if (addSpeakerRes != null) {
                options.publishCustomAudioTrackId = customAudioTrack
                val joinRes = engine?.joinChannel(
                    addSpeakerRes.connect_token,
                    createRoomRes.room_no,
                    addSpeakerRes.connect_uid.toInt(),
                    options
                )

                if (joinRes != 0) {
                    callback?.onError(
                        SpeechError(
                            ErrorCode.JOIN_ERROR.code,
                            "joinChannel 加入通道失败 $joinRes"
                        )
                    )
                    LogUtil.e(TAG, "joinChannel 加入通道失败 $joinRes")
                    return room_no
                }
                initRoomLogDir(lingCastParams, mTransModeType, room_no)
                trans = addSpeakerRes.translation_list
                subTtsByCode(trans[0].locale)
            }
        }

        return room_no
    }


    /**
     * 离开通道
     */
    fun leaveChannel() {
        engine?.registerAudioFrameObserver(null)
        engine?.leaveChannel()
        CommandManager.clear()  // 清除CommandManager的状态
        scope.launch {
            val roomNo = room_no
            room_no = ""
            if (roomNo.isNotEmpty()) {
                closeRoom(roomNo)
            }
        }
        resetRoomLogDir()
        audioObserverList.clear()
    }

    /**
     * 推送音频数据(自定义音频采集，比如说BLE作为音频源)
     * @param buffer 音频数据
     * @param sampleRate 采样率
     * @param channels 通道数
     */
    fun pushData(buffer: ByteArray, sampleRate: Int, channels: Int) {
        if (customAudioTrack != -1) {
            if (lingCastParams.saveDebugAudio) saveAudioToFile(LCLogFileType.SEND_PCM, buffer)
            engine?.let {
                it.pushExternalAudioFrame(
                    buffer,
                    it.currentMonotonicTimeInMs,
                    sampleRate,
                    channels,
                    Constants.BytesPerSample.TWO_BYTES_PER_SAMPLE,
                    customAudioTrack
                )
            }
        }
    }

    /**
     *
     * 以主动轮询的方式，拉取房间内已订阅用户的音频流帧
     * 如果房间内无人说话，则拉取到的数据为静音
     */
    fun pullAudioFrame(frame: ByteBuffer, size: Int) {
        engine?.pullPlaybackAudioFrame(frame, size)
    }


    private fun initAudioObserver(mode: TransModeType) {
        LogUtil.d(TAG, "注册音频监听... 模式:$mode")
        mAudioFrameObserver = AudioFrameObserver()
        mAudioFrameObserver?.onAgoraAudioListener = object : OnAgoraAudioListener {
            override fun onPlayBackFrameBeforeMixing(
                uid: String?,
                data: ByteArray,
                samplesPerChannel: Int,
                channels: Int,
                samplesPerSec: Int
            ) {
                if (lingCastParams.saveDebugAudio) {
                    saveAudioToFile(LCLogFileType.RECEIVE_PCM, data)
                }
                audioObserverList.forEach {
                    it.onPlayBackFrameBeforeMixing(
                        uid,
                        data,
                        samplesPerChannel,
                        channels,
                        samplesPerSec
                    )
                }
            }

            override fun onRecordFrame(
                channelId: String?,
                type: Int,
                channels: Int,
                buffer: ByteBuffer?
            ) {
                audioObserverList.forEach {
                    it.onRecordFrame(channelId, type, channels, buffer)
                }
            }

            override fun onPlaybackAudioFrame(
                channelId: String?,
                type: Int,
                samplesPerChannel: Int,
                bytesPerSample: Int,
                channels: Int,
                samplesPerSec: Int,
                data: ByteArray,
                renderTimeMs: Long
            ) {
//                LogUtil.d(TAG, "监听到播放的音频 onPlaybackAudioFrame： ${buffer?.remaining()}")
                if (lingCastParams.saveDebugAudio) {
                    saveAudioToFile(LCLogFileType.RECEIVE_PCM, data)
                }
                audioObserverList.forEach {
                    it.onPlaybackAudioFrame(
                        channelId,
                        type,
                        channels,
                        bytesPerSample,
                        channels,
                        samplesPerSec,
                        data,
                        renderTimeMs
                    )
                }
            }
        }
        val ret = engine?.registerAudioFrameObserver(mAudioFrameObserver)
        LogUtil.d(TAG, "注册音频监听结果：$ret  (0为成功)")
//        engine?.setRecordingAudioFrameParameters(16000, 1, RAW_AUDIO_FRAME_OP_MODE_READ_WRITE, 1024)
        if (mode == TransModeType.ONE_TO_ONE) {
            engine?.setPlaybackAudioFrameParameters(
                16000,
                2,
                RAW_AUDIO_FRAME_OP_MODE_READ_WRITE,
                1280
            )
        } else {
            engine?.setPlaybackAudioFrameParameters(
                16000,
                2,
                RAW_AUDIO_FRAME_OP_MODE_READ_WRITE,
                640
            )
        }
//        engine?.setPlaybackAudioFrameBeforeMixingParameters(16000, 2)   // 10ms会稳定触发一次
    }

    fun addAudioFrameObserver(o: OnAgoraAudioListener) {
        audioObserverList.add(o)
    }

    // 来自声网SDK的回调
    private val eventHandler: IRtcEngineEventHandler = object : IRtcEngineEventHandler() {

        override fun onUserJoined(uid: Int, elapsed: Int) {
            super.onUserJoined(uid, elapsed)
            LogUtil.d(TAG, "onUserJoined uid:$uid")
        }

        override fun onUserOffline(uid: Int, reason: Int) {
            super.onUserOffline(uid, reason)
            if (reason != 0) {
                callback?.onError(
                    SpeechError(
                        co.timekettle.agora.common.enums.ErrorCode.USER_OFFLINE_ERROR.code,
                        "onUserOffline uid:$uid reason:$reason"
                    )
                )
            }
            LogUtil.d(TAG, "onUserOffline uid:$uid reason:$reason")
        }

        override fun onJoinChannelSuccess(channel: String?, uid: Int, elapsed: Int) {
            super.onJoinChannelSuccess(channel, uid, elapsed)
            callback?.onSuccess()
            LogUtil.d(TAG, "onJoinChannelSuccess channel:$channel uid:$uid")
        }

        override fun onStreamMessage(uid: Int, streamId: Int, data: ByteArray) {
            super.onStreamMessage(uid, streamId, data)
            try {
                val json = String(data, Charsets.UTF_8)
                val result = JSONObject(json)
                if (result.has("event")) {
                    when (result.getString("event")) {
                        EventType.NOTIFICATION.type -> { //通知
                            if (result.has("kind")) {
                                when (result.getString("kind")) {
                                    NotifyType.CLOSE_ROOM.type -> {
                                        LogUtil.d(TAG, "closeRoomJson: $json")
                                        handler.post {
                                            callback?.onCloseRoom()
                                        }
                                    }
                                }
                            }
                        }

                        EventType.TRANSLATE_SPEECH_TO_SPEECH.type -> { //识别翻译
                            val type = object : TypeToken<BaseMessage<SpeechMessage>>() {}.type
                            val msg: BaseMessage<SpeechMessage> = Gson().fromJson(json, type)
                            when (msg.kind) {
                                SpeechType.ORIGIN.type -> { //识别
                                    val logMsg = "[识别] $json"
                                    LogUtil.d(TAG, logMsg)
                                    saveLogToFile(LCLogFileType.RECOGNIZE_TRANSLATE, logMsg)
                                    handler.post {
                                        callback?.onRecognize(msg.data)
                                    }
                                }

                                SpeechType.TRANSLATION.type -> { //翻译
                                    val logMsg = "[翻译] $json"
                                    LogUtil.d(TAG, logMsg)
                                    saveLogToFile(LCLogFileType.RECOGNIZE_TRANSLATE, logMsg)
                                    handler.post {
                                        callback?.onTranslate(msg.data)
                                    }
                                }

                                else -> {
                                    LogUtil.e(TAG, "kind error ${msg.kind}")
                                }
                            }
                        }

                        else -> {
                            LogUtil.e(TAG, "未知的Event类型: $json")
                        }  // 其它类型的Event
                    }
                }
            } catch (e: Exception) {
                LogUtil.e(TAG, "onStreamMessage error: $e")
            }

        }

        override fun onNetworkQuality(uid: Int, txQuality: Int, rxQuality: Int) {
            super.onNetworkQuality(uid, txQuality, rxQuality)
            callback?.onNetworkQuality(uid, txQuality, rxQuality)
            val logContent =
                "[onNetworkQuality] uid: $uid txQuality: $txQuality rxQuality: $rxQuality"
            saveLogToFile(LCLogFileType.NETWORK, logContent)
        }

        override fun onRtcStats(stats: RtcStats) {
            super.onRtcStats(stats)
            callback?.onRtcStats(
                RtcState(
                    stats.txBytes,
                    stats.rxBytes,
                    stats.txAudioBytes,
                    stats.rxAudioBytes,
                    stats.txPacketLossRate,
                    stats.rxPacketLossRate
                )
            )
            val logContent =
                "[onRtcStats] txAudioBytes:${stats.txAudioBytes} rxAudioBytes:${stats.rxAudioBytes} " +
                        " txPacketLossRate:${stats.txPacketLossRate} rxPacketLossRate:${stats.rxPacketLossRate} "
            saveLogToFile(LCLogFileType.NETWORK, logContent)
        }

        override fun onError(err: Int) {
            super.onError(err)
            if (err != 123) { // 123 client is banned by server, 暂时不提示，通过通知关闭房间
                callback?.onError(SpeechError(err, RtcEngine.getErrorDescription(err)))
            }
            LogUtil.d(TAG, "onError $err ${RtcEngine.getErrorDescription(err)}")
        }

        override fun onStreamMessageError(
            uid: Int,
            streamId: Int,
            error: Int,
            missed: Int,
            cached: Int
        ) {
            super.onStreamMessageError(uid, streamId, error, missed, cached)
            LogUtil.d(TAG, "onStreamMessageError： uid = $uid  streamId = $streamId error = $error missed = $missed  cached = $cached ")
        }

        override fun onAudioVolumeIndication(
            speakers: Array<out AudioVolumeInfo>,
            totalVolume: Int
        ) {
            super.onAudioVolumeIndication(speakers, totalVolume)
            val logContent =
                "[onAudioVolumeIndication] speakers:${speakers.toLogString()} totalVolume:$totalVolume"
            saveLogToFile(LCLogFileType.NETWORK, logContent)
            callback?.onAudioVolumeIndication(speakers.toVolumeInfoList(), totalVolume)
        }

    }


    /**
     * 订阅TTS By Uid
     */
    fun subTtsByUid(uid: String = ""): Boolean {
        LogUtil.d(TAG, "订阅服务端TTS uid: $uid")
        return if (uid.isNotEmpty()) { //订阅指定的音频，播放tts
            engine?.setSubscribeAudioAllowlist(arrayOf(uid.toInt()).toIntArray()) == 0
        } else { //相当于取消订阅音频，不播放tts
            engine?.setSubscribeAudioAllowlist(emptyArray<Int>().toIntArray()) == 0
        }
    }

    fun subTtsByUidList(uidList: List<String> = listOf()): Boolean {
        LogUtil.d(TAG, "订阅服务端TTS uidList: $uidList")
        return if (uidList.isNotEmpty()) { //订阅指定的音频，播放tts
            engine?.setSubscribeAudioAllowlist(uidList.map { it.toInt() }.toIntArray()) == 0
        } else { //相当于取消订阅音频，不播放tts
            engine?.setSubscribeAudioAllowlist(emptyArray<Int>().toIntArray()) == 0
        }
    }

    fun subTtsByCode(code: String): Boolean {
        // TODO: FIXME 快速进入和退出会阻塞主线程
        LogUtil.d(TAG, "订阅服务端TTS code: $code transList: $trans")
        val uid = trans.find { it.locale == code }?.subscribe_uid
        return if (!uid.isNullOrEmpty()) { //订阅指定的音频，播放tts
            engine?.setSubscribeAudioAllowlist(arrayOf(uid.toInt()).toIntArray()) == 0
        } else { //相当于取消订阅音频，不播放tts
            engine?.setSubscribeAudioAllowlist(emptyArray<Int>().toIntArray()) == 0
        }
    }


    fun getCaptainToken() = SpUtils.getString(CAPTAIN_TOKEN, "NO CAPTAIN TOKEN!!!")

    fun setCaptainToken(token: String) = SpUtils.put(CAPTAIN_TOKEN, token)

    fun getBusinessToken() = SpUtils.getString(BUSINESS_TOKEN, "NO BUSINESS TOKEN!!!")

    fun setBusinessToken(token: String) = SpUtils.putString(BUSINESS_TOKEN, token)

    fun getAgoraToken() = agoraToken

    fun setAgoraToken(token: String) {
        this.agoraToken = token
    }

    suspend fun getShareInfo(): ShareInfoRes? {
        try {
            val res = apiRepository.getShareInfo()
            if (res.code == 0) {
                val ret = res.data
                LogUtil.d(TAG, "getShareInfo $ret")
                return ret
            } else {
                LogUtil.e(TAG, "getShareInfo $res")
                return null
            }
        } catch (e: Exception) {
            processException(e, "getShareInfo")
            LogUtil.e(TAG, "getShareInfo $e")
            return null
        }
    }

    suspend fun resetRoom(room_no: String): ResetRoomRes? {
        try {
            val res = apiRepository.resetRoom(room_no)
            if (res.code == 0) {
                val ret = res.data
                LogUtil.d(TAG, "resetRoom $ret")
                return ret
            } else {
                LogUtil.e(TAG, "resetRoom $res")
                return null
            }
        } catch (e: Exception) {
            processException(e, "resetRoom")
            LogUtil.e(TAG, "resetRoom $e")
            return null
        }
    }


    private suspend fun createRoomV2(
        mode: String,
        roomNo: String, // 房间号（可选，客户端如果传递，固定使用此会议号。建议客户端首次获取到会议号后保存本地，下次请求带上，防止会议号消耗过快要做回收操作）
        scenario: String,
        locals: List<String>
    ): CreateRoomRes? {
        return try {
            val createRoomReq =
                CreateRoomReq(
                    mode,
                    scenario = scenario,
                    room_no = roomNo,
                    translation_locale = locals
                )
//            val res = apiRepository.createRoomV2(createRoomReq)
            // TODO 演讲模式使用 v1 接口，后续再更新 v2
            val res = apiRepository.createRoom(createRoomReq)
            if (res.code == 0) {
                val ret = res.data
                LogUtil.d(TAG, "createRoomV2 $roomNo ret:$ret")
                ret
            } else {
                LogUtil.e(TAG, "createRoomV2 $roomNo error: $res")
                callback?.onError(SpeechError(res.code, "createRoomV2 $roomNo ${res.message}"))
                null
            }
        } catch (e: Exception) {
            LogUtil.e(TAG, "createRoomV2 $roomNo error: $e")
            processException(e, "createRoomV2")
            null
        }
    }

    private suspend fun closeRoom(room_no: String): Boolean {
        return try {
            val res = apiRepository.closeRoom(room_no)
            if (res.code == 0) {
                LogUtil.d(TAG, "closeRoom $room_no $res")
                true
            } else {
                LogUtil.e(TAG, "closeRoom $room_no $res")
                callback?.onError(SpeechError(res.code, "closeRoom ${res.message}"))
                false
            }
        } catch (e: Exception) {
            LogUtil.e(TAG, "closeRoom $room_no $e")
            processException(e, "closeRoom")
            false
        }
    }

    private suspend fun addSpeaker(
        room_no: String,
        locale: String,
        cloudRecordingEnabled: Boolean,
        translateEngine: String = TranslateEngine.default.engineName
    ): AddSpeakerRes? {
        return try {
            val addSpeakerReq = AddSpeakerReq(locale, cloudRecordingEnabled, translate_engine = translateEngine)
            val res = apiRepository.addSpeaker(room_no, addSpeakerReq)
            if (res.code == 0) {
                val ret = res.data
                setAgoraToken(ret.token_info.token)
                LogUtil.d(TAG, "addSpeaker $ret")
                ret
            } else {
                callback?.onError(SpeechError(res.code, "addSpeaker ${res.message}"))
                LogUtil.e(TAG, "addSpeaker $res")
                null
            }
        } catch (e: Exception) {
            LogUtil.e(TAG, "addSpeaker $e")
            processException(e, "addSpeaker")
            null
        }
    }


    // 创建对话（特用于两个通道对话的场景）
    suspend fun createDialog(
        req: CreateDialogReq
    ): CreateDialogRes? {
        return try {
            val res = apiRepository.createDialog(req)
            if (res.code == 0) {
                val ret = res.data
                room_no = ret.room_no
                CommandManager.roomNo = room_no  // 设置CommandManager的roomNo
                setAgoraToken(ret.token_info.token)
                LogUtil.d(TAG, "创建对话成功 ret: $ret")
                joinAgoraRoom(ret)
                ret
            } else {
                callback?.onError(SpeechError(res.code, "创建对话失败 ${res.message}"))
                LogUtil.e(TAG, "创建对话失败 res: $res")
                null
            }
        } catch (e: Exception) {
            processException(e, "创建对话异常")
            null
        }
    }

    private fun joinAgoraRoom(res: CreateDialogRes): Boolean {
        val options = Option.Builder().build()
        options.publishCustomAudioTrackId = customAudioTrack
        LogUtil.d(TAG, "加入声网频道，房间号：${res.room_no}")
        val joinRes = engine?.joinChannel(
            res.connect_token,
            res.room_no,
            res.connect_uid.toInt(),
            options
        )
        if (joinRes != 0) {
            callback?.onError(
                SpeechError(
                    ErrorCode.JOIN_ERROR.code,
                    "加入声网房间失败 $joinRes"
                )
            )
            LogUtil.e(TAG, "加入声网房间失败 $joinRes")
            return false
        }
        LogUtil.d(TAG, "加入声网频道成功")
        initRoomLogDir(lingCastParams, mTransModeType, room_no)
        subTtsByUid(res.translation_list.first().subscribe_uid)
        return true
    }

    fun processException(e: Exception, descPrefix: String = "") {
        LogUtil.e(TAG, "processException $descPrefix $e")
        if (e is ResponseException) {
            callback?.onError(SpeechError(e.type.reasonCode, e.type.reason))
        } else {
            callback?.onError(
                SpeechError(
                    ErrorCode.CATCH_ERROR.code,
                    descPrefix + e.message.toString()
                )
            )
        }
    }

    // 退出登录/切换用户时，需要调用此方法，清除本地token
    fun clearCache() {
        setAgoraToken("")
        setCaptainToken("")
        setBusinessToken("")
    }

    suspend fun switchLanguage(locale: String, dstLocale: String = "") =
        CommandManager.switchLanguage(locale, dstLocale)

    suspend fun switchLanguage(locale: String, dstLocales: List<String>) =
        CommandManager.switchLanguage(locale, dstLocales)

    suspend fun setTtsSpeed(speed: Float) =
        CommandManager.setTtsSpeed(speed)

    suspend fun setTtsEnable(enabled: Boolean) =
        CommandManager.setTtsEnabled(enabled)

    suspend fun setTtsEnable(leftOpen: Boolean, rightOpen: Boolean) =
        CommandManager.setTtsEnabled(leftOpen, rightOpen)

    suspend fun setTtsGender(leftGender: String, rightGender: String) =
        CommandManager.setTtsGender(leftGender, rightGender)

    suspend fun setTtsVolume(leftVolume: Int, rightVolume: Int) =
        CommandManager.setTtsVolume(leftVolume, rightVolume)

    suspend fun setCustomVocabulary(enabled: Boolean) = CommandManager.setCustomVocabulary(enabled)

    suspend fun setTranslateEngine(engine: TranslateEngine) =
        CommandManager.setTranslateEngine(engine)

    suspend fun setRoomTranslateEngine(engine: TranslateEngine) =
        CommandManager.setRoomTranslateEngine(engine)

    suspend fun setTextToSpeech(textToSpeechRwq: TextToSpeechReq) =
        CommandManager.setTextToSpeech(textToSpeechRwq)

    suspend fun refreshCustomVocabulary() = CommandManager.refreshCustomVocabulary()

    const val TAG = "SpeechManager"

}