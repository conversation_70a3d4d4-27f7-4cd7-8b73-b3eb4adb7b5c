plugins {
    id 'com.android.library'
    id 'org.jetbrains.kotlin.android'
}

android {
    namespace 'com.timekettle.agora'
    compileSdk 34

    defaultConfig {
        minSdk 21

        testInstrumentationRunner "androidx.test.runner.AndroidJUnitRunner"
        consumerProguardFiles "consumer-rules.pro"
    }

    buildTypes {
        release {
            minifyEnabled false
            proguardFiles getDefaultProguardFile('proguard-android-optimize.txt'), 'proguard-rules.pro'
        }
    }
    compileOptions {
        sourceCompatibility JavaVersion.VERSION_1_8
        targetCompatibility JavaVersion.VERSION_1_8
    }
    kotlinOptions {
        jvmTarget = '1.8'
    }

}

dependencies {


    implementation 'androidx.core:core-ktx:1.8.0'
    implementation("org.jetbrains.kotlin:kotlin-stdlib:1.5.0")
    implementation("org.jetbrains.kotlinx:kotlinx-coroutines-core:1.5.0")
    implementation("org.jetbrains.kotlinx:kotlinx-coroutines-android:1.5.0")
    testImplementation 'junit:junit:4.13.2'
    androidTestImplementation 'androidx.test.ext:junit:1.2.1'
    androidTestImplementation 'androidx.test.espresso:espresso-core:3.6.1'
    implementation 'com.blankj:utilcodex:1.31.1'

    implementation 'com.tencent:mmkv-static:1.2.9'
    // Retrofit
    implementation 'com.squareup.retrofit2:retrofit:2.11.0'
    // Gson Converter (用于 JSON 解析)
    implementation 'com.squareup.retrofit2:converter-gson:2.11.0'
    // OkHttp (Retrofit 的底层 HTTP 客户端)
    implementation 'com.squareup.okhttp3:okhttp:4.12.0'
    // Logging Interceptor (用于调试)
    implementation 'com.squareup.okhttp3:logging-interceptor:4.12.0'
    // 声网 SDK
    api 'io.agora.rtc:agora-special-voice:4.4.1.137'// 立体声特调版

}