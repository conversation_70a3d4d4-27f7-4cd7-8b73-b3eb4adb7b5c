# LingCast For Android

这是一个基于声网 SDK 的 Android 库项目，用于实现音频相关功能，仅限时空壶内部使用。

## 功能特性

- 基于声网 SDK 4.4.1.9 版本（立体声特调版）
- 包含网络请求、数据存储等基础功能
- 支持自定义音频采集和播放
- 支持多种传输模式（一对一、一对多等）

## 核心类说明

### SpeechManager

`SpeechManager` 是 SDK 的核心门面类，负责管理音频通信的整个生命周期。主要功能包括：

1. **初始化配置**
   ```kotlin
   // 初始化 SDK
   SpeechManager.init(
       context: Context,
       captainUrl: String = SpeechUrl.BUSINESS_BASE_URL,
       businessParams: BusinessParams,
       lingCastParams: LingCastParams
   )
   ```

2. **创建引擎**
   ```kotlin
   // 创建声网引擎
   SpeechManager.createEngine(
       config: Config,
       customPlay: Boolean,
       sampleRate: Int,
       transModeType: TransModeType = TransModeType.NONE,
       speechCallback: SpeechCallback
   )
   ```

3. **加入/离开房间**
   ```kotlin
   // 加入房间
   suspend fun joinChannel(
       mode: String,
       roomNo: String,
       scenario: String,
       srcCode: String,
       dstCodes: List<String>,
       cloudRecordingEnabled: Boolean = false,
       options: Option
   )

   // 离开房间
   fun leaveChannel()
   ```

4. **音频处理**
   ```kotlin
   // 推送音频数据
   fun pushAudioFrame(
       buffer: ByteArray,
       sampleRate: Int,
       channels: Int
   )

   // 拉取音频数据
   fun pullAudioFrame(frame: ByteBuffer, size: Int)
   ```

5. **TTS 控制**
   ```kotlin
   // 订阅 TTS
   fun subTtsByCode(code: String): Boolean
   fun subTtsByUid(uid: String): Boolean
   fun subTtsByUidList(uidList: List<String>): Boolean

   // 设置 TTS 参数
   suspend fun setTtsSpeed(speed: Float)
   suspend fun setTtsEnabled(enabled: Boolean)
   suspend fun setTtsEnable(leftOpen: Boolean, rightOpen: Boolean)
   suspend fun setTtsGender(leftGender: String, rightGender: String)
   suspend fun setTtsVolume(leftVolume: Int, rightVolume: Int)
   ```

6. **语言切换**
   ```kotlin
   // 切换语言，重载以提供对 切换1对1、1对多语言 的支持
   suspend fun switchLanguage(locale: String, dstLocale: String = "")
   suspend fun switchLanguage(locale: String, dstLocales: List<String>)
   ```

7. **清除LingCast缓存**
   ```kotlin
   // 退出登录/切换用户时，需要调用此方法，清除本地token，否则会导致服务端判断异常，无法正常使用
   fun clearCache() 
   ```

### CommandManager

`CommandManager` 是 SDK 的指令管理器，负责处理 TTS、语言切换等指令相关的操作。主要功能包括：

1. **语言切换**
   ```kotlin
   // 切换语言（1对1模式）
   suspend fun switchLanguage(locale: String, targetLocale: String = "")
   // 切换语言（1对多模式）
   suspend fun switchLanguage(locale: String, targetLocales: List<String>)
   ```

2. **TTS 控制**
   ```kotlin
   // 设置 TTS 速度
   suspend fun setTtsSpeed(speed: Float)
   // 设置 TTS 开关
   suspend fun setTtsEnabled(enabled: Boolean)
   // 分别设置左右声道 TTS 开关
   suspend fun setTtsEnable(leftOpen: Boolean, rightOpen: Boolean)
   // 设置 TTS 性别
   suspend fun setTtsGender(leftGender: String, rightGender: String)
   // 设置 TTS 音量
   suspend fun setTtsVolume(leftVolume: Int, rightVolume: Int)
   // 设置自定义翻译是否启用
   suspend fun setCustomVocabulary(enabled: Boolean)
   // 刷新自定义词汇表
   suspend fun refreshCustomVocabulary()
   ```

3. **翻译引擎设置**
   ```kotlin
   // 设置翻译引擎
   suspend fun setTranslateEngine(engine: TranslateEngine)
   ```

4. **文本转语音**
   ```kotlin
   // 发送文本转语音请求
   suspend fun setTextToSpeech(req: TextToSpeechReq)
   ```

### SpeechCallback

`SpeechCallback` 是事件回调接口，用于接收各种状态通知：

```kotlin
interface SpeechCallback {
    fun onCloseRoom() {}  // 房间关闭回调
    fun onRecognize(msg: SpeechMessage) {}  // 语音识别结果回调
    fun onTranslate(msg: SpeechMessage) {}  // 翻译结果回调
    fun onError(msg: SpeechError) {}  // 错误回调
    fun onSuccess() {}  // 成功回调
    fun onNetworkQuality(uid: Int, txQuality: Int, rxQuality: Int) {}  // 网络质量回调
    fun onRtcStats(state: RtcState) {}  // RTC 状态回调
    fun onAudioVolumeIndication(speakers: List<VolumeInfo>, totalVolume: Int) {}  // 音量指示回调
}
```

## Token说明

> 所有 Token 都会在 SDK 内部自动管理，调用时只需要在初始化时传入 Business Token 即可，其他 Token 的获取和管理都由 SDK 自动完成。

| Token | 用途       | 获取方式 |
|------|------------|------|
| Captain Token    | LingCast的创建对话、更新语言、设置音色等接口，都需要此Token，否则服务端会报错 ​HTTP 401 Unauthorized​      | 通过不同业务传入的Business Token以及businessApiUrl在SDK内通过拦截器自动获取，获取成功之后会保存到本地，并继续未完成的请求。后续的请求也会沿用本地缓存的Captain Token，直到Token过期，才会去刷新Token   | 
| Business Token    |用来获取Captain Token，调用方传入后 SDK 会缓存到本地，直到下一次更新| 不同项目对应的不同业务Token，时空壶App的业务Token在登录后可获取到，和用户绑定；X1的业务Token则通过SN号获取，和SN绑定       | 
| Agora Token    | 用来加入声网的房间，调用声网SDK的joinChannel时会需要传入此Token，才能正常加入声网的房间      | 服务端下发，在调用服务端的创建对话/创建房间接口之后，服务端会下发一个用于加入声网SDK房间的token   |

#### 客户端设置 BusinessToken 的时机

1. 在开始调用 SDK 实际功能之前，务必确保已完成 BusinessToken 的设置。
2. 调用方应 **尽可能确保 BusinessToken 的有效性**，以避免在请求 Captain 接口时因无法刷新 CaptainToken 而导致请求失败。因此，建议在 APP 启动后尽快获取最新的 BusinessToken 并设置给 SDK。
   - 对于时空壶 APP，需在登录成功后进行设置；
   - 对于 X1，需定时刷新 BusinessToken 以保证其可用性。
3. 调用方需监听 `onError` 接口。若接口返回错误码 1004，需刷新 BusinessToken 并调用 `setBusinessToken(` 方法重新设置给 SDK。



## 依赖要求

- Android Studio
- Kotlin 1.5.0 或更高版本
- Android SDK 21+ (Android 5.0 或更高版本)
- Gradle 7.0 或更高版本

## 主要依赖

```gradle
dependencies {
    // 声网 SDK 特调版，相比线上版本，增加了一些私参支持，也解决了一些Bug
    implementation 'io.agora.rtc:agora-special-voice:4.4.1.9'
    
    // 网络请求
    implementation 'com.squareup.retrofit2:retrofit:2.11.0'
    implementation 'com.squareup.retrofit2:converter-gson:2.11.0'
    implementation 'com.squareup.okhttp3:okhttp:4.12.0'
    
    // 工具库
    implementation 'com.blankj:utilcodex:1.31.1'
    implementation 'com.tencent:mmkv-static:1.2.9'
    
    // Kotlin 相关
    implementation 'androidx.core:core-ktx:1.8.0'
    implementation 'org.jetbrains.kotlin:kotlin-stdlib:1.5.0'
    implementation 'org.jetbrains.kotlinx:kotlinx-coroutines-core:1.5.0'
    implementation 'org.jetbrains.kotlinx:kotlinx-coroutines-android:1.5.0'
}
```

## 快速开始

1. 将项目作为子模块添加到您的项目中：

```bash
# 在您的项目根目录下执行
git <NAME_EMAIL>:timekettle/lingcast-android.git lingcast_for_android
```

2. 在项目级 `settings.gradle` 文件中添加：

```gradle
include ':lingcast_for_android'
```

3. 在需要使用该库的模块的 `build.gradle` 文件中添加依赖：

```gradle
dependencies {
    implementation project(':lingcast_for_android')
}
```

4. 基本使用示例：

### 1对1 模式

```kotlin
// 1. 初始化
SpeechManager.init(context, businessParams, lingCastParams)

// 2. 创建引擎
SpeechManager.createEngine(
    Config.Builder(context).build(),
    true,  // 自定义播放
    16000, // 采样率
    TransModeType.ONE_TO_ONE,  // 1对1模式
    object : SpeechCallback {
        override fun onRecognize(msg: SpeechMessage) {
            // 处理语音识别结果
        }

        override fun onTranslate(msg: SpeechMessage) {
            // 处理翻译结果
        }

        override fun onError(msg: SpeechError) {
            // 处理错误
        }

        override fun onSuccess() {
            // 加入房间成功回调
        }
    }
)

// 3. 创建对话（1v1模式专用，会同时完成引擎创建和加入房间）
lifecycleScope.launch {
    val createDialogReq = CreateDialogReq().apply {
        voice_setting = CreateDialogReq.VoiceSetting.Default()
        scenario = "translate_speech_to_speech"
        locale_list = LocaleItem.withLeftRight("zh-CN", "en-US")  // 设置左右声道语言
        cloud_recording_enabled = false  // 是否启用云端录制
    }
    SpeechManager.createDialog(createDialogReq)
}


// 4. 推送音频数据（for循环调用）
SpeechManager.pushAudioFrame(audioData, 16000, 2)

// 6. 切换语言（可选）
lifecycleScope.launch {
    val ret = SpeechManager.switchLanguage("zh-CN", "en-US")
    if (ret) {
        Log.d(TAG, "切换语言成功")
    } else {
        Log.e(TAG, "切换语言失败")
    }
}

// 7. 离开房间
SpeechManager.leaveChannel()

// 8. 销毁引擎
SpeechManager.destroyEngine()
```

### 1对多模式

```kotlin
// 1. 初始化
SpeechManager.init(context, businessParams, lingCastParams)

// 2. 创建引擎
SpeechManager.createEngine(
    config = Config.Builder(App.context).build(),
    customPlay = true,
    sampleRate = 16000,
    transModeType = TransModeType.NONE,
    speechCallback = object : SpeechCallback {
        override fun onRecognize(msg: SpeechMessage) {
            // 处理语音识别结果
        }
        
        override fun onTranslate(msg: SpeechMessage) {
            // 处理翻译结果
        }
        
        override fun onError(msg: SpeechError) {
            // 处理错误
        }
    }
)

// 3. 加入房间
val roomNo = SpeechManager.joinChannel(
    mode = "presentation",
    roomNo = "",
    scenario = "translate_speech_to_speech",
    srcCode = "zh-CN",
    dstCodes = listOf("en-US", "ja-JP", "ko-KR"),  // 支持多个目标语言
    Option.Builder().publishMicrophoneTrack(true).publishCustomAudioTrack(false).build()
)

// 4. 推送音频数据
SpeechManager.pushAudioFrame(audioData, 16000, 2)

// 5. 离开房间
SpeechManager.leaveChannel()
```


## 相关文档
#### LingCast后端API文档

- 地址: https://timekettle.stoplight.io/docs/captain/9abc0a458c587 
- 账号: `<EMAIL>`
- 密码: `timekettle@Simon`

