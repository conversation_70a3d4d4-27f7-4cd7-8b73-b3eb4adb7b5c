/**
 * 依赖库版本
 *
 * <AUTHOR>
 * @since 2022/04/15
 */
object Versions {

    //Plugin----------------------------------------------------------------
//     https://github.com/alibaba/ARouter/issues/981 Gradle 7.0之后Arouter会报错直接无法运行项目
//     https://developer.android.com/studio/releases/gradle-plugin  版本需要对应
//     https://services.gradle.org/distributions/  gradle的所有版本
//     *******************
//     AGP 7.0版本需要JDK11才能运行，7.0的上一个版本是4.2.0  对应的gradle是6.7.1，对应的 SDK-tools是 30.0.2
//     4.2版本的AGP，最高只能编译SDK版本30，所以还需要修改targetSdkVersion为 30，否则运行项目会报错
//     接着  core-ktx 1.7.0就会要求minCompileSdk为31，所以ktx的版本也得降级
//     *****************
//     参考这里 https://developer.android.com/studio/releases/gradle-plugin#4-2-0
//     gradle、AGP、JDK、BuildTools的版本升级，都要慎重
//     7.2.2升级到7.3.0 Arouter会报错，不要升级了，参考 https://github.com/alibaba/ARouter/issues/1023
    const val AGP = "7.2.2"                           // Android Gradle plugin = AGP
    const val AROUTER_REGISTER = "1.0.2"
    const val SENSORS_DATA_PLUGIN = "4.0.4"
//    const val MOB_SDK = "+"
//    const val MOB_SDK = "2022.1026.1849"

    // Android--------------------------------------------------------------
    const val AppCompat = "1.5.1"
    const val CoreKtx = "1.7.0"
    const val ActivityKtx = "1.1.0"
    const val FragmentKtx = "1.5.3"
    const val MultiDex = "2.0.1"
    const val ConstraintLayout = "2.1.4"              // 约束布局
    const val MaterialDesign = "1.6.0"                // 材料设计UI套件
    const val SplashScreen = "1.0.0-beta02"

    // Test 2022 05 25-------------------------------------------------------
    const val Junit = "4.13.2"
    const val TestExtJunit = "1.1.3"
    const val TestEspresso = "3.4.0"

    // Kotlin---------------------------------------------------------------
    const val Kotlin = "1.8.0"                       // Kotlin
    const val Coroutines = "1.5.0"                    // 协程

    // JetPack--------------------------------------------------------------
    const val Lifecycle = "2.5.1"                     // 生命周期系列
    const val Hilt = "2.44"                           // DI框架-Hilt
    const val HiltAndroidx = "1.0.0"                  // DI框架-Hilt
    const val Palette = "1.0.0"                       // 调色板
    const val Room = "2.4.0"                          // 数据库

    // Network--------------------------------------------------------------
    const val OkHttp = "4.10.0"                        // OkHttp
    const val OkHttpInterceptorLogging = "4.10.0"      // OkHttp 请求日志拦截器
    const val Retrofit = "2.9.0"                      // Retrofit
    const val Gson = "2.9.0"                          // Gson
    const val RetrofitConverterGson = "2.9.0"         // Retrofit Gson 转换器

    // UI-------------------------------------------------------------------
    const val AutoSize = "v1.2.1"                     // 今日头条屏幕适配框架
    const val RecyclerViewAdapter = "3.0.11"          // RecyclerView 适配器框架 BRVAH
    const val Glide = "4.11.0"                        // Glide图片加载框架
    const val PictureSelector = "v3.10.7"             // 照片选择器、图片
    const val CZXing = "1.1.0"                        // 扫码器
    const val RefreshClassics = "2.0.3"               // 经典刷新头/经典加载
    const val RoundImageView = "1.2.0"                // 圆角ImageView
    const val AutoLinkTextView = "3.0.0"              // 包含文本点击功能的TextView
    const val ToolTip = "1.3.3"                       // ToolTip
    const val Alert = "1.1.6"                         // 底部弹窗
    const val Lottie = "5.2.0"                        // Lottie动画加载
    const val ShadowLayout = "3.3.2"                  // 阴影布局
    const val XPopup = "2.8.14"                       // 弹窗
    const val ImmersionBar = "3.2.2"                  // 状态栏操作
    const val Pag = "*******"                         // 腾讯PAG，用来处理动效
    const val DialogX = "0.0.47"                      // Dialog弹窗库
    const val BasePop = "3.2.1"                       // BasePop弹窗库

    // 第三方SDK-------------------------------------------------------------
    const val TencentBugly = "4.1.9"                  // 腾讯 Bugly 异常上报
    const val TencentTBSX5 = "44226"                  // 腾讯 TBS X5内核，不要更新了，新版无网初始化会崩溃
    const val SensorsAnalyticsSDK = "6.8.3"           // 神策
    const val JiPush = "4.3.0"                        // 极光推送
    const val JiCore = "2.9.0"                        // 极光推送
    const val Fastjson = "1.2.66"                     // Fastjson

    // 其他-----------------------------------------------------------------
    const val MMKV = "1.2.9"                          // 腾讯 MMKV 持久化存储
    const val ARoute = "1.5.2"                        // 阿里路由
    const val EventBus = "3.3.1"                      // 事件总线
    const val XXPermission = "16.0"                   // 权限申请
    const val AutoService = "1.0"                     // 自动生成SPI暴露服务文件
    const val Logger = "2.2.0"                        // 日志框架
    const val Leakcanary = "2.9.1"                    // 内存泄漏检测
    const val chucker = "3.5.2"                       // HTTP 检查器
    const val UtilCode = "1.31.1"                     // 工具类代码
    const val BillingVersion = "5.1.0"                // 谷歌Billing
    const val Aria = "3.8.16"                         // 文件下载库
    const val GooglePlayServicesAuth = "20.2.0"       // google的认证服务，Login模块和Mine模块都有用到
}

/***************************************依赖库地址***************************************************/

/**
 * Android 核心相关
 */
object AndroidLibs {
    const val MaterialDesign =
        "com.google.android.material:material:${Versions.MaterialDesign}"
    const val AppCompat = "androidx.appcompat:appcompat:${Versions.AppCompat}"
    const val CoreKtx = "androidx.core:core-ktx:${Versions.CoreKtx}"
    const val ConstraintLayout =
        "androidx.constraintlayout:constraintlayout:${Versions.ConstraintLayout}"
    const val ActivityKtx = "androidx.activity:activity-ktx:${Versions.ActivityKtx}"
    const val FragmentKtx = "androidx.fragment:fragment-ktx:${Versions.FragmentKtx}"
    const val MultiDex = "androidx.multidex:multidex:${Versions.MultiDex}"
    const val SplashScreen = "androidx.core:core-splashscreen:${Versions.SplashScreen}"
}

/**
 * 测试相关
 */
object TestLibs {
    const val Junit = "junit:junit:${Versions.Junit}"
    const val AndroidJUnitRunner = "androidx.test.runner.AndroidJUnitRunner"
    const val TestExtJunit = "androidx.test.ext:junit:${Versions.TestExtJunit}"
    const val TestEspresso = "androidx.test.espresso:espresso-core:${Versions.TestEspresso}"
}


/**
 * Jetpack 组件
 */
object JetPackLibs {
    const val ViewModel =
        "androidx.lifecycle:lifecycle-viewmodel-ktx:${Versions.Lifecycle}"

    const val LiveData =
        "androidx.lifecycle:lifecycle-livedata-ktx:${Versions.Lifecycle}"
    const val Runtime =
        "androidx.lifecycle:lifecycle-runtime-ktx:${Versions.Lifecycle}"
    const val ViewModelSavedState =
        "androidx.lifecycle:lifecycle-viewmodel-savedstate:${Versions.Lifecycle}"
    const val LifecycleService =
        "androidx.lifecycle:lifecycle-service:${Versions.Lifecycle}"
    const val LifecycleCompilerAPT =
        "androidx.lifecycle:lifecycle-compiler:${Versions.Lifecycle}"

    const val HiltCore = "com.google.dagger:hilt-android:${Versions.Hilt}"
    const val HiltApt = "com.google.dagger:hilt-compiler:${Versions.Hilt}"
    const val HiltAndroidx = "androidx.hilt:hilt-compiler:${Versions.HiltAndroidx}"
    const val Palette = "androidx.palette:palette:${Versions.Palette}"
    const val Room = "androidx.room:room-runtime:${Versions.Room}"
    const val RoomCompiler = "androidx.room:room-compiler:${Versions.Room}"
    const val RoomCoroutineKtx = "androidx.room:room-ktx:${Versions.Room}"
}

/**
 * Kotlin 语言相关
 */
object KotlinLibs {
    const val Kotlin = "org.jetbrains.kotlin:kotlin-stdlib:${Versions.Kotlin}"
    const val CoroutinesCore =
        "org.jetbrains.kotlinx:kotlinx-coroutines-core:${Versions.Coroutines}"
    const val CoroutinesAndroid =
        "org.jetbrains.kotlinx:kotlinx-coroutines-android:${Versions.Coroutines}"
}

/**
 * 网络相关
 */
object NetworkLibs {
    const val OkHttp = "com.squareup.okhttp3:okhttp:${Versions.OkHttp}"
    const val OkHttpInterceptorLogging =
        "com.squareup.okhttp3:logging-interceptor:${Versions.OkHttpInterceptorLogging}"
    const val Retrofit = "com.squareup.retrofit2:retrofit:${Versions.Retrofit}"
    const val RetrofitConverterGson =
        "com.squareup.retrofit2:converter-gson:${Versions.RetrofitConverterGson}"
    const val Gson = "com.google.code.gson:gson:${Versions.Gson}"
}

/**
 * UI 界面相关
 */
object UILibs {
    const val AutoSize =
        "com.github.JessYanCoding:AndroidAutoSize:${Versions.AutoSize}"
    const val RecyclerViewAdapter =
        "com.github.CymChad:BaseRecyclerViewAdapterHelper:${Versions.RecyclerViewAdapter}"
    const val Glide = "com.github.bumptech.glide:glide:${Versions.Glide}"
    const val GlideCompiler = "com.github.bumptech.glide:compiler:${Versions.Glide}"
    const val CZXing = "io.github.devilsen:czxing:${Versions.CZXing}"
    const val PictureSelector = "io.github.lucksiege:pictureselector:${Versions.PictureSelector}"
    const val PictureCompress = "io.github.lucksiege:ucrop:${Versions.PictureSelector}"
    const val PictureCrop = "io.github.lucksiege:compress:${Versions.PictureSelector}"
    const val RefreshKernel = "com.scwang.smart:refresh-layout-kernel:${Versions.RefreshClassics}"
    const val RefreshHeader = "com.scwang.smart:refresh-header-classics:${Versions.RefreshClassics}"
    const val RefreshFooter = "com.scwang.smart:refresh-footer-classics:${Versions.RefreshClassics}"
    const val RoundImageView = "org.raphets:roundimageview:${Versions.RoundImageView}"
    const val AutoLinkTextView = "com.github.armcha:AutoLinkTextViewV2:${Versions.AutoLinkTextView}"
    const val ToolTip = "com.github.licoba:tooltips:${Versions.ToolTip}"
    const val Alert = "com.github.licoba:Alert:${Versions.Alert}"
    const val Pag = "com.tencent.tav:libpag:${Versions.Pag}"
    const val Lottie = "com.airbnb.android:lottie:${Versions.Lottie}"
    const val ShadowLayout = "com.github.lihangleo2:ShadowLayout:${Versions.ShadowLayout}"
    const val XPopup = "com.github.li-xiaojun:XPopup:${Versions.XPopup}"
    const val ImmersionBar = "com.geyifeng.immersionbar:immersionbar:${Versions.ImmersionBar}"
    const val ImmersionBarKtx = "com.geyifeng.immersionbar:immersionbar-ktx:${Versions.ImmersionBar}"
    const val DialogX = "com.kongzue.dialogx:DialogX:${Versions.DialogX}"
    const val BasePop = "io.github.razerdp:BasePopup:${Versions.BasePop}"
    const val Markwon = "io.noties.markwon:core:4.6.2"


}

/**
 * 三方接入的 SDK
 */
object SDKLibs {
    const val TencentBugly = "com.tencent.bugly:crashreport:${Versions.TencentBugly}"
    const val TencentTBSX5 = "com.tencent.tbs:tbssdk:${Versions.TencentTBSX5}"
    const val JiCore = "cn.jiguang.sdk:jcore:${Versions.JiCore}"
    const val JiPush = "cn.jiguang.sdk:jpush:${Versions.JiPush}"
    const val Fastjson = "com.alibaba:fastjson:${Versions.Fastjson}"
    const val WechatSDK = "com.tencent.mm.opensdk:wechat-sdk-android:+"
    const val FaceBookSDK = "com.facebook.android:facebook-login:latest.release"
    const val SensorsAnalyticsSDK = "com.sensorsdata.analytics.android:SensorsAnalyticsSDK:${Versions.SensorsAnalyticsSDK}"
}

/**
 * 其他
 */
object OtherLibs {
    const val MMKV = "com.tencent:mmkv-static:${Versions.MMKV}"
    const val ARoute = "com.alibaba:arouter-api:${Versions.ARoute}"
    const val ARouteCompiler = "com.alibaba:arouter-compiler:${Versions.ARoute}"
    const val EventBus = "org.greenrobot:eventbus:${Versions.EventBus}"
    const val EventBusAPT = "org.greenrobot:eventbus-annotation-processor:${Versions.EventBus}"
    const val XXPermission = "com.github.getActivity:XXPermissions:${Versions.XXPermission}"
    const val AutoService = "com.google.auto.service:auto-service:${Versions.AutoService}"
    const val AutoServiceAnnotations =
        "com.google.auto.service:auto-service-annotations:${Versions.AutoService}"
    const val Logger = "com.orhanobut:logger:${Versions.Logger}"
    const val Leakcanary = "com.squareup.leakcanary:leakcanary-android:${Versions.Leakcanary}"
    const val debugChucker = "com.github.chuckerteam.chucker:library:${Versions.chucker}"
    const val releaseChucker = "com.github.chuckerteam.chucker:library-no-op:${Versions.chucker}"
    const val UtilCode = "com.blankj:utilcodex:${Versions.UtilCode}"
    const val GoogleBilling = "com.android.billingclient:billing-ktx:${Versions.BillingVersion}"
    const val Aria = "me.laoyuyu.aria:core:${Versions.Aria}"
    const val AriaCompiler = "me.laoyuyu.aria:compiler:${Versions.Aria}"
    const val GooglePlayServicesAuth =
        "com.google.android.gms:play-services-auth:${Versions.GooglePlayServicesAuth}"

}


/**
 * 公司的aar的依赖版本
 */

object TmkAARVersion {
    const val MODULE_LOGIN = "1.2.0-snapshot"
    const val MODULE_HOME = "1.2.4-snapshot"
    const val MODULE_MINE = "1.2.1-snapshot"
}


object TmkLibs {
    const val LoginAAR = "com.timekettle.aar:login:${TmkAARVersion.MODULE_LOGIN}"
    const val HomeAAR = "com.timekettle.aar:home:${TmkAARVersion.MODULE_HOME}"
    const val MineAAR = "com.timekettle.aar:mine:${TmkAARVersion.MODULE_MINE}"

}

