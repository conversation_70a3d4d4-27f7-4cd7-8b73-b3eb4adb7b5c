/**
 * 插件管理
 *
 * <AUTHOR>
 * @since 2022/04/15
 */
object GradlePluginId {
    const val AGP = "com.android.tools.build:gradle:${Versions.AGP}"
    const val KOTLIN_PLUGIN = "org.jetbrains.kotlin:kotlin-gradle-plugin:${Versions.Kotlin}"
    const val HILT_PLUGIN = "com.google.dagger:hilt-android-gradle-plugin:${Versions.Hilt}"
    const val AROUTER_REGISTER = "com.alibaba:arouter-register:${Versions.AROUTER_REGISTER}"
    const val SENSORS_DATA_PLUGIN = "com.sensorsdata.analytics.android:android-gradle-plugin2:${Versions.SENSORS_DATA_PLUGIN}"

    // Firebase start
    const val GOOGLE_SERVICE = "com.google.gms:google-services:4.3.10"
    const val APP_DISTRIBUTION = "com.google.firebase:firebase-appdistribution-gradle:5.0.0"
    const val CRASHLYTICS = "com.google.firebase:firebase-crashlytics-gradle:2.9.9"
    const val FIREBASE_PERFORMANCE = "com.google.firebase:perf-plugin:1.4.1"
    // Firebase end

    const val ANDROID_APPLICATION = "com.android.application"
    const val ANDROID_LIBRARY = "com.android.library"
    const val KOTLIN_ANDROID = "org.jetbrains.kotlin.android"
    const val KOTLIN_KAPT = "org.jetbrains.kotlin.kapt"
    const val AROUTER = "com.alibaba.arouter"
    const val HILT = "dagger.hilt.android.plugin"
    const val SENSORS_DATA = "com.sensorsdata.analytics.android"
}